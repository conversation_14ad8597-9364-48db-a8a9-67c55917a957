# 订单支付逻辑说明

## 概述

本文档详细说明了生鲜商城系统中订单支付的完整逻辑，包括支付类型、金额计算、卡支付处理等核心功能。

## 支付类型定义

系统支持以下支付类型：

| 支付类型 | 代码 | 说明 |
|---------|------|------|
| 微信支付 | 1 | 纯微信支付，无卡参与 |
| 卡支付 | 2 | 纯卡支付，卡余额足够支付全部订单金额 |
| 组合支付 | 3 | 卡+微信组合支付，卡余额不足需微信补足 |
| 现金支付 | 4 | 线下现金支付（暂未在代码中实现） |

## 订单金额构成

订单金额由以下部分组成：
- **商品价格（product_price）**: 所有商品的原价总和
- **配送费（shipping_price）**: 根据店铺配置计算的配送费用
- **原始总价（original_price）**: 商品价格 + 配送费（折扣前）
- **折扣金额（discount_amount）**: 卡等级折扣产生的优惠金额
- **最终总价（total_price）**: 应用折扣后的实际支付金额
- **卡支付金额（card_price）**: 使用卡余额支付的金额
- **需要支付金额（need_pay）**: 除卡支付外还需支付的金额

## 订单预览逻辑（preOrder）

订单预览阶段计算订单的各项金额，为用户提供支付前的价格明细。

### 1. 基础价格计算

```php
// 计算商品价格（原价，不应用折扣）
foreach ($cart_list as $k => $cart) {
    $item_price = bcmul($inventory['price'], $cart['number'], 2);
    $total_price = bcadd($total_price, $item_price, 2);
    $product_price = bcadd($product_price, $item_price, 2);
}

// 计算配送费
$shipping_price = bccomp($product_price, $shop['config']['baoyou_price'], 2) > 0 
    ? '0.00' 
    : $shop['config']['delivery_price'];
$total_price = bcadd($total_price, $shipping_price, 2);
```

### 2. 卡折扣处理

如果用户选择了卡支付且卡有等级折扣：

```php
// 获取卡等级折扣
if ($card['card_level_id'] != 0) {
    $card_level = CardLevel::where(['id' => $card['card_level_id']])->find();
    if (!empty($card_level) && $card_level['discount'] < 100) {
        $card_discount = round($card_level['discount'] / 100, 2);
    }
}

// 应用折扣到总价
if (!empty($card) && $card_discount < 1) {
    $discounted_total_price = bcmul($total_price, $card_discount, 2);
    $discount_amount = bcsub($total_price, $discounted_total_price, 2);
    $total_price = $discounted_total_price;
}
```

### 3. 支付类型判断

根据卡余额和折扣后金额判断支付类型：

```php
if (bccomp($discounted_total_price, $card['yu_money'], 2) <= 0) {
    // 卡余额足够支付 - 纯卡支付
    $card_money = $discounted_total_price;
    $need_pay = '0.00';
    $pay_type = 2; // 卡支付
} else {
    // 卡余额不足 - 组合支付
    $card_money = $card['yu_money'];
    $need_pay = bcsub($discounted_total_price, $card['yu_money'], 2);
    $pay_type = 3; // 组合支付
}
```

## 订单创建逻辑（createOrder）

订单创建阶段执行实际的支付处理和订单生成。

### 1. 卡支付处理（processCardPayment）

这是订单创建中最关键的支付逻辑处理方法：

```php
private function processCardPayment($total_price, $card, $user_id, $order_no, $clientkeynum, $card_discount = 1)
{
    $card_price = 0;
    $pay_type = 1; // 1微信支付 2卡支付 3组合支付
    $is_payed = 0;
    $status = 0;
    $need_pay = $total_price;
    $original_total_price = $total_price; // 保存原始总价
    $discount_amount = 0; // 初始化折扣金额

    if (!empty($card)) {
        // 对总价应用卡折扣
        if ($card_discount < 1) {
            $discounted_total_price = round($total_price * $card_discount, 2);
            $discount_amount = $total_price - $discounted_total_price;
            $total_price = $discounted_total_price;
            $need_pay = $total_price; // 更新需要支付的金额为折扣后的金额
        }

        // 处理卡支付逻辑
        if ($total_price - $card['yu_money'] <= 0) {
            // 卡内余额足够支付
            $use_price = $total_price;
            $remaining = $card['yu_money'] - $total_price;
            $this->updateCardBalance($card, $remaining, $order_no, $use_price, $user_id, $clientkeynum);

            $card_price += $total_price;
            $total_price = 0;
            $pay_type = 2;
        } else {
            // 卡内余额不足，需要组合支付
            $use_price = $card['yu_money'];
            $this->updateCardBalance($card, 0, $order_no, $use_price, $user_id, $clientkeynum);

            $total_price -= $card['yu_money'];
            $card_price += $card['yu_money'];
            $pay_type = 3;
        }
    }

    // 确定订单状态
    if ($total_price <= 0) {
        $status = 1;
        $need_pay = 0;
        $is_payed = 1;
        $this->createOrderLog($order_no, '订单支付', $clientkeynum);
    }

    return [
        'card_price' => $card_price,
        'need_pay' => $need_pay,
        'is_payed' => $is_payed,
        'status' => $status,
        'pay_type' => $pay_type,
        'original_price' => $original_total_price,
        'discount_amount' => $discount_amount
    ];
}
``` 