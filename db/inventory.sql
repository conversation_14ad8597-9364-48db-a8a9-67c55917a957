-- ----------------------------
-- Table structure for inventory_log
-- ----------------------------
DROP TABLE IF EXISTS `inventory_log`;
CREATE TABLE `inventory_log`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT,
    `clientkeynum`    varchar(35)    NOT NULL COMMENT '平台客户唯一标识',
    `shop_id`         int(11) DEFAULT NULL COMMENT '门店ID，为空表示平台库存变动',
    `product_id`      int(11) NOT NULL COMMENT '商品ID',
    `inventory_id`    int(11) NOT NULL COMMENT '商品库存ID（规格ID）',
    `before_quantity` decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '变动前数量',
    `change_quantity` decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '变动数量',
    `after_quantity`  decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '变动后数量',
    `change_type`     tinyint(1) NOT NULL COMMENT '变动类型：1-入库，2-出库，3-调拨入，4-调拨出，5-销售，6-退货，7-盘点，8-其他',
    `related_id`      int(11) DEFAULT NULL COMMENT '关联单据ID',
    `related_no`      varchar(32)             DEFAULT NULL COMMENT '关联单据号',
    `remark`          varchar(255)            DEFAULT NULL COMMENT '备注',
    `operator`        varchar(50)             DEFAULT NULL COMMENT '操作人',
    `created_at`      datetime                DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `unit`            varchar(20)             DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    KEY               `idx_clientkeynum` (`clientkeynum`) USING BTREE,
    KEY               `idx_shop_id` (`shop_id`) USING BTREE,
    KEY               `idx_product_id` (`product_id`) USING BTREE,
    KEY               `idx_inventory_id` (`inventory_id`) USING BTREE,
    KEY               `idx_change_type` (`change_type`) USING BTREE,
    KEY               `idx_related_no` (`related_no`) USING BTREE,
    KEY               `idx_created_at` (`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=53140 DEFAULT CHARSET=utf8mb4 COMMENT='库存变动日志表';



-- ----------------------------
-- Table structure for inventory_order
-- ----------------------------
DROP TABLE IF EXISTS `inventory_order`;
CREATE TABLE `inventory_order`
(
    `id`               int(11) NOT NULL AUTO_INCREMENT,
    `order_no`         varchar(32)    NOT NULL COMMENT '单据编号',
    `clientkeynum`     varchar(35)    NOT NULL COMMENT '平台客户唯一标识',
    `shop_id`          int(11) DEFAULT NULL COMMENT '门店ID，为空表示平台操作',
    `supplier_name`    varchar(50)             DEFAULT NULL COMMENT '供应商名称（入库时使用）',
    `member_id`        int(11) DEFAULT NULL COMMENT '会员ID（销售出库时使用）',
    `order_type`       tinyint(1) NOT NULL DEFAULT '1' COMMENT '单据类型：1-入库，2-出库，3-销售出库',
    `business_type`    tinyint(1) NOT NULL DEFAULT '1' COMMENT '业务类型：入库(1-采购入库，2-调拨入库，3-退货入库，4-其他入库)；出库(1-销售出库，2-调拨出库，3-报损出库，4-其他出库)',
    `total_amount`     decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '总金额',
    `discount_amount`  decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额（销售出库时使用）',
    `actual_amount`    decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '实际金额（销售出库时使用）',
    `related_order_no` varchar(32)             DEFAULT NULL COMMENT '关联单号（如采购单号、销售单号、订单号）',
    `status`           tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-草稿，1-已提交，2-已审核，3-已完成，-1-已取消',
    `remark`           varchar(255)            DEFAULT NULL COMMENT '备注',
    `created_by`       varchar(50)             DEFAULT NULL COMMENT '创建人',
    `reviewed_by`      varchar(50)             DEFAULT NULL COMMENT '审核人',
    `reviewed_time`    datetime                DEFAULT NULL COMMENT '审核时间',
    `completed_time`   datetime                DEFAULT NULL COMMENT '完成时间（入库/出库时间）',
    `created_at`       datetime                DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       datetime                DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_order_no` (`order_no`) USING BTREE,
    KEY                `idx_clientkeynum` (`clientkeynum`) USING BTREE,
    KEY                `idx_shop_id` (`shop_id`) USING BTREE,
    KEY                `idx_member_id` (`member_id`) USING BTREE,
    KEY                `idx_order_type` (`order_type`) USING BTREE,
    KEY                `idx_business_type` (`business_type`) USING BTREE,
    KEY                `idx_related_order_no` (`related_order_no`) USING BTREE,
    KEY                `idx_status` (`status`) USING BTREE,
    KEY                `idx_created_at` (`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=933 DEFAULT CHARSET=utf8mb4 COMMENT='库存单主表（合并入库、出库和销售出库）';

-- ----------------------------
-- Table structure for inventory_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `inventory_order_detail`;
CREATE TABLE `inventory_order_detail`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT,
    `order_id`     int(11) NOT NULL COMMENT '库存单ID',
    `order_no`     varchar(32)    NOT NULL COMMENT '库存单号',
    `order_type`   tinyint(1) NOT NULL DEFAULT '1' COMMENT '单据类型：1-入库，2-出库，3-销售出库',
    `product_id`   int(11) NOT NULL COMMENT '商品ID',
    `inventory_id` int(11) NOT NULL COMMENT '商品库存ID（规格ID）',
    `quantity`     decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '数量',
    `price`        decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '单价',
    `discount`     decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '折扣（销售出库时使用）',
    `amount`       decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '金额',
    `remark`       varchar(255)            DEFAULT NULL COMMENT '备注',
    `created_at`   datetime                DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `shop_id`      int(11) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    KEY            `idx_order_id` (`order_id`) USING BTREE,
    KEY            `idx_order_no` (`order_no`) USING BTREE,
    KEY            `idx_order_type` (`order_type`) USING BTREE,
    KEY            `idx_product_id` (`product_id`) USING BTREE,
    KEY            `idx_inventory_id` (`inventory_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1735 DEFAULT CHARSET=utf8mb4 COMMENT='库存单明细表（合并入库明细、出库明细和销售出库明细）';

-- ----------------------------
-- Table structure for order
-- ----------------------------
DROP TABLE IF EXISTS `order`;
CREATE TABLE `order`
(
    `id`                    int(11) NOT NULL AUTO_INCREMENT,
    `clientkeynum`          varchar(35) NOT NULL,
    `order_no`              varchar(20) NOT NULL,
    `shop_id`               int(11) NOT NULL,
    `user_id`               int(11) NOT NULL DEFAULT '0' COMMENT '用户id  为0则证明当前订单为门店端创建并支付方式为线下结算',
    `status`                tinyint(4) DEFAULT '0' COMMENT '0待支付 1待审核 2已接单 3正在配送   100已完成   -1 取消 -2退款',
    `type`                  tinyint(4) DEFAULT NULL COMMENT '1 自提   2配送 3 线下结算',
    `address_json`          text,
    `product_price`         decimal(10, 2) DEFAULT '0.00' COMMENT '商品价格',
    `shipping_price`        decimal(10, 2) DEFAULT '0.00' COMMENT '运费',
    `card_price`            decimal(10, 2) DEFAULT '0.00' COMMENT '卡支付金额',
    `price`                 decimal(10, 2) DEFAULT '0.00' COMMENT '微信支付金额',
    `real_price`            decimal(10, 2) DEFAULT '0.00' COMMENT '微信实付金额',
    `origin_price`          decimal(10, 2) DEFAULT '0.00' COMMENT '总支付金额',
    `need_pay_price`        decimal(10, 2) DEFAULT '0.00' COMMENT '需要微信支付的金额',
    `discount_amount`       decimal(10, 2) DEFAULT '0.00' COMMENT '折扣金额',
    `pay_type`              tinyint(4) DEFAULT '1' COMMENT '1 微信支付 2卡支付 3组合支付 4现金支付',
    `add_time`              datetime       DEFAULT NULL,
    `update_time`           datetime       DEFAULT NULL,
    `remark`                varchar(255)   DEFAULT NULL COMMENT '备注',
    `pay_time`              datetime       DEFAULT NULL COMMENT '支付时间',
    `transaction_id`        varchar(50)    DEFAULT NULL,
    `phone`                 varchar(20)    DEFAULT NULL,
    `name`                  varchar(50)    DEFAULT NULL,
    `is_free_shipping_free` tinyint(4) DEFAULT '0' COMMENT '是否满减运费  0否   1是',
    `cancel_time`           datetime       DEFAULT NULL,
    `refund_status`         tinyint(1) DEFAULT '0' COMMENT '退款状态：0-无退款，1-退款中，2-退款成功，3-退款失败',
    `refund_amount`         decimal(10, 2) DEFAULT '0.00' COMMENT '退款金额',
    `qrcode`                varchar(255)   DEFAULT NULL COMMENT '核销二维码',
    `verify_code`           varchar(20)    DEFAULT NULL,
    `pickup_time`           varchar(50)    DEFAULT NULL COMMENT '自提时间',
    `product_info`          text,
    `pickup_date`           date           DEFAULT NULL,
    `after_sales_status`    tinyint(1) NOT NULL DEFAULT '0' COMMENT '售后状态：0-无售后，1-有售后进行中，2-售后已完成',
    `pickup_number`         int(3) DEFAULT NULL COMMENT '拣货编号(1-999)',
    `product_total`         int(11) DEFAULT '0' COMMENT '当前订单商品总数',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=973 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for order_detail
-- ----------------------------
DROP TABLE IF EXISTS `order_detail`;
CREATE TABLE `order_detail`
(
    `id`                   int(11) NOT NULL AUTO_INCREMENT,
    `clientkeynum`         varchar(35)    NOT NULL,
    `order_no`             varchar(255)   NOT NULL,
    `shop_id`              int(11) NOT NULL,
    `user_id`              int(11) NOT NULL DEFAULT '0' COMMENT '用户id  为0则证明当前订单为门店端创建并支付方式为线下结算',
    `product_json`         text,
    `inventory_json`       text,
    `price`                decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '单价',
    `amount`               decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '小计金额',
    `add_time`             datetime                DEFAULT NULL,
    `update_time`          datetime                DEFAULT NULL,
    `number`               int(11) DEFAULT NULL,
    `after_sales_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '已申请售后数量',
    `refund_quantity`      int(11) NOT NULL DEFAULT '0' COMMENT '已退款数量',
    `can_after_sales`      tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可申请售后：0-不可以，1-可以',
    `actual_weight`        decimal(8, 3)           DEFAULT NULL COMMENT '实际重量（计重秤提供），仅计量商品使用',
    `weight_unit_price`    decimal(10, 2)          DEFAULT NULL COMMENT '单位价格（计重秤计算），仅计量商品使用',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1565 DEFAULT CHARSET=utf8mb4;


-- ----------------------------
-- Table structure for products
-- ----------------------------
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT,
    `clientkeynum` varchar(35) NOT NULL,
    `category_id`  int(11) DEFAULT NULL COMMENT '分类id',
    `title`        varchar(255)   DEFAULT NULL COMMENT '标题',
    `subtitle`     varchar(255)   DEFAULT NULL COMMENT '副标题',
    `cover`        varchar(255)   DEFAULT NULL COMMENT '封面',
    `banner`       text,
    `kucun`        int(11) DEFAULT '0' COMMENT '库存',
    `price`        decimal(10, 2) DEFAULT '0.00',
    `market_price` decimal(10, 2) DEFAULT NULL,
    `inventory_id` int(11) DEFAULT '0',
    `ord`          int(11) DEFAULT '0',
    `state`        tinyint(4) DEFAULT '0' COMMENT '状态: 0-保存  1-上架  -1-下架',
    `is_attribute` tinyint(1) DEFAULT '1' COMMENT '是否开启多规格：0-不开启 1-开启',
    `content`      text,
    `state_desc`   varchar(255)   DEFAULT NULL COMMENT '状态',
    `update_time`  datetime       DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `created_at`   varchar(255)   DEFAULT NULL,
    `product_type` tinyint(1) DEFAULT '1' COMMENT '商品类型：1-普通商品，2-计量商品，3-赠品',
    PRIMARY KEY (`id`) USING BTREE,
    KEY            `idx_product_type` (`product_type`)
) ENGINE=InnoDB AUTO_INCREMENT=317 DEFAULT CHARSET=utf8mb4 COMMENT='产品表';

-- ----------------------------
-- Table structure for product_inventory
-- ----------------------------
DROP TABLE IF EXISTS `product_inventory`;
CREATE TABLE `product_inventory`
(
    `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `clientkeynum` varchar(35) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `sn`           varchar(32) COLLATE utf8mb4_unicode_ci  DEFAULT '' COMMENT '商品编码',
    `title`        varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `spec_ids`     varchar(50) COLLATE utf8mb4_unicode_ci  DEFAULT '' COMMENT '属性组id',
    `attr_ids`     varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '属性id',
    `attr_vals`    varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '属性值',
    `product_id`   bigint(20) NOT NULL DEFAULT '0',
    `image`        varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '规格图片',
    `price`        decimal(10, 2)                          DEFAULT '0.00' COMMENT '价格',
    `sale_num`     int(11) DEFAULT '0' COMMENT '销量',
    `if_default`   int(11) NOT NULL DEFAULT '0' COMMENT '默认规格',
    `created_at`   datetime                                DEFAULT NULL,
    `update_time`  datetime                                DEFAULT NULL,
    `data`         varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `status`       tinyint(4) DEFAULT NULL,
    `weight_unit`  varchar(10) COLLATE utf8mb4_unicode_ci  DEFAULT 'kg' COMMENT '重量单位：kg、g、斤、两',
    `min_weight`   decimal(8, 3)                           DEFAULT '0.100' COMMENT '最小称重',
    `weight_step`  decimal(8, 3)                           DEFAULT '0.100' COMMENT '称重步长',
    `barcode`      varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '条形码',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `sn` (`sn`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=318 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for shop_product_inventory
-- ----------------------------
DROP TABLE IF EXISTS `shop_product_inventory`;
CREATE TABLE `shop_product_inventory`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT,
    `shop_id`      int(11) NOT NULL,
    `product_id`   int(11) NOT NULL,
    `inventory_id` int(11) NOT NULL,
    `stock`        int(11) DEFAULT NULL COMMENT '库存',
    `add_time`     datetime       DEFAULT NULL,
    `update_time`  datetime       DEFAULT NULL,
    `clientkeynum` varchar(35)    DEFAULT NULL,
    `weight_stock` decimal(10, 3) DEFAULT NULL COMMENT '重量库存（kg），仅计量商品使用',
    `stock_unit`   varchar(10)    DEFAULT 'pcs' COMMENT '库存单位：pcs-件数，kg-重量',
    PRIMARY KEY (`id`) USING BTREE,
    KEY            `idx_stock_unit` (`stock_unit`)
) ENGINE=InnoDB AUTO_INCREMENT=1268 DEFAULT CHARSET=utf8mb4;



INSERT INTO `fresh_store`.`inventory_order` (`id`, `order_no`, `clientkeynum`, `shop_id`, `supplier_name`, `member_id`,
                                             `order_type`, `business_type`, `total_amount`, `discount_amount`,
                                             `actual_amount`, `related_order_no`, `status`, `remark`, `created_by`,
                                             `reviewed_by`, `reviewed_time`, `completed_time`, `created_at`,
                                             `updated_at`)
VALUES (655, 'SO202506271714540742', '668AD46FFC93C10F32C8F6D0C6B1F6EB', 14, NULL, 0, 3, 1, 0.00, 0.00, 0.00,
        'D202506271714933825', 3, '订单销售出库：D202506271714933825', 'system', NULL, NULL, '2025-06-27 17:14:54',
        '2025-06-27 17:14:54', '2025-07-17 01:11:54');

INSERT INTO `fresh_store`.`inventory_order_detail` (`id`, `order_id`, `order_no`, `order_type`, `product_id`,
                                                    `inventory_id`, `quantity`, `price`, `discount`, `amount`, `remark`,
                                                    `created_at`, `shop_id`)
VALUES (1054, 607, 'SO202506271714540742', 3, 292, 297, 0.18, 71.28, 0.00, 12.83, NULL, '2025-06-27 17:14:54', 14);
INSERT INTO `fresh_store`.`inventory_order_detail` (`id`, `order_id`, `order_no`, `order_type`, `product_id`,
                                                    `inventory_id`, `quantity`, `price`, `discount`, `amount`, `remark`,
                                                    `created_at`, `shop_id`)
VALUES (1055, 607, 'SO202506271714540742', 3, 292, 297, 0.17, 67.32, 0.00, 11.44, NULL, '2025-06-27 17:14:54', 14);
INSERT INTO `fresh_store`.`inventory_order_detail` (`id`, `order_id`, `order_no`, `order_type`, `product_id`,
                                                    `inventory_id`, `quantity`, `price`, `discount`, `amount`, `remark`,
                                                    `created_at`, `shop_id`)
VALUES (1056, 607, 'SO202506271714540742', 3, 292, 297, 0.17, 67.32, 0.00, 11.44, NULL, '2025-06-27 17:14:54', 14);
INSERT INTO `fresh_store`.`inventory_order_detail` (`id`, `order_id`, `order_no`, `order_type`, `product_id`,
                                                    `inventory_id`, `quantity`, `price`, `discount`, `amount`, `remark`,
                                                    `created_at`, `shop_id`)
VALUES (1057, 607, 'SO202506271714540742', 3, 308, 311, 1.00, 0.00, 0.00, 0.00, NULL, '2025-06-27 17:14:54', 14);
INSERT INTO `fresh_store`.`inventory_order_detail` (`id`, `order_id`, `order_no`, `order_type`, `product_id`,
                                                    `inventory_id`, `quantity`, `price`, `discount`, `amount`, `remark`,
                                                    `created_at`, `shop_id`)
VALUES (1161, 655, 'SO202506271714540742', 3, 292, 297, 0.18, 71.28, 0.00, 12.83, NULL, '2025-06-27 17:14:54', 14);
INSERT INTO `fresh_store`.`inventory_order_detail` (`id`, `order_id`, `order_no`, `order_type`, `product_id`,
                                                    `inventory_id`, `quantity`, `price`, `discount`, `amount`, `remark`,
                                                    `created_at`, `shop_id`)
VALUES (1162, 655, 'SO202506271714540742', 3, 292, 297, 0.17, 67.32, 0.00, 11.44, NULL, '2025-06-27 17:14:54', 14);
INSERT INTO `fresh_store`.`inventory_order_detail` (`id`, `order_id`, `order_no`, `order_type`, `product_id`,
                                                    `inventory_id`, `quantity`, `price`, `discount`, `amount`, `remark`,
                                                    `created_at`, `shop_id`)
VALUES (1163, 655, 'SO202506271714540742', 3, 292, 297, 0.17, 67.32, 0.00, 11.44, NULL, '2025-06-27 17:14:54', 14);
INSERT INTO `fresh_store`.`inventory_order_detail` (`id`, `order_id`, `order_no`, `order_type`, `product_id`,
                                                    `inventory_id`, `quantity`, `price`, `discount`, `amount`, `remark`,
                                                    `created_at`, `shop_id`)
VALUES (1164, 655, 'SO202506271714540742', 3, 308, 311, 1.00, 0.00, 0.00, 0.00, NULL, '2025-06-27 17:14:54', 14);

INSERT INTO `fresh_store`.`order` (`id`, `clientkeynum`, `order_no`, `shop_id`, `user_id`, `status`, `type`,
                                   `address_json`, `product_price`, `shipping_price`, `card_price`, `price`,
                                   `real_price`, `origin_price`, `need_pay_price`, `discount_amount`, `pay_type`,
                                   `add_time`, `update_time`, `remark`, `pay_time`, `transaction_id`, `phone`, `name`,
                                   `is_free_shipping_free`, `cancel_time`, `refund_status`, `refund_amount`, `qrcode`,
                                   `verify_code`, `pickup_time`, `product_info`, `pickup_date`, `after_sales_status`,
                                   `pickup_number`, `offline_price`)
VALUES (742, '668AD46FFC93C10F32C8F6D0C6B1F6EB', 'D202506271714933825', 14, 0, 100, 3, NULL, 205.92, 0.00, 0.00, 205.92,
        0.00, 205.92, 0.00, 0.00, 4, '2025-06-27 17:14:54', '2025-07-15 10:11:46', NULL, '2025-06-27 17:14:54', NULL,
        NULL, NULL, 0, NULL, 0, 0.00, NULL, NULL, '17:14',
        '澳洲冰鲜谷饲安格斯牛肋条肉：0.180kg*396.00元/kg = 71.28元<br>澳洲冰鲜谷饲安格斯牛肋条肉：0.170kg*396.00元/kg = 67.32元<br>澳洲冰鲜谷饲安格斯牛肋条肉：0.170kg*396.00元/kg = 67.32元<br>z亦佰味安格斯上脑230g - 230g：1件 * 0.00元/件 = 0元<br>',
        '2025-06-27', 0, 11, 205.92);

INSERT INTO `fresh_store`.`order_detail` (`id`, `clientkeynum`, `order_no`, `shop_id`, `user_id`, `product_json`,
                                          `inventory_json`, `price`, `amount`, `add_time`, `update_time`, `number`,
                                          `after_sales_quantity`, `refund_quantity`, `can_after_sales`, `actual_weight`,
                                          `weight_unit_price`)
VALUES (949, '668AD46FFC93C10F32C8F6D0C6B1F6EB', 'D202506271714933825', 14, 0,
        '{\"id\":292,\"clientkeynum\":\"668AD46FFC93C10F32C8F6D0C6B1F6EB\",\"category_id\":18,\"title\":\"\\u6fb3\\u6d32\\u51b0\\u9c9c\\u8c37\\u9972\\u5b89\\u683c\\u65af\\u725b\\u808b\\u6761\\u8089\",\"subtitle\":null,\"cover\":\"https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/static\\/upload\\/LZTPT\\/images\\/202506\\/B1B93B971E8E28991F0E4901A6212FCD.jpg\",\"banner\":null,\"kucun\":0,\"price\":\"396.00\",\"market_price\":null,\"inventory_id\":0,\"ord\":0,\"state\":1,\"is_attribute\":0,\"content\":\"https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/static\\/upload\\/LZTPT\\/images\\/202506\\/B5868F837620EBFF25CEA1B1A4C62AC6.jpg|https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/static\\/upload\\/LZTPT\\/images\\/202506\\/5D756E12849C56614F4EDE9DDE156214.jpg\",\"state_desc\":null,\"update_time\":\"2025-06-27 01:38:26\",\"created_at\":null,\"product_type\":2}',
        '{\"id\":297,\"clientkeynum\":\"668AD46FFC93C10F32C8F6D0C6B1F6EB\",\"sn\":\"1004\",\"title\":\"\\u6fb3\\u6d32\\u51b0\\u9c9c\\u8c37\\u9972\\u5b89\\u683c\\u65af\\u725b\\u808b\\u6761\\u8089\",\"spec_ids\":\"\",\"attr_ids\":\"\",\"attr_vals\":\"\",\"product_id\":292,\"image\":\"\",\"price\":\"396.00\",\"sale_num\":0,\"if_default\":0,\"created_at\":null,\"update_time\":\"2025-06-25 12:57:22\",\"data\":\"\",\"status\":1,\"weight_unit\":\"kg\",\"min_weight\":\"0.100\",\"weight_step\":\"0.100\",\"barcode\":\"https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/public\\/uploads\\/barcode\\/barcode_1004.png\"}',
        71.28, 71.28, '2025-06-27 17:14:54', NULL, 1, 0, 0, 1, 0.180, 396.00);
INSERT INTO `fresh_store`.`order_detail` (`id`, `clientkeynum`, `order_no`, `shop_id`, `user_id`, `product_json`,
                                          `inventory_json`, `price`, `amount`, `add_time`, `update_time`, `number`,
                                          `after_sales_quantity`, `refund_quantity`, `can_after_sales`, `actual_weight`,
                                          `weight_unit_price`)
VALUES (950, '668AD46FFC93C10F32C8F6D0C6B1F6EB', 'D202506271714933825', 14, 0,
        '{\"id\":292,\"clientkeynum\":\"668AD46FFC93C10F32C8F6D0C6B1F6EB\",\"category_id\":18,\"title\":\"\\u6fb3\\u6d32\\u51b0\\u9c9c\\u8c37\\u9972\\u5b89\\u683c\\u65af\\u725b\\u808b\\u6761\\u8089\",\"subtitle\":null,\"cover\":\"https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/static\\/upload\\/LZTPT\\/images\\/202506\\/B1B93B971E8E28991F0E4901A6212FCD.jpg\",\"banner\":null,\"kucun\":0,\"price\":\"396.00\",\"market_price\":null,\"inventory_id\":0,\"ord\":0,\"state\":1,\"is_attribute\":0,\"content\":\"https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/static\\/upload\\/LZTPT\\/images\\/202506\\/B5868F837620EBFF25CEA1B1A4C62AC6.jpg|https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/static\\/upload\\/LZTPT\\/images\\/202506\\/5D756E12849C56614F4EDE9DDE156214.jpg\",\"state_desc\":null,\"update_time\":\"2025-06-27 01:38:26\",\"created_at\":null,\"product_type\":2}',
        '{\"id\":297,\"clientkeynum\":\"668AD46FFC93C10F32C8F6D0C6B1F6EB\",\"sn\":\"1004\",\"title\":\"\\u6fb3\\u6d32\\u51b0\\u9c9c\\u8c37\\u9972\\u5b89\\u683c\\u65af\\u725b\\u808b\\u6761\\u8089\",\"spec_ids\":\"\",\"attr_ids\":\"\",\"attr_vals\":\"\",\"product_id\":292,\"image\":\"\",\"price\":\"396.00\",\"sale_num\":0,\"if_default\":0,\"created_at\":null,\"update_time\":\"2025-06-25 12:57:22\",\"data\":\"\",\"status\":1,\"weight_unit\":\"kg\",\"min_weight\":\"0.100\",\"weight_step\":\"0.100\",\"barcode\":\"https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/public\\/uploads\\/barcode\\/barcode_1004.png\"}',
        67.32, 67.32, '2025-06-27 17:14:54', NULL, 1, 0, 0, 1, 0.170, 396.00);
INSERT INTO `fresh_store`.`order_detail` (`id`, `clientkeynum`, `order_no`, `shop_id`, `user_id`, `product_json`,
                                          `inventory_json`, `price`, `amount`, `add_time`, `update_time`, `number`,
                                          `after_sales_quantity`, `refund_quantity`, `can_after_sales`, `actual_weight`,
                                          `weight_unit_price`)
VALUES (951, '668AD46FFC93C10F32C8F6D0C6B1F6EB', 'D202506271714933825', 14, 0,
        '{\"id\":292,\"clientkeynum\":\"668AD46FFC93C10F32C8F6D0C6B1F6EB\",\"category_id\":18,\"title\":\"\\u6fb3\\u6d32\\u51b0\\u9c9c\\u8c37\\u9972\\u5b89\\u683c\\u65af\\u725b\\u808b\\u6761\\u8089\",\"subtitle\":null,\"cover\":\"https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/static\\/upload\\/LZTPT\\/images\\/202506\\/B1B93B971E8E28991F0E4901A6212FCD.jpg\",\"banner\":null,\"kucun\":0,\"price\":\"396.00\",\"market_price\":null,\"inventory_id\":0,\"ord\":0,\"state\":1,\"is_attribute\":0,\"content\":\"https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/static\\/upload\\/LZTPT\\/images\\/202506\\/B5868F837620EBFF25CEA1B1A4C62AC6.jpg|https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/static\\/upload\\/LZTPT\\/images\\/202506\\/5D756E12849C56614F4EDE9DDE156214.jpg\",\"state_desc\":null,\"update_time\":\"2025-06-27 01:38:26\",\"created_at\":null,\"product_type\":2}',
        '{\"id\":297,\"clientkeynum\":\"668AD46FFC93C10F32C8F6D0C6B1F6EB\",\"sn\":\"1004\",\"title\":\"\\u6fb3\\u6d32\\u51b0\\u9c9c\\u8c37\\u9972\\u5b89\\u683c\\u65af\\u725b\\u808b\\u6761\\u8089\",\"spec_ids\":\"\",\"attr_ids\":\"\",\"attr_vals\":\"\",\"product_id\":292,\"image\":\"\",\"price\":\"396.00\",\"sale_num\":0,\"if_default\":0,\"created_at\":null,\"update_time\":\"2025-06-25 12:57:22\",\"data\":\"\",\"status\":1,\"weight_unit\":\"kg\",\"min_weight\":\"0.100\",\"weight_step\":\"0.100\",\"barcode\":\"https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/public\\/uploads\\/barcode\\/barcode_1004.png\"}',
        67.32, 67.32, '2025-06-27 17:14:54', NULL, 1, 0, 0, 1, 0.170, 396.00);
INSERT INTO `fresh_store`.`order_detail` (`id`, `clientkeynum`, `order_no`, `shop_id`, `user_id`, `product_json`,
                                          `inventory_json`, `price`, `amount`, `add_time`, `update_time`, `number`,
                                          `after_sales_quantity`, `refund_quantity`, `can_after_sales`, `actual_weight`,
                                          `weight_unit_price`)
VALUES (952, '668AD46FFC93C10F32C8F6D0C6B1F6EB', 'D202506271714933825', 14, 0,
        '{\"id\":308,\"clientkeynum\":\"668AD46FFC93C10F32C8F6D0C6B1F6EB\",\"category_id\":15,\"title\":\"z\\u4ea6\\u4f70\\u5473\\u5b89\\u683c\\u65af\\u4e0a\\u8111230g\",\"subtitle\":null,\"cover\":\"https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/static\\/upload\\/LZTPT\\/images\\/202506\\/5E80E87D50D73849CF105783994F67B5.png\",\"banner\":null,\"kucun\":0,\"price\":\"0.00\",\"market_price\":null,\"inventory_id\":0,\"ord\":0,\"state\":1,\"is_attribute\":1,\"content\":\"\",\"state_desc\":null,\"update_time\":\"2025-06-26 20:20:10\",\"created_at\":null,\"product_type\":3}',
        '{\"id\":311,\"clientkeynum\":\"668AD46FFC93C10F32C8F6D0C6B1F6EB\",\"sn\":\"6970727641111\",\"title\":\"230g\",\"spec_ids\":\"\",\"attr_ids\":\"\",\"attr_vals\":\"\",\"product_id\":308,\"image\":\"\",\"price\":\"0.00\",\"sale_num\":0,\"if_default\":0,\"created_at\":null,\"update_time\":\"2025-06-26 20:20:11\",\"data\":\"323\",\"status\":0,\"weight_unit\":\"kg\",\"min_weight\":\"0.100\",\"weight_step\":\"0.100\",\"barcode\":\"https:\\/\\/zs-jiandanshop.oss-cn-beijing.aliyuncs.com\\/public\\/uploads\\/barcode\\/barcode_6970727641111.png\"}',
        0.00, 0.00, '2025-06-27 17:14:54', NULL, 1, 0, 0, 1, NULL, NULL);
