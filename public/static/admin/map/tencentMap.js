
layui.define(["jquery", "form"], function (exports) {
    var map,markerLayer,longitudeName,latitudeName;

    var obj = {
        initMap: function (elementId,longitude=116.307484,latitude=39.984120,longitudeInputName='longitude',latitudeInputName='latitude') {
            longitudeName = longitudeInputName;
            latitudeName = latitudeInputName;
            //定义地图中心点坐标
            var center = new TMap.LatLng(latitude, longitude)
            //定义map变量，调用 TMap.Map() 构造函数创建地图
            map = new TMap.Map(document.getElementById(elementId), {
                center: center,//设置地图中心点坐标
                zoom: 17.2,   //设置地图缩放级别
                pitch: 43.5,  //设置俯仰角
                rotation: 45    //设置地图旋转角度
            });

            //创建并初始化MultiMarker点标记
            markerLayer = new TMap.MultiMarker({
                map: map,  //指定地图容器
                //样式定义
                styles: {
                    //创建一个styleId为"myStyle"的样式（styles的子属性名即为styleId）
                    "myStyle": new TMap.MarkerStyle({
                        "width": 25,  // 点标记样式宽度（像素）
                        "height": 35, // 点标记样式高度（像素）
                        // "src": '/static/common/image/default/icon_address.png',  //图片路径
                        //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
                        "anchor": { x: 16, y: 32 }
                    })
                },
                //点标记数据数组
                geometries: [{
                    "id": "1",   //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
                    "styleId": 'myStyle',  //指定样式id
                    "position": new TMap.LatLng(latitude, longitude),  //点标记坐标位置
                }
                ]
            });

            //Map实例创建后，通过on方法绑定点击事件
            map.on("click",obj.clickHandler)
        },
        clickHandler:function (evt) {
            var lat = evt.latLng.getLat().toFixed(6);
            var lng = evt.latLng.getLng().toFixed(6);

            obj.changeCoordinate(lat,lng);
            // 进行逆地理编码，获取地址信息
            obj.reverseGeocode(lat, lng);
        },
        changeCoordinate:function(lat,lng){
            //修改id为4的点标记的位置
            markerLayer.updateGeometries([
                {
                    "id": "1",
                    "styleId":"myStyle",
                    "position": new TMap.LatLng(lat, lng),
                }
            ])

            //修改地图中心点
            map.setCenter(new TMap.LatLng(lat,lng));
            //获取地图中心点
            // var centerLatLng = map.getCenter();

            $("input[name="+longitudeName+"]").val(lng);
            $("input[name="+latitudeName+"]").val(lat);
        },
        searchMap:function (address) {
            //正逆地址解析
            var geocoder = new TMap.service.Geocoder();
            //根据指定的文字地址转换为经纬度，并同时提供结构化的省市区地址信息
            geocoder.getLocation({address:address}).then((res)=>{
                if (res.status != 0) {
                    layui.layer.msg(res.message);
                    console.log('获取经纬度错误：',res);
                    return;
                }
                obj.changeCoordinate(res.result.location.lat,res.result.location.lng);
            }).catch((res)=>{
                layui.layer.msg(res.message);
                console.log('获取经纬度错误：',res)
            })
        },
        // 逆地理编码：根据经纬度获取地址信息
        reverseGeocode:function (lat, lng) {
            // 检查TMap.service是否可用
            if (!TMap.service || !TMap.service.Geocoder) {
                console.error('腾讯地图服务未正确加载，请检查API引入是否包含service库');
                layui.layer && layui.layer.msg('地图服务未正确加载');
                return;
            }

            var geocoder = new TMap.service.Geocoder();

            // 根据经纬度获取地址信息
            geocoder.getAddress({
                location: new TMap.LatLng(lat, lng)
            }).then((res) => {
                console.log('逆地理编码响应：', res);

                if (res.status != 0) {
                    console.error('逆地理编码错误：', res);
                    var errorMsg = res.message || '获取地址信息失败';
                    layui.layer && layui.layer.msg('获取地址失败：' + errorMsg);
                    return;
                }

                // 获取详细地址信息
                var addressInfo = res.result;
                var fullAddress = '';

                console.log('地址信息：', addressInfo);

                // 构建完整地址 - 按优先级尝试不同的地址格式
                if (addressInfo.formatted_addresses && addressInfo.formatted_addresses.recommend) {
                    // 使用推荐的格式化地址
                    fullAddress = addressInfo.formatted_addresses.recommend;
                    console.log('使用推荐地址：', fullAddress);
                } else if (addressInfo.formatted_addresses && addressInfo.formatted_addresses.standard) {
                    // 使用标准格式化地址
                    fullAddress = addressInfo.formatted_addresses.standard;
                    console.log('使用标准地址：', fullAddress);
                } else if (addressInfo.address) {
                    // 使用基础地址
                    fullAddress = addressInfo.address;
                    console.log('使用基础地址：', fullAddress);
                } else {
                    // 手动构建地址
                    var components = addressInfo.address_component;
                    if (components) {
                        fullAddress = (components.province || '') +
                                    (components.city || '') +
                                    (components.district || '') +
                                    (components.street || '') +
                                    (components.street_number || '');
                        console.log('手动构建地址：', fullAddress);
                    }
                }

                // 提取省市区信息
                var components = addressInfo.address_component;
                var province = '';
                var city = '';
                var district = '';

                if (components) {
                    province = components.province || '';
                    city = components.city || '';
                    district = components.district || '';

                    console.log('省份：', province);
                    console.log('城市：', city);
                    console.log('区县：', district);
                }

                // 更新地址输入框
                var addressInput = $("input[name=address]");
                if (addressInput.length > 0) {
                    if (fullAddress) {
                        addressInput.val(city + fullAddress);
                        console.log('地址已更新：',  city + fullAddress);
                        // layui.layer && layui.layer.msg('地址已自动填充');
                    } else {
                        console.warn('未能获取到有效地址信息');
                        layui.layer && layui.layer.msg('未能获取到地址信息');
                    }
                } else {
                    console.warn('未找到地址输入框 input[name=address]');
                }

                // 更新省市区输入框（如果存在）
                var provinceInput = $("input[name=province]");
                var cityInput = $("input[name=city]");
                var districtInput = $("input[name=district]");

                if (provinceInput.length > 0 && province) {
                    provinceInput.val(province);
                    console.log('省份已更新：', province);
                }

                if (cityInput.length > 0 && city) {
                    cityInput.val(city);
                    console.log('城市已更新：', city);
                }

                if (districtInput.length > 0 && district) {
                    districtInput.val(district);
                    console.log('区县已更新：', district);
                }

                // 也可以通过select下拉框更新省市区
                var provinceSelect = $("select[name=province]");
                var citySelect = $("select[name=city]");
                var districtSelect = $("select[name=district]");

                if (provinceSelect.length > 0 && province) {
                    // 尝试根据省份名称选择对应的option
                    provinceSelect.find('option').each(function() {
                        if ($(this).text().indexOf(province.replace('省', '').replace('市', '').replace('自治区', '')) !== -1) {
                            provinceSelect.val($(this).val()).trigger('change');
                            console.log('省份下拉框已更新：', province);
                            return false;
                        }
                    });
                }

                if (citySelect.length > 0 && city) {
                    // 延迟一下，等省份change事件完成后再设置城市
                    setTimeout(function() {
                        citySelect.find('option').each(function() {
                            if ($(this).text().indexOf(city.replace('市', '').replace('区', '').replace('县', '')) !== -1) {
                                citySelect.val($(this).val()).trigger('change');
                                console.log('城市下拉框已更新：', city);
                                return false;
                            }
                        });
                    }, 500);
                }

                if (districtSelect.length > 0 && district) {
                    // 延迟一下，等城市change事件完成后再设置区县
                    setTimeout(function() {
                        districtSelect.find('option').each(function() {
                            if ($(this).text().indexOf(district.replace('区', '').replace('县', '').replace('镇', '')) !== -1) {
                                districtSelect.val($(this).val()).trigger('change');
                                console.log('区县下拉框已更新：', district);
                                return false;
                            }
                        });
                    }, 1000);
                }
            }).catch((error) => {
                console.error('逆地理编码异常：', error);
                var errorMsg = error.message || '网络请求失败';
                layui.layer && layui.layer.msg('获取地址异常：' + errorMsg);
            });
        },

        // 获取省市区信息的独立方法
        getProvinceCity: function(lat, lng, callback) {
            if (!TMap.service || !TMap.service.Geocoder) {
                console.error('腾讯地图服务未正确加载');
                callback && callback(null);
                return;
            }

            var geocoder = new TMap.service.Geocoder();

            geocoder.getAddress({
                location: new TMap.LatLng(lat, lng)
            }).then((res) => {
                if (res.status != 0) {
                    console.error('获取省市区信息失败：', res);
                    callback && callback(null);
                    return;
                }

                var components = res.result.address_component;
                var locationInfo = {
                    province: components ? (components.province || '') : '',
                    city: components ? (components.city || '') : '',
                    district: components ? (components.district || '') : '',
                    street: components ? (components.street || '') : '',
                    street_number: components ? (components.street_number || '') : '',
                    full_address: ''
                };

                // 构建完整地址
                if (res.result.formatted_addresses && res.result.formatted_addresses.recommend) {
                    locationInfo.full_address = res.result.formatted_addresses.recommend;
                } else if (res.result.formatted_addresses && res.result.formatted_addresses.standard) {
                    locationInfo.full_address = res.result.formatted_addresses.standard;
                } else if (res.result.address) {
                    locationInfo.full_address = res.result.address;
                } else {
                    locationInfo.full_address = locationInfo.province + locationInfo.city +
                                             locationInfo.district + locationInfo.street +
                                             locationInfo.street_number;
                }

                console.log('省市区信息：', locationInfo);
                callback && callback(locationInfo);
            }).catch((error) => {
                console.error('获取省市区信息异常：', error);
                callback && callback(null);
            });
        }
    };
    exports("tencentMap", obj);
});




