# 结算台计量商品功能实现说明

## 功能概述

已成功为结算台系统添加了计量商品支持功能。计量商品使用特殊的20位二维码格式，系统能够自动识别并正确处理计量商品的扫描、添加、结算等操作。

## 核心功能

### 1. 二维码识别和解析

- **自动识别**：当扫描的二维码长度为20位时，系统自动识别为计量商品
- **数据解析**：将20位二维码分解为4段5位数字：
  - 第1段：商品SN编号
  - 第2段：商品重量（除以100得到实际重量，单位kg）
  - 第3段：单价（除以100得到实际单价，单位元/kg）
  - 第4段：总价（除以100得到实际总价，单位元）
- **价格验证**：验证重量×单价是否等于总价，确保数据准确性

### 2. 商品扫描功能

- **普通商品**：保持原有扫描逻辑（69开头的编码）
- **计量商品**：新增handleWeightedProduct方法处理20位编码
- **商品验证**：确保扫描的商品确实是计量商品（product_type = 1）
- **库存检查**：检查重量库存（weight_stock）是否充足

### 3. 结算台管理

- **独立添加**：计量商品每次扫描都作为独立商品项添加
- **智能合并**：普通商品相同规格自动合并数量
- **区分显示**：通过is_weighted字段区分商品类型
- **精确移除**：支持通过item_index精确移除特定计量商品

### 4. 库存管理

- **普通商品**：检查和扣减stock字段（件数）
- **计量商品**：检查和扣减weight_stock字段（重量）
- **双重验证**：同时验证商品类型和库存类型的匹配

### 5. 价格计算

- **普通商品**：使用商品价格 × 数量
- **计量商品**：使用扫描得到的总价（固定数量为1）
- **统一计算**：calculateTotal方法统一处理两种商品类型

## 修改的文件

### application/store/controller/CheckoutController.php

#### 新增方法
- `handleWeightedProduct()` - 处理计量商品扫描逻辑

#### 修改方法
- `scanProduct()` - 添加20位二维码识别和分发逻辑
- `addToCheckout()` - 支持计量商品参数和独立添加逻辑
- `removeItem()` - 支持精确移除计量商品
- `updateQuantity()` - 禁止修改计量商品数量
- `calculateTotal()` - 区分处理两种商品类型的价格计算

## 数据结构

### 扫描返回数据
```php
// 计量商品扫描响应增加字段
[
    'is_weighted' => true,
    'weight_info' => [
        'weight' => 1.50,
        'unit_price' => 5.01,
        'total_price' => 7.515,
        'weight_unit' => 'kg'
    ]
]
```

### 结算台商品数据
```php
// 计量商品在结算台中的数据结构
[
    'product_id' => 123,
    'inventory_id' => 456,
    'shop_inventory_id' => 789,
    'product_name' => '苹果',
    'spec_name' => '红富士苹果',
    'price' => 7.515,        // 使用总价作为商品价格
    'quantity' => 1,         // 固定为1
    'subtotal' => 7.515,
    'is_weighted' => true,   // 标识为计量商品
    'weight' => 1.50,        // 实际重量
    'unit_price' => 5.01,    // 单价
    'weight_unit' => 'kg'    // 重量单位
]
```

## API接口变化

### 新增请求参数（addToCheckout接口）
- `is_weighted` - 是否为计量商品
- `weight` - 商品重量
- `unit_price` - 单价
- `total_price` - 总价

### 新增请求参数（removeItem接口）
- `item_index` - 商品在结算台中的索引（用于精确移除）

## 业务逻辑特点

### 计量商品特殊处理
1. **不支持数量修改**：计量商品的重量是固定的，不允许修改数量
2. **独立计价**：每个计量商品都有独立的重量和价格
3. **精确库存**：使用重量库存进行精确管理
4. **自动验价**：系统自动验证重量×单价=总价

### 兼容性保证
1. **向下兼容**：原有普通商品功能保持不变
2. **统一接口**：新老商品类型使用相同的API接口
3. **智能识别**：系统自动识别商品类型，无需手动指定

## 使用示例

### 扫描计量商品
```
二维码：12345001500050150075
解析结果：
- 商品SN：12345
- 重量：1.50kg
- 单价：5.01元/kg
- 总价：7.515元
```

### 添加到结算台
```json
{
    "product_id": 123,
    "inventory_id": 456,
    "is_weighted": true,
    "weight": 1.50,
    "unit_price": 5.01,
    "total_price": 7.515
}
```

## 注意事项

1. **二维码格式**：确保计量商品的二维码严格按照20位格式生成
2. **价格精度**：系统支持小数点后3位的价格精度
3. **库存同步**：需要确保weight_stock字段与实际库存保持同步
4. **商品配置**：计量商品必须正确设置product_type=1和weight_unit字段

## 测试建议

1. **扫描测试**：测试各种20位二维码格式的识别和解析
2. **库存测试**：测试重量库存不足时的处理逻辑
3. **价格测试**：测试价格计算异常时的错误处理
4. **混合测试**：测试普通商品和计量商品混合添加的场景

这个功能实现完全兼容现有系统，为门店POS系统提供了完整的计量商品支持。 