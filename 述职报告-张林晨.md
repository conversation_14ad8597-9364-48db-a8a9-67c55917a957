# 述职报告

**核心业务系统开发与技术创新**

**汇报人：** 张林晨  
**日期：** 2025-8-8

---

## 项目一：订单拆分系统

一个基于ThinkPHP 5框架构建的大型订单处理系统，旨在解决电商企业的订单分发、供应商协同及售后管理等核心业务痛点。

### 成果

#### 第三方系统对接(10天)

成功实现了与多个主流ERP系统的API对接，包括：

| 系统名称 | 系统名称 | 系统名称 |
|---------|---------|---------|
| 聚水潭   | 吉客云   | 管易云   |

#### 统计功能(2天)

提供订单走势分析、客户维度统计，并通过定时任务每日自动更新数据。

#### 数据可视化(1天)

展示导入及发货订单的数量趋势，支持多维度数据对比和按日期、客户筛选。

### 系统架构优化与创新

- **数据库优化**
  - 针对高频查询字段建立了复合索引，提升查询性能。

- **微信生态集成**
  - 实现微信OAuth2.0授权登录及账号绑定。

---

## 项目二：外卖平台

一个基于ThinkPHP 5.1框架开发的企业级生鲜商城系统，分为小程序、门店小程序、后台、门店等多端应用，为生鲜电商业务提供完整的技术解决方案。

今年开始就已经前后端为我自己负责开发，金龙参与了一些页面美化工作。 

### 主要功能 (1)

#### 用户认证系统(3天)

集成微信登录，支持页面内嵌二维码登录和OAuth2.0网页授权。

#### 商品与库存管理(10天)

支持按"件"销售的普通商品和按"重量"销售的计量商品，并对多规格商品进行独立库存管理。

赠品功能，设置为赠品之后，前端小程序不可见 只可在门店端查看到并下单。赠品金额为0。

小程序商品列表修改为【美团】样式

### 主要功能 (2)

#### 完整订单处理流程(12天)

覆盖从订单预览、智能创建到多方式支付（微信、储值卡）的全流程管理，并有完善的状态追踪和自动处理机制。

完整的售后功能 订单完成之后 用户可以申请售后 可选择退款退货、仅退款两种方式。 后台审核退款

定时自动取消订单，回退库存


#### 完整的库存监控(15天)

分为出入库单、库存日志、实时库存、订单整合 查询库存准确性

#### 动态配送管理(3天)

根据店铺配置和实时订单量动态计算可用配送时间，并可限制各时段的订单数量。

#### 套餐卡功能(10天)

在现有商品里创建套餐，生成套餐卡，用户小程序里兑换。

前后端都是为我开发。

### 主要功能 (3)

#### 储值卡系统(7天)

增加充值活动 用户可以在小程序上面选择对应活动充值对应金额

重构会员折扣 从折扣与用户绑定改为折扣和储值卡绑定

#### 易联云打印机(2天)

支付成功后自动打印，支持手动补打。

#### 食谱(5天)

为用户端查看的模块 支持图文和视频两种形式

### 主要功能 (4)

#### 门店小程序（11天）

提供结算台功能（扫码商品、称重商品、多方式支付、支付成功后自动打印小票）

订单一键修复(手动补打小票、一键退款)

短信对接 储值卡支付时 如果该卡已绑定用户 则需要验证该用户手机验证码才可下单

#### 数据统计分析(5天)

提供多维度订单统计、商品销量排行和支付方式分布等可视化报表。

#### 数据核对(5天)

核对金额、库存信息 并修复已知问题

---

## 项目成果与价值

1、计量产品与普通产品的区分 -- 有效区分两种商品的库存计算，对库存单、订单、库存日志的兼容

2、库存监控 -- 可有效观测库存是否有问题，方便核查问题快速修复

---

## 个人总结

需增加对功能整体的了解，不单单只是完成基本功能。

对项目排期有完整的规划，避免上线之前还有很多任务没有做完导致出现问题。

功能上线前需进行完整的回归测试，避免上线出现bug。

---

**谢谢观看**