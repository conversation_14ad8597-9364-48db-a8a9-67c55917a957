/*
 Navicat Premium Dump SQL

 Source Server         : 118
 Source Server Type    : MySQL
 Source Server Version : 50743 (5.7.43-log)
 Source Host           : 127.0.0.1:3306
 Source Schema         : fresh_store

 Target Server Type    : MySQL
 Target Server Version : 50743 (5.7.43-log)
 File Encoding         : 65001

 Date: 06/03/2025 10:18:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for address
-- ----------------------------
DROP TABLE IF EXISTS `address`;
CREATE TABLE `address` (
  `id` int(32) unsigned NOT NULL AUTO_INCREMENT,
  `CityName` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '名称',
  `CityNum` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '编号',
  `parent_id` int(32) NOT NULL COMMENT '父级id',
  `level` int(4) NOT NULL COMMENT '等级',
  `alias` text COLLATE utf8_unicode_ci NOT NULL COMMENT '别名多个用,分开',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=4055 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='地址库';

-- ----------------------------
-- Table structure for banner
-- ----------------------------
DROP TABLE IF EXISTS `banner`;
CREATE TABLE `banner` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `basekeynum` varchar(50) DEFAULT NULL,
  `title` varchar(50) DEFAULT NULL COMMENT '标题',
  `image` varchar(500) DEFAULT NULL COMMENT '轮播图',
  `sort` int(11) DEFAULT NULL COMMENT '排序字段',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态 0 不展示 1展示',
  `add_time` datetime DEFAULT NULL,
  `content` text COMMENT '详情内容',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_basekeynum` (`basekeynum`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for card_customer
-- ----------------------------
DROP TABLE IF EXISTS `card_customer`;
CREATE TABLE `card_customer` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(50) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `linkman` varchar(50) DEFAULT NULL,
  `linktel` varchar(50) DEFAULT NULL,
  `sale_id` int(11) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='销售卡的客户';

-- ----------------------------
-- Table structure for card_delay_log
-- ----------------------------
DROP TABLE IF EXISTS `card_delay_log`;
CREATE TABLE `card_delay_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `basekeynum` varchar(35) DEFAULT NULL,
  `no` varchar(20) DEFAULT NULL COMMENT '批次单号 ',
  `cardnum` varchar(1000) DEFAULT NULL COMMENT '卡号 , 分割',
  `operator` varchar(255) DEFAULT NULL COMMENT '操作人',
  `customer_id` int(11) DEFAULT NULL,
  `delay_time` datetime DEFAULT NULL COMMENT '延期时间',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `add_time` datetime DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unq_basekeynum_no` (`basekeynum`,`no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='卡号延期记录表';

-- ----------------------------
-- Table structure for card_sale
-- ----------------------------
DROP TABLE IF EXISTS `card_sale`;
CREATE TABLE `card_sale` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(50) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='销售';

-- ----------------------------
-- Table structure for card_send_order_record
-- ----------------------------
DROP TABLE IF EXISTS `card_send_order_record`;
CREATE TABLE `card_send_order_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `basekeynum` varchar(50) DEFAULT NULL,
  `operator` varchar(255) DEFAULT NULL,
  `path` varchar(255) DEFAULT NULL,
  `add_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单发货记录表';

-- ----------------------------
-- Table structure for card_status_change
-- ----------------------------
DROP TABLE IF EXISTS `card_status_change`;
CREATE TABLE `card_status_change` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `basekeynum` varchar(50) DEFAULT NULL,
  `no` varchar(25) DEFAULT NULL COMMENT '批次单号',
  `customer_id` int(11) DEFAULT NULL COMMENT '客户id',
  `sale_id` int(11) DEFAULT NULL COMMENT '销售id',
  `status` tinyint(1) DEFAULT NULL COMMENT '类型',
  `cardnum` varchar(1000) DEFAULT NULL COMMENT '卡号 使用,分割',
  `operator` varchar(255) DEFAULT NULL COMMENT '操作人',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `add_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unq_no` (`no`,`basekeynum`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COMMENT='卡状态变更表';

-- ----------------------------
-- Table structure for card_status_change_log
-- ----------------------------
DROP TABLE IF EXISTS `card_status_change_log`;
CREATE TABLE `card_status_change_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `basekeynum` varchar(255) DEFAULT NULL,
  `change_no` varchar(50) DEFAULT NULL COMMENT 'card_status_change no 字段',
  `card_id` int(11) DEFAULT NULL COMMENT '卡id',
  `customer_id` int(11) DEFAULT NULL COMMENT '客户id',
  `sale_id` int(11) DEFAULT NULL COMMENT '销售员id',
  `operator` varchar(255) DEFAULT NULL COMMENT '操作员',
  `type` tinyint(1) DEFAULT NULL COMMENT '类型',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `add_time` datetime DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1139 DEFAULT CHARSET=utf8mb4 COMMENT='卡状态变更详情表';

-- ----------------------------
-- Table structure for card_template
-- ----------------------------
DROP TABLE IF EXISTS `card_template`;
CREATE TABLE `card_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `basekeynum` varchar(255) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `sort` int(11) DEFAULT NULL COMMENT '排序字段',
  `add_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商城模版表';

-- ----------------------------
-- Table structure for card_type
-- ----------------------------
DROP TABLE IF EXISTS `card_type`;
CREATE TABLE `card_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(50) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `market_price` decimal(10,2) DEFAULT NULL COMMENT '市场价',
  `to_balance` decimal(10,2) DEFAULT NULL COMMENT '可转充值余额',
  `remark` varchar(255) DEFAULT NULL,
  `type` int(1) DEFAULT NULL COMMENT '1充值卡',
  `max_exchange_num` int(11) DEFAULT NULL COMMENT '最大兑换次数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 COMMENT='卡型管理';

-- ----------------------------
-- Table structure for choose_one_config
-- ----------------------------
DROP TABLE IF EXISTS `choose_one_config`;
CREATE TABLE `choose_one_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `basekeynum` varchar(35) DEFAULT NULL,
  `BaseNum` varchar(50) DEFAULT NULL,
  `bkey` varchar(50) DEFAULT NULL,
  `domain` varchar(255) DEFAULT NULL,
  `add_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='多选一配置';

-- ----------------------------
-- Table structure for city
-- ----------------------------
DROP TABLE IF EXISTS `city`;
CREATE TABLE `city` (
  `_id` int(11) NOT NULL,
  `name` varchar(64) DEFAULT NULL,
  `city_id` varchar(12) DEFAULT NULL,
  `province_id` varchar(12) DEFAULT NULL,
  PRIMARY KEY (`_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for client_api_log
-- ----------------------------
DROP TABLE IF EXISTS `client_api_log`;
CREATE TABLE `client_api_log` (
  `id` int(100) unsigned NOT NULL AUTO_INCREMENT,
  `action` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `appid` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `get_data` text COLLATE utf8_unicode_ci NOT NULL,
  `send_data` text COLLATE utf8_unicode_ci NOT NULL,
  `application` text COLLATE utf8_unicode_ci NOT NULL,
  `add_time` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `addtime` int(20) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='接口应用日志表';

-- ----------------------------
-- Table structure for client_application
-- ----------------------------
DROP TABLE IF EXISTS `client_application`;
CREATE TABLE `client_application` (
  `id` int(100) unsigned NOT NULL AUTO_INCREMENT,
  `appid` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `appkey` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `is_log` int(2) NOT NULL DEFAULT '1' COMMENT '是否记录日志',
  `is_sign` int(2) NOT NULL DEFAULT '1' COMMENT '是否检验签名',
  `is_open` int(2) NOT NULL DEFAULT '1' COMMENT '状态 1-开启 0-关闭',
  `clientkeynum` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `add_time` int(20) NOT NULL,
  `name` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '应用名称',
  `remark` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '备注',
  `s` varchar(10) COLLATE utf8_unicode_ci NOT NULL DEFAULT '20' COMMENT '秒数',
  `ret_url` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '回调地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='接口应用表';

-- ----------------------------
-- Table structure for client_card_use_log
-- ----------------------------
DROP TABLE IF EXISTS `client_card_use_log`;
CREATE TABLE `client_card_use_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `basekeynum` varchar(50) DEFAULT NULL,
  `member_id` int(11) NOT NULL,
  `order_sn` varchar(255) DEFAULT NULL,
  `cardnum` varchar(255) DEFAULT NULL,
  `yu_money` decimal(10,2) DEFAULT NULL,
  `after_money` decimal(10,2) DEFAULT NULL,
  `use_money` decimal(10,2) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已支付 1 是 0否',
  `add_time` datetime DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `order_sn` (`order_sn`) USING BTREE,
  KEY `cardnum` (`cardnum`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=143 DEFAULT CHARSET=utf8mb4 COMMENT='卡号使用日志表';

-- ----------------------------
-- Table structure for client_member
-- ----------------------------
DROP TABLE IF EXISTS `client_member`;
CREATE TABLE `client_member` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uuid` varchar(20) DEFAULT NULL COMMENT '唯一标识',
  `qrcode` varchar(1000) DEFAULT NULL COMMENT '二维码',
  `name` varchar(100) CHARACTER SET utf8mb4 DEFAULT '微信用户' COMMENT '会员姓名',
  `phone` varchar(11) DEFAULT NULL COMMENT '会员手机号',
  `balance` decimal(10,2) DEFAULT '0.00' COMMENT '会员拥有积分',
  `password` varchar(255) DEFAULT NULL COMMENT '登录密码',
  `add_time` datetime DEFAULT NULL COMMENT '会员生成时间',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `openid` varchar(50) DEFAULT NULL COMMENT '微信openid',
  `avatar` varchar(255) DEFAULT NULL COMMENT '微信头像',
  `sex` tinyint(1) DEFAULT '0' COMMENT '会员性别：1男，2女，0未知',
  `state` int(255) DEFAULT '0' COMMENT '1启用，0冻结禁用',
  `reg_from` tinyint(1) DEFAULT '2' COMMENT '会员注册来源:0:手机注册；1微信注册；2数据导入3-卡券',
  `remark` varchar(255) DEFAULT NULL COMMENT '账号备注',
  `nickname` varchar(50) DEFAULT '' COMMENT '微信昵称',
  `realname` varchar(50) DEFAULT '' COMMENT '真实姓名',
  `keynum` varchar(35) DEFAULT NULL COMMENT '会员唯一标示',
  `clientkeynum` varchar(35) DEFAULT NULL COMMENT '会员所属平台客户',
  `level` int(11) DEFAULT '0' COMMENT '会员级别',
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `keynum` (`keynum`) USING BTREE,
  UNIQUE KEY `id_m_c` (`id`,`clientkeynum`) USING BTREE,
  KEY `phone_c_m` (`phone`,`clientkeynum`) USING BTREE,
  KEY `name_c_m` (`name`,`clientkeynum`) USING BTREE,
  KEY `unionwx_m_c` (`clientkeynum`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=616 DEFAULT CHARSET=utf8 COMMENT='平台客户下面的积分商户会员表';

-- ----------------------------
-- Table structure for client_supplier
-- ----------------------------
DROP TABLE IF EXISTS `client_supplier`;
CREATE TABLE `client_supplier` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(50) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `linkman` varchar(50) DEFAULT NULL,
  `linktel` varchar(50) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `keynum` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='销售卡的客户';

-- ----------------------------
-- Table structure for client_ycard
-- ----------------------------
DROP TABLE IF EXISTS `client_ycard`;
CREATE TABLE `client_ycard` (
  `id` int(100) unsigned NOT NULL AUTO_INCREMENT,
  `member_id` int(11) DEFAULT NULL COMMENT '绑定的用户id',
  `is_bind_member` tinyint(1) DEFAULT '0' COMMENT '是否绑定了用户 0否 1是',
  `cardnum` varchar(100) DEFAULT NULL COMMENT '卡号',
  `cardpwd` varchar(100) DEFAULT NULL COMMENT '密码',
  `status` int(5) DEFAULT '0' COMMENT '卡号状态 \r\n0-未销售 \r\n1-已销售 \r\n2-已退卡\r\n3-开卡\r\n4-关卡\r\n-1废卡\r\n',
  `content` text COMMENT '备注',
  `is_del` int(5) DEFAULT '0' COMMENT '0-正常1-已删除',
  `kai_money` decimal(10,2) DEFAULT NULL COMMENT '开卡金额',
  `yu_money` decimal(10,2) DEFAULT NULL COMMENT '卡内剩余金额',
  `kai_exchange_num` int(10) DEFAULT NULL COMMENT '最大兑换次数',
  `yu_exchange_num` int(10) DEFAULT NULL COMMENT '剩余兑换次数',
  `begin_dui` varchar(255) DEFAULT NULL COMMENT '开始兑换时间',
  `end_dui` varchar(255) DEFAULT NULL COMMENT '结束兑换时间',
  `piciname` varchar(255) DEFAULT NULL COMMENT '批次名称',
  `batch_id` int(100) DEFAULT NULL COMMENT '批次id',
  `opend_id` int(100) DEFAULT NULL COMMENT '开卡id',
  `add_time` int(50) DEFAULT NULL COMMENT '添加时间',
  `update_time` int(50) DEFAULT NULL COMMENT '更改时间',
  `bind_time` int(50) DEFAULT NULL COMMENT '卡号绑定时间',
  `clientkeynum` varchar(100) NOT NULL DEFAULT 'F86771141D57D86C1BAD2D879E5BD33F',
  `close_id` int(11) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL COMMENT '档案客户id',
  `sale_id` int(11) DEFAULT NULL COMMENT '档案销售id',
  `pu_id` int(11) DEFAULT NULL COMMENT '铺卡记录id',
  `tui_id` int(11) DEFAULT NULL COMMENT '退卡记录id',
  `cardtype_id` int(11) DEFAULT NULL COMMENT '卡型id',
  `code` varchar(12) DEFAULT NULL COMMENT '暂定12位唯一标识',
  `last_memberid` int(11) DEFAULT NULL COMMENT '最后一个兑换人的memberid',
  `qrcode` varchar(255) DEFAULT NULL COMMENT '二维码存储地址',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2101 DEFAULT CHARSET=utf8mb4 COMMENT='储值卡表';

-- ----------------------------
-- Table structure for client_ycard_batch
-- ----------------------------
DROP TABLE IF EXISTS `client_ycard_batch`;
CREATE TABLE `client_ycard_batch` (
  `id` int(100) unsigned NOT NULL AUTO_INCREMENT,
  `time` int(20) DEFAULT NULL COMMENT '批次时间',
  `begin_cardnumber` varchar(100) DEFAULT NULL COMMENT '开始卡号',
  `end_cardnumber` varchar(100) DEFAULT NULL COMMENT '结束卡号',
  `piciname` varchar(255) DEFAULT NULL COMMENT '批次名称',
  `content` varchar(255) DEFAULT NULL COMMENT '备注',
  `card_num` int(10) DEFAULT NULL COMMENT '卡号数量',
  `begin_dui` int(32) DEFAULT NULL COMMENT '开始兑换时间',
  `end_dui` int(32) DEFAULT NULL COMMENT '结束兑换时间',
  `clientkeynum` varchar(100) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL COMMENT '操作人员',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='卡号批次表';

-- ----------------------------
-- Table structure for client_ycard_log
-- ----------------------------
DROP TABLE IF EXISTS `client_ycard_log`;
CREATE TABLE `client_ycard_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cardnum` varchar(255) DEFAULT NULL COMMENT '卡号',
  `action` varchar(255) DEFAULT NULL COMMENT '操作类型',
  `operator` varchar(255) DEFAULT NULL COMMENT '操作人',
  `operator_time` int(50) DEFAULT NULL COMMENT '操作时间',
  `content` text COMMENT '操作内容',
  `clientkeynum` varchar(32) DEFAULT NULL COMMENT '应用keynum',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3197 DEFAULT CHARSET=utf8 COMMENT='储值卡日志表';

-- ----------------------------
-- Table structure for client_ycard_pucard
-- ----------------------------
DROP TABLE IF EXISTS `client_ycard_pucard`;
CREATE TABLE `client_ycard_pucard` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(50) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `sale_id` int(11) DEFAULT NULL,
  `begin_time` int(11) DEFAULT NULL,
  `end_time` int(11) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `opeartor` varchar(255) DEFAULT NULL,
  `operator_time` int(50) DEFAULT NULL,
  `pici_number` varchar(50) DEFAULT NULL,
  `type` int(1) DEFAULT NULL COMMENT '1铺卡2退卡',
  `piciname` varchar(255) DEFAULT NULL,
  `open_num` int(11) DEFAULT NULL,
  `cardnum` longtext,
  `real_tui_money` decimal(10,2) DEFAULT NULL,
  `uncleared_money` decimal(10,2) DEFAULT NULL COMMENT '未结清款项 与财务子表有关联',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for client_ycard_pucard_son
-- ----------------------------
DROP TABLE IF EXISTS `client_ycard_pucard_son`;
CREATE TABLE `client_ycard_pucard_son` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(50) DEFAULT NULL,
  `pucard_id` int(11) DEFAULT NULL,
  `cardtype_id` int(11) DEFAULT NULL,
  `begin_card` varchar(100) DEFAULT NULL,
  `end_card` varchar(100) DEFAULT NULL,
  `market_price` decimal(10,2) DEFAULT NULL,
  `zhekou` decimal(10,2) DEFAULT NULL,
  `end_price` decimal(10,2) DEFAULT NULL,
  `all_price` decimal(10,2) DEFAULT NULL,
  `cardnum` longtext,
  `operator` varchar(50) DEFAULT NULL,
  `operator_time` int(50) DEFAULT NULL,
  `cardnum_count` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for county
-- ----------------------------
DROP TABLE IF EXISTS `county`;
CREATE TABLE `county` (
  `_id` int(11) NOT NULL,
  `name` varchar(64) DEFAULT NULL,
  `county_id` varchar(12) DEFAULT NULL,
  `city_id` varchar(12) DEFAULT NULL,
  PRIMARY KEY (`_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for express_info
-- ----------------------------
DROP TABLE IF EXISTS `express_info`;
CREATE TABLE `express_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `basekeynum` varchar(50) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL COMMENT '快递标题',
  `code` varchar(255) DEFAULT NULL COMMENT '快递编号',
  `is_show` tinyint(1) DEFAULT NULL COMMENT '是否显示 1显示 0不显示',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `add_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='快递信息表';

-- ----------------------------
-- Table structure for order
-- ----------------------------
DROP TABLE IF EXISTS `order`;
CREATE TABLE `order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(35) NOT NULL,
  `order_no` varchar(20) NOT NULL,
  `shop_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `status` tinyint(4) DEFAULT '0' COMMENT '0待支付 1待审核 2已接单 3正在配送   100已完成   -1 取消',
  `type` tinyint(4) DEFAULT NULL COMMENT '1 自提   2配送',
  `address_json` text,
  `product_price` decimal(10,2) DEFAULT '0.00' COMMENT '商品价格',
  `shipping_price` decimal(10,2) DEFAULT '0.00' COMMENT '运费',
  `card_price` decimal(10,2) DEFAULT '0.00' COMMENT '卡支付金额',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '微信支付金额',
  `real_price` decimal(10,2) DEFAULT '0.00' COMMENT '微信实付金额',
  `pay_type` tinyint(4) DEFAULT '1' COMMENT '1 微信支付 2卡支付 3组合支付',
  `add_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `transaction_id` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `is_free_shipping_free` tinyint(4) DEFAULT '0' COMMENT '是否满减运费  0否   1是',
  `cancel_time` datetime DEFAULT NULL,
  `need_pay_price` decimal(10,2) DEFAULT '0.00' COMMENT '需要微信支付的金额',
  `origin_price` decimal(10,2) DEFAULT '0.00' COMMENT '总支付金额',
  `qrcode` varchar(255) DEFAULT NULL COMMENT '核销二维码',
  `verify_code` varchar(20) DEFAULT NULL,
  `pickup_time` varchar(20) DEFAULT NULL COMMENT '自提时间',
  `product_info` text,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=207 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for order_detail
-- ----------------------------
DROP TABLE IF EXISTS `order_detail`;
CREATE TABLE `order_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(35) NOT NULL,
  `order_no` varchar(255) NOT NULL,
  `shop_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `product_json` text,
  `inventory_json` text,
  `add_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `number` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=262 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for order_log
-- ----------------------------
DROP TABLE IF EXISTS `order_log`;
CREATE TABLE `order_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(35) NOT NULL,
  `order_no` varchar(35) NOT NULL,
  `content` varchar(255) DEFAULT NULL,
  `operator` varchar(255) DEFAULT NULL,
  `add_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=117 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for order_pay_log
-- ----------------------------
DROP TABLE IF EXISTS `order_pay_log`;
CREATE TABLE `order_pay_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(35) NOT NULL,
  `order_no` varchar(35) NOT NULL,
  `user_id` int(11) NOT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `real_price` decimal(10,2) DEFAULT NULL,
  `is_pay` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0未支付  1已支付',
  `pay_time` datetime DEFAULT NULL,
  `transaction_id` varchar(50) DEFAULT NULL,
  `add_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=183 DEFAULT CHARSET=utf8mb4 COMMENT='订单支付表   不可截断';

-- ----------------------------
-- Table structure for plat_account
-- ----------------------------
DROP TABLE IF EXISTS `plat_account`;
CREATE TABLE `plat_account` (
  `account_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `keynum` varchar(255) NOT NULL COMMENT '唯一标示',
  `accountname` varchar(255) DEFAULT NULL COMMENT '账号名称',
  `basekeynum` varchar(255) DEFAULT '0' COMMENT '平台客户keynum',
  `parent_baseid` int(11) DEFAULT NULL,
  `parent_basekeynum` varchar(255) DEFAULT NULL,
  `allpath_basekeynum` text,
  `parent_keynum` varchar(255) DEFAULT '平台',
  `allpath_keynum` text,
  `accountrealname` varchar(255) DEFAULT NULL COMMENT '账号真是姓名',
  `accountphone` varchar(255) DEFAULT NULL COMMENT '账号手机号',
  `accountnum` int(11) DEFAULT NULL COMMENT '数字账号',
  `accountpassword` varchar(255) DEFAULT NULL COMMENT '账号密码',
  `tablename` varchar(255) DEFAULT NULL COMMENT '该账号来自哪张表',
  `roleidlist` text COMMENT '该账号所属角色，多个用逗号拼接',
  `rolekeylist` text COMMENT '该账号所属角色，多个用逗号拼接',
  `purviewlist` text COMMENT '权限列表',
  `cre_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `cre_name` varchar(255) DEFAULT NULL COMMENT '创建者',
  `up_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `up_name` varchar(255) DEFAULT NULL COMMENT '更新时间',
  `isdel` int(11) DEFAULT '0' COMMENT '1删除，0未删除',
  `orgkeynum` varchar(255) DEFAULT NULL COMMENT '组织机构的keynum',
  `orgid` int(11) DEFAULT NULL COMMENT '组织机构的id',
  `o` int(11) DEFAULT '0' COMMENT '排序号',
  `remark` text COMMENT '备注',
  `weburl` text COMMENT '前台域名，多个用逗号拼接',
  `glurl` text COMMENT '后台域名，多个用逗号拼接',
  `cn_try_count` int(11) DEFAULT NULL COMMENT '账号密码登录错误次数',
  `certkeynum` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`account_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8 COMMENT='后台登录账号表';

-- ----------------------------
-- Table structure for plat_accountloginlog
-- ----------------------------
DROP TABLE IF EXISTS `plat_accountloginlog`;
CREATE TABLE `plat_accountloginlog` (
  `login_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '账户登录日志自增id',
  `basekeynum` varchar(255) DEFAULT '平台',
  `accountkeynum` varchar(255) DEFAULT NULL COMMENT '账户唯一标示',
  `accountname` varchar(255) DEFAULT NULL COMMENT '账号',
  `logintime` int(11) DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(255) DEFAULT NULL COMMENT '登录ip',
  `remark` text COMMENT '详情',
  `isdel` varchar(255) DEFAULT '0' COMMENT '1删除，0没有删除',
  `logindatetime` datetime NOT NULL COMMENT '登录时间',
  `login_location` varchar(255) DEFAULT NULL COMMENT '登录地址',
  `browser` varchar(255) DEFAULT NULL COMMENT '登录浏览器',
  `os` varchar(255) DEFAULT NULL COMMENT '登录操作系统',
  `real_ip` varchar(255) DEFAULT NULL COMMENT '负载均衡真实ip',
  `real_location` varchar(255) DEFAULT NULL COMMENT '负载均衡真实地址',
  PRIMARY KEY (`login_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=325 DEFAULT CHARSET=utf8 COMMENT='账号登录日志表';

-- ----------------------------
-- Table structure for plat_accountoperationlog
-- ----------------------------
DROP TABLE IF EXISTS `plat_accountoperationlog`;
CREATE TABLE `plat_accountoperationlog` (
  `operate_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountname` varchar(255) DEFAULT NULL COMMENT '账号',
  `accountkeynum` varchar(255) DEFAULT NULL COMMENT '账号唯一标示',
  `accountid` int(11) DEFAULT NULL COMMENT '账号id',
  `basekeynum` varchar(255) DEFAULT NULL,
  `log` text COMMENT '日志内容',
  `content` text COMMENT '数据',
  `ip` varchar(255) DEFAULT NULL COMMENT '客户端ip',
  `isdel` int(11) DEFAULT '0' COMMENT '1删除，0没有删除',
  `operatetime` int(11) DEFAULT NULL COMMENT '操作时间',
  `operatedatetime` datetime DEFAULT NULL COMMENT '操作时间',
  `real_ip` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`operate_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=283 DEFAULT CHARSET=utf8 COMMENT='账号操作日志';

-- ----------------------------
-- Table structure for plat_admin
-- ----------------------------
DROP TABLE IF EXISTS `plat_admin`;
CREATE TABLE `plat_admin` (
  `admin_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `keynum` varchar(255) DEFAULT NULL,
  `basekeynum` varchar(255) DEFAULT NULL COMMENT '平台客户的keynum',
  `accountname` varchar(255) DEFAULT NULL COMMENT '账号',
  `realname` varchar(255) DEFAULT NULL,
  `linkphone` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `qq` varchar(255) DEFAULT NULL,
  `linkaddress` varchar(255) DEFAULT NULL,
  `is_del` int(11) DEFAULT '0' COMMENT '1为删除，0未删除',
  `remark` varchar(255) DEFAULT NULL,
  `o` varchar(255) DEFAULT '0',
  PRIMARY KEY (`admin_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=utf8 COMMENT='管理员表';

-- ----------------------------
-- Table structure for plat_alipay_set
-- ----------------------------
DROP TABLE IF EXISTS `plat_alipay_set`;
CREATE TABLE `plat_alipay_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `seller_email` varchar(255) CHARACTER SET gbk NOT NULL COMMENT '卖家账号',
  `partner` varchar(255) CHARACTER SET gbk NOT NULL COMMENT '合作者身份id',
  `key` varchar(255) CHARACTER SET gbk DEFAULT NULL COMMENT '支付秘钥',
  `transport` varchar(255) DEFAULT NULL COMMENT '访问模式,根据自己的服务器是否支持ssl访问，若支持请选择https；若不支持请选择http',
  `basekeynum` varchar(255) DEFAULT NULL,
  `up_time` int(11) DEFAULT NULL COMMENT '最后修改时间',
  `pay_url` varchar(255) DEFAULT NULL COMMENT '同一个平台客户统一支付url',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='支付宝支付基本配置表';

-- ----------------------------
-- Table structure for plat_attack_log
-- ----------------------------
DROP TABLE IF EXISTS `plat_attack_log`;
CREATE TABLE `plat_attack_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `attack_ip` varchar(255) DEFAULT NULL COMMENT '攻击者ip',
  `try_num` int(11) DEFAULT NULL COMMENT '错误次数',
  `send_mail_num` int(11) DEFAULT NULL COMMENT '已发送邮件次数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4 COMMENT='ip攻击日志记录';

-- ----------------------------
-- Table structure for plat_client
-- ----------------------------
DROP TABLE IF EXISTS `plat_client`;
CREATE TABLE `plat_client` (
  `client_id` int(11) NOT NULL AUTO_INCREMENT,
  `keynum` varchar(255) DEFAULT NULL COMMENT '平台客户唯一标识,和account表keynum对应',
  `clientnum` varchar(255) DEFAULT NULL COMMENT '平台客户编号,用于区分数据存贮以及账号前缀',
  `companyname` varchar(255) DEFAULT NULL COMMENT '公司名称',
  `linkman` varchar(255) DEFAULT NULL COMMENT '联系人',
  `linktel` varchar(255) DEFAULT NULL COMMENT '联系电话',
  `sitekeynum` varchar(255) DEFAULT '3EB5014520195E5FE6362A2D110F7709' COMMENT '系统版本唯一标示',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `cre_time` varchar(255) DEFAULT NULL COMMENT '创建时间',
  `isdel` int(11) DEFAULT '0' COMMENT '1删除，0未删除',
  `clientstatus` int(11) DEFAULT '1' COMMENT '1启用，0禁用',
  `end_time` varchar(255) DEFAULT NULL COMMENT '到期时间',
  `merchantnumber` int(11) DEFAULT NULL COMMENT '可创建积分商城数量',
  `system_name` varchar(255) DEFAULT NULL COMMENT '系统名称,名字用于区分',
  `day` int(5) NOT NULL DEFAULT '3' COMMENT '客户订单下单超过几小时不发货超时',
  `g_day` int(4) NOT NULL DEFAULT '12' COMMENT '供应商超过下单时间几个小时未发货',
  `certkeynum` varchar(255) DEFAULT NULL,
  `is_qrcode` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启二维码功能',
  `is_open_card` tinyint(1) DEFAULT '0' COMMENT '是否开启储值卡功能 0否 1是',
  `is_show_banner` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否显示轮播图 0否 1是',
  `is_first_category` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为一级分类 0否 1是',
  PRIMARY KEY (`client_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=gbk COMMENT='平台客户表';

-- ----------------------------
-- Table structure for plat_client_authorize_log
-- ----------------------------
DROP TABLE IF EXISTS `plat_client_authorize_log`;
CREATE TABLE `plat_client_authorize_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(255) DEFAULT NULL COMMENT '平台客户信息',
  `old_end_time` int(11) DEFAULT NULL COMMENT '变更前系统到期时间',
  `old_merchantnumber` int(11) DEFAULT NULL COMMENT '变更前系统可创建商城数量',
  `new_end_time` int(11) DEFAULT NULL COMMENT '变更后系统到期时间',
  `new_merchantnumber` int(11) DEFAULT NULL COMMENT '变更后系统可创建商城数量',
  `money` varchar(255) DEFAULT NULL COMMENT '收取费用',
  `time` int(11) DEFAULT NULL COMMENT '变更时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='平台用户授权变更记录';

-- ----------------------------
-- Table structure for plat_config
-- ----------------------------
DROP TABLE IF EXISTS `plat_config`;
CREATE TABLE `plat_config` (
  `keynum` varchar(255) DEFAULT NULL COMMENT '唯一标示',
  `titlecn` varchar(255) DEFAULT NULL COMMENT '系统标题',
  `copyright` varchar(255) DEFAULT NULL COMMENT '系统版权',
  `basekeynum` varchar(255) DEFAULT NULL,
  `login_theme` int(255) DEFAULT '1' COMMENT '登录页面模板id,1、2、3、4',
  `customer_service_image` varchar(1000) DEFAULT NULL COMMENT '客服页图片'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统基本设置';

-- ----------------------------
-- Table structure for plat_menu
-- ----------------------------
DROP TABLE IF EXISTS `plat_menu`;
CREATE TABLE `plat_menu` (
  `menu_id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '菜单自增id',
  `pid` mediumint(9) NOT NULL COMMENT '上级菜单id',
  `level` int(11) DEFAULT NULL COMMENT '菜单级别',
  `path_id` text COMMENT '菜单路径id',
  `menu_name` varchar(255) DEFAULT NULL COMMENT '菜单名字',
  `url` varchar(255) DEFAULT NULL COMMENT '菜单url',
  `o` mediumint(9) DEFAULT NULL COMMENT '排序号',
  `icon` varchar(255) DEFAULT NULL COMMENT '菜单小图标',
  `remark` varchar(255) DEFAULT NULL COMMENT '菜单备注',
  `is_show` int(11) DEFAULT '1' COMMENT '1显示0不显示',
  `is_changyong` int(1) NOT NULL COMMENT '1是常用，0不是常用',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1281 DEFAULT CHARSET=utf8 COMMENT='系统菜单表';

-- ----------------------------
-- Table structure for plat_order_wuliu
-- ----------------------------
DROP TABLE IF EXISTS `plat_order_wuliu`;
CREATE TABLE `plat_order_wuliu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `orderid` varchar(100) DEFAULT NULL COMMENT '关联订单子表订单号',
  `express` varchar(100) DEFAULT NULL COMMENT '发货快递',
  `express_code` varchar(50) DEFAULT NULL COMMENT '快递编码',
  `expressnum` varchar(50) DEFAULT NULL COMMENT '快递号',
  `status` int(10) DEFAULT NULL COMMENT '物流状态 0订阅失败 99已删除',
  `wuliu_content` longtext COMMENT '物流轨迹',
  `log` longtext COMMENT '订阅日志',
  `back_log` longtext COMMENT '推送日志',
  `clientkeynum` varchar(50) DEFAULT NULL COMMENT '经销商keynum',
  `create_time` int(50) DEFAULT NULL COMMENT '创建时间',
  `lasttime` int(50) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `orderid` (`orderid`) USING BTREE,
  KEY `expressnum` (`expressnum`) USING BTREE,
  KEY `orderid_2` (`orderid`,`expressnum`) USING BTREE,
  KEY `express_code` (`express_code`,`expressnum`,`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for plat_org
-- ----------------------------
DROP TABLE IF EXISTS `plat_org`;
CREATE TABLE `plat_org` (
  `org_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `keynum` varchar(255) DEFAULT NULL COMMENT '唯一标示',
  `basekeynum` varchar(255) DEFAULT '0',
  `parentid` int(11) DEFAULT NULL,
  `parentkeynum` varchar(255) DEFAULT NULL,
  `orgnum` varchar(255) DEFAULT NULL,
  `orgname` varchar(255) DEFAULT NULL,
  `orgleadername` varchar(255) DEFAULT NULL COMMENT '部门领导名字',
  `orgaddress` varchar(255) DEFAULT NULL COMMENT '组织地址',
  `allpathkeynum` text,
  `allpathid` text,
  `allpathtext` text,
  `o` int(11) DEFAULT NULL COMMENT '排序号',
  `remark` text COMMENT '备注',
  `isdel` int(11) DEFAULT '0' COMMENT '1删除，0未删除',
  PRIMARY KEY (`org_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8 COMMENT='组织机构表';

-- ----------------------------
-- Table structure for plat_role
-- ----------------------------
DROP TABLE IF EXISTS `plat_role`;
CREATE TABLE `plat_role` (
  `role_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增的角色id',
  `keynum` varchar(255) DEFAULT NULL COMMENT '角色的唯一标示',
  `basekeynum` varchar(255) DEFAULT NULL COMMENT '平台客户的keynum',
  `role_num` varchar(255) DEFAULT NULL COMMENT '角色编号',
  `role_name` varchar(255) DEFAULT NULL COMMENT '角色名字',
  `remark` varchar(255) DEFAULT NULL COMMENT '角色备注',
  `purviewlist_keynum` text COMMENT '菜单权限列表 keynum,多个用逗号拼接',
  `purviewlist_id` text COMMENT '菜单权限列表id,多个用逗号拼接',
  `isdel` int(11) DEFAULT '0' COMMENT '1删除，0没有删除',
  `o` int(11) DEFAULT NULL COMMENT '排序号',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8 COMMENT='系统角色表';

-- ----------------------------
-- Table structure for plat_site
-- ----------------------------
DROP TABLE IF EXISTS `plat_site`;
CREATE TABLE `plat_site` (
  `site_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `keynum` varchar(255) DEFAULT NULL,
  `sitename` varchar(255) DEFAULT NULL COMMENT '系统名称 ',
  `purviewlist_id` text COMMENT '权限列表',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `isdel` int(11) DEFAULT '0' COMMENT '1已删除，0未删除',
  `o` int(11) DEFAULT '0' COMMENT '排序号',
  `tablename` varchar(255) DEFAULT NULL COMMENT '区分礼品公司与商户',
  PRIMARY KEY (`site_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COMMENT='平台客户系统';

-- ----------------------------
-- Table structure for plat_system_set
-- ----------------------------
DROP TABLE IF EXISTS `plat_system_set`;
CREATE TABLE `plat_system_set` (
  `id` int(11) DEFAULT NULL,
  `client_version` varchar(255) DEFAULT NULL COMMENT '平台客户系统版本',
  `client_service_time` varchar(255) DEFAULT NULL COMMENT '平台客户服务时间',
  `client_service_phone` varchar(255) DEFAULT NULL COMMENT '平台客户服务电话',
  `client_service_qrcode` varchar(255) DEFAULT NULL COMMENT '平台客户服务二维码',
  `client_service_desc` varchar(255) DEFAULT NULL COMMENT '平台客户服务段描述',
  `client_author_words` text COMMENT '平台客户作者寄语',
  `client_about_us` text COMMENT '平台客户关于我们',
  `client_copy_right` varchar(255) DEFAULT NULL COMMENT '平台客户版权所有',
  `ali_accesskeyid` varchar(255) DEFAULT NULL COMMENT '阿里云oss参数',
  `ali_accesskeysecret` varchar(255) DEFAULT NULL COMMENT '阿里云oss参数',
  `ali_endpoint` varchar(255) DEFAULT NULL COMMENT '阿里云oss参数',
  `ali_bucket` varchar(255) DEFAULT NULL COMMENT '阿里云oss参数',
  `ali_bucket_url` varchar(255) DEFAULT NULL COMMENT '阿里云oss参数',
  `mod_time` int(11) DEFAULT NULL COMMENT '最后修改时间',
  `aboutus1` text COMMENT '关于我们1',
  `aboutus2` text COMMENT '关于我们2'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统总平台基本设置';

-- ----------------------------
-- Table structure for plat_terminal_log
-- ----------------------------
DROP TABLE IF EXISTS `plat_terminal_log`;
CREATE TABLE `plat_terminal_log` (
  `id` int(11) DEFAULT NULL,
  `terminal_name` varchar(255) DEFAULT NULL COMMENT '终端名字',
  `visit_nums` int(11) DEFAULT NULL COMMENT '访问次数',
  `clientkeynum` varchar(255) DEFAULT NULL,
  `merchantkeynum` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='终端访问统计表';

-- ----------------------------
-- Table structure for plat_visit_log
-- ----------------------------
DROP TABLE IF EXISTS `plat_visit_log`;
CREATE TABLE `plat_visit_log` (
  `id` int(11) DEFAULT NULL,
  `hour` varchar(255) DEFAULT NULL COMMENT '终端名字',
  `visit_uv_nums` int(11) DEFAULT NULL COMMENT '访问uv次数',
  `visit_pv_nums` int(11) DEFAULT NULL COMMENT '访问pv次数',
  `clientkeynum` varchar(255) DEFAULT NULL,
  `merchantkeynum` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='前台客户访问(小时)统计表';

-- ----------------------------
-- Table structure for plat_warn_log
-- ----------------------------
DROP TABLE IF EXISTS `plat_warn_log`;
CREATE TABLE `plat_warn_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `warn_content` text CHARACTER SET gbk COMMENT '预警内容',
  `time` varchar(255) CHARACTER SET gbk DEFAULT NULL COMMENT '时间戳',
  `datetime` varchar(255) DEFAULT NULL COMMENT '时间',
  `basekeynum` varchar(255) CHARACTER SET gbk DEFAULT NULL,
  `is_read` int(255) DEFAULT NULL COMMENT '1是已读，0是未读',
  `read_time` int(11) DEFAULT NULL COMMENT '阅读时间戳',
  `read_datetime` datetime DEFAULT NULL COMMENT '阅读时间',
  `is_send` varchar(255) DEFAULT NULL COMMENT '1是已经发送，0没有发送',
  `send_time` int(11) DEFAULT NULL COMMENT '发送时间戳',
  `send_datetime` datetime DEFAULT NULL COMMENT '发送时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统严重预警记录表';

-- ----------------------------
-- Table structure for plat_wechat_set
-- ----------------------------
DROP TABLE IF EXISTS `plat_wechat_set`;
CREATE TABLE `plat_wechat_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `appid` varchar(255) CHARACTER SET gbk NOT NULL COMMENT '公众号appid',
  `appsecret` varchar(255) CHARACTER SET gbk NOT NULL COMMENT '公众号appsecret',
  `mchid` varchar(255) CHARACTER SET gbk DEFAULT NULL COMMENT '商户号',
  `enterprise_id` varchar(255) DEFAULT NULL COMMENT '企业id',
  `customer_service_link` varchar(255) DEFAULT NULL COMMENT '客户链接',
  `key` varchar(255) CHARACTER SET gbk DEFAULT NULL COMMENT '支付秘钥',
  `v3_key` varchar(255) DEFAULT NULL COMMENT 'v3版本支付秘钥',
  `access_token` varchar(255) DEFAULT NULL COMMENT '全局调用凭证',
  `access_token_expire_time` int(11) DEFAULT NULL COMMENT 'accesstoken过期时间',
  `jsapi_ticket` varchar(255) DEFAULT NULL COMMENT 'jsapi_ticket',
  `jsapi_ticket_expire_time` int(11) DEFAULT NULL COMMENT 'jsapi_ticket过期时间',
  `apiclient_cert` varchar(2000) DEFAULT NULL COMMENT '公钥',
  `apiclient_key` varchar(2000) DEFAULT NULL COMMENT '私钥',
  `serial_no` varchar(50) DEFAULT NULL COMMENT '证书序列号',
  `basekeynum` varchar(255) DEFAULT NULL,
  `up_time` int(11) DEFAULT NULL COMMENT '最后修改时间',
  `pay_url` varchar(255) DEFAULT NULL COMMENT '同一个平台客户统一支付url',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COMMENT='微信公众号基本配置表';

-- ----------------------------
-- Table structure for product_attribute
-- ----------------------------
DROP TABLE IF EXISTS `product_attribute`;
CREATE TABLE `product_attribute` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `spec_id` bigint(20) DEFAULT '0' COMMENT '属性组id',
  `attr_value` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '属性值',
  `is_checked` tinyint(4) DEFAULT NULL COMMENT '是否选中',
  `used_count` int(11) DEFAULT '0' COMMENT '商品数',
  `sort` int(11) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for product_category
-- ----------------------------
DROP TABLE IF EXISTS `product_category`;
CREATE TABLE `product_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(35) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for product_inventory
-- ----------------------------
DROP TABLE IF EXISTS `product_inventory`;
CREATE TABLE `product_inventory` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(35) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sn` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '商品编码',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `spec_ids` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '属性组id',
  `attr_ids` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '属性id',
  `attr_vals` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '属性值',
  `product_id` bigint(20) NOT NULL DEFAULT '0',
  `image` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '规格图片',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '价格',
  `kucun` decimal(10,0) NOT NULL DEFAULT '0' COMMENT '库存',
  `sale_num` int(11) DEFAULT '0' COMMENT '销量',
  `if_default` int(11) DEFAULT '0' COMMENT '默认规格',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `data` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `sn` (`sn`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=138 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for product_spec
-- ----------------------------
DROP TABLE IF EXISTS `product_spec`;
CREATE TABLE `product_spec` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `p_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '商品类型',
  `spec_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规格名称',
  `attr_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '规格类型',
  `checked` tinyint(4) DEFAULT NULL,
  `sort` tinyint(4) DEFAULT '0' COMMENT '排序',
  `used_count` int(11) DEFAULT '0' COMMENT '商品数',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=84 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for product_spec_attr
-- ----------------------------
DROP TABLE IF EXISTS `product_spec_attr`;
CREATE TABLE `product_spec_attr` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `spec_id` bigint(20) DEFAULT '0' COMMENT '排序',
  `attr_id` bigint(20) DEFAULT '0',
  `product_id` bigint(20) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `attr_value` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `checked` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=159 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for products
-- ----------------------------
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(35) NOT NULL,
  `category_id` int(11) DEFAULT NULL COMMENT '分类id',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `subtitle` varchar(255) DEFAULT NULL COMMENT '副标题',
  `cover` varchar(255) DEFAULT NULL COMMENT '封面',
  `banner` text,
  `kucun` int(11) DEFAULT '0' COMMENT '库存',
  `price` decimal(10,2) DEFAULT '0.00',
  `market_price` decimal(10,2) DEFAULT NULL,
  `inventory_id` int(11) DEFAULT '0',
  `ord` int(11) DEFAULT '0',
  `state` tinyint(4) DEFAULT '0' COMMENT '状态: 0-保存  1-上架  -1-下架',
  `is_attribute` tinyint(1) DEFAULT '1' COMMENT '是否开启多规格：0-不开启 1-开启',
  `content` text,
  `state_desc` varchar(255) DEFAULT NULL COMMENT '状态',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `created_at` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COMMENT='产品表';

-- ----------------------------
-- Table structure for province
-- ----------------------------
DROP TABLE IF EXISTS `province`;
CREATE TABLE `province` (
  `_id` int(11) NOT NULL,
  `name` varchar(64) DEFAULT NULL,
  `province_id` varchar(12) DEFAULT NULL,
  PRIMARY KEY (`_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for shop
-- ----------------------------
DROP TABLE IF EXISTS `shop`;
CREATE TABLE `shop` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `keynum` varchar(35) DEFAULT NULL,
  `clientkeynum` varchar(35) DEFAULT NULL,
  `background_image` varchar(255) DEFAULT NULL COMMENT '背景图',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `add_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL COMMENT '联系方式',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `lat` varchar(20) DEFAULT NULL COMMENT '经度',
  `lng` varchar(20) DEFAULT NULL COMMENT '纬度',
  `code` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='门店表';

-- ----------------------------
-- Table structure for shop_city
-- ----------------------------
DROP TABLE IF EXISTS `shop_city`;
CREATE TABLE `shop_city` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `add_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店城市表';

-- ----------------------------
-- Table structure for shop_config
-- ----------------------------
DROP TABLE IF EXISTS `shop_config`;
CREATE TABLE `shop_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(50) DEFAULT NULL,
  `delivery_km` decimal(10,2) DEFAULT NULL COMMENT '配送范围 公里',
  `delivery_price` decimal(10,2) DEFAULT NULL COMMENT '配送费',
  `baoyou_price` decimal(10,2) DEFAULT NULL COMMENT '满多少包邮',
  `keynum` varchar(35) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='门店基本配置表';

-- ----------------------------
-- Table structure for shop_product
-- ----------------------------
DROP TABLE IF EXISTS `shop_product`;
CREATE TABLE `shop_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `status` tinyint(4) DEFAULT '0' COMMENT '0下架 1上架',
  `clientkeynum` varchar(35) DEFAULT NULL,
  `add_time` datetime DEFAULT NULL,
  `is_audit` int(1) DEFAULT '0' COMMENT '是否审核0未审核1已审核',
  `audit_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COMMENT='门店商品表';

-- ----------------------------
-- Table structure for shop_product_inventory
-- ----------------------------
DROP TABLE IF EXISTS `shop_product_inventory`;
CREATE TABLE `shop_product_inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `inventory_id` int(11) NOT NULL,
  `stock` int(11) DEFAULT NULL COMMENT '库存',
  `add_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `clientkeynum` varchar(35) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for shop_product_stock_log
-- ----------------------------
DROP TABLE IF EXISTS `shop_product_stock_log`;
CREATE TABLE `shop_product_stock_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `spec_id` int(11) DEFAULT NULL,
  `old_stock` int(11) DEFAULT NULL,
  `new_stock` int(11) DEFAULT NULL,
  `time` datetime DEFAULT NULL,
  `operator` varchar(255) DEFAULT NULL,
  `order_no` varchar(35) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=241 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for shop_wechat_set
-- ----------------------------
DROP TABLE IF EXISTS `shop_wechat_set`;
CREATE TABLE `shop_wechat_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `appid` varchar(255) CHARACTER SET gbk NOT NULL COMMENT '公众号appid',
  `appsecret` varchar(255) CHARACTER SET gbk NOT NULL COMMENT '公众号appsecret',
  `mchid` varchar(255) CHARACTER SET gbk DEFAULT NULL COMMENT '商户号',
  `enterprise_id` varchar(255) DEFAULT NULL COMMENT '企业id',
  `customer_service_link` varchar(255) DEFAULT NULL COMMENT '客户链接',
  `key` varchar(255) CHARACTER SET gbk DEFAULT NULL COMMENT '支付秘钥',
  `v3_key` varchar(255) DEFAULT NULL COMMENT 'v3版本支付秘钥',
  `access_token` varchar(255) DEFAULT NULL COMMENT '全局调用凭证',
  `access_token_expire_time` int(11) DEFAULT NULL COMMENT 'accesstoken过期时间',
  `jsapi_ticket` varchar(255) DEFAULT NULL COMMENT 'jsapi_ticket',
  `jsapi_ticket_expire_time` int(11) DEFAULT NULL COMMENT 'jsapi_ticket过期时间',
  `apiclient_cert` varchar(2000) DEFAULT NULL COMMENT '公钥',
  `apiclient_key` varchar(2000) DEFAULT NULL COMMENT '私钥',
  `serial_no` varchar(50) DEFAULT NULL COMMENT '证书序列号',
  `basekeynum` varchar(255) DEFAULT NULL,
  `up_time` int(11) DEFAULT NULL COMMENT '最后修改时间',
  `pay_url` varchar(255) DEFAULT NULL COMMENT '同一个平台客户统一支付url',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='微信门店端公众号基本配置表';

-- ----------------------------
-- Table structure for store_check_cardnum_log
-- ----------------------------
DROP TABLE IF EXISTS `store_check_cardnum_log`;
CREATE TABLE `store_check_cardnum_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `cardnum` varchar(1000) DEFAULT NULL,
  `money` decimal(10,2) DEFAULT NULL,
  `accountname` varchar(255) DEFAULT NULL,
  `add_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for store_users
-- ----------------------------
DROP TABLE IF EXISTS `store_users`;
CREATE TABLE `store_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `openid` varchar(50) DEFAULT NULL COMMENT 'openid 公众号唯一标识',
  `account_id` int(11) DEFAULT NULL COMMENT '账号id 与 plat_account 一致',
  `keynum` varchar(35) DEFAULT NULL COMMENT '客户keynum',
  `clientkeynum` varchar(35) DEFAULT NULL,
  `add_time` datetime DEFAULT NULL,
  `type` tinyint(4) DEFAULT '0' COMMENT '类型 0 未登陆 1 已绑定',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8mb4 COMMENT='门店端用户表';

-- ----------------------------
-- Table structure for supplier
-- ----------------------------
DROP TABLE IF EXISTS `supplier`;
CREATE TABLE `supplier` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(50) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `linkman` varchar(50) DEFAULT NULL,
  `linktel` varchar(50) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='销售卡的客户';

-- ----------------------------
-- Table structure for user_address
-- ----------------------------
DROP TABLE IF EXISTS `user_address`;
CREATE TABLE `user_address` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(35) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `province` varchar(50) DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `area` varchar(50) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `is_default` tinyint(4) DEFAULT '0',
  `add_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `consignee` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `lat` varchar(20) DEFAULT NULL,
  `lng` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for user_cart
-- ----------------------------
DROP TABLE IF EXISTS `user_cart`;
CREATE TABLE `user_cart` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(35) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `inventory_id` int(11) NOT NULL,
  `number` int(11) DEFAULT NULL,
  `add_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `shop_id` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=221 DEFAULT CHARSET=utf8mb4 COMMENT='购物车表';

-- ----------------------------
-- Table structure for user_level
-- ----------------------------
DROP TABLE IF EXISTS `user_level`;
CREATE TABLE `user_level` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(35) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `discount` tinyint(4) DEFAULT NULL COMMENT '100以内',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `add_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for web_set
-- ----------------------------
DROP TABLE IF EXISTS `web_set`;
CREATE TABLE `web_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `basekeynum` varchar(255) DEFAULT NULL COMMENT '所属商城keynum',
  `store_name` varchar(255) DEFAULT NULL COMMENT '商城名称',
  `login_type` int(11) DEFAULT NULL COMMENT '0手机号+密码，1手机号+验证码',
  `is_weixin_login` int(11) DEFAULT NULL COMMENT '是否允许微信登录1允许，0不允许',
  `is_reg` int(11) DEFAULT NULL COMMENT '是否新用户注册1允许，0不允许',
  `kefu_tel` varchar(255) DEFAULT NULL COMMENT '客服电话',
  `currency_str` varchar(255) DEFAULT NULL COMMENT '货币单位',
  `is_show_market_price` int(11) DEFAULT NULL COMMENT '是否显示市场价，1显示，0不显示',
  `state` int(255) DEFAULT NULL COMMENT '商城上线下线状态，1上线，0下线',
  `state_notice` text COMMENT '下线说明内容',
  `up_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `store_freight_charges` varchar(255) DEFAULT NULL COMMENT '满多少元包邮',
  `store_freight_price` varchar(255) DEFAULT NULL COMMENT '不满收取运费金额',
  `reg_protocol_name` varchar(255) DEFAULT NULL COMMENT '注册协议名字',
  `reg_protocol_content` text COMMENT '注册协议内容',
  `copyright` varchar(255) DEFAULT NULL COMMENT '前台网页底部版权',
  `theme_color` varchar(255) DEFAULT '1' COMMENT '页面主题色调',
  `web_theme` int(11) unsigned DEFAULT '1' COMMENT '前台魔板数字id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='网站前台设置表';

-- ----------------------------
-- Table structure for webconfig
-- ----------------------------
DROP TABLE IF EXISTS `webconfig`;
CREATE TABLE `webconfig` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(32) DEFAULT NULL,
  `background_login` varchar(255) DEFAULT NULL COMMENT '前台登录页背景图',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='前台基本设置轮播图等';

-- ----------------------------
-- Table structure for wuliu_config
-- ----------------------------
DROP TABLE IF EXISTS `wuliu_config`;
CREATE TABLE `wuliu_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientkeynum` varchar(32) DEFAULT NULL COMMENT '供应商keynum',
  `status` enum('0','1') DEFAULT '0' COMMENT '0关闭对接1开启对接',
  `join_url` varchar(255) DEFAULT NULL COMMENT '对接地址',
  `appid` varchar(255) DEFAULT NULL,
  `appkey` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `factorykeynum` (`clientkeynum`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS = 1;
