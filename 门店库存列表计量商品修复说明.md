# 门店库存列表计量商品修复说明

## 问题描述

门店库存列表页面（`application/admin/view/sinventory/stock.html`）在处理计量商品时存在以下问题：

1. **库存状态筛选错误**：只检查 `stock` 字段，忽略了计量商品的 `weight_stock` 字段
2. **库存显示错误**：对所有商品都显示数量库存，没有区分计量商品的重量库存
3. **缺少商品类型区分**：界面上无法识别哪些是计量商品，哪些是普通商品
4. **库存单位不正确**：计量商品应该显示 kg/斤/克 等重量单位，而不是 "件"

## 修复内容

### 1. 后端修复（SinventoryController.php）

#### 1.1 查询字段增强
- 增加 `p.product_type` 字段用于区分商品类型
- 增加 `COALESCE(pi.weight_unit, "kg") as weight_unit` 字段获取重量单位

#### 1.2 库存状态筛选逻辑修复
**修复前：**
```php
if ($stock_status == 1) {
    $where[] = ['spi.stock', '>', 0];
} elseif ($stock_status == 2) {
    $where[] = ['spi.stock', '<=', 0];
}
```

**修复后：**
```php
// 库存状态筛选 - 根据商品类型区分处理
if ($stock_status == 1) {
    // 有库存：普通商品stock>0 或 计量商品weight_stock>0
    $where[] = ['(p.product_type = 1 AND spi.stock > 0) OR (p.product_type = 2 AND spi.weight_stock > 0)', 'exp', ''];
} elseif ($stock_status == 2) {
    // 无库存：普通商品stock<=0 或 计量商品weight_stock<=0
    $where[] = ['(p.product_type = 1 AND spi.stock <= 0) OR (p.product_type = 2 AND spi.weight_stock <= 0)', 'exp', ''];
}
```

#### 1.3 数据处理逻辑增强
增加了根据商品类型处理库存显示的逻辑：

```php
// 根据商品类型处理库存显示
$item['product_type'] = $item['product_type'] ?? 1;
$item['product_type_text'] = $item['product_type'] == 2 ? '计量商品' : '普通商品';

if ($item['product_type'] == 2) {
    // 计量商品：显示重量库存
    $item['current_stock'] = $item['weight_stock'] ?? 0;
    $item['stock_unit'] = $item['weight_unit'] ?? 'kg';
    $item['stock_display'] = $item['current_stock'] . $item['stock_unit'];
    $item['warning_stock_display'] = ($item['warning_stock'] ?? 0) . $item['stock_unit'];
} else {
    // 普通商品：显示数量库存
    $item['current_stock'] = $item['stock'] ?? 0;
    $item['stock_unit'] = 'pcs';
    $item['stock_display'] = $item['current_stock'] . '件';
    $item['warning_stock_display'] = ($item['warning_stock'] ?? 0) . '件';
}
```

### 2. 前端修复（stock.html）

#### 2.1 新增商品类型列
增加商品类型标识列，用不同颜色的标签区分：
- 计量商品：橙色标签
- 普通商品：灰色标签

```javascript
{field: 'product_type_text', title: '商品类型', width: 100, templet: function(d){
    if (d.product_type == 2) {
        return '<span class="layui-badge layui-bg-orange">' + d.product_type_text + '</span>';
    } else {
        return '<span class="layui-badge layui-bg-gray">' + d.product_type_text + '</span>';
    }
}}
```

#### 2.2 库存显示优化
库存列根据商品类型和库存状态显示不同颜色：
- 无库存：红色加粗
- 库存不足：橙色加粗
- 库存充足：绿色

```javascript
{field: 'current_stock', title: '当前库存', width: 120, sort: true, templet: function(d){
    var stockDisplay = d.stock_display || d.current_stock;
    var warningStock = d.warning_stock || 0;
    
    // 根据商品类型判断库存状态
    var isLowStock = false;
    if (d.product_type == 2) {
        // 计量商品用weight_stock判断
        isLowStock = (d.weight_stock || 0) <= 0 || (d.weight_stock || 0) <= warningStock;
    } else {
        // 普通商品用stock判断
        isLowStock = (d.stock || 0) <= 0 || (d.stock || 0) <= warningStock;
    }
    
    if ((d.current_stock || 0) <= 0) {
        return '<span style="color: red; font-weight: bold;">' + stockDisplay + '</span>';
    } else if (isLowStock) {
        return '<span style="color: orange; font-weight: bold;">' + stockDisplay + '</span>';
    } else {
        return '<span style="color: green;">' + stockDisplay + '</span>';
    }
}}
```

#### 2.3 新增库存单位列
显示正确的库存单位：
- 计量商品：显示重量单位（kg/斤/克），橙色字体
- 普通商品：显示"件"，灰色字体

```javascript
{field: 'stock_unit', title: '库存单位', width: 100, templet: function(d){
    if (d.product_type == 2) {
        return '<span style="color: #ff7f00;">' + (d.stock_unit || 'kg') + '</span>';
    } else {
        return '<span style="color: #666;">件</span>';
    }
}}
```

## 修复效果

### 修复前
- 所有商品都显示数量库存
- 计量商品的重量库存被忽略
- 库存状态筛选不准确
- 无法区分商品类型

### 修复后
- ✅ 计量商品正确显示重量库存（如：5kg、3斤）
- ✅ 普通商品正确显示数量库存（如：10件）
- ✅ 库存状态筛选准确，根据商品类型判断
- ✅ 清晰的商品类型标识
- ✅ 正确的库存单位显示
- ✅ 根据库存状态的颜色提示

## 技术要点

1. **数据库字段使用**：
   - `products.product_type`：商品类型（1=普通商品，2=计量商品）
   - `shop_product_inventory.stock`：普通商品数量库存
   - `shop_product_inventory.weight_stock`：计量商品重量库存
   - `product_inventory.weight_unit`：计量商品重量单位

2. **逻辑判断**：
   - 使用 SQL 表达式进行复合条件查询
   - 前端 JavaScript 动态判断和渲染
   - 兼容性处理（默认值设置）

3. **用户体验**：
   - 颜色区分不同状态
   - 清晰的类型标识
   - 准确的单位显示

## 测试建议

1. **功能测试**：
   - 测试有库存/无库存筛选功能
   - 验证计量商品和普通商品的库存显示
   - 确认库存预警颜色显示

2. **数据验证**：
   - 检查计量商品的重量库存数据
   - 验证普通商品的数量库存数据
   - 确认库存单位的正确性

3. **界面测试**：
   - 验证商品类型标签显示
   - 检查表格列宽和布局
   - 测试响应式显示效果

## 相关文件

- `application/admin/controller/SinventoryController.php`：后端控制器
- `application/admin/view/sinventory/stock.html`：前端页面
- 数据库表：`shop_product_inventory`、`products`、`product_inventory`

## 关联功能

此修复与以下功能保持一致：
- 入库单和出库单的计量商品处理
- 订单系统的计量商品处理
- 商品管理的计量商品SKU配置

---

**修复完成时间**：2024年1月
**修复人员**：AI Assistant
**测试状态**：待测试 