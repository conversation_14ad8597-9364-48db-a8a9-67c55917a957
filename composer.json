{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "require": {"php": ">=5.6.0", "topthink/framework": "5.1.*", "aliyuncs/oss-sdk-php": "^2.3", "phpoffice/phpexcel": "^1.8", "phpmailer/phpmailer": "^6.0", "dh2y/think-ueditor": "2.*", "phpoffice/phpspreadsheet": "^1.6", "jdorn/sql-formatter": "^1.2", "fabpot/goutte": "^4.0", "topthink/think-captcha": "2.0.*", "w7corp/easywechat": "5.*", "endroid/qr-code": "^4.6", "mk-j/php_xlsxwriter": "^0.39.0", "spatie/simple-excel": "^1.13", "picqer/php-barcode-generator": "2.3.*", "yly-openapi/yly-openapi-sdk": "^2.0", "zhensoft/lizengbang": "dev-master"}, "autoload": {"psr-4": {"app\\": "application"}}, "extra": {"think-path": "thinkphp"}, "config": {"preferred-install": "dist", "allow-plugins": {"topthink/think-installer": true, "easywechat-composer/easywechat-composer": true}}}