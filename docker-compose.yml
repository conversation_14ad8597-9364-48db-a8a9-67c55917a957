services:
  # PHP服务
  php:
    build:
      context: ./docker/php
      dockerfile: Dockerfile
    container_name: tp5_php
    volumes:
      - ./:/var/www/html
    restart: always
    depends_on:
      - mysql
    networks:
      - app_network

  # Nginx服务
  nginx:
    image: nginx:latest
    container_name: tp5_nginx
    ports:
      - "80:80"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/nginx/logs:/var/log/nginx
    depends_on:
      - php
    restart: always
    environment:
      - TZ=Asia/Shanghai
    networks:
      - app_network

  # MySQL服务
  mysql:
    image: mysql:5.7
    container_name: tp5_mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: fresh_store
      MYSQL_ROOT_HOST: '%'
    command: --lower_case_table_names=1
    ports:
      - "3306:3306"
    volumes:
      - /Users/<USER>/docker/wwwroot/mysql:/var/lib/mysql
    restart: always
    networks:
      - app_network

networks:
  app_network:
    driver: bridge
    name: tp5_network 