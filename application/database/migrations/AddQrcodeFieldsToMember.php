<?php
namespace app\database\migrations;

use think\migration\Migrator;
use think\migration\db\Column;

class AddQrcodeFieldsToMember extends Migrator
{
    /**
     * 迁移执行方法
     * @return void
     */
    public function up()
    {
        // 添加字段
        $this->table('client_member')
            ->addColumn('qrcode_value', 'text', ['null' => true, 'comment' => '二维码base64值'])
            ->addColumn('qrcode_expire_time', 'datetime', ['null' => true, 'comment' => '二维码过期时间'])
            ->update();
    }

    /**
     * 迁移回滚方法
     * @return void
     */
    public function down()
    {
        // 删除字段
        $this->table('client_member')
            ->removeColumn('qrcode_value')
            ->removeColumn('qrcode_expire_time')
            ->update();
    }
} 