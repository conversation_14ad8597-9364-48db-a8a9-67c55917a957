<?php

namespace app\cnapi\controller;

use think\Controller;
use think\Session;
use think\Request;
use think\Db;

class IndexController extends Controller
{
    public function index()
    {
        $action = $_REQUEST['action'];
        $request = $_REQUEST;
        //当前类里面该方法存在
        if (method_exists($this, $action)) {
            $this->$action($request);
            die;
        }
        $rs = [
            'sta' => 100,
            'msg' => "没有找到对应方法！",
        ];
        echo json_encode($rs);
        die;
    }

    public function check_token($token)
    {
        if ($token == '') {
            $rs = [
                'sta' => 100,
                'msg' => "token不能为空！",
            ];
            echo json_encode($rs);
            die;
        }
        //token验证
        vendor('get_token.Jwt');
        $jwt = new \Jwt();
        $jwt->setKey(TOKEN_KEY);//key
        $verifyResult = $jwt->verifyToken($token);
        if (!$verifyResult) {
            $rs = [
                'sta' => 403,
                'msg' => "token验证无效！",
            ];
            echo json_encode($rs);
            die;
        } else {
            if (time() > $jwt->getExp()) {
                $rs = [
                    'sta' => 403,
                    'msg' => "token过期！",
                ];
                echo json_encode($rs);
                die;
            }
            return json_encode($jwt->getClaim());
        }
    }

    //获取模板
    public function get_config($request)
    {
        $param = Request::instance()->param();
        $id = isset($param['id']) ? $param['id'] : "";
        $token = isset($param['token']) ? $param['token'] : "";
        if ($id == '' || $token == '') {
            $rs = [
                'sta' => 100,
                'msg' => "重要参数丢失！",
            ];
            echo json_encode($rs);
            die;
        }
        $check_token = json_decode($this->check_token($token), 1);
        //找对应的画册
        $giftcompany = Db::table("plat_giftcompany")
            ->where("id", $param['id'])
            ->where("is_del", 1)
            ->find();
        if ($giftcompany == '') {
            //把这个人的ip记下来,这里记录ip干嘛，没看懂是为了做什么。
            $ip_num = Db::table("plat_ip_log")
                ->where("ip", $_SERVER["REMOTE_ADDR"])
                ->value('num');
            if ($ip_num != '') {
                Db::table("plat_ip_log")->where("ip", $_SERVER["REMOTE_ADDR"])->update(['num' => $ip_num + 1, 'updatetime' => time()]);
            } else {
                $ip_log = [
                    'ip' => $_SERVER["REMOTE_ADDR"],
                    'num' => 1,
                    'addtime' => time(),
                    'updatetime' => time()
                ];
                Db::table("plat_ip_log")->insert($ip_log);
            }
            $rs = [
                'sta' => 100,
                'msg' => "未找到对应信息！",
            ];
            echo json_encode($rs);
            die;
        }
        //找对应的模板
        $theme = Db::table("client_electronic_picture")
            ->where("id", $giftcompany['theme_id'])
            ->where("is_del", 1)
            ->find();
        if ($theme == '') {
            $rs = [
                'sta' => 100,
                'msg' => "未找到对应模板！",
            ];
            echo json_encode($rs);
            die;
        }
        //看模板上面固定分享设置如果是1，则使用模板上面的
        if($theme['is_fx']=='1'){
            $share_title=$theme['share_title'];
            $share_desc=$theme['share_desc'];
            $share_img=$theme['share_img'];
        }else {
            $share_title=$giftcompany['share_title'];
            $share_desc=$giftcompany['share_desc'];
            $share_img=$giftcompany['share_img'];
        }
        
        
        
        $theme['company'] = $giftcompany['company'] != '' ? $giftcompany['company'] : $theme['company'];
        $theme['linkman'] = $giftcompany['linkman'] != '' ? $giftcompany['linkman'] : $theme['linkman'];
        $theme['linktel'] = $giftcompany['linktel'] != '' ? $giftcompany['linktel'] : $theme['linktel'];
        $theme['share_title'] =$share_title ;
        $theme['share_desc'] = $share_desc;
        $theme['share_img'] = $share_img;
        $theme['is_fang'] = $giftcompany['is_fang'];
        //找到会员
        $phone = Db::table('client_member_list')->where("id", $check_token['id'])->value('phone');
        if ($giftcompany['is_fang'] == 0 && $phone != '') {
            //这里需要改成1，测试阶段为了方便改成了0
            $theme['is_fang'] = 1;  
        }

        vendor('wxshare.jssdk');
        $url=isset($param['url'])?$param['url']:"";
        //客户端需要对这个参数编码，服务端需要url解码
        $jssdk = new \JSSDK(appid, appsecret,$url);
        $signPackage = $jssdk->getSignPackage();
        $theme['appId'] = $signPackage['appId'];
        $theme['timestamp'] = $signPackage['timestamp'];
        $theme['nonceStr'] = $signPackage['nonceStr'];
        $theme['signature'] = $signPackage['signature'];

        $theme_no = [
            'is_fang' => $theme['is_fang'],
            'share_title' => $theme['share_title'],
            'share_desc' => $theme['share_desc'],
            'share_img' => $theme['share_img'],
            'appId' => $theme['appId'],
            'timestamp' => $theme['timestamp'],
            'nonceStr' => $theme['nonceStr'],
            'signature' => $theme['signature']
        ];

        if ($giftcompany['is_fang'] == 0 && $phone=='') {
            //非会员不允许访问
            $rs = [
                'sta' => 200,
                'msg' => "获取成功！",
                //'res' => $theme_no
                 'res' => $theme
            ];
        } else {
            $rs = [
                'sta' => 200,
                'msg' => "获取成功！",
                'res' => $theme
            ];
        }
        //logRes(json_encode($param), 'api');
        echo json_encode($rs);
        die;
    }



    //获取分类
    public function get_classify($request)
    {
        $param = Request::instance()->param();
        //模板id
        $theme_id = isset($param['theme_id']) ? $param['theme_id'] : "";
        $token = isset($param['token']) ? $param['token'] : "";
        $this->check_token($token);
        if ($theme_id == '' || $token == '') {
            $rs = [
                'sta' => 100,
                'msg' => "重要参数丢失！",
            ];
            echo json_encode($rs);
            die;
        }
        //找对应模板
        $classify_id = Db::table("client_electronic_picture_classify")
            ->where("theme_id", $theme_id)
            ->order("o asc")
            ->select();
        $where = "is_del=1";
        if ($classify_id != []) {
            $classify_id_str = "";
            foreach ($classify_id as $k => $v) {
                $classify_id_str .= $v['classify_id'] . ',';
            }
            $classify_id_str = substr($classify_id_str, 0, strlen($classify_id_str) - 1);
            $where .= " and id in ($classify_id_str)";
        }
        if ($classify_id_str == '') {
            $rs = [
                'sta' => 200,
                'msg' => '获取成功！',
                'res' => []
            ];
            echo json_encode($rs);
            die;
        }
        //找分类
        $classifylist = Db::table("client_good_classifylist")
            ->where($where)
            ->field('id,name,o,type')
            ->orderRaw(" FIND_IN_SET(id, '$classify_id_str')")  //tp5根据where里面的in排序
            ->select();
        foreach ($classifylist as $k => $v) {
            if ($v['type'] == '') {
                $classifylist[$k]['type'] = "商品列表";
            }
        }
        $rs = [
            'sta' => 200,
            'msg' => '获取成功！',
            'res' => $classifylist
        ];
        echo json_encode($rs);
        die;
    }


    //获取分类的商品
    public function get_goodlist($request)
    {
        //模拟延迟
        //sleep(1);
        $param = Request::instance()->param();
        $classify_id = isset($param['classify_id']) ? $param['classify_id'] : "";
        $token = isset($param['token']) ? $param['token'] : "";
        $this->check_token($token);
        if ($classify_id == '' || $token == '') {
            $rs = [
                'sta' => 100,
                'msg' => "重要参数丢失！",
            ];
            echo json_encode($rs);
            die;
        }

        //找分类
        $classifylist = Db::table("client_good_classifylist")
            ->where("id", $classify_id)
            ->field('type')
            ->find();
        if ($classifylist == '') {
            $rs = [
                'sta' => 100,
                'msg' => "未找到对应信息！",
            ];
            echo json_encode($rs);
            die;
        }
        if ($classifylist['type'] == '' || $classifylist['type'] == '商品列表') {
            //找对应的商品
            $res = Db::table('client_good')
                ->where("classify_id", $classify_id)
                ->field('goodsname as name,goods_thumb as file,id,o')
                ->order('o', 'asc')
                ->select();
        } else {
            $res = Db::table('client_classify_type')
                ->where("cid", $classify_id)
                ->field('file_name as name,img as file,id,o,extension')
                ->order('o', 'asc')
                ->select();
        }
        if ($classifylist['type'] == '') {
            $classifylist['type'] = '商品列表';
        }
        $rs = [
            'sta' => 200,
            'msg' => '获取成功！',
            'res' => $res,
            'type' => $classifylist['type']
        ];
        echo json_encode($rs);
        die;
    }



    //获取商品详情
    public function get_goodinfo($request)
    {
        $param = Request::instance()->param();
        $g_id = isset($param['g_id']) ? $param['g_id'] : "";
        $token = isset($param['token']) ? $param['token'] : "";
        $this->check_token($token);
        if ($g_id == '' || $token == '') {
            $rs = [
                'sta' => 100,
                'msg' => "重要参数丢失！",
            ];
            echo json_encode($rs);
            die;
        }
        $good = Db::table('client_good')
            ->where("id", $g_id)
            ->field('goodsname,goodsintegral,market_integral,goods_thumb,weight,volume,unit,goodspc,classify_name,classify_id')
            ->find();
            
            //把商品详情富文本里面的src替换成data-src
        $good['goodspc']= str_replace("src", "data-src", $good['goodspc']);
            
        $rs = [
            'sta' => 200,
            'msg' => '获取成功！',
            'res' => $good
        ];
        echo json_encode($rs);
        die;
    }



    //添加浏览记录
    public function add_history_log($request)
    {
        $param = Request::instance()->param();
        $token = isset($param['token']) ? $param['token'] : "";
        $check_token = json_decode($this->check_token($token), 1);
        $id = $param['id'] ? $param['id'] : "";
        if ($id == '' || $token == '') {
            $rs = [
                'sta' => 100,
                'msg' => "重要参数丢失！",
            ];
            echo json_encode($rs);
            die;
        }
        $clientkeynum = Db::table('plat_giftcompany')->where("id", $id)->value('basekeynum');
        
        //更新浏览次数
        $sql = "update plat_giftcompany set click_num=click_num+1 where id='$id'"; 
        $up_flag = Db::execute( $sql );         
        
        //找有没有这条
        $log = Db::table('client_giftcompany_log')->where("mid", $check_token['id'])->where("huace_id", $id)->find();
        if ($log) {
            //已存在 更新时间
            $c_res = Db::table('client_giftcompany_log')->where('id', $log['id'])->update(['update_time' => time()]);
        } else {
            //添加浏览记录
            $l_arr = [
                'mid' => $check_token['id'],
                'clientkeynum' => $clientkeynum,
                'addtime' => time(),
                'update_time' => time(),
                'huace_id' => $id
            ];
            $c_res = Db::table('client_giftcompany_log')->insert($l_arr);
        }
        if ($c_res) {
            $rs = [
                'sta' => 200,
                'msg' => "操作成功！",
            ];
            echo json_encode($rs);
            die;
        } else {
            $rs = [
                'sta' => 100,
                'msg' => "操作失败！",
            ];
            echo json_encode($rs);
            die;
        }
    }



    //浏览记录
    public function get_log($request)
    {
        $param = Request::instance()->param();
        $token = isset($param['token']) ? $param['token'] : "";
        $check_token = json_decode($this->check_token($token), 1);
        if ($token == '') {
            $rs = [
                'sta' => 100,
                'msg' => "重要参数丢失！",
            ];
            echo json_encode($rs);
            die;
        }
        if ($check_token['id'] == '') {
            $rs = [
                'sta' => 100,
                'msg' => "重要参数丢失！",
            ];
            echo json_encode($rs);
            die;
        }
        $mid = $check_token['id'];
        //找这个人的浏览记录
        $giftcompany_log = Db::table("client_giftcompany_log")
            ->where("mid", $mid)
            ->order('update_time', 'desc')
            ->limit(0, 10)
            ->field('huace_id')
            ->select();
        foreach ($giftcompany_log as $k => $v) {
            $giftcompany_log[$k]['info'] = Db::table('plat_giftcompany')
                ->where("id", $v['huace_id'])
                ->field('company,linkman,linktel,share_title,share_desc,share_img')
                ->find();
        }
        $rs = [
            'sta' => 200,
            'msg' => "获取成功！",
            'res' => $giftcompany_log
        ];
        echo json_encode($rs);
        die;
    }






    //绑定手机号发送短信验证码
    public function set_code($request)
    {
        $param = Request::instance()->param();
        $id = isset($param['id']) ? $param['id'] : "";
        $basekeynum = Db::table("plat_giftcompany")->where("id", $id)->value('basekeynum');
        $phone = isset($param['phone']) ? $param['phone'] : "";
        $token = isset($param['token']) ? $param['token'] : "";
        $this->check_token($token);
        if ($id == '' || $token == '') {
            $rs = [
                'sta' => 100,
                'msg' => "重要参数丢失！",
            ];
            echo json_encode($rs);
            die;
        }
        if ($phone == '' || $phone=='undefined') {
            $rs = [
                'sta' => 100,
                'msg' => "手机号不能为空！",
            ];
            echo json_encode($rs);
            die;
        }
        //找短信配置
        $duanxinsetinfo = Db::table("plat_sms_set")->where("basekeynum", "电子画册")->find();
        $content = $duanxinsetinfo['code_content'];
        $userid = $duanxinsetinfo['username'];
        $password = $duanxinsetinfo['password'];
        $qUrl = $duanxinsetinfo['url'] . "/CASServer/SmsAPI/SendMessage.jsp";
        if ($userid == '' || $password == '') {
            $return_arr['sta'] = 100;
            $return_arr['msg'] = "短信账号或者密码为空！";
            echo json_encode($return_arr);
            die;
        }
        $code = rand(1000, 9999);
        $content = str_replace("{code}", $code, $content);
        $msg = $content;
        $sendtime = date("Y-m-d H:i:s", time());
        $datalog['words'] = $msg;
        $datalog['time'] = time();
        $datalog['phonenum'] = $param['phone'];


        //短时间内不能重复获取，简易的判断
        $plat_sms_code_info = Db::table("plat_sms_code")->where("phone", $param['phone'])->order("id", "desc")->find();

        //同一个手机号3分钟内不能重复获取验证码
        if (time() <$plat_sms_code_info['time'] + 60 * 3) {
            $rt["sta"] = 0;
            $rt["msg"] = "短验证码获取频繁，请稍候再试！";
            echo json_encode($rt);
            die;
        }


        $add_code["phone"] = $param['phone'];
        $add_code["code"] = $code;
        $add_code["time"] = time();
        $add_code['clientkeynum'] = $basekeynum;
        Db::table("plat_sms_code")->insert($add_code);


        $qUrl .= '?userid=' . $userid . '&password=' . urlencode($password) . '&destnumbers=' . $param['phone'] .
            '&msg=' . urlencode($msg) . '&sendtime=' . urlencode($sendtime);
        $xmlstring = file_get_contents($qUrl);
        $return_arr['sta'] = 200;
        $return_arr['msg'] = "验证码已发送";
        $datalog['api_return_content'] = $xmlstring;
        Db::table('plat_sms_log')->insert($datalog);
        echo json_encode($return_arr);
    }



    //验证手机号并绑定会员
    public function check_phone($request)
    {
        $param = Request::instance()->param();
        $phone = isset($param['phone']) ? $param['phone'] : "";
        $code = isset($param['code']) ? $param['code'] : "";
        $token = isset($param['token']) ? $param['token'] : "";
        $check_token = json_decode($this->check_token($token), 1);
        if ($check_token['id'] == '' || $phone == '' || $code == '' || $token == '') {
            $rs = [
                'sta' => 100,
                'msg' => "重要参数丢失！",
            ];
            echo json_encode($rs);
            die;
        }
        //检测验证码是否正确
        $sms_code = Db::table('plat_sms_code')->where("phone", $phone)->order("id", 'desc')->find();
        if ($code != $sms_code['code']) {
            $rs = [
                'sta' => 100,
                'msg' => "验证码不正确！",
            ];
            echo json_encode($rs);
            die;
        }
        if (time() > $sms_code['time'] + 60 * 10) {
            $rs = [
                'sta' => 100,
                'msg' => "验证码已过期！",
            ];
            echo json_encode($rs);
            die;
        }
        //绑定
        $user = Db::table("client_member_list")->where("phone", $phone)->find();
        if ($user != '') {
            $rs = [
                'sta' => 100,
                'msg' => "该手机号已使用！",
            ];
            echo json_encode($rs);
            die;
        }
        $res = Db::table("client_member_list")->where("id", $check_token['id'])->update(['phone' => $phone]);
        if (!$res) {
            $rs = [
                'sta' => 100,
                'msg' => "操作失败！",
            ];
            echo json_encode($rs);
            die;
        } else {
            $rs = [
                'sta' => 200,
                'msg' => "操作成功！",
            ];
            echo json_encode($rs);
            die;
        }
    }






    //获取code,封装了网页授权
    public function get_code($request)
    {
        header("Content-Type: text/html;charset=utf-8");
        $param = Request::instance()->param();
        $id = isset($param['id']) ? $param['id'] : "";
        $url = isset($param['url']) ? $param['url'] : "";
        $state = isset($param['state']) ? $param['state'] : "";
        if ($state != '') {
            $state = explode('-', $param['state']);
            $id = $state[0];
            $url = $state[1];
        }
        if ($url == '' || $id == '') {
            $rs = [
                'sta' => 100,
                'msg' => "参数丢失！",
            ];
            echo json_encode($rs);
            die;
        }
        $scope = $id . "-" . urlencode($url);
        $code = isset($param['code']) ? $param['code'] : "";
        if ($code == '') {
            $url_get_code = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . appid . "&redirect_uri=" . REDIRECT_URI . "&response_type=code&scope=snsapi_userinfo&state=" . $scope . "#wechat_redirect";
            Header("Location: $url_get_code");
        }
        $url_get_id = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" . appid . "&secret=" . appsecret . "&code=" . $code . "&grant_type=authorization_code";
        $res = json_decode(file_get_contents($url_get_id), 1);
        
        //继续拉取用户信息通过openid和accesstoken
         $access_token=$res['access_token'];
         $openid=$res['openid'];
         $userinfo_url="https://api.weixin.qq.com/sns/userinfo?access_token=".$access_token."&openid=".$openid."&lang=zh_CN";
         $userinfo_arr=json_decode(file_get_contents($userinfo_url), 1);
        if ($res) {
            $this->get_user_new($res, $scope,$userinfo_arr);
        }


    }

    public function get_user_new($res, $scope,$userinfo_arr)
    {
        if (isset($res['unionid']) && $res['unionid'] != '') {
            $scope = explode('-', $scope);
            $id = $scope[0];
            $url = $scope[1];
            //生成会员
            //找找有没有这个会员
            $user = Db::table("client_member_list")->where("unionid", $res['unionid'])->find();
            //找画册
            $basekeynum = Db::table('plat_giftcompany')->where("id", $id)->value('basekeynum');
            if ($user == '') {
                //生成
                $u_arr = [
                    'unionid' => $res['unionid'],
                    'openid' => $res['openid'],
                    'nickname'=>$userinfo_arr['nickname'],
                    'headimgurl'=>$userinfo_arr['headimgurl'],
                    'addtime' => time(),
                    'basekeynum' => $basekeynum
                ];
                Db::table('client_member_list')->insert($u_arr);
                $user = Db::table("client_member_list")->where("unionid", $res['unionid'])->find();
            }else {
                 //更新
                $u_arr = [
                    'nickname'=>$userinfo_arr['nickname'],
                    'headimgurl'=>$userinfo_arr['headimgurl'],
                ];
                Db::table('client_member_list')->where("unionid", $res['unionid'])->update($u_arr);            
                $user = Db::table("client_member_list")->where("unionid", $res['unionid'])->find();
            }
            //获取token
            vendor('get_token.Jwt');
            $jwt = new \Jwt();
            $jwt->setKey(TOKEN_KEY);//key
            $jwt->setIat(time());//签发时间
            $jwt->setExp(time() + 60 * 60 * 24);//过期时间为1天
            $jwt->setClaim(['id' => $user['id'], 'nickname' => $user['phone']]);//存储数据
            //生成token并获取
            $token = $jwt->getToken();
            if ($token) {
                $new_url = urldecode($url) . "&token=" . $token . "&overdue=" . $jwt->getExp();
                
                //获取回调地址里的域名是否在授权域名列表，如果不在则拦截wl@20211013
               $check_host=parse_url(urldecode($url))['host'];
               $auth_back_url_arr=explode(",",AUTH_BACK_URL);
                if(!in_array($check_host,$auth_back_url_arr)){
                     error_tips_web("该域名未在授权范围内，请联系管理员添加！");
                     die;      
                }
                
                Header("Location: $new_url");
                die;
            } else {
                $rs = [
                    'sta' => 100,
                    'msg' => "获取用户信息失败！",
                ];
                echo json_encode($rs);
                die;
            }

        } else {
            $rs = [
                'sta' => 101,
                'msg' => "获取用户信息失败！",
            ];
            echo json_encode($rs);
            die;
        }
    }

    
    //添加画册浏览次数接口
    
    
    
    
    //分享成功回调接口
    public  function  notify_share_success(){
        $param = Request::instance()->param();
        $token = isset($param['token']) ? $param['token'] : "";
        $check_token = json_decode($this->check_token($token), 1);
        $mid=$check_token['id']; //会员的id
        $id = $param['id'] ? $param['id'] : ""; //画册id
        
        $link = $param['link'] ? $param['link'] : ""; 
        $desc = $param['desc'] ? $param['desc'] : "";  
        $title = $param['title'] ? $param['title'] : ""; 
        $img_url = $param['imgUrl'] ? $param['imgUrl'] : ""; 
        $share_type = $param['share_type'] ? $param['share_type'] : ""; 
        if ($id == '' ) {
            $rs = [
                'sta' => 100,
                'msg' => "重要参数丢失！",
            ];
            echo json_encode($rs);
            die;
        }
        $clientkeynum = Db::table('plat_giftcompany')->where("id", $id)->value('basekeynum');
        $memberinfo = Db::table('client_member_list')->where("id", $mid)->find();
        //画册分享记录
        $insert['clientkeynum']=$clientkeynum;
        $insert['addtime']=time();
        $insert['mid']=$mid;
        $insert['nickname']= $memberinfo['nickname'];
        $insert['huace_id']=$id;
        $insert['title']=$title;
        $insert['desc']=$desc;
        $insert['link']=$link;
        $insert['img_url']=$img_url;
        $insert['share_type']=$share_type;
        Db::table('client_giftcompany_share_log')->insert($insert);
        //更新分享次数
        $sql = "update plat_giftcompany set share_num=share_num+1 where id='$id'"; 
        $up_flag = Db::execute( $sql ); 
   
        if ($up_flag) {
            $rs = [
                'sta' => 200,
                'msg' => "操作成功！",
            ];
            echo json_encode($rs);
            die;
        } else {
            $rs = [
                'sta' => 100,
                'msg' => "操作失败！",
            ];
            echo json_encode($rs);
            die;
        }      

    }





}
