<?php

namespace app\cnapi\controller;

use think\Controller;
use think\Session;
use think\Request;
use think\Db;

class  CenterController extends Controller
{
    //返回结果sta和msg，sta==1是成功，sta=0是失败
    
    
    //入口方法
    public function index()
    {
        
        $ip_list_allow=["************","**************"];
        $remote_addr = $_SERVER['REMOTE_ADDR'];
        //ip白名单鉴权
         if (!in_array($remote_addr, $ip_list_allow)) {
                 logRes($remote_addr . "请把该ip添加到配置文件！", "ip_deny"); //记录登录日志
                  $rt='{"sta": 0, "msg": "请先配置您的ip'.$remote_addr.'到白名单中。"} ';
                  logRes($rt, 'apicenter');
                  $this->out_back($rt);die;
        }        
        
        
        $action = $_REQUEST['action'];
        $request = $_REQUEST;
        //当前类里面该方法存在
        if (method_exists($this, $action)) {
            $this->$action($request);
            die;
        }
        $rt = [
            'sta' => '0',
            'msg' => "没有找到对应方法！",
        ];
        $this->out_back($rt);die;
    }
    
    
    //返回方法
    private function out_back($rt){
     //可以扩展，比如记录日志啥的    
      if(is_array($rt)){
            echo json_encode($rt,JSON_UNESCAPED_UNICODE);die;
          
      }else {
           echo json_encode(json_decode($rt,1),JSON_UNESCAPED_UNICODE);die;
      }
        
    }
    

    //测试方法
    private function test (){
        
        echo 'this is a test page!';
    }
  
    //获取accesstoken
    private function  get_accesstoken (){
        vendor('wxshare.jssdk');
        $jssdk = new \JSSDK(appid, appsecret,'');
        $accesstoken = $jssdk->getAccessToken();
        $info['accesstoken']=$accesstoken;
        
        $rt['sta']=1;
        $rt['msg']='ok';
        $rt['info']=$info;
        $this->out_back($rt);die;
    }

    //获取jsapiticket，目前在旧版电子画册只调用这个接口获取jsapiticket，然后签名还是旧版电子画册自己生成，也就是当前url以及appid都在旧版电子画册设置
    private function  get_jsapiticket (){
        vendor('wxshare.jssdk');
        $jssdk = new \JSSDK(appid, appsecret,'');
        $jsapiticket = $jssdk->getJsApiTicket();
        $info['jsapiticket']=$jsapiticket;
        
        $rt['sta']=1;
        $rt['msg']='ok';
        $rt['info']=$info;
        $this->out_back($rt);die;
    }

}
