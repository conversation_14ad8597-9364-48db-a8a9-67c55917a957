<?php

namespace app\cnapi\controller;

use app\cnadmin\model\FreightTemplate;
use app\cnapi\model\ClientCustomerRegister;
use app\cnapi\model\ShoppingCart;
use app\cnapi\model\Users;
use EasyWeChat\Payment\Order;
use think\Cache;
use think\Controller;
use think\Db;
use think\Exception;
use think\Log;
use think\Request;
use EasyWeChat\Foundation\Application;

class ApiController extends Controller
{

    public function _initialize() {}

    public function getConfig()
    {
        // 查前台域名的客户
        $domain = Request::instance()->param('domain');
        $info = Db::table('plat_client')->where(Db::raw("find_in_set('{$domain}', domain)"))->find();
        if (empty($info)) {
            $this->fail('未查询到当前域名信息');
        }

        // 查询配置
        $config = Db::table('plat_client')->field('is_open_customer_register')->where(['keynum' => $info['keynum']])->find();

        $this->successly('请求成功', ['is_open_customer_register' => $config['is_open_customer_register']]);
    }

    public function register()
    {
        $name = Request::instance()->param('name'); // 联系人
        $phone = Request::instance()->param('phone'); // 联系方式
        $companyname = Request::instance()->param('companyname'); // 公司名称
        $domain = Request::instance()->param('domain');
        if (empty($name) || empty($phone) || empty($companyname)) {
            $this->fail('必填参数不能为空');
        }

        $info = Db::table('plat_client')->where(Db::raw("find_in_set('{$domain}', domain)"))->find();
        if (empty($info)) {
            $this->fail('未查询到当前域名信息');
        }

        $insert = [
            'name' => $name,
            'phone' => $phone,
            'companyname' => $companyname,
            'basekeynum' => $info['keynum'],
            'add_time' => date('Y-m-d H:i:s', time()),
        ];

        $customer_register = new ClientCustomerRegister();

        if ($customer_register->save($insert)) {
            $this->successly('申请成功,三天之内平台管理人员会与您联系！');
        } else {
            $this->fail('申请失败');
        }
    }

    public function get_js_config()
    {
        $domain = Request::instance()->param('domain');
        $url = Request::instance()->param('url');
        $info = Db::table('plat_client')->where(Db::raw("find_in_set('{$domain}', domain)"))->find();
        $wechat_set = Db::table('plat_weixin_set')->where("basekeynum='{$info['keynum']}'")->find();
        $config = [
            'debug' => true,
            'app_id' => $wechat_set['appid'],
            'secret' => $wechat_set['appsecret'],
            'token' => 'easywechat',


            // 'aes_key' => null, // 可选

            'log' => [
                'level' => 'debug',
                'file' => '/tmp/easywechat.log', // XXX: 绝对路径！！！！
            ],
        ];

        $app = new Application($config);
        $js = $app->js;
        $js = $js->setUrl($url);

        $config = $js->config(["updateTimelineShareData", "updateAppMessageShareData"], false);
        $config = json_decode($config, true);
        $this->successly('请求成功', $config);
    }

    private function check_token($token)
    {
        $user_info = Cache::get($token);
        if (empty($user_info)) {
            $this->fail('未登录', 401);
        }

        if ($user_info['type'] != 1) {
            $this->fail('未登录', 4001);
        }
    }

    public function wx_login()
    {
        $code = Request::instance()->param('code');
        $domain = Request::instance()->param('domain');
        $info = Db::table('plat_client')->where(Db::raw("find_in_set('{$domain}', domain)"))->find();
        $wechat_set = Db::table('plat_weixin_set')->where("basekeynum='{$info['keynum']}'")->find();
        $config = [
            'debug' => true,
            'app_id' => $wechat_set['appid'],
            'secret' => $wechat_set['appsecret'],
            'token' => 'easywechat',


            // 'aes_key' => null, // 可选

            'log' => [
                'level' => 'debug',
                'file' => '/tmp/easywechat.log', // XXX: 绝对路径！！！！
            ],
        ];
        try {
            $app = new Application($config);

            $user_data = $app->oauth->user();
        } catch (Exception $e) {
            Log::error($e->getMessage());
            $this->fail('获取微信授权失败');
        }


        if (empty($user_data)) {
            $this->fail('获取微信授权失败');
        }

        $open_id = $user_data->getId();
        $user = Users::where(['openid' => $open_id])->find();
        if (empty($user)) {
            // 没有注册过
            $user = new Users();
            $user->data([
                'openid' => $open_id,
                'add_time' => date('Y-m-d H:i:s'),
                'type' => 0
            ]);

            $user->save();
            $cache_data = [
                'type' => $user['type'],
                'add_time' => $user['add_time'],
                'user_id' => $user['id'],
                'account_name' => '',
                'keynum' => '',
                'basekeynum' => '',
                'table_name' => '',
            ];
        } else {
            if ($user['type'] == 1) {
                // 绑定了客户信息
                $account = Db::table('plat_account')->where(['account_id' => $user['account_id']])->find();
                if (empty($account)) {
                    $this->fail('未查询到用户信息');
                }
                $cache_data = [
                    'account_name' => $account['accountname'],
                    'keynum' => $account['keynum'],
                    'basekeynum' => $account['basekeynum'],
                    'table_name' => $account['tablename'],
                    'type' => $user['type'],
                    'add_time' => $user['add_time'],
                    'user_id' => $user['id']
                ];
            } else {
                // 未绑定客户信息
                $cache_data = [
                    'type' => $user['type'],
                    'add_time' => $user['add_time'],
                    'user_id' => $user['id'],
                    'account_name' => '',
                    'keynum' => '',
                    'basekeynum' => '',
                    'table_name' => '',
                ];
            }
        }

        $token = md5(time() . $user['id'] . $user['add_time']);
        Cache::set($token, $cache_data, 24 * 3600);
        $this->successly('登陆成功', ['token' => $token]);
    }

    public function login()
    {
        $account = Request::instance()->param('account');
        $password = Request::instance()->param('password');
        $token = Request::instance()->param('token');
        $user_info = Cache::get($token);
        Log::info(json_encode($user_info));
        $user = Users::where(['id' => $user_info['user_id']])->find();
        Log::info('user:' . json_encode($user));
        // 查询账号信息
        $account_info = Db::table('plat_account')->where(['accountname' => $account])->find();
        if (empty($account_info)) {
            $this->fail('未查询到账号信息', 500);
        }

        if ($account_info['tablename'] != 'client_customer') {
            $this->fail('该账号不可登陆');
        }

        $customer = Db::table('client_customer')->where(['keynum' => $account_info['basekeynum']])->find();

        if ($customer['is_mobile_open_login'] == 0) {
            $this->fail('您当前账号不可登录！');
        }

        $client = Db::table('plat_client')->where(['keynum' => $account_info['basekeynum']])->find();

        //        if (empty($client) || time() < $client['end_time']) {
        //            $this->fail('对不起你的账户已到期');
        //        }

        Log::info('pass:' . password($password));
        if ($account_info['accountpassword'] == (password($password))) {
            // 登陆成功 绑定账号
            // 修改用户信息
            $update_data = [
                'account_id' => $account_info['account_id'],
                'keynum' => $account_info['keynum'],
                'clientkeynum' => $account_info['parent_basekeynum'],
                'type' => 1
            ];
            Users::where(['id' => $user['id']])->update($update_data);
            $cache_data = [
                'account_name' => $account_info['accountname'],
                'keynum' => $account_info['keynum'],
                'basekeynum' => $account_info['parent_basekeynum'],
                'table_name' => $account_info['tablename'],
                'type' => 1,
                'add_time' => $user['add_time'],
                'user_id' => $user['id']
            ];
            // 更新token
            Cache::set($token, $cache_data, 24 * 3600 * 7);
            $this->successly('登陆成功', ['token' => $token, 'user_info' => $user_info]);
        } else {
            $this->fail('登陆失败');
        }
    }

    public function out_login()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);

        $user_info = Cache::get($token);

        Db::table('users')->where(['id' => $user_info['user_id']])->update([
            'account_id' => null,
            'keynum' => null,
            'clientkeynum' => null,
            'type' => 0
        ]);

        Cache::rm($token);

        $this->successly();
    }

    public function get_user_info()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);
        Log::info(json_encode($user_info));

        $customer = Db::table('client_customer')->where(['keynum' => $user_info['keynum']])->find();
        if (!empty($customer)) {
            $pay_type = [
                1 => '在线支付',
                2 => '余额支付',
                3 => '赊账'
            ];
            $user_info['pay_type'] = $pay_type[$customer['pay_type']];
            $user_info['balance'] = $customer['balance'];
            $user_info['credit_limit'] = $customer['credit_limit'];
            // 用户剩余额度
            $user_info['balance_limit'] = $customer['credit_limit'] - (-$customer['balance']);
        }

        $this->successly('请求成功', $user_info);
    }

    public function updatePassword()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);

        $account_info = Db::table('plat_account')
            ->where(['keynum' => $user_info['keynum']])
            ->find();

        $old_password = Request::instance()->param('old_password');
        $new_password = Request::instance()->param('new_password');
        $verify_new_password = Request::instance()->param('verify_new_password');

        if (empty($old_password) || empty($new_password) || empty($verify_new_password)) {
            $this->fail('必填参数不能为空');
        }


        if ($new_password != $verify_new_password) {
            $this->fail('两次输入的密码不一致');
        }

        if ($account_info['accountpassword'] != password($old_password)) {
            $this->fail('旧密码错误');
        }

        $update_data = [
            'accountpassword' => password($new_password)
        ];

        Db::table('plat_account')->where(['account_id' => $account_info['account_id']])->update($update_data);
        $this->successly('修改成功');
    }

    public function get_category()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);
        Log::info($user_info);
        //        $user_info = json_decode($user_info, true);
        $customer = Db::table('client_customer')
            ->where(['keynum' => $user_info['keynum'], 'clientkeynum' => $user_info['basekeynum']])
            ->find();

        if (empty($customer)) {
            $this->fail('未查询到客户信息');
        }

        $goods_id = Db::table('client_customer_good')
            ->where(['clientkeynum' => $user_info['basekeynum'], 'customerkeynum' => $user_info['keynum']])
            ->column('client_good_id');

        $category_id = Db::table('client_good')
            ->where(['clientkeynum' => $user_info['basekeynum']])
            ->whereIn('id', $goods_id)
            ->group('classify_id')
            ->column('classify_id');

        $category_list = Db::table('client_good_classifylist')
            ->where(['clientkeynum' => $user_info['basekeynum']])
            ->whereIn('id', $category_id)
            ->order('o', 'asc')
            ->field('id,pid,name')
            ->select();

        if (empty($category_list)) {
            $category_list = Db::table('client_good_classifylist')
                ->where(['clientkeynum' => $user_info['basekeynum']])
                ->order('o', 'asc')
                ->field('id,pid,name')
                ->select();
        }

        $this->successly('请求成功', ['list' => $category_list]);
    }


    public function goods_list()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $classify_id = Request::instance()->param('classify_id');
        $keyword = Request::instance()->param('keyword');
        $page = Request::instance()->param('page', 1);
        $page_size = Request::instance()->param('page_size', 1);

        $user_info = Cache::get($token);

        // 查询所有分类
        $categories = Db::table('client_good_classifylist')
            ->where(['clientkeynum' => $user_info['basekeynum']])
            ->field('id, name')
            ->order('o', 'asc')
            ->select();

        $goods_by_classify = [];
        foreach ($categories as $key => $category) {
            $list = Db::table('client_customer_good')
                ->field('b.client_good_id as id,b.customer_good_name as goodsname,b.customer_goodssku as goodssku,b.customer_integral as customer_integral,b.customer_market_integral as customer_market_integral')
                ->where(['b.clientkeynum' => $user_info['basekeynum'], 'b.customerkeynum' => $user_info['keynum'], 'a.classify_id' => $category['id']])
                ->order('id', 'desc')
                ->alias('b')
                ->join('client_good a', 'a.id = b.client_good_id', 'RIGHT');

            if (!empty($classify_id)) {
                $list = $list->where(['a.classify_id' => $classify_id]);
            }

            if (!empty($keyword)) {
                $list = $list->where('b.customer_good_name', 'like', '%' . $keyword . '%');
            }

            $list = $list->select();
            if (!empty($list)) {
                // 查商品表
                foreach ($list as $k => $v) {
                    $goods = Db::table('client_good')
                        ->where([
                            'id' => $v['id'],
                            'status' => 1,
                            'clientkeynum' => $user_info['basekeynum']
                        ])
                        ->find();
                    $list[$k]['goodsimg'] = $goods['goodsimg'];
                    $list[$k]['goodspic'] = $goods['goodspic'];
                    $list[$k]['goods_thumb'] = $goods['goods_thumb'];
                    $list[$k]['goodscarousel'] = $goods['goodscarousel'];
                    $list[$k]['classify_id'] = $goods['classify_id'];
                    $list[$k]['classify_name'] = $goods['classify_name'];
                    if (empty($v['market_integral'])) {
                        $list[$k]['market_integral'] = $goods['market_integral_new'];
                    }

                    if (empty($v['customer_cost_integral'])) {
                        $list[$k]['customer_cost_integral'] = $goods['goodsintegral'];
                    }
                }
                $count = Db::table('client_customer_good')
                    ->where(['clientkeynum' => $user_info['basekeynum'], 'customerkeynum' => $user_info['keynum']])
                    ->count();
            } else {
                $list = Db::table('client_good')
                    ->field('*, goodsintegral as customer_integral, market_integral_new as customer_market_integral')
                    ->where(['clientkeynum' => $user_info['basekeynum'], 'status' => 1, 'classify_id' => $category['id']])
                    ->page($page, $page_size)
                    ->order('id', 'desc');
                $count = Db::table('client_good')
                    ->where(['clientkeynum' => $user_info['basekeynum'], 'status' => 1, 'classify_id' => $category['id']]);

                if (!empty($classify_id)) {
                    $list = $list->where(['classify_id' => $classify_id]);
                    $count = $count->where(['classify_id' => $classify_id]);
                }

                if (!empty($keyword)) {
                    $list = $list->where('goodsname', 'like', "%$keyword%");
                    $count = $count->where('goodsname', 'like', "%$keyword%");
                }
                $list = $list->select();
                $count = $count->count();
            }
            if (empty($list)) {
                unset($categories[$key]);
                continue;
            }
            $goods_by_classify[][$category['id']] = $list;
        }



        // 返回分类名称和商品列表
        $this->successly('请求成功', [
            'categories' => $categories,
            'goods_by_classify' => $goods_by_classify
        ]);
    }

    public function get_goods_info()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);
        $goods_id = Request::instance()->param('id');

        // 查询 客户商品表
        $customer_goods = Db::table('client_customer_good')->where(['client_good_id' => $goods_id, 'clientkeynum' => $user_info['basekeynum'], 'customerkeynum' => $user_info['keynum']])->find();


        $goods = Db::table('client_good')->where(['id' => $goods_id, 'clientkeynum' => $user_info['basekeynum']])->find();

        

        if (empty($goods)) {
            $this->fail('商品不存在');
        }

        $goods['goods_thumb'] = explode('|', $goods['goods_thumb']);
        $goods['goodspic'] = explode('|', $goods['goodspic']);
        $goods['goodspc'] = explode('|', $goods['goodspc']);
        $goods['goodscarousel'] = explode('|', $goods['goodscarousel']);

        $cart_num = ShoppingCart::where(['goods_id' => $goods_id, 'user_id' => $user_info['user_id']])->value('num') ?? 0;

        $info = [
            'goods_thumb' => $goods['goods_thumb'],
            'goodsimg' => $goods['goodsimg'],
            'goodspic' => $goods['goodspic'],
            'goodspc' => $goods['goodspc'],
            'goodscarousel' => $goods['goodscarousel'],
            'classify_id' => $goods['classify_id'],
            'classify_name' => $goods['classify_name'],
            'market_integral' => $customer_goods['customer_market_integral'] ?? $goods['customer_market_integral'],
            'customer_cost_integral' => $goods['goodsintegral'],
            'goodsname' => $goods['goodsname'],
            'goodssku' => $goods['goodssku'],
            'weight' => $goods['weight'],
            'customer_integral' => $customer_goods['customer_integral'] ?? $goods['goodsintegral'],
            'id' => $goods['id'],
            'num' => $cart_num
        ];

        

        $this->successly('请求成功', $info);
        
    }

    public function add_shopping_cart()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);

        $goods_id = Request::instance()->param('goods_id');
        $num = Request::instance()->param('num');

        // 查询客户是否已经添加该商品了
        $is_add = ShoppingCart::where(['goods_id' => $goods_id, 'user_id' => $user_info['user_id']])->find();

        if (!empty($is_add)) {
            ShoppingCart::where(['goods_id' => $goods_id, 'user_id' => $user_info['user_id']])->setInc('num', $num);
            $this->successly('添加成功');
        }

        $insert = [
            'user_id' => $user_info['user_id'],
            'keynum' => $user_info['keynum'],
            'basekeynum' => $user_info['basekeynum'],
            'goods_id' => $goods_id,
            'num' => $num,
            'add_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];

        $shopping_cart = new ShoppingCart($insert);
        if ($shopping_cart->save()) {
            $this->successly('添加成功');
        } else {
            $this->fail('添加失败');
        }
    }

    public function shopping_cart_list()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $page = Request::instance()->param('page');
        $page_size = Request::instance()->param('page_size');

        $user_info = Cache::get($token);
        Log::error($user_info);
        $province = Request::instance()->param('province');

        $province_code = Db::table('province')->where(['name' => $province])->value('_id');


        $list = ShoppingCart::where(['basekeynum' => $user_info['basekeynum'], 'user_id' => $user_info['user_id']])
            ->page($page, $page_size)
            ->select();
        $count = ShoppingCart::where(['basekeynum' => $user_info['basekeynum'], 'user_id' => $user_info['user_id']])
            ->count();

        // 获取客户端配置
        $client = Db::table('plat_client')
            ->where('keynum', $user_info['basekeynum'])
            ->find();

        foreach ($list as $k => $v) {
            $list[$k]['goods'] = Db::table('client_customer_good')
                ->field('client_good_id as id,customer_good_name as goodsname,customer_integral as market_integral,customer_goodssku as goodssku,customer_integral as customer_cost_integral,customer_market_integral as customer_market_integral')
                ->where(['clientkeynum' => $user_info['basekeynum'], 'client_good_id' => $v['goods_id'], 'customerkeynum' => $user_info['keynum']])
                ->find();
            if (empty($list[$k]['goods'])) {
                $goods = Db::table('client_good')->field('* , goodsintegral as customer_cost_integral')->where(['id' => $v['goods_id'], 'clientkeynum' => $user_info['basekeynum']])->find();
                $list[$k]['goods'] = $goods;
                $list[$k]['goodsimg'] = $goods['goodsimg'];
                $list[$k]['goodspic'] = $goods['goodspic'];
                $list[$k]['goods_thumb'] = $goods['goods_thumb'];
                $list[$k]['goodscarousel'] = $goods['goodscarousel'];
                $list[$k]['weight'] = $goods['weight'];
                $list[$k]['supplier_id'] = $goods['supplier_id'];
            } else {
                $goods = Db::table('client_good')->where(['id' => $v['goods_id'], 'clientkeynum' => $user_info['basekeynum']])->field('* , goodsintegral as customer_cost_integral')->find();
                $list[$k]['goodsimg'] = $goods['goodsimg'];
                $list[$k]['goodspic'] = $goods['goodspic'];
                $list[$k]['goods_thumb'] = $goods['goods_thumb'];
                $list[$k]['goodscarousel'] = $goods['goodscarousel'];
                $list[$k]['weight'] = $goods['weight'];
                $list[$k]['supplier_id'] = $goods['supplier_id'];
            }

            if (empty($province_code)) {
                $list[$k]['freight'] = 0;
            } else {
                // 如果开启了运费模板
                if ($client['is_open_freight_template'] == 1) {
                    // 获取供应商信息
                    $supplier = Db::table('client_supplier')->where(['supplier_id' => $list[$k]['supplier_id']])->find();
                    if ($supplier) {
                        // 查出运费模板
                        $freight_template = FreightTemplate::where(['clientkeynum' => $user_info['basekeynum']])
                            ->whereIn('id', $supplier['freight_template_id'])
                            ->where(DB::raw("FIND_IN_SET({$province_code}, delivery_area)"))
                            ->find();

                        if ($freight_template) {
                            // 计算运费
                            $freight = $this->calculate_shipping($list[$k]['weight'] * $list[$k]['num'], $freight_template);
                            $list[$k]['freight'] = $freight;
                        } else {
                            $list[$k]['freight'] = 0;
                            $list[$k]['freight_error'] = '不支持配送当前区域';
                        }
                    } else {
                        $list[$k]['freight'] = 0;
                    }
                } else {
                    $list[$k]['freight'] = 0;
                }
            }
        }

        $this->successly('请求成功', [
            'list' => $list,
            'count' => $count
        ]);
    }

    public function update_shopping_cart_num()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);
        $cart_id = Request::instance()->param('cart_id');
        $num = Request::instance()->param('num');

        $where = [
            'user_id' => $user_info['user_id'],
            'id' => $cart_id,
            'keynum' => $user_info['keynum'],
            'basekeynum' => $user_info['basekeynum']
        ];
        ShoppingCart::where($where)->update(['num' => $num]);
        $this->successly('请求成功');
    }

    public function del_shopping_cart()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);
        $cart_id = Request::instance()->param('cart_id');

        $where = [
            'user_id' => $user_info['user_id'],
            'id' => $cart_id,
            'keynum' => $user_info['keynum'],
            'basekeynum' => $user_info['basekeynum']
        ];

        if (ShoppingCart::where($where)->delete()) {
            $this->successly('删除成功');
        } else {
            $this->fail('删除失败');
        }
    }

    public function get_address()
    {
        $list = Db::table('province')->select();

        foreach ($list as $k => $v) {
            $list[$k]['son'] = Db::table('city')->where(['province_id' => $v['province_id']])->select();
            foreach ($list[$k]['son'] as $key => $value) {
                $list[$k]['son'][$key]['son'] = Db::table('county')->where(['city_id' => $value['city_id']])->select();
            }
        }

        $this->successly('请求成功', ['list' => $list]);
    }

    /**
     * 不太建议修改 坑太多 逻辑复杂 耦合严重
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function get_order_money()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);
        Log::info(json_encode($user_info));

        $cart_id = Request::instance()->param('cart_id');

        $province = Request::instance()->param('province');

        if (empty($cart_id)) {
            $this->fail('请选择需要购买的商品');
        }

        $cart_id_arr = explode(',', $cart_id);

        // 查
        $where = [
            'keynum' => $user_info['keynum'],
            'basekeynum' => $user_info['basekeynum'],
            'user_id' => $user_info['user_id'],
            'id' => ['in', $cart_id_arr]
        ];

        $cart_list = ShoppingCart::where($where)->select();
        if (empty($cart_list)) {
            $this->fail('错误');
        }

        $customer = Db::table('client_customer')
            ->where("keynum", $user_info['keynum'])
            ->find();
        $client = Db::table('plat_client')
            ->where('keynum', $user_info['basekeynum'])
            ->find();

        $total_freight = 0;
        $province_code = Db::table('province')->where(['name' => $province])->value('_id');
        //
        if (empty($province_code)) {
            $this->fail('订单 省市区 填写错误');
        }


        $goods_ids = array_column($cart_list, 'goods_id');
        $goods_num = array_column($cart_list, 'num', 'goods_id');
        $group_data = Db::table('supplier_goods_price')
            ->where(['clientkeynum' => $user_info['basekeynum']])
            ->whereIn('goods_id', $goods_ids)
            ->field('count(id) as number, supplier_id,GROUP_CONCAT(goods_id) as goods_ids')
            ->group('supplier_id')
            ->order('number desc')
            ->select();
        if ($group_data[0]['number'] == count($goods_ids)) {
            $supplier_id = $group_data[0]['supplier_id'];
        } else {
            $supplier_id = 0;
        }
        if (!empty($supplier_id) && $client['is_open_freight_template'] == 1) {

            $supplier = Db::table('client_supplier')->where(['supplier_id' => $supplier_id])->find();
            // 查出运费模板
            $freight_template = FreightTemplate::where(['clientkeynum' => $user_info['basekeynum']])
                ->whereIn('id', $supplier['freight_template_id'])
                ->where(DB::raw("FIND_IN_SET({$province_code}, delivery_area)"))
                ->find();
            // 没有的话就是不支持
            if (empty($freight_template)) {
                $this->fail("不支持配送当前区域！");
            }
            // 计算运费
            $weight = 0;
            foreach ($cart_list as $k => $v) {
                $goods = Db::table('client_good')->where(['id' => $v['goods_id'], 'clientkeynum' => $user_info['basekeynum']])->find();
                if (empty($goods) || $goods['status'] == 0) {
                    $this->fail('商品不存在或已下架');
                }
                $weight += $goods['weight'] * $goods_num[$goods['id']];
            }
            $total_freight = $this->calculate_shipping($weight, $freight_template);
        } else if (empty($supplier_id) && $client['is_open_freight_template'] == 1) {
            $goods_group = Db::table('client_good')
                ->where(['clientkeynum' => $user_info['basekeynum']])
                ->whereIn('id', $goods_ids)
                ->field('supplier_id,GROUP_CONCAT(id) as goods_ids')
                ->group('supplier_id')
                ->select();

            foreach ($goods_group as $k => $v) {

                // 查供应商
                $supplier = Db::table('client_supplier')->where(['supplier_id' => $v['supplier_id']])->find();
                // 查出运费模板
                $freight_template = FreightTemplate::where(['clientkeynum' => $user_info['basekeynum']])
                    ->whereIn('id', $supplier['freight_template_id'])
                    ->where(DB::raw("FIND_IN_SET({$province_code}, delivery_area)"))
                    ->find();
                // 没有的话就是不支持
                if (empty($freight_template)) {
                    $this->fail("不支持配送当前区域！");
                }
                $goods_idss = explode(',', $v['goods_id']);
                $goods_weight = 0;
                foreach ($goods_idss as $k1 => $v1) {
                    $weight_goods1 = Db::table('client_good')->where(['id' => $v1])->value('weight') ?? 0;
                    $goods_weight += $weight_goods1 * $goods_num[$v1];
                }
                $total_freight += $this->calculate_shipping($goods_weight, $freight_template);
            }
        }

        foreach ($cart_list as $k => $v) {
            $goods = Db::table('client_customer_good')
                ->field('client_good_id as id,customer_good_name as goodsname,customer_integral as market_integral,customer_goodssku as goodssku,customer_integral as customer_cost_integral,customer_market_integral as customer_market_integral,customer_integral as customer_integral')
                ->where(['clientkeynum' => $user_info['basekeynum'], 'customerkeynum' => $user_info['keynum'], 'client_good_id' => $v['goods_id']])
                ->find();
            if (empty($goods)) {
                Log::info(2222);
                // 查 client_good 表
                $goods = Db::table('client_good')
                    ->where(['clientkeynum' => $user_info['basekeynum'], 'id' => $v['goods_id']])
                    ->find();
                // 取默认客户结算价
                $order_money = $goods['goodsintegral'] * $v['num'];
            } else {
                Log::info(1111);
                // 取客户结算价
                $order_money = $goods['customer_cost_integral'] * $v['num'];
                $origin_goods = Db::table('client_good')->where(['id' => $goods['id']])->find();
                $goods['supplier_id'] = $origin_goods['supplier_id'];
                $goods['freight'] = $origin_goods['freight'];
                $goods['weight'] = $origin_goods['weight'];
            }

            $total_money += $order_money;
        }


        $this->successly('请求成功', [
            'order_money' => $total_money,
            'total_money' => $total_money + $total_freight,
            'total_freight' => $total_freight,
        ]);
    }

    public function calculate_shipping($weight, $template)
    {

        Log::info(\GuzzleHttp\json_encode($weight));
        Log::info(\GuzzleHttp\json_encode($template));
        // 计算总重量
        if ($weight <= 0) {
            return 0;
        }
        if ($template['is_dynamics_first_weight'] == 1 && $weight > $template['first_weight']) {
            $template['first_weight'] = $template['dynamics_first_weight'];
        }
        // 开始计算价格
        $continue_weight = $weight - $template['first_weight'];
        $shipping_cost = $template['first_weight_price'];


        $count = $continue_weight / $template['continued_weight'];

        if ($template['is_ceil'] == 1) {
            $count = ceil($count);
        } else {
            $count = floor($count);
        }
        $shipping_cost += ($count * $template['continued_weight_price']);

        return $shipping_cost;
    }


    public function add_order_by_cart()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);
        Log::info(json_encode($user_info));

        $cart_id = Request::instance()->param('cart_id');

        $province = Request::instance()->param('province');
        $city = Request::instance()->param('city');
        $area = Request::instance()->param('area');
        $address = Request::instance()->param('address');
        $remark = Request::instance()->param('remark');
        $consignee = Request::instance()->param('consignee');
        $phone = Request::instance()->param('phone');

        if (empty($cart_id)) {
            $this->fail('请选择需要购买的商品');
        }


        $province_code = Db::table('province')->where(['name' => $province])->value('_id');

        if (empty($province_code)) {
            $this->fail('订单 省市区 填写错误');
        }

        $client = Db::table('plat_client')
            ->where('keynum', $user_info['basekeynum'])
            ->find();

        $cart_id_arr = explode(',', $cart_id);

        // 查
        $where = [
            'keynum' => $user_info['keynum'],
            'basekeynum' => $user_info['basekeynum'],
            'user_id' => $user_info['user_id'],
            'id' => ['in', $cart_id_arr]
        ];

        $cart_list = ShoppingCart::where($where)->select();
        if (empty($cart_list)) {
            $this->fail('查询购物车失败');
        }
        // 查询是否有供应商全部提供数据
        $goods_ids = array_column($cart_list, 'goods_id');
        $goods_num = array_column($cart_list, 'num', 'goods_id');
        $group_data = Db::table('supplier_goods_price')
            ->where(['clientkeynum' => $user_info['basekeynum']])
            ->whereIn('goods_id', $goods_ids)
            ->field('count(id) as number, supplier_id,GROUP_CONCAT(goods_id) as goods_ids')
            ->group('supplier_id')
            ->order('number desc')
            ->select();
        if ($group_data[0]['number'] == count($goods_ids)) {
            $supplier_id = $group_data[0]['supplier_id'];
        } else {
            $supplier_id = 0;
        }

        $total_freight = 0;
        if (!empty($supplier_id) && $client['is_open_freight_template'] == 1) {


            $supplier = Db::table('client_supplier')->where(['supplier_id' => $supplier_id])->find();
            // 查出运费模板
            $freight_template = FreightTemplate::where(['clientkeynum' => $user_info['basekeynum']])
                ->whereIn('id', $supplier['freight_template_id'])
                ->where(DB::raw("FIND_IN_SET({$province_code}, delivery_area)"))
                ->find();
            // 没有的话就是不支持
            if (empty($freight_template)) {
                $this->fail("不支持配送当前区域！");
            }
            // 计算运费
            $weight = 0;
            foreach ($cart_list as $k => $v) {
                $goods = Db::table('client_good')->where(['id' => $v['goods_id'], 'clientkeynum' => $user_info['basekeynum']])->find();
                if (empty($goods) || $goods['status'] == 0) {
                    $this->fail('商品不存在或已下架');
                }
                $weight += $goods['weight'] * $v['num'];
            }
            $total_freight = $this->calculate_shipping($weight, $freight_template);
        } else if (empty($supplier_id) && $client['is_open_freight_template'] == 1) {
            $goods_group = Db::table('client_good')
                ->where(['clientkeynum' => $user_info['basekeynum']])
                ->whereIn('id', $goods_ids)
                ->field('supplier_id,GROUP_CONCAT(id) as goods_ids')
                ->group('supplier_id')
                ->select();

            foreach ($goods_group as $k => $v) {

                // 查供应商
                $supplier = Db::table('client_supplier')->where(['supplier_id' => $v['supplier_id']])->find();
                // 查出运费模板
                $freight_template = FreightTemplate::where(['clientkeynum' => $user_info['basekeynum']])
                    ->whereIn('id', $supplier['freight_template_id'])
                    ->where(DB::raw("FIND_IN_SET({$province_code}, delivery_area)"))
                    ->find();
                // 没有的话就是不支持
                if (empty($freight_template)) {
                    $this->fail('不支持配送当前区域！');
                }
                $goods_idss = explode(',', $v['goods_id']);
                $goods_weight = 0;
                foreach ($goods_idss as $k1 => $v1) {
                    $weight_goods1 = Db::table('client_good')->where(['id' => $v1])->value('weight') ?? 0;
                    $goods_weight += $weight_goods1 * $goods_num[$v1];
                }
                $total_freight += $this->calculate_shipping($goods_weight, $freight_template);
            }
        }

        // 开始下单流程
        try {
            Db::startTrans();
            //找一下客户的销售员
            $salesperson_id = Db::table('client_customer')
                ->where("keynum", $user_info['keynum'])
                ->value('salesperson_id');
            $customer = Db::table('client_customer')
                ->where("keynum", $user_info['keynum'])
                ->find();


            $date = date('Y-m-d', time());
            $pinfo = Db::table('client_order_batch')
                ->where("date", $date)
                ->where("merchantkeynum", $user_info['keynum'])
                ->where("clientkeynum", $user_info['basekeynum'])
                ->where("type", 5)
                ->order('addtime desc')
                ->find();
            //找一下客户
            $sys_name = Db::table('client_customer')
                ->where("keynum", $user_info['keynum'])
                ->value('sys_name');
            // print_r($pinfo);
            if ($pinfo) {
                $pcname_arr = explode('-', $pinfo['batch_name']);
                $pcnum = $pcname_arr[4] + 1;
                $pcname = $sys_name . '-' . $date . '-' . $pcnum;
            } else {
                $pcname = $sys_name . '-' . $date . '-1';
            }
            //添加批次
            $pi = [
                'batch_name' => $pcname,
                'date' => $date,
                'merchantkeynum' => $user_info['keynum'],
                'clientkeynum' => $user_info['basekeynum'],
                'addtime' => time(),
                'ordernum' => count($cart_list),
                'goodsnum' => 0,
                'ordermoney' => 0,
                'excel_name' => '',
                'order_id' => '',
                'type' => 5
            ];

            $bid = Db::table('client_order_batch')->insertGetId($pi);
            $total_money = 0;
            $total_freight = 0; // 运费
            $total_num = 0;
            $order_ids = [];
            foreach ($cart_list as $k => $v) {
                $goods = Db::table('client_customer_good')
                    ->field('client_good_id as id,customer_good_name as goodsname,customer_integral as customer_integral,customer_goodssku as goodssku,customer_cost_integral as customer_cost_integral,customer_market_integral as customer_market_integral')
                    ->where(['clientkeynum' => $user_info['basekeynum'], 'customerkeynum' => $user_info['keynum'], 'client_good_id' => $v['goods_id']])
                    ->find();
                if (empty($goods)) {
                    Log::info(2222);
                    // 查 client_good 表
                    $goods = Db::table('client_good')
                        ->where(['clientkeynum' => $user_info['basekeynum'], 'id' => $v['goods_id']])
                        ->find();
                    // 取默认客户结算价
                    $order_money = $goods['goodsintegral'] * $v['num'];
                    $goods['order_money'] = $order_money;
                } else {
                    Log::info(1111);
                    // 取客户结算价
                    $order_money = $goods['customer_integral'] * $v['num'];
                    $origin_goods = Db::table('client_good')->where(['id' => $goods['id']])->find();
                    $goods['supplier_id'] = $origin_goods['supplier_id'];
                    $goods['freight'] = $origin_goods['freight'];
                    $goods['weight'] = $origin_goods['weight'];
                    $goods['order_money'] = $order_money;
                }


                $total_num += $v['num'];
                $total_money += $order_money;
                $order_sn = "Z" . date('Ymd') . str_pad(mt_rand(1, 88888), 5, '0', STR_PAD_LEFT) . substr(mt_rand(0, $phone), 0, 3);
                $is_self_goods = Db::table('client_supplier')
                    ->where(['supplier_id' => $goods['supplier_id'], 'clientkeynum' => $user_info['basekeynum']])
                    ->value('is_z');

                if ($is_self_goods == 1) {
                    $sup_price_field = 'jc_price';
                } else {
                    $sup_price_field = 'price';
                }
                $sup_price = Db::table('supplier_goods_price')
                    ->where(['supplier_id' => $goods['supplier_id'], 'clientkeynum' => $user_info['basekeynum'], 'goods_id' => $goods['id']])
                    ->value($sup_price_field) ?? 0;

                if ($client['is_open_freight_template'] == 1) {
                    $province_code = Db::table('province')->where(['name' => $province])->value('_id');

                    if (empty($province_code)) {
                        $this->fail('订单 省市区 填写错误');
                        throw new \Exception('订单 省市区 填写错误');
                        //                        $error++;
                        //                        $errormsg .= "第" . $num . "行订单 省市区 填写错误,";
                    }

                    $supplier = Db::table('client_supplier')->where(['supplier_id' => $goods['supplier_id']])->find();
                    // 查出运费模板
                    $freight_template = FreightTemplate::where(['clientkeynum' => $user_info['basekeynum']])
                        ->whereIn('id', $supplier['freight_template_id'])
                        ->where(DB::raw("FIND_IN_SET({$province_code}, delivery_area)"))
                        ->find();
                    // 没有的话就是不支持
                    if (empty($freight_template)) {
                        $this->fail("{$goods['goodsname']}不支持配送当前区域！");
                        throw new \Exception("{$goods['goodsname']}不支持配送当前区域！");
                    }
                    // 计算运费

                    $goods['freight'] = $this->calculate_shipping_cost($goods, $v['num'], $freight_template);
                    $total_freight += $goods['freight'];
                }

                //插入总订单
                $data = [
                    'consignee' => $consignee,
                    'phone' => $phone,
                    'address' => $province . $city . $area . $address,
                    'skuid' => $goods['goodssku'],
                    'goodssku' => $goods['goodssku'],
                    'goodsinfo' => $goods['goodsname'],
                    'g_num' => $v['num'],
                    'order_total_money' => $sup_price * $v['num'],
                    'goods_total_money' => $sup_price * $v['num'],
                    'order_sn' => $order_sn,
                    'merchantkeynum' => $user_info['keynum'],
                    'clientkeynum' => $user_info['basekeynum'],
                    'add_time' => time(),
                    'part_ordernum' => "0",
                    'bid' => $bid,
                    'is_pid' => 1,
                    'supplier_id' => $goods['supplier_id'],
                    'y_supplier_id' => $goods['supplier_id'],
                    'ori_ordernumber' => $v['ori_ordernumber'] ?? $order_sn,
                    'ori_remark' => '',
                    'is_remote' => '',
                    'province' => $province,
                    'city' => $city,
                    'area' => $area,
                    'detailed_address' => $address,
                    'province_code' => '',
                    'city_code' => '',
                    'area_code' => '',
                    'address_code' => '',
                    'good_specs' => $goods['specs'],
                    'ori_ordernumber_son' => '',
                    'order_time' => time(),
                    'kefu_remark' => $remark,
                    'shipping_fee' => $total_freight ?? 0,
                    'sup_price' => $sup_price,
                    'goodsintegral' => $order_money,
                    'goodsid' => $goods['id'],
                    'category_id' => Db::table('client_good')->where(['id' => $goods['id']])->value('classify_id'),
                    'sale_id' => $salesperson_id,
                    'y_goodssku' => $goods['goodssku'],
                    'company_id' => Db::table('client_customer')->where(['keynum' => $user_info['keynum'], 'clientkeynum' => $user_info['basekeynum']])->value('company_id') ?? 0,
                    'source' => 3,
                ];

                //                if ($client['is_open_customer_pay'] == 1 && ($customer['pay_type'] == 3 || $customer['pay_type'] == 2)) {
                $data['is_customer_settlement'] = 1;
                //                }

                //print_r($data);die;
                $insert1 = Db::table("client_order_info")->insertGetId($data);
                Log::info('order_sn' . $insert1);
                $order_ids[] = $insert1;
                //记日志
                $q_log = [
                    'order_sn' => $order_sn,
                    'type' => 4,
                    'content' => '客户公众号下单：' . $order_sn . "",
                    'add_time' => time(),
                    'clientkeynum' => $user_info['basekeynum'],
                    'c_name' => session("cn_accountinfo.accountname")
                ];
                $q_add = Db::table('client_order_log')->insert($q_log);

                unset($v['goods']);
            }

            if ($client['is_open_customer_pay'] == 1 && $customer['pay_type'] == 3) {
                $total_money_freight = $total_money + $total_freight;
                // 判断平台是否开启支付功能 判断客户支付方式 如果支付方式为赊账 设置批次 is_pay为1
                if ($customer['credit_limit'] < -$customer['balance'] + $total_money_freight) {
                    throw new \Exception('您的赊账额度已用完，请付清尾款');
                }
                Db::table('client_customer')
                    ->where(['clientkeynum' => $user_info['basekeynum'], 'customer_id' => $customer['customer_id']])
                    ->update(['balance' => Db::raw("balance - {$total_money_freight}")]);
                $log = [
                    'customer_id' => $customer['customer_id'],
                    'clientkeynum' => $customer['basekeynum'],
                    'type' => 2,
                    'money' => -$total_money_freight,
                    'remark' => '平台操作导入',
                    'add_time' => date('Y-m-d H:i:s'),
                    'operator' => session("cn_accountinfo.accountname"),
                    'before_money' => $customer['balance'],
                    //                    'batch_type' => 1,
                    //                    'pay_time' => date('Y-m-d H:i:s'),
                    //                    'batch_id' => $bid,
                    'after_money' => $customer['balance'] - $total_money_freight,
                ];

                Db::table('customer_balance_log')->insert($log);
                Db::table('client_order_batch')->where(['id' => $bid])->update(['is_pay' => 1]);
            } else if ($client['is_open_customer_pay'] == 1 && $customer['pay_type'] == 2) {
                $total_money_freight = $total_money + $total_freight;
                // 扣款
                if ($customer['balance'] < $total_money_freight) {
                    throw new \Exception('您的余额不足，请充值 ');
                }
                Db::table('client_customer')
                    ->where(['clientkeynum' => $user_info['basekeynum'], 'customer_id' => $customer['customer_id']])
                    ->update(['balance' => Db::raw("balance - {$total_money_freight}")]);
                // 生成日志
                $log = [
                    'customer_id' => $customer['customer_id'],
                    'clientkeynum' => $user_info['basekeynum'],
                    'type' => 2,
                    'money' => -$total_money_freight,
                    'remark' => '平台操作支付',
                    'add_time' => date('Y-m-d H:i:s'),
                    'operator' => session("cn_accountinfo.accountname"),
                    'before_money' => $customer['balance'],
                    //                    'batch_type' => 1,
                    //                    'pay_time' => date('Y-m-d H:i:s'),
                    //                    'batch_id' => $bid,
                    'after_money' => $customer['balance'] - $total_money_freight,
                ];

                Db::table('customer_balance_log')->insert($log);
                Db::table('client_order_batch')->where(['id' => $bid])->update(['is_pay' => 1]);
            }

            Db::table('client_order_batch')->where(['id' => $bid])->update([
                'goodsnum' => $total_num,
                'ordermoney' => $total_money + $total_freight,
                'order_cost_money' => $total_money,
                'order_id' => implode(',', $order_ids),
                'freight' => $total_freight,
            ]);
            //            Db::table('client_order_info')->whereIn('order_id', $order_ids)->update(['bid' => $bid]);
            ShoppingCart::where($where)->delete();
            Db::commit();
            $this->successly('请求成功', ['order_id' => $bid, 'pay_type' => $customer['pay_type']]);
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('error' . $e->getMessage());
            $this->fail($e->getMessage());
        }
    }

    public function calculate_shipping_cost($goods, $number, $template)
    {

        Log::info(\GuzzleHttp\json_encode($goods));
        Log::info(\GuzzleHttp\json_encode($template));
        // 计算总重量
        if ($goods['weight'] <= 0) {
            return 0;
        }
        $count_weight = $goods['weight'] * $number;
        if ($template['is_dynamics_first_weight'] == 1 && $count_weight > $template['first_weight']) {
            $template['first_weight'] = $template['dynamics_first_weight'];
        }
        // 开始计算价格
        $continue_weight = $count_weight - $template['first_weight'];
        $shipping_cost = $template['first_weight_price'];


        $count = $continue_weight / $template['continued_weight'];

        if ($template['is_ceil'] == 1) {
            $count = ceil($count);
        } else {
            $count = floor($count);
        }
        $shipping_cost += ($count * $template['continued_weight_price']);

        return $shipping_cost;
    }

    public function order_batch_list()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);

        $merchantkeynum = $user_info['keynum'];
        $clientkeynum = $user_info['basekeynum'];

        $where = "merchantkeynum='" . $merchantkeynum . "' and clientkeynum='$clientkeynum'  and is_error !=1 and is_del = 1 and type=5 ";
        $pagesize = Request::instance()->param('page_size', 10);
        $p = Request::instance()->param('page', 1);
        $status = Request::instance()->param('status', 0);

        // print_r($where);
        $batchlist = Db::table('client_order_batch')
            ->where($where)
            ->order("id desc")
            ->page($p, $pagesize);

        if ($status != 0) {
            if ($status == 1) {
                $batchlist = $batchlist->where(['is_pay' => 0]);
            } else if ($status == 2) {
                $batchlist = $batchlist->where(['is_pay' => 1])->where(Db::raw('(select count(*) from client_order_info where bid = id and order_status = 2) = 0'));
            } else if ($status == 3) {
                // 查询已付款  订单已部分发货
                $batchlist = $batchlist->where(['is_pay' => 1])->where(Db::raw('(select count(*) from client_order_info where bid = id and order_status = 2) between 1 and ordernum-1'));
            } else if ($status == 4) {
                // 查询已付款  订单已全部发货
                $batchlist = $batchlist->where(['is_pay' => 1])->where(Db::raw('(select count(*) from client_order_info where bid = id and order_status = 2) = ordernum'));
            }
        }

        $batchlist = $batchlist->select();
        //找一下是否有这个批次的订单，没有需要标红重新下载上传
        foreach ($batchlist as $k => $v) {
            $order = Db::table('client_order_info')
                ->where("bid", $v['id'])
                ->value('order_id');
            // print_r($v);
            $batchlist[$k]['k_name'] = Db::table('client_customer')
                ->where("keynum", $v['merchantkeynum'])
                ->value('companyname');
            if ($order == '') {
                $batchlist[$k]['iserror'] = '是';
            }
            $order = Db::table('client_order_info')->where(['order_id' => $order])->find();
            $batchlist[$k]['province'] = $order['province'];
            $batchlist[$k]['city'] = $order['city'];
            $batchlist[$k]['area'] = $order['area'];
            $batchlist[$k]['address'] = $order['address'];
            $batchlist[$k]['phone'] = $order['phone'];
            $batchlist[$k]['consignee'] = $order['consignee'];
            // 查看发货的订单数量
            $send_count = Db::table('client_order_info')->where(['bid' => $v['id'], 'order_status' => 2])->count();
            if ($send_count > 0) {
                $batchlist[$k]['status'] = '待发货';
            } elseif ($send_count < 0 && $send_count) {

                $batchlist[$k]['status'] = '部分发货';
            } else if ($send_count < 0) {

                $batchlist[$k]['status'] = '已全部发货';
            }
            //找一下这个批次的订单号是否含有未审核订单
            $is_check_order = Db::table("client_order_info")
                ->where("bid", $v['id'])
                ->where("is_check", 0)
                ->field('order_id')
                ->select();
            if ($is_check_order != []) {
                $is_check_exist = 1;
            } else {
                $is_check_exist = 0;
            }
            if ($v['is_check'] == '1') {
                $batchlist[$k]['is_check_msg'] = "已审核";
            } else {
                $batchlist[$k]['is_check_msg'] = "未审核";
            }
            $batchlist[$k]['is_check_exist'] = $is_check_exist;
            $batchlist[$k]['addtime'] = date('Y-m-d H:i:s', $v['addtime']);

            // 查询批次下订单的商品
            $goodssku = Db::table('client_order_info')
                ->whereIn('order_id', explode(',', $v['order_id']))
                ->column('goodssku');
            $goods = Db::table('client_good')
                ->where(['clientkeynum' => $clientkeynum])
                ->whereIn('goodssku', $goodssku)
                ->select();
            $batchlist[$k]['goods'] = $goods;
        }
        //print_r($batchlist);
        $count = Db::table('client_order_batch')
            ->where($where)
            ->count();

        $this->successly('请求成功', [
            'list' => $batchlist,
            'count' => $count
        ]);
    }

    public function order_list()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);
        $bid = Request::instance()->param('bid');

        $where = "1 = 1  and clientkeynum='{$user_info['basekeynum']}' and is_pid=1 and bid=" . $bid . "";
        //print_r($param);

        $pagesize = Request::instance()->param('page_size');
        $p = Request::instance()->param('page');
        $count = Db::table('client_order_info')->where($where)->count();
        $list = Db::table('client_order_info')
            ->where($where)
            ->order("order_id desc")
            ->page($p, $pagesize)
            ->select();
        if ($list != []) {
            foreach ($list as $k => $v) {
                $all_order_sn1 = "";
                $end_order_sn1 = "";
                //订单状态
                //	0已付款待发货，1已审核发货中，2已发货已发货，3已收货待完成，4订单完成，5取消，6申请售后中，7售后完成
                switch ($v['order_status']) {
                    case 0:
                        $list[$k]['order_status'] = "待发货";
                        break;
                    case 1:
                        $list[$k]['order_status'] = "发货中";
                        break;
                    case 2:
                        $list[$k]['order_status'] = "已发货";
                        break;
                    case 3:
                        $list[$k]['order_status'] = "待完成";
                        break;
                    case 4:
                        $list[$k]['order_status'] = "订单完成";
                        break;
                    case 5:
                        $list[$k]['order_status'] = "已取消";
                        break;
                    case 6:
                        $list[$k]['order_status'] = "申请售后";
                        break;
                    case 7:
                        $list[$k]['order_status'] = "售后完成";
                        break;
                    case 8:
                        $list[$k]['order_status'] = "缺货";
                        break;
                    case 9:
                        $list[$k]['order_status'] = "缺货指派给其他供应商";
                        break;
                    case 10:
                        $list[$k]['order_status'] = "含有缺货订单";
                        break;
                    case 11:
                        $list[$k]['order_status'] = "已发货申请取消订单";
                        break;
                    case 12:
                        $list[$k]['order_status'] = "拒绝取消订单申请";
                        break;
                }
                switch ($v['is_check']) {
                    case 1:
                        $list[$k]['is_check'] = "已审核";
                        break;
                    case 0:
                        $list[$k]['is_check'] = "未审核";
                        break;
                }
                $order_son = Db::table('client_order_info')
                    ->where('part_ordernum', $v['order_sn'])
                    ->field('order_sn')
                    ->select();

                foreach ($order_son as $kk => $vv) {
                    $list[$k]['order_son'] .= $vv['order_sn'] . ",";
                }
                if ($order_son != []) {
                    $list[$k]['is_son'] = 1;
                } else {
                    $list[$k]['is_son'] = 0;
                }
                $list[$k]['add_time'] = date('Y-m-d H:i:s', $v['add_time']);
                $goods = Db::table('client_good')
                    ->where(['id' => $v['goodsid']])
                    ->select();
                $list[$k]['goods'] = $goods;
            }
        }
        $batch = Db::table('client_order_batch')->where(['id' => $bid])->find();
        $batch['addtime'] = date('Y-m-d H:i:s', $batch['addtime']);
        $this->successly('请求成功', [
            'list' => $list,
            'count' => $count,
            'batch' => $batch
        ]);
    }

    public function order_detail()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);
        $id = Request::instance()->param('id');


        $where = "1 = 1  and clientkeynum='{$user_info['basekeynum']}'  and order_id=" . $id . "";


        $info = Db::table('client_order_info')
            ->where($where)
            ->find();
        $info['goods'] = Db::table('client_good')->where(['id' => $info['goodsid']])->find();

        $this->successly('请求成功', $info);
    }

    public function pay()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);
        $merchantkeynum = $user_info['keynum'];
        $clientkeynum = $user_info['basekeynum'];
        $batch_id = Request::instance()->param('batch_id');
        // 查询微信配置
        $user = Users::where(['id' => $user_info['user_id']])->find();
        $info = Db::table('plat_weixin_set')->where("basekeynum='$clientkeynum'")->find();
        $wxConf = [
            /**
             * Debug 模式，bool 值：true/false
             *
             * 当值为 false 时，所有的日志都不会记录
             */
            'debug' => true,
            /**
             * 账号基本信息，请从微信公众平台/开放平台获取
             */
            'app_id' => $info['appid'], // AppID
            'secret' => $info['appsecret'], // AppSecret
            'token' => 'TOKEN', // Token
            'aes_key' => '', // EncodingAESKey，安全模式下请一定要填写！！！
            /**
             * 日志配置
             *
             * level: 日志级别, 可选为：
             * debug/info/notice/warning/error/critical/alert/emergency
             * permission：日志文件权限(可选)，默认为null（若为null值,monolog会取0644）
             * file：日志文件位置(绝对路径!!!)，要求可写权限
             */
            'log' => [
                'level' => 'debug',
                'permission' => 0777,
                'file' => ROOT_PATH . 'runtime/wechat/easywechat.log',
            ],
            /**
             * OAuth 配置
             *
             * scopes：公众平台（snsapi_userinfo / snsapi_base），开放平台：snsapi_login
             * callback：OAuth授权完成后的回调页地址
             */
            'oauth' => [
                'scopes' => ['snsapi_userinfo'],
                'callback' => '/examples/oauth_callback.php',
            ],
            /**
             * 微信支付
             */
            'payment' => [
                'merchant_id' => $info['mchid'],
                'key' => $info['key'],
                'cert_path' => '', // XXX: 绝对路径！！！！
                'key_path' => '', // XXX: 绝对路径！！！！
                'notify_url' => 'http://plat.v2.lipingu.com/admin.php/callback/notify',
            ],
            /**
             * Guzzle 全局设置
             *
             * 更多请参考： http://docs.guzzlephp.org/en/latest/request-options.html
             */
            'guzzle' => [
                'timeout' => 3.0, // 超时时间（秒）
                //'verify' => false, // 关掉 SSL 认证（强烈不建议！！！）
            ],
        ];
        //实例化easyWeChat
        $wxApp = new Application($wxConf);

        $batch = Db::table('client_order_batch')->where(['id' => $batch_id, 'clientkeynum' => $clientkeynum])->find();
        if (empty($batch)) {
            $this->fail("未查询到该批次");
            die;
        }
        if ($batch['is_pay'] == 1) {
            $this->fail("当前批次已支付");
            die;
        }


        $pay_log_data = [
            'batch_id' => $batch_id,
            'is_pay' => 0,
            'add_time' => date('Y-m-d H:i:s'),
            'merchantkeynum' => $batch['merchantkeynum'],
            'clientkeynum' => $clientkeynum,
            'pay_price' => $batch['ordermoney'],
            'pay_type' => 1
        ];
        $pay_id = Db::table('customer_pay_log')->insertGetId($pay_log_data);

        $order_attr = [
            'trade_type' => 'JSAPI', // JSAPI，NATIVE，APP...
            'body' => '订单批次支付',
            'out_trade_no' => $pay_id,
            'total_fee' => $batch['ordermoney'] * 100, // 单位：分// 支付结果通知网址，如果不设置则会使用配置里的默认地址
            'openid' => $user['openid'],
        ];

        $payment = $wxApp->payment;

        $order = new Order($order_attr);
        Log::info(json_encode($order));
        $result = $payment->prepare($order);
        Log::info(json_encode($result));
        //
        //        $log = [
        //            'customer_id' => $customer['customer_id'],
        //            'clientkeynum' => $batch['clientkeynum'],
        //            'type' => 3,
        //            'money' => $batch['ordermoney'],
        //            'remark' => '客户操作在线支付',
        //            'add_time' => date('Y-m-d H:i:s'),
        //            'operator' => session("cn_accountinfo.accountname"),
        //            'before_money' => $customer['balance'],
        //            'pay_type' => 1,
        //            'pay_time' => date('Y-m-d H:i:s'),
        //            'batch_id' => $batch['id'],
        //        ];

        if ($result->return_code == 'SUCCESS' && $result->result_code == 'SUCCESS') {
            $return = $payment->configForJSSDKPayment($result['prepay_id']);
            $return['timeStamp'] = $return['timestamp'];
            unset($return['timestamp']);
            $this->successly('请求成功', $return);
        } else {
            $this->fail('请求失败');
        }


        //        $result = $app->order->unify($order);
        //        Log::info(json_encode($result));
        //        $code_url = $result['code_url'];
        //        $qrcode = \Endroid\QrCode\QrCode::create($code_url)->setSize(200);
        //        $this->success(0, '成功', ['image' => $qrcode]);
    }

    public function get_batch_pay_status()
    {
        $batch_id = Request::instance()->param('batch_id');
        $merchantkeynum = session("cn_accountinfo.basekeynum");
        $clientkeynum = session("cn_accountinfo.parent_basekeynum");
        $batch = Db::table('client_order_batch')->where(['id' => $batch_id, 'clientkeynum' => $clientkeynum])->find();
        if (empty($batch)) {
            $this->fail(-1, '未查询到该批次');
        }
        $this->success(0, '成功', ['status' => $batch['is_pay'], 'money' => $batch['ordermoney']]);
    }

    public function search_address()
    {
        $token = Request::instance()->param('token');
        $this->check_token($token);
        $user_info = Cache::get($token);
        $merchantkeynum = $user_info['keynum'];
        $clientkeynum = $user_info['basekeynum'];
        $page = Request::instance()->param('page', 1);
        $page_size = Request::instance()->param('page_size', 10);

        $keyword = Request::instance()->param('keyword');

        //        if (empty($keyword)) {
        //            $this->successly('请求成功', ['list' => [], 'count' => 0]);
        //        }
        // 通过keyword搜索 文件

        $where = "clientkeynum='$clientkeynum' and merchantkeynum='$merchantkeynum'";

        if (!empty($keyword)) {
            $where .= " and (phone like '%$keyword&' or address like '%$keyword*' or consignee like '%$keyword%')";
        }

        $list = Db::table('client_order_info')->distinct(true)->field('address,consignee,phone,province,city,area,detailed_address')->where($where)->page($page, $page_size)->select();
        $count = Db::table('client_order_info')->where($where)->page($page, $page_size)->count();

        $this->successly('请求成功', ['list' => $list, 'count' => $count]);
    }


    protected function successly($msg = '请求成功', $data = [], $code = 200)
    {
        $return = [
            'msg' => $msg,
            'data' => $data,
            'code' => $code
        ];
        echo json_encode($return);
        die;
    }

    protected function fail($msg = '请求失败', $code = 500)
    {
        $return = [
            'msg' => $msg,
            'code' => $code
        ];
        echo json_encode($return);
        die;
    }
}
