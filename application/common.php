<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright ( c ) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 流年 <<EMAIL>>
// +----------------------------------------------------------------------

// 应用公共文件
use OSS\Core\OssException;
use OSS\OssClient;
use PHPMailer\PHPMailer\PHPMailer;
use think\facade\Config;
use think\Db;
use think\facade\Session;
use think\facade\Url;
use lizengbang\Sms;

define('MAGIC_QUOTES_GPC',ini_set("magic_quotes_runtime",0)?True:False);

function get_wechat_bind($account_id)
{
    $wechat_bind_info = Db::table('plat_third_auth')->where('account_id', $account_id)->where('auth_type', 'wechat')->where('status', 1)->find();
    return empty($wechat_bind_info) ? 0 : 1;
}

//第三方检测系统是否正常字符串
function  api_check_str()
{
    $plat_system_set = Db::table('plat_system_set')->where("id='1' ")->find();
    return $plat_system_set["api_check_str"];
}

//短信平台
function get_duanxin_lizengbang_info($keynum)
{
    $retinfo        = '';
    $xmlstring      = '';
    $info           = '';
    $keynum         = $keynum;
    $duanxinsetinfo = Db::table('plat_lizengbang_sms_set')->where("basekeynum='$keynum'")->find();
    $plat_system_set_info = Db::table('plat_system_set')->where("id ='1' ")->find();

    if (empty($duanxinsetinfo)) {
        $return_arr['sta'] = '1';
        $return_arr['msg'] = "没有找到记录！";
        return $return_arr;
        die;
    }
    $appid        = $duanxinsetinfo['appid'];
    $appkey       = $duanxinsetinfo['appkey'];
    //$api_url           = $duanxinsetinfo['api_url'];
    $api_url      = $plat_system_set_info['lizengbang_api_url'];
    $code_status    = $duanxinsetinfo['code_status'];
    $content        = $duanxinsetinfo['code_content'];

    if ($appid == '' || $appkey == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "短信应用配置不能为空！";
        echo json_encode($return_arr);
        die();
    }


    //发送短信开始
    $appid = $appid;
    $appkey = $appkey; //此秘钥只是示例，请使用正确秘钥
    $api_url = $api_url;
    $obj = new  Sms($api_url, $appid, $appkey);
    $rt_arr = $obj->get_account_info();
    if ($rt_arr['sta'] != "1") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = $rt_arr['msg'];
        return $return_arr;
        die;
    }

    $return_arr['sta']       = '1';
    $return_arr['riches']    = $rt_arr['riches'];
    return $return_arr;
    die;
}

/*
 * 发送短信验证码礼赠帮 v2
 */
function send_phone_code_lizengbang_v2($basekeynum, $phone, $reqCount = '1')
{
    $duanxinsetinfo = Db::table('plat_lizengbang_sms_set')->where("basekeynum='$basekeynum'")->find();
    $plat_system_set_info = Db::table('plat_system_set')->where("id ='1' ")->find();
    $destnumbers    = $phone;
    $appid        = $duanxinsetinfo['appid'];
    $appkey       = $duanxinsetinfo['appkey'];
    //$api_url           = $duanxinsetinfo['api_url'];
    $api_url      = $plat_system_set_info['lizengbang_api_url'];
    $code_status    = $duanxinsetinfo['code_status'];
    $content        = $duanxinsetinfo['code_content'];
    //总开关是否开启
    $sms_status = $duanxinsetinfo['sms_status'];
    if ($sms_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "短信验证码服务总开关没有开启";
        echo json_encode($return_arr);
        die;
    }
    if ($code_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "短信验证码服务没有开启";
        echo json_encode($return_arr);
        die;
    }
    if ($appid == '' || $appkey == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "短信应用配置不能为空！";
        echo json_encode($return_arr);
        die();
    }

    if ($destnumbers == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "发送手机号不能为空!";
        echo json_encode($return_arr);
        die();
    }

    $code                      = rand(100000, 999999);
    $content                   = str_replace("{code}", $code, $content);
    $msg                       = $content;

    $sign = $duanxinsetinfo['sign'];
    $qianming = "【" . $sign . "】";
    //这种情况要剔除内容里面的签名【】
    $msg =  $qianming . preg_replace('/\【.*?\】/', '', $content);


    $datalog['words']          = $msg;
    $datalog['time']           = time();
    $datalog['phonenum']       = $destnumbers;
    $datalog["clientkeynum"]   = $basekeynum;

    $add_code["phone"]          = $destnumbers;
    $add_code["code"]           = $code;
    $add_code["time"]           = time(); //存入生成时间，有效期由验证的时候自己把握
    $add_code["clientkeynum"]   = $basekeynum;

    //发送短信开始
    $appid = $appid;
    $appkey = $appkey; //此秘钥只是示例，请使用正确秘钥
    $api_url = $api_url;
    $destnumbers = $phone;
    $msg = $msg;
    $obj = new  Sms($api_url, $appid, $appkey);


    //如果是32位字符串则使用它否则改成空
    if (strlen(trim($sign)) == 32) {
        $sign = trim($sign);
    } else {
        $sign = "";
    }

    $rt_arr = $obj->sendsms_v2($destnumbers, $code, $reqCount, $sign);
    if ($rt_arr['sta'] != "1") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = $rt_arr['msg'];
        echo json_encode($return_arr);
        die;
    }
    //发送短信结束
    $datalog['api_return_content'] = json_encode($rt_arr, JSON_UNESCAPED_UNICODE);


    Db::table('plat_sms_log')->insert($datalog);
    Db::table("plat_sms_code")->insert($add_code);
    $return_arr['sta'] = 1;
    $return_arr['msg'] = "发送成功!";
    echo json_encode($return_arr);
    die();
}



//获取总平台基本配置,传入字段返回字段的值
function get_plat_system_set($field)
{
    $info = Db::table('plat_system_set')->where("id=1")->find();
    return $info[$field];
}

/**
 * 请求成功返回信息方法
 * @param $code int 状态码
 * @param $message string 信息
 * @param $data array 返回数据
 * @return void
 */
function success($code = 0, $message = '请求成功', $data = [], $count = 0, $append = []){
    echo json_encode([
        'code' => $code,
        'msg' => $message,
        'data' => $data,
        'count' => $count,
        'append' => $append
    ]);die;
}


function fail($code = -1, $message = '请求失败'){
    echo json_encode([
        'code' => $code,
        'msg' => $message,
    ]);die;
}

// 获取两个地址相距的位置
function getDistance($lat1, $lng1, $lat2, $lng2, $len_type = 2, $decimal = 2)
{
    $pi = 3.1415926535898;
    $earth_radius = 6378.137;
//        define('PI',3.1415926535898);
//        define('EARTH_RADIUS',6378.137);
    $radLat1 = $lat1 * $pi / 180.0;
    $radLat2 = $lat2 * $pi / 180.0;
    $a = $radLat1 - $radLat2;
    $b = ($lng1 * $pi / 180.0) - ($lng2 * $pi / 180.0);
    $s = 2 * asin(sqrt(pow(sin($a/2),2) + cos($radLat1) * cos($radLat2) * pow(sin($b/2),2)));
    $s = $s * $earth_radius;
    $s = round($s * 1000);
    if ($len_type > 1)
    {
        $s /= 1000;
    }
    return round($s, $decimal);
}

//对外接口需要的函数
/**
 *    作用：格式化参数，签名过程需要使用
 */

function formatBizQueryParaMap_wl($paraMap, $urlencode = 0)
{
    $buff = '';
    ksort($paraMap);
    foreach ($paraMap as $k => $v) {
        if ($urlencode) {
            $v = urlencode($v);
        }
        $buff .= $k . '=' . $v . '&';
    }
    $reqPar = '';
    if (strlen($buff) > 0) {
        $reqPar = substr($buff, 0, strlen($buff) - 1);
    }
    \think\facade\Log::info($reqPar);
    return $reqPar;
}
/**
 *    作用：生成签名接口使用
 */

function getSign_wl($Obj, $key)
{
    foreach ($Obj as $k => $v) {
        if (trim($v) != '') {
            //必须存在 去掉空
            $Parameters[$k] = $v;
        }
    }
    //签名步骤一：按字典序排序参数
    ksort($Parameters);

    $String = formatBizQueryParaMap_wl($Parameters, false);
    //签名步骤二：在string后加入KEY
    $String = $String . '&appkey=' . $key;
    //签名步骤三：MD5加密
    $String = md5($String);
    //签名步骤四：所有字符转为大写
    $result_ = strtoupper($String);
    return $result_;
}

function checkSign_wl($tmpData, $key)
{
    $post_sign = $tmpData['sign'];
    unset($tmpData['sign']);
    unset($tmpData['action']);
    $sign = getSign_wl($tmpData, $key);
    //根据传过来的参数生成签名
    //echo $sign;
    if ($post_sign == $sign) {
        return TRUE;
    }
    return FALSE;
}
/**
 *    作用：产生随机字符串，不长于32位
 */

function createNoncestr_wl($length = 32)
{
    $chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    $str = '';
    for ($i = 0; $i < $length; $i++) {
        $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
    }
    return $str;
}

//////////////////////////////支付宝支付开始///////////////////////////////////////////////////////
/**
 * 建立请求，以表单HTML形式构造（默认）
 * @param $para_temp 请求参数数组
 * @param $method 提交方式。两个值可选：post、get
 * @param $button_name 确认按钮显示文字
 * @return 提交表单HTML文本
 */

function buildRequestForm($para_temp, $method, $button_name, $alipay_gateway, $key)
{
    //待请求参数数组
    $para = buildRequestPara($para_temp, $key);
    $sHtml = "<form id='alipaysubmit' name='alipaysubmit' action='" . $alipay_gateway . "_input_charset=utf-8' method='" . $method . "'>";
    while (list($key, $val) = each($para)) {
        $sHtml .= "<input type='hidden' name='" . $key . "' value='" . $val . "'/>";
    }
    //submit按钮控件请不要含有name属性
    $sHtml = $sHtml . "<input  style='display:none'   type='submit' value='" . $button_name . "'></form>";
    $sHtml = $sHtml . "<script>document.forms['alipaysubmit'].submit();</script>";
    return $sHtml;
}
//清空文件夹函数和清空文件夹后删除空文件夹函数的处理

function deldir($path)
{
    //如果是目录则继续
    if (is_dir($path)) {
        //扫描一个文件夹内的所有文件夹和文件并返回数组
        $p = scandir($path);
        //print_r( $p );
        die;
        foreach ($p as $val) {
            //排除目录中的.和..
            if ($val != '.' && $val != '..') {
                //如果是目录则递归子目录，继续操作
                if (is_dir($path . $val)) {
                    //子目录中操作删除文件夹和文件
                    deldir($path . $val . '/');
                    //目录清空后删除空文件夹
                    @rmdir($path . $val . '/');
                } else {
                    //如果是文件直接删除
                    unlink($path . $val);
                }
            }
        }
    }
}

function deldir_2($dir)
{
    //先删除目录下的文件：
    $dh = opendir($dir);
    while ($file = readdir($dh)) {
        if ($file != '.' && $file != '..') {
            $fullpath = $dir . '/' . $file;
            if (!is_dir($fullpath)) {
                unlink($fullpath);
            } else {
                deldir($fullpath);
            }
        }
    }

    closedir($dh);
    //删除当前文件夹：
    if (rmdir($dir)) {
        return true;
    } else {
        return false;
    }
}
//PHP 计算两个时间戳之间相差的时间
//功能：计算两个时间戳之间相差的日时分秒
//$begin_time  开始时间戳
//$end_time 结束时间戳

function timediff($begin_time, $end_time)
{
    if ($begin_time < $end_time) {
        $starttime = $begin_time;
        $endtime = $end_time;
    } else {
        $starttime = $end_time;
        $endtime = $begin_time;
    }
    //计算天数
    $timediff = $endtime - $starttime;
    $days = intval($timediff / 86400);
    //计算小时数
    $remain = $timediff % 86400;
    $hours = intval($remain / 3600);
    //计算分钟数
    $remain = $remain % 3600;
    $mins = intval($remain / 60);
    //计算秒数
    $secs = $remain % 60;
    $res = array('day' => $days, 'hour' => $hours, 'min' => $mins, 'sec' => $secs);
    return $res;
}
//随机生成字母加数字

function getRandomString($len, $chars = null)
{
    if (is_null($chars)) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    }
    mt_srand(10000000 * (float)microtime());
    for (
        $i = 0, $str = '', $lc = strlen($chars) - 1;
        $i < $len;
        $i++
    ) {
        $str .= $chars[mt_rand(0, $lc)];
    }
    return $str;
}

/**
 * 表格文件转数组
 */

function excelToArray($dir)
{

    ini_set('memory_limit', '-1');
    ini_set('max_execution_time', '0');
    //加载excel文件
    $filename = $dir;
    $objPHPExcelReader = PHPExcel_IOFactory::load($filename);
    $sheet = $objPHPExcelReader->getSheet(0);
    // 读取第一个工作表( 编号从 0 开始 )
    $highestRow = $sheet->getHighestRow();
    // 取得总行数
    $highestColumn = $sheet->getHighestColumn();
    // 取得总列数
    $arr = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');
    // 一次读取一列
    $res_arr = array();
    $msg_arr = array('status' => 0);
    for ($row = 2; $row <= $highestRow; $row++) {
        $row_arr = array();
        $msg = array();
        for ($column = 0; $arr[$column] != 'Z'; $column++) {
            $num = 1;
            $val = $sheet->getCellByColumnAndRow($column, $row)->getValue();
            $val = trim($val);
            $number = explode('+', $val);
            $row_arr[] = $val;
        }
        if (!empty($msg)) {
            $msg_arr[msg][] = $msg;
        }

        if (count($row_arr)) {
            $res_arr[] = $row_arr;
        }
    }
    if ($msg_arr['status'] == 1) {
        return $msg_arr;
    } else {
        return $res_arr;
    }
}
//根据某个字段进行分组

function dataGroup($dataArr, $keyStr)
{
    $newArr = [];
    foreach ($dataArr as $k => $val) {
        $newArr[$val[$keyStr]][] = $val;
    }
    return $newArr;
}
//excel导出

function create_xls($data, $filename = 'dingdan.xls')
{
    ini_set('max_execution_time', '0');
    $filename = str_replace('.xls', '', $filename) . '.xls';
    $phpexcel = new PHPExcel();
    $phpexcel->getProperties()
        ->setCreator('Maarten Balliauw')
        ->setLastModifiedBy('Maarten Balliauw')
        ->setTitle('Office 2007 XLSX Test Document')
        ->setSubject('Office 2007 XLSX Test Document')
        ->setDescription('Test document for Office 2007 XLSX, generated using PHP classes.')
        ->setKeywords('office 2007 openxml php')
        ->setCategory('Test result file');
    $phpexcel->getActiveSheet()->fromArray($data);
    $phpexcel->getActiveSheet()->setTitle('Sheet1');
    $phpexcel->setActiveSheetIndex(0);
    header('Content-Type: application/vnd.ms-excel');
    header("Content-Disposition: attachment;filename=$filename");
    header('Cache-Control: max-age=0');
    header('Cache-Control: max-age=1');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    // Date in the past
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    // always modified
    header('Cache-Control: cache, must-revalidate');
    // HTTP/1.1
    header('Pragma: public');
    // HTTP/1.0
    $objwriter = PHPExcel_IOFactory::createWriter($phpexcel, 'Excel5');
    $objwriter->save('php://output');
    exit;
}

//excel导出到指定目录中

function create_xls_down($data, $filename = 'dingdan.xls', $name, $destination_folder)
{
    ini_set('max_execution_time', '0');
    // require_once dirname( __FILE__ ) . '../extend/Classes/PHPExcel.php';
    $filename = str_replace('.xls', '', $filename) . '.xls';
    $phpexcel = new PHPExcel();
    $phpexcel->getProperties()
        ->setCreator('Maarten Balliauw')
        ->setLastModifiedBy('Maarten Balliauw')
        ->setTitle('Office 2007 XLSX Test Document')
        ->setSubject('Office 2007 XLSX Test Document')
        ->setDescription('Test document for Office 2007 XLSX, generated using PHP classes.')
        ->setKeywords('office 2007 openxml php')
        ->setCategory('Test result file');
    $phpexcel->getActiveSheet()->fromArray($data);
    $phpexcel->getActiveSheet()->setTitle('Sheet1');
    $phpexcel->setActiveSheetIndex(0);
    //    header( 'Content-Type: application/vnd.ms-excel' );
    //    header( "Content-Disposition: attachment;filename=$filename" );
    //    header( 'Cache-Control: max-age=0' );
    //    header( 'Cache-Control: max-age=1' );
    //    header( 'Expires: Mon, 26 Jul 1997 05:00:00 GMT' );
    // Date in the past
    //    header( 'Last-Modified: ' . gmdate( 'D, d M Y H:i:s' ) . ' GMT' );
    // always modified
    //    header( 'Cache-Control: cache, must-revalidate' );
    // HTTP/1.1
    //    header( 'Pragma: public' );
    // HTTP/1.0
    $objwriter = PHPExcel_IOFactory::createWriter($phpexcel, 'Excel5');
    //    $objwriter->save( 'php://output' );

    $ftype = 'xls';
    //不重复名字的文件拼接上原文件后缀
    $imgname = $name . '.' . $ftype;
    $destination = $destination_folder . $imgname;
    //目录不存在创建目录
    if (!is_dir($destination_folder)) {
        mkdir($destination_folder, 0777, true);
    }

    // echo $destination;
    $objwriter->save("$destination");
    //return $destination;
    //  exit;
}
/**
 * 生成要请求给支付宝的参数数组
 * @param $para_temp 请求前的参数数组
 * @return 要请求的参数数组
 */

function buildRequestPara($para_temp, $key)
{
    //除去待签名参数数组中的空值和签名参数
    $para_filter = paraFilter($para_temp);

    //对待签名参数数组排序
    $para_sort = argSort($para_filter);

    //生成签名结果
    $mysign = buildRequestMysign($para_sort, $key);

    //签名结果与签名方式加入请求提交参数组中
    $para_sort['sign'] = $mysign;
    $para_sort['sign_type'] = strtoupper(trim('MD5'));
    return $para_sort;
}

/**
 * 生成签名结果
 * @param $para_sort 已排序要签名的数组
 * return 签名结果字符串
 */

function buildRequestMysign($para_sort, $key)
{
    //把数组所有元素，按照“参数 = 参数值”的模式用“&”字符拼接成字符串
    $prestr = createLinkstring($para_sort);
    $mysign = '';
    $mysign = md5Sign($prestr, $key);
    return $mysign;
}

/**
 * 把数组所有元素，按照“参数 = 参数值”的模式用“&”字符拼接成字符串
 * @param $para 需要拼接的数组
 * return 拼接完成以后的字符串
 */

function createLinkstring($para)
{
    $arg  = '';
    while (list($key, $val) = each($para)) {
        $arg .= $key . '=' . $val . '&';
    }
    //去掉最后一个&字符
    $arg = substr($arg, 0, count($arg) - 2);

    $arg = addslashes($arg);
//    //如果存在转义字符，那么去掉转义
//    if (get_magic_quotes_gpc()) {
//        $arg = stripslashes($arg);
//    }

    return $arg;
}
/**
 * 把数组所有元素，按照“参数 = 参数值”的模式用“&”字符拼接成字符串，并对字符串做urlencode编码
 * @param $para 需要拼接的数组
 * return 拼接完成以后的字符串
 */

function createLinkstringUrlencode($para)
{
    $arg  = '';
    while (list($key, $val) = each($para)) {
        $arg .= $key . '=' . urlencode($val) . '&';
    }
    //去掉最后一个&字符
    $arg = substr($arg, 0, count($arg) - 2);

    $arg = addslashes($arg);

    //如果存在转义字符，那么去掉转义
//    if (get_magic_quotes_gpc()) {
//        $arg = stripslashes($arg);
//    }

    return $arg;
}
/**
 * 除去数组中的空值和签名参数
 * @param $para 签名参数组
 * return 去掉空值与签名参数后的新签名参数组
 */

function paraFilter($para)
{
    $para_filter = array();
    while (list($key, $val) = each($para)) {
        if ($key == 'sign' || $key == 'sign_type' || $val == '') continue;
        else    $para_filter[$key] = $para[$key];
    }
    return $para_filter;
}
/**
 * 对数组排序
 * @param $para 排序前的数组
 * return 排序后的数组
 */

function argSort($para)
{
    ksort($para);
    reset($para);
    return $para;
}

/**
 * 签名字符串
 * @param $prestr 需要签名的字符串
 * @param $key 私钥
 * return 签名结果
 */

function md5Sign($prestr, $key)
{
    $prestr = $prestr . $key;
    return md5($prestr);
}

/**
 * 验证签名
 * @param $prestr 需要签名的字符串
 * @param $sign 签名结果
 * @param $key 私钥
 * return 签名结果
 */

function md5Verify($prestr, $sign, $key)
{
    $prestr = $prestr . $key;
    $mysgin = md5($prestr);

    if ($mysgin == $sign) {
        return true;
    } else {
        return false;
    }
}

/**
 * 写日志，方便测试（看网站需求，也可以改成把记录存入数据库）
 * 注意：服务器需要开通fopen配置
 * @param $word 要写入日志里的文本内容 默认值：空值
 */

function logRes($word = '', $dir = 'test')
{
    $dirnew = '../log/' . $dir . '/';
    //log文件夹放到非web目录防止被他们通过http方式访问，重要！！！！！！！！！！！！
    //目录不存在创建目录
    if (!is_dir($dirnew)) {
        mkdir($dirnew, 0777, true);
    }

    $logname = $dirnew . date('Ymd') . '.txt';
    $fp = fopen($logname, 'a');
    flock($fp, LOCK_EX);
    fwrite($fp, '执行日期：' . strftime('%Y-%m-%d %H:%M:%S', time()) . "\r\n" . $word . "\r\n");
    flock($fp, LOCK_UN);
    fclose($fp);
}


//PHP 生成唯一订单号函数, 待验证是否唯一
//在开发购物功能，涉及到支付比如 微信、支付宝，当下单时需要生成一条唯一的订单，需要用到唯一的订单号管理。

function create_order_sn()
{
    @date_default_timezone_set('PRC');
    $order_id_main = date('YmdHis') . rand(10000000, 99999999);
    //订单号码主体长度
    $order_id_len = strlen($order_id_main);
    $order_id_sum = 0;
    for ($i = 0; $i < $order_id_len; $i++) {
        $order_id_sum += (int)(substr($order_id_main, $i, 1));
    }
    //唯一订单号码（YYYYMMDDHHIISSNNNNNNNNCC）
    $order_sn = $order_id_main . str_pad((100 - $order_id_sum % 100) % 100, 2, '0', STR_PAD_LEFT);
    //生成唯一订单号
    return $order_sn;
}

//异步回调
/**
 * 针对notify_url验证消息是否是支付宝发出的合法消息
 * @return 验证结果
 */

function verifyNotify($post, $key, $transport, $partner)
{
    if (empty($post)) {
        //判断POST来的数组是否为空
        return false;
    } else {
        //生成签名结果
        $isSign = getSignVeryfy($post, $post['sign'], $key);
        //获取支付宝远程服务器ATN结果（验证是否是支付宝发来的消息）
        $responseTxt = 'true';
        if (!empty($_POST['notify_id'])) {
            $responseTxt = getResponse($post['notify_id'], $transport, $partner);
        }

        //验证
        //$responsetTxt的结果不是true，与服务器设置问题、合作身份者ID、notify_id一分钟失效有关
        //isSign的结果不是true，与安全校验码、请求时的参数格式（如：带自定义参数等）、编码格式有关
        if (preg_match("/true$/i", $responseTxt) && $isSign) {
            return true;
        } else {
            return false;
        }
    }
}

/**
 * 获取返回时的签名验证结果
 * @param $para_temp 通知返回来的参数数组
 * @param $sign 返回的签名结果
 * @return 签名验证结果
 */

function getSignVeryfy($para_temp, $sign, $key)
{
    //除去待签名参数数组中的空值和签名参数
    $para_filter = paraFilter($para_temp);
    //对待签名参数数组排序
    $para_sort = argSort($para_filter);
    //把数组所有元素，按照“参数 = 参数值”的模式用“&”字符拼接成字符串
    $prestr = createLinkstring($para_sort);
    $isSgin = false;
    $isSgin = md5Verify($prestr, $sign, $key);
    return $isSgin;
}

/**
 * 获取远程服务器ATN结果, 验证返回URL
 * @param $notify_id 通知校验ID
 * @return 服务器ATN结果
 * 验证结果集：
 * invalid命令参数不对 出现这个错误，请检测返回处理中partner和key是否为空
 * true 返回正确信息
 * false 请检查防火墙或者是服务器阻止端口问题以及验证时间是否超过一分钟
 */

function getResponse($notify_id, $transport, $partner)
{
    $transport = strtolower(trim($transport));
    $partner = trim($partner);
    $veryfy_url = '';
    if ($transport == 'https') {
        $veryfy_url = 'https://mapi.alipay.com/gateway.do?service=notify_verify&';
    } else {
        $veryfy_url = 'http://notify.alipay.com/trade/notify_query.do?';
    }
    $veryfy_url = $veryfy_url . 'partner=' . $partner . '&notify_id=' . $notify_id;
    $responseTxt = getHttpResponseGET($veryfy_url, getcwd() . '\\cacert.pem');

    return $responseTxt;
}

/**
 * 远程获取数据，GET模式
 * 注意：
 * 1.使用Crul需要修改服务器中php.ini文件的设置，找到php_curl.dll去掉前面的';'就行了
 * 2.文件夹中cacert.pem是SSL证书请保证其路径有效，目前默认路径是：getcwd().'\\cacert.pem'
 * @param $url 指定URL完整路径地址
 * @param $cacert_url 指定当前工作目录绝对路径
 * return 远程输出的数据
 */

function getHttpResponseGET($url, $cacert_url)
{

    logRes($url);
    //http://notify.alipay.com/trade/notify_query.do?partner = 2088711315113634&notify_id = 02851d0dec68c73b8587d109377c39cmem

    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_HEADER, 0);
    // 过滤HTTP头
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    // 显示输出结果
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, true);
    //SSL证书认证
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 2);
    //严格认证
    curl_setopt($curl, CURLOPT_CAINFO, $cacert_url);
    //证书地址
    $responseText = curl_exec($curl);
    //var_dump( curl_error( $curl ) );
    //如果执行curl过程中出现异常，可打开此开关，以便查看异常内容
    curl_close($curl);

    return $responseText;
}

//////////////////////////////支付宝支付结束///////////////////////////////////////////////////////

//////////////////////////////微信公众号支付开始///////////////////////////////////////////////////////

/**
 *  作用：post请求xml
 */

function postXml($xml, $url)
{
    $response = do_post_request($xml, $url);
    return $response;
}

function do_post_request($data, $url)
{
    $params = array('http' => array(
        'method' => 'POST',
        'content' => $data
    ));

    $ctx = stream_context_create($params);
    $fp = @fopen($url, 'rb', false, $ctx);
    if (!$fp) {
        throw new Exception("Problem with $url, $php_errormsg");
    }
    $response = @stream_get_contents($fp);
    if ($response === false) {
        throw new Exception("Problem reading data from $url, $php_errormsg");
    }
    return $response;
}

/**
 *  作用：array转xml
 */

function arrayToXml($arr)
{
    $xml = '<xml>';
    foreach ($arr as $key => $val) {
        if (is_numeric($val)) {
            $xml .= '<' . $key . '>' . $val . '</' . $key . '>';
        } else
            $xml .= '<' . $key . '><![CDATA[' . $val . ']]></' . $key . '>';
    }
    $xml .= '</xml>';
    return $xml;
}

/**
 *  作用：产生随机字符串，不长于32位
 */

function createNoncestr($length = 32)
{
    $chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    $str = '';
    for ($i = 0; $i < $length; $i++) {
        $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
    }
    return $str;
}

/**
 *  作用：格式化参数，签名过程需要使用
 */

function formatBizQueryParaMap($paraMap, $urlencode)
{
    $buff = '';
    ksort($paraMap);
    foreach ($paraMap as $k => $v) {
        if ($urlencode) {
            $v = urlencode($v);
        }
        //$buff .= strtolower( $k ) . '=' . $v . '&';
        $buff .= $k . '=' . $v . '&';
    }
    $reqPar;
    if (strlen($buff) > 0) {
        $reqPar = substr($buff, 0, strlen($buff) - 1);
    }
    return $reqPar;
}

/**
 *  作用：生成签名
 */

function getSign($Obj, $key)
{
    foreach ($Obj as $k => $v) {
        $Parameters[$k] = $v;
    }
    //签名步骤一：按字典序排序参数
    ksort($Parameters);
    $String = formatBizQueryParaMap($Parameters, false);
    //echo '【string1】'.$String.'</br>';
    //签名步骤二：在string后加入KEY
    $String = $String . '&key=' . $key;
    //echo '【string2】'.$String.'</br>';
    //签名步骤三：MD5加密
    $String = md5($String);
    //echo '【string3】 '.$String.'</br>';
    //签名步骤四：所有字符转为大写
    $result_ = strtoupper($String);
    //echo '【result】 '.$result_.'</br>';
    return $result_;
}

//用户真实ip

function  get_client_ip()
{
    $cip = 'unknown';
    if ($_SERVER['REMOTE_ADDR']) {
        $cip = $_SERVER['REMOTE_ADDR'];
    } else if (getenv('REMOTE_ADDR')) {
        $cip = getenv('REMOTE_ADDR');
    }
    return $cip;
}

function checkSign($tmpData, $key)
{
    $post_sign = $tmpData['sign'];
    unset($tmpData['sign']);
    $sign = getSign($tmpData, $key);
    //根据微信传过来的参数生成签名
    if ($post_sign == $sign) {
        return TRUE;
    }
    return FALSE;
}

function get_openid($appID, $appsecret)
{
    //从微信域名跳转回来会get过来一个code参数如果没有code参数则需要跳转到微信域名去授权
    $code = @$_GET['code'];
    if (!$code) {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? 'https://' : 'http://';
        $redirect_uri = "$protocol$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
        $redirect_uri = urlencode($redirect_uri);
        $scope = 'snsapi_base';
        //scope中snsapi_base和snsapi_userinfo
        $headerurl = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' . $appID . '&redirect_uri=' . $redirect_uri . '&response_type=code&scope=' . $scope . '&state=STATE#wechat_redirect';
        header("location:$headerurl");
        die;
    }
    //通过code换取openid
    $url = 'https://api.weixin.qq.com/sns/oauth2/access_token?appid=' . $appID . '&secret=' . $appsecret . '&code=' . $code . '&grant_type=authorization_code';
    $json = file_get_contents($url);
    //如果php.ini没有开启openssl会报错, 解决方案www.wlphp.com/?post = 16, 获取采用curl代替file_get_contents
    $arr = json_decode($json, 1);
    return $arr;
}

/**
 *  作用：设置jsapi的参数
 */

function getParameters($appID, $prepay_id, $key)
{

    $timeStamp = time();
    $jsApiObj['appId'] = $appID;
    $jsApiObj['timeStamp'] = "$timeStamp";
    //参数值必须有引号字符串类型否则苹果手机报错，提示timeStamp参数缺失
    $jsApiObj['nonceStr'] = createNoncestr();
    $jsApiObj['package'] = "prepay_id=$prepay_id";
    $jsApiObj['signType'] = 'MD5';
    $jsApiObj['paySign'] = getSign($jsApiObj, $key);
    $parameters = json_encode($jsApiObj);
    return $parameters;
}

//////////////////////////////微信公众号支付结束///////////////////////////////////////////////////////


//快递100实时查询物理轨迹
function  get_kuaidi100_logistics_track($basekeynum = '', $com = '', $num = '', $phone = '')
{
    //账号zhensoft1 密码：wl905507
    // 实时查询示例代码
    // 授权信息可通过链接查看：https://api.kuaidi100.com/manager/page/myinfo/enterprise
    //$com = 'shunfeng';
    //$num = 'SF1324866433451';
    //$phone = '';
    //参数设置
    $plat_kuaidi100_set_info = Db::table('plat_kuaidi100_set')->where("basekeynum='$basekeynum'")->find();
    $check_status         = $plat_kuaidi100_set_info['check_status'];
    $subscribe_status       = $plat_kuaidi100_set_info['subscribe_status'];
    $callbackurl       = $plat_kuaidi100_set_info['callbackurl'];
    $customer       = $plat_kuaidi100_set_info['customer'];
    $key       = $plat_kuaidi100_set_info['key'];

    //开关是否开启
    $check_status = $plat_kuaidi100_set_info['check_status'];
    if ($sms_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "实时物流查询未开启！";
        echo json_encode($return_arr);
        die;
    }

    if ($customer == '' || $key == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "customer或者key为空！";
        echo json_encode($return_arr);
        die();
    }
    if ($com == '' || $num == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "快递单号或者编码为空！";
        echo json_encode($return_arr);
        die();
    }


    $key = $key;
    //客户授权key
    $customer = $customer;
    //查询公司编号
    $param = array(
        'com' => $com,             //快递公司编码
        'num' => $num,     //快递单号
        'phone' => $phone,                //手机号
        'from' => '',                 //出发地城市
        'to' => '',                   //目的地城市
        'resultv2' => '1'             //开启行政区域解析
    );
    //请求参数
    $post_data = array();
    $post_data['customer'] = $customer;
    $post_data['param'] = json_encode($param);
    $sign = md5($post_data['param'] . $key . $post_data['customer']);
    $post_data['sign'] = strtoupper($sign);
    $url = 'http://poll.kuaidi100.com/poll/query.do';
    //实时查询请求地址
    $params = '';
    foreach ($post_data as $k => $v) {
        $params .= "$k=" . urlencode($v) . '&';
        //默认UTF-8编码格式
    }
    $post_data = substr($params, 0, -1);
    //echo '请求参数<br/>'.$post_data;
    //发送post请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    $result = curl_exec($ch);
    $data = str_replace("\"", '"', $result);
    //$data = json_decode( $data, 1 );
    print_r($data);
}


//快递100实时查询物理轨迹,区别这个方法所有客户调用总平台的参数，然后记录他的调用次数，根据次数收费
function  get_kuaidi100_logistics_track_all($basekeynum = '', $com = '', $num = '', $phone = '')
{
    //账号zhensoft1 密码：wl905507
    // 实时查询示例代码
    // 授权信息可通过链接查看：https://api.kuaidi100.com/manager/page/myinfo/enterprise
    //$com = 'shunfeng';
    //$num = 'SF1324866433451';
    //$phone = '';
    //参数设置
    $plat_kuaidi100_set_info = Db::table('plat_kuaidi100_all_set')->where("id='1'")->find();
    $check_status         = $plat_kuaidi100_set_info['check_status'];
    $subscribe_status       = $plat_kuaidi100_set_info['subscribe_status'];
    $callbackurl       = $plat_kuaidi100_set_info['callbackurl'];
    $customer       = $plat_kuaidi100_set_info['customer'];
    $key       = $plat_kuaidi100_set_info['key'];

    //开关是否开启
    $check_status = $plat_kuaidi100_set_info['check_status'];
    if ($sms_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "实时物流查询未开启！";
        echo json_encode($return_arr);
        die;
    }

    if ($customer == '' || $key == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "customer或者key为空！";
        echo json_encode($return_arr);
        die();
    }
    if ($com == '' || $num == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "快递单号或者编码为空！";
        echo json_encode($return_arr);
        die();
    }


    $key = $key;
    //客户授权key
    $customer = $customer;
    //查询公司编号
    $param = array(
        'com' => $com,             //快递公司编码
        'num' => $num,     //快递单号
        'phone' => $phone,                //手机号
        'from' => '',                 //出发地城市
        'to' => '',                   //目的地城市
        'resultv2' => '1'             //开启行政区域解析
    );
    //请求参数
    $post_data = array();
    $post_data['customer'] = $customer;
    $post_data['param'] = json_encode($param);
    $sign = md5($post_data['param'] . $key . $post_data['customer']);
    $post_data['sign'] = strtoupper($sign);
    $url = 'http://poll.kuaidi100.com/poll/query.do';
    //实时查询请求地址
    $params = '';
    foreach ($post_data as $k => $v) {
        $params .= "$k=" . urlencode($v) . '&';
        //默认UTF-8编码格式
    }
    $post_data = substr($params, 0, -1);
    //echo '请求参数<br/>'.$post_data;
    //发送post请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    $result = curl_exec($ch);
    $data = str_replace("\"", '"', $result);
    //$data = json_decode( $data, 1 );
    print_r($data);
    $arr = json_decode($data, 1);
    //把返回结果转化成数组成
    //记录调用日志和增加统计计费表（计费表不存在新增，存在更新调用次数）
    $logistics_track_log["words"] = json_encode($arr['data'], JSON_UNESCAPED_UNICODE);
    $logistics_track_log["time"] = time();
    $logistics_track_log["clientkeynum"] = $basekeynum;
    $logistics_track_log["api_return_content"] = $data;
    $logistics_track_log["kuaidi_name"] = "";
    $logistics_track_log["kuaidi_code"] = $com;
    $logistics_track_log["kuaidi_num"] = $num;
    $logistics_track_log["state"] = $arr['state'];
    Db::table('plat_kuaidi100_logistics_track_log')->insert($logistics_track_log);
    //返回正确才计入账单
    if ($arr['status'] == "200") {
        //账单
        $info = Db::table('plat_kuaidi100_logistics_track_bill')->where(" kuaidi_code='$com' and kuaidi_num='$num' ")->find();
        if (empty($info)) {
            $logistics_track_bill["words"] = json_encode($arr['data'], JSON_UNESCAPED_UNICODE);
            $logistics_track_bill["api_return_content"] = $data;
            $logistics_track_bill["time"] = time();
            $logistics_track_bill["clientkeynum"] = $basekeynum;
            $logistics_track_bill["kuaidi_code"] = $com;
            $logistics_track_bill["kuaidi_num"] = $num;
            $logistics_track_bill["num"] = "1";
            $logistics_track_bill["state"] = $arr['state'];
            Db::table('plat_kuaidi100_logistics_track_bill')->insert($logistics_track_bill);
        } else {
            $logistics_track_bill["words"] = json_encode($arr['data'], JSON_UNESCAPED_UNICODE);
            $logistics_track_bill["api_return_content"] = $data;
            $logistics_track_bill["clientkeynum"] = $basekeynum;
            $logistics_track_bill["num"] = $info['num'] + 1;
            $logistics_track_bill["mod_time"] = time();
            $logistics_track_bill["state"] = $arr['state'];
            Db::table('plat_kuaidi100_logistics_track_bill')->where(" kuaidi_code='$com' and kuaidi_num='$num' ")->update($logistics_track_bill);
        }
    }
}



//快递100订阅快递单号查询
function  subscribe_kuaidi100_logistics_track($basekeynum = '', $com = '', $num = '')
{
    //账号zhensoft1 密码：wl905507
    // 实时查询示例代码
    // 授权信息可通过链接查看：https://api.kuaidi100.com/manager/page/myinfo/enterprise
    $plat_kuaidi100_set_info = Db::table('plat_kuaidi100_set')->where("basekeynum='$basekeynum'")->find();
    $check_status         = $plat_kuaidi100_set_info['check_status'];
    $subscribe_status       = $plat_kuaidi100_set_info['subscribe_status'];
    $callbackurl       = $plat_kuaidi100_set_info['callbackurl'];
    $customer       = $plat_kuaidi100_set_info['customer'];
    $key       = $plat_kuaidi100_set_info['key'];

    //开关是否开启
    $subscribe_status = $plat_kuaidi100_set_info['subscribe_status'];
    if ($subscribe_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "订阅功能未开启！";
        echo json_encode($return_arr);
        die;
    }

    if ($key == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "key为空！";
        echo json_encode($return_arr);
        die();
    }
    if ($com == '' || $num == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "快递单号或者编码为空！";
        echo json_encode($return_arr);
        die();
    }
    if ($callbackurl == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "callback回调地址为空！";
        echo json_encode($return_arr);
        die();
    }
    //参数设置
    $key = $key;                          //客户授权key
    $callbackurl = $callbackurl;
    $company = $com;
    $number = $num;
    $param = array(
        'company' => $company,             //快递公司编码
        'number' => $number,      //快递单号
        'from' => '',                     //出发地城市
        'to' => '',                       //目的地城市
        'key' => $key,                    //客户授权key
        'parameters' => array(
            'callbackurl' =>  $callbackurl, //回调地址
            'salt' => '',                 //加密串
            'resultv2' => '1',            //行政区域解析
            'autoCom' => '0',             //单号智能识别
            'interCom' => '0',            //开启国际版
            'departureCountry' => '',     //出发国
            'departureCom' => '',         //出发国快递公司编码
            'destinationCountry' => '',   //目的国
            'destinationCom' => '',       //目的国快递公司编码
            'phone' => ''                 //手机号
        )
    );

    //请求参数
    $post_data = array();
    $post_data["schema"] = 'json';
    $post_data["param"] = json_encode($param);
    $url = 'http://poll.kuaidi100.com/poll';    //订阅请求地址
    $params = "";
    foreach ($post_data as $k => $v) {
        $params .= "$k=" . urlencode($v) . "&";     //默认UTF-8编码格式
    }
    $post_data = substr($params, 0, -1);
    //发送post请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    $result = curl_exec($ch);
    $data = str_replace("\"", '"', $result);
    //$data = json_decode( $data, 1 );
    print_r($data);
}

//获取http协议:http还是https
function get_http_type()
{
    $http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
    return $http_type;
}

//获取随机整数
function rand_z($big)
{
    if ($big < 0) {
        $big = 0;
    }
    $number = mt_rand(0, $big);
    return $number;
}

function decrypt($data)
{
    $x = 0;
    $data = base64_decode($data);
    $len = strlen($data);

    $char = "";
    for ($i = 0; $i < $len; $i++) {

        $char .= substr($x, 1);
        $x++;
    }
    $str = "";
    for ($i = 0; $i < $len; $i++) {
        if (ord(substr($data, $i, 1)) < ord(substr($char, $i, 1))) {
            $str .= chr((ord(substr($data, $i, 1)) + 256) - ord(substr($char, $i, 1)));
        } else {
            $str .= chr(ord(substr($data, $i, 1)) - ord(substr($char, $i, 1)));
        }
    }
    return $str;
}

//分割字母和数字
function fenge($res)
{
    $reg = '/^[a-zA-Z]$/';                    // 正则句

    $proArr = str_split($res);           // 将字符串切割成数组
    $proLen = count($proArr);               // 计算该数组的长度
    $wordStr = '';                           // 存放字母
    $numStr = '';                            // 存放数字，包括前导零

    // 通过循环去核对数组中每个数据是否满足正则句
    for ($iCount = 0; $iCount < $proLen; $iCount++) {
        if (preg_match($reg, $proArr[$iCount], $match))    // 满足正则句表示该数据是字符
        {
            $wordStr .= $proArr[$iCount];                    // 字符存放的数组
        } else                                  // 数字（包括0）
        {
            $numStr .= $proArr[$iCount];    // 数字存放的数组，保留所有数字字符，包括0
        }
    }

    // 确保即使是纯0的字符串也能正确保留
    // 例如：'A000' 会返回 ['word' => 'A', 'num' => '000']

    return array('word' => $wordStr, 'num' => $numStr);    // 为了方便使用重新组合成数组
}
function encrypt($data)
{
    //$key = md5($key);
    $len = strlen($data);
    //$l = strlen($key)
    $str = "";
    for ($i = 0; $i < $len; $i++) {
        $str .= chr(ord($data[
            $i]));
    }
    return base64_encode($str);
}

/*获取用户基本信息*/
function wx_get_userinfo($appID, $appsecret)
{
    //从微信域名跳转回来会get过来一个code参数如果没有code参数则需要跳转到微信域名去授权
    $code = @$_GET['code'];
    //print_r($code);die;
    if (!$code) {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
        $redirect_uri = "$protocol$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
        $redirect_uri = urlencode($redirect_uri);
        $scope = "snsapi_userinfo"; //scope中snsapi_base和snsapi_userinfo
        $headerurl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $appID . "&redirect_uri=" . $redirect_uri . "&response_type=code&scope=" . $scope . "&state=STATE#wechat_redirect";
        header("location:$headerurl");
        die;
    }
    //通过code换取openid
    $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" . $appID . "&secret=" . $appsecret . "&code=" . $code . "&grant_type=authorization_code";
    $json = file_get_contents($url);     //如果php.ini没有开启openssl会报错,解决方案www.wlphp.com/?post=16,获取采用curl代替file_get_contents
    $arr = json_decode($json, true);
    //检验授权凭证（access_token）是否有效
    $url = "https://api.weixin.qq.com/sns/auth?access_token=" . $arr[access_token] . "&openid=" . $arr[openid];
    $json = file_get_contents($url);
    $check = json_decode($json, true);
    if ($check[errcode] !== 0) {
        //刷新access_token（如果需要）
        $url = "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=" . $appID . "&grant_type=refresh_token&refresh_token=" . $arr[refresh_token];
        $json = file_get_contents($url);
        $arr = json_decode($json, true);
    }
    /**拉取用户信息(需scope为 snsapi_userinfo)
     * {
     * "openid":" OPENID",
     * " nickname": NICKNAME,
     * "sex":"1",
     * "province":"PROVINCE"
     * "city":"CITY",
     * "country":"COUNTRY",
     * "headimgurl": "http://wx.qlogo.cn/mmopen/。。。",
     * "privilege":[
     * "PRIVILEGE1"
     * "PRIVILEGE2"
     * ],
     * "unionid": "o6_bmasdasdsad6_2sgVt7hMZOPfL"
     * }
     */
    // $url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=".gzhAccessToken($appID,$appsecret)."&openid=".$arr[openid]."&lang=zh_CN";
    $url = "https://api.weixin.qq.com/sns/userinfo?access_token=" . $arr[access_token] . "&openid=" . $arr[openid] . "&lang=zh_CN";
    $json = file_get_contents($url);
    $arr1 = json_decode($json, true);
    $arr = array_merge($arr, $arr1);
    return $arr;
}


/**
 * 接口描述：数据导出（带图片）  index/index/outExcel
 * <AUTHOR>
 * @date 2022-10-12
 */
function outExcel($data, $filename)
{
    //1.清除缓冲区，避免乱码
    //2.导入PHPExcel类库
    //3.查询出数据库数据
    //4.实例化PHPExcel对象
    //5.设置sheet的名称,以及单元格字段信息
    //6.设置内容（在此处添加导入图片的代码）
    //7.通过PHPExcel_IOFactory的写函数将上面数据写出来
    //8.设置表格文件的名称
    //9.生成并下载表格
    //====================================================

    //1.清除缓冲区,避免乱码
    ob_end_clean();
    //2.导入PHPExcel类库
    // vendor("PHPExcel.PHPExcel");
    // vendor("PHPExcel.PHPExcel.IOFactory");
    // vendor("PHPExcel.PHPExcel.Writer.Excel2007");

    //4.实例化PHPExcel对象
    $objPHPExcel = new \PHPExcel();
    $xlsWriter = new \PHPExcel_Writer_Excel5($objPHPExcel); //用其他版本格式
    //5.设置当前sheet和单元格信息
    $objPHPExcel->getActiveSheet()->setTitle('test'); //设置sheet名称
    $objActSheet = $objPHPExcel->setActiveSheetIndex(0) //设置sheet的起始位置为0
        ->setCellValue('A1', '卡号')  //设置单元格字段信息
        ->setCellValue('B1', '密码')
        ->setCellValue('C1', '状态');
    // ->setCellValue('D1', '二维码');
    // 设置C列单元格宽度为15（用来放置图片）

    //6.设置内容
    $count = count($data);  //计算有多少条数据
    //设置内容 i=2表示从第二段开始设值
    for ($i = 2; $i <= $count + 1; $i++) {
        //填入数据详情是这样的 A2,data[0]['Id'];
        //                   A3,data[1]['id'];      以此类推
        $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $data[$i - 2][0]);
        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $data[$i - 2][1]);
        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $data[$i - 2][2]);


        //导入图片代码方法一：无法获取网络图片，只可导入本地图片！！！！！============

        // $objDrawing = new \PHPExcel_Worksheet_Drawing();//图片生成

        // //获取图片路径
        // $objDrawing->setPath("本地图片路径");
        // //设置宽度高度
        // $objDrawing->setHeight(220);//照片高度
        // $objDrawing->setWidth(220); //照片宽度

        // // /*设置图片要插入的单元格*/
        // $objDrawing->setCoordinates('C' . $i);
        // // 图片偏移距离
        // $objDrawing->setOffsetX(0);
        // $objDrawing->setOffsetY(0);
        // $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
    }

    //7.通过PHPExcel_IOFactory的写函数将上面数据写出来
    $xlsWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");

    //8.设置表格文件的名称
    $outputFileName = $filename;

    header("Content-Type: application/force-download");

    header("Content-Type: application/octet-stream");

    header("Content-Type: application/download");

    header('Content-Disposition:inline;filename="' . $outputFileName . '"');

    header("Content-Transfer-Encoding: binary");

    header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");

    header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");

    header("Cache-Control: must-revalidate, post-check=0, pre-check=0");

    header("Pragma: no-cache");
    //9.生成并下载表格
    $xlsWriter->save("php://output");
}


/**
 * 后台账号登录日志记录
 */
function addloginlog($remark, $accountname = false)
{
    $plat_accountloginlog = Db::table('plat_accountloginlog');
    $data['accountname']  = $accountname;
    //取出该账号对应的平台客户basekeynum,没有默认是平台
    $accountinfo           = Db::table('plat_account')->where("accountname='$accountname'")->find();
    $data['basekeynum']    = $accountinfo['basekeynum'];
    $data['logintime']     = time();
    $data['logindatetime'] = date("Y-m-d H:i:s", time());
    $data['remark']        = $remark;
    $data['loginip']       = $_SERVER['REMOTE_ADDR'];
    $data['login_location']        = get_location($_SERVER['REMOTE_ADDR']);
    $data['browser']        = get_browser1();
    $data['os']        = get_os();

//    $data['real_ip']       = $_SERVER['HTTP_X_FORWARDED_FOR'];  //如果是负载均衡的话，用这个或者HTTP_REMOTEIP，获取客户端ip地址
//    $data['real_location']        = get_location($_SERVER['HTTP_X_FORWARDED_FOR']);
    $plat_accountloginlog->insert($data);
}

// 添加后台操作日志$log简描述  $data具体数据，不要在查询的地方滥用
function addoperatelog($log, $content)
{
    $data['accountname']     = session("cn_accountinfo.accountname");
    $data['accountkeynum']   = session("cn_accountinfo.keynum");
    $data['accountid']       = session("cn_accountinfo.account_id");
    $data['basekeynum']      = session("cn_accountinfo.basekeynum");
    $data['log']             = $log;
    $data['content']         = $content;
    $data['ip']              = $_SERVER["REMOTE_ADDR"];
    // $data['real_ip']         =  $_SERVER['HTTP_X_FORWARDED_FOR'];  //如果是负载均衡的话，用这个或者HTTP_REMOTEIP，获取客户端ip地址
    $data['operatetime']     = time();
    $data['operatedatetime'] = date("Y-m-d H:i:s", time());
    Db::table('plat_accountoperationlog')->insert($data);
}

//账号密码加密函数 不可逆加密
function password($password)
{
    return md5('' . $password . '');
}

//全球唯一标识符号
function create_guid()
{
    $charid = strtoupper(md5(uniqid(mt_rand(), true)));
    $hyphen = "";
    $uuid   = substr($charid, 6, 2) . substr($charid, 4, 2) .
        substr($charid, 2, 2) . substr($charid, 0, 2) . $hyphen
        . substr($charid, 10, 2) . substr($charid, 8, 2) . $hyphen
        . substr($charid, 14, 2) . substr($charid, 12, 2) . $hyphen
        . substr($charid, 16, 4) . $hyphen . substr($charid, 20, 12);
    return $uuid;
}

/**
 * 后台基类初始化菜单
 */
function EffectiveHttp($str)
{
    if (strpos($str, "http://") === 0 or strpos($str, "https://") === 0) {
        return true;
    } else {
        return false;
    }
}

/**
 * 判断是ie8以及ie8以下内核浏览器
 */
function check_browser()
{
    if (strpos($_SERVER["HTTP_USER_AGENT"], "Firefox")) {
        return false;
    } else if (strpos($_SERVER["HTTP_USER_AGENT"], "Chrome")) {
        return false;
    } else if (strpos($_SERVER["HTTP_USER_AGENT"], "Safari")) {
        return false;
    } else if (strpos($_SERVER["HTTP_USER_AGENT"], "Opera")) {
        return false;
    } else {
        return true;
    }
}

//判断是否ie浏览器
function isIEbrower()
{
    return false !== stristr($_SERVER['HTTP_USER_AGENT'], 'MSIE');
}

//组织机构分类数
function Get_Tree($arr, $pid)
{
    $tree = array();
    foreach ($arr as $key => $val) {
        if ($val['parentId'] == $pid) {
            $val["spread"]   = true;
            $val['children'] = Get_Tree($arr, $val['id']);
            $tree[]          = $val;
        }
    }
    return $tree;
}

//获取菜单树结构
function Get_Menu_Tree($arr, $pid)
{
    $tree = array();
    foreach ($arr as $key => $val) {
        if ($val['pid'] == $pid) {
            $val['children'] = Get_Menu_Tree($arr, $val['menu_id']);
            $tree[]          = $val;
        }
    }
    return $tree;
}

//单张图片上传
function UpImage($callBack = "image", $width = 100, $height = 100, $image = "")
{
    echo '<iframe scrolling="no" frameborder="0" border="0" onload="this.height=this.contentWindow.document.body.scrollHeight;this.width=this.contentWindow.document.body.scrollWidth;" width=' . $width . ' height="' . $height . '"  src="' . url('Upload/uploadpic') . '?Width=' . $width . '&Height=' . $height . '&BackCall=' . $callBack . '&Img=' . $image . '"></iframe>
         <input type="hidden" name="' . $callBack . '" id="' . $callBack . '">';
}

//视频上传
function UpVideo($callBack = "video", $width = 100, $height = 100, $video = "")
{
    echo '<iframe scrolling="no" frameborder="0" border="0" onload="this.height=this.contentWindow.document.body.scrollHeight;this.width=this.contentWindow.document.body.scrollWidth;" width=' . $width . ' height="' . $height . '" 
    
    src="' . url('Upload/uploadvideo') . '?Width=' . $width . '&Height=' . $height . '&BackCall=' . $callBack . '&Video=' . $video . '"></iframe>
         <input type="hidden"    name="' . $callBack . '" id="' . $callBack . '">';
}

//多张图片上传
function BatchImage($callBack = "image", $width = 100, $height = 100, $image = "")
{
    echo '<iframe scrolling="no" frameborder="0" border="0" onload="this.height=this.contentWindow.document.body.scrollHeight;this.width=this.contentWindow.document.body.scrollWidth;" width=' . $width . ' height="' . $height . '" 
    
    src="' . url('Upload/batchpic') . '?BackCall=' . $callBack . '&Img=' . $image . '"></iframe>
		<input type="hidden" name="' . $callBack . '" id="' . $callBack . '">';
}

//文件上传到阿里云oss方法
function uploadFileToAliOss($local_file, $remote_file)
{
    //从数据取出来阿里云oss配置参数
    $info = $rs = Db::table('plat_system_set')->where("id='1'")->find();
    //上传到阿里云oss
    // 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录 https://ram.console.aliyun.com 创建RAM账号。
    $accessKeyId     = $info['ali_accesskeyid']; //"LTAIVZPMH2ErRTa7";
    $accessKeySecret = $info['ali_accesskeysecret']; //"yxgVMMD3Eh8Jko2MR64AuCkkslbkok";
    // Endpoint以杭州为例，其它Region请按实际情况填写。
    $endpoint = $info['ali_endpoint']; //"http://oss-cn-beijing.aliyuncs.com";
    // 存储空间名称
    $bucket = $info['ali_bucket']; //"kunyuan";
    // 文件名称
    $object = $remote_file; //文件的路径
    // <yourLocalFile>由本地文件路径加文件名包括后缀组成，例如/users/local/myfile.txt
    $filePath = $local_file;
    $flag = true;
    try {
        $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
        $ossClient->uploadFile($bucket, $object, $filePath);
    } catch (OssException $e) {
        $msg = $e->getMessage();
        $flag = false;
    }
    //根据flag判断是否成功
    if (!$flag) {
        $rt['sta'] = "0";
        $rt['msg'] = "上传到oss失败！详细信息：" . $msg;
        $rt['url'] = "";
        return $rt;
        die;
    }
    //成功返回
    $rt['sta'] = "1";
    $rt['msg'] = "上传到oss成功！";
    $rt['url'] = $info['ali_bucket_url'] . "/" . $object;
    return $rt;
    die;
}


//文件上传到服务器web目录可以通过浏览器直接访问
function uploadFileToWebDir()
{
    //后期封装
}


/*Mb和GB转化*/
function getFilesize($num)
{
    $p      = 3;
    $format = 'GB';
    $num /= pow(1024, $p);
    return number_format($num, 2);
}

//xml转数组
function xmlToArray($xml)
{
    //将XML转为array
    $array_data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
    return $array_data;
}

//短信平台
function get_duanxin_info($keynum)
{
    $retinfo        = '';
    $xmlstring      = '';
    $info           = '';
    $keynum         = $keynum;
    $duanxinsetinfo = Db::table('plat_sms_set')->where("basekeynum='$keynum'")->find();
    if (empty($duanxinsetinfo)) {
        $return_arr['sta'] = '1';
        $return_arr['msg'] = "没有找到记录！";
        return $return_arr;
        die;
    }

    $userid   = $duanxinsetinfo['username'];
    $password = $duanxinsetinfo['password'];
    $qUrl     = $duanxinsetinfo['url'] . "/CASServer/SmsAPI/QueryUserInfo.jsp";
    $qUrl .= '?userid=' . $userid . '&password=' . urlencode($password);
    //echo $qUrl;die;
    $xmlstring = file_get_contents($qUrl);
    if (!$xmlstring) {
        $return_arr['sta'] = '0';
        $return_arr['msg'] = "未知错误！";
        return $return_arr;
        die;
    }

    $arr = xmlToArray($xmlstring);
    //print_r($arr);die;
    if ($arr['@attributes']['return'] != '0') {
        $return_arr['sta'] = '0';
        $return_arr['msg'] = $arr['@attributes']['info'];
        return $return_arr;
        die;
    }
    $return_arr['sta']       = '1';
    $return_arr['name']      = $arr['NAME'];
    $return_arr['sign']      = $arr['SIGN'];
    $return_arr['riches']    = $arr['RICHES'];
    $return_arr['totalin']   = $arr['TOTALIN'];
    $return_arr['totalsend'] = $arr['TOTALSEND'];
    return $return_arr;
    die;
}

/**
 * 去除多余的转义字符
 */
function doaddslashes($param)
{
    $param = addslashesDeep($param);
//    if (!get_magic_quotes_gpc()) {
//        $param = addslashesDeep($param);
//    }
    return $param;
}

/**
 * 递归去除转义字符
 */
function addslashesDeep($value)
{
    $value = is_array($value) ? array_map('addslashesDeep', $value) : addslashes($value);
    return $value;
}

/**
 * 获取最近七天所有日期
 */
function get_weeks($time = '', $format = 'Y-m-d')
{
    $time = $time != '' ? $time : time();
    //组合数据
    $data = [];
    for ($i = 1; $i <= 7; $i++) {
        $data[$i] = date($format, strtotime(+($i - 7) . ' days', $time));
    }
    return $data;
}

/**
 * 获取最近七天所有日期会员数量
 */
function get_weeks_member($time = '', $format = 'Y-m-d')
{
    return  1;
    $time = $time != '' ? $time : time();
    //组合数据
    $date = [];
    for ($i = 1; $i <= 7; $i++) {
        $data[$i] = date($format, strtotime(+($i - 7) . ' days', $time));
        $ts1      = strtotime("$ts  00:00:00");
        $ts2      = strtotime("$ts  23:59:59");
        $count    = Db::table('client_merchant_member')->where("add_time>='$ts1' && add_time<='$ts2'   ")->count();
        $data[$i] = $count;
    }
    return $data;
}

/**
 * 获取终端访问统计数量
 */
function get_terminal()
{
    $list = Db::table('plat_terminal_log')->where("1=1")->select();
    foreach ($list as $key => $value) {
        $data1[$key] = "'" . $value['terminal_name'] . "'";
        $data2[$key] = "{value:" . $value['visit_nums'] . ", name:'" . $value['terminal_name'] . "'}";
    }
    $data['name']       = $data1;
    $data['visit_nums'] = $data2;
    return $data;
}


/**
 * 最近流量趋势
 */
function get_recent_visit()
{
    $list = Db::table('plat_visit_log')->where("1=1")->limit(24)->select();
    foreach ($list as $key => $value) {
        $data1[$key] = "'" . $value['hour'] . "'";
        $data2[$key] = "{value:" . $value['visit_pv_nums'] . ", name:'" . $value['hour'] . "'}";
        $data3[$key] = "{value:" . $value['visit_uv_nums'] . ", name:'" . $value['hour'] . "'}";
    }
    $data['name']          = $data1;
    $data['visit_pv_nums'] = $data2;
    $data['visit_uv_nums'] = $data3;
    return $data;
}


//发送邮件验证码
function send_mail($recipient = "", $html = "", $subject = "", $param = "", $basekeynum = "")
{
    date_default_timezone_set('PRC');
    $mail            = new PHPMailer();
    $mail->SMTPDebug = 0; //调试模式 0关闭
    $mail->isSMTP();
    $mail->SMTPAuth   = true;
    $mail->Host       = 'smtp.qq.com';
    $mail->SMTPSecure = 'ssl';
    //设置ssl连接smtp服务器的远程服务器端口号 可选465或587
    $mail->Port     = 465;
    $mail->Hostname = 'localhost';
    $mail->CharSet  = 'UTF-8';
    $mail->FromName = 'wlphp.com'; //发件人名字(发件人和收件人不是好友的情况下显示)

    $mail->Username = '<EMAIL>'; //发件邮箱的账号
    $mail->Password = 'kwgceqrennkoidjd'; //发件邮箱邮箱授权码
    $mail->From     = '<EMAIL>';

    $mail->isHTML(true);
    $mail->addAddress($recipient, ''); //收件人的姓名(发件人和收件人不是好友的情况下显示)
    $mail->Subject = $subject ? $subject : '预警提示'; //邮件主题
    //$mail->Body = "58同城-邮箱绑定邮件<br/>亲爱的58同城用户lkxlg_p0：请复制下面的动态码，并返回原页面提交以完成绑定邮箱。<br/>963854<br/>绑定成功后可获得 50 个信用。信用值越高，每天可发布的信息数量越多，认证后的邮箱可用于登录和找回密码。<br/>58赶集集团<br/>http://www.58.com";
    //$mail->Body =file_get_contents("http://address.wlphp.com/");
    //$mail->addAttachment('test.jpg','test.png');   //test.jpg本地文件路径   test.png接收到邮件里面文件名称
    $mail->Body = $html;
    $status     = $mail->send();
    //记录邮件发送日志
    $data['subject']    = $subject;
    $data['words']      = $html;
    $data['basekeynum'] = $basekeynum;
    $data['recipient']  = $recipient;
    $data['json']       = json_encode($param, JSON_UNESCAPED_UNICODE);
    $data['ip']         = $_SERVER["REMOTE_ADDR"];
    $data['time']       = time();
    $data['datetime']   = date("Y-m-d H:i:s", time());

    if ($status) {
        $rt['sta']            = '1';
        $rt['msg']            = '发送邮件成功' . date('Y-m-d H:i:s');
        $data['is_send_ok']   = 1;
        $data['send_message'] = $rt['msg'];
        Db::table('plat_mail_log')->insert($data);
        return $rt;
    } else {
        $rt['sta']            = '0';
        $rt['msg']            = '发送邮件失败，错误信息未：' . $mail->ErrorInfo;
        $data['is_send_ok']   = 0;
        $data['send_message'] = $rt['msg'];
        Db::table('plat_mail_log')->insert($data);
        return $rt;
    }
}

/**
 *菜单权限校验函数 $ajax如果是1表示返回ajax，否则返回页面错误
 */
function check_auth($url, $ajax = 0)
{
    //如果登陆者是平台级别则免疫所有权限
    $basekeynum = session("cn_accountinfo.basekeynum");
    if ($basekeynum == '平台') {
        return true;
    }

    $menulist = session("cn_accountinfo.menulist");
    if (empty($menulist)) {
        //没有权限
        if ($ajax == 1) {
            $rt['sta'] = "0";
            $rt['msg'] = "当前登录用户没有此菜单操作权限，请联系管理员授权！";
            echo json_encode($rt);
            die;
        } else {
            error_tips("当前登录用户没有此菜单操作权限，请联系管理员授权！");
            die;
        }
    }

    $where = "  menu_id  in ($menulist)  and  url='$url' ";
    $info  = Db::table('plat_menu')->where($where)->find();
    if (!$info['menu_id']) {
        //没有权限
        if ($ajax == 1) {
            $rt['sta'] = "0";
            $rt['msg'] = "当前登录用户没有此菜单操作权限，请联系管理员授权！";
            echo json_encode($rt);
            die;
        } else {
            error_tips("当前登录用户没有此菜单操作权限，请联系管理员授权！");
            die;
        }
    }
}

/**
 *页面错误提示函数
 */
function error_tips($errorinfo)
{
    //获取静态资源前缀
    $view_replace_str_arr = Config::get('view_replace_str');
    $static               = $view_replace_str_arr['/static'];

    $html                 = '<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>出错了</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="' . $static . '/admin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="' . $static . '/admin/style/admin.css" media="all">
</head>
<body>
<div class="layui-fluid">
  <div class="layadmin-tips">
    <i class="layui-icon" face  style="font-size: 180px;">&#xe664;</i>
    <div class="layui-text" style="font-size: 20px;">
     ' . $errorinfo . '
    </div>

  </div>
</div>
</body>
</html>';
    echo $html;
    die;
}

//获取参数值并且处理这个参数,如果不存在赋值并且返回空值
function getparam($param)
{
    $param = trim($param);
    return isset($param) && $param != '' ? $param : '';
}

//获取后台登录域名,第一个
function get_glurl($keynum)
{
    $accountinfo = Db::table('plat_account')->where("basekeynum='$keynum' and  keynum='$keynum' ")->find();
    $str_glurl   = $accountinfo['glurl'];
    $arr_glurl   = explode(',', $str_glurl);
    return "http://" . $arr_glurl['0'];
}

//获取前台域名,第一个
function get_weburl($keynum)
{
    if (!empty($keynum)) {
        $accountinfo = Db::table('plat_account')->where("basekeynum='$keynum' and  keynum='$keynum' ")->find();
        if($accountinfo){
            $str_weburl  = $accountinfo['weburl'];
            $arr_weburl  = explode(',', $str_weburl);
            return $arr_weburl['0'];
        }else{
            return "";
        }

    }
}

//生成自动登录签名
function cre_aotologin_sign($keynum, $ts, $autologin_key)
{
    $autologin_key = Config::get('autologin_key');
    return strtoupper(md5($keynum . $ts . $autologin_key));
}
//验证自动登录签名
function check_autologin_sign($keynum, $ts, $autologin_key, $sign)
{
    $autologin_key = Config::get('autologin_key');
    if (cre_aotologin_sign($keynum, $ts, $autologin_key) == $sign) {
        return true;
    }
    return false;
}

//判断是手机终端
function isMobile()
{
    return 1;
    // 如果有HTTP_X_WAP_PROFILE则一定是移动设备
    if (isset($_SERVER['HTTP_X_WAP_PROFILE'])) {
        return true;
    }
    // 如果via信息含有wap则一定是移动设备,部分服务商会屏蔽该信息
    if (isset($_SERVER['HTTP_VIA'])) {
        // 找不到为flase,否则为true
        return stristr($_SERVER['HTTP_VIA'], "wap") ? true : false;
    }
    // 脑残法，判断手机发送的客户端标志,兼容性有待提高
    if (isset($_SERVER['HTTP_USER_AGENT'])) {
        $clientkeywords = array(
            'nokia',
            'sony',
            'ericsson',
            'mot',
            'samsung',
            'htc',
            'sgh',
            'lg',
            'sharp',
            'sie-',
            'philips',
            'panasonic',
            'alcatel',
            'lenovo',
            'iphone',
            'ipod',
            'blackberry',
            'meizu',
            'android',
            'netfront',
            'symbian',
            'ucweb',
            'windowsce',
            'palm',
            'operamini',
            'operamobi',
            'openwave',
            'nexusone',
            'cldc',
            'midp',
            'wap',
            'mobile',
        );
        // 从HTTP_USER_AGENT中查找手机浏览器的关键字
        if (preg_match("/(" . implode('|', $clientkeywords) . ")/i", strtolower($_SERVER['HTTP_USER_AGENT']))) {
            return true;
        }
    }
    // 协议法，因为有可能不准确，放到最后判断
    if (isset($_SERVER['HTTP_ACCEPT'])) {
        // 如果只支持wml并且不支持html那一定是移动设备
        // 如果支持wml和html但是wml在html之前则是移动设备
        if ((strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') !== false) && (strpos($_SERVER['HTTP_ACCEPT'], 'text/html') === false || (strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') < strpos($_SERVER['HTTP_ACCEPT'], 'text/html')))) {
            return true;
        }
    }
    return false;
}


/*
 * 发送短信验证啊$keynum
 */
function send_phone_code($basekeynum, $phone)
{
    $duanxinsetinfo = Db::table('plat_sms_set')->where("basekeynum='$basekeynum'")->find();
    $destnumbers    = $phone;
    $userid         = $duanxinsetinfo['username'];
    $password       = $duanxinsetinfo['password'];
    $qUrl           = $duanxinsetinfo['url'] . "/CASServer/SmsAPI/SendMessage.jsp";
    $code_status    = $duanxinsetinfo['code_status'];
    $content        = $duanxinsetinfo['code_content'];
    //总开关是否开启
    $sms_status = $duanxinsetinfo['sms_status'];
    if ($sms_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "短信验证码服务总开关没有开启";
        echo json_encode($return_arr);
        die;
    }
    if ($code_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "短信验证码服务没有开启";
        echo json_encode($return_arr);
        die;
    }
    if ($userid == '' || $password == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "短信账号或者密码为空！";
        echo json_encode($return_arr);
        die();
    }

    if ($destnumbers == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "发送手机号不能为空!";
        echo json_encode($return_arr);
        die();
    }

    $code                      = rand(1000, 9999);
    $content                   = str_replace("{code}", $code, $content);
    $msg                       = $content;
    $sendtime                  = date("Y-m-d H:i:s", time());
    $duanxin_log               = Db::table('sms_log');
    $datalog['words']          = $msg;
    $datalog['time']           = time();
    $datalog['phonenum']       = $destnumbers;
    $datalog["clientkeynum"]   = $basekeynum;

    $add_code["phone"]          = $destnumbers;
    $add_code["code"]           = $code;
    $add_code["time"]           = time(); //存入生成时间，有效期由验证的时候自己把握
    $add_code["clientkeynum"]   = $basekeynum;

    $qUrl .= '?userid=' . $userid . '&password=' . urlencode($password) . '&destnumbers=' . $destnumbers .
        '&msg=' . urlencode($msg) . '&sendtime=' . urlencode($sendtime);
    $xmlstring                     = file_get_contents($qUrl);
    $datalog["api_return_content"] = json_encode(xmlToArray($xmlstring));
    Db::table('plat_sms_log')->insert($datalog);
    Db::table("plat_sms_code")->insert($add_code);
    $return_arr['sta'] = 1;
    $return_arr['msg'] = "发送成功!";
    echo json_encode($return_arr);
    die();
}


/**
 * 生成签名并发起请求   阿里云短信
 *
 * @param $accessKeyId string AccessKeyId (https://ak-console.aliyun.com/)
 * @param $accessKeySecret string AccessKeySecret
 * @param $domain string API接口所在域名
 * @param $params array API具体参数
 * @param $security boolean 使用https
 * @param $method boolean 使用GET或POST方法请求，VPC仅支持POST
 * @return bool|\stdClass 返回API接口调用结果，当发生错误时返回false
 */
function request_ali($accessKeyId, $accessKeySecret, $domain, $params, $security = false, $method = 'POST')
{
    $apiParams = array_merge(array(
        "SignatureMethod" => "HMAC-SHA1",
        "SignatureNonce" => uniqid(mt_rand(0, 0xffff), true),
        "SignatureVersion" => "1.0",
        "AccessKeyId" => $accessKeyId,
        "Timestamp" => gmdate("Y-m-d\TH:i:s\Z"),
        "Format" => "JSON",
    ), $params);
    ksort($apiParams);

    $sortedQueryStringTmp = "";
    foreach ($apiParams as $key => $value) {
        $sortedQueryStringTmp .= "&" . encode($key) . "=" . encode($value);
    }

    $stringToSign = "${method}&%2F&" . encode(substr($sortedQueryStringTmp, 1));

    $sign = base64_encode(hash_hmac("sha1", $stringToSign, $accessKeySecret . "&", true));

    $signature = encode($sign);

    $url = ($security ? 'https' : 'http') . "://{$domain}/";

    try {
        $content = fetchContent($url, $method, "Signature={$signature}{$sortedQueryStringTmp}");
        return json_decode($content, 1);
    } catch (\Exception $e) {
        return false;
    }
}


function encode($str)
{
    $res = urlencode($str);
    $res = preg_replace("/\+/", "%20", $res);
    $res = preg_replace("/\*/", "%2A", $res);
    $res = preg_replace("/%7E/", "~", $res);
    return $res;
}

function fetchContent($url, $method, $body)
{
    $ch = curl_init();

    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_POST, 1); //post提交方式
        curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    } else {
        $url .= '?' . $body;
    }

    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        "x-sdk-client" => "php/2.0.0"
    ));

    if (substr($url, 0, 5) == 'https') {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }

    $rtn = curl_exec($ch);

    if ($rtn === false) {
        // 大多由设置等原因引起，一般无法保障后续逻辑正常执行，
        // 所以这里触发的是E_USER_ERROR，会终止脚本执行，无法被try...catch捕获，需要用户排查环境、网络等故障
        trigger_error("[CURL_" . curl_errno($ch) . "]: " . curl_error($ch), E_USER_ERROR);
    }
    curl_close($ch);

    return $rtn;
}



/*
 * 阿里云发送短信验证啊$keynum
 */
function send_phone_code_ali($basekeynum, $phone)
{
    $duanxinsetinfo = Db::table('plat_sms_set_ali')->where("basekeynum='$basekeynum'")->find();
    $accesskeyid         = $duanxinsetinfo['accesskeyid'];
    $accesskeysecret       = $duanxinsetinfo['accesskeysecret'];
    $signname       = $duanxinsetinfo['signname'];
    $templatecode       = $duanxinsetinfo['templatecode'];

    //总开关是否开启
    $sms_status = $duanxinsetinfo['sms_status'];
    if ($sms_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "短信验证码服务总开关没有开启";
        echo json_encode($return_arr);
        die;
    }
    if ($code_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "短信验证码服务没有开启";
        echo json_encode($return_arr);
        die;
    }
    if ($accesskeyid == '' || $accesskeysecret == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "accesskeyid或者accesskeysecret为空！";
        echo json_encode($return_arr);
        die();
    }

    if ($phone == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "发送手机号不能为空!";
        echo json_encode($return_arr);
        die();
    }
    //生成短信验证码
    $code                      = rand(1000, 9999);
    $params = array();
    // *** 需用户填写部分 ***
    // fixme 必填：是否启用https
    $security = false;
    // fixme 必填: 请参阅 https://ak-console.aliyun.com/ 取得您的AK信息
    $accessKeyId = $accesskeyid;
    $accessKeySecret = $accesskeysecret;
    // fixme 必填: 短信接收号码
    $params["PhoneNumbers"] = $phone;
    // fixme 必填: 短信签名，应严格按"签名名称"填写，请参考: https://dysms.console.aliyun.com/dysms.htm#/develop/sign
    $params["SignName"] = $signname;
    // fixme 必填: 短信模板Code，应严格按"模板CODE"填写, 请参考: https://dysms.console.aliyun.com/dysms.htm#/develop/template
    $params["TemplateCode"] = $templatecode;
    // fixme 可选: 设置模板参数, 假如模板中存在变量需要替换则为必填项
    $params['TemplateParam'] = array(
        "code" => $code,
        //"product" => "阿里通信"
    );
    // fixme 可选: 设置发送短信流水号
    $params['OutId'] = "";
    // fixme 可选: 上行短信扩展码, 扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段
    $params['SmsUpExtendCode'] = "";
    // *** 需用户填写部分结束, 以下代码若无必要无需更改 ***
    if (!empty($params["TemplateParam"]) && is_array($params["TemplateParam"])) {
        $params["TemplateParam"] = json_encode($params["TemplateParam"], JSON_UNESCAPED_UNICODE);
    }
    // 此处可能会抛出异常，注意catch
    $arr = request_ali(
        $accessKeyId,
        $accessKeySecret,
        "dysmsapi.aliyuncs.com",
        array_merge($params, array(
            "RegionId" => "cn-hangzhou",
            "Action" => "SendSms",
            "Version" => "2017-05-25",
        )),
        $security
    );

    $datalog['words']          = $code;
    $datalog['time']           = time();
    $datalog['phonenum']       = $phone;
    $datalog["clientkeynum"]   = $basekeynum;
    $datalog['api_return_content'] = json_encode($arr, JSON_UNESCAPED_UNICODE);
    $add_code["phone"]          = $phone;
    $add_code["code"]           = $code;
    $add_code["time"]           = time(); //存入生成时间，有效期由验证的时候自己把握
    $add_code["clientkeynum"]   = $basekeynum;
    //添加两个表
    Db::table('plat_sms_log')->insert($datalog);
    Db::table("plat_sms_code")->insert($add_code);

    $return_arr['sta'] = 1;
    $return_arr['msg'] = "发送成功!";
    echo json_encode($return_arr);
    die();
}

//重复拉取会员信息
function refresh_memberinfo()
{
    $mid = session("cn_member.id"); //会员id
    if ($mid != '') {
        $member = Db::table("client_merchant_member")->where("id=' $mid'")->find();
        session("cn_member", $member);
    }
}

//获取随机数函数
function GetRandStr($length)
{
    $str     = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $len     = strlen($str) - 1;
    $randstr = '';
    for ($i = 0; $i < $length; $i++) {
        $num = mt_rand(0, $len);
        $randstr .= $str[$num];
    }
    return $randstr;
}

/**
 *页面错误提示函数
 */
function error_tips_web($errorinfo)
{
    //获取静态资源前缀
    $view_replace_str_arr = Config::get('view_replace_str');
    $static               = $view_replace_str_arr['__STATICWEB__'];

    $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>出错</title>
    <script src="' . $static . '/web/theme1/m/js/rem.js"></script>

    <link rel="stylesheet" href="' . $static . '/web/theme1/m/css/layui.css"  media="all">
    <link rel="stylesheet" href="' . $static . '/web/theme1/m/css/common.css">
    <link rel="stylesheet" href="' . $static . '/web/theme1/m/css/tips.css">
</head>
<body style="background: #FFF">
        <div id="app">
                <div class="page-header">
                        <div class="mint-header">
                           <div class="h-left">

                           </div>
                           <div class="h-right">

                           </div>
                        </div>
                    </div>
                    <!-- 图文 -->
                    <div class="error-img">
                        <img src="' . $static . '/web/theme1/m/images/no.png" alt="">
                    </div>
                    <div class="error-text">
                        <span>' . $errorinfo . '</span>
                    </div>
        </div>

</body>
</html>';
    echo $html;
}

//获取订单状态
function get_order_status($order_status)
{
    // $info            = Db::table('client_order_info')->where("order_id='$order_id'")->find();
    // $order_status    = $info['order_status'];
    //0下单成功，1已审核，2已发货，3签收，4签收过几日订单完成，5取消，6申请售后中，7售后完成
    if ($order_status == '0') {
        return "待审核";
    } else if ($order_status == '1') {
        return "发货中";
    } else if ($order_status == '2') {
        return "发货中";
    } else if ($order_status == '3') {
        return "已发货";
    } else {
        return "未知";
    }
}
//获取订单状态
function get_order_status_admin($order_status)
{
    // $info            = Db::table('client_order_info')->where("order_id='$order_id'")->find();
    // $order_status    = $info['order_status'];
    //0下单成功，1已审核，2已发货，3签收，4签收过几日订单完成，5取消，6申请售后中，7售后完成
    if ($order_status == '0') {
        return "待审核";
    } else if ($order_status == '1') {
        return "生成导出批次";
    } else if ($order_status == '2') {
        return "发货中";
    } else if ($order_status == '3') {
        return "已发货";
    } else {
        return "未知";
    }
}
/**
 * @return bool
 * 判断是否微信内置浏览器
 */
function is_weixin()
{
    if (empty($_SERVER['HTTP_USER_AGENT']) || strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') === false && strpos($_SERVER['HTTP_USER_AGENT'], 'Windows Phone') === false) {
        return false;
    }
    return true;
}

/**
 * @param $url 发送post请求的url
 * @param $data 发送的数据
 * @return mixed
 */
function curlPostErp($url, $data)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
    @curl_setopt($ch, CURLOPT_SAFE_UPLOAD, false); //php5.6.0开始，需要加上这行代码方可上传，否则取不到文件
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $tmpInfo = curl_exec($ch);
    $errorno = curl_errno($ch);
    if ($errorno) {
        $rt['slay'] = "-1";
        $rt['rs']   = "curl错误:$errorno";
        return json_encode($rt);
        die;
    }
    return $tmpInfo;
}

/**
 * 调取erp接口封装方法
 */
function sendtoerp($merchantkeynum, $data)
{

    $info      = Db::table("kufang_set")->where("basekeynum='$merchantkeynum'")->find();
    $AppKey    = trim($info['appkey']);
    $AppSecret = trim($info['appsecret']);
    $Token     = trim($info['token']);
    $apiurl    = trim($info['api_url']);
    //url地址
    $msg  = $data;
    $sign = strtoupper(md5($AppSecret . $msg . $AppKey));
    //post方法
    $senddata['msg']  = $msg;
    $senddata['code'] = $AppKey;
    $senddata['sign'] = $sign;
    $rs               = curlPostErp($apiurl, $senddata);
    logRes("发送数据:" . $msg . "---->返回数据：" . $rs, "erp");
    return $rs;
}






//和礼赠通对接核心方法
/*
 * 发送post请求
 */
function curlPost($url, $data)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $tmpInfo = curl_exec($ch);
    //$errorno=curl_errno($ch);
    return $tmpInfo;
}

function   sendtointerface($sendurl, $sendjson = array(), $sendfile = array())
{

    $private_key = '-----BEGIN RSA PRIVATE KEY-----
MIICXAIBAAKBgQDYucsb9ufvvGbdOv/kCMZ4oJfIz+eDJj6m2bjRzIM8P6sGXLFl
aXoJyYNbKi9ZCVTvYlE4i2RWlesSk63YX+1y4o5n9JjzbfkydZQy97FIaem/QvEP
Mfq3/A44PDykjtQRO11iIviFJlbR6bAYtbzem+dYS2cFZ+7XtGItS2ZxKwIDAQAB
AoGAF8iL2Y3G9jAeubh3zHaKEULOZWMrbOENjxHljmB7Jml2Q0R400A3/Er491Ax
0fuMLpkDgMkLQdVB7s/2DAC69eTR4vsnrQU0/eBikvEV2EEAPiuMQr2r/BYhEAyY
0R4edFjAG1sPCew/FqytSVMSWtxzGVOyB9CoAGI+4nBtCEECQQD4RAETQK9nZFhG
4TdLZv2WTdRcPjd6tLD3RtfQ7ldMtyhgO+WzNjj0zKoTbE3lp9WqDJonf5vTdfZr
S+1J80z/AkEA33o/sEV3G93tEewaAx1AncWFrRcjraMU5Y59Q2iUxWck+ASUPmj9
e4qfBIozBcVgH8YIyl6aLa4X4SzdN9uf1QJBAKdegTMPirCzT2gJcROeTRtFQQMm
1pQcuKkb02cBJ02KtOebudFFnsQ6LfaGL0XCDiNj95DlUJIQlp3jRAd0xNUCQE7l
WNDBZXCII+b85J4O2L4aerwF/EmT1o/Igz6mEEe0x19sNm3mIP9ZjBRNOgAik3IE
NxxC08mmubV8YkkTF1UCQBuLA9MAfqGcp9JOwoo9qPcBycLXfLpWrOcJcnweqMFC
A+lwOe0H9FBLT0N+2xRtLRX/XTqWlb7jOqJRCrbZmXow
-----END RSA PRIVATE KEY-----';


    $public_key = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDYucsb9ufvvGbdOv/kCMZ4oJfI
z+eDJj6m2bjRzIM8P6sGXLFlaXoJyYNbKi9ZCVTvYlE4i2RWlesSk63YX+1y4o5n
9JjzbfkydZQy97FIaem/QvEPMfq3/A44PDykjtQRO11iIviFJlbR6bAYtbzem+dY
S2cFZ+7XtGItS2ZxKwIDAQAB
-----END PUBLIC KEY-----';


    $private_key_pkcs8 = '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';






    $content = $sendjson;
    $randstr = random(16);
    $iv = $randstr;

    // 使用 openssl_encrypt 替换已废弃的 mcrypt_encrypt (PHP 7.4 兼容)
    $encrypted = openssl_encrypt($content, 'AES-128-CBC', $randstr, OPENSSL_RAW_DATA, $iv);
    $msg = base64_encode($encrypted);

    $pu_key = openssl_pkey_get_public($public_key); //这个函数可用来判断公钥是否是可用的
    openssl_public_encrypt($randstr, $encrypted, $pu_key); //公钥加密
    $encrypted = base64_encode($encrypted);
    $code = $encrypted;

    $pi_key = openssl_pkey_get_private($private_key); //这个函数可用来判断私钥是否是可用的，可用返回资源id Resource id

    openssl_private_encrypt($msg, $encrypted, $pi_key); //私钥加密
    $encrypted = base64_encode($encrypted); //加密后的内容通常含有特殊字符，需要编码转换下，在网络间通过url传输时要注意base64编码是否是url安全的
    $sign = $encrypted;


    $sign = sign($msg, $private_key); //签名函数
    $url = $sendurl;


    $data = $sendfile; //文件数据
    $data['msg'] = $msg;
    $data['sign'] = $sign;
    $data['code'] = $code;
    $content = curlPost($url, $data); //通过curl发送数据到接口
    $content = str_replace(array("\r\n", "\r", "\n", "\0"), "", $content); //替换掉特殊字符

    $arr = json_decode($content, 1);

    //获取接口返回的加密sign、code、msg
    $resign = $arr['sign'];
    $recode = $arr['code'];
    $remsg = $arr['msg'];

    openssl_private_decrypt(base64_decode($recode), $decrypted, $pi_key); //私钥解密
    $returnrandstr = $decrypted;
    $encryptedData = base64_decode($remsg);

    // 使用 openssl_decrypt 替换已废弃的 mcrypt_decrypt (PHP 7.4 兼容)
    $decrypted = openssl_decrypt($encryptedData, 'AES-128-CBC', $returnrandstr, OPENSSL_RAW_DATA, $returnrandstr);
    $returnjson = $decrypted;

    $returnjson = str_replace(array("\r\n", "\r", "\n", "\0"), "", $returnjson);

    $sign = sign($remsg, $private_key);

    if ($sign != $resign) {
        $returnjson = '[{"sta":"0","msg":"签名验证失败客户端1！"}]';
        return $returnjson;
    }

    return $returnjson;
}
/**
 * 把客户端发来的密文转化成明文
 * @param $resign
 * @param $recode
 * @param $remsg
 * @return mixed
 */
function  getparmtoplain($resign, $recode, $remsg)
{

    $private_key = '-----BEGIN RSA PRIVATE KEY-----
MIICXAIBAAKBgQDYucsb9ufvvGbdOv/kCMZ4oJfIz+eDJj6m2bjRzIM8P6sGXLFl
aXoJyYNbKi9ZCVTvYlE4i2RWlesSk63YX+1y4o5n9JjzbfkydZQy97FIaem/QvEP
Mfq3/A44PDykjtQRO11iIviFJlbR6bAYtbzem+dYS2cFZ+7XtGItS2ZxKwIDAQAB
AoGAF8iL2Y3G9jAeubh3zHaKEULOZWMrbOENjxHljmB7Jml2Q0R400A3/Er491Ax
0fuMLpkDgMkLQdVB7s/2DAC69eTR4vsnrQU0/eBikvEV2EEAPiuMQr2r/BYhEAyY
0R4edFjAG1sPCew/FqytSVMSWtxzGVOyB9CoAGI+4nBtCEECQQD4RAETQK9nZFhG
4TdLZv2WTdRcPjd6tLD3RtfQ7ldMtyhgO+WzNjj0zKoTbE3lp9WqDJonf5vTdfZr
S+1J80z/AkEA33o/sEV3G93tEewaAx1AncWFrRcjraMU5Y59Q2iUxWck+ASUPmj9
e4qfBIozBcVgH8YIyl6aLa4X4SzdN9uf1QJBAKdegTMPirCzT2gJcROeTRtFQQMm
1pQcuKkb02cBJ02KtOebudFFnsQ6LfaGL0XCDiNj95DlUJIQlp3jRAd0xNUCQE7l
WNDBZXCII+b85J4O2L4aerwF/EmT1o/Igz6mEEe0x19sNm3mIP9ZjBRNOgAik3IE
NxxC08mmubV8YkkTF1UCQBuLA9MAfqGcp9JOwoo9qPcBycLXfLpWrOcJcnweqMFC
A+lwOe0H9FBLT0N+2xRtLRX/XTqWlb7jOqJRCrbZmXow
-----END RSA PRIVATE KEY-----';


    $public_key = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDYucsb9ufvvGbdOv/kCMZ4oJfI
z+eDJj6m2bjRzIM8P6sGXLFlaXoJyYNbKi9ZCVTvYlE4i2RWlesSk63YX+1y4o5n
9JjzbfkydZQy97FIaem/QvEPMfq3/A44PDykjtQRO11iIviFJlbR6bAYtbzem+dY
S2cFZ+7XtGItS2ZxKwIDAQAB
-----END PUBLIC KEY-----';


    $private_key_pkcs8 = '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';


    $pi_key = openssl_pkey_get_private($private_key);//这个函数可用来判断私钥是否是可用的，可用返回资源id Resource id
    $pu_key = openssl_pkey_get_public($public_key);//这个函数可用来判断公钥是否是可用的

    //解密code
    openssl_private_decrypt(base64_decode($recode), $decrypted, $pi_key);//私钥解密
    $returnrandstr = $decrypted;

    //解密msg
    $encryptedData = base64_decode($remsg);
    $decrypted = mcrypt_decrypt(MCRYPT_RIJNDAEL_128, $returnrandstr, $encryptedData, MCRYPT_MODE_CBC, $returnrandstr);

    $msgyuanwen = str_replace(array("\r\n", "\r", "\n", "\0"), "", $decrypted);

    logRes("传入密文签名sign:" . $resign . "\r\n传入密文code:" . $recode . "\r\n传入密文msg:" . $remsg . "\r\n解析后的明文：" . $msgyuanwen,"lzt");

    $sign = sign($remsg, $private_key);
    $signrs = verity($remsg, $resign, $public_key);
    if (!$signrs) {
        $returnjson = '[{"sta":"0","msg":"签名验证失败服务端2！"}]';
        sendtolzt($returnjson);
        die;
    }

    return $msgyuanwen;
    openssl_public_decrypt(base64_decode($resign), $decrypted, $pu_key);//私钥加密的内容通过公钥可用解密出来
}




/**
 * 把通过业务逻辑转化后的明文数据加密后返还给客户端
 * @param $returnjson
 * @param string $logname
 */
function sendtolzt($returnjson, $logname = '')
{


    $private_key = '-----BEGIN RSA PRIVATE KEY-----
MIICXAIBAAKBgQDYucsb9ufvvGbdOv/kCMZ4oJfIz+eDJj6m2bjRzIM8P6sGXLFl
aXoJyYNbKi9ZCVTvYlE4i2RWlesSk63YX+1y4o5n9JjzbfkydZQy97FIaem/QvEP
Mfq3/A44PDykjtQRO11iIviFJlbR6bAYtbzem+dYS2cFZ+7XtGItS2ZxKwIDAQAB
AoGAF8iL2Y3G9jAeubh3zHaKEULOZWMrbOENjxHljmB7Jml2Q0R400A3/Er491Ax
0fuMLpkDgMkLQdVB7s/2DAC69eTR4vsnrQU0/eBikvEV2EEAPiuMQr2r/BYhEAyY
0R4edFjAG1sPCew/FqytSVMSWtxzGVOyB9CoAGI+4nBtCEECQQD4RAETQK9nZFhG
4TdLZv2WTdRcPjd6tLD3RtfQ7ldMtyhgO+WzNjj0zKoTbE3lp9WqDJonf5vTdfZr
S+1J80z/AkEA33o/sEV3G93tEewaAx1AncWFrRcjraMU5Y59Q2iUxWck+ASUPmj9
e4qfBIozBcVgH8YIyl6aLa4X4SzdN9uf1QJBAKdegTMPirCzT2gJcROeTRtFQQMm
1pQcuKkb02cBJ02KtOebudFFnsQ6LfaGL0XCDiNj95DlUJIQlp3jRAd0xNUCQE7l
WNDBZXCII+b85J4O2L4aerwF/EmT1o/Igz6mEEe0x19sNm3mIP9ZjBRNOgAik3IE
NxxC08mmubV8YkkTF1UCQBuLA9MAfqGcp9JOwoo9qPcBycLXfLpWrOcJcnweqMFC
A+lwOe0H9FBLT0N+2xRtLRX/XTqWlb7jOqJRCrbZmXow
-----END RSA PRIVATE KEY-----';


    $public_key = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDYucsb9ufvvGbdOv/kCMZ4oJfI
z+eDJj6m2bjRzIM8P6sGXLFlaXoJyYNbKi9ZCVTvYlE4i2RWlesSk63YX+1y4o5n
9JjzbfkydZQy97FIaem/QvEPMfq3/A44PDykjtQRO11iIviFJlbR6bAYtbzem+dY
S2cFZ+7XtGItS2ZxKwIDAQAB
-----END PUBLIC KEY-----';


    $private_key_pkcs8 = '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

    $arr = json_decode($returnjson, 1);
    $cleanarr = null_filter($arr); //把null转化成空字符串
    $returnjson = json_encode($cleanarr,JSON_UNESCAPED_UNICODE);


    $content = $returnjson;
    $randstr = random(16);
    $iv = $randstr;

    $encrypted = mcrypt_encrypt(MCRYPT_RIJNDAEL_128, $randstr, $content, MCRYPT_MODE_CBC, $iv);
    $msg = base64_encode($encrypted);

    $pu_key = openssl_pkey_get_public($public_key);//这个函数可用来判断公钥是否是可用的
    openssl_public_encrypt($randstr, $encrypted, $pu_key);//公钥加密
    $encrypted = base64_encode($encrypted);
    $code = $encrypted;

    $pi_key = openssl_pkey_get_private($private_key);//这个函数可用来判断私钥是否是可用的，可用返回资源id Resource id
    openssl_private_encrypt($msg, $encrypted, $pi_key);//私钥加密
    $encrypted = base64_encode($encrypted);//加密后的内容通常含有特殊字符，需要编码转换下，在网络间通过url传输时要注意base64编码是否是url安全的

    $sign = sign($msg, $private_key);

    $data['msg'] = $msg;
    $data['sign'] = $sign;
    $data['code'] = $code;

    logRes("返回给客户端的明文：" . $returnjson . "\r\n返回密文签名sign:" . $sign . "\r\n返回密文code:" . $code . "\r\n返回密文msg:" . $msg,"lzt");
    echo json_encode($data);
    die;
}


/**
 *签名数据：
 *data：utf-8编码的订单原文，
 *privatekeyFile：私钥路径
 *passphrase：私钥密码
 *返回：base64转码的签名数据
 */

function sign($data, $privatekey)
{
    $signature = '';
    $res = openssl_get_privatekey($privatekey);
    openssl_sign($data, $signature, $res);
    openssl_free_key($res);

    return base64_encode($signature);
}


/**
 * 验证签名：
 *data：原文
 *signature：签名
 *publicKeyPath：公钥路径
 *返回：签名结果，true为验签成功，false为验签失败
 */
function verity($data, $signature, $pubKey)
{
    $res = openssl_get_publickey($pubKey);
    $result = (bool)openssl_verify($data, base64_decode($signature), $res);
    openssl_free_key($res);
    return $result;
}


function null_filter($arr)
{
    foreach ($arr as $key => &$val) {
        if (is_array($val)) {
            $val = null_filter($val);
        } else {
            if ($val === null) {
                //unset($arr[$key]);
                $arr[$key] = '';
            }
        }
    }
    return $arr;
}


/**
 * 随机字符
 * @param number $length 长度
 * @param string $type 类型
 * @param number $convert 转换大小写
 * @return string
 */
function random($length = 6, $type = 'string', $convert = 0)
{
    $config = array(
        'number' => '1234567890',
        'letter' => 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
        'string' => 'abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ23456789',
        'all' => 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890'
    );

    if (!isset($config[$type])) $type = 'string';
    $string = $config[$type];

    $code = '';
    $strlen = strlen($string) - 1;
    for ($i = 0; $i < $length; $i++) {
        $code .= $string[mt_rand(0, $strlen)];
    }
    if (!empty($convert)) {
        $code = ($convert > 0) ? strtoupper($code) : strtolower($code);
    }
    return $code;
}


/*
* 后台密码加密
*/
function admin_encrypt($str)
{
    $key = "9C654249";
    $iv = "9C654249";
    //加密，返回大写十六进制字符串 - 使用 openssl 替换已废弃的 mcrypt (PHP 7.4 兼容)
    $blocksize = 8; // DES 块大小为 8 字节
    $str = pkcs5Pad($str, $blocksize);
    // 使用 openssl_encrypt 替换 mcrypt_encrypt
    $encrypted = openssl_encrypt($str, 'DES-CBC', $key, OPENSSL_RAW_DATA, $iv);
    return strtoupper(md5(strtoupper(bin2hex($encrypted))));
}
function pkcs5Pad($text, $blocksize)
{
    $pad = $blocksize - (strlen($text) % $blocksize);
    return $text . str_repeat(chr($pad), $pad);
}


/**
 * 创建表
 */
function  create_msyql_table_client_activity_grade_phone($activity_code)
{
    if ($activity_code == "") {
        return 0;
    }
    $sql = <<<sql
   CREATE TABLE `client_activity_grade_phone_$activity_code` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `phone` char(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
        `status` int(11) NULL DEFAULT NULL COMMENT '1开启，0关闭',
        `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
        `clientkeynum` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
        `o` int(11) NULL DEFAULT NULL COMMENT '排序号越小越靠前',
        `add_time` int(11) NULL DEFAULT NULL COMMENT '添加时间',
        `mod_time` int(11) NULL DEFAULT NULL COMMENT '修改时间',
        `project_id` int(11) NULL DEFAULT NULL COMMENT '项目id',
        `activity_id` int(11) NULL DEFAULT NULL COMMENT '活动的id',
        `batch_id` int(11) NULL DEFAULT NULL COMMENT '批次id',
        `grade_id` int(11) NULL DEFAULT NULL COMMENT '档次id',
        `order_sn` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单号，关联号',
        `order_keynum` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单的keynum',
        `order_id` int(11) NULL DEFAULT NULL COMMENT '订单的id',
        `is_order` int(11) NULL DEFAULT 0 COMMENT '1为生成订单，0未生成订单',
        `order_time` int(11) NULL DEFAULT NULL COMMENT '生成订单时间',
        PRIMARY KEY (`id`) USING BTREE,
        UNIQUE INDEX `phone`(`phone`) USING BTREE COMMENT '一个活动手机号唯一'
      ) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '平台客户的活动里档次里达标手机号，动态创建此表' ROW_FORMAT = Compact;
sql;
    try {
        Db::execute($sql);
        return 1;
    } catch (Exception $e) {
        return 0;
    }
}


/**
 * 模板日期格式化函数
 */
function date_format_tpl($strtime)
{
    if ($strtime == '') {
        return  "";
    }
    return  date("Y-m-d H:i:s", $strtime);
}


/**
 * 获得访客操作系统
 */
function get_os()
{
    if (!empty($_SERVER['HTTP_USER_AGENT'])) {
        $os = $_SERVER['HTTP_USER_AGENT'];
        if (preg_match('/win/i', $os)) {
            $os = 'Windows';
        } else if (preg_match('/mac/i', $os)) {
            $os = 'MAC';
        } else if (preg_match('/linux/i', $os)) {
            $os = 'Linux';
        } else if (preg_match('/unix/i', $os)) {
            $os = 'Unix';
        } else if (preg_match('/bsd/i', $os)) {
            $os = 'BSD';
        } else {
            $os = 'Other';
        }
        return $os;
    } else {
        return 'unknow';
    }
}

/**
 * 获得访问者浏览器
 */
function get_browser1()
{
    if (!empty($_SERVER['HTTP_USER_AGENT'])) {
        $br = $_SERVER['HTTP_USER_AGENT'];
        if (preg_match('/MSIE/i', $br)) {
            $br = 'MSIE';
        } else if (preg_match('/Firefox/i', $br)) {
            $br = 'Firefox';
        } else if (preg_match('/Chrome/i', $br)) {
            $br = 'Chrome';
        } else if (preg_match('/Safari/i', $br)) {
            $br = 'Safari';
        } else if (preg_match('/Opera/i', $br)) {
            $br = 'Opera';
        } else {
            $br = 'Other';
        }
        return $br;
    } else {
        return 'unknow';
    }
}



/**
 * 获取登录地址
 */
function get_location($ip)
{
    return null;
    $str = "http://ip2region.wlphp.com/api.php?ip=" . $ip;
    $rs = file_get_contents($str);
    $arr = json_decode($rs, 1);
    return $arr['region'];
}


/**
 * 切除过长的字符串，用...代替
 * @param $string       字符串
 * @param $sublen       设置长度
 * @param int $start 开始位置
 * @param string $code
 * @return string       返回字符串结果
 */
function cut_str($string, $sublen, $start = 0, $code = 'UTF-8')
{
    if ($code == 'UTF-8') {
        $pa = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|\xe0[\xa0-\xbf][\x80-\xbf]|[\xe1-\xef][\x80-\xbf][\x80-\xbf]|\xf0[\x90-\xbf][\x80-\xbf][\x80-\xbf]|[\xf1-\xf7][\x80-\xbf][\x80-\xbf][\x80-\xbf]/";
        preg_match_all($pa, $string, $t_string);
        if (count($t_string[0]) - $start > $sublen) {
            return join('', array_slice($t_string[0], $start, $sublen)) . "...";
        }

        return join('', array_slice($t_string[0], $start, $sublen));
    } else {
        $start = $start * 2;
        $sublen = $sublen * 2;
        $strlen = strlen($string);
        $tmpstr = '';
        for ($i = 0; $i < $strlen; $i++) {
            if ($i >= $start && $i < ($start + $sublen)) {
                if (ord(substr($string, $i, 1)) > 129) {
                    $tmpstr .= substr($string, $i, 2);
                } else {
                    $tmpstr .= substr($string, $i, 1);
                }
            }
            if (ord(substr($string, $i, 1)) > 129) {
                $i++;
            }
        }
        if (strlen($tmpstr) < $strlen) {
            $tmpstr .= "...";
        }

        return $tmpstr;
    }
}



//有时候一些项目运行在apache或者php-fpm环境，无法使用workerman/redis-queue项目，可以参考如下函数实现发送
function redis_queue_send($redis, $queue, $data, $delay = 0)
{
    $queue_waiting = 'redis-queue-waiting';
    $queue_delay = 'redis-queue-delayed';
    $now = time();
    $package_str = json_encode([
        'id'       => rand(),
        'time'     => $now,
        'delay'    => 0,
        'attempts' => 0,
        'queue'    => $queue,
        'data'     => $data
    ]);
    if ($delay) {
        return $redis->zAdd($queue_delay, $now + $delay, $package_str);
    }
    return $redis->lPush($queue_waiting . $queue, $package_str);
}


function strip_html_tags($tags, $str)
{
    $html = array();
    foreach ($tags as $tag) {
        $html[] = '/<' . $tag . '.*?>[\s|\S]*?<\/' . $tag . '>/';
        $html[] = '/<' . $tag . '.*?>/';
    }
    $data = preg_replace($html, '', $str);
    return $data;
}

//记录接口日志函数
function do_api_log($request, $return_arr, $applicationinfo)
{
    $data['appid'] = $applicationinfo['appid'];
    $data['action'] = $request['action'];
    $data['get_data'] = json_encode($request, JSON_UNESCAPED_UNICODE);
    $data['send_data'] = json_encode($return_arr, JSON_UNESCAPED_UNICODE);
    $data['application'] = json_encode($applicationinfo, JSON_UNESCAPED_UNICODE);
    $data['add_time'] = date("Y-m-d H:i:s");
    $data['addtime'] = time();
    Db::table('client_api_log')->insert($data);
}

function ycard_status($status)
{
    // 生成卡号是5 铺卡后是0 开卡后是1 关卡是2 使用是3 退款是6
    $str = "";
    switch ($status) {
        case 1:
            $str = '已销售';
            break;
        case 2:
            $str = '已退卡';
            break;
        case 3:
            $str = '已开卡';
            break;
        case 4:
            $str = '已关卡';
            break;
        case -1:
            $str = '已废卡';
            break;
        case 5:
            $str = '未销售';
            break;
        default:
            $str = "出错了";
            break;
    }
    return $str;
}

/**
 * [生成随机字符串]
 * @E-mial <EMAIL>
 * @TIME   2017-04-07
 * @WEB    http://blog.iinu.com.cn
 * @param  integer $length [生成的长度]
 * @param  integer $type   [生成的类型]
 * @return [type]   str       [description]
 * @php 随机码类型：0，数字+大写字母；1，数字；2，小写字母；3，大写字母；4，特殊字符；-1，数字+大小写字母+特殊字符
 */
function randCode($length = 5, $type = 0)
{
    $code = "";
    $arr = array(1 => "0123456789", 2 => "abcdefghijklmnopqrstuvwxyz", 3 => "ABCDEFGHIJKLMNOPQRSTUVWXYZ");
    if ($type == 0) {
        array_pop($arr);
        $string = implode("", $arr);
    } else if ($type == "-1") {
        $string = implode("", $arr);
    } else {
        $string = $arr[$type];
    }
    $count = strlen($string) - 1;
    for ($i = 0; $i < $length; $i++) {
        $str[$i] = $string[rand(0, $count)];
        $code .= $str[$i];
    }
    return $code;
}

///二维码
function createImg($url = '')
{

    vendor('phpqrcode.phpqrcode'); //引入类库tp5
    //        require "../vendor/phpqccode/phpqrcode.php";//tp6引入类库方法

    $value = $url;         //二维码内容
    $errorCorrectionLevel = 'L';  //容错级别
    $matrixPointSize = 5;      //生成图片大小
    //生成二维码图片
    // 判断是否有这个文件夹  没有的话就创建一个
    if (!is_dir("qrcode")) {
        // 创建文件加
        mkdir("qrcode");
    }
    //设置二维码文件名
    $filename = './qrcode/' . time() . rand(10000, 9999999) . '.png';
    //生成二维码
    \QRcode::png($value, $filename, $errorCorrectionLevel, $matrixPointSize, 2);
    return $filename;
}

/*生成小程序二维码*/
function create_wx_qrcode($code, $cardnumber)
{
    //平台客户的keynum
    $clientkeynum = session("cn_accountinfo.basekeynum");
    $wx_config = Db::table("plat_weixin_set")->where("basekeynum='$clientkeynum'")->find();
    // 先获取access_token
    $url = "https://api.weixin.qq.com/cgi-bin/token?appid=" . $wx_config["appid"] .
        "&secret=" . $wx_config["appsecret"] . "&grant_type=client_credential";
    $rs = file_get_contents($url);
    $rs = json_decode($rs, 1);
    $access_token = $rs["access_token"];
    $url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" . $access_token;
    $data["scene"] = "code=" . $code;
    $data["page"] = "pages/home/<USER>";
    // $data["env_version"] = "trial";
    $data["is_hyaline"] = true;
    $data = json_encode($data);
    $rs = curlPost($url, $data);
    $filename = $cardnumber; //create_guid();
    $img_name = $filename . ".png";
    $dir = "./qrcode" . "/" . $clientkeynum . "/";
    if (!is_dir($dir)) {
        mkdir($dir, 0777, true);
    }
    if ($fp = fopen($dir . $img_name, 'w')) {
        if (fwrite($fp, $rs)) {
            fclose($fp);
        }
    }
    $arr = uploadFileToAliOss($dir . $img_name, "qrcode/" . $clientkeynum . "/" . $img_name);
    if ($arr['sta'] != '1') {
        return  $arr['msg'];
        die;
    }
    unlink($dir . $img_name);
    return  $arr['url'];
    die;
    // $new_dir = "/" . "qrcode" . "/" . $clientkeynum . "/" . $img_name;
    // return $new_dir;
}

function send_to_wuliu($appid, $appkey, $body, $url)
{
    $arr['appId'] = $appid;
    if (!empty($body)) {
        $arr['param'] = $body;
        $param_str = json_encode($body, JSON_UNESCAPED_UNICODE);
        $param_str = str_replace("\\", "", $param_str);
    }
    $arr['nonceStr'] = createNoncestr_wl();
    $arr['timeStamp'] = time();
    $String = $arr['appId'] . $param_str . $arr['nonceStr'] . $arr['timeStamp'] . $appkey;
    // echo $String;die;
    //签名步骤三：MD5加密
    $String = md5($String);
    //签名步骤四：所有字符转为大写
    $sign = strtoupper($String);
    $arr['sign'] = $sign;
    $arr = json_encode($arr, JSON_UNESCAPED_UNICODE);
    // print_r($arr);die;
    $result = curlPostJson($url, $arr);
    logRes("对接增值平台;发送地址:" . $url . "发送内容:" . $arr . "返回结果:" . $result);
    return $result;
}

/**
 * @param $url 发送post请求的url
 * @param $jsonStr 发送的数据
 * @return mixed
 */
function curlPostJson($url, $jsonStr)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt(
        $ch,
        CURLOPT_HTTPHEADER,
        array(
            'Content-Type: application/json; charset=utf-8',
            'Content-Length: ' . strlen($jsonStr)
        )
    );
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    return $response;
    // return array($httpCode, $response);
}


// 订阅物流轨迹
function wuliu_dingyue($orderid, $basekeynum)
{
    $config = db("wuliu_config")->where("clientkeynum='$basekeynum'")->find();
    if ($config["status"] != '1') {
        logRes("订阅失败,未开启订阅开关");
        return;
    }
    if (empty($config["join_url"]) or empty($config["appid"]) or empty($config["appkey"])) {
        logRes("订阅失败,配置项为空");
        return;
    }
    $orderinfo = db("client_order_orderdetail")
        ->where("id='$orderid' and clientkeynum='$basekeynum' and status='3'")->field("shipping_name,shipping_num,clientkeynum,order_sn")->find();
    $phone = db("client_order_info")
        ->where(['order_sn' => $orderinfo['order_sn'], 'clientkeynum' => $basekeynum])
        ->value("phone");

    if (empty($orderinfo) or empty($orderinfo["shipping_name"]) or empty($orderinfo["shipping_num"])) {
        logRes("订阅失败,订单信息为空");
        return;
    }
    $body = array();
    $add = array();

//    foreach ($orderinfo as $key => $value) {
    $shipping_name = $orderinfo["shipping_name"];
    $shipping_num = $orderinfo["shipping_num"];
    $find = db("plat_order_wuliu")
        ->where("orderid='$orderid' and express='$shipping_name' and expressnum='$shipping_num' and status not in (0,99)")->value("id");

    if ($find) {
        return;
    }
    $express_code = "暂无";
    // 判断如果快递名称等于未知 调取接口获取快递
    $add_one = array();
    // 判断快递单号是否有重复的，可能多个订单是同一个快递单号，不重复再进行订阅，否则只插入本系统记录
    $find2 = db("plat_order_wuliu")->where("express='$shipping_name' and expressnum='$shipping_num' and status not in (0,99)")->value("id");
    if (!$find2) {
        $body_one["com"] = $express_code;
        $body_one["num"] = $orderinfo;
        $body_one["callBackUrl"] = "http://" . $_SERVER["HTTP_HOST"] . "/admin.php/cnapi/wuliu_callback";
        $body_one["recPhone"] = $phone;
        $body[] = $body_one;
    }
    $add_one["orderid"] = $orderid;
    $add_one["express"] = $shipping_name;
    $add_one["express_code"] = $express_code;
    $add_one["expressnum"] = $orderinfo;
    $add_one["status"] = '0';
    $add_one["factorykeynum"] = $basekeynum;
    $add_one["clientkeynum"] = $orderinfo["clientkeynum"];
    $add_one["create_time"] = time();
    $add[] = $add_one;
//    }
    if (!empty($body)) {
        $action = "/prod-api/openapi/express/subscriberecord/moredata";
        $appid = $config["appid"];
        $appkey = $config["appkey"];
        $url = $config["join_url"] . $action;
        $rs = send_to_wuliu($appid, $appkey, $body, $url);
        $rs_arr = json_decode($rs, 1);
    } else {
        $rs_arr["code"] = '200';
        $rs = json_encode($rs_arr);
    }
    // 本地插入记录
    foreach ($add as $ak => &$av) {
        $add[$ak]["log"] = $rs;
        if ($rs_arr["code"] == '200') {
            $add[$ak]["status"] = '1';
        }
    }
    $save["status"] = 99;
    db("plat_order_wuliu")->where("orderid='$orderid' and status='0'")->update($save);
    db("plat_order_wuliu")->insertAll($add);
}


function zip_download($files)
{
    $picAllArr = $files;

    $tmpDir = 'zip/'; // 类似于/www/public/upload/

    if (!file_exists($tmpDir)) {
        //创建文件夹
        mkdir($tmpDir, 0777, true);
    }

    $zipName = date('His') . mt_rand(1000, 9999) . '.zip'; // 压缩包文件名
    $zipNameUrl = $tmpDir . $zipName; // 文件路径

    // 生成文件
    $zip = new \ZipArchive();
    if ($zip->open($zipNameUrl, \ZipArchive::OVERWRITE) !== true) {
        //OVERWRITE 参数会覆写压缩包的文件 文件必须已经存在
        if ($zip->open($zipNameUrl, \ZipArchive::CREATE) !== true) {
            // 文件不存在则生成一个新的文件 用CREATE打开文件会追加内容至zip
            return  '下载失败，文件夹不存在';
        }
    }

    foreach ($picAllArr as $file) {
        //抓取图片内容
        $fileContent = file_get_contents($file);
        //添加图片
        $zip->addFromString(basename($file), $fileContent);
    }
    // 关闭
    $zip->close();

    //没有文件
    if (!file_exists($zipNameUrl)) {
        return '下载失败，图片不存在或无法下载';
    }

    header("Cache-Control: public");
    header("Content-Description: File Transfer");
    header('Content-disposition: attachment; filename=' . $zipName); //文件名
    header("Content-Type: application/zip"); //zip格式的
    header("Content-Transfer-Encoding: binary"); //告诉浏览器，这是二进制文件
    header('Content-Length: ' . filesize($zipNameUrl)); //告诉浏览器，文件大小

    // 下面2步必须要
    ob_clean();
    flush();

    @readfile($zipNameUrl);
    unlink($zipNameUrl); // 删除文件
    exit;
}


function create_sku()
{

    $charid = strtoupper(md5(uniqid(mt_rand(), true)));
    //$hyphen = chr(45);// "-"
    $hyphen = "";
    $uuid = substr($charid, 6, 2) . substr($charid, 4, 2) .
        substr($charid, 2, 2) . substr($charid, 0, 2) . $hyphen;
    return $uuid;
}
