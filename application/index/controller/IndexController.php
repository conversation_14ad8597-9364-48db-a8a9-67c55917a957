<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright ( c ) 2020 04 07 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\index\controller;

use think\Controller;
use think\Db;
use think\facade\Env;
use think\Request;
use think\facade\Session;
use think\facade\Cache;
use app\store\model\ShopProductInventory;
use app\store\model\OrderDetail;
use think\facade\Log;
use app\store\model\InventoryOrder;
use app\store\model\InventoryOrderDetail;
use app\store\lib\Response;
use app\api\model\OrderLog;

class IndexController extends CnController
{
    public function index()
    {
//        $this->redirect(Env::get('APP_DOMAIN') . '/admin.php/index/index.html');
    }


        /**
     * 订单重新入库操作（公开接口，无需登录验证）
     * 
     * @param string $order_no 订单号
     * @param string $clientkeynum 客户端密钥
     * @return \think\response\Json
     */
    public function restock(Request $request)
    {
        $order_no = $request->param('order_no');
        $clientkeynum = $request->param('clientkeynum');
        try {
            // 验证参数
            if (empty($order_no)) {
                return Response::json(Response::ERROR, '订单号不能为空');
            }
            
            if (empty($clientkeynum)) {
                return Response::json(Response::ERROR, '客户端密钥不能为空');
            }
            
            // 验证订单是否存在
            $order = \app\store\model\Order::where([
                'order_no' => $order_no,
                'clientkeynum' => $clientkeynum
            ])->find();
            
            if (empty($order)) {
                return Response::json(Response::ERROR, '订单不存在');
            }
            
            // 获取订单详情
            $order_details = OrderDetail::where([
                'order_no' => $order_no,
                'clientkeynum' => $clientkeynum
            ])->select();
            
            if (empty($order_details)) {
                return Response::json(Response::ERROR, '订单详情不存在');
            }
            
            // 准备入库单明细数据
            $inventory_details = [];
            
            foreach ($order_details as $detail) {
                if (is_string($detail['product_json'])) {
                    $product_data = json_decode($detail['product_json'], true);
                } else {
                    $product_data = $detail['product_json'];
                }
                
                if (is_string($detail['inventory_json'])) {
                    $inventory_data = json_decode($detail['inventory_json'], true);
                } else {
                    $inventory_data = $detail['inventory_json'];
                }
                
                if (empty($product_data) || empty($inventory_data)) {
                    Log::error("订单详情数据异常：order_no={$order_no}, detail_id={$detail['id']}");
                    continue;
                }
                
                // 获取库存记录
                $shop_inventory = ShopProductInventory::where([
                    'shop_id' => $detail['shop_id'],
                    'product_id' => $product_data['id'],
                    'inventory_id' => $inventory_data['id']
                ])->find();
                
                if (empty($shop_inventory)) {
                    Log::error("库存记录不存在：shop_id={$detail['shop_id']}, product_id={$product_data['id']}, inventory_id={$inventory_data['id']}");
                    continue;
                }
                
                // 检查是否为计量商品（通过订单详情中的actual_weight字段判断）
                if (!empty($detail['actual_weight'])) {
                    // 计量商品：恢复重量库存
                    $before_quantity = $shop_inventory->weight_stock;
                    $change_quantity = $detail['actual_weight'];
                    $after_quantity = $before_quantity + $change_quantity;
                    
                    // $shop_inventory->addWeightStock($detail['actual_weight'], '订单重新入库');
                    
                    // 记录库存变动日志
                    // Db::name('inventory_log')->insert([
                    //     'clientkeynum' => $clientkeynum,
                    //     'shop_id' => $detail['shop_id'],
                    //     'product_id' => $product_data['id'],
                    //     'inventory_id' => $inventory_data['id'],
                    //     'before_quantity' => $before_quantity,
                    //     'change_quantity' => $change_quantity,
                    //     'after_quantity' => $after_quantity,
                    //     'change_type' => 6, // 6-退货
                    //     'related_no' => $order_no,
                    //     'operator' => '系统自动入库',
                    //     'remark' => "订单重新入库：{$product_data['title']}，重量：{$detail['actual_weight']}kg",
                    //     'created_at' => date('Y-m-d H:i:s')
                    // ]);
                    
                    Log::info("恢复重量库存：订单{$order_no}，商品{$product_data['title']}，重量{$detail['actual_weight']}kg");
                    
                    // 准备入库单明细数据（计量商品）
                    $inventory_details[] = [
                        'product_id' => $product_data['id'],
                        'inventory_id' => $inventory_data['id'],
                        'quantity' => $detail['actual_weight'], // 计量商品使用重量作为数量
                        'price' => $detail['weight_unit_price'] ?? 0, // 使用单位价格
                        'discount' => 0,
                        'amount' => $detail['price'], // 使用订单中的总价
                        'product' => $product_data,
                        'inventory' => $inventory_data,
                        'is_weight_product' => true
                    ];
                } else {
                    // 普通商品：恢复数量库存
                    $before_quantity = $shop_inventory->stock;
                    $change_quantity = $detail['number'];
                    $after_quantity = $before_quantity + $change_quantity;
                    
                    // ShopProductInventory::where([
                    //     'shop_id' => $detail['shop_id'],
                    //     'product_id' => $product_data['id'],
                    //     'inventory_id' => $inventory_data['id']
                    // ])->update([
                    //     'stock' => Db::raw('stock + ' . $detail['number'])
                    // ]);
                    
                    // // 记录库存变动日志
                    // Db::name('inventory_log')->insert([
                    //     'clientkeynum' => $clientkeynum,
                    //     'shop_id' => $detail['shop_id'],
                    //     'product_id' => $product_data['id'],
                    //     'inventory_id' => $inventory_data['id'],
                    //     'before_quantity' => $before_quantity,
                    //     'change_quantity' => $change_quantity,
                    //     'after_quantity' => $after_quantity,
                    //     'change_type' => 6, // 6-退货
                    //     'related_no' => $order_no,
                    //     'operator' => '系统自动入库',
                    //     'remark' => "订单重新入库：{$product_data['title']} - {$inventory_data['title']}，数量：{$detail['number']}",
                    //     'created_at' => date('Y-m-d H:i:s')
                    // ]);
                    
                    Log::info("恢复数量库存：订单{$order_no}，商品{$product_data['title']} - {$inventory_data['title']}，数量{$detail['number']}");
                    
                    // 准备入库单明细数据（普通商品）
                    $inventory_details[] = [
                        'product_id' => $product_data['id'],
                        'inventory_id' => $inventory_data['id'],
                        'quantity' => $detail['number'],
                        'price' => $detail['price'] ?? 0, // 使用订单中的单价
                        'discount' => 0,
                        'amount' => $detail['amount'] ?? ($detail['price'] * $detail['number']), // 使用订单中的金额
                        'product' => $product_data,
                        'inventory' => $inventory_data,
                        'is_weight_product' => false
                    ];
                }
            }
            
            // 创建入库单
            if (!empty($inventory_details)) {
                // 创建入库单主表数据
                $inventoryOrderData = [
                    'order_no' => 'II' . date('YmdHis') . mt_rand(1000, 9999), // 自动生成入库单号
                    'clientkeynum' => $clientkeynum,
                    'shop_id' => $order['shop_id'],
                    'order_type' => 1, // 1-入库
                    'business_type' => 4, // 4-其他入库
                    'related_order_no' => $order_no, // 关联订单号
                    'status' => 3, // 已完成状态
                    'remark' => "订单重新入库，订单号：{$order_no}",
                    'created_by' => '系统自动入库',
                    'reviewed_by' => '系统自动入库',
                    'reviewed_time' => date('Y-m-d H:i:s'),
                    'completed_time' => date('Y-m-d H:i:s'),
                    'created_at' => date('Y-m-d H:i:s'),
                ];

                try {
                    // 创建入库单主表记录
                    $inventoryOrder = new \app\store\model\InventoryOrder();
                    $inventoryOrderResult = $inventoryOrder->save($inventoryOrderData);

                    if (!$inventoryOrderResult) {
                        throw new \Exception('创建入库单失败');
                    }

                    $total_amount = 0;
                    $details = [];

                    // 准备所有入库单明细数据
                    foreach ($inventory_details as $detail) {
                        $product = $detail['product'];
                        $inventory = $detail['inventory'];
                        $is_weight_product = $detail['is_weight_product'] ?? false;

                        $detail_data = [
                            'order_id' => $inventoryOrder->id,
                            'order_no' => $inventoryOrderData['order_no'],
                            'order_type' => 1, // 1-入库
                            'product_id' => $detail['product_id'],
                            'inventory_id' => $detail['inventory_id'],
                            'quantity' => $detail['quantity'],
                            'price' => $detail['price'],
                            'discount' => $detail['discount'],
                            'amount' => $detail['amount'],
                            'remark' => $is_weight_product 
                                ? "计量商品：{$product['title']}，重量：{$detail['quantity']}kg" 
                                : "普通商品：{$product['title']} - {$inventory['title']}，数量：{$detail['quantity']}",
                            'created_at' => date('Y-m-d H:i:s'),
                            'shop_id' => $order['shop_id'],
                            'clientkeynum' => $clientkeynum,
                        ];

                        $details[] = $detail_data;
                        $total_amount += $detail['amount'];
                    }

                    // 批量创建入库单明细
                    $detailModel = new \app\store\model\InventoryOrderDetail();
                    $detailResult = $detailModel->saveAll($details);

                    if (!$detailResult) {
                        throw new \Exception('创建入库单明细失败');
                    }

                    // 更新入库单主表的总金额
                    $inventoryOrder->total_amount = $total_amount;
                    $inventoryOrder->save();

                    // 记录订单日志
                    $this->createOrderLog($order_no, '系统自动将订单重新入库', $clientkeynum, '系统自动入库');

                    Log::info("订单重新入库成功：入库单号{$inventoryOrderData['order_no']}，关联订单{$order_no}，总金额{$total_amount}");
                    
                    return Response::json(Response::SUCCESS, '订单重新入库成功', [
                        'inventory_order_no' => $inventoryOrderData['order_no'],
                        'total_amount' => $total_amount,
                        'processed_items' => count($inventory_details)
                    ]);

                } catch (\Exception $e) {
                    Log::error('创建订单入库单失败：' . $e->getMessage());
                    Log::error($e->getTraceAsString());
                    return Response::json(Response::ERROR, '创建入库单失败：' . $e->getMessage());
                }
            } else {
                return Response::json(Response::ERROR, '没有可入库的商品');
            }
        } catch (\Exception $e) {
            Log::error('订单重新入库异常：' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return Response::json(Response::ERROR, '订单重新入库失败：' . $e->getMessage());
        }
    }

        /**
     * 创建订单日志
     *
     * @param string $order_no 订单号
     * @param string $content 日志内容
     * @param string $clientkeynum 客户端密钥
     * @param string $operator 操作员
     */
    private function createOrderLog($order_no, $content, $clientkeynum, $operator = '系统')
    {
        OrderLog::create([
            'order_no' => $order_no,
            'clientkeynum' => $clientkeynum,
            'content' => $content,
            'operator' => $operator,
            'add_time' => date('Y-m-d H:i:s')
        ]);
    }

}
