<html>
	<head>
	<title>立即支付</title>
	<meta  charset="utf-8">
	<meta name="viewport" content="width=device-width,height=device-height, user-scalable=no,initial-scale=1, minimum-scale=1, maximum-scale=1,target-densitydpi=device-dpi ">  
    </head>
<body>
<script>
function onBridgeReady(){
   WeixinJSBridge.invoke(
       'getBrandWCPayRequest', 
	    {$jsApiParameters},
       function(res){     
					WeixinJSBridge.log(res.err_msg);
					//alert(res.err_code+res.err_desc+res.err_msg);
				    //取消订单或者支付失败
					if(res.err_msg=="get_brand_wcpay_request:cancel"){
						 window.location.href='{$return_url}&success=0';
						 return false;	 						 
					}
                    //支付成功
					if(res.err_msg=="get_brand_wcpay_request:ok"){
					     window.location.href='{$return_url}&success=1';
                         return false;						 
					}	      
       }
   ); 
}


if (typeof WeixinJSBridge == "undefined"){
   if( document.addEventListener ){
       document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
   }else if (document.attachEvent){
       document.attachEvent('WeixinJSBridgeReady', onBridgeReady); 
       document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
   }
}else{
   onBridgeReady();
} 
</script>
</body>
</html>