<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

return [
    'app\admin\command\OrderCancel', // 定时任务取消订单
    'app\admin\command\OrderProductInfoFix',
    'app\api\command\RefreshQrcode', // 定时刷新过期二维码
    'app\api\command\RecalculateInventory', // 重新计算库存信息
    'app\api\command\SyncShopProductInventory', // 同步门店商品库存数据
    'app\api\command\InventoryVerify', // 验证库存数据正确性
    'app\api\command\InventoryFix', // 修复库存数据问题
    'app\api\command\RebuildInventoryLog', // 重建库存日志数据
    'app\api\command\OrderToInventory', // 订单转库存单命令
    'app\api\command\ResetInventory', // 重置库存并重新处理所有出入库单和已完成订单
    'app\api\command\OrderInventoryCheck',
    'app\api\command\CheckOrderInventory', // 检查状态为100的订单是否有对应出入库单
    'app\api\command\VerifyOrderInventory', // 验证订单详情和出库单详情的商品数量是否一致
    'app\api\command\CheckCancelledOrderInventory', // 检查状态为-1的已取消订单是否有异常的出入库记录
    'app\api\command\CheckDuplicateInventoryOrder', // 检查订单状态为100的订单是否存在重复出库记录且没有对应入库
    'app\api\command\UpdateOrderProductTotal', // 更新订单的商品总数到order表的product_total字段
    'app\api\command\CheckOrderPriceConsistency', // 检查订单状态为100的订单详情金额和订单总金额是否一致
    'app\api\command\CheckOrderProductTotal', // 检查订单的product_total与实际商品数量是否一致
];
