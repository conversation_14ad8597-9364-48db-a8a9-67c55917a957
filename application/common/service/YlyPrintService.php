<?php

namespace app\common\service;

use app\api\model\YlyPlatformConfig;
use app\store\model\YlyShopPrinterConfig;
use app\admin\model\YlyPrintLog;
use think\Exception;
use think\facade\Log;

// 包含易联云SDK自动加载器
require_once __DIR__ . '/../../../vendor/yly-openapi/yly-openapi-sdk/Lib/Autoloader.php';

use App\Config\YlyConfig;
use App\Oauth\YlyOauthClient;
use App\Api\PrintService;

/**
 * 易联云打印服务类（使用官方SDK）
 * Class YlyPrintService
 * @package app\common\service
 */
class YlyPrintService
{
    private $clientkeynum;
    private $platformConfig;
    private $config;
    private $accessToken;
    
    // 易联云API地址
    const API_BASE_URL = 'https://open-api.10ss.net/v2';
    
    public function __construct($clientkeynum)
    {
        $this->clientkeynum = $clientkeynum;
        $this->platformConfig = YlyPlatformConfig::getByClientkey($clientkeynum);
        
        if (!$this->platformConfig) {
            throw new Exception('未找到易联云平台配置');
        }
        
        $this->initConfig();
        $this->accessToken = $this->getValidToken();
    }
    
    /**
     * 初始化SDK配置
     */
    private function initConfig()
    {
        $this->config = new YlyConfig(
            $this->platformConfig['app_id'], 
            $this->platformConfig['app_secret']
        );
        
        // 设置v2.0接口域名
        $this->config->setRequestUrl('https://open-api.10ss.net/v2');
    }
    
    /**
     * 获取有效的Token
     * @return string
     * @throws Exception
     */
    private function getValidToken()
    {
        // 检查当前Token是否有效
        if (YlyPlatformConfig::isTokenValid($this->platformConfig)) {
            return $this->platformConfig['access_token'];
        }
        
        // Token无效，需要重新获取
        return $this->refreshToken();
    }
    
    /**
     * 刷新或获取新Token
     * @return string
     * @throws Exception
     */
    private function refreshToken()
    {
        // 检查今日获取次数限制
        if (YlyPlatformConfig::isTokenLimitExceeded($this->platformConfig)) {
            throw new Exception('今日Token获取次数已达上限(20次)，请明日再试');
        }
        
        try {
            $oauthClient = new YlyOauthClient($this->config);
            $tokenData = $oauthClient->getToken();
            
            if (!$tokenData || !isset($tokenData->access_token)) {
                throw new Exception('获取Token失败');
            }
            
            // 更新数据库中的Token
            YlyPlatformConfig::updateToken(
                $this->platformConfig['id'],
                $tokenData->access_token,
                $tokenData->refresh_token ?? ''
            );
            
            return $tokenData->access_token;
            
        } catch (\Exception $e) {
            throw new Exception('获取Token失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 打印订单小票
     * @param int $shopId
     * @param string $orderNo
     * @param array $orderData
     * @return array
     * @throws Exception
     */
    public function printOrderTicket($shopId, $orderNo, $orderData)
    {
        // 获取门店打印机配置
        $printerConfig = YlyShopPrinterConfig::getByShop($shopId, $this->clientkeynum);
        if (!$printerConfig) {
            throw new Exception('门店未配置打印机');
        }
        
        // 生成打印内容
        $content = $this->generateOrderTicketContent($orderData);
        
        
        try {

            // 记录打印日志
            $logId = $this->createPrintLog($shopId, $orderNo, $printerConfig['printer_sn'], $content);
        
            // 使用官方SDK执行打印
            $printService = new PrintService($this->accessToken, $this->config);
            $result = $printService->index(
                $printerConfig['printer_sn'], 
                $content, 
                $orderNo
            );
            
            // SDK返回的是标准对象，需要检查结果
            if (!$result) {
                throw new Exception('打印接口调用失败');
            }
            
            // 更新打印日志为成功
            YlyPrintLog::updateStatus($logId, YlyPrintLog::STATUS_SUCCESS, json_encode($result));
            
            // 更新打印机统计
            YlyShopPrinterConfig::updatePrintCount($shopId, $this->clientkeynum);
            
            return [
                'success' => true,
                'message' => '打印成功',
                'data' => $result
            ];
            
        } catch (Exception $e) {
            Log::error('打印失败: ' . $e->getMessage());
            // 打印失败，记录错误日志
            YlyPrintLog::updateStatus($logId, YlyPrintLog::STATUS_FAILED, '', $e->getMessage());
            
            return [
                'success' => false,
                'message' => '打印失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 测试打印
     * @param int $shopId
     * @return array
     * @throws Exception
     */
    public function testPrint($shopId)
    {
        $printerConfig = YlyShopPrinterConfig::getByShop($shopId, $this->clientkeynum);
        if (!$printerConfig) {
            throw new Exception('门店未配置打印机');
        }
        
        $content = $this->generateTestContent();
        $orderNo = 'TEST_' . date('YmdHis');
        
        // 记录测试打印日志
        $logId = $this->createPrintLog($shopId, $orderNo, $printerConfig['printer_sn'], $content, 'test');
        
        try {
            // 使用官方SDK执行测试打印
            $printService = new PrintService($this->accessToken, $this->config);
            $result = $printService->index(
                $printerConfig['printer_sn'], 
                $content, 
                $orderNo
            );
            
            // SDK返回的是标准对象，需要检查结果
            if (!$result) {
                throw new Exception('测试打印接口调用失败');
            }
            
            YlyPrintLog::updateStatus($logId, YlyPrintLog::STATUS_SUCCESS, json_encode($result));
            
            return [
                'success' => true,
                'message' => '测试打印成功',
                'data' => $result
            ];
            
        } catch (Exception $e) {
            YlyPrintLog::updateStatus($logId, YlyPrintLog::STATUS_FAILED, '', $e->getMessage());
            
            return [
                'success' => false,
                'message' => '测试打印失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 生成订单小票内容
     * @param array $orderData
     * @return string
     */
    private function generateOrderTicketContent($orderData)
    {
        Log::info('生成订单小票内容', $orderData);
        $content = "<MN>2</MN>\n";
        
        // 拣货单号显示在最上面并居中（加大字体）
        if (!empty($orderData['pickup_number'])) {
            $content .= "<center><FS2>拣货单号：" . $orderData['pickup_number'] . "</FS2></center>\n";
            $content .= "\n";
        }
        
        $content .= "" . ($orderData['shop_name'] ?? '商店') . "\n";
        $content .= "--------------------------------\n";
        $content .= "订单号：" . $orderData['order_no'] . "\n";
        $content .= "下单时间：" . ($orderData['created_at'] ?? date('Y-m-d H:i:s')) . "\n";
        
        // 订单类型
        if (!empty($orderData['order_type'])) {
            $orderTypeText = $this->getOrderTypeText($orderData['order_type']);
            $content .= "订单类型：" . $orderTypeText . "\n";
        }
        
        if (!empty($orderData['customer_name'])) {
            $content .= "客户：" . $orderData['customer_name'] . "\n";
        }
        
        if (!empty($orderData['customer_phone'])) {
            // 处理手机号 中间的 * 号
            $content .= "电话：" . substr_replace($orderData['customer_phone'], '****', 3, 4) . "\n";
        }
        
        if (!empty($orderData['delivery_address'])) {
            $content .= "地址：" . $orderData['delivery_address'] . "\n";
        }
        
        if (!empty($orderData['delivery_time'])) {
            $content .= "配送时间：" . $orderData['delivery_time'] . "\n";
        }
        
        $content .= "--------------------------------\n";
        
        // 商品明细
        if (!empty($orderData['products'])) {
            foreach ($orderData['products'] as $product) {
                $content .= $product['product_name'] . "\n";
                
                // 规格信息（仅普通商品显示，计量商品不显示规格）
                if (!empty($product['spec_name']) && $product['product_type'] != 2) {
                    $content .= "  规格：" . $product['spec_name'] . "\n";
                }
                
                // 计量商品和普通商品的显示方式不同
                if ($product['product_type'] == 2) {
                    // 计量商品 - 按重量显示
                    $content .= sprintf("  %.2fkg × %.2f元/kg = %.2f元\n", 
                        $product['weight'], 
                        $product['price'], 
                        $product['total_amount']
                    );
                } else {
                    // 普通商品 - 按数量显示
                    $content .= sprintf("  %d × %.2f元 = %.2f元\n", 
                        $product['quantity'], 
                        $product['price'], 
                        $product['total_amount']
                    );
                }
            }
        }
        
        $content .= "--------------------------------\n";
        $content .= sprintf("商品金额：%.2f元\n", $orderData['total_amount'] ?? 0);
        
        // 根据订单类型显示配送费（仅外卖单显示配送费）
        if ($this->isDeliveryOrder($orderData['order_type'] ?? 0) && 
            !empty($orderData['delivery_fee']) && $orderData['delivery_fee'] > 0) {
            $content .= sprintf("配送费：%.2f元\n", $orderData['delivery_fee']);
        }
        
        if (!empty($orderData['discount_amount']) && $orderData['discount_amount'] > 0) {
            $content .= sprintf("优惠金额：-%.2f元\n", $orderData['discount_amount']);
        }
        
        $content .= sprintf("实付金额：%.2f元\n", $orderData['actual_amount'] ?? 0);
        $content .= "支付方式：" . ($orderData['pay_type_text'] ?? '未知') . "\n";
        
        if (!empty($orderData['remark'])) {
            $content .= "备注：" . $orderData['remark'] . "\n";
        }
        
        $content .= "--------------------------------\n";
        $content .= "<center>谢谢惠顾，欢迎再次光临！</center>\n";
        $content .= "\n\n\n";
        
        return $content;
    }
    
    /**
     * 获取订单类型文本
     * @param int $orderType
     * @return string
     */
    private function getOrderTypeText($orderType)
    {
        $typeMap = [
            1 => '自提订单',
            2 => '配送订单',
            3 => '结算台订单'
        ];
        
        return $typeMap[$orderType] ?? '未知类型';
    }
    
    /**
     * 判断是否为配送订单
     * @param int $orderType
     * @return bool
     */
    private function isDeliveryOrder($orderType)
    {
        // 2=配送订单需要显示配送费，1=自提订单和3=结算台订单不显示配送费
        return $orderType == 2;
    }
    
    /**
     * 生成测试打印内容
     * @return string
     */
    private function generateTestContent()
    {
        $content = "\n";
        $content .= "<CB>打印机测试</CB>\n";
        $content .= "--------------------------------\n";
        $content .= "测试时间：" . date('Y-m-d H:i:s') . "\n";
        $content .= "打印机状态：正常\n";
        $content .= "--------------------------------\n";
        $content .= "<center>测试完成</center>\n";
        $content .= "\n\n\n";
        
        return $content;
    }
    
    /**
     * 创建打印日志
     * @param int $shopId
     * @param string $orderNo
     * @param string $printerSn
     * @param string $content
     * @param string $type
     * @return int
     */
    private function createPrintLog($shopId, $orderNo, $printerSn, $content, $type = 'order')
    {
        $logData = [
            'clientkeynum' => $this->clientkeynum,
            'shop_id' => $shopId,
            'order_no' => $orderNo,
            'printer_sn' => $printerSn,
            'print_content' => $content,
            'print_type' => $type,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $log = YlyPrintLog::createLog($logData);
        return $log->id;
    }
    
    /**
     * 重试失败的打印任务
     * @param int $maxRetry
     * @return array
     */
    public function retryFailedPrints($maxRetry = 3)
    {
        $failedLogs = YlyPrintLog::getFailedLogs($this->clientkeynum, $maxRetry);
        $results = [];
        
        foreach ($failedLogs as $log) {
            try {
                // 增加重试次数
                YlyPrintLog::incrementRetry($log['id']);
                
                // 使用官方SDK重新执行打印
                $printService = new PrintService($this->accessToken, $this->config);
                $result = $printService->index(
                    $log['printer_sn'], 
                    $log['print_content'], 
                    $log['order_no']
                );
                
                // SDK返回的是标准对象，需要检查结果
                if (!$result) {
                    throw new Exception('重试打印接口调用失败');
                }
                
                // 更新为成功
                YlyPrintLog::updateStatus($log['id'], YlyPrintLog::STATUS_SUCCESS, json_encode($result));
                
                $results[] = [
                    'order_no' => $log['order_no'],
                    'success' => true,
                    'message' => '重试成功'
                ];
                
            } catch (Exception $e) {
                // 重试失败
                YlyPrintLog::updateStatus($log['id'], YlyPrintLog::STATUS_FAILED, '', $e->getMessage());
                
                $results[] = [
                    'order_no' => $log['order_no'],
                    'success' => false,
                    'message' => '重试失败: ' . $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
} 