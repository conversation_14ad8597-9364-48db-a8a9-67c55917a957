<?php
/**
 * 拣货编号服务类（简化版）
 * 支持支付成功后生成编号，所有订单类型都生成编号
 * 无需管理表，到达999直接重置为1
 */
namespace app\common\service;

use think\Db;
use think\Exception;
use think\facade\Log;

class PickupNumberService
{
    /**
     * 生成拣货编号
     * @param int $shop_id 门店ID
     * @param string $clientkeynum 客户端密钥
     * @return int|false 返回编号或false
     */
    public static function generatePickupNumber($shop_id, $clientkeynum)
    {
        // 开启事务确保原子性
        Db::startTrans();
        
        try {
            // 获取当天该门店的最大编号
            $today = date('Y-m-d');
            $maxNumber = Db::name('order')
                ->where([
                    'shop_id' => $shop_id,
                    'clientkeynum' => $clientkeynum
                ])
                ->whereTime('add_time', $today)
                ->whereNotNull('pickup_number')
                ->lock(true) // 加锁防止并发
                ->max('pickup_number');
            
            // 计算下一个编号
            $nextNumber = empty($maxNumber) ? 1 : $maxNumber + 1;
            
            // 如果超过999，重置为1
            if ($nextNumber > 999) {
                $nextNumber = 1;
            }
            
            Db::commit();
            return $nextNumber;
            
        } catch (Exception $e) {
            Db::rollback();
            Log::error('生成拣货编号失败：' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 为订单设置拣货编号
     * @param string $order_no 订单号
     * @param int $shop_id 门店ID
     * @param string $clientkeynum 客户端密钥
     * @return bool
     */
    public static function setPickupNumberForOrder($order_no, $shop_id, $clientkeynum)
    {
        $pickup_number = self::generatePickupNumber($shop_id, $clientkeynum);
        
        if ($pickup_number === false) {
            return false;
        }
        
        // 更新订单的拣货编号
        $result = Db::name('order')
            ->where('order_no', $order_no)
            ->update([
                'pickup_number' => $pickup_number
            ]);
        
        if ($result) {
            Log::info("订单 {$order_no} 生成拣货编号：{$pickup_number}");
            return true;
        }
        
        return false;
    }
    
    /**
     * 格式化显示编号
     * @param int $number 编号
     * @return string
     */
    public static function formatNumber($number)
    {
        return sprintf('#%03d', $number);
    }
    

} 