<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright (c) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------

//------------------------
// ThinkPHP 助手函数
//-------------------------

use OSS\Core\OssException;
use OSS\OssClient;
use PHPMailer\PHPMailer\PHPMailer;
use think\facade\Config;
use think\Db;
use think\facade\Session;
use think\facade\Url;
use lizengbang\Sms;



/*
 * 发送短信验证码礼赠帮 v2
 */
function send_phone_code_lizengbang_v2($basekeynum, $phone, $reqCount = '1')
{
    $duanxinsetinfo = Db::table('plat_lizengbang_sms_set')->where("basekeynum='$basekeynum'")->find();
    $plat_system_set_info = Db::table('plat_system_set')->where("id ='1' ")->find();
    $destnumbers    = $phone;
    $appid        = $duanxinsetinfo['appid'];
    $appkey       = $duanxinsetinfo['appkey'];
    //$api_url           = $duanxinsetinfo['api_url'];
    $api_url      = $plat_system_set_info['lizengbang_api_url'];
    $code_status    = $duanxinsetinfo['code_status'];
    $content        = $duanxinsetinfo['code_content'];
    //总开关是否开启
    $sms_status = $duanxinsetinfo['sms_status'];
    if ($sms_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "短信验证码服务总开关没有开启";
        echo json_encode($return_arr);
        die;
    }
    if ($code_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "短信验证码服务没有开启";
        echo json_encode($return_arr);
        die;
    }
    if ($appid == '' || $appkey == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "短信应用配置不能为空！";
        echo json_encode($return_arr);
        die();
    }

    if ($destnumbers == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "发送手机号不能为空!";
        echo json_encode($return_arr);
        die();
    }

    $code                      = rand(1000, 9999);
    $content                   = str_replace("{code}", $code, $content);
    $msg                       = $content;

    $sign = $duanxinsetinfo['sign'];
    $qianming = "【" . $sign . "】";
    //这种情况要剔除内容里面的签名【】
    $msg =  $qianming . preg_replace('/\【.*?\】/', '', $content);


    $datalog['words']          = $msg;
    $datalog['time']           = time();
    $datalog['phonenum']       = $destnumbers;
    $datalog["clientkeynum"]   = $basekeynum;

    $add_code["phone"]          = $destnumbers;
    $add_code["code"]           = $code;
    $add_code["time"]           = time(); //存入生成时间，有效期由验证的时候自己把握
    $add_code["clientkeynum"]   = $basekeynum;

    //发送短信开始
    $appid = $appid;
    $appkey = $appkey; //此秘钥只是示例，请使用正确秘钥
    $api_url = $api_url;
    $destnumbers = $phone;
    $msg = $msg;
    $obj = new  Sms($api_url, $appid, $appkey);


    //如果是32位字符串则使用它否则改成空
    if (strlen(trim($sign)) == 32) {
        $sign = trim($sign);
    } else {
        $sign = "";
    }

    $rt_arr = $obj->sendsms_v2($destnumbers, $code, $reqCount, $sign);
    if ($rt_arr['sta'] != "1") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = $rt_arr['msg'];
        echo json_encode($return_arr);
        die;
    }
    //发送短信结束
    $datalog['api_return_content'] = json_encode($rt_arr, JSON_UNESCAPED_UNICODE);


    Db::table('plat_sms_log')->insert($datalog);
    Db::table("plat_sms_code")->insert($add_code);
    $return_arr['sta'] = 1;
    $return_arr['msg'] = "发送成功!";
    echo json_encode($return_arr);
    die();
}

/**
 * 后台账号登录日志记录
 */
function addloginlog($remark, $accountname = false)
{
    $plat_accountloginlog = Db::table('plat_accountloginlog');
    $data['accountname']  = $accountname;
    //取出该账号对应的平台客户basekeynum,没有默认是平台
    $accountinfo           = Db::table('plat_account')->where("accountname='$accountname'")->find();
    $data['basekeynum']    = $accountinfo['basekeynum'];
    $data['logintime']     = time();
    $data['logindatetime'] = date("Y-m-d H:i:s", time());
    $data['remark']        = $remark;
    $data['loginip']       = $_SERVER['REMOTE_ADDR'];
    $data['login_location']        = get_location($_SERVER['REMOTE_ADDR']);
    $data['browser']        = get_browser1();
    $data['os']        = get_os();

    // $data['real_ip']       = $_SERVER['HTTP_X_FORWARDED_FOR'];  //如果是负载均衡的话，用这个或者HTTP_REMOTEIP，获取客户端ip地址
    // $data['real_location']        = get_location($_SERVER['HTTP_X_FORWARDED_FOR']);
    $plat_accountloginlog->insert($data);
}

// 添加后台操作日志$log简描述  $data具体数据，不要在查询的地方滥用
function addoperatelog($log, $content)
{
    $data['accountname']     = session("cn_accountinfo.accountname");
    $data['accountkeynum']   = session("cn_accountinfo.keynum");
    $data['accountid']       = session("cn_accountinfo.account_id");
    $data['basekeynum']      = session("cn_accountinfo.basekeynum");
    $data['log']             = $log;
    $data['content']         = $content;
    $data['ip']              = $_SERVER["REMOTE_ADDR"];
    // $data['real_ip']              =  $_SERVER['HTTP_X_FORWARDED_FOR'];  //如果是负载均衡的话，用这个或者HTTP_REMOTEIP，获取客户端ip地址
    $data['operatetime']     = time();
    $data['operatedatetime'] = date("Y-m-d H:i:s", time());
    Db::table('plat_accountoperationlog')->insert($data);
}

//账号密码加密函数 不可逆加密
function password($password)
{
    return md5('' . $password . '');
}

//全球唯一标识符号
function create_guid()
{
    $charid = strtoupper(md5(uniqid(mt_rand(), true)));
    $hyphen = "";
    $uuid   = substr($charid, 6, 2) . substr($charid, 4, 2) .
        substr($charid, 2, 2) . substr($charid, 0, 2) . $hyphen
        . substr($charid, 10, 2) . substr($charid, 8, 2) . $hyphen
        . substr($charid, 14, 2) . substr($charid, 12, 2) . $hyphen
        . substr($charid, 16, 4) . $hyphen . substr($charid, 20, 12);
    return $uuid;
}

/**
 * 后台基类初始化菜单
 */
function EffectiveHttp($str)
{
    if (strpos($str, "http://") === 0 or strpos($str, "https://") === 0) {
        return true;
    } else {
        return false;
    }
}

/**
 * 判断是ie8以及ie8以下内核浏览器
 */
function check_browser()
{
    if (strpos($_SERVER["HTTP_USER_AGENT"], "Firefox")) {
        return false;
    } else if (strpos($_SERVER["HTTP_USER_AGENT"], "Chrome")) {
        return false;
    } else if (strpos($_SERVER["HTTP_USER_AGENT"], "Safari")) {
        return false;
    } else if (strpos($_SERVER["HTTP_USER_AGENT"], "Opera")) {
        return false;
    } else {
        return true;
    }
}

//判断是否ie浏览器
function isIEbrower()
{
    return false !== stristr($_SERVER['HTTP_USER_AGENT'], 'MSIE');
}

//组织机构分类数
function Get_Tree($arr, $pid)
{
    $tree = array();
    foreach ($arr as $key => $val) {
        if ($val['parentId'] == $pid) {
            $val["spread"]   = true;
            $val['children'] = Get_Tree($arr, $val['id']);
            $tree[]          = $val;
        }
    }
    return $tree;
}

//获取菜单树结构
function Get_Menu_Tree($arr, $pid)
{
    $tree = array();
    foreach ($arr as $key => $val) {
        if ($val['pid'] == $pid) {
            $val['children'] = Get_Menu_Tree($arr, $val['menu_id']);
            $tree[]          = $val;
        }
    }
    return $tree;
}

//单张图片上传
function UpImage($callBack = "image", $width = 100, $height = 100, $image = "")
{
    echo '<iframe scrolling="no" frameborder="0" border="0" onload="this.height=this.contentWindow.document.body.scrollHeight;this.width=this.contentWindow.document.body.scrollWidth;" width=' . $width . ' height="' . $height . '"  src="' . url('Upload/uploadpic') . '?Width=' . $width . '&Height=' . $height . '&BackCall=' . $callBack . '&Img=' . $image . '"></iframe>
         <input type="hidden" name="' . $callBack . '" id="' . $callBack . '">';
}

//视频上传
function UpVideo($callBack = "video", $width = 100, $height = 100, $video = "")
{
    echo '<iframe scrolling="no" frameborder="0" border="0" onload="this.height=this.contentWindow.document.body.scrollHeight;this.width=this.contentWindow.document.body.scrollWidth;" width=' . $width . ' height="' . $height . '" 
    
    src="' . url('Upload/uploadvideo') . '?Width=' . $width . '&Height=' . $height . '&BackCall=' . $callBack . '&Video=' . $video . '"></iframe>
         <input type="hidden"    name="' . $callBack . '" id="' . $callBack . '">';
}

//多张图片上传
function BatchImage($callBack = "image", $width = 100, $height = 100, $image = "")
{
    echo '<iframe scrolling="no" frameborder="0" border="0" width=100% height=150  src="' . url('Upload/batchpic') . '?BackCall=' . $callBack . '&Img=' . $image . '"></iframe>
        <input type="hidden" name="' . $callBack . '" id="' . $callBack . '">';
}

//文件上传到阿里云oss方法
function uploadFileToAliOss($local_file, $remote_file)
{
    //从数据取出来阿里云oss配置参数
    $info = $rs = Db::table('plat_system_set')->where("id='1'")->find();
    //上传到阿里云oss
    // 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录 https://ram.console.aliyun.com 创建RAM账号。
    $accessKeyId     = $info['ali_accesskeyid']; //"LTAIVZPMH2ErRTa7";
    $accessKeySecret = $info['ali_accesskeysecret']; //"yxgVMMD3Eh8Jko2MR64AuCkkslbkok";
    // Endpoint以杭州为例，其它Region请按实际情况填写。
    $endpoint = $info['ali_endpoint']; //"http://oss-cn-beijing.aliyuncs.com";
    // 存储空间名称
    $bucket = $info['ali_bucket']; //"kunyuan";
    // 文件名称
    $object = $remote_file; //文件的路径
    // <yourLocalFile>由本地文件路径加文件名包括后缀组成，例如/users/local/myfile.txt
    $filePath = $local_file;
    $flag = true;
    try {
        $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
        $ossClient->uploadFile($bucket, $object, $filePath);
    } catch (OssException $e) {
        $msg = $e->getMessage();
        $flag = false;
    }
    //根据flag判断是否成功
    if (!$flag) {
        $rt['sta'] = "0";
        $rt['msg'] = "上传到oss失败！详细信息：" . $msg;
        $rt['url'] = "";
        return $rt;
        die;
    }
    //成功返回
    $rt['sta'] = "1";
    $rt['msg'] = "上传到oss成功！";
    $rt['url'] = $info['ali_bucket_url'] . "/" . $object;
    return $rt;
    die;
}


//文件上传到服务器web目录可以通过浏览器直接访问
function uploadFileToWebDir()
{
    //后期封装
}


/*Mb和GB转化*/
function getFilesize($num)
{
    $p      = 3;
    $format = 'GB';
    $num /= pow(1024, $p);
    return number_format($num, 2);
}

//xml转数组
function xmlToArray($xml)
{
    //将XML转为array
    $array_data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
    return $array_data;
}

//短信平台
function get_duanxin_info($keynum)
{
    $retinfo        = '';
    $xmlstring      = '';
    $info           = '';
    $keynum         = $keynum;
    $duanxinsetinfo = Db::table('plat_sms_set')->where("basekeynum='$keynum'")->find();
    if (empty($duanxinsetinfo)) {
        $return_arr['sta'] = '1';
        $return_arr['msg'] = "没有找到记录！";
        return $return_arr;
        die;
    }

    $userid   = $duanxinsetinfo['username'];
    $password = $duanxinsetinfo['password'];
    $qUrl     = $duanxinsetinfo['url'] . "/CASServer/SmsAPI/QueryUserInfo.jsp";
    $qUrl .= '?userid=' . $userid . '&password=' . urlencode($password);
    //echo $qUrl;die;
    $xmlstring = file_get_contents($qUrl);
    if (!$xmlstring) {
        $return_arr['sta'] = '0';
        $return_arr['msg'] = "未知错误！";
        return $return_arr;
        die;
    }

    $arr = xmlToArray($xmlstring);
    //print_r($arr);die;
    if ($arr['@attributes']['return'] != '0') {
        $return_arr['sta'] = '0';
        $return_arr['msg'] = $arr['@attributes']['info'];
        return $return_arr;
        die;
    }
    $return_arr['sta']       = '1';
    $return_arr['name']      = $arr['NAME'];
    $return_arr['sign']      = $arr['SIGN'];
    $return_arr['riches']    = $arr['RICHES'];
    $return_arr['totalin']   = $arr['TOTALIN'];
    $return_arr['totalsend'] = $arr['TOTALSEND'];
    return $return_arr;
    die;
}

/**
 * 去除多余的转义字符
 */
function doaddslashes($param)
{
    if (!get_magic_quotes_gpc()) {
        $param = addslashesDeep($param);
    }
    return $param;
}

/**
 * 递归去除转义字符
 */
function addslashesDeep($value)
{
    $value = is_array($value) ? array_map('addslashesDeep', $value) : addslashes($value);
    return $value;
}

/**
 * 获取最近七天所有日期
 */
function get_weeks($time = '', $format = 'Y-m-d')
{
    $time = $time != '' ? $time : time();
    //组合数据
    $data = [];
    for ($i = 1; $i <= 7; $i++) {
        $data[$i] = "'" . date($format, strtotime('+' . $i - 7 . ' days', $time)) . "'";
    }
    return $data;
}

/**
 * 获取最近七天所有日期会员数量
 */
function get_weeks_member($time = '', $format = 'Y-m-d')
{
    return  1;
    $time = $time != '' ? $time : time();
    //组合数据
    $date = [];
    for ($i = 1; $i <= 7; $i++) {
        $ts       = date($format, strtotime('+' . $i - 7 . ' days', $time));
        $ts1      = strtotime("$ts  00:00:00");
        $ts2      = strtotime("$ts  23:59:59");
        $count    = Db::table('client_merchant_member')->where("add_time>='$ts1' && add_time<='$ts2'   ")->count();
        $data[$i] = $count;
    }
    return $data;
}

/**
 * 获取终端访问统计数量
 */
function get_terminal()
{
    $list = Db::table('plat_terminal_log')->where("1=1")->select();
    foreach ($list as $key => $value) {
        $data1[$key] = "'" . $value['terminal_name'] . "'";
        $data2[$key] = "{value:" . $value['visit_nums'] . ", name:'" . $value['terminal_name'] . "'}";
    }
    $data['name']       = $data1;
    $data['visit_nums'] = $data2;
    return $data;
}


/**
 * 最近流量趋势
 */
function get_recent_visit()
{
    $list = Db::table('plat_visit_log')->where("1=1")->limit(24)->select();
    foreach ($list as $key => $value) {
        $data1[$key] = "'" . $value['hour'] . "'";
        $data2[$key] = "{value:" . $value['visit_pv_nums'] . ", name:'" . $value['hour'] . "'}";
        $data3[$key] = "{value:" . $value['visit_uv_nums'] . ", name:'" . $value['hour'] . "'}";
    }
    $data['name']          = $data1;
    $data['visit_pv_nums'] = $data2;
    $data['visit_uv_nums'] = $data3;
    return $data;
}


//发送邮件验证码
function send_mail($recipient = "", $html = "", $subject = "", $param = "", $basekeynum = "")
{
    date_default_timezone_set('PRC');
    $mail            = new PHPMailer();
    $mail->SMTPDebug = 0; //调试模式 0关闭
    $mail->isSMTP();
    $mail->SMTPAuth   = true;
    $mail->Host       = 'smtp.qq.com';
    $mail->SMTPSecure = 'ssl';
    //设置ssl连接smtp服务器的远程服务器端口号 可选465或587
    $mail->Port     = 465;
    $mail->Hostname = 'localhost';
    $mail->CharSet  = 'UTF-8';
    $mail->FromName = 'wlphp.com'; //发件人名字(发件人和收件人不是好友的情况下显示)

    $mail->Username = '<EMAIL>'; //发件邮箱的账号
    $mail->Password = 'kwgceqrennkoidjd'; //发件邮箱邮箱授权码
    $mail->From     = '<EMAIL>';

    $mail->isHTML(true);
    $mail->addAddress($recipient, ''); //收件人的姓名(发件人和收件人不是好友的情况下显示)
    $mail->Subject = $subject ? $subject : '预警提示'; //邮件主题
    //$mail->Body = "58同城-邮箱绑定邮件<br/>亲爱的58同城用户lkxlg_p0：请复制下面的动态码，并返回原页面提交以完成绑定邮箱。<br/>963854<br/>绑定成功后可获得 50 个信用。信用值越高，每天可发布的信息数量越多，认证后的邮箱可用于登录和找回密码。<br/>58赶集集团<br/>http://www.58.com";
    //$mail->Body =file_get_contents("http://address.wlphp.com/");
    //$mail->addAttachment('test.jpg','test.png');   //test.jpg本地文件路径   test.png接收到邮件里面文件名称
    $mail->Body = $html;
    $status     = $mail->send();
    //记录邮件发送日志
    $data['subject']    = $subject;
    $data['words']      = $html;
    $data['basekeynum'] = $basekeynum;
    $data['recipient']  = $recipient;
    $data['json']       = json_encode($param, JSON_UNESCAPED_UNICODE);
    $data['ip']         = $_SERVER["REMOTE_ADDR"];
    $data['time']       = time();
    $data['datetime']   = date("Y-m-d H:i:s", time());

    if ($status) {
        $rt['sta']            = '1';
        $rt['msg']            = '发送邮件成功' . date('Y-m-d H:i:s');
        $data['is_send_ok']   = 1;
        $data['send_message'] = $rt['msg'];
        Db::table('plat_mail_log')->insert($data);
        return $rt;
    } else {
        $rt['sta']            = '0';
        $rt['msg']            = '发送邮件失败，错误信息未：' . $mail->ErrorInfo;
        $data['is_send_ok']   = 0;
        $data['send_message'] = $rt['msg'];
        Db::table('plat_mail_log')->insert($data);
        return $rt;
    }
}

/**
 *菜单权限校验函数 $ajax如果是1表示返回ajax，否则返回页面错误
 */
function check_auth($url, $ajax = 0)
{
    //如果登陆者是平台级别则免疫所有权限
    $basekeynum = session("cn_accountinfo.basekeynum");
    if ($basekeynum == '平台') {
        return true;
    }

    $menulist = session("cn_accountinfo.menulist");
    if (empty($menulist)) {
        //没有权限
        if ($ajax == 1) {
            $rt['sta'] = "0";
            $rt['msg'] = "当前登录用户没有此菜单操作权限，请联系管理员授权！";
            echo json_encode($rt);
            die;
        } else {
            error_tips("当前登录用户没有此菜单操作权限，请联系管理员授权！");
            die;
        }
    }

    $where = "  menu_id  in ($menulist)  and  url='$url' ";
    $info  = Db::table('plat_menu')->where($where)->find();
    if (!$info['menu_id']) {
        //没有权限
        if ($ajax == 1) {
            $rt['sta'] = "0";
            $rt['msg'] = "当前登录用户没有此菜单操作权限，请联系管理员授权！";
            echo json_encode($rt);
            die;
        } else {
            error_tips("当前登录用户没有此菜单操作权限，请联系管理员授权！");
            die;
        }
    }
}

/**
 *页面错误提示函数
 */
function error_tips($errorinfo)
{
    //获取静态资源前缀
    $view_replace_str_arr = Config::get('view_replace_str');
    $static               = $view_replace_str_arr['/static'];

    $html                 = '<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>出错了</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="' . $static . '/admin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="' . $static . '/admin/style/admin.css" media="all">
</head>
<body>
<div class="layui-fluid">
  <div class="layadmin-tips">
    <i class="layui-icon" face  style="font-size: 180px;">&#xe664;</i>
    <div class="layui-text" style="font-size: 20px;">
     ' . $errorinfo . '
    </div>

  </div>
</div>
</body>
</html>';
    echo $html;
    die;
}

//获取参数值并且处理这个参数,如果不存在赋值并且返回空值
function getparam($param)
{
    $param = trim($param);
    return isset($param) && $param != '' ? $param : '';
}

//获取后台登录域名,第一个
function get_glurl($keynum)
{
    $accountinfo = Db::table('plat_account')->where("basekeynum='$keynum' and  keynum='$keynum' ")->find();
    $str_glurl   = $accountinfo['glurl'];
    $arr_glurl   = explode(',', $str_glurl);
    return "http://" . $arr_glurl['0'];
}

//获取前台域名,第一个
function get_weburl($keynum)
{
    $accountinfo = Db::table('plat_account')->where("basekeynum='$keynum' and  keynum='$keynum' ")->find();
    $str_weburl  = $accountinfo['weburl'];
    $arr_weburl  = explode(',', $str_weburl);
    return $arr_weburl['0'];
}

//生成自动登录签名
function cre_aotologin_sign($keynum, $ts, $autologin_key)
{
    $autologin_key = Config::get('autologin_key');
    return strtoupper(md5($keynum . $ts . $autologin_key));
}
//验证自动登录签名
function check_autologin_sign($keynum, $ts, $autologin_key, $sign)
{
    $autologin_key = Config::get('autologin_key');
    if (cre_aotologin_sign($keynum, $ts, $autologin_key) == $sign) {
        return true;
    }
    return false;
}

//判断是手机终端
function isMobile()
{
    return 1;
    // 如果有HTTP_X_WAP_PROFILE则一定是移动设备
    if (isset($_SERVER['HTTP_X_WAP_PROFILE'])) {
        return true;
    }
    // 如果via信息含有wap则一定是移动设备,部分服务商会屏蔽该信息
    if (isset($_SERVER['HTTP_VIA'])) {
        // 找不到为flase,否则为true
        return stristr($_SERVER['HTTP_VIA'], "wap") ? true : false;
    }
    // 脑残法，判断手机发送的客户端标志,兼容性有待提高
    if (isset($_SERVER['HTTP_USER_AGENT'])) {
        $clientkeywords = array(
            'nokia',
            'sony',
            'ericsson',
            'mot',
            'samsung',
            'htc',
            'sgh',
            'lg',
            'sharp',
            'sie-',
            'philips',
            'panasonic',
            'alcatel',
            'lenovo',
            'iphone',
            'ipod',
            'blackberry',
            'meizu',
            'android',
            'netfront',
            'symbian',
            'ucweb',
            'windowsce',
            'palm',
            'operamini',
            'operamobi',
            'openwave',
            'nexusone',
            'cldc',
            'midp',
            'wap',
            'mobile',
        );
        // 从HTTP_USER_AGENT中查找手机浏览器的关键字
        if (preg_match("/(" . implode('|', $clientkeywords) . ")/i", strtolower($_SERVER['HTTP_USER_AGENT']))) {
            return true;
        }
    }
    // 协议法，因为有可能不准确，放到最后判断
    if (isset($_SERVER['HTTP_ACCEPT'])) {
        // 如果只支持wml并且不支持html那一定是移动设备
        // 如果支持wml和html但是wml在html之前则是移动设备
        if ((strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') !== false) && (strpos($_SERVER['HTTP_ACCEPT'], 'text/html') === false || (strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') < strpos($_SERVER['HTTP_ACCEPT'], 'text/html')))) {
            return true;
        }
    }
    return false;
}


/*
 * 发送短信验证啊$keynum
 */
function send_phone_code($basekeynum, $phone)
{
    $duanxinsetinfo = Db::table('plat_sms_set')->where("basekeynum='$basekeynum'")->find();
    $destnumbers    = $phone;
    $userid         = $duanxinsetinfo['username'];
    $password       = $duanxinsetinfo['password'];
    $qUrl           = $duanxinsetinfo['url'] . "/CASServer/SmsAPI/SendMessage.jsp";
    $code_status    = $duanxinsetinfo['code_status'];
    $content        = $duanxinsetinfo['code_content'];
    //总开关是否开启
    $sms_status = $duanxinsetinfo['sms_status'];
    if ($sms_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "短信验证码服务总开关没有开启";
        echo json_encode($return_arr);
        die;
    }
    if ($code_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "短信验证码服务没有开启";
        echo json_encode($return_arr);
        die;
    }
    if ($userid == '' || $password == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "短信账号或者密码为空！";
        echo json_encode($return_arr);
        die();
    }

    if ($destnumbers == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "发送手机号不能为空!";
        echo json_encode($return_arr);
        die();
    }

    $code                      = rand(1000, 9999);
    $content                   = str_replace("{code}", $code, $content);
    $msg                       = $content;
    $sendtime                  = date("Y-m-d H:i:s", time());
    $duanxin_log               = Db::table('sms_log');
    $datalog['words']          = $msg;
    $datalog['time']           = time();
    $datalog['phonenum']       = $destnumbers;
    $datalog["clientkeynum"]   = $basekeynum;

    $add_code["phone"]          = $destnumbers;
    $add_code["code"]           = $code;
    $add_code["time"]           = time(); //存入生成时间，有效期由验证的时候自己把握
    $add_code["clientkeynum"]   = $basekeynum;

    $qUrl .= '?userid=' . $userid . '&password=' . urlencode($password) . '&destnumbers=' . $destnumbers .
        '&msg=' . urlencode($msg) . '&sendtime=' . urlencode($sendtime);
    $xmlstring                     = file_get_contents($qUrl);
    $datalog["api_return_content"] = json_encode(xmlToArray($xmlstring));
    Db::table('plat_sms_log')->insert($datalog);
    Db::table("plat_sms_code")->insert($add_code);
    $return_arr['sta'] = 1;
    $return_arr['msg'] = "发送成功!";
    echo json_encode($return_arr);
    die();
}


/**
 * 生成签名并发起请求   阿里云短信
 *
 * @param $accessKeyId string AccessKeyId (https://ak-console.aliyun.com/)
 * @param $accessKeySecret string AccessKeySecret
 * @param $domain string API接口所在域名
 * @param $params array API具体参数
 * @param $security boolean 使用https
 * @param $method boolean 使用GET或POST方法请求，VPC仅支持POST
 * @return bool|\stdClass 返回API接口调用结果，当发生错误时返回false
 */
function request_ali($accessKeyId, $accessKeySecret, $domain, $params, $security = false, $method = 'POST')
{
    $apiParams = array_merge(array(
        "SignatureMethod" => "HMAC-SHA1",
        "SignatureNonce" => uniqid(mt_rand(0, 0xffff), true),
        "SignatureVersion" => "1.0",
        "AccessKeyId" => $accessKeyId,
        "Timestamp" => gmdate("Y-m-d\TH:i:s\Z"),
        "Format" => "JSON",
    ), $params);
    ksort($apiParams);

    $sortedQueryStringTmp = "";
    foreach ($apiParams as $key => $value) {
        $sortedQueryStringTmp .= "&" . encode($key) . "=" . encode($value);
    }

    $stringToSign = "${method}&%2F&" . encode(substr($sortedQueryStringTmp, 1));

    $sign = base64_encode(hash_hmac("sha1", $stringToSign, $accessKeySecret . "&", true));

    $signature = encode($sign);

    $url = ($security ? 'https' : 'http') . "://{$domain}/";

    try {
        $content = fetchContent($url, $method, "Signature={$signature}{$sortedQueryStringTmp}");
        return json_decode($content, 1);
    } catch (\Exception $e) {
        return false;
    }
}


function encode($str)
{
    $res = urlencode($str);
    $res = preg_replace("/\+/", "%20", $res);
    $res = preg_replace("/\*/", "%2A", $res);
    $res = preg_replace("/%7E/", "~", $res);
    return $res;
}

function fetchContent($url, $method, $body)
{
    $ch = curl_init();

    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_POST, 1); //post提交方式
        curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    } else {
        $url .= '?' . $body;
    }

    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        "x-sdk-client" => "php/2.0.0"
    ));

    if (substr($url, 0, 5) == 'https') {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }

    $rtn = curl_exec($ch);

    if ($rtn === false) {
        // 大多由设置等原因引起，一般无法保障后续逻辑正常执行，
        // 所以这里触发的是E_USER_ERROR，会终止脚本执行，无法被try...catch捕获，需要用户排查环境、网络等故障
        trigger_error("[CURL_" . curl_errno($ch) . "]: " . curl_error($ch), E_USER_ERROR);
    }
    curl_close($ch);

    return $rtn;
}



/*
 * 阿里云发送短信验证啊$keynum
 */
function send_phone_code_ali($basekeynum, $phone)
{
    $duanxinsetinfo = Db::table('plat_sms_set_ali')->where("basekeynum='$basekeynum'")->find();
    $accesskeyid         = $duanxinsetinfo['accesskeyid'];
    $accesskeysecret       = $duanxinsetinfo['accesskeysecret'];
    $signname       = $duanxinsetinfo['signname'];
    $templatecode       = $duanxinsetinfo['templatecode'];

    //总开关是否开启
    $sms_status = $duanxinsetinfo['sms_status'];
    if ($sms_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "短信验证码服务总开关没有开启";
        echo json_encode($return_arr);
        die;
    }
    if ($code_status == "0") {
        $return_arr["sta"] = 0;
        $return_arr["msg"] = "短信验证码服务没有开启";
        echo json_encode($return_arr);
        die;
    }
    if ($accesskeyid == '' || $accesskeysecret == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "accesskeyid或者accesskeysecret为空！";
        echo json_encode($return_arr);
        die();
    }

    if ($phone == '') {
        $return_arr['sta'] = 0;
        $return_arr['msg'] = "发送手机号不能为空!";
        echo json_encode($return_arr);
        die();
    }
    //生成短信验证码
    $code                      = rand(1000, 9999);
    $params = array();
    // *** 需用户填写部分 ***
    // fixme 必填：是否启用https
    $security = false;
    // fixme 必填: 请参阅 https://ak-console.aliyun.com/ 取得您的AK信息
    $accessKeyId = $accesskeyid;
    $accessKeySecret = $accesskeysecret;
    // fixme 必填: 短信接收号码
    $params["PhoneNumbers"] = $phone;
    // fixme 必填: 短信签名，应严格按"签名名称"填写，请参考: https://dysms.console.aliyun.com/dysms.htm#/develop/sign
    $params["SignName"] = $signname;
    // fixme 必填: 短信模板Code，应严格按"模板CODE"填写, 请参考: https://dysms.console.aliyun.com/dysms.htm#/develop/template
    $params["TemplateCode"] = $templatecode;
    // fixme 可选: 设置模板参数, 假如模板中存在变量需要替换则为必填项
    $params['TemplateParam'] = array(
        "code" => $code,
        //"product" => "阿里通信"
    );
    // fixme 可选: 设置发送短信流水号
    $params['OutId'] = "";
    // fixme 可选: 上行短信扩展码, 扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段
    $params['SmsUpExtendCode'] = "";
    // *** 需用户填写部分结束, 以下代码若无必要无需更改 ***
    if (!empty($params["TemplateParam"]) && is_array($params["TemplateParam"])) {
        $params["TemplateParam"] = json_encode($params["TemplateParam"], JSON_UNESCAPED_UNICODE);
    }
    // 此处可能会抛出异常，注意catch
    $arr = request_ali(
        $accessKeyId,
        $accessKeySecret,
        "dysmsapi.aliyuncs.com",
        array_merge($params, array(
            "RegionId" => "cn-hangzhou",
            "Action" => "SendSms",
            "Version" => "2017-05-25",
        )),
        $security
    );

    $datalog['words']          = $code;
    $datalog['time']           = time();
    $datalog['phonenum']       = $phone;
    $datalog["clientkeynum"]   = $basekeynum;
    $datalog['api_return_content'] = json_encode($arr, JSON_UNESCAPED_UNICODE);
    $add_code["phone"]          = $phone;
    $add_code["code"]           = $code;
    $add_code["time"]           = time(); //存入生成时间，有效期由验证的时候自己把握
    $add_code["clientkeynum"]   = $basekeynum;
    //添加两个表
    Db::table('plat_sms_log')->insert($datalog);
    Db::table("plat_sms_code")->insert($add_code);

    $return_arr['sta'] = 1;
    $return_arr['msg'] = "发送成功!";
    echo json_encode($return_arr);
    die();
}

//重复拉取会员信息
function refresh_memberinfo()
{
    $mid = session("cn_member.id"); //会员id
    if ($mid != '') {
        $member = Db::table("client_merchant_member")->where("id=' $mid'")->find();
        session("cn_member", $member);
    }
}

//获取随机数函数
function GetRandStr($length)
{
    $str     = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $len     = strlen($str) - 1;
    $randstr = '';
    for ($i = 0; $i < $length; $i++) {
        $num = mt_rand(0, $len);
        $randstr .= $str[$num];
    }
    return $randstr;
}

/**
 *页面错误提示函数
 */
function error_tips_web($errorinfo)
{
    //获取静态资源前缀
    $view_replace_str_arr = Config::get('view_replace_str');
    $static               = $view_replace_str_arr['__STATICWEB__'];

    $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>出错</title>
    <script src="' . $static . '/web/theme1/m/js/rem.js"></script>

    <link rel="stylesheet" href="' . $static . '/web/theme1/m/css/layui.css"  media="all">
    <link rel="stylesheet" href="' . $static . '/web/theme1/m/css/common.css">
    <link rel="stylesheet" href="' . $static . '/web/theme1/m/css/tips.css">
</head>
<body style="background: #FFF">
        <div id="app">
                <div class="page-header">
                        <div class="mint-header">
                           <div class="h-left">

                           </div>
                           <div class="h-right">

                           </div>
                        </div>
                    </div>
                    <!-- 图文 -->
                    <div class="error-img">
                        <img src="' . $static . '/web/theme1/m/images/no.png" alt="">
                    </div>
                    <div class="error-text">
                        <span>' . $errorinfo . '</span>
                    </div>
        </div>

</body>
</html>';
    echo $html;
}

//获取订单状态
function get_order_status($order_status)
{
    // $info            = Db::table('client_order_info')->where("order_id='$order_id'")->find();
    // $order_status    = $info['order_status'];
    //0下单成功，1已审核，2已发货，3签收，4签收过几日订单完成，5取消，6申请售后中，7售后完成
    if ($order_status == '0') {
        return "待审核";
    } else if ($order_status == '1') {
        return "发货中";
    } else if ($order_status == '2') {
        return "发货中";
    } else if ($order_status == '3') {
        return "已发货";
    } else {
        return "未知";
    }
}
//获取订单状态
function get_order_status_admin($order_status)
{
    // $info            = Db::table('client_order_info')->where("order_id='$order_id'")->find();
    // $order_status    = $info['order_status'];
    //0下单成功，1已审核，2已发货，3签收，4签收过几日订单完成，5取消，6申请售后中，7售后完成
    if ($order_status == '0') {
        return "待审核";
    } else if ($order_status == '1') {
        return "生成导出批次";
    } else if ($order_status == '2') {
        return "发货中";
    } else if ($order_status == '3') {
        return "已发货";
    } else {
        return "未知";
    }
}
/**
 * @return bool
 * 判断是否微信内置浏览器
 */
function is_weixin()
{
    if (empty($_SERVER['HTTP_USER_AGENT']) || strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') === false && strpos($_SERVER['HTTP_USER_AGENT'], 'Windows Phone') === false) {
        return false;
    }
    return true;
}

/**
 * @param $url 发送post请求的url
 * @param $data 发送的数据
 * @return mixed
 */
function curlPostErp($url, $data)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
    @curl_setopt($ch, CURLOPT_SAFE_UPLOAD, false); //php5.6.0开始，需要加上这行代码方可上传，否则取不到文件
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $tmpInfo = curl_exec($ch);
    $errorno = curl_errno($ch);
    if ($errorno) {
        $rt['slay'] = "-1";
        $rt['rs']   = "curl错误:$errorno";
        return json_encode($rt);
        die;
    }
    return $tmpInfo;
}

/**
 * 调取erp接口封装方法
 */
function sendtoerp($merchantkeynum, $data)
{

    $info      = Db::table("kufang_set")->where("basekeynum='$merchantkeynum'")->find();
    $AppKey    = trim($info['appkey']);
    $AppSecret = trim($info['appsecret']);
    $Token     = trim($info['token']);
    $apiurl    = trim($info['api_url']);
    //url地址
    $msg  = $data;
    $sign = strtoupper(md5($AppSecret . $msg . $AppKey));
    //post方法
    $senddata['msg']  = $msg;
    $senddata['code'] = $AppKey;
    $senddata['sign'] = $sign;
    $rs               = curlPostErp($apiurl, $senddata);
    logRes("发送数据:" . $msg . "---->返回数据：" . $rs, "erp");
    return $rs;
}






//和礼赠通对接核心方法
/*
 * 发送post请求
 */
function curlPost($url, $data)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $tmpInfo = curl_exec($ch);
    //$errorno=curl_errno($ch);
    return $tmpInfo;
}

function   sendtointerface($sendurl, $sendjson = array(), $sendfile = array())
{

    $private_key = '-----BEGIN RSA PRIVATE KEY-----
MIICXAIBAAKBgQDYucsb9ufvvGbdOv/kCMZ4oJfIz+eDJj6m2bjRzIM8P6sGXLFl
aXoJyYNbKi9ZCVTvYlE4i2RWlesSk63YX+1y4o5n9JjzbfkydZQy97FIaem/QvEP
Mfq3/A44PDykjtQRO11iIviFJlbR6bAYtbzem+dYS2cFZ+7XtGItS2ZxKwIDAQAB
AoGAF8iL2Y3G9jAeubh3zHaKEULOZWMrbOENjxHljmB7Jml2Q0R400A3/Er491Ax
0fuMLpkDgMkLQdVB7s/2DAC69eTR4vsnrQU0/eBikvEV2EEAPiuMQr2r/BYhEAyY
0R4edFjAG1sPCew/FqytSVMSWtxzGVOyB9CoAGI+4nBtCEECQQD4RAETQK9nZFhG
4TdLZv2WTdRcPjd6tLD3RtfQ7ldMtyhgO+WzNjj0zKoTbE3lp9WqDJonf5vTdfZr
S+1J80z/AkEA33o/sEV3G93tEewaAx1AncWFrRcjraMU5Y59Q2iUxWck+ASUPmj9
e4qfBIozBcVgH8YIyl6aLa4X4SzdN9uf1QJBAKdegTMPirCzT2gJcROeTRtFQQMm
1pQcuKkb02cBJ02KtOebudFFnsQ6LfaGL0XCDiNj95DlUJIQlp3jRAd0xNUCQE7l
WNDBZXCII+b85J4O2L4aerwF/EmT1o/Igz6mEEe0x19sNm3mIP9ZjBRNOgAik3IE
NxxC08mmubV8YkkTF1UCQBuLA9MAfqGcp9JOwoo9qPcBycLXfLpWrOcJcnweqMFC
A+lwOe0H9FBLT0N+2xRtLRX/XTqWlb7jOqJRCrbZmXow
-----END RSA PRIVATE KEY-----';


    $public_key = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDYucsb9ufvvGbdOv/kCMZ4oJfI
z+eDJj6m2bjRzIM8P6sGXLFlaXoJyYNbKi9ZCVTvYlE4i2RWlesSk63YX+1y4o5n
9JjzbfkydZQy97FIaem/QvEPMfq3/A44PDykjtQRO11iIviFJlbR6bAYtbzem+dY
S2cFZ+7XtGItS2ZxKwIDAQAB
-----END PUBLIC KEY-----';


    $private_key_pkcs8 = '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';






    $content = $sendjson;
    $randstr = random(16);
    $iv = $randstr;

    // 使用 openssl_encrypt 替换已废弃的 mcrypt_encrypt (PHP 7.4 兼容)
    $encrypted = openssl_encrypt($content, 'AES-128-CBC', $randstr, OPENSSL_RAW_DATA, $iv);
    $msg = base64_encode($encrypted);

    $pu_key = openssl_pkey_get_public($public_key); //这个函数可用来判断公钥是否是可用的
    openssl_public_encrypt($randstr, $encrypted, $pu_key); //公钥加密
    $encrypted = base64_encode($encrypted);
    $code = $encrypted;

    $pi_key = openssl_pkey_get_private($private_key); //这个函数可用来判断私钥是否是可用的，可用返回资源id Resource id

    openssl_private_encrypt($msg, $encrypted, $pi_key); //私钥加密
    $encrypted = base64_encode($encrypted); //加密后的内容通常含有特殊字符，需要编码转换下，在网络间通过url传输时要注意base64编码是否是url安全的
    $sign = $encrypted;


    $sign = sign($msg, $private_key); //签名函数
    $url = $sendurl;


    $data = $sendfile; //文件数据
    $data['msg'] = $msg;
    $data['sign'] = $sign;
    $data['code'] = $code;
    $content = curlPost($url, $data); //通过curl发送数据到接口
    $content = str_replace(array("\r\n", "\r", "\n", "\0"), "", $content); //替换掉特殊字符

    $arr = json_decode($content, 1);

    //获取接口返回的加密sign、code、msg
    $resign = $arr['sign'];
    $recode = $arr['code'];
    $remsg = $arr['msg'];

    openssl_private_decrypt(base64_decode($recode), $decrypted, $pi_key); //私钥解密
    $returnrandstr = $decrypted;
    $encryptedData = base64_decode($remsg);

    // 使用 openssl_decrypt 替换已废弃的 mcrypt_decrypt (PHP 7.4 兼容)
    $decrypted = openssl_decrypt($encryptedData, 'AES-128-CBC', $returnrandstr, OPENSSL_RAW_DATA, $returnrandstr);
    $returnjson = $decrypted;

    $returnjson = str_replace(array("\r\n", "\r", "\n", "\0"), "", $returnjson);

    $sign = sign($remsg, $private_key);

    if ($sign != $resign) {
        $returnjson = '[{"sta":"0","msg":"签名验证失败客户端1！"}]';
        return $returnjson;
    }

    return $returnjson;
}
/**
 * 把客户端发来的密文转化成明文
 * @param $resign
 * @param $recode
 * @param $remsg
 * @return mixed
 */
function  getparmtoplain($resign, $recode, $remsg)
{

    $private_key = '-----BEGIN RSA PRIVATE KEY-----
MIICXAIBAAKBgQDYucsb9ufvvGbdOv/kCMZ4oJfIz+eDJj6m2bjRzIM8P6sGXLFl
aXoJyYNbKi9ZCVTvYlE4i2RWlesSk63YX+1y4o5n9JjzbfkydZQy97FIaem/QvEP
Mfq3/A44PDykjtQRO11iIviFJlbR6bAYtbzem+dYS2cFZ+7XtGItS2ZxKwIDAQAB
AoGAF8iL2Y3G9jAeubh3zHaKEULOZWMrbOENjxHljmB7Jml2Q0R400A3/Er491Ax
0fuMLpkDgMkLQdVB7s/2DAC69eTR4vsnrQU0/eBikvEV2EEAPiuMQr2r/BYhEAyY
0R4edFjAG1sPCew/FqytSVMSWtxzGVOyB9CoAGI+4nBtCEECQQD4RAETQK9nZFhG
4TdLZv2WTdRcPjd6tLD3RtfQ7ldMtyhgO+WzNjj0zKoTbE3lp9WqDJonf5vTdfZr
S+1J80z/AkEA33o/sEV3G93tEewaAx1AncWFrRcjraMU5Y59Q2iUxWck+ASUPmj9
e4qfBIozBcVgH8YIyl6aLa4X4SzdN9uf1QJBAKdegTMPirCzT2gJcROeTRtFQQMm
1pQcuKkb02cBJ02KtOebudFFnsQ6LfaGL0XCDiNj95DlUJIQlp3jRAd0xNUCQE7l
WNDBZXCII+b85J4O2L4aerwF/EmT1o/Igz6mEEe0x19sNm3mIP9ZjBRNOgAik3IE
NxxC08mmubV8YkkTF1UCQBuLA9MAfqGcp9JOwoo9qPcBycLXfLpWrOcJcnweqMFC
A+lwOe0H9FBLT0N+2xRtLRX/XTqWlb7jOqJRCrbZmXow
-----END RSA PRIVATE KEY-----';


    $public_key = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDYucsb9ufvvGbdOv/kCMZ4oJfI
z+eDJj6m2bjRzIM8P6sGXLFlaXoJyYNbKi9ZCVTvYlE4i2RWlesSk63YX+1y4o5n
9JjzbfkydZQy97FIaem/QvEPMfq3/A44PDykjtQRO11iIviFJlbR6bAYtbzem+dY
S2cFZ+7XtGItS2ZxKwIDAQAB
-----END PUBLIC KEY-----';


    $private_key_pkcs8 = '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';


    $pi_key = openssl_pkey_get_private($private_key); //这个函数可用来判断私钥是否是可用的，可用返回资源id Resource id
    $pu_key = openssl_pkey_get_public($public_key); //这个函数可用来判断公钥是否是可用的

    //解密code
    openssl_private_decrypt(base64_decode($recode), $decrypted, $pi_key); //私钥解密
    $returnrandstr = $decrypted;

    //解密msg
    $encryptedData = base64_decode($remsg);
    // 使用 openssl_decrypt 替换已废弃的 mcrypt_decrypt (PHP 7.4 兼容)
    $decrypted = openssl_decrypt($encryptedData, 'AES-128-CBC', $returnrandstr, OPENSSL_RAW_DATA, $returnrandstr);

    $msgyuanwen = str_replace(array("\r\n", "\r", "\n", "\0"), "", $decrypted);

    logRes("传入密文签名sign:" . $resign . "\r\n传入密文code:" . $recode . "\r\n传入密文msg:" . $remsg . "\r\n解析后的明文：" . $msgyuanwen, "lzt");

    $sign = sign($remsg, $private_key);
    $signrs = verity($remsg, $resign, $public_key);
    if (!$signrs) {
        $returnjson = '[{"sta":"0","msg":"签名验证失败服务端2！"}]';
        sendtolzt($returnjson);
        die;
    }

    return $msgyuanwen;
    openssl_public_decrypt(base64_decode($resign), $decrypted, $pu_key); //私钥加密的内容通过公钥可用解密出来
}




/**
 * 把通过业务逻辑转化后的明文数据加密后返还给客户端
 * @param $returnjson
 * @param string $logname
 */
function sendtolzt($returnjson, $logname = '')
{


    $private_key = '-----BEGIN RSA PRIVATE KEY-----
MIICXAIBAAKBgQDYucsb9ufvvGbdOv/kCMZ4oJfIz+eDJj6m2bjRzIM8P6sGXLFl
aXoJyYNbKi9ZCVTvYlE4i2RWlesSk63YX+1y4o5n9JjzbfkydZQy97FIaem/QvEP
Mfq3/A44PDykjtQRO11iIviFJlbR6bAYtbzem+dYS2cFZ+7XtGItS2ZxKwIDAQAB
AoGAF8iL2Y3G9jAeubh3zHaKEULOZWMrbOENjxHljmB7Jml2Q0R400A3/Er491Ax
0fuMLpkDgMkLQdVB7s/2DAC69eTR4vsnrQU0/eBikvEV2EEAPiuMQr2r/BYhEAyY
0R4edFjAG1sPCew/FqytSVMSWtxzGVOyB9CoAGI+4nBtCEECQQD4RAETQK9nZFhG
4TdLZv2WTdRcPjd6tLD3RtfQ7ldMtyhgO+WzNjj0zKoTbE3lp9WqDJonf5vTdfZr
S+1J80z/AkEA33o/sEV3G93tEewaAx1AncWFrRcjraMU5Y59Q2iUxWck+ASUPmj9
e4qfBIozBcVgH8YIyl6aLa4X4SzdN9uf1QJBAKdegTMPirCzT2gJcROeTRtFQQMm
1pQcuKkb02cBJ02KtOebudFFnsQ6LfaGL0XCDiNj95DlUJIQlp3jRAd0xNUCQE7l
WNDBZXCII+b85J4O2L4aerwF/EmT1o/Igz6mEEe0x19sNm3mIP9ZjBRNOgAik3IE
NxxC08mmubV8YkkTF1UCQBuLA9MAfqGcp9JOwoo9qPcBycLXfLpWrOcJcnweqMFC
A+lwOe0H9FBLT0N+2xRtLRX/XTqWlb7jOqJRCrbZmXow
-----END RSA PRIVATE KEY-----';


    $public_key = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDYucsb9ufvvGbdOv/kCMZ4oJfI
z+eDJj6m2bjRzIM8P6sGXLFlaXoJyYNbKi9ZCVTvYlE4i2RWlesSk63YX+1y4o5n
9JjzbfkydZQy97FIaem/QvEPMfq3/A44PDykjtQRO11iIviFJlbR6bAYtbzem+dY
S2cFZ+7XtGItS2ZxKwIDAQAB
-----END PUBLIC KEY-----';


    $private_key_pkcs8 = '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

    $arr = json_decode($returnjson, 1);
    $cleanarr = null_filter($arr); //把null转化成空字符串
    $returnjson = json_encode($cleanarr, JSON_UNESCAPED_UNICODE);


    $content = $returnjson;
    $randstr = random(16);
    $iv = $randstr;

    // 使用 openssl_encrypt 替换已废弃的 mcrypt_encrypt (PHP 7.4 兼容)
    $encrypted = openssl_encrypt($content, 'AES-128-CBC', $randstr, OPENSSL_RAW_DATA, $iv);
    $msg = base64_encode($encrypted);

    $pu_key = openssl_pkey_get_public($public_key); //这个函数可用来判断公钥是否是可用的
    openssl_public_encrypt($randstr, $encrypted, $pu_key); //公钥加密
    $encrypted = base64_encode($encrypted);
    $code = $encrypted;

    $pi_key = openssl_pkey_get_private($private_key); //这个函数可用来判断私钥是否是可用的，可用返回资源id Resource id
    openssl_private_encrypt($msg, $encrypted, $pi_key); //私钥加密
    $encrypted = base64_encode($encrypted); //加密后的内容通常含有特殊字符，需要编码转换下，在网络间通过url传输时要注意base64编码是否是url安全的

    $sign = sign($msg, $private_key);

    $data['msg'] = $msg;
    $data['sign'] = $sign;
    $data['code'] = $code;

    logRes("返回给客户端的明文：" . $returnjson . "\r\n返回密文签名sign:" . $sign . "\r\n返回密文code:" . $code . "\r\n返回密文msg:" . $msg, "lzt");
    echo json_encode($data);
    die;
}

/**
 *签名数据：
 *data：utf-8编码的订单原文，
 *privatekeyFile：私钥路径
 *passphrase：私钥密码
 *返回：base64转码的签名数据
 */

function sign($data, $privatekey)
{
    $signature = '';
    $res = openssl_get_privatekey($privatekey);
    openssl_sign($data, $signature, $res);
    openssl_free_key($res);

    return base64_encode($signature);
}


/**
 * 验证签名：
 *data：原文
 *signature：签名
 *publicKeyPath：公钥路径
 *返回：签名结果，true为验签成功，false为验签失败
 */
function verity($data, $signature, $pubKey)
{
    $res = openssl_get_publickey($pubKey);
    $result = (bool)openssl_verify($data, base64_decode($signature), $res);
    openssl_free_key($res);
    return $result;
}


function null_filter($arr)
{
    foreach ($arr as $key => &$val) {
        if (is_array($val)) {
            $val = null_filter($val);
        } else {
            if ($val === null) {
                //unset($arr[$key]);
                $arr[$key] = '';
            }
        }
    }
    return $arr;
}


/**
 * 随机字符
 * @param number $length 长度
 * @param string $type 类型
 * @param number $convert 转换大小写
 * @return string
 */
function random($length = 6, $type = 'string', $convert = 0)
{
    $config = array(
        'number' => '1234567890',
        'letter' => 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
        'string' => 'abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ23456789',
        'all' => 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890'
    );

    if (!isset($config[$type])) $type = 'string';
    $string = $config[$type];

    $code = '';
    $strlen = strlen($string) - 1;
    for ($i = 0; $i < $length; $i++) {
        $code .= $string{
            mt_rand(0, $strlen)};
    }
    if (!empty($convert)) {
        $code = ($convert > 0) ? strtoupper($code) : strtolower($code);
    }
    return $code;
}


/*
* 后台密码加密
*/
function admin_encrypt($str)
{
    $key = "9C654249";
    $iv = "9C654249";
    //加密，返回大写十六进制字符串 - 使用 openssl 替换已废弃的 mcrypt (PHP 7.4 兼容)
    $blocksize = 8; // DES 块大小为 8 字节
    $str = pkcs5Pad($str, $blocksize);
    // 使用 openssl_encrypt 替换 mcrypt_encrypt
    $encrypted = openssl_encrypt($str, 'DES-CBC', $key, OPENSSL_RAW_DATA, $iv);
    return strtoupper(md5(strtoupper(bin2hex($encrypted))));
}
function pkcs5Pad($text, $blocksize)
{
    $pad = $blocksize - (strlen($text) % $blocksize);
    return $text . str_repeat(chr($pad), $pad);
}


/**
 * 创建表
 */
function  create_msyql_table_client_activity_grade_phone($activity_code)
{
    if ($activity_code == "") {
        return 0;
    }
    $sql = <<<sql
   CREATE TABLE `client_activity_grade_phone_$activity_code` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `phone` char(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
        `status` int(11) NULL DEFAULT NULL COMMENT '1开启，0关闭',
        `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
        `clientkeynum` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
        `o` int(11) NULL DEFAULT NULL COMMENT '排序号越小越靠前',
        `add_time` int(11) NULL DEFAULT NULL COMMENT '添加时间',
        `mod_time` int(11) NULL DEFAULT NULL COMMENT '修改时间',
        `project_id` int(11) NULL DEFAULT NULL COMMENT '项目id',
        `activity_id` int(11) NULL DEFAULT NULL COMMENT '活动的id',
        `batch_id` int(11) NULL DEFAULT NULL COMMENT '批次id',
        `grade_id` int(11) NULL DEFAULT NULL COMMENT '档次id',
        `order_sn` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单号，关联号',
        `order_keynum` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单的keynum',
        `order_id` int(11) NULL DEFAULT NULL COMMENT '订单的id',
        `is_order` int(11) NULL DEFAULT 0 COMMENT '1为生成订单，0未生成订单',
        `order_time` int(11) NULL DEFAULT NULL COMMENT '生成订单时间',
        PRIMARY KEY (`id`) USING BTREE,
        UNIQUE INDEX `phone`(`phone`) USING BTREE COMMENT '一个活动手机号唯一'
      ) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '平台客户的活动里档次里达标手机号，动态创建此表' ROW_FORMAT = Compact;
sql;
    try {
        Db::execute($sql);
        return 1;
    } catch (Exception $e) {
        return 0;
    }
}


/**
 * 模板日期格式化函数
 */
function date_format_tpl($strtime)
{
    if ($strtime == '') {
        return  "";
    }
    return  date("Y-m-d H:i:s", $strtime);
}


/**
 * 获得访客操作系统
 */
function get_os()
{
    if (!empty($_SERVER['HTTP_USER_AGENT'])) {
        $os = $_SERVER['HTTP_USER_AGENT'];
        if (preg_match('/win/i', $os)) {
            $os = 'Windows';
        } else if (preg_match('/mac/i', $os)) {
            $os = 'MAC';
        } else if (preg_match('/linux/i', $os)) {
            $os = 'Linux';
        } else if (preg_match('/unix/i', $os)) {
            $os = 'Unix';
        } else if (preg_match('/bsd/i', $os)) {
            $os = 'BSD';
        } else {
            $os = 'Other';
        }
        return $os;
    } else {
        return 'unknow';
    }
}

/**
 * 获得访问者浏览器
 */
function get_browser1()
{
    if (!empty($_SERVER['HTTP_USER_AGENT'])) {
        $br = $_SERVER['HTTP_USER_AGENT'];
        if (preg_match('/MSIE/i', $br)) {
            $br = 'MSIE';
        } else if (preg_match('/Firefox/i', $br)) {
            $br = 'Firefox';
        } else if (preg_match('/Chrome/i', $br)) {
            $br = 'Chrome';
        } else if (preg_match('/Safari/i', $br)) {
            $br = 'Safari';
        } else if (preg_match('/Opera/i', $br)) {
            $br = 'Opera';
        } else {
            $br = 'Other';
        }
        return $br;
    } else {
        return 'unknow';
    }
}



/**
 * 获取登录地址
 */
function get_location($ip)
{
    return null;
    // $str = "http://ip2region.wlphp.com/api.php?ip=" . $ip;
    // $rs = file_get_contents($str);
    // $arr = json_decode($rs, 1);
    // return $arr['region'];
}


/**
 * 切除过长的字符串，用...代替
 * @param $string       字符串
 * @param $sublen       设置长度
 * @param int $start 开始位置
 * @param string $code
 * @return string       返回字符串结果
 */
function cut_str($string, $sublen, $start = 0, $code = 'UTF-8')
{
    if ($code == 'UTF-8') {
        $pa = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|\xe0[\xa0-\xbf][\x80-\xbf]|[\xe1-\xef][\x80-\xbf][\x80-\xbf]|\xf0[\x90-\xbf][\x80-\xbf][\x80-\xbf]|[\xf1-\xf7][\x80-\xbf][\x80-\xbf][\x80-\xbf]/";
        preg_match_all($pa, $string, $t_string);
        if (count($t_string[0]) - $start > $sublen) {
            return join('', array_slice($t_string[0], $start, $sublen)) . "...";
        }

        return join('', array_slice($t_string[0], $start, $sublen));
    } else {
        $start = $start * 2;
        $sublen = $sublen * 2;
        $strlen = strlen($string);
        $tmpstr = '';
        for ($i = 0; $i < $strlen; $i++) {
            if ($i >= $start && $i < ($start + $sublen)) {
                if (ord(substr($string, $i, 1)) > 129) {
                    $tmpstr .= substr($string, $i, 2);
                } else {
                    $tmpstr .= substr($string, $i, 1);
                }
            }
            if (ord(substr($string, $i, 1)) > 129) {
                $i++;
            }
        }
        if (strlen($tmpstr) < $strlen) {
            $tmpstr .= "...";
        }

        return $tmpstr;
    }
}



//有时候一些项目运行在apache或者php-fpm环境，无法使用workerman/redis-queue项目，可以参考如下函数实现发送
function redis_queue_send($redis, $queue, $data, $delay = 0)
{
    $queue_waiting = 'redis-queue-waiting';
    $queue_delay = 'redis-queue-delayed';
    $now = time();
    $package_str = json_encode([
        'id'       => rand(),
        'time'     => $now,
        'delay'    => 0,
        'attempts' => 0,
        'queue'    => $queue,
        'data'     => $data
    ]);
    if ($delay) {
        return $redis->zAdd($queue_delay, $now + $delay, $package_str);
    }
    return $redis->lPush($queue_waiting . $queue, $package_str);
}


function strip_html_tags($tags, $str)
{
    $html = array();
    foreach ($tags as $tag) {
        $html[] = '/<' . $tag . '.*?>[\s|\S]*?<\/' . $tag . '>/';
        $html[] = '/<' . $tag . '.*?>/';
    }
    $data = preg_replace($html, '', $str);
    return $data;
}

//记录接口日志函数
function do_api_log($request, $return_arr, $applicationinfo)
{
    $data['appid'] = $applicationinfo['appid'];
    $data['action'] = $request['action'];
    $data['get_data'] = json_encode($request, JSON_UNESCAPED_UNICODE);
    $data['send_data'] = json_encode($return_arr, JSON_UNESCAPED_UNICODE);
    $data['application'] = json_encode($applicationinfo, JSON_UNESCAPED_UNICODE);
    $data['add_time'] = date("Y-m-d H:i:s");
    $data['addtime'] = time();
    Db::table('client_api_log')->insert($data);
}

function ycard_status($status)
{
    // 生成卡号是5 铺卡后是0 开卡后是1 关卡是2 使用是3 退款是6
    $str = "";
    switch ($status) {
        case 1:
            $str = '已销售';
            break;
        case 2:
            $str = '已退卡';
            break;
        case 3:
            $str = '已开卡';
            break;
        case 4:
            $str = '已关卡';
            break;
        case -1:
            $str = '已废卡';
            break;
        case 5:
            $str = '未销售';
            break;
        default:
            $str = "出错了";
            break;
    }
    return $str;
}

/**
 * [生成随机字符串]
 * @E-mial <EMAIL>
 * @TIME   2017-04-07
 * @WEB    http://blog.iinu.com.cn
 * @param  integer $length [生成的长度]
 * @param  integer $type   [生成的类型]
 * @return [type]   str       [description]
 * @php 随机码类型：0，数字+大写字母；1，数字；2，小写字母；3，大写字母；4，特殊字符；-1，数字+大小写字母+特殊字符
 */
function randCode($length = 5, $type = 0)
{
    $code = "";
    $arr = array(1 => "0123456789", 2 => "abcdefghijklmnopqrstuvwxyz", 3 => "ABCDEFGHIJKLMNOPQRSTUVWXYZ");
    if ($type == 0) {
        array_pop($arr);
        $string = implode("", $arr);
    } else if ($type == "-1") {
        $string = implode("", $arr);
    } else {
        $string = $arr[$type];
    }
    $count = strlen($string) - 1;
    for ($i = 0; $i < $length; $i++) {
        $str[$i] = $string[rand(0, $count)];
        $code .= $str[$i];
    }
    return $code;
}

///二维码
function createImg($url = '')
{

    vendor('phpqrcode.phpqrcode'); //引入类库tp5
    //        require "../vendor/phpqccode/phpqrcode.php";//tp6引入类库方法

    $value = $url;         //二维码内容
    $errorCorrectionLevel = 'L';  //容错级别
    $matrixPointSize = 5;      //生成图片大小
    //生成二维码图片
    // 判断是否有这个文件夹  没有的话就创建一个
    if (!is_dir("qrcode")) {
        // 创建文件加
        mkdir("qrcode");
    }
    //设置二维码文件名
    $filename = './qrcode/' . time() . rand(10000, 9999999) . '.png';
    //生成二维码
    \QRcode::png($value, $filename, $errorCorrectionLevel, $matrixPointSize, 2);
    return $filename;
}

/*生成小程序二维码*/
function create_wx_qrcode($code, $cardnumber)
{
    //平台客户的keynum
    $clientkeynum = session("cn_accountinfo.basekeynum");
    $wx_config = Db::table("plat_weixin_set")->where("basekeynum='$clientkeynum'")->find();
    // 先获取access_token
    $url = "https://api.weixin.qq.com/cgi-bin/token?appid=" . $wx_config["appid"] .
        "&secret=" . $wx_config["appsecret"] . "&grant_type=client_credential";
    $rs = file_get_contents($url);
    $rs = json_decode($rs, 1);
    $access_token = $rs["access_token"];
    $url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" . $access_token;
    $data["scene"] = "code=" . $code;
    $data["page"] = "pages/home/<USER>";
    // $data["env_version"] = "trial";
    $data["is_hyaline"] = true;
    $data = json_encode($data);
    $rs = curlPost($url, $data);
    $filename = $cardnumber; //create_guid();
    $img_name = $filename . ".png";
    $dir = "./qrcode" . "/" . $clientkeynum . "/";
    if (!is_dir($dir)) {
        mkdir($dir, 0777, true);
    }
    if ($fp = fopen($dir . $img_name, 'w')) {
        if (fwrite($fp, $rs)) {
            fclose($fp);
        }
    }
    $arr = uploadFileToAliOss($dir . $img_name, "qrcode/" . $clientkeynum . "/" . $img_name);
    if ($arr['sta'] != '1') {
        return  $arr['msg'];
        die;
    }
    unlink($dir . $img_name);
    return  $arr['url'];
    die;
    // $new_dir = "/" . "qrcode" . "/" . $clientkeynum . "/" . $img_name;
    // return $new_dir;
}

function send_to_wuliu($appid, $appkey, $body, $url)
{
    $arr['appId'] = $appid;
    if (!empty($body)) {
        $arr['param'] = $body;
        $param_str = json_encode($body, JSON_UNESCAPED_UNICODE);
        $param_str = str_replace("\\", "", $param_str);
    }
    $arr['nonceStr'] = createNoncestr_wl();
    $arr['timeStamp'] = time();
    $String = $arr['appId'] . $param_str . $arr['nonceStr'] . $arr['timeStamp'] . $appkey;
    // echo $String;die;
    //签名步骤三：MD5加密
    $String = md5($String);
    //签名步骤四：所有字符转为大写
    $sign = strtoupper($String);
    $arr['sign'] = $sign;
    $arr = json_encode($arr, JSON_UNESCAPED_UNICODE);
    // print_r($arr);die;
    $result = curlPostJson($url, $arr);
    logRes("对接增值平台;发送地址:" . $url . "发送内容:" . $arr . "返回结果:" . $result);
    return $result;
}

/**
 * @param $url 发送post请求的url
 * @param $jsonStr 发送的数据
 * @return mixed
 */
function curlPostJson($url, $jsonStr)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt(
        $ch,
        CURLOPT_HTTPHEADER,
        array(
            'Content-Type: application/json; charset=utf-8',
            'Content-Length: ' . strlen($jsonStr)
        )
    );
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    return $response;
    // return array($httpCode, $response);
}


// 订阅物流轨迹
function wuliu_dingyue($orderid, $basekeynum)
{
    $config = db("wuliu_config")->where("clientkeynum='$basekeynum'")->find();
    if ($config["status"] != '1') {
        logRes("订阅失败,未开启订阅开关");
        return;
    }
    if (empty($config["join_url"]) or empty($config["appid"]) or empty($config["appkey"])) {
        logRes("订阅失败,配置项为空");
        return;
    }
    $orderinfo = db("client_order_orderdetail")
        ->where("id='$orderid' and clientkeynum='$basekeynum' and status='3'")->field("shipping_name,shipping_num,clientkeynum,order_sn")->find();
    $phone = db("client_order_info")
        ->where(['order_sn' => $orderinfo['order_sn'], 'clientkeynum' => $basekeynum])
        ->value("phone");

    if (empty($orderinfo) or empty($orderinfo["shipping_name"]) or empty($orderinfo["shipping_num"])) {
        logRes("订阅失败,订单信息为空");
        return;
    }
    $body = array();
    $add = array();

//    foreach ($orderinfo as $key => $value) {
        $shipping_name = $orderinfo["shipping_name"];
        $shipping_num = $orderinfo["shipping_num"];
        $find = db("plat_order_wuliu")
            ->where("orderid='$orderid' and express='$shipping_name' and expressnum='$shipping_num' and status not in (0,99)")->value("id");

        if ($find) {
            return;
        }
        $express_code = "暂无";
        // 判断如果快递名称等于未知 调取接口获取快递
        $add_one = array();
        // 判断快递单号是否有重复的，可能多个订单是同一个快递单号，不重复再进行订阅，否则只插入本系统记录
        $find2 = db("plat_order_wuliu")->where("express='$shipping_name' and expressnum='$shipping_num' and status not in (0,99)")->value("id");
        if (!$find2) {
            $body_one["com"] = $express_code;
            $body_one["num"] = $orderinfo;
            $body_one["callBackUrl"] = "http://" . $_SERVER["HTTP_HOST"] . "/admin.php/cnapi/wuliu_callback";
            $body_one["recPhone"] = $phone;
            $body[] = $body_one;
        }
        $add_one["orderid"] = $orderid;
        $add_one["express"] = $shipping_name;
        $add_one["express_code"] = $express_code;
        $add_one["expressnum"] = $orderinfo;
        $add_one["status"] = '0';
        $add_one["factorykeynum"] = $basekeynum;
        $add_one["clientkeynum"] = $orderinfo["clientkeynum"];
        $add_one["create_time"] = time();
        $add[] = $add_one;
//    }
    if (!empty($body)) {
        $action = "/prod-api/openapi/express/subscriberecord/moredata";
        $appid = $config["appid"];
        $appkey = $config["appkey"];
        $url = $config["join_url"] . $action;
        $rs = send_to_wuliu($appid, $appkey, $body, $url);
        $rs_arr = json_decode($rs, 1);
    } else {
        $rs_arr["code"] = '200';
        $rs = json_encode($rs_arr);
    }
    // 本地插入记录
    foreach ($add as $ak => &$av) {
        $add[$ak]["log"] = $rs;
        if ($rs_arr["code"] == '200') {
            $add[$ak]["status"] = '1';
        }
    }
    $save["status"] = 99;
    db("plat_order_wuliu")->where("orderid='$orderid' and status='0'")->update($save);
    db("plat_order_wuliu")->insertAll($add);
}


function zip_download($files)
{
    $picAllArr = $files;

    $tmpDir = 'zip/'; // 类似于/www/public/upload/

    if (!file_exists($tmpDir)) {
        //创建文件夹
        mkdir($tmpDir, 0777, true);
    }

    $zipName = date('His') . mt_rand(1000, 9999) . '.zip'; // 压缩包文件名
    $zipNameUrl = $tmpDir . $zipName; // 文件路径

    // 生成文件
    $zip = new \ZipArchive();
    if ($zip->open($zipNameUrl, \ZipArchive::OVERWRITE) !== true) {
        //OVERWRITE 参数会覆写压缩包的文件 文件必须已经存在
        if ($zip->open($zipNameUrl, \ZipArchive::CREATE) !== true) {
            // 文件不存在则生成一个新的文件 用CREATE打开文件会追加内容至zip
            return  '下载失败，文件夹不存在';
        }
    }

    foreach ($picAllArr as $file) {
        //抓取图片内容
        $fileContent = file_get_contents($file);
        //添加图片
        $zip->addFromString(basename($file), $fileContent);
    }
    // 关闭
    $zip->close();

    //没有文件
    if (!file_exists($zipNameUrl)) {
        return '下载失败，图片不存在或无法下载';
    }

    header("Cache-Control: public");
    header("Content-Description: File Transfer");
    header('Content-disposition: attachment; filename=' . $zipName); //文件名
    header("Content-Type: application/zip"); //zip格式的
    header("Content-Transfer-Encoding: binary"); //告诉浏览器，这是二进制文件
    header('Content-Length: ' . filesize($zipNameUrl)); //告诉浏览器，文件大小

    // 下面2步必须要
    ob_clean();
    flush();

    @readfile($zipNameUrl);
    unlink($zipNameUrl); // 删除文件
    exit;
}

function create_sku()
{

    $charid = strtoupper(md5(uniqid(mt_rand(), true)));
    //$hyphen = chr(45);// "-"
    $hyphen = "";
    $uuid = substr($charid, 6, 2) . substr($charid, 4, 2) .
        substr($charid, 2, 2) . substr($charid, 0, 2) . $hyphen;
    return $uuid;
}
