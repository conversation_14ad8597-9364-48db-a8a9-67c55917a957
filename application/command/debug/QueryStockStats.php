<?php
namespace app\command\debug;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\facade\Log;

class QueryStockStats extends Command
{
    protected function configure()
    {
        $this->setName('debug:query_stock_stats')
            ->setDescription('查询库存统计数据');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("开始查询库存统计数据...");

        // 构建查询条件
        $where = [];

        // 统计数据
        $stats = Db::name('shop_product_inventory')->alias('spi')
            ->join('products p', 'p.id = spi.product_id')
            ->where($where)
            ->field([
                'count(distinct spi.product_id) as total_products',
                'sum(case when p.product_type IN (1, 3) then spi.stock else 0 end) as total_normal_stock',
                'sum(case when p.product_type = 2 then spi.weight_stock else 0 end) as total_weight_stock',
                'count(case when p.product_type IN (1, 3) then 1 end) as normal_product_count',
                'count(case when p.product_type = 2 then 1 end) as weight_product_count'
            ])
            ->find();

        // 格式化输出
        $output->writeln("总商品数量: " . ($stats['total_products'] ?? 0));
        $output->writeln("普通商品数量: " . ($stats['normal_product_count'] ?? 0));
        $output->writeln("计量商品数量: " . ($stats['weight_product_count'] ?? 0));
        $output->writeln("普通商品总库存: " . round($stats['total_normal_stock'] ?? 0, 2) . " 件");
        $output->writeln("计量商品总重量: " . round($stats['total_weight_stock'] ?? 0, 2) . " kg");
        
        // 打印SQL语句
        $lastSql = Db::getLastSql();
        $output->writeln("SQL语句: " . $lastSql);

        $output->writeln("查询结束");
    }
} 