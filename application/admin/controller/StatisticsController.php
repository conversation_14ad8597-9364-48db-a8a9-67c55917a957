<?php
/**
 * 统计控制器
 * 
 * 功能说明：
 * 1. 提供店铺和商品的统计功能
 * 2. 包括数量统计、状态统计、趋势分析、排行榜等
 * 
 * <AUTHOR>
 * @date 2023-03-06
 */
namespace app\admin\controller;

use app\admin\controller\CnController;
use think\Request;
use think\Db;
use think\facade\Cache;

class StatisticsController extends CnController
{
    public function initialize()
    {
        $this->init();
    }
    
    public function shop_statistics(){
        check_auth(request()->controller() . '/shop_statistics', 0);
        return $this->fetch();
    }

    public function ajax_shop_statistics(){
        check_auth(request()->controller() . '/shop_statistics', 1);
        $request = request();
        $param = $request->param();
        
        $basekeynum = session("cn_accountinfo.basekeynum");
        $data = [];
        
        // 获取店铺总数
        $data['shop_count'] = Db::table('shop')->where('clientkeynum', $basekeynum)->count();
        
        // 获取商品总数
        $data['product_count'] = Db::table('products')->where('clientkeynum', $basekeynum)->count();
        
        // 获取上架商品数
        $data['product_online_count'] = Db::table('products')
            ->where('clientkeynum', $basekeynum)
            ->where('state', 1)
            ->count();
        
        // 获取下架商品数
        $data['product_offline_count'] = Db::table('products')
            ->where('clientkeynum', $basekeynum)
            ->where('state', -1)
            ->count();
        
        // 获取库存商品数
        $data['product_stock_count'] = Db::table('products')
            ->where('clientkeynum', $basekeynum)
            ->where('kucun', '>', 0)
            ->count();
        
        // 获取缺货商品数
        $data['product_out_of_stock_count'] = Db::table('products')
            ->where('clientkeynum', $basekeynum)
            ->where('kucun', '=', 0)
            ->count();
        
        // 获取店铺商品关联数
        $data['shop_product_count'] = Db::table('shop_product')
            ->where('clientkeynum', $basekeynum)
            ->count();
        
        // 获取店铺上架商品数
        $data['shop_product_online_count'] = Db::table('shop_product')
            ->where('clientkeynum', $basekeynum)
            ->where('status', 1)
            ->count();
        
        // 获取最近7天新增商品数
        $data['recent_product_count'] = Db::table('products')
            ->where('clientkeynum', $basekeynum)
            ->whereTime('created_at', '>=', date('Y-m-d', strtotime('-7 days')))
            ->count();
        
        // 获取最近7天新增店铺数
        $data['recent_shop_count'] = Db::table('shop')
            ->where('clientkeynum', $basekeynum)
            ->whereTime('add_time', '>=', date('Y-m-d', strtotime('-7 days')))
            ->count();
        
        // 获取最近7天的商品增长趋势
        $recent_days = [];
        $recent_product_trend = [];
        
        for ($i = 6; $i >= 0; $i--) {
            $day = date('Y-m-d', strtotime("-$i days"));
            $recent_days[] = $day;
            
            $count = Db::table('products')
                ->where('clientkeynum', $basekeynum)
                ->whereTime('created_at', 'between', [$day . ' 00:00:00', $day . ' 23:59:59'])
                ->count();
            
            $recent_product_trend[] = $count;
        }
        
        $data['recent_days'] = $recent_days;
        $data['recent_product_trend'] = $recent_product_trend;
        
        // 获取商品分类统计
        $category_stats = Db::table('products')
            ->alias('p')
            ->join('product_category c', 'p.category_id = c.id')
            ->where('p.clientkeynum', $basekeynum)
            ->field('c.id, c.title, COUNT(p.id) as count')
            ->group('c.id')
            ->order('count DESC')
            ->limit(10)
            ->select();
        
        $data['category_stats'] = $category_stats;
        
        // 获取店铺商品数量排行
        $shop_product_rank = Db::table('shop_product')
            ->alias('sp')
            ->join('shop s', 'sp.shop_id = s.id')
            ->where('sp.clientkeynum', $basekeynum)
            ->field('s.id, s.title, COUNT(sp.id) as count')
            ->group('s.id')
            ->order('count DESC')
            ->limit(10)
            ->select();
        
        $data['shop_product_rank'] = $shop_product_rank;
        
        return json($data);
    }

    public function order_statistics(){
        check_auth(request()->controller() . '/order_statistics', 0);
        return $this->fetch();
    }
    
    /**
     * 获取门店列表 - 用于筛选下拉框
     */
    public function ajax_get_shops(){
        check_auth(request()->controller() . '/order_statistics', 1);
        $basekeynum = session("cn_accountinfo.basekeynum");
        
        $shops = Db::table('shop')
            ->where('clientkeynum', $basekeynum)
            ->field('id, title')
            ->order('add_time desc')
            ->select();
            
        return json(['shops' => $shops]);
    }

    public function ajax_order_statistics(){
        check_auth(request()->controller() . '/order_statistics', 1);
        $request = request();
        $param = $request->param();
        
        $basekeynum = session("cn_accountinfo.basekeynum");
        $data = [];
        
        // 获取时间范围参数，支持直接传入开始和结束日期
        $start_date = isset($param['start_date']) ? $param['start_date'] : '';
        $end_date = isset($param['end_date']) ? $param['end_date'] : '';
        $time_range = isset($param['time_range']) ? $param['time_range'] : '';
        
        // 获取筛选参数
        $shop_id = isset($param['shop_id']) ? intval($param['shop_id']) : 0; // 门店ID，0表示全部
        $delivery_type = isset($param['delivery_type']) ? intval($param['delivery_type']) : 0; // 配送方式，0表示全部，1自提，2配送，3结算台
        
        // 如果没有直接传入日期，则根据time_range参数计算
        if (empty($start_date) || empty($end_date)) {
            // 兼容原有的time_range参数
            $time_range = $time_range ?: '7';
            
            // 根据时间范围设置开始日期
            switch ($time_range) {
                case 'today':
                    $start_date = date('Y-m-d'); // 今天
                    break;
                case '7':
                    $start_date = date('Y-m-d', strtotime('-7 days'));
                    break;
                case '30':
                    $start_date = date('Y-m-d', strtotime('-30 days'));
                    break;
                case '180':
                    $start_date = date('Y-m-d', strtotime('-180 days'));
                    break;
                case '365':
                    $start_date = date('Y-m-d', strtotime('-365 days'));
                    break;
                case 'all':
                    $start_date = '1970-01-01'; // 从很早的日期开始，相当于"至今"
                    break;
                default:
                    $start_date = date('Y-m-d', strtotime('-7 days'));
                    break;
            }
            $end_date = date('Y-m-d');
        } else {
            // 如果传入了具体日期，验证格式
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $start_date) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $end_date)) {
                return json(['error' => '日期格式错误，请使用YYYY-MM-DD格式']);
            }
            
            // 确保开始日期不晚于结束日期
            if (strtotime($start_date) > strtotime($end_date)) {
                $temp = $start_date;
                $start_date = $end_date;
                $end_date = $temp;
            }
            
            // 设置time_range为custom，表示自定义时间范围
            $time_range = 'custom';
        }
        
        // 获取店铺排行显示数量，默认为10
        $shop_limit = isset($param['shop_limit']) ? intval($param['shop_limit']) : 10;
        
        // 构建通用的筛选条件
        $base_where = [
            ['clientkeynum', '=', $basekeynum]
        ];
        
        // 添加门店筛选
        if ($shop_id > 0) {
            $base_where[] = ['shop_id', '=', $shop_id];
        }
        
        // 添加配送方式筛选
        if ($delivery_type > 0) {
            $base_where[] = ['type', '=', $delivery_type];
        }
        
        $data['time_range'] = $time_range;
        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;
        $data['shop_id'] = $shop_id;
        $data['delivery_type'] = $delivery_type;
        
        // 获取订单总数（排除待支付、待审核、已取消订单，按时间范围筛选）
        $data['order_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->whereNotIn('status', [0, 1, -1])
            ->count();
        
        // 获取各状态订单数量（按时间范围筛选）
        $data['pending_payment_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('status', 0)
            ->count();
            
        $data['pending_review_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('status', 1)
            ->count();
            
        $data['accepted_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('status', 2)
            ->count();
            
        $data['delivering_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('status', 3)
            ->count();
            
        $data['completed_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('status', 100)
            ->count();
            
        $data['cancelled_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('status', -1)
            ->count();
        
        // 获取订单支付方式统计（排除待支付、待审核、已取消订单，按时间范围筛选）
        $data['wechat_payment_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('pay_type', 1)
            ->whereNotIn('status', [0, 1, -1])
            ->count();
            
        $data['card_payment_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('pay_type', 2)
            ->whereNotIn('status', [0, 1, -1])
            ->count();
            
        $data['combined_payment_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('pay_type', 3)
            ->whereNotIn('status', [0, 1, -1])
            ->count();
            
        $data['cash_payment_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('pay_type', 4)
            ->whereNotIn('status', [0, 1, -1])
            ->count();
        
        // 获取订单趋势数据
        $recent_days = [];
        $recent_order_trend = [];
        
        // 根据时间范围确定趋势图的数据点数量
        $days_count = 0;
        switch ($time_range) {
            case 'today':
                $days_count = 24; // 今天按小时显示，24个点
                break;
            case '7':
                $days_count = 7;
                break;
            case '30':
                $days_count = 30;
                break;
            case '180':
                $days_count = 30; // 对于180天，每6天取一个点
                break;
            case '365':
                $days_count = 30; // 对于365天，每12天取一个点
                break;
            case 'all':
                $days_count = 30; // 对于"至今"，取30个点
                break;
            case 'custom':
                // 自定义时间范围，根据天数动态计算
                $total_days = (strtotime($end_date) - strtotime($start_date)) / (24 * 3600) + 1;
                if ($total_days <= 7) {
                    $days_count = $total_days;
                } elseif ($total_days <= 30) {
                    $days_count = $total_days;
                } elseif ($total_days <= 90) {
                    $days_count = 30; // 每3天一个点
                } elseif ($total_days <= 365) {
                    $days_count = 30; // 每12天一个点
                } else {
                    $days_count = 30; // 超过一年，每月一个点
                }
                break;
            default:
                $days_count = 7;
                break;
        }
        
        // 计算每个数据点的间隔天数
        $interval = 1;
        if ($time_range == '180') {
            $interval = 6;
        } else if ($time_range == '365') {
            $interval = 12;
        } else if ($time_range == 'all') {
            // 计算从最早订单到现在的总天数
            $first_order = Db::table('order')
                ->where('clientkeynum', $basekeynum)
                ->order('add_time asc')
                ->find();
            
            if ($first_order) {
                $first_date = date('Y-m-d', strtotime($first_order['add_time']));
                $total_days = (strtotime($end_date) - strtotime($first_date)) / (24 * 3600);
                $interval = max(1, floor($total_days / 30));
            } else {
                $interval = 30;
            }
        } else if ($time_range == 'custom') {
            // 自定义时间范围，根据总天数计算间隔
            $total_days = (strtotime($end_date) - strtotime($start_date)) / (24 * 3600) + 1;
            if ($total_days <= 30) {
                $interval = 1; // 30天内，每天一个点
            } elseif ($total_days <= 90) {
                $interval = max(1, floor($total_days / 30)); // 90天内，大约30个点
            } elseif ($total_days <= 365) {
                $interval = max(1, floor($total_days / 30)); // 一年内，大约30个点
            } else {
                $interval = max(1, floor($total_days / 30)); // 超过一年，大约30个点
            }
        }
        
        // 生成趋势图数据
        if ($time_range == 'today') {
            // 今天按小时统计
            for ($i = 0; $i < 24; $i++) {
                $hour = sprintf('%02d:00', $i);
                $recent_days[] = $hour;
                
                $start_time = date('Y-m-d') . ' ' . sprintf('%02d:00:00', $i);
                $end_time = date('Y-m-d') . ' ' . sprintf('%02d:59:59', $i);
                
                $count = Db::table('order')
                    ->where('clientkeynum', $basekeynum)
                    ->whereTime('add_time', 'between', [$start_time, $end_time])
                    ->whereNotIn('status', [0, 1, -1])
                    ->count();
                
                $recent_order_trend[] = $count;
            }
        } else {
            // 其他时间范围按天统计
            if ($time_range == 'custom') {
                // 自定义时间范围，从开始日期到结束日期
                $current_date = strtotime($start_date);
                $end_timestamp = strtotime($end_date);
                
                while ($current_date <= $end_timestamp) {
                    $day = date('Y-m-d', $current_date);
                    $recent_days[] = $day;
                    
                    // 计算这个时间段的数据
                    if ($interval > 1) {
                        $period_end = min($current_date + ($interval - 1) * 24 * 3600, $end_timestamp);
                        $end_day = date('Y-m-d', $period_end);
                        
                        $count = Db::table('order')
                            ->where('clientkeynum', $basekeynum)
                            ->whereTime('add_time', 'between', [$day . ' 00:00:00', $end_day . ' 23:59:59'])
                            ->whereNotIn('status', [0, 1, -1])
                            ->count();
                    } else {
                        $count = Db::table('order')
                            ->where('clientkeynum', $basekeynum)
                            ->whereTime('add_time', 'between', [$day . ' 00:00:00', $day . ' 23:59:59'])
                            ->whereNotIn('status', [0, 1, -1])
                            ->count();
                    }
                    
                    $recent_order_trend[] = $count;
                    $current_date += $interval * 24 * 3600;
                }
            } else {
                // 预设时间范围，从现在往前推
                for ($i = $days_count - 1; $i >= 0; $i--) {
                    $day_offset = $i * $interval;
                    $day = date('Y-m-d', strtotime("-$day_offset days"));
                    $recent_days[] = $day;
                    
                    // 如果是长时间范围，则每个点统计多天的数据
                    if ($interval > 1) {
                        $start_day = date('Y-m-d', strtotime("-" . ($day_offset + $interval - 1) . " days"));
                        $end_day = $day;
                        
                        $count = Db::table('order')
                            ->where('clientkeynum', $basekeynum)
                            ->whereTime('add_time', 'between', [$start_day . ' 00:00:00', $end_day . ' 23:59:59'])
                            ->whereNotIn('status', [0, 1, -1])
                            ->count();
                    } else {
                        $count = Db::table('order')
                            ->where('clientkeynum', $basekeynum)
                            ->whereTime('add_time', 'between', [$day . ' 00:00:00', $day . ' 23:59:59'])
                            ->whereNotIn('status', [0, 1, -1])
                            ->count();
                    }
                    
                    $recent_order_trend[] = $count;
                }
            }
        }
        
        $data['recent_days'] = $recent_days;
        $data['recent_order_trend'] = $recent_order_trend;
        
        // 获取订单金额统计（按时间范围筛选，排除待支付、待审核、已取消订单）
        $data['total_order_amount'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->whereNotIn('status', [0, 1, -1])
            ->sum('price');
            
        $data['total_product_amount'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->whereNotIn('status', [0, 1, -1])
            ->sum('product_price');
            
        $data['total_shipping_amount'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->whereNotIn('status', [0, 1, -1])
            ->sum('shipping_price');
            
        // 获取微信支付和卡支付金额
        $data['total_wechat_amount'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->whereNotIn('status', [0, 1, -1])
            ->where('pay_type', 'in', [1, 3]) // 微信支付和组合支付
            ->sum('price');
            
        $data['total_card_amount'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->whereNotIn('status', [0, 1, -1])
            ->where('pay_type', 'in', [2, 3]) // 卡支付和组合支付
            ->sum('card_price');
            
        // 获取线下支付金额（现金支付）
        $data['total_cash_amount'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->whereNotIn('status', [0, 1, -1])
            ->where('pay_type', 4) // 现金支付
            ->sum('price');
        
        // 获取店铺订单数量排行（按时间范围筛选，排除待支付、待审核、已取消订单）
        // 如果筛选了门店，则不显示店铺排行
        if ($shop_id > 0) {
            $data['shop_order_rank'] = [];
        } else {
            // 构建店铺排行查询条件
            $shop_rank_where = [
                ['o.clientkeynum', '=', $basekeynum]
            ];
            
            // 添加配送方式筛选
            if ($delivery_type > 0) {
                $shop_rank_where[] = ['o.type', '=', $delivery_type];
            }
            
            $shop_order_rank = Db::table('order')
                ->alias('o')
                ->join('shop s', 'o.shop_id = s.id')
                ->where($shop_rank_where)
                ->whereTime('o.add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
                ->whereNotIn('o.status', [0, 1, -1])
                ->field('s.id, s.title, COUNT(o.id) as count, SUM(o.price) as amount')
                ->group('s.id')
                ->order('count DESC')
                ->limit($shop_limit)
                ->select();
            
            $data['shop_order_rank'] = $shop_order_rank;
        }
        
        // 获取配送方式统计（按时间范围筛选，排除待支付、待审核、已取消订单）
        $data['self_pickup_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('type', 1)
            ->whereNotIn('status', [0, 1, -1])
            ->count();
            
        $data['delivery_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('type', 2)
            ->whereNotIn('status', [0, 1, -1])
            ->count();

            $data['checkout_count'] = Db::table('order')
            ->where($base_where)
            ->whereTime('add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->where('type', 3)
            ->whereNotIn('status', [0, 1, -1])
            ->count();    
        
        // 获取店铺总数，用于分页
        $shop_count = Db::table('shop')
            ->where('clientkeynum', $basekeynum)
            ->count();
            
        $data['shop_count'] = $shop_count;
        
        // 获取商品排行数量，默认为10
        $product_limit = isset($param['product_limit']) ? intval($param['product_limit']) : 10;
        
        // 构建商品排行查询条件
        $product_rank_where = [
            ['od.clientkeynum', '=', $basekeynum]
        ];
        
        // 添加门店筛选
        if ($shop_id > 0) {
            $product_rank_where[] = ['o.shop_id', '=', $shop_id];
        }
        
        // 添加配送方式筛选
        if ($delivery_type > 0) {
            $product_rank_where[] = ['o.type', '=', $delivery_type];
        }
        
        // 获取订单商品数量排行（排除待支付、待审核、已取消订单）
        $product_order_rank = Db::table('order_detail')
            ->alias('od')
            ->join('order o', 'od.order_no = o.order_no')
            ->join('products p', 'JSON_EXTRACT(od.product_json, "$.id") = p.id')
            ->where($product_rank_where)
            ->whereTime('od.add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
            ->whereNotIn('o.status', [0, 1, -1])
            ->field('p.id, p.title, p.cover, COUNT(od.id) as count, SUM(od.number) as total_number')
            ->group('p.id')
            ->order('count DESC, total_number DESC')
            ->limit($product_limit)
            ->select();
            
        // 如果上面的查询失败（可能是因为JSON_EXTRACT不支持），尝试另一种方式
        if (empty($product_order_rank)) {
            // 获取所有订单详情（排除待支付、待审核、已取消订单）
            $order_details = Db::table('order_detail')
                ->alias('od')
                ->join('order o', 'od.order_no = o.order_no')
                ->where($product_rank_where)
                ->whereTime('od.add_time', 'between', [$start_date . ' 00:00:00', $end_date . ' 23:59:59'])
                ->whereNotIn('o.status', [0, 1, -1])
                ->field('od.id, od.product_json, od.number')
                ->select();

            // 统计商品数量
            $product_stats = [];
            foreach ($order_details as $detail) {
                $product_json = json_decode($detail['product_json'], true);
                if (isset($product_json['id'])) {
                    $product_id = $product_json['id'];
                    if (!isset($product_stats[$product_id])) {
                        $product_stats[$product_id] = [
                            'count' => 0,
                            'total_number' => 0
                        ];
                    }
                    $product_stats[$product_id]['count']++;
                    $product_stats[$product_id]['total_number'] += $detail['number'];
                }
            }
            
            // 获取商品信息
            if (!empty($product_stats)) {
                $product_ids = array_keys($product_stats);
                $products = Db::table('products')
                    ->where('id', 'in', $product_ids)
                    ->where('clientkeynum', $basekeynum)
                    ->field('id, title, cover')
                    ->select();
                    
                // 合并数据
                $product_order_rank = [];
                foreach ($products as $product) {
                    $product_id = $product['id'];
                    if (isset($product_stats[$product_id])) {
                        $product_order_rank[] = [
                            'id' => $product_id,
                            'title' => $product['title'],
                            'cover' => $product['cover'],
                            'count' => $product_stats[$product_id]['count'],
                            'total_number' => $product_stats[$product_id]['total_number']
                        ];
                    }
                }
                
                // 按订单数量和总数量排序
                usort($product_order_rank, function($a, $b) {
                    if ($a['count'] == $b['count']) {
                        return $b['total_number'] - $a['total_number'];
                    }
                    return $b['count'] - $a['count'];
                });
                
                // 限制数量
                $product_order_rank = array_slice($product_order_rank, 0, $product_limit);
            }
        }
        
        $data['product_order_rank'] = $product_order_rank;
        
        // 获取商品总数，用于分页
        $product_count = Db::table('products')
            ->where('clientkeynum', $basekeynum)
            ->count();
            
        $data['product_count'] = $product_count;
        
        return json($data);
    }

    /**
     * 商品销量统计页面
     */
    public function product_sales_statistics()
    {
        check_auth(request()->controller() . '/product_sales_statistics', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        // 获取门店列表
        $shop_list = Db::name('shop')
            ->where(['clientkeynum' => $basekeynum])
            ->field('id, title')
            ->select();
        
        $this->assign('shop_list', $shop_list);
        return $this->fetch();
    }
    
    /**
     * 获取商品销量统计数据
     */
    public function ajax_product_sales_statistics()
    {
        check_auth(request()->controller() . '/product_sales_statistics', 1);
        $request = request();
        $param = $request->param();
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        // 获取分页参数
        $page = isset($param['page']) ? intval($param['page']) : 1;
        $limit = isset($param['limit']) ? intval($param['limit']) : 10;
        
        // 获取筛选参数
        $start_date = isset($param['start_date']) ? $param['start_date'] : date('Y-m-d', strtotime('-30 days'));
        $end_date = isset($param['end_date']) ? $param['end_date'] : date('Y-m-d');
        $shop_id = isset($param['shop_id']) ? intval($param['shop_id']) : 0;
        $keyword = isset($param['keyword']) ? trim($param['keyword']) : '';
        $product_type = isset($param['product_type']) ? intval($param['product_type']) : 0; // 0全部，1普通商品，2计量商品
        
        // 构建基础查询条件
        $where = [
            ['od.clientkeynum', '=', $basekeynum],
            ['o.status', 'in', [2, 3, 4, 100]] // 只统计已接单、配送中、已完成的订单
        ];
        
        // 添加日期筛选
        $where[] = ['o.add_time', '>=', $start_date . ' 00:00:00'];
        $where[] = ['o.add_time', '<=', $end_date . ' 23:59:59'];
        
        // 添加门店筛选
        if ($shop_id > 0) {
            $where[] = ['o.shop_id', '=', $shop_id];
        }
        
        // 构建商品销量查询
        $query = Db::table('order_detail')
            ->alias('od')
            ->join('order o', 'od.order_no = o.order_no')
            ->where($where);
        
        // 关键词搜索
        if (!empty($keyword)) {
            $query = $query->where(function ($q) use ($keyword) {
                $q->where("JSON_EXTRACT(od.product_json, '$.title') like '%".$keyword."%'")
                  ->whereOr("JSON_EXTRACT(od.inventory_json, '$.title') like '%".$keyword."%'");
            });
        }
        
        // 商品类型筛选
        if ($product_type > 0) {
            if ($product_type == 1) {
                // 普通商品：product_type=1
                $query = $query->where("JSON_EXTRACT(od.product_json, '$.product_type') = 1");
            } else if ($product_type == 2) {
                // 计量商品：product_type=2
                $query = $query->where("JSON_EXTRACT(od.product_json, '$.product_type') = 2");
            }
        }
        
        // 分组统计
        $query = $query->field([
            "JSON_EXTRACT(od.product_json, '$.id') as product_id",
            "JSON_EXTRACT(od.product_json, '$.title') as product_title",
            "JSON_EXTRACT(od.inventory_json, '$.title') as inventory_title",
            "JSON_EXTRACT(od.product_json, '$.product_type') as product_type",
            "SUM(CASE WHEN od.actual_weight > 0 THEN od.actual_weight ELSE od.number END) as total_sales",
            "SUM(od.amount) as total_amount",
            "COUNT(DISTINCT o.id) as order_count"
        ])
        ->group("product_id, JSON_EXTRACT(od.inventory_json, '$.id')");
        
        // 获取总记录数
        $countQuery = clone $query;
        $count = count($countQuery->select());
        
        // 分页查询
        $list = $query->order('total_sales', 'desc')
            ->limit(($page - 1) * $limit, $limit)
            ->select();
        
        // 处理结果集
        foreach ($list as &$item) {
            $item['product_id'] = trim($item['product_id'], '"');
            $item['product_title'] = trim($item['product_title'], '"');
            $item['inventory_title'] = trim($item['inventory_title'], '"');
            $item['product_type'] = trim($item['product_type'], '"');
            
            // 判断商品类型
            $item['product_type_text'] = $item['product_type'] == 2 ? '计量商品' : '普通商品';
            
            // 格式化销量和金额
            if ($item['product_type'] == 2) {
                $item['sales_display'] = $item['total_sales'] . 'kg';
            } else {
                $item['sales_display'] = intval($item['total_sales']) . '件';
            }
            
            $item['total_amount'] = number_format($item['total_amount'], 2);
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $count,
            'data' => $list
        ]);
    }
    
    /**
     * 获取商品销量趋势数据
     */
    public function ajax_product_sales_trend()
    {
        check_auth(request()->controller() . '/product_sales_statistics', 1);
        $request = request();
        $param = $request->param();
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        $product_id = isset($param['product_id']) ? intval($param['product_id']) : 0;
        $inventory_id = isset($param['inventory_id']) ? intval($param['inventory_id']) : 0;
        $days = isset($param['days']) ? intval($param['days']) : 30;
        
        if ($days <= 0 || $days > 365) {
            $days = 30;
        }
        
        // 构建日期数组
        $dates = [];
        $sales = [];
        $amounts = [];
        
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i days"));
            $dates[] = $date;
            
            // 查询当天销量
            $where = [
                ['od.clientkeynum', '=', $basekeynum],
                ['o.status', 'in', [2, 3, 4, 100]],
                ['o.add_time', 'between', [$date . ' 00:00:00', $date . ' 23:59:59']]
            ];
            
            if ($product_id > 0) {
                $where[] = ["JSON_EXTRACT(od.product_json, '$.id') = " . $product_id];
            }
            
            if ($inventory_id > 0) {
                $where[] = ["JSON_EXTRACT(od.inventory_json, '$.id') = " . $inventory_id];
            }
            
            $result = Db::table('order_detail')
                ->alias('od')
                ->join('order o', 'od.order_no = o.order_no')
                ->where($where)
                ->field([
                    "SUM(CASE WHEN od.actual_weight > 0 THEN od.actual_weight ELSE od.number END) as daily_sales",
                    "SUM(od.amount) as daily_amount"
                ])
                ->find();
            
            $sales[] = $result['daily_sales'] ? floatval($result['daily_sales']) : 0;
            $amounts[] = $result['daily_amount'] ? floatval($result['daily_amount']) : 0;
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'dates' => $dates,
                'sales' => $sales,
                'amounts' => $amounts
            ]
        ]);
    }
    
    /**
     * 获取销量统计卡片数据
     */
    public function ajax_sales_summary()
    {
        check_auth(request()->controller() . '/product_sales_statistics', 1);
        $request = request();
        $param = $request->param();
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        // 获取筛选参数
        $start_date = isset($param['start_date']) ? $param['start_date'] : date('Y-m-d', strtotime('-30 days'));
        $end_date = isset($param['end_date']) ? $param['end_date'] : date('Y-m-d');
        $shop_id = isset($param['shop_id']) ? intval($param['shop_id']) : 0;
        $keyword = isset($param['keyword']) ? trim($param['keyword']) : '';
        
        // 构建基础查询条件
        $where = [
            ['od.clientkeynum', '=', $basekeynum],
            ['o.status', 'in', [2, 3, 4, 100]] // 只统计已接单、配送中、已完成的订单
        ];
        
        // 添加日期筛选
        $where[] = ['o.add_time', '>=', $start_date . ' 00:00:00'];
        $where[] = ['o.add_time', '<=', $end_date . ' 23:59:59'];
        
        // 添加门店筛选
        if ($shop_id > 0) {
            $where[] = ['o.shop_id', '=', $shop_id];
        }
        
        // 构建查询对象
        $query = Db::table('order_detail')
            ->alias('od')
            ->join('order o', 'od.order_no = o.order_no')
            ->where($where);
        
        // 关键词搜索
        if (!empty($keyword)) {
            $query = $query->where(function ($q) use ($keyword) {
                $q->where(function($sq) use ($keyword) {
                    $sq->whereExp("JSON_EXTRACT(od.product_json, '$.title')", "like", "%$keyword%");
                })->whereOr(function($sq) use ($keyword) {
                    $sq->whereExp("JSON_EXTRACT(od.inventory_json, '$.title')", "like", "%$keyword%");
                });
            });
        }
        
        // 获取总销量
        $total_result = $query->field([
            "SUM(CASE WHEN od.actual_weight > 0 THEN od.actual_weight ELSE od.number END) as total_sales"
        ])->find();
        
        // 获取普通商品销量
        $normal_query = clone $query;
        $normal_result = $normal_query->where("JSON_EXTRACT(od.product_json, '$.product_type') = 1")
            ->field([
                "SUM(od.number) as normal_sales"
            ])->find();
        
        // 获取计量商品销量
        $measure_query = clone $query;
        $measure_result = $measure_query->where("JSON_EXTRACT(od.product_json, '$.product_type') = 2")
            ->field([
                "SUM(od.actual_weight) as measure_sales"
            ])->find();
        
        // 获取赠品销量
        $gift_query = clone $query;
        $gift_result = $gift_query->where("JSON_EXTRACT(od.product_json, '$.product_type') = 3")
            ->field([
                "SUM(od.number) as gift_sales"
            ])->find();
        
        // 格式化数据
        $total_sales = $total_result['total_sales'] ? round($total_result['total_sales'], 2) : 0;
        $normal_sales = $normal_result['normal_sales'] ? intval($normal_result['normal_sales']) : 0;
        $measure_sales = $measure_result['measure_sales'] ? round($measure_result['measure_sales'], 2) : 0;
        $gift_sales = $gift_result['gift_sales'] ? intval($gift_result['gift_sales']) : 0;
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'total_sales' => $total_sales,
                'normal_sales' => $normal_sales,
                'measure_sales' => $measure_sales . 'kg',
                'gift_sales' => $gift_sales
            ]
        ]);
    }

    /**
     * 订单趋势分析页面
     * 提供订单数量、金额的走势分析，包括饼图、柱状图等多种可视化展示
     */
    public function order_trend_statistics()
    {
        check_auth(request()->controller() . '/order_trend_statistics', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        // 获取门店列表
        $shop_list = Db::name('shop')
            ->where(['clientkeynum' => $basekeynum])
            ->field('id, title')
            ->select();
        
        $this->assign('shop_list', $shop_list);
        return $this->fetch();
    }
    
    /**
     * 获取订单趋势统计数据
     * 返回订单金额、数量、类型等多维度的统计数据
     */
    public function ajax_order_trend_statistics()
    {
        check_auth(request()->controller() . '/order_trend_statistics', 1);
        $request = request();
        $param = $request->param();
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        $data = [];
        
        // 获取筛选参数
        $start_date = isset($param['start_date']) ? $param['start_date'] : date('Y-m-d', strtotime('-30 days'));
        $end_date = isset($param['end_date']) ? $param['end_date'] : date('Y-m-d');
        $shop_id = isset($param['shop_id']) ? intval($param['shop_id']) : 0;
        $time_unit = isset($param['time_unit']) ? $param['time_unit'] : 'day'; // 时间单位：day, week, month
        
        // 构建基础查询条件
        $where = [
            ['clientkeynum', '=', $basekeynum],
            ['status', 'in', [2, 3, 4, 100]] // 只统计已接单、配送中、已完成的订单
        ];
        
        // 添加日期筛选
        $where[] = ['add_time', '>=', $start_date . ' 00:00:00'];
        $where[] = ['add_time', '<=', $end_date . ' 23:59:59'];
        
        // 添加门店筛选
        if ($shop_id > 0) {
            $where[] = ['shop_id', '=', $shop_id];
        }
        
        // 1. 订单总体数据
        $order_total = Db::table('order')
            ->where($where)
            ->field([
                'COUNT(*) as total_count',
                'SUM(price) as total_amount',
                'AVG(price) as avg_amount',
                'MIN(price) as min_amount',
                'MAX(price) as max_amount'
            ])
            ->find();
        
        $data['order_total'] = [
            'total_count' => $order_total['total_count'] ?: 0,
            'total_amount' => round($order_total['total_amount'] ?: 0, 2),
            'avg_amount' => round($order_total['avg_amount'] ?: 0, 2),
            'min_amount' => round($order_total['min_amount'] ?: 0, 2),
            'max_amount' => round($order_total['max_amount'] ?: 0, 2)
        ];
        
        // 2. 配送方式分布（饼图数据）
        $delivery_stats = Db::table('order')
            ->where($where)
            ->field([
                'type',
                'COUNT(*) as count',
                'SUM(price) as amount'
            ])
            ->group('type')
            ->select();
        
        $data['delivery_stats'] = [];
        $delivery_types = [1 => '自提', 2 => '配送', 3 => '结算台'];
        
        foreach ($delivery_stats as $item) {
            $type_name = isset($delivery_types[$item['type']]) ? $delivery_types[$item['type']] : '其他';
            $data['delivery_stats'][] = [
                'name' => $type_name,
                'value' => $item['count'],
                'amount' => round($item['amount'], 2)
            ];
        }
        
        // 3. 支付方式分布（饼图数据）
        $payment_stats = Db::table('order')
            ->where($where)
            ->field([
                'pay_type',
                'COUNT(*) as count',
                'SUM(price) as amount'
            ])
            ->group('pay_type')
            ->select();
        
        $data['payment_stats'] = [];
        $payment_types = [1 => '微信支付', 2 => '卡支付', 3 => '组合支付', 4 => '现金支付'];
        
        foreach ($payment_stats as $item) {
            $type_name = isset($payment_types[$item['pay_type']]) ? $payment_types[$item['pay_type']] : '其他';
            $data['payment_stats'][] = [
                'name' => $type_name,
                'value' => $item['count'],
                'amount' => round($item['amount'], 2)
            ];
        }
        
        // 4. 订单状态分布（饼图数据）
        $status_stats = Db::table('order')
            ->where([
                ['clientkeynum', '=', $basekeynum],
                ['add_time', '>=', $start_date . ' 00:00:00'],
                ['add_time', '<=', $end_date . ' 23:59:59'],
            ])
            ->field([
                'status',
                'COUNT(*) as count',
                'SUM(price) as amount'
            ])
            ->group('status')
            ->select();
        
        $data['status_stats'] = [];
        $status_types = [
            0 => '待支付',
            1 => '待审核',
            2 => '已接单',
            3 => '配送中',
            4 => '已签收',
            100 => '已完成',
            -1 => '已取消'
        ];
        
        foreach ($status_stats as $item) {
            $status_name = isset($status_types[$item['status']]) ? $status_types[$item['status']] : '其他';
            $data['status_stats'][] = [
                'name' => $status_name,
                'value' => $item['count'],
                'amount' => round($item['amount'], 2)
            ];
        }
        
        // 5. 订单趋势数据（折线图和柱状图数据）
        $dates = [];
        $order_counts = [];
        $order_amounts = [];
        
        // 根据时间单位获取趋势数据
        if ($time_unit == 'month') {
            // 按月统计
            $start = strtotime($start_date);
            $end = strtotime($end_date);
            $current = $start;
            
            while ($current <= $end) {
                $month = date('Y-m', $current);
                $month_start = date('Y-m-01', $current);
                $month_end = date('Y-m-t', $current);
                
                $result = Db::table('order')
                    ->where($where)
                    ->whereTime('add_time', 'between', [$month_start . ' 00:00:00', $month_end . ' 23:59:59'])
                    ->field([
                        'COUNT(*) as count',
                        'SUM(price) as amount'
                    ])
                    ->find();
                
                $dates[] = $month;
                $order_counts[] = $result['count'] ?: 0;
                $order_amounts[] = round($result['amount'] ?: 0, 2);
                
                // 下一个月
                $current = strtotime('+1 month', $current);
            }
        } elseif ($time_unit == 'week') {
            // 按周统计
            $start = strtotime($start_date);
            $end = strtotime($end_date);
            $current = $start;
            $week_num = 1;
            
            while ($current <= $end) {
                $week_start = date('Y-m-d', $current);
                $week_end = date('Y-m-d', strtotime('+6 days', $current));
                
                if (strtotime($week_end) > $end) {
                    $week_end = date('Y-m-d', $end);
                }
                
                $result = Db::table('order')
                    ->where($where)
                    ->whereTime('add_time', 'between', [$week_start . ' 00:00:00', $week_end . ' 23:59:59'])
                    ->field([
                        'COUNT(*) as count',
                        'SUM(price) as amount'
                    ])
                    ->find();
                
                $dates[] = "第{$week_num}周 ({$week_start}~{$week_end})";
                $order_counts[] = $result['count'] ?: 0;
                $order_amounts[] = round($result['amount'] ?: 0, 2);
                
                // 下一周
                $current = strtotime('+7 days', $current);
                $week_num++;
            }
        } else {
            // 按天统计（默认）
            $start = strtotime($start_date);
            $end = strtotime($end_date);
            $current = $start;
            
            while ($current <= $end) {
                $day = date('Y-m-d', $current);
                
                $result = Db::table('order')
                    ->where($where)
                    ->whereTime('add_time', 'between', [$day . ' 00:00:00', $day . ' 23:59:59'])
                    ->field([
                        'COUNT(*) as count',
                        'SUM(price) as amount'
                    ])
                    ->find();
                
                $dates[] = $day;
                $order_counts[] = $result['count'] ?: 0;
                $order_amounts[] = round($result['amount'] ?: 0, 2);
                
                // 下一天
                $current = strtotime('+1 day', $current);
            }
        }
        
        $data['trend'] = [
            'dates' => $dates,
            'order_counts' => $order_counts,
            'order_amounts' => $order_amounts
        ];
        
        // 6. 热门商品排行（柱状图数据）
        $hot_products = Db::table('order_detail')
            ->alias('od')
            ->join('order o', 'od.order_no = o.order_no')
            ->where([
                ['o.clientkeynum', '=', $basekeynum],
                ['o.status', 'in', [2, 3, 4, 100]],
                ['o.add_time', '>=', $start_date . ' 00:00:00'],
                ['o.add_time', '<=', $end_date . ' 23:59:59'],
            ])
            ->field([
                "JSON_EXTRACT(od.product_json, '$.id') as product_id",
                "JSON_EXTRACT(od.product_json, '$.title') as product_title",
                "COUNT(od.id) as order_count",
                "SUM(od.number) as total_quantity",
                "SUM(od.amount) as total_amount"
            ])
            ->group('product_id')
            ->order('order_count DESC, total_amount DESC')
            ->limit(10)
            ->select();
        
        $product_names = [];
        $product_counts = [];
        $product_amounts = [];
        
        foreach ($hot_products as $product) {
            $product_names[] = trim($product['product_title'], '"');
            $product_counts[] = $product['order_count'];
            $product_amounts[] = round($product['total_amount'], 2);
        }
        
        $data['hot_products'] = [
            'names' => $product_names,
            'counts' => $product_counts,
            'amounts' => $product_amounts
        ];
        
        // 7. 热门门店排行（柱状图数据）
        if ($shop_id == 0) {
            $hot_shops = Db::table('order')
                ->alias('o')
                ->join('shop s', 'o.shop_id = s.id')
                ->where([
                    ['o.clientkeynum', '=', $basekeynum],
                    ['o.status', 'in', [2, 3, 4, 100]],
                    ['o.add_time', '>=', $start_date . ' 00:00:00'],
                    ['o.add_time', '<=', $end_date . ' 23:59:59'],
                ])
                ->field([
                    's.id',
                    's.title',
                    'COUNT(o.id) as order_count',
                    'SUM(o.price) as total_amount'
                ])
                ->group('s.id')
                ->order('order_count DESC, total_amount DESC')
                ->limit(10)
                ->select();
            
            $shop_names = [];
            $shop_counts = [];
            $shop_amounts = [];
            
            foreach ($hot_shops as $shop) {
                $shop_names[] = $shop['title'];
                $shop_counts[] = $shop['order_count'];
                $shop_amounts[] = round($shop['total_amount'], 2);
            }
            
            $data['hot_shops'] = [
                'names' => $shop_names,
                'counts' => $shop_counts,
                'amounts' => $shop_amounts
            ];
        }
        
        // 8. 各时段订单分布（柱状图数据）
        $hour_stats = Db::table('order')
            ->where($where)
            ->field([
                'HOUR(add_time) as hour',
                'COUNT(*) as count',
                'SUM(price) as amount'
            ])
            ->group('hour')
            ->select();
        
        $hours = [];
        $hour_counts = [];
        $hour_amounts = [];
        
        // 初始化24小时的数据
        for ($i = 0; $i < 24; $i++) {
            $hours[$i] = sprintf('%02d:00', $i);
            $hour_counts[$i] = 0;
            $hour_amounts[$i] = 0;
        }
        
        // 填充实际数据
        foreach ($hour_stats as $item) {
            $hour = intval($item['hour']);
            $hour_counts[$hour] = $item['count'];
            $hour_amounts[$hour] = round($item['amount'], 2);
        }
        
        $data['hour_stats'] = [
            'hours' => array_values($hours),
            'counts' => array_values($hour_counts),
            'amounts' => array_values($hour_amounts)
        ];
        
        // 9. 周一至周日订单分布（柱状图数据）
        $weekday_stats = Db::table('order')
            ->where($where)
            ->field([
                'DAYOFWEEK(add_time) as weekday',
                'COUNT(*) as count',
                'SUM(price) as amount'
            ])
            ->group('weekday')
            ->select();
        
        $weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        $weekday_counts = [0, 0, 0, 0, 0, 0, 0];
        $weekday_amounts = [0, 0, 0, 0, 0, 0, 0];
        
        // 填充实际数据
        foreach ($weekday_stats as $item) {
            $weekday = intval($item['weekday']) - 1; // DAYOFWEEK返回1-7，对应周日-周六
            $weekday_counts[$weekday] = $item['count'];
            $weekday_amounts[$weekday] = round($item['amount'], 2);
        }
        
        $data['weekday_stats'] = [
            'weekdays' => $weekdays,
            'counts' => $weekday_counts,
            'amounts' => $weekday_amounts
        ];
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $data
        ]);
    }
}