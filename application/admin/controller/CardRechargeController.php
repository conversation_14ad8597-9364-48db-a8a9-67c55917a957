<?php
/**
 * 折扣卡充值管理控制器
 * Date: 2024/03/17
 */

namespace app\admin\controller;

use think\Request;
use app\admin\model\CardRechargePackage;
use app\admin\model\CardRechargeRecord;

class CardRechargeController extends CnController
{
    /**
     * 充值套餐列表
     */
    public function packageList()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        if (request()->isAjax()) {
            $page = input('page', 1);
            $limit = input('limit', 15);
            $keyword = input('keyword', '');
            $status = input('status', '');
            
            $where = ['clientkeynum' => $clientkeynum];
            
            if (!empty($keyword)) {
                $where['name'] = ['like', "%{$keyword}%"];
            }
            
            if ($status !== '') {
                $where['status'] = $status;
            }
            
            $model = new CardRechargePackage();
            $list = $model->where($where)
                ->order('sort_order asc, id desc')
                ->paginate($limit, false, ['page' => $page])
                ->toArray();
            
            // 格式化数据
            foreach ($list['data'] as &$item) {
                $item['status_text'] = $item['status'] == 1 ? '启用' : '禁用';
                $item['time_range'] = '';
                if (!empty($item['start_time']) || !empty($item['end_time'])) {
                    $start = $item['start_time'] ?: '不限';
                    $end = $item['end_time'] ?: '不限';
                    $item['time_range'] = $start . ' 至 ' . $end;
                }
            }
            
            return json([
                'code' => 0,
                'msg' => 'success',
                'count' => $list['total'],
                'data' => $list['data']
            ]);
        }
        
        return $this->fetch();
    }
    
    /**
     * 添加充值套餐
     */
    public function packageAdd()
    {
        if (request()->isPost()) {
            $data = input('post.');
            $clientkeynum = session('cn_accountinfo.basekeynum');
            $adminId = session('cn_accountinfo.account_id');
            $adminName = session('cn_accountinfo.accountname');
            
            // 验证数据
            if (empty($data['name'])) {
                return json(['code' => 1, 'msg' => '请输入套餐名称']);
            }
            if (empty($data['recharge_amount']) || $data['recharge_amount'] <= 0) {
                return json(['code' => 1, 'msg' => '请输入有效的充值金额']);
            }
            // 设置默认的赠送金额为0（当前版本暂不支持赠送金额）
            $data['bonus_amount'] = 0;
            
            // 检查套餐名称是否重复
            $exists = CardRechargePackage::where('clientkeynum', $clientkeynum)
                ->where('name', $data['name'])
                ->find();
            if ($exists) {
                return json(['code' => 1, 'msg' => '套餐名称已存在']);
            }
            
            // 准备数据
            $packageData = [
                'clientkeynum' => $clientkeynum,
                'name' => $data['name'],
                'recharge_amount' => $data['recharge_amount'],
                'bonus_amount' => $data['bonus_amount'],
                'total_amount' => $data['recharge_amount'] + $data['bonus_amount'],
                'discount_rate' => $data['discount_rate'] ?? null,
                'status' => isset($data['status']) ? $data['status'] : 1,
                'sort_order' => $data['sort_order'] ?? 0,
                'description' => $data['description'] ?? '',
                'start_time' => !empty($data['start_time']) ? $data['start_time'] : null,
                'end_time' => !empty($data['end_time']) ? $data['end_time'] : null,
                'create_admin_id' => $adminId,
                'create_admin_name' => $adminName,
            ];
            
            $model = new CardRechargePackage();
            if ($model->createPackage($packageData)) {
                return json(['code' => 0, 'msg' => '添加成功']);
            } else {
                return json(['code' => 1, 'msg' => '添加失败']);
            }
        }
        
        return $this->fetch();
    }
    
    /**
     * 编辑充值套餐
     */
    public function packageEdit()
    {
        $id = input('id');
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        $model = new CardRechargePackage();
        $package = $model->where('id', $id)
            ->where('clientkeynum', $clientkeynum)
            ->find();
            
        if (empty($package)) {
            return json(['code' => 1, 'msg' => '套餐不存在']);
        }
        
        if (request()->isPost()) {
            $data = input('post.');
            
            // 验证数据
            if (empty($data['name'])) {
                return json(['code' => 1, 'msg' => '请输入套餐名称']);
            }
            if (empty($data['recharge_amount']) || $data['recharge_amount'] <= 0) {
                return json(['code' => 1, 'msg' => '请输入有效的充值金额']);
            }
            // 设置默认的赠送金额为0（当前版本暂不支持赠送金额）
            $data['bonus_amount'] = 0;
            
            // 检查套餐名称是否重复（排除自己）
            $exists = CardRechargePackage::where('clientkeynum', $clientkeynum)
                ->where('name', $data['name'])
                ->where('id', 'neq', $id)
                ->find();
            if ($exists) {
                return json(['code' => 1, 'msg' => '套餐名称已存在']);
            }
            
            // 准备更新数据
            $updateData = [
                'name' => $data['name'],
                'recharge_amount' => $data['recharge_amount'],
                'bonus_amount' => $data['bonus_amount'],
                'discount_rate' => $data['discount_rate'] ?? null,
                'status' => isset($data['status']) ? $data['status'] : 1,
                'sort_order' => $data['sort_order'] ?? 0,
                'description' => $data['description'] ?? '',
                'start_time' => !empty($data['start_time']) ? $data['start_time'] : null,
                'end_time' => !empty($data['end_time']) ? $data['end_time'] : null,
            ];
            
            if ($model->updatePackage($id, $updateData)) {
                return json(['code' => 0, 'msg' => '更新成功']);
            } else {
                return json(['code' => 1, 'msg' => '更新失败']);
            }
        }
        
        $this->assign('package', $package);
        return $this->fetch();
    }
    
    /**
     * 删除充值套餐
     */
    public function packageDelete()
    {
        $id = input('id');
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        $model = new CardRechargePackage();
        if ($model->deletePackage($id, $clientkeynum)) {
            return json(['code' => 0, 'msg' => '删除成功']);
        } else {
            return json(['code' => 1, 'msg' => '删除失败，该套餐已有充值记录']);
        }
    }
    
    /**
     * 更新套餐状态
     */
    public function packageStatus()
    {
        $id = input('id');
        $status = input('status');
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        $model = new CardRechargePackage();
        if ($model->updatePackage($id, ['status' => $status])) {
            return json(['code' => 0, 'msg' => '状态更新成功']);
        } else {
            return json(['code' => 1, 'msg' => '状态更新失败']);
        }
    }
    
    /**
     * 充值记录列表
     */
    public function recordList()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        if (request()->isAjax()) {
            $page = input('page', 1);
            $limit = input('limit', 15);
            $keyword = input('keyword', '');
            $status = input('status', '');
            $startTime = input('start_time', '');
            $endTime = input('end_time', '');
            $paymentMethod = input('payment_method', '');
            
            $where = ['r.clientkeynum' => $clientkeynum];
            
            if (!empty($keyword)) {
                $where['r.recharge_no|r.cardnum|u.mobile|u.nickname'] = ['like', "%{$keyword}%"];
            }
            
            if ($status !== '') {
                $where['r.payment_status'] = $status;
            }
            
            if (!empty($paymentMethod)) {
                $where['r.payment_method'] = $paymentMethod;
            }
            
            if (!empty($startTime)) {
                $where['r.add_time'] = ['>=', $startTime];
            }
            
            if (!empty($endTime)) {
                if (isset($where['r.add_time'])) {
                    $where['r.add_time'] = ['between', [$startTime, $endTime]];
                } else {
                    $where['r.add_time'] = ['<=', $endTime];
                }
            }
            
            $model = new CardRechargeRecord();
            $list = $model->alias('r')
                ->join('member u', 'r.user_id = u.id', 'left')
                ->join('card_recharge_package p', 'r.package_id = p.id', 'left')
                ->field('r.*, u.mobile, u.nickname, p.name as package_name')
                ->where($where)
                ->order('r.add_time desc')
                ->paginate($limit, false, ['page' => $page])
                ->toArray();
            
            // 格式化数据
            foreach ($list['data'] as &$item) {
                $paymentMethods = [1 => '微信支付', 2 => '支付宝', 3 => '现金', 4 => '银行卡'];
                $paymentStatus = [0 => '待支付', 1 => '已支付', 2 => '支付失败', 3 => '已退款'];
                $operatorTypes = [1 => '用户自助', 2 => '管理员代充'];
                
                $item['payment_method_text'] = $paymentMethods[$item['payment_method']] ?? '未知';
                $item['payment_status_text'] = $paymentStatus[$item['payment_status']] ?? '未知';
                $item['operator_type_text'] = $operatorTypes[$item['operator_type']] ?? '未知';
                $item['user_info'] = ($item['nickname'] ?: $item['mobile']) ?: '未知用户';
            }
            
            return json([
                'code' => 0,
                'msg' => 'success',
                'count' => $list['total'],
                'data' => $list['data']
            ]);
        }
        
        return $this->fetch();
    }
    
    /**
     * 充值记录详情
     */
    public function recordDetail()
    {
        $id = input('id');
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        $model = new CardRechargeRecord();
        $record = $model->alias('r')
            ->join('member u', 'r.user_id = u.id', 'left')
            ->join('card_recharge_package p', 'r.package_id = p.id', 'left')
            ->join('ycard c', 'r.card_id = c.id', 'left')
            ->field('r.*, u.mobile, u.nickname, u.realname, p.name as package_name, c.cardnum as full_cardnum')
            ->where('r.id', $id)
            ->where('r.clientkeynum', $clientkeynum)
            ->find();
            
        if (empty($record)) {
            return json(['code' => 1, 'msg' => '记录不存在']);
        }
        
        // 格式化数据
        $paymentMethods = [1 => '微信支付', 2 => '支付宝', 3 => '现金', 4 => '银行卡'];
        $paymentStatus = [0 => '待支付', 1 => '已支付', 2 => '支付失败', 3 => '已退款'];
        $operatorTypes = [1 => '用户自助', 2 => '管理员代充'];
        
        $record['payment_method_text'] = $paymentMethods[$record['payment_method']] ?? '未知';
        $record['payment_status_text'] = $paymentStatus[$record['payment_status']] ?? '未知';
        $record['operator_type_text'] = $operatorTypes[$record['operator_type']] ?? '未知';
        
        $this->assign('record', $record);
        return $this->fetch();
    }
    
    /**
     * 管理员代充
     */
    public function adminRecharge()
    {
        if (request()->isPost()) {
            $data = input('post.');
            $clientkeynum = session('cn_accountinfo.basekeynum');
            $adminId = session('cn_accountinfo.account_id');
            $adminName = session('cn_accountinfo.accountname');
            
            // 验证数据
            if (empty($data['cardnum'])) {
                return json(['code' => 1, 'msg' => '请输入卡号']);
            }
            if (empty($data['recharge_amount']) || $data['recharge_amount'] <= 0) {
                return json(['code' => 1, 'msg' => '请输入有效的充值金额']);
            }
            // 设置默认的赠送金额为0（当前版本暂不支持赠送金额）
            $data['bonus_amount'] = 0;
            
            // 查找卡信息
            $card = \think\Db::name('ycard')
                ->where('cardnum', $data['cardnum'])
                ->where('clientkeynum', $clientkeynum)
                ->find();
                
            if (empty($card)) {
                return json(['code' => 1, 'msg' => '卡号不存在']);
            }
            
            if ($card['status'] != 3) {
                return json(['code' => 1, 'msg' => '卡状态异常，无法充值']);
            }
            
            // 准备充值数据
            $rechargeData = [
                'clientkeynum' => $clientkeynum,
                'user_id' => $card['member_id'],
                'card_id' => $card['id'],
                'package_id' => $data['package_id'] ?? null,
                'package_name' => null,
                'recharge_amount' => $data['recharge_amount'],
                'bonus_amount' => $data['bonus_amount'],
                'total_amount' => $data['recharge_amount'] + $data['bonus_amount'],
                'payment_method' => 3, // 现金支付
                'payment_status' => 1, // 已支付
                'operator_type' => 2, // 管理员代充
                'operator_id' => $adminId,
                'operator_name' => $adminName,
                'remark' => $data['remark'] ?? ''
            ];
            
            // 如果选择了套餐，获取套餐信息
            if (!empty($data['package_id'])) {
                $package = CardRechargePackage::where('id', $data['package_id'])
                    ->where('clientkeynum', $clientkeynum)
                    ->find();
                if ($package) {
                    $rechargeData['package_name'] = $package['name'];
                    $rechargeData['recharge_amount'] = $package['recharge_amount'];
                    $rechargeData['bonus_amount'] = $package['bonus_amount'];
                    $rechargeData['total_amount'] = $package['total_amount'];
                }
            }
            
            $model = new CardRechargeRecord();
            $result = $model->createRechargeRecord($rechargeData);
            
            if (substr($result, 0, 2) === 'RC') {
                return json(['code' => 0, 'msg' => '充值成功', 'recharge_no' => $result]);
            } else {
                return json(['code' => 1, 'msg' => $result]);
            }
        }
        
        // 获取充值套餐列表
        $packages = CardRechargePackage::where('clientkeynum', session('cn_accountinfo.basekeynum'))
            ->where('status', 1)
            ->order('sort_order asc')
            ->select();
        
        $this->assign('packages', $packages);
        return $this->fetch();
    }
    
    /**
     * 获取卡信息
     */
    public function getCardInfo()
    {
        $cardnum = input('cardnum');
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        $card = \think\Db::name('ycard')
            ->alias('c')
            ->join('member m', 'c.member_id = m.id', 'left')
            ->field('c.*, m.mobile, m.nickname, m.realname')
            ->where('c.cardnum', $cardnum)
            ->where('c.clientkeynum', $clientkeynum)
            ->find();
            
        if (empty($card)) {
            return json(['code' => 1, 'msg' => '卡号不存在']);
        }
        
        $statusMap = [1 => '待激活', 2 => '已激活', 3 => '正常使用', 4 => '已冻结', 5 => '已注销'];
        $card['status_text'] = $statusMap[$card['status']] ?? '未知状态';
        $card['user_info'] = ($card['realname'] ?: $card['nickname']) ?: $card['mobile'];
        
        return json(['code' => 0, 'msg' => 'success', 'data' => $card]);
    }
} 