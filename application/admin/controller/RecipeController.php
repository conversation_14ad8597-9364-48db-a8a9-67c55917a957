<?php
/**
 * 食谱管理控制器
 * @date 2024-10-31
 */

namespace app\admin\controller;

use app\admin\model\Recipe;
use app\admin\model\RecipeCategory;
use app\admin\model\RecipeTag;
use think\facade\Request;

class RecipeController extends CnController
{
    /**
     * 食谱列表页面
     * @return mixed
     */
    public function recipe_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/recipe_list', 0);

        return $this->fetch();
    }

    /**
     * 获取食谱列表数据
     */
    public function ajax_recipe_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/recipe_list', 1);

        $page = Request::instance()->param('page', 1);
        $page_size = Request::instance()->param('page_size', 10);
        $title = Request::instance()->param('title', '');
        $category_id = Request::instance()->param('category_id', '');
        $status = Request::instance()->param('status', '');
        $content_type = Request::instance()->param('content_type', '');

        $where = [];

        if (!empty($title)) {
            $where['title'] = ['like', "%{$title}%"];
        }

        if (!empty($category_id)) {
            $where['category_id'] = $category_id;
        }

        if ($status !== '') {
            $where['status'] = $status;
        }

        if ($content_type !== '') {
            $where['content_type'] = $content_type;
        }

        $list = Recipe::getList($where, $page, $page_size);
        $count = Recipe::getCount($where);

        success(0, '请求成功', $list, $count);
    }

    /**
     * 添加食谱页面
     * @return mixed
     */
    public function add_recipe()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/recipe_list', 0);

        // 获取所有分类
        $categories = RecipeCategory::getAllCategories();
        $this->assign('categories', $categories);

        // 获取所有标签
        $tags = RecipeTag::getAllTags();
        // 需要处理成xmselect 可以处理的数据格式
        $tags_array = [];
        foreach ($tags as $tag) {
            $tags_array[] = [
                'name' => $tag['name'],
                'value' => $tag['id']
            ];
        }
        $this->assign('tags', json_encode($tags_array));

        return $this->fetch();
    }

    /**
     * 添加食谱处理
     */
    public function ajax_add_recipe()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/recipe_list', 1);

        $params = Request::instance()->param();

        // 处理图片数据
        if (isset($params['images_array']) && !empty($params['images_array'])) {
            $params['images'] = json_encode($params['images_array'], JSON_UNESCAPED_UNICODE);
        } else {
            $params['images'] = '';
        }

        // 获取当前登录用户ID
        $user_id = session("cn_accountinfo.account_id");

        $data = [
            'title' => $params['title'],
            'user_id' => $user_id,
            'category_id' => $params['category_id'],
            'cover_image' => $params['cover_image'],
            'description' => isset($params['description']) ? $params['description'] : '',
            // 'cooking_time' => isset($params['cooking_time']) ? $params['cooking_time'] : 0,
            // 'serving_size' => isset($params['serving_size']) ? $params['serving_size'] : 0,
            // 'difficulty' => isset($params['difficulty']) ? $params['difficulty'] : 1,
            'content_type' => $params['content_type'],
            'video_url' => isset($params['video_url']) ? $params['video_url'] : '',
            'video_duration' => isset($params['video_duration']) ? $params['video_duration'] : 0,
            'images' => $params['images'],
            'status' => $params['status']
        ];

        // 获取标签数据
        $tags = isset($params['tags']) ? $params['tags'] : [];

        $res = Recipe::add($data, $tags);
        if ($res) {
            success(0, '添加成功');
        } else {
            fail(-1, '添加失败');
        }
    }

    /**
     * 编辑食谱页面
     * @return mixed
     */
    public function edit_recipe()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/recipe_list', 0);

        $id = Request::instance()->param('id');
        if (empty($id)) {
            error_tips('NOT FOUND');
        }

        $info = Recipe::getInfoById($id);
        if (empty($info)) {
            error_tips('NOT FOUND');
        }

        $this->assign('info', $info);

        // 获取所有分类
        $categories = RecipeCategory::getAllCategories();
        $this->assign('categories', $categories);

        // 获取所有标签
        $tags = RecipeTag::getAllTags();
        $tags_array = [];
        foreach ($tags as $tag) {
            $tags_array[] = [
                'name' => $tag['name'],
                'value' => $tag['id']
            ];
        }
        $this->assign('tags', json_encode($tags_array));

        // 获取已选标签ID
        $selected_tags = [];
        if (!empty($info['tags'])) {
            foreach ($info['tags'] as $tag) {
                $selected_tags[] = $tag['id'];
            }
        }
        $this->assign('selected_tags', json_encode($selected_tags));

        return $this->fetch();
    }

    /**
     * 编辑食谱处理
     */
    public function ajax_edit_recipe()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/recipe_list', 1);

        $id = Request::instance()->param('id');
        if (empty($id)) {
            fail(-1, 'NOT FOUND');
        }

        $params = Request::instance()->param();

        // 处理图片数据
//        if (isset($params['images_array']) && !empty($params['images_array'])) {
//            $params['images'] = json_encode($params['images_array'], JSON_UNESCAPED_UNICODE);
//        } else {
//            $params['images'] = '';
//        }

        $data = [
            'title' => $params['title'],
            'category_id' => $params['category_id'],
            'cover_image' => $params['cover_image'],
            'description' => isset($params['description']) ? $params['description'] : '',
            // 'cooking_time' => isset($params['cooking_time']) ? $params['cooking_time'] : 0,
            // 'serving_size' => isset($params['serving_size']) ? $params['serving_size'] : 0,
            // 'difficulty' => isset($params['difficulty']) ? $params['difficulty'] : 1,
            'content_type' => $params['content_type'],
            'video_url' => isset($params['video_url']) ? $params['video_url'] : '',
            'video_duration' => isset($params['video_duration']) ? $params['video_duration'] : 0,
            'images' => $params['images'],
            'status' => $params['status'],
            'update_time' => date('Y-m-d H:i:s')
        ];

        $where = ['id' => $id];

        // 获取标签数据
        $tags = isset($params['tags']) ? $params['tags'] : [];

        if (Recipe::edit($where, $data, $tags)) {
            success(0, '编辑成功');
        } else {
            fail(-1, '编辑失败');
        }
    }

    /**
     * 删除食谱
     */
    public function del_recipe()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/recipe_list', 1);

        $id = Request::instance()->param('id');
        if (empty($id)) {
            fail(-1, '请选择要删除的食谱');
        }

        if (Recipe::del($id)) {
            success(0, '删除成功');
        } else {
            fail(-1, '删除失败');
        }
    }

    /**
     * 食谱详情页面
     * @return mixed
     */
    public function recipe_detail()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/recipe_list', 0);

        $id = Request::instance()->param('id');
        if (empty($id)) {
            error_tips('NOT FOUND');
        }

        $info = Recipe::getInfoById($id);
        if (empty($info)) {
            error_tips('NOT FOUND');
        }

        $this->assign('info', $info);

        return $this->fetch();
    }
}
