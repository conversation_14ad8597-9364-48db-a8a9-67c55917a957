<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright ( c ) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use app\admin\model\CardTypeGoodsModel;
use app\admin\model\GoodsClassifyModel;
use app\admin\model\GoodsModel;
use app\admin\model\PlatClient;
use think\facade\Log;
use think\facade\Request;
use think\Db;
use Goutte\Client;

class GoodsController extends CnController
{

    /**
     * 初始化方法
     */

    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    public function get_JD_detail()
    {
        $client = new Client();
        $url = \request()->get('url');
        $res = $client->request('get', $url);
        var_dump($res);
    }

    public function ajax_edit_goods_price()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goods_list?status=1', 1);
        $params = Request::instance()->param();
        $basekeynum = session('cn_accountinfo.basekeynum');
        // 金额正则
        $money_reg = '/^[1-9]\d*|^[1-9]\d*.\d+[1-9]$/';
        if (empty($params['id'])) fail(-1, '请求错误，请刷新重试');
        if (empty($params['price']) || !preg_match($money_reg, $params['price'])) fail(-1, '价格不能为空或价格格式不正确');

        try {
            Db::startTrans();
            $where = ['clientkeynum' => $basekeynum];
            GoodsModel::updatePrice($params['id'], $params['price'], $where);

            if (!empty($params['select_list'])) {
                // 批量更新商品绑定表的价格
                foreach ($params['select_list'] as $v) {
                    if (!preg_match($money_reg, $v['price'])) throw new \Exception('批量更新的金额格式有误');
                }
                $batch_result = CardTypeGoodsModel::batchUpdatePrice($params['select_list']);
                if (!$batch_result) throw new \Exception('批量修改商品价格失败');
            }
            Db::commit();
            success(0, '修改成功');
        } catch (\Exception $e) {
            Db::rollback();
            Log::error($e->getMessage());
            Log::error('error file:' . $e->getFile() . ' line:' . $e->getLine());
            fail(-1, '修改失败，请重试');
        }
        echo json_encode($params);die;
    }

    /**
     * 修改商品价格view
     * @return mixed
     */
    public function edit_goods_price()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goods_list?status=1', 0);
        $id = Request::instance()->param('id');
        $basekeynum = session('cn_accountinfo.basekeynum');
        $where = ['basekeynum' => $basekeynum];
        $info = GoodsModel::getInfoById($id,$where);
        if (empty($info)) {
            echo '当前商品不存在或已被删除';die;
        }
        $list_where = ['clientkeynum' => $basekeynum];
        // 获取已绑定的商品信息
        $list = CardTypeGoodsModel::getListByGoodsId($id, $list_where);
        if (!empty($list)) {
            // layui field 不支持多维数组嵌套渲染 需改为二维数组
            foreach ($list as $k => $v) {
                $list[$k]['goodsimg']                   = $v['goods']['goodsimg'];
                $list[$k]['goodsname']                  = $v['goods']['goodsname'];
                $list[$k]['card_type_classify_name']    = $v['card_type_classify_name'];
                $list[$k]['card_template_name']         = $v['card_template_name'];
            }
        }
        Log::error('list:' . json_encode($list));
        $this->assign('info', $info);
        $this->assign('list', json_encode($list));

        return $this->fetch('edit_goods_price');
    }

    public function supplier()
    {
        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/supplier', 1);
            //平台供应商的keynum
            $basekeynum = session('cn_accountinfo.basekeynum');
            $keyword = isset($param['keyword']) ? $param['keyword'] : '';
            $order = isset($param['order']) ? $param['order'] : 'DESC';
            $where = " 1 = 1  and clientkeynum='$basekeynum' ";
            if ($keyword) {
                $where .= "and  name  like '%$keyword%' ";
            }
            $p = isset($param['page']) ? intval($param['page']) : '1';
            $pagesize = $param['limit'];
            #每页数量
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量
            $count = Db::table('supplier')->where($where)->count();
            $list = Db::table('supplier')->where($where)->order('id desc')->limit($offset . ',' . $pagesize)->select();
            $rtdata['data'] = $list;
            $rtdata['count'] = $count;
            $rtdata['code'] = 0;
            $rtdata['msg'] = '';
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/supplier', 0);
            return $this->fetch('supplier');
        }
    }

    public function supplier_del()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/supplier', 1);
        $request = Request::instance();
        $param = $request->param();
        $id = isset($param['id']) ? $param['id'] : false;
        if ($id) {
            if (is_array($id)) {
                $map['id'] = array('in', $id);
            } else {
                $map = "id='$id'";
            }
            if (Db::table('supplier')->where($map)->delete()) {
                $return_arr['sta'] = 1;
                $return_arr['msg'] = '操作成功';
                addoperatelog('删除档案供应商', json_encode($param, JSON_UNESCAPED_UNICODE));
            } else {
                $return_arr['sta'] = 0;
                $return_arr['msg'] = '操作失败';
            }
        } else {
            $return_arr['sta'] = 0;
            $return_arr['msg'] = '操作失败';
        }
        echo json_encode($return_arr);
        die;
    }

    public function add_supplier()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/supplier', 0);
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = isset($param['id']) ? $param['id'] : '';
        if ($id) {
            $info = Db::table('supplier')->where("id='$id'")->find();
            $this->assign('info', $info);
        }
        return $this->fetch('add_supplier');
    }

    public function ajax_add_supplier()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/supplier', 1);
        $request = Request::instance();
        $param = $request->param();
        //平台供应商的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $param["id"];
        $data = $param;
        $data["clientkeynum"] = $basekeynum;
        if ($id) {
            $has = Db::table('supplier')->where("name='" . $data['name'] . "' and clientkeynum='basekeynum' and id!='$id'")->value("id");
            if ($has) {
                $rt['sta'] = 0;
                $rt['msg'] = '名称重复!';
                echo json_encode($rt);
                die;
            }
            Db::table('supplier')->where('id', $id)->update($data);
            $rt['sta'] = 1;
            $rt['msg'] = '修改成功!';
            echo json_encode($rt);
            addoperatelog('修改档案供应商', json_encode($param, JSON_UNESCAPED_UNICODE));
            die;
        } else {
            $has = Db::table('supplier')->where("name='" . $data['name'] . "' and clientkeynum='basekeynum'")->value("id");
            if ($has) {
                $rt['sta'] = 0;
                $rt['msg'] = '名称重复!';
                echo json_encode($rt);
                die;
            }
            Db::table('supplier')->insert($data);
            $rt['sta'] = 1;
            $rt['msg'] = '新增成功!';
            echo json_encode($rt);
            addoperatelog('新增档案供应商', json_encode($param, JSON_UNESCAPED_UNICODE));
            die;
        }
    }


    public function goodsclassify()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goodsclassify', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $plat_config = PlatClient::getInfoByBaseKeyNum($basekeynum);
        $is_first_category = $plat_config['is_first_category'];

        $this->assign('is_first_category', $is_first_category);
        return $this->fetch('goodsclassify');
    }

    public function ajax_get_goodsclassify()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goodsclassify', 1);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();
        $param = $request->param();
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        $where = "basekeynum='$basekeynum'";

        $pid = Request::instance()->param('pid', 0);

        $plat_config = PlatClient::getInfoByBaseKeyNum($basekeynum);

        if ($plat_config['is_first_category'] == 1) {
            $where .= " and pid = 0";
        } else {
            if (!empty($pid)) {
                $where .= " and (pid = $pid)";
            }
        }

        $list = Db::table('goodsclassify')->where($where)->order("o asc")->limit($offset . ',' . $pagesize)->select();
        foreach ($list as $key => $value) {
            $list[$key]['pname'] = Db::table('goodsclassify')
                ->where(['basekeynum' => $basekeynum, 'id' => $value['pid']])
                ->value('classifyname') ?? '顶级分类';
            $list[$key]['goods_count'] = Db::table('goods')
                ->where(['goodsclassify' => $value['id']])
                ->count();
        }
        $count = Db::table('goodsclassify')->where($where)->count();
        $rtdata['data'] = $list;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        echo json_encode($rtdata);
        die;
    }

    public function add_goodsclassify()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goodsclassify', 0);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();


        $plat_config = PlatClient::getInfoByBaseKeyNum($basekeynum);
        $this->assign('plat_config', $plat_config);

        $pid = $request->param('pid', 0);

        $param = $request->param();
        $id = $param["id"];
        $info = Db::table('goodsclassify')->where("id ='$id'")->find();

        $where = [
            'basekeynum' => $basekeynum,
            'pid' => 0
        ];
        if (!empty($info)) {
            $where['id'] = ['<>', $info['id']];
        }
        $top_cate = GoodsClassifyModel::getList($where);

        $this->assign('info', $info);
        $this->assign('pid', $pid);
        $this->assign('top_cate', $top_cate);
        return $this->fetch('add_goodsclassify');
    }


    public function ajax_add_goodsclassify()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goodsclassify', 0);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();
        $param = $request->param();
        $id = $param["id"];
        // 新增
        if (empty($id)) {
            $add = $param;
            $add["basekeynum"] = $basekeynum;
            if (!isset($add['pid'])) $add['pid'] = 0;
            $rs = Db::table("goodsclassify")->insert($add);
        } else {
            // 修改
            $save = $param;
            $rs = Db::table("goodsclassify")->where("id='$id'")->update($save);
        }
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        addoperatelog('修改商品分类档案', json_encode($param, JSON_UNESCAPED_UNICODE));
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    public function ajax_del_goodsclassify()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goodsclassify', 1);
        $request = Request::instance();
        $param = $request->param();
        $ids = $param['ids'];
        //把传过来的逗号拼接的字符串切成数组
        $arridslist = explode(',', $ids);
        $where['id'] = array('in', $arridslist);
        $classify_count = Db::table('goodsclassify')->where(['pid' => ['in', $arridslist]])->count();
        if ($classify_count > 0) {
            $rt['sta'] = 0;
            $rt['msg'] = '当前分类下有子分类，请删除清空子分类之后再次删除';
            echo json_encode($rt);
            die;
        }
        $goods_count = Db::table('goods')->where(['goodsclassify' => ['in', $arridslist]])->count();
        if ($goods_count > 0) {
            $rt['sta'] = 0;
            $rt['msg'] = '当前分类下有商品，请删除清空商品之后再次删除';
            echo json_encode($rt);
            die;
        }
        $rs = Db::table('goodsclassify')->where($where)->delete();
        addoperatelog('删除商品分类档案', json_encode($param, JSON_UNESCAPED_UNICODE));
        if (!$rs) {
            $rt['sta'] = 0;
            $rt['msg'] = '删除失败';
            echo json_encode($rt);
            die;
        }
        $rt['sta'] = 1;
        $rt['msg'] = '删除成功';
        echo json_encode($rt);
        die;
    }

    public function goods_list()
    {
        $request = Request::instance();
        $param = $request->param();
        $status = $param["status"];
        // 根据status判断具体功能 3同步 1上架0下架2蛋糕叔叔下架
        $this->assign("status", $status);
        $this->assign("join_id", $param["join_id"]);
        $this->assign("action", $param["action"]);
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goods_list?status=' . $status, 0);
        return $this->fetch('goods_list');
    }


    //获取地址树
    public function get_goodsclassify_treedata()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $area_list = Db::table('goodsclassify')
            ->field('id as id ,classifyname as title, pid as parentId')
            ->where("basekeynum='$basekeynum'");

        $plat_config = PlatClient::getInfoByBaseKeyNum($basekeynum);

        if ($plat_config['is_first_category'] == 1) {
            $area_list = $area_list->where("pid = 0");
        }

        $area_list = $area_list->order("o asc")
        ->select();
        $alllist = $this->getCateInfo($area_list,0);
        $result = [
            'status' => [
                'code' => '200',
                'message' => '请求成功'
            ],
            'data' => $alllist
        ];
        echo json_encode($result);
    }

    private function getCateInfo($cateInfo,$pid=0,$level=1)
    {
        $info = [];
        foreach($cateInfo as $k => $v){
            if($v['parentId'] == $pid){
                $son = $this->getCateInfo($cateInfo, $v['id'], $level+1);
                $v['children'] = $son;
                $info[] = $v;
            }
        }
        return $info;
    }


    // 每日获取一下商品上下架状态进行更新
    public function ajax_get_goods_list()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();
        $param = $request->param();
        $status = $param["status"];
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goods_list?status=' . $status, 1);
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        //计算记录偏移量
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $where = "basekeynum='$basekeynum' and status='$status'";
        if ($keyword) {
            $where .= " and $field like '%$keyword%'";
        }
        if ($param["id"] && $param["id"] != "-1") {
            $classify_info = GoodsClassifyModel::getInfo(['basekeynum' => $basekeynum, 'id' => $param['id']]);
            if (!empty($classify_info) && $classify_info['pid'] == 0) {
                $classify_ids = GoodsClassifyModel::where(['pid' => $param['id']])->column('id');
                if (!empty($classify_ids)) {
                    $where .= " and goodsclassify in (" . implode(',', $classify_ids) . ")";
                } else {
                    $where .= " and goodsclassify='" . $param["id"] . "'";
                }
            } else {
                $where .= " and goodsclassify='" . $param["id"] . "'";
            }
        }
        $join_id = isset($param['join_id']) ? $param['join_id'] : '';
        $action = isset($param['action']) ? $param['action'] : '';
        if ($action == 'card_type_classify') {
            $card_template_id = db('card_type_classify')->where("id='$join_id' and clientkeynum='$basekeynum'")->value("card_template_id");
            $goodsid_str = db('card_type_goods')->where("card_template_id='$card_template_id' and clientkeynum='$basekeynum'")->column("goodsid");
            $goodsid_str = implode(',', $goodsid_str);
            if (!empty($goodsid_str)) {
                $where .= " and id not in ($goodsid_str)";
            }
        }
        $order_by = "o asc";
        session("goods_list_where", $where);
        $list = Db::table('goods')->where($where)->order($order_by)->limit($offset . ',' . $pagesize)->select();
        foreach ($list as $key => &$value) {
            $value['goodsclassify'] = db('goodsclassify')->where("id='" . $value['goodsclassify'] . "'")->value("classifyname");
            $value['supplier_name'] = db('supplier')->where("id='" . $value['supplier_id'] . "'")->value("name");
            $value["status"] = $value["status"] == '1' ? '上架' : '下架';
        }
        $count = Db::table('goods')->where($where)->count();
        $rtdata['data'] = $list;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        echo json_encode($rtdata);
        die;
    }

    public function add_goods()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goods_list?status=1', 0);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $plat_config = PlatClient::getInfoByBaseKeyNum($basekeynum);
        $this->assign('plat_config', $plat_config);
        $request = Request::instance();
        $param = $request->param();
        $id = $param["id"];
        $info = Db::table('goods')->where("id ='$id'")->find();
        $this->assign('goods_info', $info);
        $goodsclassify = db('goodsclassify')->where("basekeynum='$basekeynum'")->order("o asc")->field("id,classifyname,pid as parentId")->select();
        $goodsclassify = $this->getCateInfo($goodsclassify);
        $this->assign("goodsclassify", $goodsclassify);
        $supplier_list = db('supplier')->where("clientkeynum='$basekeynum'")->order("id asc")->field("id,name")->select();
        $this->assign("supplier_list", $supplier_list);
        return $this->fetch('add_goods');
    }

    public function ajax_add_goods()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goods_list?status=1', 0);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();
        $param = $request->param();

        if (empty($param['price']) || $param['price'] <= 0) {
            $rt["sta"] = 0;
            $rt["msg"] = "请输入金额或者金额不可小于0";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }

        if (empty($param['market_price']) || $param['market_price'] <= 0) {
            $rt["sta"] = 0;
            $rt["msg"] = "请输入金额或者金额不可小于0";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }

        $param["status"] = $param["status"] == 'on' ? '1' : '0';
        $id = $param["id"];
        // 新增
        if (empty($id)) {
            $add = $param;
            $add["basekeynum"] = $basekeynum;
            $add["clientkeynum"] = $basekeynum;
            $rs = Db::table("goods")->insert($add);
        } else {
            // 修改
            $save = $param;
            $rs = Db::table("goods")->where("id='$id'")->update($save);
        }
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        addoperatelog('修改商品档案', json_encode($param, JSON_UNESCAPED_UNICODE));
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    public function ajax_del_goods()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goods_list?status=1', 0);
        $request = Request::instance();
        $param = $request->param();
        $ids = $param['ids'];
        //把传过来的逗号拼接的字符串切成数组
        $arridslist = explode(',', $ids);
        $where['id'] = array('in', $arridslist);
        $rs = Db::table('goods')->where($where)->delete();
        addoperatelog('删除商品', json_encode($param, JSON_UNESCAPED_UNICODE));
        if (!$rs) {
            $rt['sta'] = 0;
            $rt['msg'] = '删除失败';
            echo json_encode($rt);
            die;
        }
        $rt['sta'] = 1;
        $rt['msg'] = '删除成功';
        echo json_encode($rt);
        die;
    }

    public function ajax_xiajia_goods()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();
        $param = $request->param();
        $alldata = json_decode($param["alldata"], 1);
        $ids = "";
        foreach ($alldata as $key => $value) {
            $ids .= $value["id"] . ",";
        }
        $ids = trim($ids, ",");
        if (empty($ids)) {
            $rt["sta"] = 0;
            $rt["msg"] = "请选择要操作的数据";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        $where = "basekeynum='$basekeynum' and status='1' and id in ($ids)";
        $save["status"] = 0;
        $rs = Db::table("goods")->where($where)->update($save);
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        addoperatelog('标记本系统商品到下架', $ids);
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    public function ajax_shangjia_goods()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();
        $param = $request->param();
        $alldata = json_decode($param["alldata"], 1);
        $ids = "";
        foreach ($alldata as $key => $value) {
            $ids .= $value["id"] . ",";
        }
        $ids = trim($ids, ",");
        if (empty($ids)) {
            $rt["sta"] = 0;
            $rt["msg"] = "请选择要操作的数据";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        $where = "basekeynum='$basekeynum' and status='0' and id in ($ids)";
        $save["status"] = 1;
        $rs = Db::table("goods")->where($where)->update($save);
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        addoperatelog('标记本系统商品到上架', $ids);
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    public function ajax_add_check()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/goods_list?status=1', 1);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();
        $param = $request->param();
        $action = $param["action"];
        $join_id = $param["join_id"];
        $alldata = json_decode($param["alldata"], 1);
        if ($action == 'card_type_classify') {
            $card_type_classify_info = db('card_type_classify')->where("id='$join_id' and clientkeynum='$basekeynum'")->find();
            if (empty($card_type_classify_info)) {
                $rt["sta"] = 0;
                $rt["msg"] = "卡型分类信息不存在";
                echo json_encode($rt, JSON_UNESCAPED_UNICODE);
                die;
            }
            foreach ($alldata as $key => $value) {
                $find = db('card_type_goods')
                    ->where("card_type_classify_id='$join_id' and clientkeynum='$basekeynum' and goodsid='" . $value["id"] . "'")->find();
                $data = array();
                $data["goodsid"] = $value["id"];
                $data["price"] = $value["price"];
                $data["o"] = $value["o"];
                $data["clientkeynum"] = $basekeynum;
                $data["card_template_id"] = $card_type_classify_info["card_template_id"];
                $data["card_type_classify_id"] = $card_type_classify_info["id"];
                if ($find) {
                    continue;
                }
                db('card_type_goods')->insert($data);
            }
            addoperatelog('分配商品到卡型分类', "");
            $rt["sta"] = 1;
            $rt["msg"] = "操作成功";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
    }
}
