<?php

namespace app\admin\controller;

use app\admin\model\Card;
use think\Controller;
use think\Db;
use think\Request;
use app\admin\model\CardSale;
use think\facade\Log;

/**
 * 套餐卡管理控制器
 */
class CardController extends Controller
{
    /**
     * 卡列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $where = [];
            $card_no = input('card_no', '');
            $type_id = input('type_id', '');
            $status = input('status', '');
            $customer_id = input('customer_id', '');
            $salesperson_id = input('salesperson_id', '');
            $valid_start = input('valid_start', '');
            $valid_end = input('valid_end', '');

            if ($card_no) {
                $where[] = ['card_no', 'like', "%{$card_no}%"];
            }

            if ($type_id) {
                $where[] = ['type_id', '=', $type_id];
            }

            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }

            if ($customer_id) {
                $where[] = ['customer_id', '=', $customer_id];
            }

            if ($salesperson_id) {
                $where[] = ['salesperson_id', '=', $salesperson_id];
            }

            if ($valid_start && $valid_end) {
                $where[] = ['valid_end_time', 'between', [$valid_start, $valid_end]];
            } else if ($valid_start) {
                $where[] = ['valid_end_time', '>=', $valid_start];
            } else if ($valid_end) {
                $where[] = ['valid_end_time', '<=', $valid_end];
            }

            $list = model('Card')
                ->with(['cardType', 'customer', 'salesperson', 'saleBatch'])
                ->where($where)
                ->order('id', 'desc')
                ->paginate(input('limit', 15));

            foreach ($list as $key => $value) {
                $list[$key]['status_text'] = $this->getCardStatusText($value['status']);

                // 添加批次号信息
                if ($value['saleBatch']) {
                    $list[$key]['batch_no'] = $value['saleBatch']['batch_no'];
                    $list[$key]['sale_time'] = $value['saleBatch']['sale_time'];
                } else {
                    $list[$key]['batch_no'] = '';
                    $list[$key]['sale_time'] = '';
                }
            }

            return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $list->items()]);
        }

        // 获取卡型列表
        $cardTypes = model('CardPackageType')->where('status', 1)->select();
        $this->assign('cardTypes', $cardTypes);

        // 获取客户列表
        $customers = model('CardCustomer')->getEnumByBaseKeyNum();
        $this->assign('customers', $customers);

        // 获取销售员列表
        $salespersons = model('CardSale')->where('clientkeynum', session('cn_accountinfo.basekeynum'))->select();
        $this->assign('salespersons', $salespersons);

        return $this->fetch();
    }

    /**
     * 生成卡
     */
    public function generate()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $operator       = session('cn_accountinfo.accountname');
        $operator_id    = session('cn_accountinfo.account_id');
        if (request()->isAjax()) {
            $data = input('post.');
            $model = new \app\admin\model\Card;

            // 开启事务
            $model->startTrans();
            try {
                $count = intval($data['count']);
                $start_number = $data['start_number'];
                $batch_name = $data['batch_name'];
                $remark = isset($data['remark']) ? $data['remark'] : '';

                // 验证开始卡号格式
                if (!preg_match('/^([A-Za-z]+)(\d+)$/', $start_number, $matches)) {
                    throw new \Exception('开始卡号格式不正确，必须是字母开头加数字');
                }

                $prefix = $matches[1]; // 字母前缀
                $number = intval($matches[2]); // 数字部分
                $numberLength = strlen($matches[2]); // 数字部分长度，用于补零

                // 生成批次号
                $batch_no = 'B' . date('YmdHis') . mt_rand(1000, 9999);

                // 创建生成记录
                $generationRecord = new \app\admin\model\CardGenerationRecord;
                $generationRecord->data([
                    'clientkeynum' => $clientkeynum,
                    'batch_no' => $batch_no,
                    'batch_name' => $batch_name,
                    'quantity' => $count,
                    'start_number' => $start_number,
                    'end_number' => $prefix . str_pad($number + $count - 1, $numberLength, '0', STR_PAD_LEFT),
                    'operator_id' => $operator_id,
                    'operator_name' => $operator,
                    'success_count' => 0,
                    'fail_count' => 0,
                    'status' => 0, // 生成中
                    'remark' => $remark,
                    'add_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ]);
                $generationRecord->save();

                $successCount = 0;
                $failCount = 0;

                for ($i = 0; $i < $count; $i++) {
                    try {
                        // 生成卡号，保持数字部分的前导零
                        $currentNumber = $number + $i;
                        $card_no = $prefix . str_pad($currentNumber, $numberLength, '0', STR_PAD_LEFT);

                        // 检查卡号是否已存在
                        if ($model->where('card_no', $card_no)->find()) {
                            throw new \Exception("卡号 {$card_no} 已存在");
                        }

                        // 生成随机密码 (6位数字)
                        $password = sprintf("%06d", mt_rand(100000, 999999));
                        $encrypted_password = encrypt($password); // 加密密码

                        // 生成qrcode_text
                        $qrcode_text = $card_no . '-' . $password;
                        // 添加卡数据
                        $cardModel = new \app\admin\model\Card;
                        $cardModel->data([
                            'clientkeynum' => $clientkeynum,
                            'card_no' => $card_no,
                            'card_password' => $encrypted_password, // 存储加密后的密码
                            'password_raw' => $password, // 存储原始密码，用于导出
                            'generation_batch_no' => $batch_no,
                            'status' => 0, // 待销售
                            'remark' => $remark,
                            'qrcode_text' => $qrcode_text,
                            'add_time' => date('Y-m-d H:i:s'),
                            'update_time' => date('Y-m-d H:i:s')
                        ]);
                        $cardModel->save();

                        // 添加生成详情
                        $cardDetailModel = new \app\admin\model\CardGenerationDetail;
                        $cardDetailModel->data([
                            'clientkeynum' => $clientkeynum,
                            'generation_id' => $generationRecord->id,
                            'card_id' => $cardModel->id,
                            'card_no' => $card_no,
                            'original_number' => $i + 1,
                            'status' => 1, // 生成成功
                            'add_time' => date('Y-m-d H:i:s')
                        ]);
                        $cardDetailModel->save();

                        // 记录卡号状态变更
                        $cardStatusRecordModel = new \app\admin\model\CardStatusRecord;
                        $cardStatusRecordModel->data([
                            'clientkeynum' => $clientkeynum,
                            'card_id' => $cardModel->id,
                            'card_no' => $card_no,
                            'old_status' => 0, // 新生成的卡，旧状态为0（待销售）
                            'new_status' => 1,
                            'operator_id' => $operator_id,
                            'operator_name' => $operator,
                            'operation_time' => date('Y-m-d H:i:s'),
                            'reason' => '卡生成',
                            'add_time' => date('Y-m-d H:i:s')
                        ]);
                        $cardStatusRecordModel->save();

                        $successCount++;
                    } catch (\Exception $e) {
                        // 记录失败详情
                        $cardDetailModel = new \app\admin\model\CardGenerationDetail;
                        $cardDetailModel->data([
                            'clientkeynum' => $clientkeynum,
                            'generation_id' => $generationRecord->id,
                            'card_id' => 0,
                            'card_no' => '',
                            'original_number' => $i + 1,
                            'status' => 0, // 生成失败
                            'error_message' => $e->getMessage(),
                            'add_time' => date('Y-m-d H:i:s')
                        ]);
                        $cardDetailModel->save();

                        $failCount++;
                    }
                }

                // 更新生成记录
                $generationRecord->success_count = $successCount;
                $generationRecord->fail_count = $failCount;
                $generationRecord->status = $failCount == 0 ? 1 : ($successCount > 0 ? 1 : 2); // 1-生成完成，2-生成失败
                $generationRecord->update_time = date('Y-m-d H:i:s');
                $generationRecord->save();

                $model->commit();
                return json(['code' => 0, 'msg' => "生成成功，成功：{$successCount}张，失败：{$failCount}张", 'batch_no' => $batch_no]);
            } catch (\Exception $e) {
                $model->rollback();
                return json(['code' => 1, 'msg' => '生成失败：' . $e->getMessage()]);
            }
        }

        return $this->fetch();
    }

    /**
     * 卡销售
     */
    public function sell()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $operator     = session('cn_accountinfo.accountname');
        $operator_id  = session('cn_accountinfo.account_id');

        if (request()->isAjax()) {
            $data = input('post.');
            $model = new \app\admin\model\Card;

            // 开启事务
            $model->startTrans();
            try {
                // 获取客户信息
                $customerName = $data['customer_id'];
                $linkman = isset($data['linkman']) ? $data['linkman'] : '';
                $linktel = isset($data['linktel']) ? $data['linktel'] : '';
                $saleName = isset($data['sale_name']) ? $data['sale_name'] : '';
                // 为空就从今天开始
                if (empty($data['begin_time'])) {
                    $data['begin_time'] = date('Y-m-d');
                }
                $beginTime = isset($data['begin_time']) ? strtotime($data['begin_time'] . ' 00:00:00') : 0;
                $endTime = isset($data['end_time']) ? strtotime($data['end_time'] . ' 23:59:59') : 0;
                $remark = isset($data['content']) ? $data['content'] : '';
                $isAutoOpen = isset($data['is_auto_open']) ? intval($data['is_auto_open']) : 0;

                // 获取销售员信息
                $saleInfo = \think\Db::table('card_sale')->where([
                    'clientkeynum' => $clientkeynum,
                    'name' => $saleName
                ])->find();

                if (empty($saleInfo)) {
                    // 创建销售员
                    return json(['code' => 1, 'msg' => '销售员不存在']);
                } else {
                    $saleId = $saleInfo['id'];
                }

                // 获取客户信息
                $customerInfo = \think\Db::table('card_customer')->where([
                    'clientkeynum' => $clientkeynum,
                    'name' => $customerName
                ])->find();

                if (empty($customerInfo)) {
                    // 创建客户
                    return json(['code' => 1, 'msg' => '客户不存在']);
                } else {
                    $customerId = $customerInfo['id'];
                }

                // 生成批次号
                $batchNo = 'S' . date('YmdHis') . mt_rand(1000, 9999);

                // 创建销售批次主表数据
                $batchModel = new \app\admin\model\CardSaleBatch();
                $batchData = [
                    'clientkeynum' => $clientkeynum,
                    'batch_no' => $batchNo,
                    'customer_id' => $customerId,
                    'salesperson_id' => $saleId,
                    'total_price' => 0, // 先填充0，后面再更新
                    'card_count' => 0, // 先填充0，后面再更新
                    'sale_time' => date('Y-m-d H:i:s'),
                    'payment_method' => '1', // 默认现金
                    'transaction_no' => '',
                    'remark' => $remark,
                    'operator_id' => $operator_id,
                    'operator_name' => $operator,
                    'begin_time' => $beginTime ? date('Y-m-d 00:00:00', $beginTime) : null,
                    'end_time' => $endTime ? date('Y-m-d 23:59:59', $endTime) : null,
                    'linkman' => $linkman,
                    'linktel' => $linktel,
                    'is_auto_open' => $isAutoOpen
                ];
                $batchId = $batchModel->addSaleBatch($batchData);

                // 处理卡片数据
                $successCount = 0;
                $totalPrice = 0;
                $failedCards = []; // 记录失败的卡号和原因

                // 循环处理每个卡型和卡号范围
                $cardtypeIds = $data['cardtype_id'];
                $beginCards = $data['begin_card'];
                $endCards = $data['end_card'];
                $marketPrices = isset($data['market_price']) ? $data['market_price'] : [];
                $zhekous = isset($data['zhekou']) ? $data['zhekou'] : [];
                $endPrices = isset($data['end_price']) ? $data['end_price'] : [];
                $allPrices = isset($data['all_price']) ? $data['all_price'] : [];

                $saleRecordModel = new \app\admin\model\CardSaleRecord;
                $batchDetailModel = new \app\admin\model\CardSaleBatchDetail;

                // 创建单独的数组来跟踪每个索引下的批次详情
                $batchDetailData = [];

                for ($i = 0; $i < count($cardtypeIds); $i++) {
                    $cardtypeId = $cardtypeIds[$i];
                    $beginCard = $beginCards[$i];
                    $endCard = $endCards[$i];
                    $marketPrice = isset($marketPrices[$i]) ? $marketPrices[$i] : 0;
                    $zhekou = isset($zhekous[$i]) ? $zhekous[$i] : 100;
                    $endPrice = isset($endPrices[$i]) ? $endPrices[$i] : 0;
                    $allPrice = isset($allPrices[$i]) ? $allPrices[$i] : 0;

                    if (empty($cardtypeId) || empty($beginCard) || empty($endCard)) {
                        continue;
                    }

                    // 获取卡型信息
                    $cardTypeInfo = model('CardPackageType')->where('id', $cardtypeId)->find();
                    if ($cardTypeInfo) {
                        $marketPrice = $cardTypeInfo['price']; // 从卡型表获取价格
                    }

                    // 解析卡号
                    preg_match('/^([A-Za-z]*)(\d+)$/', $beginCard, $beginMatches);
                    preg_match('/^([A-Za-z]*)(\d+)$/', $endCard, $endMatches);

                    if (!$beginMatches || !$endMatches || $beginMatches[1] !== $endMatches[1]) {
                        throw new \Exception('卡号格式不正确或前缀不一致');
                    }

                    $prefix = $beginMatches[1];
                    $startNum = intval($beginMatches[2]);
                    $endNum = intval($endMatches[2]);
                    $numLength = strlen($beginMatches[2]);

                    // 验证卡号范围
                    if ($endNum <= $startNum) {
                        throw new \Exception('结束卡号必须大于起始卡号');
                    }

                    // 批次详情数据 - 记录每种卡型的详情
                    $typeCardCount = 0;
                    $typeAllPrice = 0;

                    // 初始化批次详情条目，使用索引i作为唯一键
                    $batchDetailData[$i] = [
                        'clientkeynum' => $clientkeynum,
                        'batch_id' => $batchId,
                        'batch_no' => $batchNo,
                        'type_id' => $cardtypeId,
                        'begin_card' => $beginCard,
                        'end_card' => $endCard,
                        'market_price' => $marketPrice,
                        'zhekou' => $zhekou,
                        'end_price' => $endPrice,
                        'card_count' => 0,
                        'all_price' => 0
                    ];

                    // 获取卡号列表并处理每张卡
                    for ($num = $startNum; $num <= $endNum; $num++) {
                        $cardNo = $prefix . str_pad($num, $numLength, '0', STR_PAD_LEFT);

                        // 检查卡是否存在并状态是否正确
                        $card = $model->where('card_no', $cardNo)->find();
                        if (!$card) {
                            $failedCards[] = ['card_no' => $cardNo, 'reason' => '卡号不存在'];
                            continue;
                        }

                        if ($card->status != 0) {
                            $statusText = $this->getCardStatusText($card->status);
                            $failedCards[] = ['card_no' => $cardNo, 'reason' => "卡状态不正确，当前状态：{$statusText}"];
                            continue;
                        }

                        try {
                            // 记录卡号状态变更
                            $cardStatusRecordModel = new \app\admin\model\CardStatusRecord;
                            $cardStatusRecordModel->data([
                                'clientkeynum' => $clientkeynum,
                                'card_id' => $card->id,
                                'card_no' => $card->card_no,
                                'old_status' => $card->status,
                                'new_status' => $isAutoOpen ? 3 : 1,
                                'operator_id' => $operator_id,
                                'operator_name' => $operator,
                                'operation_time' => date('Y-m-d H:i:s'),
                                'reason' => $isAutoOpen ? '卡销售并开卡' : '卡销售',
                                'add_time' => date('Y-m-d H:i:s')
                            ]);
                            if (!$cardStatusRecordModel->save()) {
                                throw new \Exception('状态记录保存失败');
                            }

                            // 更新卡信息
                            $card->type_id = $cardtypeId; // 设置卡型ID
                            $card->customer_id = $customerId;
                            $card->salesperson_id = $saleId;
                            $card->batch_id = $batchId; // 关联销售批次ID
                            $card->status = $isAutoOpen ? 3 : 1; // 3-已开卡, 1-已销售
                            $card->update_time = date('Y-m-d H:i:s');

                            // 设置有效期
                            if ($beginTime && $endTime) {
                                $card->valid_start_time = date('Y-m-d 00:00:00', $beginTime);
                                $card->valid_end_time = date('Y-m-d 23:59:59', $endTime);
                            }

                            if (!$card->save()) {
                                throw new \Exception('卡信息更新失败');
                            }

                            // 添加销售记录
                            $saleRecordData = [
                                'clientkeynum' => $clientkeynum,
                                'card_id' => $card->id,
                                'card_no' => $card->card_no,
                                'type_id' => $cardtypeId,
                                'customer_id' => $customerId,
                                'salesperson_id' => $saleId,
                                'sale_price' => $endPrice,
                                'zhekou' => $zhekou,
                                'sale_time' => date('Y-m-d H:i:s'),
                                'payment_method' => '1', // 默认现金
                                'batch_no' => $batchNo,
                                'batch_id' => $batchId,
                                'remark' => $remark,
                                'operator_id' => $operator_id,
                                'operator_name' => $operator,
                                'start_time' => $beginTime ? date('Y-m-d 00:00:00', $beginTime) : null,
                                'end_time' => $endTime ? date('Y-m-d 23:59:59', $endTime) : null,
                                'add_time' => date('Y-m-d H:i:s'),
                                'update_time' => date('Y-m-d H:i:s')
                            ];
                            if (!$saleRecordModel->insert($saleRecordData)) {
                                throw new \Exception('销售记录保存失败');
                            }

                            $successCount++;
                            $totalPrice += $endPrice;

                            // 累计当前卡型的卡数和总额
                            $typeCardCount++;
                            $typeAllPrice += $endPrice;

                            // 更新批次详情数据
                            $batchDetailData[$i]['card_count'] = $typeCardCount;
                            $batchDetailData[$i]['all_price'] = $typeAllPrice;
                        } catch (\Exception $e) {
                            $failedCards[] = ['card_no' => $cardNo, 'reason' => '数据库操作失败：' . $e->getMessage()];
                            continue;
                        }
                    }
                }

                // 检查是否有失败的卡，如果有则抛出异常进行回滚
                if (!empty($failedCards)) {
                    $failCount = count($failedCards);
                    $errorMsg = "销售失败，共{$failCount}张卡处理失败：\n";
                    
                    // 限制显示的失败卡数量，避免错误信息过长
                    $displayLimit = 10;
                    $displayCards = array_slice($failedCards, 0, $displayLimit);
                    
                    foreach ($displayCards as $failedCard) {
                        $errorMsg .= "卡号：{$failedCard['card_no']}，原因：{$failedCard['reason']}\n";
                    }
                    
                    if ($failCount > $displayLimit) {
                        $errorMsg .= "...还有" . ($failCount - $displayLimit) . "张卡失败";
                    }
                    
                    throw new \Exception($errorMsg);
                }

                // 添加所有批次详情记录
                foreach ($batchDetailData as $detailData) {
                    if ($detailData['card_count'] > 0) {
                        $batchDetailModel->addBatchDetail($detailData);
                    }
                }

                // 更新批次主表的总金额和卡数量
                $batchModel->updateBatch($batchId, [
                    'total_price' => $totalPrice,
                    'card_count' => $successCount,
                    'update_time' => date('Y-m-d H:i:s')
                ]);

                $model->commit();

                return json([
                    'code' => 0,
                    'msg' => "销售成功，共销售{$successCount}张卡",
                    'batch_no' => $batchNo
                ]);
            } catch (\Exception $e) {
                $model->rollback();
                \think\facade\Log::error('销售失败：' . $e->getMessage());
                return json(['code' => 1, 'msg' => '销售失败：' . $e->getMessage()]);
            }
        }

        // 获取卡型列表
        $cardTypes = model('CardPackageType')->where([
            'clientkeynum' => session('cn_accountinfo.basekeynum'),
            'status' => 1
        ])->select();
        $this->assign('cardTypes', $cardTypes);

        return $this->fetch();
    }

    /**
     * 获取客户信息
     */
    public function get_customer_info()
    {
        $basekeynum = session("cn_accountinfo.basekeynum");
        $customerName = input('customer_name', '');

        if (empty($customerName)) {
            return json(['sta' => 0, 'msg' => '客户名称不能为空']);
        }

        $customerInfo = \think\Db::name('card_customer')->where([
            'clientkeynum' => $basekeynum,
            'name' => $customerName
        ])->find();

        if (empty($customerInfo)) {
            return json(['sta' => 0, 'msg' => '客户不存在']);
        }

        // 获取销售员信息
        $saleInfo = \think\Db::name('card_sale')->where([
            'id' => $customerInfo['sale_id']
        ])->find();

        $customerInfo['sale_name'] = $saleInfo ? $saleInfo['name'] : '';
        $customerInfo['sta'] = 1;

        return json($customerInfo);
    }

    /**
     * 获取卡型信息
     */
    public function get_card_type_info()
    {
        $basekeynum = session("cn_accountinfo.basekeynum");
        $cardtypeId = input('cardtype_id', 0, 'intval');

        if (empty($cardtypeId)) {
            return json(['sta' => 0, 'msg' => '卡型ID不能为空']);
        }

        $cardTypeInfo = \think\Db::name('card_package_type')->where([
            'clientkeynum' => $basekeynum,
            'id' => $cardtypeId
        ])->find();

        if (empty($cardTypeInfo)) {
            return json(['sta' => 0, 'msg' => '卡型不存在']);
        }

        $cardTypeInfo['sta'] = 1;

        return json($cardTypeInfo);
    }

    /**
     * 获取客户列表用于自动完成
     */
    public function ajax_card_customer()
    {
        $basekeynum = session("cn_accountinfo.basekeynum");

        // 获取所有客户
        $customerList = \think\Db::name('card_customer')->where([
            'clientkeynum' => $basekeynum
        ])->field('id, name')->select();

        $result = [];
        foreach ($customerList as $customer) {
            $result[] = [
                'value' => $customer['name'],
                'label' => $customer['name']
            ];
        }

        return json($result);
    }

    /**
     * 更新卡状态
     */
    public function changeStatus()
    {
        if (request()->isAjax()) {
            $data = input('post.');
            $model = new \app\admin\model\Card;

            // 判断是批量处理还是单卡处理
            if (isset($data['change_type']) && $data['change_type'] === 'batch') {
                // 批量处理
                if (empty($data['card_list_json'])) {
                    return json(['code' => 1, 'msg' => '没有可变更的卡片数据']);
                }

                // 解析卡号列表
                $cardNos = json_decode($data['card_list_json'], true);
                if (empty($cardNos)) {
                    return json(['code' => 1, 'msg' => '没有可变更的卡片数据']);
                }

                // 开启事务
                $model->startTrans();
                try {
                    $successCount = 0;
                    $failCount = 0;

                    foreach ($cardNos as $cardNo) {
                        // 查询卡信息
                        $card = $model->where('card_no', $cardNo)->find();
                        if (!$card) {
                            $failCount++;
                            continue;
                        }

                        // 更新卡状态
                        $result = $model->updateCardStatus(
                            $card->id,
                            $data['status'],
                            session('cn_accountinfo.account_id'),
                            session('cn_accountinfo.accountname'),
                            $this->getStatusChangeReason($data['status']),
                            $data['remark'],
                            session('cn_accountinfo.basekeynum')
                        );

                        if ($result) {
                            $successCount++;
                        } else {
                            $failCount++;
                        }
                    }

                    $model->commit();

                    if ($successCount > 0) {
                        return json(['code' => 0, 'msg' => "批量状态变更完成, 成功: {$successCount}, 失败: {$failCount}"]);
                    } else {
                        return json(['code' => 1, 'msg' => '批量状态变更失败']);
                    }
                } catch (\Exception $e) {
                    $model->rollback();
                    return json(['code' => 1, 'msg' => '状态变更失败：' . $e->getMessage()]);
                }
            } else {
                // 单卡处理
                // 查询卡信息
                $card = $model->where('card_no', $data['card_no'])->find();
                if (!$card) {
                    return json(['code' => 1, 'msg' => '卡不存在']);
                }

                // 更新卡状态
                $result = $model->updateCardStatus(
                    $card->id,
                    $data['status'],
                    session('cn_accountinfo.account_id'),
                    session('cn_accountinfo.accountname'),
                    $this->getStatusChangeReason($data['status']),
                    $data['remark'],
                    session('cn_accountinfo.basekeynum')
                );

                if ($result) {
                    return json(['code' => 0, 'msg' => '状态变更成功']);
                } else {
                    return json(['code' => 1, 'msg' => '状态变更失败']);
                }
            }
        }

        return $this->fetch();
    }

    /**
     * 验证批量状态变更的卡片
     */
    public function validateStatusChange()
    {
        if (request()->isAjax()) {
            $startCardNo = input('start_card_no', '');
            $endCardNo = input('end_card_no', '');
            $targetStatus = input('target_status', '');

            if (empty($startCardNo) || empty($endCardNo)) {
                return json(['code' => 1, 'msg' => '请输入起始卡号和结束卡号']);
            }

            if (empty($targetStatus) && $targetStatus !== '0') {
                return json(['code' => 1, 'msg' => '请选择目标状态']);
            }

            // 提取前缀和数字部分
            $startMatch = preg_match('/^([A-Za-z]*)(\d+)$/', $startCardNo, $startMatches);
            $endMatch = preg_match('/^([A-Za-z]*)(\d+)$/', $endCardNo, $endMatches);

            if (!$startMatch || !$endMatch) {
                return json(['code' => 1, 'msg' => '卡号格式不正确，应为字母前缀+数字']);
            }

            // 检查前缀是否一致
            if ($startMatches[1] !== $endMatches[1]) {
                return json(['code' => 1, 'msg' => '起始卡号和结束卡号的前缀必须一致']);
            }

            $prefix = $startMatches[1];
            $startNum = intval($startMatches[2]);
            $endNum = intval($endMatches[2]);

            // 检查结束编号是否大于起始编号
            if ($endNum <= $startNum) {
                return json(['code' => 1, 'msg' => '结束卡号必须大于起始卡号']);
            }

            // 检查卡号数量是否过多
            $cardCount = $endNum - $startNum + 1;
            if ($cardCount > 1000) {
                return json(['code' => 1, 'msg' => '一次最多只能处理1000张卡']);
            }

            // 生成卡号并查询数据库
            $numLength = strlen($startMatches[2]);
            $cardList = [];
            $errorCards = [];
            $validCards = [];

            for ($i = $startNum; $i <= $endNum; $i++) {
                $numStr = str_pad($i, $numLength, '0', STR_PAD_LEFT);
                $cardNo = $prefix . $numStr;

                // 查询卡是否存在
                $card = model('Card')->where('card_no', $cardNo)->find();
                if (!$card) {
                    $errorCards[] = [
                        'card_no' => $cardNo,
                        'error_msg' => '卡号不存在'
                    ];
                    continue;
                }

                // 获取状态文本
                $statusText = '';
                switch ($card->status) {
                    case 0:
                        $statusText = '待销售';
                        break;
                    case 1:
                        $statusText = '已销售';
                        break;
                    case 2:
                        $statusText = '已兑换';
                        break;
                    case 3:
                        $statusText = '已开卡';
                        break;
                    case 4:
                        $statusText = '已关卡';
                        break;
                    case 5:
                        $statusText = '已废卡';
                        break;
                    case -1:
                        $statusText = '已过期';
                        break;
                    case -2:
                        $statusText = '已退卡';
                        break;
                    default:
                        $statusText = '未知状态';
                }

                // 检查是否可以更改到目标状态
                $canChange = true;

                // 以下为状态变更规则，可以根据实际业务调整
                if ($card->status == $targetStatus) {
                    $canChange = false;
                    $errorCards[] = [
                        'card_no' => $cardNo,
                        'error_msg' => '卡已经是目标状态，无需变更'
                    ];
                    continue;
                }

                // 这里检查特定状态是否可变更，根据业务逻辑增加条件
                // 例如：已作废的卡不能变更为其他状态等
                if ($card->status == 4) {
                    $canChange = false;
                    $errorCards[] = [
                        'card_no' => $cardNo,
                        'error_msg' => '已作废的卡不能变更状态'
                    ];
                    continue;
                }

                // 如果可以变更，添加到有效卡片列表
                if ($canChange) {
                    $validCards[] = [
                        'id' => $card->id,
                        'card_no' => $card->card_no,
                        'type_id' => $card->type_id,
                        'type_name' => $card->cardType ? $card->cardType->name : '',
                        'status' => $card->status,
                        'status_text' => $statusText
                    ];
                }
            }

            return json([
                'code' => 0,
                'msg' => '验证完成',
                'data' => [
                    'total' => $cardCount,
                    'valid_count' => count($validCards),
                    'error_count' => count($errorCards),
                    'valid_cards' => $validCards,
                    'error_cards' => $errorCards
                ]
            ]);
        }

        return json(['code' => 1, 'msg' => '非法请求']);
    }

    /**
     * 查看卡详情
     */
    public function detail($id)
    {
        // 获取卡信息
        $card = model('Card')
            ->with(['cardType', 'customer', 'salesperson', 'saleBatch'])
            ->where('id', $id)
            ->find();

        if (!$card) {
            $this->error('卡不存在');
        }

        // 获取销售记录
        $saleRecord = model('CardSaleRecord')
            ->where('card_id', $id)
            ->find();

        // 获取销售批次信息
        $saleBatch = null;
        if ($card->batch_id) {
            $saleBatch = model('CardSaleBatch')
                ->where('id', $card->batch_id)
                ->find();
        }

        // 获取状态变更记录
        $statusRecords = model('CardStatusRecord')
            ->where('card_id', $id)
            ->order('id', 'desc')
            ->select();

        // 获取兑换记录
        $redemptionRecords = model('CardPackageRedemption')
            ->where('card_id', $id)
            ->order('id', 'desc')
            ->select();

        $this->assign('card', $card);
        $this->assign('saleRecord', $saleRecord);
        $this->assign('saleBatch', $saleBatch);
        $this->assign('statusRecords', $statusRecords);
        $this->assign('redemptionRecords', $redemptionRecords);

        return $this->fetch();
    }

    /**
     * 导出卡列表
     */
    public function export()
    {
        $where = [];
        $card_no = input('card_no', '');
        $type_id = input('type_id', '');
        $status = input('status', '');
        $customer_id = input('customer_id', '');
        $salesperson_id = input('salesperson_id', '');
        $valid_start = input('valid_start', '');
        $valid_end = input('valid_end', '');

        if ($card_no) {
            $where[] = ['card_no', 'like', "%{$card_no}%"];
        }

        if ($type_id) {
            $where[] = ['type_id', '=', $type_id];
        }

        if ($status !== '') {
            $where[] = ['status', '=', $status];
        }

        if ($customer_id) {
            $where[] = ['customer_id', '=', $customer_id];
        }

        if ($salesperson_id) {
            $where[] = ['salesperson_id', '=', $salesperson_id];
        }

        if ($valid_start && $valid_end) {
            $where[] = ['valid_end_time', 'between', [$valid_start, $valid_end]];
        } else if ($valid_start) {
            $where[] = ['valid_end_time', '>=', $valid_start];
        } else if ($valid_end) {
            $where[] = ['valid_end_time', '<=', $valid_end];
        }

        $list = model('Card')
            ->with(['cardType', 'customer', 'salesperson'])
            ->where($where)
            ->order('id', 'desc')
            ->select();

        // 导出Excel
        $statusMap = [
            0 => '待销售',
            1 => '已销售',
            2 => '已兑换',
            3 => '已开卡',
            4 => '已关卡',
            5 => '已废卡',
            -1 => '已过期',
            -2 => '已退卡'
        ];

        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'card_no' => $item->card_no,
                'type_name' => $item->cardType ? $item->cardType->name : '',
                'customer_name' => $item->customer ? $item->customer->name : '',
                'salesperson_name' => $item->salesperson ? $item->salesperson->name : '',
                'status' => isset($statusMap[$item->status]) ? $statusMap[$item->status] : '',
                'valid_end_time' => $item->valid_end_time,
                'add_time' => $item->add_time
            ];
        }

        $header = [
            '卡号',
            '卡型',
            '客户',
            '销售员',
            '状态',
            '有效期',
            '添加时间'
        ];

        // 导出Excel
        $this->exportExcel('卡列表', $header, $data);
    }

    /**
     * 卡状态日志
     */
    public function statusLogs()
    {
        $id = input('id', 0, 'intval');
        $clientkeynum = session('cn_accountinfo.basekeynum');

        if (request()->isAjax()) {
            $limit = input('limit', 10, 'intval');
            $page = input('page', 1, 'intval');
            $startTime = input('start_time', '');
            $endTime = input('end_time', '');

            $where = [
                ['card_id', '=', $id],
                ['clientkeynum', '=', $clientkeynum]
            ];

            if ($startTime && $endTime) {
                $where[] = ['operation_time', 'between', [$startTime, $endTime]];
            } else if ($startTime) {
                $where[] = ['operation_time', '>=', $startTime];
            } else if ($endTime) {
                $where[] = ['operation_time', '<=', $endTime];
            }

            $list = model('CardStatusRecord')
                ->where($where)
                ->order('operation_time', 'desc')
                ->paginate($limit, false, ['page' => $page]);

            // 处理状态文本
            $data = [];
            foreach ($list->items() as $item) {
                $item['old_status_text'] = $this->getCardStatusText($item['old_status']);
                $item['new_status_text'] = $this->getCardStatusText($item['new_status']);
                $data[] = $item;
            }

            return json([
                'code' => 0,
                'msg' => 'success',
                'count' => $list->total(),
                'data' => $data
            ]);
        }

        // 获取卡信息
        $card = model('Card')->where('id', $id)->find();
        if (!$card) {
            $this->error('卡不存在');
        }

        $this->assign('card', $card);
        return $this->fetch();
    }

    /**
     * 导出Excel
     */
    private function exportExcel($title, $header, $data)
    {
        // 清除缓冲区
        ob_end_clean();

        // 实例化PHPExcel对象
        $objPHPExcel = new \PHPExcel();
        $objPHPExcel->getProperties()->setCreator('卡管理系统')
            ->setLastModifiedBy('卡管理系统')
            ->setTitle($title)
            ->setSubject($title)
            ->setDescription($title)
            ->setKeywords($title)
            ->setCategory($title);

        // 设置当前sheet和单元格信息
        $objPHPExcel->getActiveSheet()->setTitle($title);

        // 设置表头
        $colIndex = 0;
        foreach ($header as $val) {
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($colIndex, 1, $val);
            // 设置列宽
            $objPHPExcel->getActiveSheet()->getColumnDimension(chr(65 + $colIndex))->setWidth(20);
            $colIndex++;
        }

        // 设置数据
        $rowIndex = 2;
        foreach ($data as $row) {
            $colIndex = 0;
            foreach ($row as $val) {
                $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($colIndex, $rowIndex, $val);
                $colIndex++;
            }
            $rowIndex++;
        }

        // 设置样式
        $styleArray = [
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
            ],
            'borders' => [
                'allborders' => [
                    'style' => \PHPExcel_Style_Border::BORDER_THIN,
                ],
            ],
            'fill' => [
                'type' => \PHPExcel_Style_Fill::FILL_SOLID,
                'color' => ['rgb' => 'f2f2f2'],
            ],
        ];

        $objPHPExcel->getActiveSheet()->getStyle('A1:' . chr(65 + count($header) - 1) . '1')->applyFromArray($styleArray);

        // 创建Excel文件
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');

        // 设置响应头
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $title . '.xlsx"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');

        $objWriter->save('php://output');
        exit;
    }

    /**
     * 获取卡状态文本
     */
    private function getCardStatusText($status)
    {
        $statusMap = [
            0 => '待销售',
            1 => '已销售',
            2 => '已兑换',
            3 => '已开卡',
            4 => '已关卡',
            5 => '已废卡',
            -1 => '已过期',
            -2 => '已退卡'
        ];

        return isset($statusMap[$status]) ? $statusMap[$status] : '未知状态';
    }

    /**
     * 卡生成记录列表
     */
    public function generationRecords()
    {
        if (request()->isAjax()) {
            $where = [];
            $batch_no = input('batch_no', '');
            $status = input('status', '');
            $start_time = input('start_time', '');
            $end_time = input('end_time', '');

            if ($batch_no) {
                $where[] = ['batch_no', 'like', "%{$batch_no}%"];
            }


            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }

            if ($start_time && $end_time) {
                $where[] = ['add_time', 'between', [$start_time, $end_time]];
            } else if ($start_time) {
                $where[] = ['add_time', '>=', $start_time];
            } else if ($end_time) {
                $where[] = ['add_time', '<=', $end_time];
            }

            $list = model('CardGenerationRecord')
                ->where($where)
                ->order('id', 'desc')
                ->paginate(input('limit', 15));

            return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $list->items()]);
        }


        return $this->fetch();
    }

    /**
     * 卡号状态日志查询
     */
    public function cardStatusLogs()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');

        if (request()->isAjax()) {
            $limit = input('limit', 10, 'intval');
            $page = input('page', 1, 'intval');
            $cardNo = input('card_no', '');
            $startTime = input('start_time', '');
            $endTime = input('end_time', '');
            $status = input('status', '');

            // if (empty($cardNo)) {
            //     return json(['code' => 1, 'msg' => '请输入卡号', 'count' => 0, 'data' => []]);
            // }


            // // 先查询卡信息
            // $card = model('Card')->where([
            //     'clientkeynum' => $clientkeynum,
            //     'card_no' => $cardNo
            // ])->find();

            // if (!$card) {
            //     return json(['code' => 1, 'msg' => '卡号不存在', 'count' => 0, 'data' => []]);
            // }

            // 构建查询条件
            $where = [
                // ['card_id', '=', $card->id],
                ['clientkeynum', '=', $clientkeynum]
            ];

            if ($startTime && $endTime) {
                $where[] = ['operation_time', 'between', [$startTime, $endTime]];
            } else if ($startTime) {
                $where[] = ['operation_time', '>=', $startTime];
            } else if ($endTime) {
                $where[] = ['operation_time', '<=', $endTime];
            }

            if ($status !== '') {
                $where[] = ['new_status', '=', $status];
            }

            if ($cardNo) {
                $where[] = ['card_no', 'like', "%{$cardNo}%"];
            }

            $list = model('CardStatusRecord')
                ->with(['card'])
                ->where($where)
                ->order('operation_time', 'desc')
                ->paginate($limit, false, ['page' => $page]);

            // 处理状态文本
            $data = [];
            foreach ($list->items() as $item) {
                $item['old_status_text'] = $this->getCardStatusText($item['old_status']);
                $item['new_status_text'] = $this->getCardStatusText($item['new_status']);
                $data[] = $item;
            }

            return json([
                'code' => 0,
                'msg' => 'success',
                'count' => $list->total(),
                'data' => $data
            ]);
        }

        return $this->fetch();
    }

    /**
     * 查看批次生成详情
     */
    public function generationDetail($id)
    {
        // 获取生成记录
        $record = model('CardGenerationRecord')
            ->where('id', $id)
            ->find();

        if (!$record) {
            $this->error('生成记录不存在');
        }

        // 获取生成详情
        if (request()->isAjax()) {
            $where = [
                ['generation_id', '=', $id]
            ];

            $status = input('status', '');
            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }

            $card_no = input('card_no', '');
            if ($card_no) {
                $where[] = ['card_no', 'like', "%{$card_no}%"];
            }

            $list = model('CardGenerationDetail')
                ->where($where)
                ->order('id', 'asc')
                ->paginate(input('limit', 15));

            return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $list->items()]);
        }

        $this->assign('record', $record);
        return $this->fetch();
    }

    /**
     * 验证导出密码
     */
    public function verifyExportPassword()
    {
        if (!request()->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $batch_no = input('batch_no', '');
        $adminPassword = input('admin_password', '');

        // 验证参数
        if (empty($batch_no)) {
            return json(['code' => 1, 'msg' => '批次号不能为空']);
        }

        if (empty($adminPassword)) {
            return json(['code' => 1, 'msg' => '请输入高级密码']);
        }

        // 获取当前登录管理员信息
        $adminId = session('cn_accountinfo.keynum');
        $adminName = session('cn_accountinfo.accountname');
        $clientInfo = \think\Db::name('plat_client')->where('keynum', $adminId)->find();

        if (!$clientInfo) {
            return json(['code' => 1, 'msg' => '平台信息不存在']);
        }

        // 验证高级密码是否正确
        if (empty($clientInfo['super_password'])) {
            // 如果没有设置高级密码，则验证通过
            \think\facade\Log::info('管理员[' . $adminName . ']导出卡密批次验证通过（未设置高级密码）');
            return json(['code' => 0, 'msg' => '验证成功']);
        }

        if (!password_verify($adminPassword, $clientInfo['super_password'])) {
            \think\facade\Log::error('管理员[' . $adminName . ']导出卡密批次时高级密码验证失败');
            return json(['code' => 1, 'msg' => '高级密码验证失败']);
        }

        // 验证批次是否存在
        $record = model('CardGenerationRecord')->where('batch_no', $batch_no)->find();
        if (!$record) {
            return json(['code' => 1, 'msg' => '批次记录不存在']);
        }

        \think\facade\Log::info('管理员[' . $adminName . ']导出卡密批次密码验证成功，批次号：' . $batch_no);
        return json(['code' => 0, 'msg' => '验证成功']);
    }

    /**
     * 导出生成批次卡
     */
    public function exportBatchCards($batch_no)
    {
        $adminPassword = input('admin_password', '');

        // 验证高级密码
        if (empty($adminPassword)) {
            $this->error('请输入高级密码进行验证');
        }

        // 获取当前登录管理员信息
        $adminId = session('cn_accountinfo.keynum');
        $adminName = session('cn_accountinfo.accountname');
        $clientInfo = \think\Db::name('plat_client')->where('keynum', $adminId)->find();

        if (!$clientInfo) {
            $this->error('平台信息不存在');
        }

        // 验证高级密码是否正确（这里假设高级密码存储在管理员表的password字段中）
        if (!password_verify($adminPassword, $clientInfo['super_password'])) {
            \think\facade\Log::error('管理员[' . $adminName . ']导出卡密批次时高级密码验证失败');
            $this->error('高级密码验证失败');
        }

        // 获取批次信息
        $record = model('CardGenerationRecord')
            ->where('batch_no', $batch_no)
            ->find();

        if (!$record) {
            $this->error('批次记录不存在');
        }

        // 获取卡信息
        $cards = model('Card')
            ->where('generation_batch_no', $batch_no)
            ->select();

        // 导出Excel
        $data = [];
        foreach ($cards as $card) {
            // 使用原始密码字段，如果没有则尝试解密
            $password = $card->password_raw;
            if (empty($password) && !empty($card->card_password)) {
                // 尝试解密
                try {
                    $password = decrypt($card->card_password);
                } catch (\Exception $e) {
                    $password = '******'; // 解密失败则显示星号
                }
            }

            $data[] = [
                'card_no' => $card->card_no,
                'password' => $password, // 使用原始密码
                'qrcode_text' => $card->qrcode_text,
                'status' => $this->getCardStatusText($card->status),
                'add_time' => $card->add_time
            ];
        }

        $header = ['卡号', '密码', '二维码内容', '状态', '添加时间'];

        // 记录导出操作日志
        addoperatelog('导出卡密批次', '批次号:' . $batch_no . ',数量:' . count($data));

        // 导出Excel
        $this->exportExcel("卡批次{$batch_no}列表", $header, $data);
    }

    /**
     * 卡批量销售记录
     */
    public function batchSaleRecords()
    {
        if (request()->isAjax()) {
            $where = [];
            $batch_no = input('batch_no', '');
            $customer_id = input('customer_id', '');
            $salesperson_id = input('salesperson_id', '');
            $start_time = input('start_time', '');
            $end_time = input('end_time', '');

            if ($batch_no) {
                $where[] = ['batch_no', 'like', "%{$batch_no}%"];
            }

            if ($customer_id) {
                $where[] = ['customer_id', '=', $customer_id];
            }

            if ($salesperson_id) {
                $where[] = ['salesperson_id', '=', $salesperson_id];
            }

            if ($start_time && $end_time) {
                $where[] = ['sale_time', 'between', [$start_time, $end_time]];
            } else if ($start_time) {
                $where[] = ['sale_time', '>=', $start_time];
            } else if ($end_time) {
                $where[] = ['sale_time', '<=', $end_time];
            }

            // 从销售批次主表查询
            $list = model('CardSaleBatch')
                ->where($where)
                ->order('sale_time', 'desc')
                ->paginate(input('limit', 15));

            // 获取关联数据
            $result = [];
            foreach ($list->items() as $item) {
                $customer = model('CardCustomer')->where('id', $item['customer_id'])->find();
                $salesperson = model('CardSale')->where('id', $item['salesperson_id'])->find();

                $item['customer_name'] = $customer ? $customer['name'] : '';
                $item['salesperson_name'] = $salesperson ? $salesperson['name'] : '';
                $item['payment_method_text'] = $this->getPaymentMethodText($item['payment_method']);

                // 获取批次详情信息
                $batchDetails = model('CardSaleBatchDetail')->where('batch_id', $item['id'])->select();
                $item['detail_count'] = count($batchDetails);

                $result[] = $item;
            }

            return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $result]);
        }

        // 获取客户列表
        $customers = model('CardCustomer')->select();
        $this->assign('customers', $customers);

        // 获取销售员列表
        $salespeople = model('CardSale')->select();
        $this->assign('salespeople', $salespeople);

        return $this->fetch();
    }

    /**
     * 销售批次详情
     * @param int $id 批次ID
     */
    public function batchSaleDetail($id)
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');

        // 获取批次信息
        $batchInfo = model('CardSaleBatch')->with(['customer', 'salesperson'])->where([
            'id' => $id,
            'clientkeynum' => $clientkeynum
        ])->find();

        if (!$batchInfo) {
            $this->error('批次不存在');
        }

        // 获取批次详情列表
        $batchDetails = model('CardSaleBatchDetail')->with(['cardType'])->where([
            'batch_id' => $id,
            'clientkeynum' => $clientkeynum
        ])->select();

        // 不再直接加载所有卡信息，改为通过AJAX请求分页加载

        $this->assign('batchInfo', $batchInfo);
        $this->assign('batchDetails', $batchDetails);

        return $this->fetch();
    }

    /**
     * 比较两个卡号的大小
     * @param string $cardNo1 卡号1
     * @param string $cardNo2 卡号2
     * @return int 返回 -1 表示 $cardNo1 < $cardNo2，0 表示相等，1 表示 $cardNo1 > $cardNo2
     */
    private function compareCardNumbers($cardNo1, $cardNo2)
    {
        // 解析卡号
        preg_match('/^([A-Za-z]*)(\d+)$/', $cardNo1, $matches1);
        preg_match('/^([A-Za-z]*)(\d+)$/', $cardNo2, $matches2);

        if (!$matches1 || !$matches2) {
            return strcmp($cardNo1, $cardNo2); // 格式不符合预期时直接进行字符串比较
        }

        $prefix1 = $matches1[1];
        $prefix2 = $matches2[1];

        // 先比较前缀
        if ($prefix1 !== $prefix2) {
            return strcmp($prefix1, $prefix2);
        }

        // 前缀相同则比较数字部分
        $num1 = intval($matches1[2]);
        $num2 = intval($matches2[2]);

        if ($num1 < $num2) {
            return -1;
        } else if ($num1 > $num2) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 获取支付方式文本
     */
    private function getPaymentMethodText($paymentMethod)
    {
        $methods = [
            1 => '现金',
            2 => '微信',
            3 => '支付宝',
            4 => '银行卡'
        ];

        return isset($methods[$paymentMethod]) ? $methods[$paymentMethod] : '未知';
    }

    /**
     * 验证卡号范围并返回卡片列表
     */
    public function validateCardRange()
    {
        if (request()->isAjax()) {
            $startCardNo = input('start_card_no', '');
            $endCardNo = input('end_card_no', '');
            $typeId = input('type_id', 0, 'intval');
            $zhekou = input('zhekou', 100, 'floatval');
            $endPrice = input('end_price', 0, 'floatval');
            $type = input('type', 1, 'intval'); // 1-根据折扣计算价格, 2-根据价格计算折扣

            if (empty($startCardNo) || empty($endCardNo)) {
                return json(['code' => 1, 'msg' => '请输入起始卡号和结束卡号']);
            }

            // 提取前缀和数字部分
            $startMatch = preg_match('/^([A-Za-z]*)(\d+)$/', $startCardNo, $startMatches);
            $endMatch = preg_match('/^([A-Za-z]*)(\d+)$/', $endCardNo, $endMatches);

            if (!$startMatch || !$endMatch) {
                return json(['code' => 1, 'msg' => '卡号格式不正确，应为字母前缀+数字']);
            }

            // 检查前缀是否一致
            if ($startMatches[1] !== $endMatches[1]) {
                return json(['code' => 1, 'msg' => '起始卡号和结束卡号的前缀必须一致']);
            }

            $prefix = $startMatches[1];
            $startNum = intval($startMatches[2]);
            $endNum = intval($endMatches[2]);

            // 检查结束编号是否大于起始编号
            if ($endNum <= $startNum) {
                return json(['code' => 1, 'msg' => '结束卡号必须大于起始卡号']);
            }

            // 检查卡号数量是否过多
            $cardCount = $endNum - $startNum + 1;
            if ($cardCount > 1000) {
                return json(['code' => 1, 'msg' => '一次最多只能处理1000张卡']);
            }

            // 获取卡型信息
            $cardType = model('CardPackageType')->where('id', $typeId)->find();
            if (!$cardType) {
                return json(['code' => 1, 'msg' => '卡型不存在']);
            }

            $marketPrice = $cardType->price;

            // 根据操作类型计算价格或折扣
            if ($type == 1) { // 根据折扣计算价格
                $endPrice = round($marketPrice * ($zhekou / 100), 2);
            } else if ($type == 2) { // 根据价格计算折扣
                if ($marketPrice > 0) {
                    $zhekou = round(($endPrice / $marketPrice) * 100, 2);
                } else {
                    $zhekou = 100;
                }
            }

            // 计算总价
            $allPrice = round($endPrice * $cardCount, 2);

            // 生成卡号并查询数据库
            $numLength = strlen($startMatches[2]);
            $cardList = [];
            $errorCards = [];
            $validCards = [];

            for ($i = $startNum; $i <= $endNum; $i++) {
                $numStr = str_pad($i, $numLength, '0', STR_PAD_LEFT);
                $cardNo = $prefix . $numStr;

                // 查询卡是否存在
                $card = model('Card')->where('card_no', $cardNo)->find();
                if (!$card) {
                    $errorCards[] = [
                        'card_no' => $cardNo,
                        'error_msg' => '卡号不存在'
                    ];
                    continue;
                }

                // 检查卡状态
                if ($card->status != 0) {
                    $errorCards[] = [
                        'card_no' => $cardNo,
                        'error_msg' => '卡状态不正确，只能销售待销售的卡'
                    ];
                    continue;
                }

                // 构建有效卡信息
                $validCards[] = [
                    'id' => $card->id,
                    'card_no' => $card->card_no,
                    'type_id' => $typeId,
                    'type_name' => $cardType ? $cardType->name : '',
                    'status' => 0,
                    'status_text' => '待销售'
                ];
            }

            return json([
                'code' => 0,
                'msg' => '验证完成',
                'data' => [
                    'total' => $cardCount,
                    'valid_count' => count($validCards),
                    'error_count' => count($errorCards),
                    'valid_cards' => $validCards,
                    'error_cards' => $errorCards,
                    'zhekou' => round($zhekou, 2),
                    'end_price' => round($endPrice, 2),
                    'all_price' => round($allPrice, 2)
                ]
            ]);
        }

        return json(['code' => 1, 'msg' => '非法请求']);
    }

    /**
     * 处理Excel上传 - 预览卡号
     */
    public function ajax_add_cardbatch()
    {
        // 定义路径
        $rootPath = \think\facade\Env::get('root_path');
        ini_set("memory_limit", "368M");
        set_time_limit(3600);

        // 获取表单上传文件
        $file = request()->file('file');
        $path = $rootPath . 'public' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . 'excel';
        $info = $file->move($path);
        $clientkeynum = session("cn_accountinfo.basekeynum");

        // 数据为空返回错误
        if (empty($info)) {
            $msg['sta'] = -1;
            $msg['msg'] = "导入失败";
            return json($msg);
        }

        // 获取文件名
        $exclePath = $info->getSaveName();
        // 上传文件的地址
        $filename = $rootPath . 'public' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . 'excel' . DIRECTORY_SEPARATOR . $exclePath;

        // 使用PHPExcel库读取Excel文件
        $objReader = \PHPExcel_IOFactory::createReader('Excel2007');
        if (!$objReader->canRead($filename)) {
            $objReader = \PHPExcel_IOFactory::createReader('Excel5');
        }
        $objPHPExcel = $objReader->load($filename);
        $sheet = $objPHPExcel->getSheet(0);
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

        // 转换为数组
        $arr = [];
        for ($row = 1; $row <= $highestRow; $row++) {
            $rowData = [];
            for ($col = 'A'; $col <= $highestColumn; $col++) {
                $value = $sheet->getCell($col . $row)->getValue();
                $rowData[] = $value;
            }
            if (!empty($rowData[0])) {
                $arr[] = $rowData;
            }
        }

        // 检查数据是否为空
        if (empty($arr)) {
            $msg['sta'] = 0;
            $msg['msg'] = "Excel为空";
            return json($msg);
        }

        // 检查卡号是否重复
        $error = 0;
        $errormsg = "";
        $list = [];

        foreach ($arr as $key => $value) {
            $cardnum = trim($value[0]);
            // 跳过标题行
            if ($key === 0 && ($cardnum === '卡号' || $cardnum === '卡号' || $cardnum === '卡号密码')) {
                continue;
            }

            // 检查卡号是否已存在
            $find = \think\Db::name('card')->where([
                'card_no' => $cardnum,
                'clientkeynum' => $clientkeynum
            ])->find();

            if ($find) {
                $error++;
                $errormsg .= $cardnum . "重复,请重新填写 ";
            } else {
                // 判断是否有密码
                if (isset($value[1]) && !empty($value[1])) {
                    $list[] = $cardnum . "(" . $value[1] . ")";
                } else {
                    $list[] = $cardnum;
                }
            }
        }

        $count = count($list);
        // 随机抽取几个卡号用于预览
        $previewCount = min(5, $count);
        $newlist = array_slice($list, rand(0, max(0, $count - $previewCount)), $previewCount);

        if ($error > 0) {
            $msg['sta'] = 0;
            $msg['msg'] = $errormsg;
            $msg['list'] = $newlist;
            $msg['all_cardlist'] = $list;
            $msg['count'] = $count;
        } else {
            $msg['sta'] = 1;
            $msg['msg'] = "生成成功";
            $msg['list'] = $newlist;
            $msg['all_cardlist'] = $list;
            $msg['count'] = $count;
        }

        return json($msg);
    }

    /**
     * 处理导入卡号提交
     */
    public function ajax_add_card()
    {
        $clientkeynum = session("cn_accountinfo.basekeynum");
        $operator = session('cn_accountinfo.accountname');
        $operator_id = session('cn_accountinfo.account_id');

        $param = request()->param();

        // 卡号列表
        $cardnumberlist = explode(',', trim($param['cardnumberlist'], ','));
        $count = count($cardnumberlist);

        if (empty($cardnumberlist)) {
            return json(['sta' => 0, 'msg' => '请上传卡号']);
        }

        // 开启事务
        \think\Db::startTrans();
        try {
            // 处理第一个和最后一个卡号
            $first = $cardnumberlist[0];
            $last = end($cardnumberlist);

            // 检查是否包含密码
            $firstCardNo = $first;
            if (strpos($first, '(') !== false) {
                $firstCardNo = substr($first, 0, strpos($first, '('));
            }

            $lastCardNo = $last;
            if (strpos($last, '(') !== false) {
                $lastCardNo = substr($last, 0, strpos($last, '('));
            }

            // 生成批次号
            $batch_no = 'B' . date('YmdHis') . mt_rand(1000, 9999);

            // 创建生成记录
            $generationRecord = new \app\admin\model\CardGenerationRecord;
            $generationRecord->data([
                'clientkeynum' => $clientkeynum,
                'batch_no' => $batch_no,
                'batch_name' => $param['piciname'],
                'quantity' => $count,
                'start_number' => $firstCardNo,
                'end_number' => $lastCardNo,
                'operator_id' => $operator_id,
                'operator_name' => $operator,
                'success_count' => 0,
                'fail_count' => 0,
                'status' => 0, // 生成中
                'remark' => isset($param['content']) ? $param['content'] : '',
                'add_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]);
            $generationRecord->save();

            $successCount = 0;
            $failCount = 0;

            foreach ($cardnumberlist as $key => $value) {
                try {
                    // 解析卡号和密码
                    $cardnum = $value;
                    $password = '';

                    if (strpos($value, '(') !== false && strpos($value, ')') !== false) {
                        $parts = explode('(', $value);
                        $cardnum = $parts[0];
                        $password = substr($parts[1], 0, strpos($parts[1], ')'));
                    }

                    // 如果没有提供密码，则生成随机密码
                    if (empty($password)) {
                        $password = sprintf("%06d", mt_rand(100000, 999999));
                    }

                    // 加密密码
                    $encrypted_password = encrypt($password);

                    // 检查卡号是否已存在
                    if (\think\Db::name('card')->where(['card_no' => $cardnum])->find()) {
                        throw new \Exception("卡号 {$cardnum} 已存在");
                    }

                    // 添加卡数据
                    $cardModel = new \app\admin\model\Card;
                    $cardModel->data([
                        'clientkeynum' => $clientkeynum,
                        'card_no' => $cardnum,
                        'card_password' => $encrypted_password,
                        'password_raw' => $password, // 存储原始密码，用于导出
                        'generation_batch_no' => $batch_no,
                        'status' => 0, // 待销售
                        'remark' => isset($param['content']) ? $param['content'] : '',
                        'add_time' => date('Y-m-d H:i:s'),
                        'update_time' => date('Y-m-d H:i:s')
                    ]);
                    $cardModel->save();

                    // 添加生成详情
                    $cardDetailModel = new \app\admin\model\CardGenerationDetail;
                    $cardDetailModel->data([
                        'clientkeynum' => $clientkeynum,
                        'generation_id' => $generationRecord->id,
                        'card_id' => $cardModel->id,
                        'card_no' => $cardnum,
                        'original_number' => $key + 1,
                        'status' => 1, // 生成成功
                        'add_time' => date('Y-m-d H:i:s')
                    ]);
                    $cardDetailModel->save();

                    // 记录卡号状态变更
                    $cardStatusRecordModel = new \app\admin\model\CardStatusRecord;
                    $cardStatusRecordModel->data([
                        'clientkeynum' => $clientkeynum,
                        'card_id' => $cardModel->id,
                        'card_no' => $cardnum,
                        'old_status' => 0, // 新生成的卡，旧状态为0（待销售）
                        'new_status' => 1,
                        'operator_id' => $operator_id,
                        'operator_name' => $operator,
                        'operation_time' => date('Y-m-d H:i:s'),
                        'reason' => '卡生成',
                        'add_time' => date('Y-m-d H:i:s')
                    ]);
                    $cardStatusRecordModel->save();


                    $successCount++;
                } catch (\Exception $e) {
                    // 记录失败详情
                    $cardDetailModel = new \app\admin\model\CardGenerationDetail;
                    $cardDetailModel->data([
                        'clientkeynum' => $clientkeynum,
                        'generation_id' => $generationRecord->id,
                        'card_id' => 0,
                        'card_no' => isset($cardnum) ? $cardnum : '',
                        'original_number' => $key + 1,
                        'status' => 0, // 生成失败
                        'error_message' => $e->getMessage(),
                        'add_time' => date('Y-m-d H:i:s')
                    ]);
                    $cardDetailModel->save();

                    $failCount++;
                }
            }

            // 更新生成记录
            $generationRecord->success_count = $successCount;
            $generationRecord->fail_count = $failCount;
            $generationRecord->status = $failCount == 0 ? 1 : ($successCount > 0 ? 1 : 2); // 1-生成完成，2-生成失败
            $generationRecord->update_time = date('Y-m-d H:i:s');
            $generationRecord->save();

            \think\Db::commit();
            return json(['sta' => 1, 'msg' => "生成成功，成功：{$successCount}张，失败：{$failCount}张", 'batch_no' => $batch_no]);
        } catch (\Exception $e) {
            \think\Db::rollback();
            return json(['sta' => 0, 'msg' => '生成失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取批次卡列表接口
     */
    public function getBatchCardList()
    {
        if (request()->isAjax()) {
            $batchId = input('batch_id', 0, 'intval');
            $limit = input('limit', 10, 'intval');
            $page = input('page', 1, 'intval');
            $clientkeynum = session('cn_accountinfo.basekeynum');

            // 获取批次信息
            $batchInfo = model('CardSaleBatch')->where([
                'id' => $batchId,
                'clientkeynum' => $clientkeynum
            ])->find();

            if (!$batchInfo) {
                return json(['code' => 1, 'msg' => '批次不存在', 'count' => 0, 'data' => []]);
            }

            // 分页获取卡列表
            $cards = model('Card')
                ->with(['cardType'])
                ->where([
                    'batch_id' => $batchId,
                    'clientkeynum' => $clientkeynum
                ])
                ->order('id', 'asc')
                ->paginate($limit, false, ['page' => $page]);

            // 处理状态文本
            $result = [];
            foreach ($cards->items() as $card) {
                $card['status_text'] = $this->getCardStatusText($card['status']);
                $card['sale_time'] = $batchInfo['sale_time'];
                $card['card_type_name'] = $card['cardType'] ? $card['cardType']['name'] : '--';
                $result[] = $card;
            }

            return json([
                'code' => 0,
                'msg' => 'success',
                'count' => $cards->total(),
                'data' => $result
            ]);
        }

        return json(['code' => 1, 'msg' => '非法请求', 'count' => 0, 'data' => []]);
    }

    /**
     * 卡状态变更页面
     */
    public function changeCardStatus()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');

        if (request()->isAjax()) {
            $data = input('post.');
            $model = new \app\admin\model\Card;

            // 开启事务
            $model->startTrans();
            try {
                // 获取卡号列表
                $cardNos = explode(',', $data['card_nums']);
                if (empty($cardNos)) {
                    throw new \Exception('没有可变更的卡片');
                }

                // 移除空值和重复值
                $cardNos = array_filter(array_unique($cardNos), function ($item) {
                    return !empty(trim($item));
                });

                if (empty($cardNos)) {
                    throw new \Exception('没有有效的卡号');
                }

                // 创建批次记录
                $batchModel = new \app\admin\model\CardStatusChangeBatch;
                $batch = $batchModel::createBatch(
                    $data['customer_id'],
                    $data['status'],
                    count($cardNos),
                    '系统状态变更',
                    $data['remark'],
                    session('cn_accountinfo.account_id'),
                    session('cn_accountinfo.accountname'),
                    $clientkeynum
                );

                if (!$batch) {
                    throw new \Exception('创建批次记录失败');
                }

                $successCount = 0;
                $failCount = 0;
                $failCards = [];

                foreach ($cardNos as $cardNo) {
                    // 查询卡信息
                    $card = $model->where([
                        'clientkeynum' => $clientkeynum,
                        'card_no' => $cardNo
                    ])->find();

                    if (!$card) {
                        $failCount++;
                        $failCards[] = $cardNo;
                        continue;
                    }

                    // 验证状态是否可以变更
                    $canChange = false;
                    switch ($data['status']) {
                        case '3': // 开卡
                            $canChange = in_array($card->status, [1]); // 已销售可开卡
                            break;
                        case '4': // 关卡
                            $canChange = in_array($card->status, [3]); // 已开卡可关卡
                            break;
                        case '5': // 废卡
                            $canChange = in_array($card->status, [0, 1, 3, 4]); // 待销售/已销售/已开卡/已关卡可废卡
                            break;
                    }

                    if (!$canChange) {
                        $failCount++;
                        $failCards[] = $cardNo;
                        continue;
                    }

                    // 更新卡状态
                    $result = $model->updateCardStatus(
                        $card->id,
                        $data['status'],
                        session('cn_accountinfo.account_id'),
                        session('cn_accountinfo.accountname'),
                        $this->getStatusChangeReason($data['status']),
                        $data['remark'],
                        session('cn_accountinfo.basekeynum'),
                        $batch->id
                    );

                    if ($result) {
                        $successCount++;
                    } else {
                        $failCount++;
                        $failCards[] = $cardNo;
                    }
                }

                // 更新批次统计数据
                $batchModel::updateBatchStats($batch->id, $successCount, $failCount);

                $model->commit();

                $msg = "状态变更完成，成功：{$successCount}张";
                if ($failCount > 0) {
                    $msg .= "，失败：{$failCount}张";
                    if (!empty($failCards)) {
                        $msg .= "，失败卡号：" . implode(',', $failCards);
                    }
                }

                return json(['code' => 0, 'msg' => $msg, 'batch_id' => $batch->id]);
            } catch (\Exception $e) {
                $model->rollback();
                return json(['code' => 1, 'msg' => '状态变更失败：' . $e->getMessage()]);
            }
        }

        // 获取客户列表
        $customers = model('CardCustomer')->where([
            'clientkeynum' => $clientkeynum
        ])->select();
        $this->assign('customers', $customers);

        return $this->fetch('change_card_status');
    }

    /**
     * 获取卡状态变更预览数据
     */
    public function getCardStatusChangePreview()
    {
        if (request()->isAjax()) {
            $customerId = input('customer_id', 0, 'intval');
            $status = input('status', '');
            $saleNumber = input('sale_number', '');
            $startCardNo = input('start_card_no', '');
            $endCardNo = input('end_card_no', '');
            $clientkeynum = session('cn_accountinfo.basekeynum');

            if (empty($customerId)) {
                return json(['code' => 1, 'msg' => '请选择客户']);
            }

            if (empty($status)) {
                return json(['code' => 1, 'msg' => '请选择目标状态']);
            }

            // 根据销售单号或卡号范围查询卡
            $cardList = [];
            $cardNos = [];

            try {
                if (!empty($saleNumber)) {
                    // 根据销售单号查询
                    $batch = model('CardSaleBatch')->where([
                        'clientkeynum' => $clientkeynum,
                        'batch_no' => $saleNumber
                    ])->find();

                    if (!$batch) {
                        return json(['code' => 1, 'msg' => '销售单号不存在']);
                    }

                    $cardList = model('Card')->where([
                        'clientkeynum' => $clientkeynum,
                        'customer_id' => $customerId,
                        'batch_id' => $batch->id
                    ])->select();
                } else if (!empty($startCardNo) && !empty($endCardNo)) {
                    // 解析卡号
                    $prefix = '';
                    $startNum = 0;
                    $endNum = 0;
                    $numLength = 0;

                    // 提取卡号前缀和数字部分
                    preg_match('/^([^\d]*)(\d+)$/', $startCardNo, $startMatches);
                    preg_match('/^([^\d]*)(\d+)$/', $endCardNo, $endMatches);

                    if (count($startMatches) < 3 || count($endMatches) < 3) {
                        return json(['code' => 1, 'msg' => '卡号格式无法解析']);
                    }

                    $startPrefix = $startMatches[1];
                    $endPrefix = $endMatches[1];
                    if ($startPrefix !== $endPrefix) {
                        return json(['code' => 1, 'msg' => '卡号前缀不一致']);
                    }

                    $prefix = $startPrefix;
                    $startNum = intval($startMatches[2]);
                    $endNum = intval($endMatches[2]);
                    $numLength = strlen($startMatches[2]);

                    if ($startNum > $endNum) {
                        return json(['code' => 1, 'msg' => '起始卡号大于结束卡号']);
                    }

                    if ($endNum - $startNum > 10000) {
                        return json(['code' => 1, 'msg' => '卡号范围过大，一次最多支持10000张卡']);
                    }

                    // 生成卡号列表
                    for ($i = $startNum; $i <= $endNum; $i++) {
                        $cardNos[] = $prefix . str_pad($i, $numLength, '0', STR_PAD_LEFT);
                    }
                    $cardList = model('Card')->where([
                        'clientkeynum' => $clientkeynum,
                    ])->where('card_no', 'in', $cardNos)->select();
                } else {
                    return json(['code' => 1, 'msg' => '请输入销售单号或卡号范围']);
                }

                if (empty($cardList)) {
                    return json(['code' => 1, 'msg' => '未找到符合条件的卡']);
                }

                // 处理预览数据
                $previewData = [];
                $failCards = [];
                $changeableCount = 0;
                $nonChangeableCount = 0;

                foreach ($cardList as $card) {
                    $canChange = false;
                    switch ($status) {
                        case '3': // 开卡
                            $canChange = in_array($card->status, [1]); // 已销售可开卡
                            break;
                        case '4': // 关卡
                            $canChange = in_array($card->status, [3]); // 已开卡可关卡
                            break;
                        case '5': // 废卡
                            $canChange = in_array($card->status, [0, 1, 3, 4]); // 待销售/已销售/已开卡/已关卡可废卡
                            break;
                    }

                    if (!$canChange) {
                        $failCards[] = $card->card_no;
                        $nonChangeableCount++;
                    } else {
                        $changeableCount++;
                    }

                    $previewData[] = [
                        'card_no' => $card->card_no,
                        'type_name' => $card->cardType ? $card->cardType->name : '',
                        'status' => $this->getCardStatusText($card->status),
                        'status_code' => $card->status,
                        'can_change' => $canChange
                    ];
                }

                // 统计信息
                $stats = [
                    'changeable_count' => $changeableCount,
                    'non_changeable_count' => $nonChangeableCount
                ];

                // 提取可用于预览的纯卡号列表
                $plainCardNos = array_column($previewData, 'card_no');

                return json([
                    'code' => 0,
                    'msg' => '获取预览数据成功',
                    'data' => $previewData,
                    'plain_card_nos' => $plainCardNos,
                    'stats' => $stats
                ]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '获取预览数据失败：' . $e->getMessage()]);
            }
        }

        return json(['code' => 1, 'msg' => '非法请求']);
    }

    /**
     * 根据卡号列表获取预览数据
     */
    public function getCardPreviewData()
    {
        if (request()->isAjax()) {
            $cardnum = input('cardnum', '');
            $customerId = input('customer_id', 0, 'intval');
            $status = input('status', '');
            $clientkeynum = session('cn_accountinfo.basekeynum');

            // 验证参数
            if (empty($customerId)) {
                return json(['code' => 1, 'msg' => '请选择客户']);
            }

            if (empty($status)) {
                return json(['code' => 1, 'msg' => '请选择目标状态']);
            }

            if (empty($cardnum)) {
                return json(['code' => 1, 'msg' => '请输入卡号']);
            }

            // 处理卡号
            $cardRows = explode("\n", $cardnum);
            $cardNos = array_map('trim', $cardRows);
            $cardNos = array_filter($cardNos, function($card) {
                return !empty($card);
            });

            if (empty($cardNos)) {
                return json(['code' => 1, 'msg' => '未找到有效的卡号']);
            }

            try {
                // 查询卡信息
                $cardList = model('Card')->where([
                    'clientkeynum' => $clientkeynum,
                    'customer_id' => $customerId
                ])->where('card_no', 'in', $cardNos)->select();

                // 处理预览数据
                $previewData = [];
                $failCards = [];
                $changeableCount = 0;
                $nonChangeableCount = 0;

                foreach ($cardList as $card) {
                    $canChange = false;
                    switch ($status) {
                        case '3': // 开卡
                            $canChange = in_array($card->status, [1]); // 已销售可开卡
                            break;
                        case '4': // 关卡
                            $canChange = in_array($card->status, [3]); // 已开卡可关卡
                            break;
                        case '5': // 废卡
                            $canChange = in_array($card->status, [0, 1, 3, 4]); // 待销售/已销售/已开卡/已关卡可废卡
                            break;
                    }

                    $previewData[] = [
                        'card_no' => $card->card_no,
                        'type_name' => $card->cardType ? $card->cardType->name : '',
                        'status' => $this->getCardStatusText($card->status),
                        'status_code' => $card->status,
                        'can_change' => $canChange
                    ];

                    if (!$canChange) {
                        $failCards[] = $card->card_no;
                        $nonChangeableCount++;
                    } else {
                        $changeableCount++;
                    }
                }

                // 查找未在数据库中的卡号
                $foundCards = array_column($previewData, 'card_no');
                $notFoundCards = array_diff($cardNos, $foundCards);

                foreach ($notFoundCards as $notFoundCard) {
                    $previewData[] = [
                        'card_no' => $notFoundCard,
                        'type_name' => '',
                        'status' => '未找到',
                        'can_change' => false
                    ];
                    $failCards[] = $notFoundCard;
                    $nonChangeableCount++;
                }

                // 统计信息
                $stats = [
                    'changeable_count' => $changeableCount,
                    'non_changeable_count' => $nonChangeableCount
                ];

                return json([
                    'code' => 0,
                    'msg' => '获取预览数据成功' . (!empty($failCards) ? '，部分卡不可变更' : ''),
                    'data' => $previewData,
                    'stats' => $stats
                ]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '获取预览数据失败：' . $e->getMessage()]);
            }
        }

        return json(['code' => 1, 'msg' => '非法请求']);
    }

    /**
     * 卡状态变更记录列表
     */
    public function statusChangeBatchRecords()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');

        if (request()->isAjax()) {
            $limit = input('limit', 10, 'intval');
            $page = input('page', 1, 'intval');
            $customerId = input('customer_id', 0, 'intval');
            $status = input('status', '');
            $startTime = input('start_time', '');
            $endTime = input('end_time', '');
            $operatorId = input('operator_id', 0, 'intval');

            $where = ['clientkeynum' => $clientkeynum];

            if (!empty($customerId)) {
                $where['customer_id'] = $customerId;
            }

            if ($status !== '') {
                $where['target_status'] = $status;
            }

            if (!empty($operatorId)) {
                $where['operator_id'] = $operatorId;
            }

            // 时间范围查询
            if (!empty($startTime) && !empty($endTime)) {
                $where['operation_time'] = ['between', [$startTime, $endTime . ' 23:59:59']];
            } else if (!empty($startTime)) {
                $where['operation_time'] = ['>=', $startTime];
            } else if (!empty($endTime)) {
                $where['operation_time'] = ['<=', $endTime . ' 23:59:59'];
            }

            $count = model('CardStatusChangeBatch')
                ->where($where)
                ->count();

            $list = model('CardStatusChangeBatch')
                ->with(['customer'])
                ->where($where)
                ->order('operation_time desc')
                ->page($page, $limit)
                ->select();

            $result = [
                'code' => 0,
                'msg' => '',
                'count' => $count,
                'data' => []
            ];

            if (!empty($list)) {
                foreach ($list as $item) {
                    $result['data'][] = [
                        'id' => $item->id,
                        'batch_no' => $item->batch_no,
                        'customer_name' => $item->customer ? $item->customer->name : '',
                        'target_status' => $item->getAttr('target_status_text'),
                        'card_count' => $item->card_count,
                        'success_count' => $item->success_count,
                        'fail_count' => $item->fail_count,
                        'reason' => $item->reason,
                        'operator_name' => $item->operator_name,
                        'operation_time' => $item->operation_time,
                        'add_time' => $item->add_time
                    ];
                }
            }

            return json($result);
        }

        // 获取客户列表
        $customers = model('CardCustomer')->where([
            'clientkeynum' => $clientkeynum
        ])->select();
        $this->assign('customers', $customers);

        return $this->fetch('status_change_batch_records');
    }

    /**
     * 卡状态变更批次详情
     */
    public function statusChangeBatchDetail($id)
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');

        // 获取批次信息
        $batch = model('CardStatusChangeBatch')->where([
            'clientkeynum' => $clientkeynum,
            'id' => $id
        ])->find();

        if (!$batch) {
            $this->error('批次信息不存在');
        }

        // 获取状态记录
        $records = model('CardStatusRecord')->with(['card'])->where([
            'clientkeynum' => $clientkeynum,
            'batch_id' => $id
        ])->select();

        foreach ($records as $record) {
            $record->old_status_text = $this->getCardStatusText($record->old_status);
            $record->new_status_text = $this->getCardStatusText($record->new_status);
        }

        $this->assign('batch', $batch);
        $this->assign('records', $records);

        return $this->fetch('status_change_batch_detail');
    }

    /**
     * 显示退卡页面
     */
    public function refund()
    {
        // 获取客户列表
        $customers = model('CardCustomer')->field('id,name')->where([
            'clientkeynum' => session('cn_accountinfo.basekeynum')
        ])->select();
        $this->assign('customers', $customers);
        return $this->fetch();
    }

    /**
     * 处理预览文本区域变化的AJAX请求
     */
    public function getCardRefundPreviewData()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 1, 'msg' => '请求方式错误']);
        }

        $cardnum = $this->request->post('cardnum');
        $customer_id = $this->request->post('customer_id');

        if (empty($cardnum) || empty($customer_id)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 处理卡号数据
        $cardNos = array_filter(array_unique(explode("\n", str_replace("\r", "", $cardnum))));
        if (empty($cardNos)) {
            return json(['code' => 1, 'msg' => '未找到有效的卡号']);
        }

        try {
            // 获取卡片预览数据
            $result = model('Card')->getRefundPreviewData($cardNos, $customer_id);
            return json([
                'code' => 0, 
                'msg' => '获取成功', 
                'data' => $result['data'],
                'stats' => $result['stats']
            ]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 处理预览按钮的AJAX请求
     */
    public function getCardRefundPreview()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 1, 'msg' => '请求方式错误']);
        }

        $params = $this->request->post();
        if (empty($params['customer_id'])) {
            return json(['code' => 1, 'msg' => '请选择客户']);
        }

        try {
            // 获取卡片预览数据
            $result = model('Card')->getRefundPreviewByParams($params);
            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $result['data'],
                'plain_card_nos' => $result['plain_card_nos'],
                'stats' => $result['stats']
            ]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 处理退卡提交的AJAX请求
     */
    public function refundCards()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 1, 'msg' => '请求方式错误']);
        }

        $params = $this->request->post();

        // 验证必填参数
        if (empty($params['customer_id'])) {
            return json(['code' => 1, 'msg' => '请选择客户']);
        }
        if (empty($params['card_nums'])) {
            return json(['code' => 1, 'msg' => '请选择要退卡的卡号']);
        }
        // if (empty($params['reason'])) {
        //     return json(['code' => 1, 'msg' => '请输入退卡原因']);
        // }
        // if (empty($params['refund_type'])) {
        //     return json(['code' => 1, 'msg' => '请选择退款方式']);
        // }

        // 处理卡号数据
        $cardNos = array_filter(array_unique(explode(',', $params['card_nums'])));
        if (empty($cardNos)) {
            return json(['code' => 1, 'msg' => '未找到有效的卡号']);
        }

        try {
            // 执行退卡操作
            $result = model('Card')->refundCards($cardNos, $params);
            if ($result === true) {
                return json(['code' => 0, 'msg' => '退卡成功']);
            } else {
                return json(['code' => 1, 'msg' => $result]);
            }
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }
    
    /**
     * 退卡记录列表
     */
    public function refundRecords()
    {
        // GET请求展示页面
        if ($this->request->isGet()) {
            // 获取客户列表
            $customers = model('CardCustomer')->field('id,name')->where([
                'clientkeynum' => session('cn_accountinfo.basekeynum')
            ])->select();
            $this->assign('customers', $customers);
            return $this->fetch();
        }
        
        // AJAX请求获取数据
        $params = $this->request->param();
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 15;
        
        // 构建查询条件
        $where = [];
        
        // 按批次号筛选
        if (!empty($params['batch_no'])) {
            $where[] = ['batch_no', 'like', '%' . $params['batch_no'] . '%'];
        }
        
        // 按客户筛选
        if (!empty($params['customer_id'])) {
            $where[] = ['customer_id', '=', $params['customer_id']];
        }
        
        // 按退卡时间范围筛选
        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $where[] = ['refund_time', 'between', [$params['start_time'] . ' 00:00:00', $params['end_time'] . ' 23:59:59']];
        } else if (!empty($params['start_time'])) {
            $where[] = ['refund_time', '>=', $params['start_time'] . ' 00:00:00'];
        } else if (!empty($params['end_time'])) {
            $where[] = ['refund_time', '<=', $params['end_time'] . ' 23:59:59'];
        }
        
        // 获取数据
        $result = model('CardRefundBatch')->getList($where, 'id desc', $page, $limit);
        
        // 处理数据
        $list = [];
        foreach ($result['list'] as $item) {
            $list[] = [
                'id' => $item['id'],
                'batch_no' => $item['batch_no'],
                'customer_name' => $item['customer']['name'],
                'total_amount' => $item['total_amount'],
                'actual_refund_amount' => $item['actual_refund_amount'],
                'card_count' => $item['card_count'],
                'refund_time' => $item['refund_time'],
                'refund_type' => model('CardRefundBatch')::getRefundTypeText($item['refund_type']),
                'reason' => $item['reason'],
                'operator_name' => $item['operator_name'],
                'status' => model('CardRefundBatch')::getStatusText($item['status']),
                'add_time' => $item['add_time']
            ];
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $result['total'],
            'data' => $list
        ]);
    }
    
    /**
     * 退卡记录详情
     * @param int $id 批次ID
     * @return mixed
     */
    public function refundDetail($id)
    {
        if (empty($id)) {
            $this->error('参数错误');
        }
        
        // 获取批次详情
        $detail = model('CardRefundBatch')->getDetail($id);
        if (empty($detail)) {
            $this->error('未找到对应的退卡批次');
        }
        
        // 处理数据
        $batch = $detail['batch'];
        $batch['customer_name'] = $batch['customer']['name'];
        $batch['refund_type_text'] = model('CardRefundBatch')::getRefundTypeText($batch['refund_type']);
        $batch['status_text'] = model('CardRefundBatch')::getStatusText($batch['status']);
        
        $this->assign('batch', $batch);
        $this->assign('details', $detail['details']);
        
        return $this->fetch();
    }

    /**
     * 根据目标状态获取具体的变更原因
     * @param int $targetStatus 目标状态
     * @return string 变更原因
     */
    private function getStatusChangeReason($targetStatus)
    {
        switch ($targetStatus) {
            case 0:
                return '卡片生成-待销售';
            case 1:
                return '卡片销售';
            case 2:
                return '卡片兑换';
            case 3:
                return '卡片开卡激活';
            case 4:
                return '卡片关闭停用';
            case 5:
                return '卡片作废处理';
            case -1:
                return '卡片过期处理';
            case -2:
                return '卡片退卡处理';
            default:
                return '卡片状态变更';
        }
    }

    /**
     * 卡延期列表页
     */
    public function cardDelay()
    {
        return $this->fetch();
    }

    /**
     * 卡延期列表数据API
     */
    public function ajaxCardDelay()
    {
        $params = request()->param();
        $clientkeynum = session("cn_accountinfo.basekeynum");
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 15;
        $field = isset($params['field']) ? $params['field'] : '';
        $keyword = isset($params['keyword']) ? $params['keyword'] : '';
        $start_time = isset($params['start_time']) ? $params['start_time'] : '';
        $end_time = isset($params['end_time']) ? $params['end_time'] : '';

        $where = [
            'clientkeynum' => $clientkeynum
        ];

        if (!empty($start_time)) {
            $where[] = ['add_time', '>=', $start_time];
        }

        if (!empty($end_time)) {
            $where[] = ['add_time', '<=', $end_time . ' 23:59:59'];
        }

        if (!empty($field) && !empty($keyword)) {
            if ($field == 'customer_id') {
                $customers = model('CardCustomer')->where('name', 'like', "%{$keyword}%")->column('id');
                if (!empty($customers)) {
                    $where[] = ['customer_id', 'in', $customers];
                } else {
                    $where[] = ['customer_id', '=', 0]; // 确保没有结果
                }
            }
        }

        $list = model('CardDelayRecord')->where($where)
            ->order('id', 'desc')
            ->paginate($limit, false, ['page' => $page]);

        $data = [];
        foreach ($list as $item) {
            $cardCount = count(explode(',', $item['cardnum']));
            $customer = model('CardCustomer')->where('id', $item['customer_id'])->find();
            
            $data[] = [
                'id' => $item['id'],
                'no' => $item['no'],
                'operator' => $item['operator'],
                'add_time' => $item['add_time'],
                'customer_id' => $item['customer_id'],
                'customer_name' => $customer ? $customer['name'] : '',
                'card_count' => $cardCount,
                'delay_time' => $item['delay_time'],
                'remarks' => $item['remarks']
            ];
        }

        return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $data]);
    }

    /**
     * 卡延期详情页
     */
    public function cardDelayDetail()
    {
        $no = input('no');
        if (empty($no)) {
            $this->error('参数错误');
        }
        $this->assign('no', $no);
        return $this->fetch();
    }

    /**
     * 卡延期详情数据API
     */
    public function ajaxCardDelayDetail()
    {
        $no = input('no');
        $clientkeynum = session("cn_accountinfo.basekeynum");

        if (empty($no)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        $info = model('CardDelayRecord')->where([
            'clientkeynum' => $clientkeynum,
            'no' => $no
        ])->find();

        if (empty($info)) {
            return json(['code' => 1, 'msg' => '数据不存在或已被删除']);
        }

        $cardNums = explode(',', $info['cardnum']);
        $list = model('Card')->where([
            'clientkeynum' => $clientkeynum,
        ])->whereIn('card_no', $cardNums)->select();

        $data = [];
        foreach ($list as $card) {
            $data[] = [
                'id' => $card['id'],
                'cardnum' => $card['card_no'],
                'delay_time' => $info['delay_time']
            ];
        }

        return json(['code' => 0, 'msg' => 'success', 'count' => count($data), 'data' => $data]);
    }

    /**
     * 添加卡延期页面
     */
    public function addCardDelay()
    {
        $clientkeynum = session("cn_accountinfo.basekeynum");
        $customers = model('CardCustomer')->where('clientkeynum', $clientkeynum)->select();
        $this->assign('customer_list', $customers);
        return $this->fetch();
    }

    /**
     * 获取卡延期预览数据
     */
    public function getCardDelayPreview()
    {
        $clientkeynum = session("cn_accountinfo.basekeynum");
        $customer_id = input('customer_id');
        $sale_number = input('sale_number'); // 销售单号
        $begin_cardnumber = input('begin_cardnumber');
        $end_cardnumber = input('end_cardnumber');

        if (empty($customer_id)) {
            return json(['code' => 1, 'msg' => '请选择客户']);
        }

        // 拼接查询条件
        $where = [
            'clientkeynum' => $clientkeynum,
            'customer_id' => $customer_id,
        ];

        // 优先根据销售单号查询
        if (!empty($sale_number)) {
            $saleBatch = model('CardSaleBatch')->where([
                'clientkeynum' => $clientkeynum,
                'batch_no' => $sale_number
            ])->find();

            if (empty($saleBatch)) {
                return json(['code' => 1, 'msg' => '未查询到销售信息']);
            }

            $cardList = model('Card')->where([
                'clientkeynum' => $clientkeynum,
                'batch_id' => $saleBatch['id']
            ])->select();

            $result = [];
            foreach ($cardList as $card) {
                $result[] = [
                    'id' => $card['id'],
                    'cardnum' => $card['card_no'],
                    'status' => $this->getCardStatusText($card['status']),
                    'end_dui' => $card['valid_end_time']
                ];
            }

            if (!empty($result)) {
                return json(['code' => 0, 'msg' => '生成预览成功', 'data' => $result]);
            } else {
                return json(['code' => 1, 'msg' => '未查询到卡信息！']);
            }
        } 
        // 根据卡号范围查询
        else if (!empty($begin_cardnumber) && !empty($end_cardnumber)) {
            // 分析卡号格式和范围
            if (!preg_match('/^([A-Za-z]+)(\d+)$/', $begin_cardnumber, $startMatches) || 
                !preg_match('/^([A-Za-z]+)(\d+)$/', $end_cardnumber, $endMatches)) {
                return json(['code' => 1, 'msg' => '卡号格式不正确，应为字母开头加数字']);
            }

            $startPrefix = $startMatches[1];
            $endPrefix = $endMatches[1];
            if ($startPrefix !== $endPrefix) {
                return json(['code' => 1, 'msg' => '卡号前缀不一致']);
            }

            $prefix = $startPrefix;
            $startNum = intval($startMatches[2]);
            $endNum = intval($endMatches[2]);
            $numLength = strlen($startMatches[2]);

            if ($startNum > $endNum) {
                return json(['code' => 1, 'msg' => '起始卡号大于结束卡号']);
            }

            if ($endNum - $startNum > 1000) {
                return json(['code' => 1, 'msg' => '卡号范围过大，一次最多支持1000张卡']);
            }

            // 生成卡号列表
            $cardNos = [];
            for ($i = $startNum; $i <= $endNum; $i++) {
                $cardNos[] = $prefix . str_pad($i, $numLength, '0', STR_PAD_LEFT);
            }

            $cardList = model('Card')->where([
                'clientkeynum' => $clientkeynum,
                'customer_id' => $customer_id
            ])->whereIn('card_no', $cardNos)->select();

            $result = [];
            foreach ($cardList as $card) {
                $result[] = [
                    'id' => $card['id'],
                    'cardnum' => $card['card_no'],
                    'status' => $this->getCardStatusText($card['status']),
                    'end_dui' => $card['valid_end_time']
                ];
            }

            if (!empty($result)) {
                return json(['code' => 0, 'msg' => '生成预览成功', 'data' => $result]);
            } else {
                return json(['code' => 1, 'msg' => '未查询到卡信息！']);
            }
        } else {
            return json(['code' => 1, 'msg' => '请输入销售单号或卡号范围']);
        }
    }

    /**
     * 添加卡延期处理
     */
    public function ajaxAddCardDelay()
    {
        $params = request()->param();
        $clientkeynum = session("cn_accountinfo.basekeynum");
        $operator = session('cn_accountinfo.accountname');
        $delay_time = $params['delay_time'];
        $card_nums = $params['card_nums'];
        $remarks = $params['remarks'];
        $customer_id = $params['customer_id'];

        if (empty($delay_time)) {
            return json(['code' => 1, 'msg' => '请选择延期时间']);
        }

        if (empty($card_nums)) {
            return json(['code' => 1, 'msg' => '请选择要延期的卡']);
        }

        // 添加结束时间
        $delay_time .= ' 23:59:59';

        // 查询卡信息
        $cardArray = explode(',', $card_nums);
        $cardList = model('Card')->where([
            'clientkeynum' => $clientkeynum,
            'customer_id' => $customer_id
        ])->whereIn('card_no', $cardArray)->select();

        // 验证卡状态和过期时间
        $fail_list = [];
        $status_fail_list = [];
        foreach ($cardList as $card) {
            if (!empty($card['valid_end_time']) && $card['valid_end_time'] > $delay_time) {
                $fail_list[] = $card['card_no'];
            }

            if (!in_array($card['status'], [Card::STATUS_OPENED, Card::STATUS_EXPIRED])) { // 1-已激活，2-已使用
                $status_fail_list[] = $card['card_no'];
            }
        }

        if (!empty($fail_list)) {
            return json(['code' => 1, 'msg' => '卡号 ' . implode(',', $fail_list) . ' 过期时间大于要延期的时间']);
        }

        if (!empty($status_fail_list)) {
            return json(['code' => 1, 'msg' => '卡号 ' . implode(',', $status_fail_list) . ' 的当前状态不可延期']);
        }

        // 开始事务
        Db::startTrans();
        try {
            // 生成批次号
            $no = 'D' . date('YmdHis') . mt_rand(1000, 9999);
            
            // 添加延期记录
            $delayLog = model('CardDelayRecord');
            $logData = [
                'clientkeynum' => $clientkeynum,
                'no' => $no,
                'cardnum' => $card_nums,
                'operator' => $operator,
                'customer_id' => $customer_id,
                'delay_time' => $delay_time,
                'remarks' => $remarks,
                'add_time' => date('Y-m-d H:i:s'),
            ];
            $delayLog->save($logData);

            // 批量更新卡的有效期
            foreach ($cardList as $card) {
                model('Card')->where('id', $card['id'])->update([
                    'valid_end_time' => $delay_time,
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            }

            Db::commit();
            return json(['code' => 0, 'msg' => '延期成功！']);
        } catch (\Exception $e) {
            Db::rollback();
            Log::error($e->getMessage());
            Log::error('error file:' . $e->getFile() . ' line:' . $e->getLine());
            return json(['code' => -1, 'msg' => '请求失败：' . $e->getMessage()]);
        }
    }

    /**
     * 导出卡延期详情
     */
    public function exportCardDelayDetail()
    {
        $no = input('no');
        $clientkeynum = session("cn_accountinfo.basekeynum");

        if (empty($no)) {
            $this->error('参数错误');
        }

        $info = model('CardDelayRecord')->where([
            'clientkeynum' => $clientkeynum,
            'no' => $no
        ])->find();

        if (empty($info)) {
            $this->error('数据不存在或已被删除');
        }

        $cardNums = explode(',', $info['cardnum']);
        $list = model('Card')->where([
            'clientkeynum' => $clientkeynum,
            'card_no' => ['in', $cardNums]
        ])->select();

        $data = [];
        foreach ($list as $key => $card) {
            $data[] = [
                'index' => $key + 1,
                'card_no' => $card['card_no'],
                'delay_time' => $info['delay_time']
            ];
        }

        $title = "卡延期详情-{$no}";
        $header = ['序号', '卡号', '延期时间'];
        $this->exportExcel($title, $header, $data);
    }

    /**
     * 导出卡延期记录
     */
    public function exportCardDelay()
    {
        $clientkeynum = session("cn_accountinfo.basekeynum");
        $customer = input('customer', '');
        $operator = input('operator', '');
        $start_time = input('start_time', '');
        $end_time = input('end_time', '');

        $where = [
            'clientkeynum' => $clientkeynum
        ];

        if (!empty($start_time)) {
            $where[] = ['add_time', '>=', $start_time];
        }

        if (!empty($end_time)) {
            $where[] = ['add_time', '<=', $end_time . ' 23:59:59'];
        }

        if (!empty($customer)) {
            $customers = model('CardCustomer')->where('name', 'like', "%{$customer}%")->column('id');
            if (!empty($customers)) {
                $where[] = ['customer_id', 'in', $customers];
            } else {
                $where[] = ['customer_id', '=', 0]; // 确保没有结果
            }
        }

        if (!empty($operator)) {
            $where[] = ['operator', 'like', "%{$operator}%"];
        }

        $list = model('CardDelayRecord')->where($where)->order('id', 'desc')->select();

        $data = [];
        foreach ($list as $key => $item) {
            $cardCount = count(explode(',', $item['cardnum']));
            $customer = model('CardCustomer')->where('id', $item['customer_id'])->find();
            
            $data[] = [
                'index' => $key + 1,
                'operator' => $item['operator'],
                'add_time' => $item['add_time'],
                'customer_name' => $customer ? $customer['name'] : '',
                'card_count' => $cardCount,
                'delay_time' => $item['delay_time'],
                'remarks' => $item['remarks']
            ];
        }

        $title = "卡延期记录";
        $header = ['序号', '操作人', '操作时间', '客户', '延期数量', '延期时间', '备注'];
        $this->exportExcel($title, $header, $data);
    }

    /**
     * 卡管理统计页面
     */
    public function statistics()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        if (request()->isAjax()) {
            $dateRange = input('date_range', 'today'); // today, week, month, year, custom
            $startDate = input('start_date', '');
            $endDate = input('end_date', '');
            
            // 根据日期范围设置查询条件
            switch ($dateRange) {
                case 'today':
                    $startDate = date('Y-m-d');
                    $endDate = date('Y-m-d');
                    break;
                case 'week':
                    $startDate = date('Y-m-d', strtotime('-7 days'));
                    $endDate = date('Y-m-d');
                    break;
                case 'month':
                    $startDate = date('Y-m-01');
                    $endDate = date('Y-m-d');
                    break;
                case 'year':
                    $startDate = date('Y-01-01');
                    $endDate = date('Y-m-d');
                    break;
                case 'custom':
                    if (empty($startDate) || empty($endDate)) {
                        return json(['code' => 1, 'msg' => '请选择自定义日期范围']);
                    }
                    break;
            }
            
            // 1. 卡状态统计
            $statusStats = $this->getCardStatusStatistics($clientkeynum);
            
            // 2. 销售统计
            $salesStats = $this->getSalesStatistics($clientkeynum, $startDate, $endDate);
            
            // 3. 卡型统计
            $typeStats = $this->getCardTypeStatistics($clientkeynum);
            
            // 4. 客户统计
            $customerStats = $this->getCustomerStatistics($clientkeynum, $startDate, $endDate);
            
            // 5. 销售员统计
            $salespersonStats = $this->getSalespersonStatistics($clientkeynum, $startDate, $endDate);
            
            // 6. 趋势统计（最近7天）
            $trendStats = $this->getTrendStatistics($clientkeynum);
            
            // 7. 生成批次统计
            $generationStats = $this->getGenerationStatistics($clientkeynum, $startDate, $endDate);
            
            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'status_stats' => $statusStats,
                    'sales_stats' => $salesStats,
                    'type_stats' => $typeStats,
                    'customer_stats' => $customerStats,
                    'salesperson_stats' => $salespersonStats,
                    'trend_stats' => $trendStats,
                    'generation_stats' => $generationStats,
                    'date_range' => [
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'range_type' => $dateRange
                    ]
                ]
            ]);
        }
        
        return $this->fetch();
    }
    
    /**
     * 获取卡状态统计
     */
    private function getCardStatusStatistics($clientkeynum)
    {
        $statusMap = [
            0 => '待销售',
            1 => '已销售', 
            2 => '已兑换',
            3 => '已开卡',
            4 => '已关卡',
            5 => '已废卡',
            -1 => '已过期',
            -2 => '已退卡'
        ];
        
        $stats = [];
        $totalCards = 0;
        
        foreach ($statusMap as $status => $statusText) {
            $count = model('Card')->where([
                'clientkeynum' => $clientkeynum,
                'status' => $status
            ])->count();
            
            $stats[] = [
                'status' => $status,
                'status_text' => $statusText,
                'count' => $count
            ];
            
            $totalCards += $count;
        }
        
        return [
            'total_cards' => $totalCards,
            'status_list' => $stats
        ];
    }
    
    /**
     * 获取销售统计
     */
    private function getSalesStatistics($clientkeynum, $startDate, $endDate)
    {
        $where = [
            'clientkeynum' => $clientkeynum
        ];
        
        if ($startDate && $endDate) {
            $where[] = ['sale_time', 'between', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']];
        }
        
        // 销售批次统计
        $salesBatches = model('CardSaleBatch')->where($where)->select();
        
        $totalSales = 0;
        $totalAmount = 0;
        $totalCards = 0;
        
        foreach ($salesBatches as $batch) {
            $totalSales++;
            $totalAmount += $batch['total_price'];
            $totalCards += $batch['card_count'];
        }
        
        // 今日销售
        $todaySales = model('CardSaleBatch')->where([
            'clientkeynum' => $clientkeynum
        ])->whereTime('sale_time', 'today')->sum('total_price');
        
        $todayCards = model('CardSaleBatch')->where([
            'clientkeynum' => $clientkeynum
        ])->whereTime('sale_time', 'today')->sum('card_count');
        
        // 本月销售
        $monthSales = model('CardSaleBatch')->where([
            'clientkeynum' => $clientkeynum
        ])->whereTime('sale_time', 'month')->sum('total_price');
        
        $monthCards = model('CardSaleBatch')->where([
            'clientkeynum' => $clientkeynum
        ])->whereTime('sale_time', 'month')->sum('card_count');
        
        return [
            'period_sales' => $totalSales,
            'period_amount' => round($totalAmount, 2),
            'period_cards' => $totalCards,
            'today_amount' => round($todaySales, 2),
            'today_cards' => $todayCards,
            'month_amount' => round($monthSales, 2),
            'month_cards' => $monthCards
        ];
    }
    
    /**
     * 获取卡型统计
     */
    private function getCardTypeStatistics($clientkeynum)
    {
        $cardTypes = model('CardPackageType')->where([
            'clientkeynum' => $clientkeynum,
            'status' => 1
        ])->select();
        
        $stats = [];
        foreach ($cardTypes as $type) {
            $cardCount = model('Card')->where([
                'clientkeynum' => $clientkeynum,
                'type_id' => $type['id']
            ])->count();
            
            $salesCount = model('Card')->where([
                'clientkeynum' => $clientkeynum,
                'type_id' => $type['id'],
                'status' => ['in', [1, 2, 3, 4]] // 已销售、已兑换、已开卡、已关卡
            ])->count();
            
            $stats[] = [
                'type_id' => $type['id'],
                'type_name' => $type['name'],
                'price' => $type['price'],
                'total_cards' => $cardCount,
                'sold_cards' => $salesCount,
                'unsold_cards' => $cardCount - $salesCount
            ];
        }
        
        return $stats;
    }
    
    /**
     * 获取客户统计
     */
    private function getCustomerStatistics($clientkeynum, $startDate, $endDate)
    {
        $where = [
            'clientkeynum' => $clientkeynum
        ];
        
        if ($startDate && $endDate) {
            $where[] = ['sale_time', 'between', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']];
        }
        
        // 获取销售数据按客户分组
        $customerSales = Db::table('card_sale_batch')
            ->field('customer_id, COUNT(*) as batch_count, SUM(total_price) as total_amount, SUM(card_count) as total_cards')
            ->where($where)
            ->group('customer_id')
            ->order('total_amount', 'desc')
            ->limit(10)
            ->select();
        
        $stats = [];
        foreach ($customerSales as $sale) {
            $customer = model('CardCustomer')->where('id', $sale['customer_id'])->find();
            $stats[] = [
                'customer_id' => $sale['customer_id'],
                'customer_name' => $customer ? $customer['name'] : '未知客户',
                'batch_count' => $sale['batch_count'],
                'total_amount' => round($sale['total_amount'], 2),
                'total_cards' => $sale['total_cards']
            ];
        }
        
        return $stats;
    }
    
    /**
     * 获取销售员统计
     */
    private function getSalespersonStatistics($clientkeynum, $startDate, $endDate)
    {
        $where = [
            'clientkeynum' => $clientkeynum
        ];
        
        if ($startDate && $endDate) {
            $where[] = ['sale_time', 'between', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']];
        }
        
        // 获取销售数据按销售员分组
        $salespersonSales = Db::table('card_sale_batch')
            ->field('salesperson_id, COUNT(*) as batch_count, SUM(total_price) as total_amount, SUM(card_count) as total_cards')
            ->where($where)
            ->group('salesperson_id')
            ->order('total_amount', 'desc')
            ->limit(10)
            ->select();
        
        $stats = [];
        foreach ($salespersonSales as $sale) {
            $salesperson = model('CardSale')->where('id', $sale['salesperson_id'])->find();
            $stats[] = [
                'salesperson_id' => $sale['salesperson_id'],
                'salesperson_name' => $salesperson ? $salesperson['name'] : '未知销售员',
                'batch_count' => $sale['batch_count'],
                'total_amount' => round($sale['total_amount'], 2),
                'total_cards' => $sale['total_cards']
            ];
        }
        
        return $stats;
    }
    
    /**
     * 获取趋势统计（最近7天）
     */
    private function getTrendStatistics($clientkeynum)
    {
        $stats = [];
        
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            
            // 当日销售金额
            $dailyAmount = model('CardSaleBatch')->where([
                'clientkeynum' => $clientkeynum
            ])->whereTime('sale_time', 'between', [$date . ' 00:00:00', $date . ' 23:59:59'])
            ->sum('total_price');
            
            // 当日销售卡数
            $dailyCards = model('CardSaleBatch')->where([
                'clientkeynum' => $clientkeynum
            ])->whereTime('sale_time', 'between', [$date . ' 00:00:00', $date . ' 23:59:59'])
            ->sum('card_count');
            
            // 当日生成卡数
            $generatedCards = model('Card')->where([
                'clientkeynum' => $clientkeynum
            ])->whereTime('add_time', 'between', [$date . ' 00:00:00', $date . ' 23:59:59'])
            ->count();
            
            $stats[] = [
                'date' => $date,
                'sales_amount' => round($dailyAmount, 2),
                'sales_cards' => $dailyCards,
                'generated_cards' => $generatedCards
            ];
        }
        
        return $stats;
    }
    
    /**
     * 获取生成批次统计
     */
    private function getGenerationStatistics($clientkeynum, $startDate, $endDate)
    {
        $where = [
            'clientkeynum' => $clientkeynum
        ];
        
        if ($startDate && $endDate) {
            $where[] = ['add_time', 'between', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']];
        }
        
        $totalBatches = model('CardGenerationRecord')->where($where)->count();
        $successBatches = model('CardGenerationRecord')->where($where)->where('status', 1)->count();
        $failBatches = model('CardGenerationRecord')->where($where)->where('status', 2)->count();
        
        $totalGenerated = model('CardGenerationRecord')->where($where)->sum('success_count');
        $totalFailed = model('CardGenerationRecord')->where($where)->sum('fail_count');
        
        return [
            'total_batches' => $totalBatches,
            'success_batches' => $successBatches,
            'fail_batches' => $failBatches,
            'total_generated' => $totalGenerated,
            'total_failed' => $totalFailed,
            'success_rate' => $totalBatches > 0 ? round(($successBatches / $totalBatches) * 100, 2) : 0
        ];
    }
}
