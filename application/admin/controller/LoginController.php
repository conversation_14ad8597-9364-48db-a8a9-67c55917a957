<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright (c) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use think\Db;
use think\facade\Request;
use think\facade\Session;

class LoginController extends CnController
{

    //登录页面模板
    public function index()
    {
        //根据域名找到平台客户
        $url = $_SERVER['HTTP_HOST']; //获取当前域名 登录之前根据域名来确定平台客户
        //根据域名取出来的平台客户basekeynum
        $basekeynum = Db::table('plat_account')->where("find_in_set('$url',glurl) and isdel=0")->value('basekeynum');
        //plat_config取出登录页面的新
        $plat_config = Db::table("plat_config")->where("basekeynum='$basekeynum'")->find();
        $theme = isset($plat_config['login_theme']) ? $plat_config['login_theme'] : 1;
        $titlecn = isset($plat_config['titlecn']) ? $plat_config['titlecn'] : "后台管理系统";
        //初始化是否显示验证码
        $attack_ip = $_SERVER['REMOTE_ADDR']; //访问者ip
        $attack_info = Db::table('plat_attack_log')->where("attack_ip", $attack_ip)->find();
        $try_num = $attack_info['try_num'] ?? 0;
        $this->assign("try_num", $try_num);

        $this->assign("titlecn", $titlecn);

        $plat_system_setting = Db::table('plat_system_set')->where("id=1")->find();
        $wechat_login_enabled = $plat_system_setting['wechat_login_enabled'] ?? 0;
        $this->assign("wechat_login_enabled", $wechat_login_enabled);
        $theme = 3;
        return $this->fetch('index' . $theme);
    }



    /**
     * 登录验证的方法
     */
    public function login()
    {
        $url = $_SERVER['HTTP_HOST']; //获取当前域名 登录之前根据域名来确定平台客户
        //根据域名取出来的平台客户basekeynum
        $basekeynum = Db::table('plat_account')->where("find_in_set('$url',glurl) and isdel=0")->value('basekeynum');

        $request = Request::instance();
        $param = $request->param();
        //对用户传入的所有参数转义过滤防止sql注入
        $param = doaddslashes($param);
        $verify = $param['verify'];
        $accountname = isset($param['accountname']) ? trim($param['accountname']) : '';
        $accountpassword = isset($param['accountpassword']) ? trim($param['accountpassword']) : '';
        //登陆失败错误两次以上校验证码 ,基于session的限制只能限制通过页面打开进行尝试的人 or 错误ip，基于ip限制攻击者
        $attack_ip = $_SERVER['REMOTE_ADDR']; //访问者ip
        $plat_attack_log = Db::table('plat_attack_log');
        $attack_info = $plat_attack_log->where("attack_ip='$attack_ip'")->find(); //某个ip登录错误3次出来验证码。

        //基于账号的密码错误三次限制必须验证验证码
        $plat_ainfo =  Db::table("plat_account")->where("accountname='$accountname' ")->find();
        $cn_try_count = $plat_ainfo['cn_try_count'] ?? 0;
        if (($attack_info['try_num'] ?? 0) > 3 || $cn_try_count > 3) { //尝试次数过多显示验证码
            //校验验证码是否正确
            if (!captcha_check($verify)) {
                $rt['sta'] = 0;
                $rt['msg'] = "验证码不正确！";
                $rt['try_count'] = "4";
                $rt['try_num'] = "4";
                echo json_encode($rt);
                die;
            }
        }

        //如果勾选了记住密码
        $remember = isset($param['remember']) ? $param['remember'] : 0;
        if ($remember) {
            cookie('cn_accountname', $accountname, 3600 * 24 * 7); //记住我
            //cookie('cn_accountpassword', $accountpassword, 3600 * 24 * 7);//记住我
        } else {
            cookie('cn_accountname', null);
            //cookie('cn_accountpassword', null);
        }
        if (strlen($accountpassword) == 32) {
        } else {
            $accountpassword = password($accountpassword); //加密密码
        }
        if ($accountname == '') {
            $rt['sta'] = 0;
            $rt['msg'] = "账号不能为空！";
            echo json_encode($rt);
            die;
        } elseif ($accountpassword == '') {
            $rt['sta'] = 0;
            $rt['msg'] = "密码不能为空！";
            echo json_encode($rt);
            die;
        }

        $plat_accountinfo =  Db::table("plat_account")->where("accountname='$accountname' and accountpassword='$accountpassword' and isdel=0")->find();
        if ($plat_accountinfo) {
            //验证该账号密码是否在对应的域名下登录
            //校验域名取出来的basekeynum 和账号密码对应的basekeynum
            /*
            if ($plat_accountinfo['basekeynum']!=$basekeynum) {
                $rt["sta"] = 0;
                $rt["msg"] = "对不起,请在对应域名下登录。";
                echo json_encode($rt);
                die;
            }*/
            //验证码账号密码是否过期
            if ($plat_accountinfo["tablename"] == "plat_client") {
                $keynum = $plat_accountinfo["keynum"];
                $endtime = Db::table("plat_client")->where("keynum='$keynum'")->value("end_time");
                if ($endtime < time()) {
                    $rt["sta"] = 0;
                    $rt["msg"] = "对不起你的账户已到期";
                    echo json_encode($rt);
                    die;
                }
            }
            //登录成功清空失败次数
            $updata['cn_try_count'] = 0;
            Db::table("plat_account")->where("accountname='$accountname' and accountpassword='$accountpassword' ")->update($updata);
            //登录成功清空登录失败ip记录
            $attack_ip = $_SERVER['REMOTE_ADDR']; //访问者ip
            Db::table('plat_attack_log')->where("attack_ip='$attack_ip'")->delete();
            session("cn_login_flag", 'self');
            session("cn_accountinfo", $plat_accountinfo);
            addloginlog("登录成功", $accountname);
            $rt['sta'] = 1;
            $rt['msg'] = "登录成功";
            echo json_encode($rt);
            die;
        } else {
            //登录失败次数增加1 因为账号不能重复了，所有可以根据账号来判断了
            $plat_accountinfo =  Db::table("plat_account")->where("accountname='$accountname' ")->find();
            $updata['cn_try_count'] = $plat_accountinfo['cn_try_count'] + 1;
            Db::table("plat_account")->where("accountname='$accountname' ")->update($updata);

            //登录失败把请求者ip记录到数据库
            $attack_ip = $_SERVER['REMOTE_ADDR']; //访问者ip
            $attack_info = Db::table('plat_attack_log')->where("attack_ip", $attack_ip)->find();
            if ($attack_info['id']) {
                $attack['try_num'] = $attack_info['try_num'] + 1;
                Db::table('plat_attack_log')->where("attack_ip", $attack_ip)->update($attack);
            } else {
                $attack['attack_ip'] = $attack_ip;
                $attack['try_num'] = 1;
                Db::table('plat_attack_log')->insert($attack);
            }


            //同一个ip尝试9次以上，并且发送邮件少于2的时候再次发送邮件提醒开发者
            $attack_info = Db::table('plat_attack_log')->where("attack_ip", $attack_ip)->find();
            if ($attack['try_num'] > 9) {
                if ($attack_info['send_mail_num'] < 2) { //没有发送过邮件的话，发送邮件
                    //同一个ip账号密码错误9次，确认为攻击行为，发送邮件给开发者
                    $html = "登录账号：" . $param['accountname'] . "  登录密码：" . $param['accountpassword'] . "域名:" . $_SERVER['HTTP_HOST'] . "尝试次数:" . $attack_info['try_num'] . "攻击者ip:" . $attack_ip . "其他信息" . json_encode($param, JSON_UNESCAPED_UNICODE);
                    $subject = "高危攻击漏洞";
                    $qq = "<EMAIL>";
                    send_mail($qq, $html, $subject, $param, $basekeynum);
                    //更新邮件发送次数
                    $attack_log['send_mail_num'] = $attack_info['send_mail_num'] + 1;
                    Db::table('plat_attack_log')->where("attack_ip", $attack_ip)->update($attack_log);
                }
            }

            $plat_accountinfo =  Db::table("plat_account")->where("accountname='$accountname' ")->find();
            addloginlog("登录失败", $accountname);
            $rt['sta'] = 0;
            $rt['try_count'] = $plat_accountinfo['cn_try_count'];
            $rt['try_num'] = $attack_info['try_num'];
            $rt['msg'] = "登录失败";
            echo json_encode($rt);
            die;
        }
    }


    /**
     * 自动登录方法
     */
    public function auto_login()
    {

        $url = $_SERVER['HTTP_HOST']; //获取当前域名 登录之前根据域名来确定平台客户
        //根据域名取出来的平台客户basekeynum
        $basekeynum = Db::table('plat_account')->where("find_in_set('$url',glurl) and isdel=0")->value('basekeynum');

        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];

        //验证签名
        $ts = $param['ts'];
        $sign = $param['sign'];
        $flag = check_autologin_sign($keynum, $ts, "", $sign);
        if (!$flag) {
            error_tips("对不起签名错误,登录失败！");
            die;
        }
        //ts和当前时间差不能超过半个小时的业务逻辑
        if (abs($ts - time()) > 3600) {
            error_tips("登录连接已过期！");
            die;
        }

        $plat_accountinfo = Db::table('plat_account')->where("basekeynum='$keynum' and  keynum='$keynum' ")->find();
        if ($plat_accountinfo) {

            session("cn_accountinfo", $plat_accountinfo);
            addloginlog("keynum登录成功" . $keynum, $plat_accountinfo['accountname']);
            $this->redirect(url('Index/index'));
            die;
        } else {
            $attack_ip = $_SERVER['REMOTE_ADDR']; //访问者ip
            //如果出现碰库行为
            $html = "登录账号keynum：" . $keynum . "域名:" . $_SERVER['HTTP_HOST']  . "攻击者ip:" . $attack_ip . "其他信息" . json_encode($param, JSON_UNESCAPED_UNICODE);
            $subject = "自动登录碰库攻击";
            $qq = "<EMAIL>";
            send_mail($qq, $html, $subject, $param, $basekeynum);

            addloginlog("keynum登录失败" . $keynum, "");
            error_tips("对不起自动登录失败，请联系管理员！");
            die;
            die;
        }
    }





    /*
     * 礼赠通平台自动登录
     */
    public function autologin()
    {

        $mi = $_GET["code"]; //传来的密文
        if (empty($mi)) {
            $this->error("参数错误", url("index"), 5);
            die;
        }
        $new = hex2bin($mi); //hex解码
        $private_key = 'OKLU520XJB09906H'; //秘钥
        // 使用 openssl_decrypt 替换已废弃的 mcrypt_decrypt (PHP 7.4 兼容)
        $decrypted = mcrypt_decrypt(MCRYPT_RIJNDAEL_128, $private_key, $new, MCRYPT_MODE_CBC, $private_key);//秘钥解密



        //处理json
        $json_str = str_replace("\u0000", "", json_encode($decrypted));
        $arr = json_decode(json_decode($json_str), true);
        //获取账号密码
        $accountname = $arr[0]["N"] . "_" . $arr[0]["N"];
        $accountname2 = $arr[0]["N"];
        $accountpassword = md5($arr[0]["P"]);
        $g_accountpassword = $accountpassword; //admin_encrypt($arr[0]["P"]);
        //验证登录
        $plat_accountinfo = Db::table("plat_account")
            ->where("(accountname='$accountname' or accountname='$accountname2') and (accountpassword='$accountpassword' or accountpassword='$g_accountpassword')")->find();
        if ($plat_accountinfo) {
            if ($plat_accountinfo["tablename"] == "plat_client") {
                $keynum = $plat_accountinfo["keynum"];
                $endtime = Db::table("plat_client")->where("keynum='$keynum'")->value("end_time");
                if ($endtime < time()) {
                    error_tips("对不起您的账户已到期！");
                    die;
                }
            }
            session("cn_accountinfo", $plat_accountinfo);
            session("cn_login_flag", 'lzt');
            addloginlog("登录成功", $accountname);
            $this->redirect("Index/index");
        } else {
            error_tips("对不起登录失败！");
            die;
        }
    }

    // public function test_sms()
    // {
    //     send_phone_gly("2429C9B29076DEBB2E3A155D13B7D096", "***********", "132456");
    // }
}
