<?php
// +----------------------------------------------------------------------
// | cnadmin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright (c) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;
use think\Db;
use think\Controller;
use think\Session;
use think\facade\Request;
use think\Cache;
use think\facade\Log;

class LztController extends Controller
{

//测试方法
    public function test (){
        $config=config("bind_domain_config");
        print_r($config);
    }

//入口方法
    public function index(){
        //接受参数
        $request = Request::instance();
        $param = $request->param();
        $resign = $param['sign'];
        $recode = $param['code'];
        $remsg = $param['msg'];
        //模拟数据开始
        // 开通
        /*
        $resign="j76jBTRevLJZsLSX68hX3K4UkN2bBLqVIWafYA5RQncoLcCC7w9mJMvefFbaA5epHhdUI354YH558+9SmLqE7rPPuS+Wx7uNg2OogxdXeTI3dPHlloeXpwrgqRbF6SacLaKSnzNrPZs/toTu1USr8kELqS9UTNwHzSKu+OU5Ebc=";
        $recode="uniHv77K52iVcoSb+6xvHmaDRALVaagdIM2Myd0FcVdOSXLbhyEK7LOCQ7BJdlI2syaOwkkqgqlPtfU+fcE6dYDyWa+VyC9kJs9+89UL9enycJX44mF/cxyzYmEfpTsY5if/KCNcI4L/L1WKpic7p/aie2YKfWGs7ZmtrCJcvWk=";
        $remsg="AypWVN1hPENtOyX5iQx/bEs88wnFkIjCGNxgVlKlpBVZ2uFauvrIuinKplDmmxBPvKj/J4MolVX3oW/pMEyc56D0Wlj0YoMAY57wmJNcgoZWPgbSR2/3NFt1WLIRMmWVVEALfe51Qxq4ODkPKMfXTOE6WK6hLg6d+1Ssn2iJzsBkcnvdkzLUCWi352AEzZvYd6gzbCrMbVhJGomUvfv38pbxS69Qe5MeJw/PyoT9rIQTwZAB+yfHwAPls7DteVe0I5PAdihT7ztFPCvKBdhZOQJtxZRgTVMvWYlSAwgVcfL3X6Ivun1MBPQ/AAfN0+BA8MNY//VIpdB6j6XXQE6DDgfQjAu+w6ZlzP2GihTNQiMFZ0ZbOCx4EE25mR83MHC/Su3g7n9p4rNoRpoyMT1Z90IQWr/MR5fBGvhnSE689SuIqWeQONQioXOHiW/FlH7/0Qy42jzEtwWe4frs4ma+sV4cB9T897IR0iJ66GQGyaylywxRnsyU7Fxb87/v5i/a5ejH8RPFPjUZqqxV1Kq6fe+r8Inflrgiwmat0pM1hcq9UhgitptfM3eASmudqQ3bbS5S93twr32q1+Dzp20zSw==";
        */
        /*延期
        $resign="YuGuIEEbEuhzY/1B/k1+CrNpe7Y7occmx2IaYpIjHT2444I+tcS1oXfpb1gjrhKGK2tuAES3QGbmmcjtU6xl3hPcdFwSRAhBJLZiyHzYaiNkHt2GAE51kzL0gW+lkSoXGGjQ3hmCiPsWeP1JP8lVlzN97/ziazGaT1BJHalLKBU=";
        $recode="VJ+7kf7si0aiB9gBPidgwM49+21Dw8EN7RfEJQgcBfw4ZcUx04Ti60Br3p2mj+GP9ksUzo4Eisti5/xkVYSaa9pbdOMgakqUoWVVipz1QGK7EdGh5LHkFwWh5CW7UQvjgWi3wo5Q0DvXFFYm6i0hpJyl5JqDsmSgPXHa8OBVuUw=";
        $remsg="HEqrj/OOrqRDK62tmGQQ+TNxabhWPSVpDheWE94Je9CLbS2EJBfl9PgaFGAYemW7bV6m8VpUMOVx9315XT1NcpWQRVkYk0FVsraey++JHUIDQxiNP9uJeGz1bfCtqIZWe3Gr0E08/EptbcJtjCbOa0LhVixKU2H/3U58f4hDoq0lY61K+AFP5GCvTdrGtmzKVW0D5QLd1OVq/YN+i1ZNIp22qrAgbIC2gQ6OcPh0ZmL4ekZMBEObDzTu0yxZtf18D7vrOXtlZcFGP0bqrfPnf4S/1kgCmRKV7Uf/oE+eEb/yODQmLifTZj93pRV8QyGTC9+fL4c4MfmgZkFTeZSw8Aq8H4n44kyqV0Jq8gFvNcA41B5hQ4WffjhCx2yyu+6SEfXPSJmPy8Qydq2dBKvhe1OE5+OJhmHjN0D/FkTzUOaAAnuZUpqeHZiIZv+Idq4t6Vq/oCvUrDtgJWqfIDIyNSG/QSDB+UTv3SM/a7G+hyDamsefjz4l12+F5C9JKpvdaAmvpso4xJNg2hBUVKRp4rFMEdDzq4aPD+Qz6HML3mw=";
        */

        /*修改
        $resign="gaFFibc5bb7CJhAbZgeSrVtF/EGRnyj+Psl+HFwI7rfOKkADEO2qfBwS/8xQ1Av110NndNTw1GJj81vxd35/lBx7E8OMOkWy8CcQ0yPfpLy9ljDLEl6nAb1s7zKrpttjxxudWAa68YhabnKY6WVpJ+fKboLy5BgkISukJyuWnWM=";
        $recode="zwo8pJZH0OfvxjMEL+OS1nxx1a6FiJ4OFsY8cUjLTt9oWxzpBKk3quTHyByPrZSE2KzAuwrhS3PqabmfS/QDwbbz2pFJidbnkpruAD+Le9lQGJVTu7/13EMpb2H4ZUthR5ozKr+FNbxapmW1gInMytariyr5PGnllzkyKV5dZyw=";
        $remsg="jAUHB6NmANKBnfGdCT4N1czUsgdOchFs3FbbCaptKEHaO+cuiJ1p9fwD12uenW+Tu8n3Y+o8/BRBTtp9dMVOAmmX/utSCQ6gTSO0CvCaQKwbPvg2MkXqvakpMv5U5KBDWIlybBEM+jpJuDACjJZkSahxf3dKUExxVDulodH2dnth/wp+q7cwWS7T7C2zdNykpd2qf/iWJRlom1kCzhdSqn7tueeHpdkv0mNPOWRsbicgs8OSgnUJw6CiMpjrznMI1HwR/4Pd+kSJLCrw13WUyTbDUgH7OVb68QsTKVl5DIvreSmhJemP74jLLBYfg+XHdbWcQSwfiv8YknM4B0LATlP5rZBMvhdyRzz+Kan5EdlCLaUFIybI/TJ66CRZ4f42y64ua9YVj7EHqfjmWD0aAzqHC8bx5T25wRrBuJuFVuiHtMSvNRERpBpQbQjYuE5sOsb70/0fKrgFvzlEs8L+MO8sFHZKrv7JTt2+FiN4mbnuhj4p92nzHTON0r3t28zUEzB12nhNfCrWryb4t77knP4Nklkj9a//c5QRgJ1icM19qyDTD35KVBIamOeKmftx";
        */

        //模拟数据结束

        $file = $_FILES;        //文件不加密

        if (strpos($resign, "%") !== false) {
            $resign = urldecode($resign);
        }
        if (strpos($recode, "%") !== false) {
            $recode = urldecode($recode);
        }
        if (strpos($remsg, "%") !== false) {
            $remsg = urldecode($remsg);
        }
        $plainjson = getparmtoplain($resign, $recode, $remsg); //把接受到的密文转化成明文

        logRes($plainjson,"lzt_new");

        //$plainjson='[{"Action":"OpenCert","CertKeyNum":"2F44EE3E863E4FA2A8AE3F40C8CF29xx","MemberKeyNum":"D13F89EAE5964B059043E529BF8137xx","CompanyName":"北京真诚恒业软件技术有限公司xx","LinkMan":"xx","CellPhone":"18601061584","SysNum":"ZCHYxx","SysPwd":"xxx","SysTitle":"真诚恒业软件技术订单拆分系统xx（平台版）","BegTime":"","EndTime":"2022-05-31 00:00:00","BindURL":"ZCHYxx.v2.lipingu.com","MenuType":"0","IsBatch":"0","Fee":"0.00"}]';


        $plainarr1 = json_decode($plainjson, 1);        //转化成数组进行相关业务逻辑
        $plainarr = $plainarr1[0];
        $this->do_interface($plainarr, $file);
    }

    private function do_interface($plainarr, $file) {
        $action = $plainarr['Action']; //处理业务逻辑的方法名
        //判断当前类里面是否存在该方法
        if (method_exists($this, $action)) {
            $this->$action($plainarr, $file);
        } else {
            $rt['slay'] = "1";
            $rt['msg'] = "没有找到对应方法！";
            $returnjson = json_encode($rt);
            sendtolzt($returnjson);
            die;
        }
    }

    //开通
    private function OpenCert($plainarr,$file){
        $arr = array(
            'slay' => "1",
            'data' => $plainarr,
            'file' => $file,
        );
        $CertKeyNum = $arr['data']['CertKeyNum'];
        if ($CertKeyNum == '') {
            $rt['slay'] = "1";
            $rt['msg'] = "缺少购买标示！";
            $returnjson = json_encode($rt);
            sendtolzt($returnjson);
            die;
        } else if (Db::table("plat_account")->where("certkeynum='$CertKeyNum'")->find()) {
            $rt["slay"] = 1;
            $rt["msg"] = "该购买标示已存在";
            $returnjson = json_encode($rt);
            sendtolzt($returnjson);
            die;
        }
        $accountname=$plainarr["SysNum"]."@".$plainarr["SysNum"];
        if(!empty($accountname)){
            if(Db::table("plat_account")->where("accountname='$accountname'")->find()){
                $rt["slay"]=1;
                $rt["msg"]="该后台管理员账号已存在";
                $returnjson= json_encode($rt);
                sendtolzt($returnjson);
                die;
            }
        }else{
            //自己生成
            $uu=GetRandStr(6, "admin");
            $accountname="gl@".$uu;
        }

        $accountpassword=$plainarr["SysPwd"];
        if(empty($accountpassword)){
            $accountpassword = "111111";
        }

        $glname=$plainarr["SysTitle"];
        if(empty($glname)){
            $glname="后台用户_".$accountname;
        }

        $clientname=$plainarr["CompanyName"];
        if(empty($clientname)){
            $clientname=$glname;
        }

        $linkman=$plainarr["LinkMan"];
        $linktel=$plainarr["CellPhone"];

        $fee=$plainarr["Fee"];
        if(empty($fee)){
            $fee=0;
        }
        $endtime=$plainarr["EndTime"];
        $glurl = $arr['data']['BindURL'];

        if (!empty($glurl)) {
            $glurl = strtolower($glurl);
            //$weburlarr1 = Db::table('plat_account')->where("weburl like '%$glurl%'")->value("weburl");
            $glurlarr1 = Db::table('plat_account')->where("glurl like '%$glurl%'")->value("glurl");
            foreach ($glurlarr1 as $val) {
                $varr = explode(",", $val);
                foreach ($varr as $v) {
                    if ($v == $glurl) {
                        $rt['slay'] = 1;
                        $rt['msg'] = "后台域名" . $v . "已存在";
                        $returnjson = json_encode($rt);
                        sendtolzt($returnjson);
                        die;
                    }
                }
            }
            /*
            foreach ($weburlarr1 as $val) {
                $varr = explode(",", $val);
                foreach ($varr as $v) {
                    if ($v == $glurl) {
                        $rt['slay'] = 1;
                        $rt['msg'] = "后台域名" . $v . "已存在";
                        $returnjson = json_encode($rt);
                        sendtolzt($returnjson);
                        die;
                    }
                }
            }
            */

            $domain_apiinfo=config("bind_domain_config");
            $bind_url = $domain_apiinfo['url']."/api.php?act=add_domain&id=" .$domain_apiinfo['webid'] . "&webname=" .$domain_apiinfo['webname']  . "&domain=" . $glurl;
            $content = json_decode(file_get_contents($bind_url), true);
            /*
            if (!$content["status"]) {
                $rt['slay'] = 1;
                $rt['msg'] = "指定域名已绑定过!";
                $returnjson = json_encode($rt);
                sendtolzt($returnjson);
                die;
            }*/
        } else {
            //  $glurl = $uu . ".quanmindian.com";
            $glurl = $uu . ".v2.lipingu.com";
        }

        $system_name=$plainarr["SysTitle"];
        $companyname=$plainarr["CompanyName"];
        $linkman = $linkman;
        $linktel =$linktel;
        $accountpassword = $accountpassword;
        $endtime=$endtime;
        $remark="礼赠通平台创建的客户";
        //向大客户表添加数据 palt_client表
        $data["system_name"]=$system_name;
        $data['keynum'] = create_guid();
        $data['clientnum'] = $plainarr["SysNum"];
        $data['companyname'] = $clientname;
        $data['linkman'] = $linkman;
        $data['linktel'] = $linktel;
        $data['remark']=$remark;
        $data["end_time"]= strtotime($endtime);
        $data['cre_time'] = time();
        $data["certkeynum"]=$CertKeyNum;
        //目前是电子画册role_keynum
        $data["role_keynum"]="79C32D39CB47675FB8E065CE16CBDA87";


        //向account 表添加数据
        $data1['accountname'] = $accountname;
        $data1['accountpassword'] = password($accountpassword);
        $data1['keynum'] = $data['keynum'];
        $data1['accountrealname'] =$linkman;
        $data1['accountphone'] = $linktel;
        $data1['tablename'] = "plat_client";
        $data1['basekeynum'] = $data['keynum'];
        $data1["parent_keynum"]="平台";
        $data1["parent_basekeynum"]="平台";
        $data1["allpath_keynum"]="平台,".$data["keynum"];
        $data1["allpath_basekeynum"]="平台".",".$data["keynum"];
        $data1["parent_baseid"]=0;
        $data1["glurl"]=$glurl;
        $data1["weburl"]=$weburl;
        $data1["certkeynum"]=$CertKeyNum;
        //平台客户表添加记录
        Db::table('plat_client')->insert($data);
        //account表添加记录
        Db::table('plat_account')->insert($data1);

        //系统基本设置表
        $data3['keynum']=create_guid();
        $data3['titlecn']=$system_name;
        $data3['copyright']=$companyname." 版权所有";
        $data3['basekeynum']=$data["keynum"];
        Db::table("plat_config")->insert($data3);

        //添加组织机构
        $org['basekeynum']=$data['keynum'];
        $org['keynum']=create_guid();
        $org['orgnum']="zb";
        $org['orgname']="总部";
        $org['orgleadername']=$linkman;
        $org['allpathkeynum']=$org['keynum'];
        $org['o']=0;

        $aid=Db::table('plat_org')->insertGetId($org);
        Db::table('plat_org')->where("org_id=$aid")->update(['allpathid'=>$aid]);

        //添加角色
        $role['basekeynum']=$data['keynum'];
        $role['keynum']=create_guid();
        $role['role_num']="gly";
        $role['role_name']="管理员";
        $role['o']=0;

        //找权限
        $purviewlist_id=Db::table('plat_role')->where("keynum","79C32D39CB47675FB8E065CE16CBDA87")->value('purviewlist_id');

        //$purviewlist_id=Db::table('plat_role')->where("keynum","CE1A5D675AFF4C4658B743DBD6C2E926")->value('purviewlist_id');


        $role['purviewlist_id']=$purviewlist_id;
        Db::table("plat_role")->insert($role);



        //添加商品分类和模板
//        $addcat['clientkeynum']=$data['keynum'];
//        $addcat['name']="默认分类";
//        $addcat['add_time']=time();
//        $addcat['o']="0";
//        $addcat['pid']="0";
//        $addcat['is_del']="1";
//        $addcat['type']="商品列表";
//        $cat_id=Db::table("client_good_classifylist")->insertGetId($addcat);
//        $data_picture = [
//            'clientkeynum' =>$data['keynum'],
//            'theme' => "",
//            'show_video' => "",
//            'show_img' => "",
//            'share_title' =>"礼册产品团购特刊",
//            'share_desc' =>"让礼品守护我们美好的生活！",
//            'share_img' => "http://tp5-order.oss-cn-beijing.aliyuncs.com/static/upload/ceshi/images/202111/7F95EB6F7762BA1C3B86E4C7D1CEB6B1.png",
//            'btn_backcolor' => "",
//            'btn_fontcolor' => "",
//            'btn_color' => "",
//            'btn_fontcolor_x' => "",
//            'is_show_video' => "",
//            'is_show_img' =>"0",
//            'is_fx' => "0",
//            'is_theme' => "",
//            'theme_name' => "默认模板",
//            'theme1_top_img' => "http://tp5-order.oss-cn-beijing.aliyuncs.com/static/upload/ceshi/images/202110/51173A7D80B0ACE453540B9A5F4D5519.jpg",
//            'theme1_bg_img' => "http://tp5-order.oss-cn-beijing.aliyuncs.com/static/upload/ceshi/images/202107/0AD8188870B47516868D40F5217B3056.jpg",
//            'addtime' => time()
//        ];
//        $picture_id=Db::table("client_electronic_picture")->insertGetId($data_picture);
//
//        $addpicture_classify['clientkeynum']=$data['keynum'];
//        $addpicture_classify['o']="0";
//        $addpicture_classify['classify_id']=$cat_id;
//        $addpicture_classify['theme_id']=$picture_id;
//        $addpicture_classify['addtime']=time();
//        Db::table("client_electronic_picture_classify")->insertGetId($addpicture_classify);

        $rt["slay"]=0;
        $rt["data"] = $plainarr;
        addoperatelog("礼赠通添加平台客户成功", json_encode($plainarr,JSON_UNESCAPED_UNICODE));
        $returnjson = json_encode($rt);
        sendtolzt($returnjson);
        die;
    }


    //续费
    private function RenewCert($plainarr, $file) {
        $arr = array(
            'slay' => "1",
            'data' => $plainarr,
            'file' => $file,
        );
        $keynum = $arr['data']['CertKeyNum'];
        if ($keynum == '') {
            $rt['slay'] = "1";
            $rt['msg'] = "缺少购买标示！";
            $returnjson = json_encode($rt);
            sendtolzt($returnjson);
            die;
        }
        $where = "CertKeyNum='$keynum'";
        $info =Db::table("plat_client")->where($where)->find();
        $save["end_time"] = strtotime($plainarr["EndTime"]);
        Db::table("plat_client")->where($where)->update($save);

        $log["clientkeynum"] =Db::table("plat_client")->where("CertKeyNum='$keynum'")->value("keynum");
        $log["old_end_time"] = $info["end_time"];
        $log["new_end_time"] = strtotime($plainarr["EndTime"]);
        $log["money"] = $plainarr["Fee"];
        $log["time"] = time();
        $log["remark"] = $plainarr["remark"]."礼赠通平台延期";
        Db::table("plat_client_authorize_log")->insert($log);
        $return_arr["slay"] = 0;
        $return_arr["msg"] = "续费成功";
        $returnjson = json_encode($return_arr);
        addoperatelog("礼赠通添加平台客户成功", json_encode($plainarr,JSON_UNESCAPED_UNICODE));
        sendtolzt($returnjson);
        die;
    }

    //修改信息
    private function EditCert($plainarr, $file) {
        $arr = array(
            'slay' => "1",
            'data' => $plainarr,
            'file' => $file,
        );
        $keynum = $arr['data']['CertKeyNum'];
        if ($keynum == '') {
            $rt['slay'] = "1";
            $rt['msg'] = "缺少购买标示！";
            $returnjson = json_encode($rt);
            sendtolzt($returnjson);
            die;
        }
        $accountname = $plainarr["SysNum"]."@".$plainarr["SysNum"];
        $clientnum =$plainarr["SysNum"];
        $glurl = $plainarr["BindURL"];
        $glurlarr = array($plainarr["BindURL"]);
        $name = Db::table("plat_account")->where("CertKeyNum='$keynum'")->value("accountname");
        if ($name != $accountname &&  Db::table("plat_account")->where("accountname='$accountname'")->find()) {
            $data["slay"] = 1;
            $data["msg"] = "该用户名已存在";
            $returnjson = json_encode($data);
            sendtolzt($returnjson);
            die;
        }
        foreach ($glurlarr as $value) {
            // $weburlarr1 = Db::table('plat_account')->where("CertKeyNum <> '$keynum' and weburl like '%$value%'")->value("weburl");
            $glurlarr1 = Db::table('plat_account')->where("CertKeyNum <> '$keynum' and glurl like '%$value%'")->value("glurl");
            foreach ($glurlarr1 as $val) {
                $varr = explode(",", $val);
                foreach ($varr as $v) {
                    if ($v == $value) {
                        $rt['slay'] = 1;
                        $rt['msg'] = "后台域名" . $v . "已存在";
                        $returnjson = json_encode($rt);
                        sendtolzt($returnjson);
                        die;
                    }
                }
            }
            /*
            foreach ($weburlarr1 as $val) {
                $varr = explode(",", $val);
                foreach ($varr as $v) {
                    if ($v == $value) {
                        $rt['slay'] = 1;
                        $rt['msg'] = "后台域名" . $v . "已存在";
                        $returnjson = json_encode($rt);
                        sendtolzt($returnjson);
                        die;
                    }
                }
            }*/
        }
        //绑定域名
        $domain_apiinfo=config("bind_domain_config");
        $bind_url = $domain_apiinfo['url']."/api.php?act=add_domain&id=" .$domain_apiinfo['webid'] . "&webname=" . $domain_apiinfo['webname']  . "&domain=" . $glurl;
        $content = file_get_contents($bind_url);
        //删除旧域名
        $oldurl =Db::table("plat_account")->where("CertKeyNum='$keynum'")->value("glurl");
        $del_url = $domain_apiinfo['url']."/api.php?act=del_domain&id=" . $domain_apiinfo['webid'] . "&webname=" .$domain_apiinfo['webname'] . "&domain=" . $oldurl . "&port=80";
        file_get_contents($del_url);
        //不等于空则是修改
        $data['clientnum'] = $clientnum;
        $data["system_name"] = $plainarr["SysTitle"];

        //向account 表添加数据
        $data1['accountname'] = $accountname;
        if ($plainarr["SysPwd"]) {
            $data1["accountpassword"] = md5($plainarr["SysPwd"]);
        }
        $data1['tablename'] = "plat_client";
        $data1["glurl"] = $glurl;

        //大客户表添加记录
        Db::table('plat_client')->where("CertKeyNum='$keynum'")->update($data);
        //account表添加记录
        Db::table('plat_account')->where("CertKeyNum ='$keynum'")->update($data1);

        $rt['slay'] = 0;
        $rt['msg'] = '修改成功';
        $returnjson = json_encode($rt);

        $addlog["account"] = $data1;
        $addlog["client"] = $data;
        addoperatelog("礼赠通修改平台客户", json_encode($addlog), $plainarr);
        sendtolzt($returnjson);
    }



    //修改密码,暂时没有用到
    private function CertPwd($plainarr,$file){
        $arr = array(
            'slay' => "1",
            'data' => $plainarr,
            'file' => $file,
        );
        $keynum = $arr['data']['CertKeyNum'];
        if($keynum==''){
            $rt['slay'] = "1";
            $rt['msg'] = "缺少购买标示！";
            $returnjson = json_encode($rt);
            sendtolzt($returnjson);
            die;
        }
        $accountpassword= md5($plainarr["SysPwd"]);
        Db::table("plat_account")->where("CertKeyNum='$keynum'")->save(["accountpassword"=>$accountpassword]);
        $rt["slay"]=0;
        $rt["msg"]="操作成功";
        $returnjson= json_encode($rt);
        sendtolzt($returnjson);
    }


    //更新keynum以及修改密码，暂时没有用到
    private function CertPwd_set($plainarr, $file) {
        $arr = array(
            'slay' => "1",
            'data' => $plainarr,
            'file' => $file,
        );
        $CertKeyNum = $arr['data']['CertKeyNum'];
        if ($CertKeyNum == '') {
            $rt['slay'] = "1";
            $rt['msg'] = "缺少购买标示！";
            $returnjson = json_encode($rt);
            sendtolzt($returnjson);
            die;
        }
        $username = $arr['data']['SysNum']."@".$arr['data']['SysNum'];
        $bindurl = $arr['data']['BindURL'];
        $where = "accountname='$username'";
        $info = Db::table('plat_account')->where($where)->find();
        if ($info) {
            $keynum = $info["keynum"];
            $pwd = $arr['data']['SysPwd'];
            if (!empty($pwd)) {
                $save["accountpassword"] = md5($pwd);
            }
            $save["CertKeyNum"] = $CertKeyNum;
            Db::table("plat_account")->where("keynum='$keynum'")->save($save);
            Db::table("plat_client")->where("keynum='$keynum'")->save(["CertKeyNum" => $CertKeyNum]);
            $rt["slay"] = 0;
            $rt["msg"] = "操作成功";
        } else {
            $rt["slay"] = 1;
            $rt["msg"] = "操作失败";
        }
        sendtolzt($returnjson);
        $returnjson = json_encode($rt);
        die;

    }


    //礼赠通账号密码管理器绑定的时候调用这个接口
    public function AuthCheck($plainarr, $file)
    {
        $arr = array(
            'slay' => "1",
            'data' => $plainarr,
            'file' => $file,
        );
        if ($plainarr == '') {
            $rt['slay'] = "1";
            $rt['rs']["msg"] = "参数接收不正确！";
            $returnjson = json_encode($rt);
            sendtolzt($returnjson);
            die;
        }
        $UserName = $plainarr["UserName"];
        $UserPwd = $plainarr["UserPwd"];
        $UserPwd = password($UserPwd);
        $basekeynum =  Db::table('plat_account')->where("accountname='$UserName' and accountpassword='$UserPwd'")->value("basekeynum");
        if (!$basekeynum) {
            $rt['slay'] = "1";
            $rt['rs']["msg"] = "用户名或密码错误！";
            $returnjson = json_encode($rt);
            sendtolzt($returnjson);
            die;
        }

        $CertKeyNum =  Db::table('plat_account')->where("keynum='$basekeynum'")->value("certkeynum");

        $rt['slay'] = "0";
        $rt['rs']["msg"] = "验证成功！";
        $rt['rs']["CertKeyNum"] = $CertKeyNum;
        if (!$CertKeyNum) {
            $glurl =  Db::table('plat_account')->where("keynum='$basekeynum'")->value("glurl");
            $glurl = end(array_filter(explode(",", $glurl)));
            //如果是localhost或者127.0.0.1这个时候则去掉下面的参数
            if($glurl='127.0.0.1' || $glurl='localhost'){
                //这里为空就获取当前域名吧
                $rt['rs']["url"] = "http://" . $_SERVER['HTTP_HOST'] . "/admin.php/login/autologin";
            }else {
                $rt['rs']["url"] = "http://" . $glurl . "/admin.php/login/autologin";
            }


        }
        $returnjson = json_encode($rt);
        sendtolzt($returnjson);
        die;
    }

    //获取磁盘信息接口
    private function GetDiskInfo($plainarr, $file)
    {
        $arr = array(
            'slay' => "1",
            'data' => $plainarr,
            'file' => $file,
        );

        //popen此函数必须从php禁用函数里面去掉
        $fun_arr = get_loaded_extensions();
        $disfun_str = ini_get('disable_functions');
        $disfun_arr = explode(",", $disfun_str);
        if (in_array("popen", $disfun_arr)) {
            $return_arr['slay'] = '1';
            $return_arr['msg'] = 'popen函数被禁用，请检查对应php' . phpversion() . '版本的php.ini';
            $returnjson = json_encode($return_arr);
            sendtolzt($returnjson);
            die;
        }
        //业务逻辑
        $fp = popen('df -lh | grep -E "^(/)"', "r");
        $arr = array();
        $i = 0;
        //输出文本中所有的行，直到文件结束为止。
        while (!feof($fp)) {
            $arr[$i] = fgets($fp); //fgets()函数从文件指针中读取一行
            $i++;
        }
        fclose($fp);
        $arr = array_filter($arr); //去掉空数组
        $item = array();
        foreach ($arr  as $key => $val) {
            $rs = preg_replace("/\s{2,}/", ' ', $val); //把多个空格换成 ""
            $hd = explode(" ", $rs);
            //$item['zone'] = $hd[0];
            //$item['total_space'] = $hd[1];
            //$item['uesd_space'] = $hd[2];
            //$item['free_space'] = $hd[3];
            //$item['percent'] = $hd[4];
            //$item['mount_point'] = trim($hd[5], "\n");

            $item['DiskNum'] = trim($hd[5], "\n");
            $item['DiskSize'] = bcmul(str_replace("G", '', $hd[1]), 1024, 2);
            $item['UsedSize'] = bcmul(str_replace("G", '', $hd[2]), 1024, 2);

            $arr1[] = $item;
        }

        $return_arr["slay"] = 0;
        $return_arr["msg"] = "获取成功！";
        $return_arr["rs"] = $arr1;
        $returnjson = json_encode($return_arr);
        sendtolzt($returnjson);
        die;
    }


    //获取昨天和今天的订单信息接口
    private function GetCountInfo($plainarr, $file)
    {
        $arr = array(
            'slay' => "1",
            'data' => $plainarr,
            'file' => $file,
        );
        date_default_timezone_set('Asia/Shanghai');
        $midnight =  date("Y-m-d 00:00:00");
        $yesterday = date("Y-m-d 00:00:00",strtotime('-1 day'));

        $where1=" add_time>='$midnight' ";
        $where2=" add_time<='$midnight'  and add_time>='$yesterday' ";

        $C1 = Db::table('order')->where('status', '>',1)->where($where1)->count();
        $C2 = Db::table('order')->where('status', '>',1)->where($where2)->count();
        $arr1=array();

        $arr1["C1"]=(String)$C1;
        $arr1["C2"]=(String)$C2;
        $return_arr["slay"] = 0;
        $return_arr["msg"] = "获取成功！";
        $return_arr["rs"] = $arr1;
        $returnjson = json_encode($return_arr);
        sendtolzt($returnjson);
        die;
    }








}



