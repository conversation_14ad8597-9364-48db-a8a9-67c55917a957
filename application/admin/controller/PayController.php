<?php

namespace App\Admin\Controller;

use App\Common\Controller\AdminController;
use think\Controller;
use think\Db;
use think\facade\Env;
use EasyWeChat\Factory;
use think\facade\Log;

class PayController extends Controller
{
    public function index()
    {
        return $this->fetch();
    }

    // 企业付款到微信
    public function payToWechat()
    {
        $basekeynum = session("cn_accountinfo.basekeynum");
        $openid = input('openid');
        $amount = input('amount');
        $order_no = 'test' .rand(********, ********);

        $data = [
            'openid' => $openid,
            'amount' => $amount,
            'order_no' => $order_no,
        ];

        $pay_config = Db::name('plat_wechat_set')->where('basekeynum', $basekeynum)->find();

        // 商户的 certpath 和 keypath 都是字符串形式  需要有绝对路径 生成文件
        $cert_path = Env::get('APP_PATH') . "cert/" . $basekeynum . ".pem";
        $key_path = Env::get('APP_PATH') . "cert/" . $basekeynum . ".key";
        if (!file_exists($cert_path)) { 
            // 生成文件
            file_put_contents($cert_path, $pay_config['apiclient_cert']);
        }
        if (!file_exists($key_path)) { 
            // 生成文件
            file_put_contents($key_path, $pay_config['apiclient_key']);
        }
        
        $config = [
            // 必要配置
            'app_id'             => $pay_config['appid'],
            'mch_id'             => $pay_config['mchid'],
            'key'                => $pay_config['key'],   // API v2 密钥 (注意: 是v2密钥 是v2密钥 是v2密钥)
        
            // 如需使用敏感接口（如退款、发送红包等）需要配置 API 证书路径(登录商户平台下载 API 证书)
            'cert_path'          => $cert_path, // XXX: 绝对路径！！！！
            'key_path'           => $key_path,      // XXX: 绝对路径！！！！
        
            'notify_url'         => '',     // 你也可以在下单时单独设置来想覆盖它
        ];
        $app = Factory::payment($config);

        $amount = $amount * 100;
        $result = $app->transfer->toBalance([
            'partner_trade_no' => $order_no, // 商户订单号，需保持唯一性(只能是字母或者数字，不能包含有符号)
            'openid' => $openid,
            'check_name' => 'NO_CHECK', // NO_CHECK：不校验真实姓名, FORCE_CHECK：强校验真实姓名
            'amount' => $amount, // 企业付款金额，单位为分
            'desc' => '企业付款', // 企业付款操作说明信息。必填
        ]);

        Log::info(json_encode($result));

        if ($result['return_code'] == 'SUCCESS' && $result['result_code'] == 'SUCCESS') {
            return json(['code' => 1, 'msg' => '付款成功']);
        } else {
            return json(['code' => 0, 'msg' => $result['err_code_des']]);
        }


        
        

    }
}
