<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/9/20 上午9:51
 */

namespace app\admin\controller;

use app\admin\model\PlatAccount;
use app\admin\model\PlatClient;
use app\admin\model\Product;
use app\admin\model\Shop;
use app\api\lib\Response;
use app\api\model\Order;
use app\api\model\ShopConfig;
use app\api\model\ShopProduct;
use app\api\model\ShopProductInventory;
use think\Db;
use think\Request;
use app\common\service\YlyPrintService;

class ShopController extends CnController
{
    /**
     * 初始化方法
     */
    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }



    public function add_shop()
    {
        check_auth(request()->controller() . '/add_shop', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');

        $bianhao = PlatClient::where("keynum= '$basekeynum' ")->value("clientnum");
        $front = $bianhao;
        //   $front = empty($front) == false ? $front . "-C" : 'C';
        $front = $front . "-";

        $this->assign('front', $front);

        return $this->fetch();
    }

    public function ajax_add_shop(Request $request)
    {
        check_auth(request()->controller() . '/add_shop', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');

        $code = $request->param('code');
        $title = $request->param('title');
        $phone = $request->param('phone');
        $address = $request->param('address');
        $lat = $request->param('latitude');
        $lng = $request->param('longitude');
        $background_image = $request->param('background_image');
        $cover = $request->param('cover');

        if (empty($code)) return Response::json(Response::ERROR, '客户编码必填');
        if (empty($title)) return Response::json(Response::ERROR, '门店名称必填');
        if (empty($phone)) return Response::json(Response::ERROR, '联系方式必填');
        if (empty($address)) return Response::json(Response::ERROR, '地址必填');
        if (empty($lat)) return Response::json(Response::ERROR, '经度必填');
        if (empty($lng)) return Response::json(Response::ERROR, '纬度必填');
        if (empty($background_image)) return Response::json(Response::ERROR, '请上传背景图');
        if (empty($cover)) return Response::json(Response::ERROR, '请上传logo');

        $front = PlatClient::where("keynum= '$basekeynum' ")->value("clientnum");
        $front = $front . '-';
        //账号前面拼接上平台客户编号 结束
        $accountname = $front . $code;

        $has = PlatAccount::where(['accountname' => $accountname, 'basekeynum' => $basekeynum])->find();
        if ($has) return Response::json(Response::ERROR, '当前编号已存在');

        try {
            Db::startTrans();
            $adderclientinfo = PlatAccount::where("keynum='$basekeynum'")->find();
            $adder_keynum = session("cn_accountinfo.keynum");
            $adderinfo = PlatAccount::where("keynum='$adder_keynum'")->find();


            // 创建门店
            $shop = [
                'clientkeynum' => $basekeynum,
                'title' => $title,
                'phone' => $phone,
                'address' => $address,
                'lat' => $lat,
                'lng' => $lng,
                'background_image' => $background_image,
                'cover' => $cover,
                'code' => $code,
                'keynum' => create_guid(),
            ];

            Shop::create($shop);
            // 生成随机密码
            $password = rand(100000, 999999);
            // 创建账号
            $account = [
                'keynum' => $shop['keynum'],
                'accountname' => $accountname,
                'accountpassword' => password($password),
                'sitekeynum' => '',
                'siteid' => 6,
                'tablename' => 'shop',
                'basekeynum' => $shop['keynum'],
                'parent_baseid' => $adderclientinfo['account_id'],
                'parent_basekeynum' => $adderclientinfo['keynum'],
                'allpath_basekeynum' => $adderclientinfo['allpath_basekeynum'] . ',' . $shop['keynum'],
                'parent_keynum' => $adderinfo['keynum'],
                'allpath_keynum' => $adderinfo['allpath_keynum'] . "," . $shop['keynum'],
                'cre_time' => time(),
            ];

            PlatAccount::create($account);

            $data3['keynum'] = create_guid();
            $data3['titlecn'] = "连锁店系统客户端";
            $data3['copyright'] = $title . " 客户 版权所有";
            $data3['basekeynum'] = $shop["keynum"];
            Db::table("plat_config")->insert($data3);


            //添加组织机构
            $org['basekeynum'] = $shop['keynum'];
            $org['keynum'] = create_guid();
            $org['orgnum'] = "zb";
            $org['orgname'] = "总部";
            $org['orgleadername'] = $title;
            $org['allpathkeynum'] = $org['keynum'];
            $org['o'] = 0;
            $aid = Db::table('plat_org')->insertGetId($org);
            Db::table('plat_org')->where("org_id=$aid")->update(['allpathid' => $aid]);

            //添加角色
            $role['basekeynum'] = $shop['keynum'];
            $role['keynum'] = create_guid();
            $role['role_num'] = "gly";
            $role['role_name'] = "管理员";
            $role['o'] = 0;
            Db::table("plat_role")->insert($role);

            Db::commit();
            return Response::json(Response::SUCCESS, '请求成功');
        } catch (\Exception $e) {
            Db::rollback();
            return Response::json(Response::ERROR, $e->getMessage());
        }
    }

    public function shop_list()
    {
        check_auth(request()->controller() . '/shop_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');

        return $this->fetch();
    }

    public function ajax_shop_list(Request $request)
    {
        check_auth(request()->controller() . '/shop_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $where[] = ['clientkeynum', '=', $basekeynum];

        $title = $request->param('title');

        if (!empty($title)) $where[] = ['title', 'like', "%$title%"];

        $page = $request->param('page', 1);
        $page_size = $request->param('page_size', 10);
        $client = Db::table('plat_client')->where('keynum', $basekeynum)->value('clientnum');


        $list = Shop::where($where)->page($page, $page_size)->select();
        $count = Shop::where($where)->count();
        if(!empty($list)){
            foreach($list as $key => $value){
                $list[$key]['clientnum'] = $client . '-' . $value['code'];
            }
        }

        success(0, '请求成功', $list, $count);
    }

    public function edit_shop()
    {
        check_auth(request()->controller() . '/shop_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = request()->param('id');
        $info = Shop::where(['id' => $id, 'clientkeynum' => $basekeynum])->find();

        $info['open_time'] = substr($info['open_time'], 0, 5);
        $info['close_time'] = substr($info['close_time'], 0, 5);
        $this->assign('info', $info);
        return $this->fetch();

    }

    public function ajax_edit_shop(Request $request)
    {
        check_auth(request()->controller() . '/shop_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $request->param('id');
        $info = Shop::where(['id' => $id, 'clientkeynum' => $basekeynum])->find();
        if (empty($info)) return Response::json(Response::ERROR, '未查询到门店信息');


        $title = $request->param('title');
        $phone = $request->param('phone');
        $address = $request->param('address');
        $lat = $request->param('latitude');
        $lng = $request->param('longitude');
        $background_image = $request->param('background_image');
        $cover = $request->param('cover');
        $open_time = $request->param('open_time');
        $close_time = $request->param('close_time');

        $info['title'] = $title;
        $info['phone'] = $phone;
        $info['address'] = $address;
        $info['lat'] = $lat;
        $info['lng'] = $lng;
        $info['background_image'] = $background_image;
        $info['cover'] = $cover;
        $info['open_time'] = $open_time ? $open_time . ':00' : $info['open_time'];
        $info['close_time'] = $close_time ? $close_time . ':00' : $info['close_time'];
        if ($info->save()) {
            return Response::json(Response::SUCCESS);
        } else {
            return Response::json(Response::ERROR);
        }

    }

    public function del_shop(Request $request)
    {
        check_auth(request()->controller() . '/shop_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = request()->param('id');
        $info = Shop::where(['id' => $id, 'clientkeynum' => $basekeynum])->find();

        // 查询门店是否有订单

        $has = Order::where(['clientkeynum' => $basekeynum, 'shop_id' => $id])->find();

        if ($has) return Response::json(Response::ERROR, '当前门店已有订单，不可删除');

        try {
            Db::startTrans();
            ShopConfig::where(['keynum' => $info['keynum']])->delete();
            Db::table('plat_role')->where(['basekeynum' => $info['keynum']])->delete();
            Db::table('plat_org')->where(['basekeynum' => $info['keynum']])->delete();
            Db::table('plat_config')->where(['basekeynum' => $info['keynum']])->delete();
            PlatAccount::where(['keynum' => $info['keynum']])->delete();
            ShopProduct::where(['shop_id' => $id, 'clientkeynum' => $basekeynum])->delete();
            ShopProductInventory::where(['shop_id' => $id])->delete();
            $info->delete();

            Db::commit();
            return Response::json(Response::SUCCESS, '删除成功');
        } catch (\Exception $e) {
            Db::rollback();
            return Response::json(Response::ERROR, $e->getMessage());
        }
    }

    public function shop_product_list(Request $request)
    {
        check_auth(request()->controller() . '/shop_list', 0);
        $id = $request->param('id');
        $this->assign('id', $id);
        return $this->fetch();
    }

    public function ajax_shop_product_list(Request $request)
    {
        check_auth(request()->controller() . '/shop_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $request->param('id');

        $page = $request->param('page', 1);
        $page_size = $request->param('page_size', 10);

        $shop = Shop::where(['id' => $id, 'clientkeynum' => $basekeynum])->find();
        if (empty($shop)) return Response::json(Response::ERROR, '未查询到门店信息');

        // 查询门店的商品

        $list = ShopProduct::where(['shop_id' => $id, 'clientkeynum' => $basekeynum])->with(['product'])->page($page, $page_size)->select();
        $count = ShopProduct::where(['shop_id' => $id, 'clientkeynum' => $basekeynum])->count();


        success(0, '', $list, $count);
        return Response::json(Response::SUCCESS, '', [
            'list' => $list,
            'count' => $count
        ]);
    }

    public function ajax_add_shop_product_list(Request $request)
    {
        check_auth(request()->controller() . '/shop_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = request()->param('id');
        $page = $request->param('page', 1);
        $page_size = $request->param('page_size', 10);
        $info = Shop::where(['id' => $id, 'clientkeynum' => $basekeynum])->find();
        if (empty($info)) fail(-1, '查询门店信息失败');

        // 查询门店的所有商品
        $product_ids = ShopProduct::where(['shop_id' => $id, 'clientkeynum' => $basekeynum])->column('product_id');

        $list = Product::where(['clientkeynum' => $basekeynum])
            ->whereNotIn('id', $product_ids)
            ->page($page, $page_size)
            ->order('id', 'desc')
            ->select();
        $count = Product::where(['clientkeynum' => $basekeynum])->whereNotIn('id', $product_ids)->count();

        success(0, '', $list, $count);
    }

    public function add_shop_product(Request $request)
    {
        check_auth(request()->controller() . '/shop_list', 0);
        $id = $request->param('id');
        $this->assign('id', $id);
        return $this->fetch();
    }

    public function ajax_add_shop_product(Request $request){
        check_auth(request()->controller() . '/shop_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $request->param('id'); // 门店id
        $ids = $request->param('ids'); // 商品id

        $shop = Shop::where(['id' => $id, 'clientkeynum' => $basekeynum])->find();
        if (empty($shop)) fail(-1, '未查询到门店信息');

        $ids = explode(',', $ids);
        $ids = array_filter($ids);

        if (empty($ids)) fail(-1, '请选择商品！');

        // 查询门店里没有的商品，并添加
        $shop_product_ids = ShopProduct::where(['shop_id' => $id, 'clientkeynum' => $basekeynum])
            ->whereIn('product_id', $ids)
            ->column('product_id');
        // 去除数据库里面已经有的数据
        $ids = array_diff($ids, $shop_product_ids);

        $insert = [];

        foreach ($ids as $product_id) {
            if (Product::where(['id' => $product_id, 'clientkeynum' => $basekeynum])->find()) {
                // 查询到  放进添加数据
                $insert[] = [
                    'product_id' => $product_id,
                    'clientkeynum' => $basekeynum,
                    'shop_id' => $id,
                    'add_time' => date('Y-m-d H:i:s', time()),
                    'status' => 1
                ];
            }
        }

        try {
            $model = new ShopProduct();
            $model->saveAll($insert);
            success(0, '成功');
        } catch (\Exception $e) {
            fail(-1, $e->getMessage());
        }





    }

    public function del_shop_product(Request $request)
    {
        check_auth(request()->controller() . '/shop_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $product_id = $request->param('product_id');
        $shop_id = $request->param('shop_id');

        try {
            Db::startTrans();
            ShopProduct::where(['shop_id' => $shop_id, 'clientkeynum' => $basekeynum, 'product_id' => $product_id])->delete();
            ShopProductInventory::where(['shop_id' => $shop_id, 'clientkeynum' => $basekeynum, 'product_id' => $product_id])->delete();
            Db::commit();
            success();
        } catch (\Exception $e) {
            Db::rollback();
            fail(-1, $e->getMessage());
        }

    }

    /**
     * 门店打印配置页面
     */
    public function shop_printer_config()
    {
        check_auth(request()->controller() . '/shop_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $shop_id = request()->param('shop_id');
        
        // 验证门店是否属于当前平台
        $shop = Shop::where(['id' => $shop_id, 'clientkeynum' => $basekeynum])->find();
        if (empty($shop)) {
            $this->error('未查询到门店信息');
        }
        
        // 获取门店现有的打印机配置
        $printerConfig = Db::table('yly_shop_printer_config')
            ->where(['shop_id' => $shop_id, 'clientkeynum' => $basekeynum])
            ->find();
        
        $this->assign([
            'shop' => $shop,
            'shop_id' => $shop_id,
            'config' => $printerConfig
        ]);
        
        return $this->fetch('shop_printer_config');
    }

    /**
     * 保存门店打印配置
     */
    public function ajax_save_shop_printer_config(Request $request)
    {
        check_auth(request()->controller() . '/shop_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        $shop_id = $request->param('shop_id');
        $printer_sn = trim($request->param('printer_sn'));
        $printer_key = trim($request->param('printer_key'));
        $printer_name = trim($request->param('printer_name', ''));
        $print_copies = intval($request->param('print_copies', 1));
        $auto_print = intval($request->param('auto_print', 1));
        $status = intval($request->param('status', 1));
        
        // 验证门店是否属于当前平台
        $shop = Shop::where(['id' => $shop_id, 'clientkeynum' => $basekeynum])->find();
        if (empty($shop)) {
            return Response::json(Response::ERROR, '未查询到门店信息');
        }
        
        // 验证必填字段
        if (empty($printer_sn)) {
            return Response::json(Response::ERROR, '打印机序列号不能为空');
        }
        
        if (empty($printer_key)) {
            return Response::json(Response::ERROR, '打印机密钥不能为空');
        }
        
        if ($print_copies < 1 || $print_copies > 5) {
            return Response::json(Response::ERROR, '打印份数必须在1-5之间');
        }
        
        try {
            // 构建保存数据
            $data = [
                'clientkeynum' => $basekeynum,
                'shop_id' => $shop_id,
                'printer_sn' => $printer_sn,
                'printer_key' => $printer_key,
                'printer_name' => $printer_name,
                'print_copies' => $print_copies,
                'auto_print' => $auto_print,
                'status' => $status,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // 检查是否已存在配置
            $existConfig = Db::table('yly_shop_printer_config')
                ->where(['shop_id' => $shop_id, 'clientkeynum' => $basekeynum])
                ->find();
            
            if ($existConfig) {
                // 更新现有配置
                $result = Db::table('yly_shop_printer_config')
                    ->where('id', $existConfig['id'])
                    ->update($data);
            } else {
                // 创建新配置
                $data['created_at'] = date('Y-m-d H:i:s');
                $result = Db::table('yly_shop_printer_config')->insert($data);
            }
            
            if ($result !== false) {
                return Response::json(Response::SUCCESS, '保存成功');
            } else {
                return Response::json(Response::ERROR, '保存失败');
            }
            
        } catch (\Exception $e) {
            return Response::json(Response::ERROR, '保存失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试门店打印机
     */
    public function test_shop_printer(Request $request)
    {
        check_auth(request()->controller() . '/shop_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        $shop_id = $request->param('shop_id');
        $printer_sn = trim($request->param('printer_sn'));
        $printer_key = trim($request->param('printer_key'));
        
        // 验证门店是否属于当前平台
        $shop = Shop::where(['id' => $shop_id, 'clientkeynum' => $basekeynum])->find();
        if (empty($shop)) {
            return Response::json(Response::ERROR, '未查询到门店信息');
        }
        
        // 验证必填字段
        if (empty($printer_sn)) {
            return Response::json(Response::ERROR, '打印机序列号不能为空');
        }
        
        if (empty($printer_key)) {
            return Response::json(Response::ERROR, '打印机密钥不能为空');
        }
        
        try {
            // 这里可以集成易联云打印服务进行测试
            // 暂时模拟测试成功
            $testContent = "=== 门店打印测试 ===\n";
            $testContent .= "门店名称：{$shop['title']}\n";
            $testContent .= "门店地址：{$shop['address']}\n";
            $testContent .= "测试时间：" . date('Y-m-d H:i:s') . "\n";
            $testContent .= "打印机SN：{$printer_sn}\n";
            $testContent .= "===================\n";
            $testContent .= "如您看到此信息，\n";
            $testContent .= "说明打印机配置正常！\n";
            $testContent .= "===================\n";
            
            // 实际应用中，这里应该调用易联云打印服务
            // 例如：
            $printService = new YlyPrintService($basekeynum);
            $result = $printService->testPrint($shop_id, $printer_sn, $printer_key, $testContent);
            
            // 暂时模拟成功响应
            $success = true;
            $message = '测试打印指令已发送，请检查打印机是否正常出纸';
            
            if ($success) {
                // 记录测试打印日志
                $logData = [
                    'clientkeynum' => $basekeynum,
                    'shop_id' => $shop_id,
                    'order_no' => 'TEST_' . date('YmdHis') . '_' . rand(1000, 9999),
                    'printer_sn' => $printer_sn,
                    'print_content' => $testContent,
                    'print_status' => 2, // 测试成功
                    'print_type' => 'test',
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                // 检查是否存在打印日志表
                $tableExists = Db::query("SHOW TABLES LIKE 'yly_print_log'");
                if (!empty($tableExists)) {
                    Db::table('yly_print_log')->insert($logData);
                }
                
                return Response::json(Response::SUCCESS, $message);
            } else {
                return Response::json(Response::ERROR, '测试打印失败');
            }
            
        } catch (\Exception $e) {
            return Response::json(Response::ERROR, '测试打印异常: ' . $e->getMessage());
        }
    }

}


























