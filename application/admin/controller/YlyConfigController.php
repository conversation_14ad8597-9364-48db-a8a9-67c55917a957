<?php

namespace app\admin\controller;

use app\admin\model\YlyPlatformConfig;
use app\common\service\YlyPrintService;
use think\Request;
use think\Controller;

/**
 * 易联云平台配置控制器
 * Class YlyConfigController
 * @package app\admin\controller
 */
class YlyConfigController extends Controller
{
    /**
     * 配置首页
     */
    public function index()
    {
        $clientkeynum = session('cn_accountinfo.clientkeynum');
        
        // 获取当前配置
        $config = YlyPlatformConfig::getByClientkey($clientkeynum);
        
        // 获取统计信息
        $statistics = [];
        if ($config) {
            $statistics = YlyPlatformConfig::getStatistics($clientkeynum);
        }
        
        $this->assign([
            'config' => $config,
            'statistics' => $statistics
        ]);
        
        return $this->fetch();
    }
    
    /**
     * 保存配置
     */
    public function save()
    {
        if (request()->isPost()) {
            $params = request()->param();
            $clientkeynum = session('cn_accountinfo.clientkeynum');
            
            // 验证必填参数
            if (empty($params['app_id']) || empty($params['app_secret'])) {
                return json(['code' => 0, 'msg' => '应用ID和密钥不能为空']);
            }
            
            $data = [
                'clientkeynum' => $clientkeynum,
                'app_id' => trim($params['app_id']),
                'app_secret' => trim($params['app_secret']),
                'status' => isset($params['status']) ? intval($params['status']) : 1
            ];
            
            try {
                // 检查是否已存在配置
                $exists = YlyPlatformConfig::getByClientkey($clientkeynum);
                
                if ($exists) {
                    // 更新配置
                    $result = YlyPlatformConfig::where('id', $exists['id'])->update($data);
                } else {
                    // 新增配置
                    $result = YlyPlatformConfig::create($data);
                }
                
                if ($result) {
                    return json(['code' => 1, 'msg' => '保存成功']);
                } else {
                    return json(['code' => 0, 'msg' => '保存失败']);
                }
                
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '保存失败: ' . $e->getMessage()]);
            }
        }
        
        return json(['code' => 0, 'msg' => '请求方式错误']);
    }
    
    /**
     * 测试连接
     */
    public function testConnection()
    {
        if (request()->isPost()) {
            $clientkeynum = session('cn_accountinfo.clientkeynum');
            
            try {
                $printService = new YlyPrintService($clientkeynum);
                
                return json([
                    'code' => 1,
                    'msg' => '连接测试成功',
                    'data' => [
                        'status' => 'success',
                        'message' => '易联云API连接正常'
                    ]
                ]);
                
            } catch (\Exception $e) {
                return json([
                    'code' => 0,
                    'msg' => '连接测试失败: ' . $e->getMessage()
                ]);
            }
        }
        
        return json(['code' => 0, 'msg' => '请求方式错误']);
    }
    
    /**
     * 刷新Token
     */
    public function refreshToken()
    {
        if (request()->isPost()) {
            $clientkeynum = session('cn_accountinfo.clientkeynum');
            
            try {
                $config = YlyPlatformConfig::getByClientkey($clientkeynum);
                if (!$config) {
                    return json(['code' => 0, 'msg' => '请先配置易联云应用信息']);
                }
                
                // 检查今日获取次数限制
                if (YlyPlatformConfig::isTokenLimitExceeded($config)) {
                    return json(['code' => 0, 'msg' => '今日Token获取次数已达上限(20次)']);
                }
                
                $printService = new YlyPrintService($clientkeynum);
                
                return json([
                    'code' => 1,
                    'msg' => 'Token刷新成功'
                ]);
                
            } catch (\Exception $e) {
                return json([
                    'code' => 0,
                    'msg' => 'Token刷新失败: ' . $e->getMessage()
                ]);
            }
        }
        
        return json(['code' => 0, 'msg' => '请求方式错误']);
    }
    
    /**
     * 获取统计信息
     */
    public function getStatistics()
    {
        $clientkeynum = session('cn_accountinfo.clientkeynum');
        $statistics = YlyPlatformConfig::getStatistics($clientkeynum);
        
        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => $statistics
        ]);
    }
} 