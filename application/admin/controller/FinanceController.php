<?php
/**
 * 财务管理
 * @date 2023-4-25
 * <AUTHOR>
 */

namespace app\admin\controller;

use app\admin\model\CardCustomer;
use app\admin\model\ClientMember;
use app\admin\model\ClientOrderInfo;
use app\admin\model\ClientYcardPucardModel;
use app\admin\model\FinanceDetailModel;
use app\admin\model\FinanceModel;
use app\admin\model\PaymentRecord;
use think\Db;
use think\Exception;
use think\exception\DbException;
use think\facade\Log;
use think\facade\Request;

class FinanceController extends CnController
{
    /**
     * 初始化方法
     */
    public function initialize()
    {
        $this->init(); //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    public function payment_record()
    {
        check_auth(request()->controller() . '/payment_record', 0);
        return $this->fetch();
    }

    public function ajax_payment_record()
    {
        check_auth(request()->controller() . '/payment_record', 1);
        $start_time = Request::instance()->param('start_time');
        $end_time = Request::instance()->param('end_time');
        $is_pay = Request::instance()->param('is_pay');
        $field = Request::instance()->param('field');
        $keyword = Request::instance()->param('keyword');
        $page = Request::instance()->param('page', 1);
        $page_size = Request::instance()->param('page_size', 10);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $where = [
            'basekeynum' => $basekeynum
        ];
        if (!empty($start_time)) {
            $start_time = strtotime($start_time , ' 00:00:00');
            $where[' pay_time'] = ['<', $start_time];
        }

        if (!empty($end_time)) {
            $end_time = strtotime($end_time . ' 23:59:59');
            $where['pay_time'] = ['<', $end_time];
        }

        if (!empty($is_pay) || in_array($is_pay, [1, 2])) {
            $arr = [
                1 => 0,
                2 => 1
            ];
            $where['pay_status'] = ['=', $arr[$is_pay]];
        }

        if (!empty($field) && !empty($keyword)) {
            $where[$field] = ['like', "%$keyword%"];
        }

        $list = PaymentRecord::getList($where, $page, $page_size);

        foreach ($list as $v) {
            $v['member_name'] = ClientMember::where(['id' => $v['member_id']])->value('name');
        }

        $count = PaymentRecord::getCount($where);

        $append['pay_total_money'] = PaymentRecord::where($where)->sum('pay_money');

        success(0, '请求成功', $list, $count, $append);

    }

    public function get_list_by_puno()
    {
        check_auth(request()->controller() . '/finance_list', 0);
        $no = Request::instance()->param('no', '');
        $this->assign('no', $no);
        return $this->fetch();
    }

    /**
     * 通过销售单号获取该销售单的所有结算详情
     * @return void
     * @throws DbException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function ajax_get_list_by_puno()
    {
        check_auth(request()->controller() . '/finance_list', 1);
        $params = Request::instance()->param();
        $no = $params['no'] ?? '';
        if (empty($no)) fail(-1, '参数错误');
        $basekeynum = session("cn_accountinfo.basekeynum");
        $where = ['basekeynum' => $basekeynum];
        $data = FinanceDetailModel::getListByPuNo($no, $where);

        success(0, '请求成功', $data);
    }

    /**
     * 预览订单详情金额
     * @return void
     * @throws DbException
     */
    public function preview_price()
    {
        check_auth(request()->controller() . '/finance_list', 1);
        $params = Request::instance()->param();

        $status = $params['status'];
        $no = $params['no'];
        $basekeynum = session("cn_accountinfo.basekeynum");

        if (empty($status)) fail(-1, '状态不可为空');
        if (empty($no)) fail(-1, '单号为空');

        $where = [
            'type' => $status,
            'clientkeynum' => $basekeynum
        ];

        $info = ClientYcardPucardModel::getInfoByPiciNumber($no, $where);

        success(0, '请求成功',$info);

    }

    /**
     * 新增财务详情api
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function ajax_add_finance_detail()
    {
        check_auth(request()->controller() . '/finance_list', 1);
        $params = Request::instance()->param();

        if (empty($params['finance_id'])) fail(-1, '参数错误');
        if (empty($params['total_price'])) fail(-1, '请输入结算总金额');
        if ($params['total_price'] < 0) fail(-1, '结算总金额不可为负数');
        if (!preg_match('/^(0|[1-9]\d{0,10})(\.\d{1,2})?$/',$params['total_price'])) fail(-1, '结算总金额格式不正确');
        if (empty($params['data'])) fail(-1, '未选择需要结算的数据');

        $basekeynum = session("cn_accountinfo.basekeynum");
        foreach ($params['data'] as $v) {
            $pu_card_info = ClientYcardPucardModel::getInfoByPiciNumber($v['no'], ['clientkeynum' => $basekeynum]);
            if (empty($pu_card_info)) fail(-1, $v['no'] . '此销售单不存在');
            if ($pu_card_info['uncleared_money'] < $v['money']) fail(-1, $v['no'] . '此销售单的结算金额大于可结算金额，请重新输入');
        }

        $all_money = array_sum(array_column($params['data'],'money'));

        if ($all_money > $params['total_price']) fail(-1, '结算总金额小于销售单的所有金额总和');

        $operator   = session("cn_accountinfo.accountname");
        $where = [
            'basekeynum' => $basekeynum
        ];

        $finance_info = FinanceModel::getInfoById($params['finance_id'], $where);

        if (empty($finance_info)) fail(-1, '主表信息查询失败');

        if ($finance_info['balance'] < $params['total_price']) fail(-1, '该客户余额不足！');

        try {
            Db::startTrans();

            foreach ($params['data'] as $v) {
                $insert = [
                    'basekeynum' => $basekeynum,
                    'finance_id' => $params['finance_id'],
                    'finance_no' => $finance_info['no'],
                    'no' => $v['no'],
                    'operator' => $operator,
                    'money' => $v['money'],
                    'add_time' => date('Y-m-d H:i:s'),
                    'remarks' => $params['remarks'],
                    'status' => $finance_info->getData()['status'],
                    'customer_id' => $finance_info->getData()['customer_id'],
                ];
                FinanceDetailModel::add($insert);
                ClientYcardPucardModel::where(['pici_number' => $v['no']])->update(['uncleared_money' => Db::raw('uncleared_money-' . $v['money'])]);
            }

            $finance_info->balance -= $params['total_price'];
            $finance_info->income  += $params['total_price'];
            if ($finance_info->balance < 0) {
                throw new \Exception('该客户余额不足');
            }
            $res = $finance_info->save();
            if (!$res) throw new \Exception('更新主表失败');

            Db::commit();
            success();
        }catch (\Exception $e) {
            Db::rollback();
            Log::error('fail info :' . $e->getMessage());
            fail();
        }

    }

//    /**
//     * 新增财务详情view
//     * @return mixed
//     */
//    public function add_finance_detail()
//    {
//        check_auth(request()->controller() . '/finance_list', 0);
//        $id = Request::instance()->param('id');
//        $this->assign('finance_id', $id);
//        return $this->fetch();
//    }

    /**
     * 财务记录详情api
     * @return void
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function ajax_finance_detail_list()
    {
        check_auth(request()->controller() . '/finance_list', 1);
        $finance_id     = Request::instance()->param('id',null);
        $page           = Request::instance()->param('page', 1);
        $page_size      = Request::instance()->param('page_size', 10);
        $basekeynum     = session("cn_accountinfo.basekeynum");
        if (empty($finance_id)) fail(-1, '参数错误！');
        $where = [
            'basekeynum' => $basekeynum,
            'finance_id' => $finance_id,
        ];

        $list = FinanceDetailModel::getList($where, $page, $page_size);
        $count = FinanceDetailModel::getCount($where);

        success(0, '请求成功', $list, $count);

    }

    // 财务记录详请view
    public function finance_detail_list()
    {
        check_auth(request()->controller() . '/finance_list', 0);
        $id = Request::instance()->param('id');
        $basekeynum = session('cn_accountinfo.basekeynum');
        if (empty($id)) fail();
        $where = ['basekeynum' => $basekeynum];
        $info = FinanceModel::getInfoById($id, $where);
        if (empty($info)) fail();
        // 查询客户的未结清销售单
        $where = [
            'clientkeynum' => $basekeynum,
            'type' => $info->getData()['status'],
            'uncleared_money' => ['>', 0]
        ];
        $uncleared_list = ClientYcardPucardModel::getAllByCustomer($info->getData()['customer_id'], $where);
        foreach ($uncleared_list as $k => $v) {
            $uncleared_list[$k]['money'] = $v['uncleared_money'];
        }
        $this->assign('uncleared_list', json_encode($uncleared_list));
        $this->assign('info', $info);
        $this->assign('id', $id);
        return $this->fetch();
    }

    // 添加财务记录view
    public function add_finance_record()
    {
        check_auth(request()->controller() . '/add_finance_record', 0);
        $customer_list = CardCustomer::getEnumByBaseKeyNum();
        $this->assign('customer_list', $customer_list);
        return $this->fetch();
    }

    // 添加财务记录api
    public function ajax_add_finance_record()
    {
        check_auth(request()->controller() . '/add_finance_record', 1);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $request = Request::instance();
        $param = $request->param();
        if (!isset($param['customer_id']) || empty($param['customer_id'])) {
            echo json_encode(['code' => 0,'msg' => '请选择客户！']);die;
        }
        if (!isset($param['money']) || empty($param['money'])) {

            echo json_encode(['code' => 0,'msg' => '请输入金额！']);die;
        }
        if (!preg_match('/^(0|[1-9]\d{0,10})(\.\d{1,2})?$/',$param['money'])) {
            echo json_encode(['code' => 1,'msg' => '金额格式不正确！']);die;
        }


        $data = [
            'customer_id'   => $param['customer_id'],
            'operator'      => session('cn_accountinfo.accountname'),
            'basekeynum'    => $basekeynum,
            'money'         => $param['money'],
            'status'        => $param['status'],
            'balance'       => $param['money'],
            'remarks'       => $param['remarks'],
            'add_time'      => date('Y-m-d H:i:s')
        ];

        if (FinanceModel::add($data)) {
            echo json_encode([
                'code' => 0,
                'msg'  => '添加成功！'
            ]);
        } else {
            echo json_encode([
                'code' => 1,
                'msg' => '操作失败！'
            ]);
        }
    }

    // 财务列表view
    public function finance_list()
    {
        check_auth(request()->controller() . '/finance_list', 0);
        $customer_list = CardCustomer::getEnumByBaseKeyNum();
        $this->assign('customer_list', $customer_list);
        return $this->fetch();
    }

    // 财务列表api
    public function ajax_finance_list()
    {
        check_auth(request()->controller() . '/finance_list', 1);
        $basekeynum             = session("cn_accountinfo.basekeynum");
        $request                = Request::instance();
        $param                  = $request->param();
        $p                      = $param['page'] ? $param['page'] : 1;
        $pagesize               = $param['limit'];
        $offset                 = $pagesize * ($p - 1); //计算记录偏移量
        $where['basekeynum']    = ['=', $basekeynum];

        if ($param['customer_id'] != 0)     $where['customer_id'] = ['=', $param['customer_id']];
        if ($param['status'] != 0)          $where['status'] = ['=', $param['status']];
        if ($param['start_time'] != null)   $where[' add_time'] = ['>', $param['start_time']];
        if ($param['end_time'] != null)     $where['add_time'] = ['<', $param['end_time']];

        $result = FinanceModel::getList($where, $offset, $pagesize);
        $count  = FinanceModel::getCount($where);
        $data = [
            'data' => $result,
            'count' => $count,
            'code' => 0,
            'msg' => '请求成功'
        ];
        echo json_encode($data);die;
    }

    // 导出财务记录api
    public function export_finance_list()
    {
        $basekeynum = session("cn_accountinfo.basekeynum");
        $param = Request::instance()->param();
        $where['basekeynum'] = ['=', $basekeynum];
        if ($param['customer_id'] != 0)     $where['customer_id']   = ['=', $param['customer_id']];
        if ($param['status'] != 0)          $where['status']        = ['=', $param['status']];
        if ($param['start_time'] != null)   $where[' add_time']      = ['>', $param['start_time']];
        if ($param['end_time'] != null)     $where['add_time']      = ['<', $param['end_time']];

        $count = FinanceModel::getCount($where);
        if ($count > 10000) {
            error_tips("单次最大可导出1万条数据,请缩小搜索范围");
            die;
        }
        $export[] = array(
            '序号', '操作人', '客户', '金额', '操作时间', '备注'
        );
        $finance_list = FinanceModel::getExportAll($where);
        if (empty($finance_list)) fail(-1, '未查询到需要导出的数据');

        $result = [];
        foreach ($finance_list as $k => $v) {
            $result[1] = $v["id"];
            $result[2] = $v["operator"];
            $result[3] = $v["customer_id"];
            $result[4] = $v["money"];
            $result[5] = $v["add_time"];
            $result[6] = $v["remarks"];
            $export[] = $result;
        }
        \think\Loader::import('PHPExcel.PHPExcel');
        $objPHPExcel = new \PHPExcel();
        $export = array_filter($export);
        $xls_name = "财务记录".date('Y-m-d');
        create_xls($export, $xls_name);
    }


}
