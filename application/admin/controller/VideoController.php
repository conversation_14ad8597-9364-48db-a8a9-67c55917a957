<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright (c) 2023 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
namespace app\admin\controller;

use app\admin\tool\VideoApi;
use think\Db;
use think\Request;

class VideoController extends CnController
{
    /**
     * 视频管理首页
     */
    public function index()
    {
        $this->assign('upload_url', VideoApi::instance()->getUploadPageUrl());
        return $this->fetch();
    }
    
    /**
     * 获取视频列表
     */
    public function getVideoList(Request $request)
    {
        $videoName = $request->param('videoName', '');
        $playUrl = $request->param('playUrl', '');
        
        $result = VideoApi::instance()->getVideoList($videoName, $playUrl);
        
        if (isset($result['code']) && $result['code'] == 200) {
            success(0, '获取成功', $result['rows'], $result['total']);
        } else {
            success(1, '获取失败', [], 0);
        }
    }
    
    /**
     * 获取视频存储和流量信息
     */
    public function getVideoStorage()
    {
        $result = VideoApi::instance()->getClientVideoStorage();
        
        if (isset($result['code']) && $result['code'] == '200') {
            success(0, '获取成功', $result['data'], 0);
        } else {
            success(1, '获取失败', [], 0);
        }
    }
    
    /**
     * 修改视频名称
     */
    public function changeVideoName(Request $request)
    {
        $videoVid = $request->param('videoVid');
        $videoName = $request->param('videoName');
        
        if (empty($videoVid) || empty($videoName)) {
            success(1, '参数错误', [], 0);
        }
        
        $result = VideoApi::instance()->changeVideoName($videoVid, $videoName);
        
        if (isset($result['code']) && $result['code'] == '200') {
            success(0, '修改成功', [], 0);
        } else {
            success(1, '修改失败', [], 0);
        }
    }
    
    /**
     * 删除视频
     */
    public function deleteVideo(Request $request)
    {
        $videoVid = $request->param('videoVid');
        
        if (empty($videoVid)) {
            success(1, '参数错误', [], 0);
        }
        
        $result = VideoApi::instance()->deleteVideo($videoVid);
        
        if (isset($result['code']) && $result['code'] == '200') {
            success(0, '删除成功', [], 0);
        } else {
            success(1, '删除失败', [], 0);
        }
    }
} 