<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright ( c ) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use app\admin\model\ClientMember;
use app\admin\model\Order;
use app\admin\model\PlatClient;
use app\api\model\CardLevel;
use think\Db;
use think\Request;

class MemberController extends CnController
{

    /**
     * 初始化方法
     */

    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    public function set_user_level_view(Request $request)
    {
        check_auth(request()->controller() . '/member_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $user_id = $request->param('user_id');

        $user = ClientMember::where(['clientkeynum' => $basekeynum, 'id' => $user_id])->find();
        if (empty($user)) error_tips('未查询到会员信息');

        $this->assign('user', $user);
        $this->assign('level_list', CardLevel::where(['clientkeynum' => $basekeynum])->select());

        return $this->fetch();

    }

    public function set_user_level(Request $request)
    {
        check_auth(request()->controller() . '/member_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');

        $user_id = $request->param('user_id');
        $level_id = $request->param('level_id');

        if (empty($user_id) || empty($level_id)) fail(-1, '参数错误');

        $user = ClientMember::where(['clientkeynum' => $basekeynum, 'id' => $user_id])->find();
        if (empty($user)) fail(-1, '未查询到会员信息');
        $level = CardLevel::where(['clientkeynum' => $basekeynum, 'id' => $level_id])->find();
        if (empty($level)) fail(-1, '未查询到等级信息');
        if (ClientMember::where(['clientkeynum' => $basekeynum, 'id' => $user_id])->update(['level' => $level_id])) {
            success(0, '成功');
        } else {
            fail();
        }
    }


    public function ajax_add_user_level(Request $request)
    {
        check_auth(request()->controller() . '/user_level_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $title = $request->param('title');
        $discount = $request->param('discount'); // 折扣
        $sort = $request->param('sort'); // 折扣

        if (empty($title)) fail(-1, '标题必填');
        if (empty($discount) && ($discount > 0 && $discount <= 100)) fail(-1, '折扣必填并且必须在1和100之间');

        $id = $request->param('id');
        if (empty($id)) {
            $model = new CardLevel();
        } else {
            $model = CardLevel::where(['clientkeynum' => $basekeynum])->get($id);
        }

        $model->title = $title;
        $model->discount = $discount;
        $model->sort = $sort;
        $model->clientkeynum = $basekeynum;
        if ($model->save()) {
            success(0, '成功');
        } else {
            fail();
        }


    }

    public function add_user_level(Request $request)
    {
        check_auth(request()->controller() . '/user_level_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $request->param('id');
        $this->assign('id', $id);
        if (!empty($id)) {
            $user_level = CardLevel::where(['id' => $id, 'clientkeynum' => $basekeynum])->find();
            $this->assign('info', $user_level);
        }

        return $this->fetch();
    }

    public function user_level_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/user_level_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
//        $plat_client_info = PlatClient::getInfoByBaseKeyNum($basekeynum);
//        $this->assign('plat_client_info', $plat_client_info);


        return $this->fetch();
    }

    public function ajax_user_level_list(Request $request)
    {
        check_auth(request()->controller() . '/user_level_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $page = $request->param('page', 1);
        $page_size = $request->param('page_size', 10);

        $list = CardLevel::where(['clientkeynum' => $basekeynum])
            ->page($page, $page_size)
            ->order('sort', 'desc')
            ->select();
        $count = CardLevel::where(['clientkeynum' => $basekeynum])->count();

        success(0, '请求成功', $list, $count);
    }

    public function del_user_level(Request $request)
    {
        check_auth(request()->controller() . '/user_level_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $request->param('id');
        if (empty($id)) fail(-1, '参数错误');

        // 查询用户是否有当前等级
        $has = ClientMember::where(['clientkeynum' => $basekeynum, 'level' => $id])->find();
        if ($has) fail(-1, '当前有用户为当前等级！');

        if (CardLevel::where(['id' => $id, 'clientkeynum' => $basekeynum])->delete()) {
            success();
        } else {
            fail();
        }

    }

    public function  member_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/member_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $plat_client_info = PlatClient::getInfoByBaseKeyNum($basekeynum);
        $this->assign('plat_client_info', $plat_client_info);


        return $this->fetch('member_list');
    }

    /**
     * 获取会员支付统计数据
     * 返回在线支付总额、储值卡支付总额和用户总支付金额
     */
    public function ajax_get_member_payment_stats(Request $request)
    {
        //权限校验
        check_auth(request()->controller() . '/member_list', 1);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        try {
            // 获取筛选条件，与会员列表保持一致
            $param = $request->param();
            $field = isset($param['field']) ? $param['field'] : '';
            $keyword = isset($param['keyword']) ? $param['keyword'] : '';
            
            // 构建会员查询条件
            $where = "1=1 and clientkeynum='$basekeynum' and phone is not null";
            if ($keyword) {
                $where .= " and $field like '%$keyword%'";
            }
            
            // 获取符合条件的会员ID列表
            $member_ids = Db::table('client_member')->where($where)->column('id');
            
            if (!empty($member_ids)) {
                // 获取在线支付总额
                $online_payment_total = (float)Order::where(['clientkeynum' => $basekeynum])
                    ->where('status', '>', 0)
                    ->whereIn('user_id', $member_ids)
                    ->sum('real_price');
                    
                // 获取储值卡支付总额
                $card_payment_total = (float)Order::where(['clientkeynum' => $basekeynum])
                    ->where('status', '>', 0)
                    ->whereIn('user_id', $member_ids)
                    ->sum('card_price');
            } else {
                $online_payment_total = 0;
                $card_payment_total = 0;
            }
            
            // 计算总支付金额（在线支付 + 储值卡支付）
            $total_payment = $online_payment_total + $card_payment_total;
            
            // 格式化金额，保留两位小数
            $result = [
                'online_payment_total' => number_format($online_payment_total, 2, '.', ''),
                'card_payment_total' => number_format($card_payment_total, 2, '.', ''),
                'total_payment' => number_format($total_payment, 2, '.', '')
            ];
            
            success(0, '获取统计数据成功', $result);
        } catch (\Exception $e) {
            fail(-1, '获取统计数据失败：' . $e->getMessage());
        }
    }

    public function ajax_get_member_list(Request $request)
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/member_list', 1);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $param = $request->param();
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        //计算记录偏移量
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $sort_field = isset($param['sort_field']) ? $param['sort_field'] : '';
        $sort_order = isset($param['sort_order']) ? $param['sort_order'] : 'desc';
        $where = "1=1  and  clientkeynum='$basekeynum' and phone is not null";
        $where_num = 0;
        if ($keyword) {
            $where .= " and $field like '%$keyword%'";
            $where_num++;
        }
        
        // 判断是否需要特殊排序（针对计算字段）
        $need_special_sort = $sort_field && in_array($sort_field, ['payed_money', 'card_money']);
        
        if ($need_special_sort) {
            // 如果需要按金额排序，先获取所有符合条件的会员数据
            $all_members = Db::table('client_member')->where($where)->select();
            
            // 计算每个会员的金额数据
            foreach ($all_members as $key => &$value) {
                $value['card_money'] = Order::where(['clientkeynum' => $basekeynum, 'user_id' => $value['id']])->where('status', '>', 0)->sum('card_price');
                $value['payed_money'] = Order::where(['clientkeynum' => $basekeynum, 'user_id' => $value['id']])->where('status', '>', 0)->sum('real_price');
            }
            
            // 对所有数据进行排序
            usort($all_members, function($a, $b) use ($sort_field, $sort_order) {
                if ($sort_order === 'asc') {
                    return $a[$sort_field] - $b[$sort_field];
                } else {
                    return $b[$sort_field] - $a[$sort_field];
                }
            });
            
            // 计算总数
            $count = count($all_members);
            
            // 手动分页
            $list = array_slice($all_members, $offset, $pagesize);
        } else {
            // 常规查询方式
            $list = Db::table('client_member')->where($where)->order('id', 'desc')->limit($offset . ',' . $pagesize)->select();
            
            // 计算每个会员的金额数据
            foreach ($list as $key => &$value) {
                $value['card_money'] = Order::where(['clientkeynum' => $basekeynum, 'user_id' => $value['id']])->where('status', '>', 0)->sum('card_price');
                $value['payed_money'] = Order::where(['clientkeynum' => $basekeynum, 'user_id' => $value['id']])->where('status', '>', 0)->sum('real_price');
            }
            
            $count = Db::table('client_member')->where($where)->count();
        }

        $level_enum = CardLevel::where(['clientkeynum' => $basekeynum])->column('title', 'id');
        foreach ($list as $key => &$value) {
            $value['level_title'] = $level_enum[$value['level']] ?? '普通会员';
//            $value["add_time"] = date("Y-m-d H:i:s", $value["add_time"]);
            // 可以获取订单数，订单金额
        }


        $rtdata['data'] = $list;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        echo json_encode($rtdata);
        die;
    }
}
