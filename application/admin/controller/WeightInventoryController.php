<?php
namespace app\admin\controller;

use app\admin\model\Product;
use app\store\model\ShopProductInventory;
use app\store\model\WeightProductSales;
use think\Request;
use think\Db;

/**
 * 计量商品重量库存管理控制器
 * Class WeightInventoryController
 * @package app\admin\controller
 */
class WeightInventoryController extends CnController
{
    /**
     * 初始化方法
     */
    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    /**
     * 计量商品库存列表页面
     */
    public function index()
    {
        //权限校验
        check_auth(request()->controller() . '/index', 0);
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        // 获取门店列表
        $shop_list = Db::name('shop')
            ->where(['clientkeynum' => $basekeynum])
            ->field('id, title')
            ->select();
        
        $this->assign('shop_list', $shop_list);
        return $this->fetch();
    }

    /**
     * 获取计量商品库存列表 (AJAX接口)
     */
    public function ajax_weight_stock_list()
    {
        return $this->index();
    }

    /**
     * 获取计量商品库存列表
     */
    public function getWeightStockList()
    {
        //权限校验
        check_auth(request()->controller() . '/index', 1);
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        $page = request()->param('page', 1);
        $page_size = request()->param('limit', 10);
        $shop_id = request()->param('shop_id', 0);
        $keyword = request()->param('keyword', '');
        $stock_status = request()->param('stock_status', ''); // 1-有库存，2-无库存

        // 构建查询条件
        $where = [
            ['p.clientkeynum', '=', $basekeynum],
            ['p.product_type', '=', Product::PRODUCT_TYPE_WEIGHT] // 只查询计量商品
        ];

        if (!empty($shop_id)) {
            $where[] = ['spi.shop_id', '=', $shop_id];
        }

        if (!empty($keyword)) {
            $where[] = ['p.title', 'like', "%{$keyword}%"];
        }

        if ($stock_status == 1) {
            $where[] = ['spi.weight_stock', '>', 0];
        } elseif ($stock_status == 2) {
            $where[] = ['spi.weight_stock', '<=', 0];
        }

        // 查询数据
        $query = Db::name('products')->alias('p')
            ->join('shop_product_inventory spi', 'p.id = spi.product_id')
            ->join('product_inventory pi', 'pi.id = spi.inventory_id', 'LEFT')
            ->join('shop s', 's.id = spi.shop_id', 'LEFT')
            ->where($where)
            ->field('p.id as product_id, p.title as product_name, p.cover as product_image, 
                     spi.id as inventory_record_id, spi.inventory_id, spi.weight_stock, spi.stock_unit,
                     pi.title as inventory_name, s.title as shop_name, spi.shop_id')
            ->order('spi.id', 'desc');

        $count = $query->count();
        $list = $query->page($page, $page_size)->select();

        // 处理数据
        foreach ($list as &$item) {
            // 如果没有规格名称，显示默认规格
            if (empty($item['inventory_name'])) {
                $item['inventory_name'] = '默认规格';
            }
            
            // 格式化库存显示
            $item['stock_display'] = $item['weight_stock'] . 'kg';
            
            // 库存状态
            $item['stock_status'] = $item['weight_stock'] > 0 ? '有库存' : '无库存';
            $item['stock_status_class'] = $item['weight_stock'] > 0 ? 'layui-badge layui-bg-green' : 'layui-badge layui-bg-gray';
        }

        success(0, '请求成功', $list, $count);
    }

    /**
     * 重量库存入库页面
     */
    public function weight_inbound()
    {
        //权限校验
        check_auth(request()->controller() . '/weight_inbound', 0);
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        // 获取门店列表
        $shop_list = Db::name('shop')
            ->where(['clientkeynum' => $basekeynum])
            ->field('id, title')
            ->select();
        
        $this->assign('shop_list', $shop_list);
        return $this->fetch();
    }

    /**
     * 重量入库接口 (view页面调用)
     */
    public function weightInbound()
    {
        return $this->ajax_weight_inbound();
    }

    /**
     * 处理重量库存入库
     */
    public function ajax_weight_inbound()
    {
        //权限校验
        check_auth(request()->controller() . '/weight_inbound', 1);
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        $shop_id = request()->param('shop_id');
        $inventory_id = request()->param('inventory_id');
        $weight = request()->param('weight');
        $remark = request()->param('remark', '后台手动入库');

        // 参数验证
        if (empty($shop_id) || empty($inventory_id) || empty($weight)) {
            fail(-1, '参数不能为空');
        }

        if ($weight <= 0) {
            fail(-1, '入库重量必须大于0');
        }

        try {
            // 查找库存记录
            $shop_inventory = ShopProductInventory::where([
                'shop_id' => $shop_id,
                'inventory_id' => $inventory_id,
                'clientkeynum' => $basekeynum
            ])->find();

            if (empty($shop_inventory)) {
                fail(-1, '库存记录不存在');
            }

            // 验证是否为计量商品
            $product = Product::where(['id' => $shop_inventory['product_id'], 'clientkeynum' => $basekeynum])->find();
            if (empty($product) || $product['product_type'] != Product::PRODUCT_TYPE_WEIGHT) {
                fail(-1, '该商品不是计量商品');
            }

            // 执行入库
            $shop_inventory->addWeightStock($weight, $remark);

            success(0, '入库成功');
        } catch (\Exception $e) {
            fail(-1, $e->getMessage());
        }
    }

    /**
     * 获取门店的计量商品列表
     */
    public function ajax_shop_weight_products()
    {
        //权限校验
        check_auth(request()->controller() . '/weight_inbound', 1);
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        $shop_id = request()->param('shop_id');

        if (empty($shop_id)) {
            fail(-1, '门店ID不能为空');
        }

        // 查询门店的计量商品
        $products = Db::name('products')->alias('p')
            ->join('shop_product_inventory spi', 'p.id = spi.product_id')
            ->join('product_inventory pi', 'pi.id = spi.inventory_id', 'LEFT')
            ->where([
                ['p.clientkeynum', '=', $basekeynum],
                ['p.product_type', '=', Product::PRODUCT_TYPE_WEIGHT],
                ['spi.shop_id', '=', $shop_id]
            ])
            ->field('p.id as product_id, p.title as product_name, 
                     spi.inventory_id, pi.title as inventory_name, 
                     spi.weight_stock, spi.id as inventory_record_id')
            ->select();

        // 处理数据
        foreach ($products as &$item) {
            if (empty($item['inventory_name'])) {
                $item['inventory_name'] = '默认规格';
            }
            $item['display_name'] = $item['product_name'] . ' - ' . $item['inventory_name'];
            $item['current_stock'] = $item['weight_stock'] . 'kg';
        }

        success(0, '请求成功', $products);
    }

    /**
     * 库存变动日志接口 (view页面调用)
     */
    public function stockLogs()
    {
        return $this->ajax_weight_stock_logs();
    }

    /**
     * 重量库存变动日志
     */
    public function weight_stock_logs()
    {
        //权限校验
        check_auth(request()->controller() . '/weight_stock_logs', 0);
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        // 获取门店列表
        $shop_list = Db::name('shop')
            ->where(['clientkeynum' => $basekeynum])
            ->field('id, title')
            ->select();
        
        $this->assign('shop_list', $shop_list);
        return $this->fetch();
    }

    /**
     * 获取重量库存变动日志列表
     */
    public function ajax_weight_stock_logs()
    {
        //权限校验
        check_auth(request()->controller() . '/weight_stock_logs', 1);
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        $page = request()->param('page', 1);
        $page_size = request()->param('limit', 10);
        $shop_id = request()->param('shop_id', 0);
        $product_id = request()->param('product_id', 0);
        $start_date = request()->param('start_date', '');
        $end_date = request()->param('end_date', '');

        // 构建查询条件
        $where = [
            ['log.clientkeynum', '=', $basekeynum],
            ['log.change_type', 'in', [2, 3]] // 2-重量变动，3-两者都变动
        ];

        if (!empty($shop_id)) {
            $where[] = ['log.shop_id', '=', $shop_id];
        }

        if (!empty($product_id)) {
            $where[] = ['p.id', '=', $product_id];
        }

        if (!empty($start_date)) {
            $where[] = ['log.create_time', '>=', strtotime($start_date)];
        }

        if (!empty($end_date)) {
            $where[] = ['log.create_time', '<=', strtotime($end_date . ' 23:59:59')];
        }

        // 查询数据
        $query = Db::name('shop_product_stock_log')->alias('log')
            ->join('products p', 'p.id = log.product_id', 'LEFT')
            ->join('product_inventory pi', 'pi.id = log.inventory_id', 'LEFT')
            ->join('shop s', 's.id = log.shop_id', 'LEFT')
            ->where($where)
            ->field('log.*, p.title as product_name, pi.title as inventory_name, s.title as shop_name')
            ->order('log.id', 'desc');

        $count = $query->count();
        $list = $query->page($page, $page_size)->select();

        // 处理数据
        foreach ($list as &$item) {
            if (empty($item['inventory_name'])) {
                $item['inventory_name'] = '默认规格';
            }
            
            // 格式化时间
            $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
            
            // 格式化变动类型
            $change_types = [
                1 => '数量变动',
                2 => '重量变动',
                3 => '数量和重量变动'
            ];
            $item['change_type_text'] = $change_types[$item['change_type']] ?? '未知类型';
            
            // 格式化重量变动
            if (!empty($item['weight_change'])) {
                $item['weight_change_display'] = ($item['weight_change'] > 0 ? '+' : '') . $item['weight_change'] . 'kg';
                $item['weight_stock_display'] = $item['old_weight_stock'] . 'kg → ' . $item['new_weight_stock'] . 'kg';
            }
        }

        success(0, '请求成功', $list, $count);
    }

    /**
     * 销售统计接口 (view页面调用)
     */
    public function salesStats()
    {
        return $this->ajax_sales_stats();
    }

    /**
     * 销售统计概览数据
     */
    public function salesStatsOverview()
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        // 今日统计
        $today = date('Y-m-d');
        $today_stats = Db::name('weight_product_sales')
            ->where('clientkeynum', $basekeynum)
            ->where('sale_time', '>=', $today)
            ->where('sale_time', '<', date('Y-m-d', strtotime('+1 day')))
            ->field('SUM(weight) as today_weight, SUM(total_price) as today_amount')
            ->find();
        
        // 本月统计
        $month_start = date('Y-m-01');
        $month_stats = Db::name('weight_product_sales')
            ->where('clientkeynum', $basekeynum)
            ->where('sale_time', '>=', $month_start)
            ->field('SUM(weight) as month_weight, SUM(total_price) as month_amount')
            ->find();
        
        $data = [
            'today_weight' => $today_stats['today_weight'] ?? 0,
            'today_amount' => $today_stats['today_amount'] ?? 0,
            'month_weight' => $month_stats['month_weight'] ?? 0,
            'month_amount' => $month_stats['month_amount'] ?? 0
        ];
        
        success(0, '请求成功', $data);
    }

    /**
     * 销售记录接口
     */
    public function salesRecords()
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $page = request()->param('page', 1);
        $page_size = request()->param('limit', 10);
        $product_id = request()->param('product_id', 0);
        
        $where = [['ws.clientkeynum', '=', $basekeynum]];
        
        if (!empty($product_id)) {
            $where[] = ['ws.product_id', '=', $product_id];
        }
        
        // 查询销售记录
        $query = Db::name('weight_product_sales')->alias('ws')
            ->join('products p', 'p.id = ws.product_id')
            ->join('product_inventory pi', 'pi.id = ws.inventory_id', 'LEFT')
            ->join('shop s', 's.id = ws.shop_id', 'LEFT')
            ->join('order o', 'o.order_no = ws.order_no', 'LEFT')
            ->join('client_member cm', 'cm.id = o.user_id', 'LEFT')
            ->where($where)
            ->field('ws.*, p.title as product_name, pi.title as inventory_name, 
                     s.title as shop_name, ws.order_no, cm.name as customer_name')
            ->order('ws.id', 'desc');
        
        $count = $query->count();
        $list = $query->page($page, $page_size)->select();
        
        // 处理数据
        foreach ($list as &$item) {
            if (empty($item['inventory_name'])) {
                $item['inventory_name'] = '默认规格';
            }
            $item['sales_time'] = $item['sale_time'];
            $item['sales_weight'] = $item['weight'];
            $item['sales_amount'] = $item['total_price'];
        }
        
        success(0, '请求成功', $list, $count);
    }

    /**
     * 计量商品销售统计
     */
    public function sales_stats()
    {
        //权限校验
        check_auth(request()->controller() . '/sales_stats', 0);
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        // 获取门店列表
        $shop_list = Db::name('shop')
            ->where(['clientkeynum' => $basekeynum])
            ->field('id, title')
            ->select();
        
        $this->assign('shop_list', $shop_list);
        return $this->fetch();
    }

    /**
     * 获取计量商品销售统计数据
     */
    public function ajax_sales_stats()
    {
        //权限校验
        check_auth(request()->controller() . '/sales_stats', 1);
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        $page = request()->param('page', 1);
        $page_size = request()->param('limit', 10);
        $shop_id = request()->param('shop_id', 0);
        $start_date = request()->param('start_date', '');
        $end_date = request()->param('end_date', '');

        // 构建查询条件
        $where = [['ws.clientkeynum', '=', $basekeynum]];

        if (!empty($shop_id)) {
            $where[] = ['ws.shop_id', '=', $shop_id];
        }

        if (!empty($start_date)) {
            $where[] = ['ws.sale_time', '>=', $start_date];
        }

        if (!empty($end_date)) {
            $where[] = ['ws.sale_time', '<=', $end_date . ' 23:59:59'];
        }

        // 查询统计数据
        $query = Db::name('weight_product_sales')->alias('ws')
            ->join('products p', 'p.id = ws.product_id')
            ->join('product_inventory pi', 'pi.id = ws.inventory_id', 'LEFT')
            ->join('shop s', 's.id = ws.shop_id', 'LEFT')
            ->where($where)
            ->field('p.title as product_name, pi.title as inventory_name, s.title as shop_name,
                     SUM(ws.weight) as total_weight, SUM(ws.total_price) as total_amount, 
                     COUNT(*) as sale_count, AVG(ws.unit_price) as avg_unit_price')
            ->group('ws.product_id, ws.inventory_id, ws.shop_id')
            ->order('total_amount', 'desc');

        $count = $query->count();
        $list = $query->page($page, $page_size)->select();

        // 处理数据
        foreach ($list as &$item) {
            if (empty($item['inventory_name'])) {
                $item['inventory_name'] = '默认规格';
            }
            
            // 格式化数据
            $item['total_weight'] = round($item['total_weight'], 3) . 'kg';
            $item['total_amount'] = '¥' . number_format($item['total_amount'], 2);
            $item['avg_unit_price'] = '¥' . number_format($item['avg_unit_price'], 2) . '/kg';
        }

        success(0, '请求成功', $list, $count);
    }
} 