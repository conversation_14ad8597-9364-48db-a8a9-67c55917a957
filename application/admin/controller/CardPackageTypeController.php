<?php
namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\Request;

/**
 * 套餐卡类型管理控制器
 */
class CardPackageTypeController extends Controller
{
    /**
     * 卡型列表
     */
    public function index()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        if (request()->isAjax()) {
            $where = [];
            $name = input('name', '');
            $status = input('status', '');
            $where[] = ['clientkeynum', '=', $clientkeynum];
            
            if ($name) {
                $where[] = ['name', 'like', "%{$name}%"];
            }
            
            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }
            
            $list = model('CardPackageType')
                ->where($where)
                ->order('id', 'desc')
                ->paginate(input('limit', 15));
                
            return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $list->items()]);
        }
        
        return $this->fetch();
    }
    
    /**
     * 添加卡型
     */
    public function add()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        if (request()->isAjax()) {
            $data = input('post.');
            $model = new \app\admin\model\CardPackageType;
            
            // 开启事务
            $model->startTrans();
            try {
                // 新增卡型
                $model->save([
                    'clientkeynum' => $clientkeynum,
                    'name' => $data['name'],
                    'price' => $data['price'],
                    'description' => $data['description'],
                    'status' => $data['status'],
                    'add_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ]);
                $typeId = $model->id;
                
                // 处理关联套餐
                if (isset($data['package_ids']) && !empty($data['package_ids'])) {
                    $relationData = [];
                    foreach ($data['package_ids'] as $packageId) {
                        // 检查packageId是否为逗号分隔的字符串，如果是则分割处理
                        if (strpos($packageId, ',') !== false) {
                            $packageIdArray = explode(',', $packageId);
                            foreach ($packageIdArray as $singleId) {
                                if (is_numeric($singleId) && !empty($singleId)) {
                                    $relationData[] = [
                                        'clientkeynum' => $clientkeynum,
                                        'type_id' => $typeId,
                                        'package_id' => intval($singleId),
                                        'sort' => isset($data['package_sorts'][$packageId]) ? intval($data['package_sorts'][$packageId]) : 0,
                                        'add_time' => date('Y-m-d H:i:s')
                                    ];
                                }
                            }
                        } else {
                            // 直接处理单个ID
                            if (is_numeric($packageId) && !empty($packageId)) {
                                $relationData[] = [
                                    'clientkeynum' => $clientkeynum,
                                    'type_id' => $typeId,
                                    'package_id' => intval($packageId),
                                    'sort' => isset($data['package_sorts'][$packageId]) ? intval($data['package_sorts'][$packageId]) : 0,
                                    'add_time' => date('Y-m-d H:i:s')
                                ];
                            }
                        }
                    }
                    
                    if (!empty($relationData)) {
                        model('CardTypePackageRelation')->saveAll($relationData);
                    }
                }
                
                $model->commit();
                return json(['code' => 0, 'msg' => '添加成功']);
            } catch (\Exception $e) {
                $model->rollback();
                return json(['code' => 1, 'msg' => '添加失败：' . $e->getMessage()]);
            }
        }
        
        // 获取所有套餐
        $packages = model('CardPackage')->where('status', 1)->select();
        $this->assign('packages', $packages);
        
        return $this->fetch();
    }
    
    /**
     * 编辑卡型
     */
    public function edit($id)
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        if (request()->isAjax()) {
            $data = input('post.');
            $model = new \app\admin\model\CardPackageType;
            
            // 开启事务
            $model->startTrans();
            try {
                // 更新卡型
                $model->where('id', $data['id'])->update([
                    'name' => $data['name'],
                    'price' => $data['price'],
                    'description' => $data['description'],
                    'status' => $data['status'],
                    'update_time' => date('Y-m-d H:i:s')
                ]);
                
                // 处理关联套餐
                if (isset($data['package_ids'])) {
                    // 先删除原有关联
                    model('CardTypePackageRelation')->where('clientkeynum', $clientkeynum)->where('type_id', $data['id'])->delete();
                    
                    // 添加新关联
                    if (!empty($data['package_ids'])) {
                        $relationData = [];
                        foreach ($data['package_ids'] as $key => $packageId) {
                            // 检查packageId是否为逗号分隔的字符串，如果是则分割处理
                            if (strpos($packageId, ',') !== false) {
                                $packageIdArray = explode(',', $packageId);
                                foreach ($packageIdArray as $singleId) {
                                    if (is_numeric($singleId) && !empty($singleId)) {
                                        $relationData[] = [
                                            'clientkeynum' => $clientkeynum,
                                            'type_id' => $data['id'],
                                            'package_id' => intval($singleId),
                                            'sort' => isset($data['package_sorts'][$key]) ? intval($data['package_sorts'][$key]) : 0,
                                            'add_time' => date('Y-m-d H:i:s')
                                        ];
                                    }
                                }
                            } else {
                                // 直接处理单个ID
                                if (is_numeric($packageId) && !empty($packageId)) {
                                    $relationData[] = [
                                        'clientkeynum' => $clientkeynum,
                                        'type_id' => $data['id'],
                                        'package_id' => intval($packageId),
                                        'sort' => isset($data['package_sorts'][$key]) ? intval($data['package_sorts'][$key]) : 0,
                                        'add_time' => date('Y-m-d H:i:s')
                                    ];
                                }
                            }
                        }
                        
                        if (!empty($relationData)) {
                            model('CardTypePackageRelation')->saveAll($relationData);
                        }
                    }
                }
                
                $model->commit();
                return json(['code' => 0, 'msg' => '更新成功']);
            } catch (\Exception $e) {
                $model->rollback();
                return json(['code' => 1, 'msg' => '更新失败：' . $e->getMessage()]);
            }
        }
        
        // 获取卡型信息
        $info = model('CardPackageType')->where('clientkeynum', $clientkeynum)->where('id', $id)->find();
        if (!$info) {
            $this->error('卡型不存在');
        }
        
        // 获取关联套餐
        $selectedPackages = model('CardTypePackageRelation')
            ->where('clientkeynum', $clientkeynum)
            ->where('type_id', $id)
            ->order('sort asc')
            ->column('package_id');
        
        // 获取所有套餐
        $packages = model('CardPackage')->where('clientkeynum', $clientkeynum)->where('status', 1)->select();
        
        $this->assign('info', $info);
        $this->assign('packages', $packages);
        $this->assign('selectedPackages', $selectedPackages);
        
        return $this->fetch('edit');
    }
    
    /**
     * 删除卡型
     */
    public function delete()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $id = input('id');
        
        // 检查是否有关联的卡
        $cardCount = model('Card')->where('clientkeynum', $clientkeynum)->where('type_id', $id)->count();
        if ($cardCount > 0) {
            return json(['code' => 1, 'msg' => '该卡型下有关联的卡，不能删除']);
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 删除卡型
            model('CardPackageType')->where('clientkeynum', $clientkeynum)->where('id', $id)->delete();
            
            // 删除关联套餐
            model('CardTypePackageRelation')->where('clientkeynum', $clientkeynum)->where('type_id', $id)->delete();
            
            Db::commit();
            return json(['code' => 0, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 更新卡型状态
     */
    public function updateStatus()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $id = input('id');
        $status = input('status');
        
        $result = model('CardPackageType')
            ->where('id', $id)
            ->where('clientkeynum', $clientkeynum)
            ->update([
                'status' => $status,
                'update_time' => date('Y-m-d H:i:s')
            ]);
            
        if ($result) {
            return json(['code' => 0, 'msg' => '状态更新成功']);
        } else {
            return json(['code' => 1, 'msg' => '状态更新失败']);
        }
    }
} 