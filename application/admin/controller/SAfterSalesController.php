<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright ( c ) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use app\admin\model\AfterSalesOrder;
use app\admin\model\AfterSalesItem;
use app\admin\model\AfterSalesLog;
use app\admin\model\AfterSalesRefund;
use app\admin\model\ClientMember;
use app\admin\model\ClientYcardModel;
use app\admin\model\Order;
use app\admin\model\Shop;
use app\admin\model\Product;
use app\admin\model\Inventory;
use app\admin\model\OrderDetail;
use app\admin\model\UserCard;
use app\api\model\OrderPayLog;
use app\api\model\PlatWechatSet;
use app\api\lib\Wechat;
use think\facade\Session;
use think\facade\Request;
use think\Db;
use think\Exception;
use think\facade\Log;
use app\api\model\CardUseLog;

/**
 * 门店售后管理控制器
 * 提供门店管理售后申请的功能，包括：
 * 1. 售后申请列表查看
 * 2. 售后申请详情查看
 * 3. 售后申请审核（通过/拒绝）
 * 4. 售后退款处理
 * 5. 售后操作日志查看
 */
class SAfterSalesController extends CnController
{
    /**
     * 初始化方法
     */
    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    /**
     * 售后申请列表页面
     * @return mixed
     */
    public function aftersaleslist()
    {
        $request = Request::instance();
        $param = $request->param();
        $status = $param['status'] ?? '';
        
        if ($status != '') {
            //权限校验
            check_auth(request()->controller() . '/aftersaleslist?status=' . $status, 0);
        } else {
            //权限校验
            check_auth(request()->controller() . '/aftersaleslist', 0);
        }

        return $this->fetch('aftersaleslist');
    }

    /**
     * 获取售后申请列表 - AJAX接口
     * @return void
     */
    public function ajax_get_after_sales_list()
    {
        $request = Request::instance();
        $param = $request->param();
        $status = $param['status'] ?? '';
        if ($status != '') {
            //权限校验
            check_auth(request()->controller() . '/aftersaleslist?status=' . $status, 1);
        } else {
            //权限校验
            check_auth(request()->controller() . '/aftersaleslist', 1);
        }
        
        $basekeynum = session('cn_accountinfo.keynum');
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        
        // 计算记录偏移量
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        
        // 获取当前门店ID
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        
        $where = "1=1 and shop_id='$shop_id'";
        
        // 状态筛选
        if ($status !== '') {
            $where .= " and status='$status'";
        }
        
        // 关键词搜索
        if ($keyword && $field) {
            $where .= " and $field like '%$keyword%'";
        }
        
        // 时间范围筛选
        $starttime = $param['starttime'] ?? '';
        $endtime = $param['endtime'] ?? '';
        if ($starttime && $endtime) {
            $where .= " and apply_time>='$starttime' and apply_time<='$endtime'";
        }
        
        // 查询售后申请列表
        $list = Db::table('after_sales_order')
            ->where($where)
            ->order('id desc')
            ->limit($offset . ',' . $pagesize)
            ->select();
            
        $count = Db::table('after_sales_order')->where($where)->count();
        
        // 处理列表数据
        foreach ($list as $key => &$value) {
            $value['status_text'] = $this->get_after_sales_status($value['status']);
            $value['type_text'] = $this->get_after_sales_type($value['after_sales_type']);
            
            // 获取用户信息
            $user_info = ClientMember::where('id', $value['user_id'])->find();
            $value['user_name'] = $user_info['name'] ?? '未知用户';
            $value['user_phone'] = $user_info['phone'] ?? '';
            
            // 获取售后商品信息
            $items = Db::table('after_sales_item')
                ->where('after_sales_id', $value['id'])
                ->select();
                
            $goodsinfo = "";
            foreach ($items as $item) {
                $product_info = json_decode($item['product_info'], true);
                $inventory_info = json_decode($item['inventory_info'], true);
                $goodsinfo .= ($product_info['title'] ?? '') . '【' . ($inventory_info['title'] ?? '') . '】' . "(" . $item['apply_quantity'] . ")" . '+';
            }
            $value['goodsinfo'] = rtrim($goodsinfo, '+');
            
            // 处理图片数据
            if (!empty($value['images'])) {
                // 如果images是JSON字符串，解析数组并计算数量
                $images_array = json_decode($value['images'], true);
                if (is_array($images_array) && count($images_array) > 0) {
                    $value['image_count'] = count($images_array);
                    
                    // 生成图片HTML
                    $image_html = '<div style="display: flex; flex-wrap: wrap; gap: 2px; align-items: center;">';
                    
                    // 显示前4张图片
                    $display_count = min(4, count($images_array));
                    for ($i = 0; $i < $display_count; $i++) {
                        $image_url = htmlspecialchars($images_array[$i]);
                        $image_html .= '<img src="' . $image_url . '" 
                                        style="width: 35px; height: 35px; object-fit: cover; border-radius: 3px; cursor: pointer; border: 1px solid #e6e6e6;" 
                                        onclick="previewImages(\'' . htmlspecialchars($value['images']) . '\', ' . $i . ')"
                                        title="点击查看大图">';
                    }
                    
                    // 如果还有更多图片，显示+数字
                    if (count($images_array) > 4) {
                        $more_count = count($images_array) - 4;
                        $image_html .= '<div style="width: 35px; height: 35px; background: #f0f0f0; border-radius: 3px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666; cursor: pointer; border: 1px solid #e6e6e6;" 
                                       onclick="previewImages(\'' . htmlspecialchars($value['images']) . '\', 0)" 
                                       title="还有' . $more_count . '张图片">
                                       +' . $more_count . '
                                       </div>';
                    }
                    
                    $image_html .= '</div>';
                    $value['image_html'] = $image_html;
                } else {
                    $value['image_count'] = 0;
                    $value['images'] = '[]';
                    $value['image_html'] = '<span style="color: #ccc; font-size: 12px;">无图片</span>';
                }
            } else {
                $value['image_count'] = 0;
                $value['images'] = '[]';
                $value['image_html'] = '<span style="color: #ccc; font-size: 12px;">无图片</span>';
            }
        }
        
        $rtdata['data'] = $list;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        
        echo json_encode($rtdata);
        die;
    }

    /**
     * 售后申请详情页面
     * @return mixed
     */
    public function detail()
    {
        //权限校验
        // check_auth(request()->controller() . '/detail', 0);
        
        $id = Request()->param('id');
        if (empty($id)) {
            error_tips('参数错误');
        }
        
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        
        // 获取售后申请详情
        $after_sales = Db::table('after_sales_order')
            ->where('id', $id)
            ->where('shop_id', $shop_id)
            ->find();
            
        if (empty($after_sales)) {
            error_tips('售后申请不存在');
        }
        
        // 获取用户信息
        $user_info = ClientMember::where('id', $after_sales['user_id'])->find();
        
        // 获取原订单信息
        $order_info = Db::table('order')->where('order_no', $after_sales['order_no'])->find();
        
        // 获取售后商品明细
        $items = Db::table('after_sales_item')
            ->where('after_sales_id', $id)
            ->select();
            
        // 处理商品明细数据
        foreach ($items as &$item) {
            $product_info = json_decode($item['product_info'], true);
            $inventory_info = json_decode($item['inventory_info'], true);
            $item['product_title'] = $product_info['title'] ?? '';
            $item['inventory_title'] = $inventory_info['title'] ?? '';
        }
        
        $this->assign('after_sales', $after_sales);
        $this->assign('user_info', $user_info);
        $this->assign('order_info', $order_info);
        $this->assign('items', $items);
        $this->assign('id', $id);
        
        return $this->fetch('detail');
    }

    /**
     * 审核售后申请（审核通过后直接执行退款）
     * @return void
     */
    public function audit()
    {
        //权限校验
        // check_auth(request()->controller() . '/audit', 1);
        
        $request = Request::instance();
        $param = $request->param();
        
        $after_sales_id = $param['after_sales_id'] ?? 0;
        $audit_result = $param['audit_result'] ?? 0; // 1-通过 2-拒绝
        $admin_remark = $param['admin_remark'] ?? '';
        $approved_amount = $param['approved_amount'] ?? 0;
        
        if (empty($after_sales_id) || !in_array($audit_result, [1, 2])) {
            $rtdata['code'] = 1;
            $rtdata['msg'] = '参数错误';
            echo json_encode($rtdata);
            die;
        }
        
        // 审核通过时必须提供退款金额
        if ($audit_result == 1 && $approved_amount <= 0) {
            $rtdata['code'] = 1;
            $rtdata['msg'] = '审核通过时必须提供退款金额';
            echo json_encode($rtdata);
            die;
        }
        
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $admin_info = session('cn_accountinfo');
        
        // 开启事务
        Db::startTrans();
        try {
            // 获取售后申请信息
            $after_sales = Db::table('after_sales_order')
                ->where('id', $after_sales_id)
                ->where('shop_id', $shop_id)
                ->find();
                
            if (empty($after_sales)) {
                throw new Exception('售后申请不存在');
            }
            
            if ($after_sales['status'] != 0) {
                throw new Exception('售后申请状态不允许审核');
            }
            
            if ($audit_result == 1) {
                // 审核通过，直接执行退款并完成售后
                
                // 如果是退货退款，需要回库存
                if ($after_sales['after_sales_type'] == 2) {
                    $this->restoreInventory($after_sales_id);
                }
                
                // 获取原订单信息
                $order = Db::table('order')->where('order_no', $after_sales['order_no'])->find();
                if (empty($order)) {
                    throw new Exception('原订单不存在');
                }
                
                // 生成退款单号
                $refund_no = 'RF' . date('YmdHis') . rand(1000, 9999);
                
                // 执行退款
                $this->processRefundByPaymentType($order, $after_sales, $approved_amount, $refund_no);
                
                // 更新售后申请状态为已完成
                $update_data = [
                    'status' => 10, // 直接设置为已完成
                    'admin_remark' => $admin_remark,
                    'approved_amount' => $approved_amount,
                    'actual_refund_amount' => $approved_amount,
                    'audit_time' => date('Y-m-d H:i:s'),
                    'audit_user' => $admin_info['username'],
                    'refund_time' => date('Y-m-d H:i:s'),
                    'complete_time' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                // 记录审核通过日志
                $this->addAfterSalesLog(
                    $after_sales_id,
                    $after_sales['after_sales_no'],
                    'audit',
                    2, // 管理员操作
                    $admin_info['id'],
                    $admin_info['username'],
                    '审核通过',
                    0,
                    1,
                    $admin_remark
                );
                
                // 记录退款日志
                $this->addAfterSalesLog(
                    $after_sales_id,
                    $after_sales['after_sales_no'],
                    'refund',
                    2, // 管理员操作
                    $admin_info['id'],
                    $admin_info['username'],
                    "执行退款，金额：{$approved_amount}元",
                    1,
                    9,
                    "退款单号：{$refund_no}"
                );
                
                // 记录完成日志
                $this->addAfterSalesLog(
                    $after_sales_id,
                    $after_sales['after_sales_no'],
                    'complete',
                    2, // 管理员操作
                    $admin_info['id'],
                    $admin_info['username'],
                    '售后流程完成',
                    9,
                    10,
                    '审核通过后直接完成退款'
                );
                
                $success_msg = '审核通过并退款成功';
                
            } else {
                // 审核拒绝
                $update_data = [
                    'status' => 2, // 审核拒绝
                    'admin_remark' => $admin_remark,
                    'audit_time' => date('Y-m-d H:i:s'),
                    'audit_user' => $admin_info['username'],
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                // 记录审核拒绝日志
                $this->addAfterSalesLog(
                    $after_sales_id,
                    $after_sales['after_sales_no'],
                    'audit',
                    2, // 管理员操作
                    $admin_info['id'],
                    $admin_info['username'],
                    '审核拒绝',
                    0,
                    2,
                    $admin_remark
                );
                
                $success_msg = '审核拒绝成功';
            }
            
            // 更新售后申请状态
            Db::table('after_sales_order')
                ->where('id', $after_sales_id)
                ->update($update_data);
            
            Db::commit();
            
            $rtdata['code'] = 0;
            $rtdata['msg'] = $success_msg;
            
        } catch (Exception $e) {
            Db::rollback();
            $rtdata['code'] = 1;
            $rtdata['msg'] = '操作失败：' . $e->getMessage();
        }
        
        echo json_encode($rtdata);
        die;
    }

    /**
     * 处理退款
     * @return void
     */
    public function process_refund()
    {
        //权限校验
        // check_auth(request()->controller() . '/process_refund', 1);
        
        $request = Request::instance();
        $param = $request->param();
        
        $after_sales_id = $param['after_sales_id'] ?? 0;
        $refund_amount = $param['refund_amount'] ?? 0;
        
        if (empty($after_sales_id) || $refund_amount <= 0) {
            $rtdata['code'] = 1;
            $rtdata['msg'] = '参数错误';
            echo json_encode($rtdata);
            die;
        }
        
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $admin_info = session('cn_accountinfo');
        
        // 开启事务
        Db::startTrans();
        try {
            // 获取售后申请信息
            $after_sales = Db::table('after_sales_order')
                ->where('id', $after_sales_id)
                ->where('shop_id', $shop_id)
                ->find();
                
            if (empty($after_sales)) {
                throw new Exception('售后申请不存在');
            }
            
            if ($after_sales['status'] != 1) {
                throw new Exception('售后申请状态不允许退款');
            }
            
            // 获取原订单信息
            $order = Db::table('order')->where('order_no', $after_sales['order_no'])->find();
            if (empty($order)) {
                throw new Exception('原订单不存在');
            }
            
            // 更新售后申请状态为退款中
            Db::table('after_sales_order')
                ->where('id', $after_sales_id)
                ->update([
                    'status' => 8,
                    'actual_refund_amount' => $refund_amount,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            
            // 生成退款单号
            $refund_no = 'RF' . date('YmdHis') . rand(1000, 9999);
            
            // 处理退款逻辑
            $this->processRefundByPaymentType($order, $after_sales, $refund_amount, $refund_no);
            
            // 更新售后申请状态为退款成功
            Db::table('after_sales_order')
                ->where('id', $after_sales_id)
                ->update([
                    'status' => 9,
                    'refund_time' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            
            // 记录操作日志
            $this->addAfterSalesLog(
                $after_sales_id,
                $after_sales['after_sales_no'],
                'refund',
                2, // 管理员操作
                $admin_info['id'],
                $admin_info['username'],
                "处理退款，金额：{$refund_amount}元",
                8,
                9,
                "退款单号：{$refund_no}"
            );
            
            Db::commit();
            
            $rtdata['code'] = 0;
            $rtdata['msg'] = '退款处理成功';
            
        } catch (Exception $e) {
            Db::rollback();
            $rtdata['code'] = 1;
            $rtdata['msg'] = '退款处理失败：' . $e->getMessage();
        }
        
        echo json_encode($rtdata);
        die;
    }

    /**
     * 完成售后
     * @return void
     */
    public function complete()
    {
        //权限校验
        // check_auth(request()->controller() . '/complete', 1);
        
        $after_sales_id = Request()->param('after_sales_id', 0);
        
        if (empty($after_sales_id)) {
            $rtdata['code'] = 1;
            $rtdata['msg'] = '参数错误';
            echo json_encode($rtdata);
            die;
        }
        
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $admin_info = session('cn_accountinfo');
        
        try {
            // 获取售后申请信息
            $after_sales = Db::table('after_sales_order')
                ->where('id', $after_sales_id)
                ->where('shop_id', $shop_id)
                ->find();
                
            if (empty($after_sales)) {
                throw new Exception('售后申请不存在');
            }
            
            if ($after_sales['status'] != 9) {
                throw new Exception('售后申请状态不允许完成');
            }
            
            // 更新售后申请状态为完成
            Db::table('after_sales_order')
                ->where('id', $after_sales_id)
                ->update([
                    'status' => 10,
                    'complete_time' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            
            // 记录操作日志
            $this->addAfterSalesLog(
                $after_sales_id,
                $after_sales['after_sales_no'],
                'complete',
                2, // 管理员操作
                $admin_info['id'],
                $admin_info['username'],
                '完成售后处理',
                9,
                10,
                '售后流程完成'
            );
            
            $rtdata['code'] = 0;
            $rtdata['msg'] = '售后完成';
            
        } catch (Exception $e) {
            $rtdata['code'] = 1;
            $rtdata['msg'] = '操作失败：' . $e->getMessage();
        }
        
        echo json_encode($rtdata);
        die;
    }

    /**
     * 获取售后操作日志
     * @return void
     */
    public function ajax_after_sales_logs()
    {
        //权限校验
        // check_auth(request()->controller() . '/ajax_after_sales_logs', 1);
        
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.parent_basekeynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $id = Request()->param('id');

        $after_sales = Db::table('after_sales_order')
            ->where('clientkeynum', $clientkeynum)
            ->where('id', $id)
            ->where('shop_id', $shop_id)
            ->find();
            
        if (empty($after_sales)) {
            $rtdata['code'] = 1;
            $rtdata['msg'] = '售后申请不存在';
            $rtdata['data'] = [];
            $rtdata['count'] = 0;
            echo json_encode($rtdata);
            die;
        }

        $page = Request()->param('page', 1);
        $page_size = Request()->param('page_size', 10);
        $offset = $page_size * ($page - 1);

        $where = [
            'after_sales_id' => $id
        ];

        $list = Db::table('after_sales_log')
            ->where($where)
            ->order('id desc')
            ->limit($offset . ',' . $page_size)
            ->select();
            
        $count = Db::table('after_sales_log')->where($where)->count();

        $rtdata['code'] = 0;
        $rtdata['msg'] = '请求成功';
        $rtdata['data'] = $list;
        $rtdata['count'] = $count;
        
        echo json_encode($rtdata);
        die;
    }

    /**
     * 获取售后状态文本
     * @param int $status
     * @return string
     */
    private function get_after_sales_status($status)
    {
        $status_arr = [
            '0' => '待审核',
            '1' => '审核通过',
            '2' => '审核拒绝',
            '8' => '退款中',
            '9' => '退款成功',
            '10' => '售后完成',
            '-1' => '已取消'
        ];
        
        return $status_arr[$status] ?? '未知状态';
    }

    /**
     * 获取售后类型文本
     * @param int $type
     * @return string
     */
    private function get_after_sales_type($type)
    {
        $type_arr = [
            '1' => '仅退款',
            '2' => '退货退款'
        ];
        
        return $type_arr[$type] ?? '未知类型';
    }

    /**
     * 回库存处理（仅退货退款类型）
     * @param int $after_sales_id
     * @throws Exception
     */
    private function restoreInventory($after_sales_id)
    {
        $after_sales = Db::table('after_sales_order')->where('id', $after_sales_id)->find();
        if ($after_sales['after_sales_type'] != 2) {
            return; // 仅退货退款需要回库存
        }
        
        $items = Db::table('after_sales_item')->where('after_sales_id', $after_sales_id)->select();
        
        foreach ($items as $item) {
            // 获取订单详情信息
            $order_detail = Db::table('order_detail')->where('id', $item['order_detail_id'])->find();
            if (empty($order_detail)) {
                continue;
            }
            
            // 回库存
            Db::table('inventory')
                ->where('id', $order_detail['inventory_id'])
                ->setInc('stock', $item['apply_quantity']);
        }
    }

    /**
     * 根据支付方式处理退款
     * @param array $order 原订单信息
     * @param array $after_sales 售后申请信息
     * @param float $refund_amount 退款金额
     * @param string $refund_no 退款单号
     * @throws Exception
     */
    private function processRefundByPaymentType($order, $after_sales, $refund_amount, $refund_no)
    {
        // 创建退款记录
        $refund_data = [
            'after_sales_id' => $after_sales['id'],
            'after_sales_no' => $after_sales['after_sales_no'],
            'order_no' => $order['order_no'],
            'refund_no' => $refund_no,
            'total_amount' => $refund_amount,
            'status' => 0,
            'refund_reason' => $after_sales['refund_reason'],
            'operator_id' => session('cn_accountinfo.account_id'),
            'operator_name' => session('cn_accountinfo.accountname'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 根据支付方式处理退款
        if ($order['card_price'] > 0) {
            // 纯卡支付
            $this->processCardRefund($order, $refund_amount, $refund_data);
        } elseif ($order['real_price'] > 0) {
            // 微信支付
            $this->processWechatRefund($order, $refund_amount, $refund_data);
        } else {
            throw new Exception('订单支付信息异常');
        }
    }

    /**
     * 处理卡支付退款
     * @param array $order
     * @param float $refund_amount
     * @param array $refund_data
     * @throws Exception
     */
    private function processCardRefund($order, $refund_amount, $refund_data)
    {
        $refund_data['refund_type'] = 2;
        $refund_data['refund_channel'] = 'card';
        $refund_data['card_amount'] = $refund_amount;
        
        // 获取用户卡信息
        $user_card = ClientYcardModel::where('member_id', $order['user_id'])
            ->where('clientkeynum', $order['clientkeynum'])
            ->find();
            
        if (empty($user_card)) {
            throw new Exception('用户卡信息不存在');
        }
        
        // 退还卡余额
        ClientYcardModel::where('id', $user_card['id'])
            ->setInc('yu_money', $refund_amount);
        

        // 加入 client_ycard_use_log 表
        // 记录卡使用日志
        CardUseLog::create([
            'basekeynum' => $order['clientkeynum'],
            'member_id' => $order['user_id'],
            'order_sn' => $order['order_no'],
            'cardnum' => $user_card['cardnum'],
            'yu_money' => $user_card['yu_money'],
            'after_money' => $user_card['yu_money'] - $refund_amount,
            'use_money' => $refund_amount,
            'status' => 2,
            'add_time' => date('Y-m-d H:i:s'),
        ]);

        // // 记录卡消费日志
        // Db::table('user_card_log')->insert([
        //     'user_id' => $order['user_id'],
        //     'card_id' => $user_card['id'],
        //     'clientkeynum' => $order['clientkeynum'],
        //     'type' => 'refund',
        //     'amount' => $refund_amount,
        //     'balance_before' => $user_card['price'],
        //     'balance_after' => $user_card['price'] + $refund_amount,
        //     'order_no' => $order['order_no'],
        //     'remark' => "售后退款：{$refund_data['after_sales_no']}",
        //     'create_time' => time()
        // ]);
        
        $refund_data['status'] = 1;
        $refund_data['refund_time'] = date('Y-m-d H:i:s');
        
        // 插入退款记录
        Db::table('after_sales_refund')->insert($refund_data);
    }

    /**
     * 处理微信支付退款
     * @param array $order
     * @param float $refund_amount
     * @param array $refund_data
     * @throws Exception
     */
    private function processWechatRefund($order, $refund_amount, $refund_data)
    {
        try {
            $refund_data['refund_type'] = 1;
            $refund_data['refund_channel'] = 'wechat';
            $refund_data['wechat_amount'] = $refund_amount;
            
            // 获取支付记录
            $pay_log = OrderPayLog::where(['order_no' => $order['order_no']])
                ->where('is_pay', 1)
                ->order('id desc')
                ->find();

            if (empty($pay_log)) {
                throw new Exception('未找到支付记录');
            }

            // 获取微信支付配置
            $wechat_config = PlatWechatSet::getInfoByKeyNum($order['clientkeynum']);
            if (empty($wechat_config)) {
                throw new Exception('微信支付配置不存在');
            }

            // 调用微信退款API
            $result = Wechat::handleRefund(
                $wechat_config,
                $pay_log['id'], // 原订单号
                $refund_data['refund_no'], // 退款单号
                intval($order['real_price'] * 100), // 原订单金额（分）
                intval($refund_amount * 100) // 退款金额（分）
            );

            Log::info('微信退款结果：' . json_encode($result));

            if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
                // 退款成功
                $refund_data['status'] = 1;
                $refund_data['refund_time'] = date('Y-m-d H:i:s');
                $refund_data['third_party_refund_no'] = $result['refund_id'] ?? '';
                $refund_data['refund_response'] = json_encode($result);
                
                // 插入退款记录
                Db::table('after_sales_refund')->insert($refund_data);
                
                Log::info('微信售后退款成功：' . $refund_data['refund_no']);
            } else {
                // 退款失败
                $error_msg = $result['err_code_des'] ?? ($result['return_msg'] ?? '退款失败');
                $refund_data['status'] = 2;
                $refund_data['error_msg'] = $error_msg;
                $refund_data['refund_response'] = json_encode($result);
                
                // 插入退款记录
                Db::table('after_sales_refund')->insert($refund_data);
                
                throw new Exception('微信退款失败：' . $error_msg);
            }
            
        } catch (Exception $e) {
            Log::error('微信售后退款异常：' . $e->getMessage());
            
            // 记录失败的退款记录
            $refund_data['status'] = 2;
            $refund_data['error_msg'] = $e->getMessage();
            Db::table('after_sales_refund')->insert($refund_data);
            
            throw new Exception('微信退款失败：' . $e->getMessage());
        }
    }

    /**
     * 处理组合支付退款
     * @param array $order
     * @param float $refund_amount
     * @param array $refund_data
     * @throws Exception
     */
    private function processComboRefund($order, $refund_amount, $refund_data)
    {
        try {
            $refund_data['refund_type'] = 3;
            $refund_data['refund_channel'] = 'combo';
            
            $card_amount = min($refund_amount, $order['card_price']);
            $wechat_amount = $refund_amount - $card_amount;
            
            $refund_data['card_amount'] = $card_amount;
            $refund_data['wechat_amount'] = $wechat_amount;
            
            // 处理卡余额退款
            if ($card_amount > 0) {
                $user_card = ClientYcardModel::where('user_id', $order['user_id'])
                    ->where('clientkeynum', $order['clientkeynum'])
                    ->find();
                    
                if (empty($user_card)) {
                    throw new Exception('用户卡信息不存在');
                }
                
                // 退还卡余额
                ClientYcardModel::where('id', $user_card['id'])
                    ->setInc('price', $card_amount);
                    
                // 记录卡消费日志
                Db::table('user_card_log')->insert([
                    'user_id' => $order['user_id'],
                    'card_id' => $user_card['id'],
                    'clientkeynum' => $order['clientkeynum'],
                    'type' => 'refund',
                    'amount' => $card_amount,
                    'balance_before' => $user_card['price'],
                    'balance_after' => $user_card['price'] + $card_amount,
                    'order_no' => $order['order_no'],
                    'remark' => "售后退款：{$refund_data['after_sales_no']}",
                    'create_time' => time()
                ]);
            }
            
            // 处理微信退款
            if ($wechat_amount > 0) {
                // 获取支付记录
                $pay_log = OrderPayLog::where(['order_no' => $order['order_no']])
                    ->where('is_pay', 1)
                    ->order('id desc')
                    ->find();

                if (empty($pay_log)) {
                    throw new Exception('未找到支付记录');
                }

                // 获取微信支付配置
                $wechat_config = PlatWechatSet::getInfoByKeyNum($order['clientkeynum']);
                if (empty($wechat_config)) {
                    throw new Exception('微信支付配置不存在');
                }

                // 调用微信退款API
                $result = Wechat::handleRefund(
                    $wechat_config,
                    $pay_log['id'], // 原订单号
                    $refund_data['refund_no'], // 退款单号
                    intval($order['total_price'] * 100), // 原订单金额（分）
                    intval($wechat_amount * 100) // 退款金额（分）
                );

                Log::info('组合支付微信退款结果：' . json_encode($result));

                if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
                    $refund_data['third_party_refund_no'] = $result['refund_id'] ?? '';
                    $refund_data['refund_response'] = json_encode($result);
                    Log::info('组合支付微信退款成功：' . $refund_data['refund_no']);
                } else {
                    $error_msg = $result['err_code_des'] ?? ($result['return_msg'] ?? '微信退款失败');
                    throw new Exception('微信退款失败：' . $error_msg);
                }
            }
            
            $refund_data['status'] = 1;
            $refund_data['refund_time'] = date('Y-m-d H:i:s');
            
            // 插入退款记录
            Db::table('after_sales_refund')->insert($refund_data);
            
            Log::info('组合支付售后退款成功：' . $refund_data['refund_no']);
            
        } catch (Exception $e) {
            Log::error('组合支付售后退款异常：' . $e->getMessage());
            
            // 记录失败的退款记录
            $refund_data['status'] = 2;
            $refund_data['error_msg'] = $e->getMessage();
            if (isset($result)) {
                $refund_data['refund_response'] = json_encode($result);
            }
            Db::table('after_sales_refund')->insert($refund_data);
            
            throw new Exception('组合支付退款失败：' . $e->getMessage());
        }
    }

    /**
     * 添加售后操作日志
     * @param int $after_sales_id
     * @param string $after_sales_no
     * @param string $action_type
     * @param int $operator_type
     * @param int $operator_id
     * @param string $operator_name
     * @param string $operation_content
     * @param int $before_status
     * @param int $after_status
     * @param string $remark
     */
    private function addAfterSalesLog($after_sales_id, $after_sales_no, $action_type, $operator_type, $operator_id, $operator_name, $operation_content, $before_status, $after_status, $remark = '')
    {
        Db::table('after_sales_log')->insert([
            'after_sales_id' => $after_sales_id,
            'after_sales_no' => $after_sales_no,
            'action_type' => $action_type,
            'operator_type' => $operator_type,
            'operator_id' => $operator_id,
            'operator_name' => $operator_name,
            'operation_content' => $operation_content,
            'before_status' => $before_status,
            'after_status' => $after_status,
            'remark' => $remark,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
} 