<?php

namespace app\admin\controller;

use app\admin\model\YlyPrintLog;
use app\admin\model\Shop;
use app\common\service\YlyPrintService;
use think\Request;
use think\Controller;

/**
 * 打印日志管理控制器
 * Class YlyPrintLogController
 * @package app\admin\controller
 */
class YlyPrintLogController extends Controller
{
    /**
     * 打印日志列表
     */
    public function index(Request $request)
    {
        $clientkeynum = $this->getClientkeynum();
        $where = ['clientkeynum' => $clientkeynum];
        
        // 搜索条件
        $shopId = $request->param('shop_id', '');
        $orderNo = $request->param('order_no', '');
        $printStatus = $request->param('print_status', '');
        $startDate = $request->param('start_date', '');
        $endDate = $request->param('end_date', '');
        
        if (!empty($shopId)) {
            $where['shop_id'] = $shopId;
        }
        
        if (!empty($orderNo)) {
            $where['order_no'] = $orderNo;
        }
        
        if ($printStatus !== '') {
            $where['print_status'] = $printStatus;
        }
        
        if (!empty($startDate)) {
            $where['start_date'] = $startDate;
        }
        
        if (!empty($endDate)) {
            $where['end_date'] = $endDate;
        }
        
        // 获取分页数据
        $list = YlyPrintLog::getList($where, 20);
        
        // 获取门店列表
        $shopList = Shop::where('clientkeynum', $clientkeynum)->field('id,name')->select();
        
        // 获取统计信息
        $statistics = YlyPrintLog::getStatistics($clientkeynum);
        $todayStatistics = YlyPrintLog::getStatistics($clientkeynum, date('Y-m-d'));
        
        // 状态选项
        $statusOptions = [
            YlyPrintLog::STATUS_PENDING => YlyPrintLog::getStatusText(YlyPrintLog::STATUS_PENDING),
            YlyPrintLog::STATUS_PRINTING => YlyPrintLog::getStatusText(YlyPrintLog::STATUS_PRINTING),
            YlyPrintLog::STATUS_SUCCESS => YlyPrintLog::getStatusText(YlyPrintLog::STATUS_SUCCESS),
            YlyPrintLog::STATUS_FAILED => YlyPrintLog::getStatusText(YlyPrintLog::STATUS_FAILED),
        ];
        
        $this->assign('list', $list);
        $this->assign('shopList', $shopList);
        $this->assign('statistics', $statistics);
        $this->assign('todayStatistics', $todayStatistics);
        $this->assign('statusOptions', $statusOptions);
        $this->assign('search', [
            'shop_id' => $shopId,
            'order_no' => $orderNo,
            'print_status' => $printStatus,
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);
        
        return $this->fetch();
    }
    
    /**
     * 查看打印内容
     */
    public function viewContent(Request $request)
    {
        $id = $request->param('id', 0);
        $log = YlyPrintLog::find($id);
        
        if (!$log) {
            $this->error('日志记录不存在');
        }
        
        $this->assign('log', $log);
        return $this->fetch();
    }
    
    /**
     * 重试打印
     */
    public function retry(Request $request)
    {
        if (!$request->isPost()) {
            $this->error('请求方式错误');
        }
        
        $id = $request->param('id', 0);
        $clientkeynum = $this->getClientkeynum();
        
        try {
            $printService = new YlyPrintService($clientkeynum);
            $result = $printService->retryPrint($id);
            
            if ($result['success']) {
                $this->success($result['message']);
            } else {
                $this->error($result['message']);
            }
        } catch (\Exception $e) {
            $this->error('重试失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 批量重试失败的打印任务
     */
    public function batchRetry(Request $request)
    {
        if (!$request->isPost()) {
            $this->error('请求方式错误');
        }
        
        $clientkeynum = $this->getClientkeynum();
        
        try {
            $printService = new YlyPrintService($clientkeynum);
            
            // 获取失败的打印记录
            $failedLogs = YlyPrintLog::getFailedLogs($clientkeynum);
            
            if (empty($failedLogs)) {
                $this->error('没有可重试的失败记录');
            }
            
            $successCount = 0;
            $failedCount = 0;
            
            foreach ($failedLogs as $log) {
                $result = $printService->retryPrint($log['id']);
                if ($result['success']) {
                    $successCount++;
                } else {
                    $failedCount++;
                }
            }
            
            $message = "批量重试完成：成功 {$successCount} 条，失败 {$failedCount} 条";
            $this->success($message);
            
        } catch (\Exception $e) {
            $this->error('批量重试失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 删除打印日志
     */
    public function delete(Request $request)
    {
        if (!$request->isPost()) {
            $this->error('请求方式错误');
        }
        
        $id = $request->param('id', 0);
        
        try {
            $result = YlyPrintLog::destroy($id);
            if ($result) {
                $this->success('删除成功');
            } else {
                $this->error('删除失败');
            }
        } catch (\Exception $e) {
            $this->error('删除失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 清理过期日志
     */
    public function cleanExpired(Request $request)
    {
        if (!$request->isPost()) {
            $this->error('请求方式错误');
        }
        
        $days = $request->param('days', 30);
        
        try {
            $count = YlyPrintLog::cleanExpiredLogs($days);
            $this->success("清理完成，共删除 {$count} 条过期日志");
        } catch (\Exception $e) {
            $this->error('清理失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 导出打印日志
     */
    public function export(Request $request)
    {
        $clientkeynum = $this->getClientkeynum();
        $where = ['clientkeynum' => $clientkeynum];
        
        // 搜索条件
        $shopId = $request->param('shop_id', '');
        $orderNo = $request->param('order_no', '');
        $printStatus = $request->param('print_status', '');
        $startDate = $request->param('start_date', '');
        $endDate = $request->param('end_date', '');
        
        if (!empty($shopId)) {
            $where['shop_id'] = $shopId;
        }
        
        if (!empty($orderNo)) {
            $where['order_no'] = $orderNo;
        }
        
        if ($printStatus !== '') {
            $where['print_status'] = $printStatus;
        }
        
        if (!empty($startDate)) {
            $where['start_date'] = $startDate;
        }
        
        if (!empty($endDate)) {
            $where['end_date'] = $endDate;
        }
        
        // 获取所有数据（不分页）
        $list = YlyPrintLog::getList($where, 10000);
        
        // 设置CSV头
        $filename = '打印日志_' . date('Y-m-d_H-i-s') . '.csv';
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        
        // 输出CSV内容
        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // UTF-8 BOM
        
        // CSV表头
        fputcsv($output, [
            'ID', '门店名称', '订单号', '打印机序列号', 
            '打印状态', '错误信息', '重试次数', '打印时间', '创建时间'
        ]);
        
        // CSV数据
        foreach ($list as $item) {
            fputcsv($output, [
                $item['id'],
                $item['shop']['name'] ?? '',
                $item['order_no'],
                $item['printer_sn'],
                YlyPrintLog::getStatusText($item['print_status']),
                $item['error_message'],
                $item['retry_count'],
                $item['print_time'],
                $item['created_at']
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * 获取客户端标识
     */
    private function getClientkeynum()
    {
        return session('clientkeynum') ?: 'default';
    }
} 