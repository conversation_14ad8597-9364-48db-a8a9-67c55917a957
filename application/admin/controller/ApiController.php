<?php

namespace app\admin\controller;

use app\admin\model\Address;
use app\admin\model\CardTypeClassifyModel;
use app\admin\model\CardType;
use app\admin\model\ClientCardUseLog;
use app\admin\model\ClientMember;
use app\admin\model\ClientOrderInfo;
use app\admin\model\ClientYcardModel;
use app\admin\model\GoodsClassifyModel;
use app\admin\model\GoodsModel;
use app\admin\model\PaymentRecord;
use app\admin\model\PlatClient;
use app\admin\model\PlatWeixinSet;
use app\admin\tool\wechat\MiniProgram;
use app\admin\tool\wechat\PayV2;
use think\Db;
use think\Controller;
use think\facade\Log;
use think\facade\Request;

class ApiController extends Controller
{

    /*
     * 接口统一入口函数
     */
    public function index()
    {
        $request = Request::instance();
        $param = $request->param();
        //tp5写法
        $action = isset($param['action']) ? $param['action'] : '';
        $request = $param; //只接受post或者get到的数据

        //通过appid获取应用里面的客户信息clientkeynum
        $appid = isset($param['appid']) ? $param['appid'] : '';
        $applicationinfo = Db::table("client_application")->where("appid='$appid'")->find();
        //应用信息不存在
        if (empty($applicationinfo)) {
            $return_arr['sta'] = "40001";
            $return_arr['msg'] = "应用信息不存在！";
            $this->send_back($return_arr);
        }
        $clientkeynum = isset($applicationinfo['clientkeynum']) ? $applicationinfo['clientkeynum'] : ''; //平台客户的keynum
        $client_info = Db::table("plat_client")->where("keynum='$clientkeynum'")->find();
        if (empty($client_info)) {
            $return_arr['sta'] = "40006";
            $return_arr['msg'] = "平台客户信息获取失败！";
            $this->send_back($return_arr);
        }

        $this->clientkeynum = $clientkeynum;
        $this->is_log = isset($applicationinfo['is_log']) ? $applicationinfo['is_log'] : ''; //是否记录日志
        $this->is_sign = isset($applicationinfo['is_sign']) ? $applicationinfo['is_sign'] : ''; //是否验证签名
        $this->appkey = isset($applicationinfo['appkey']) ? $applicationinfo['appkey'] : ''; //应用的秘钥
        $this->request = isset($request) ? $request : ''; //接收到的数据
        $this->applicationinfo = isset($applicationinfo) ? $applicationinfo : ''; //应用信息

        $ts = isset($request['ts']) ? $request['ts'] : '';
        $sign = isset($request['sign']) ? $request['sign'] : '';

        //应用已被停用
        if ($applicationinfo['is_open'] != '1') {
            $return_arr['sta'] = "40002";
            $return_arr['msg'] = "应用已经被停用！";
            $this->send_back($return_arr);
        }

        //签名验证
        if ($action != 'check_sign_tool') {
            if ($applicationinfo['is_sign'] == '1') {
                //检验是否有时间戳，随机字符串参数
                if ($ts == '' or $sign == '') {
                    $return_arr['sta'] = "40003";
                    $return_arr['msg'] = "必要参数时间戳或者随机字符串为空！";
                    $this->send_back($return_arr);
                }
                //获取时间戳和服务器时间戳不能超过10s
                if (abs($ts - time()) > $applicationinfo['s']) {
                    $return_arr['sta'] = "40005";
                    $return_arr['msg'] = "客户端时间不正确！";
                    $this->send_back($return_arr);
                }

                if (!checkSign_wl($request, $applicationinfo['appkey'])) {
                    $return_arr['sta'] = "40003";
                    $return_arr['msg'] = "签名算法错误！";
                    $this->send_back($return_arr);
                }
            }
        }
        //当前类里面该方法存在
        if (method_exists($this, $action)) {
            $this->$action($request);
            die;
        }
        $return_arr['sta'] = "40004";
        $return_arr['msg'] = "没有找到对应方法！";
        $this->send_back($return_arr);
    }


    //统一返回函数
    private function send_back($return_arr)
    {
        //记录日志
        $is_log = isset($this->is_log) ? $this->is_log : '';
        $request = $this->request;
        $applicationinfo = isset($this->applicationinfo) ? $this->applicationinfo : '';
        if ($is_log == '1') {
            do_api_log($request, $return_arr, $applicationinfo);
        }
        echo json_encode($return_arr, JSON_UNESCAPED_UNICODE);
        die;
    }


    //签名校验工具wl
    private function check_sign_tool($request)
    {
        $appkey = $this->appkey;
        //$request = $_REQUEST;
        $param = Request::instance()->param();
        //$request= $_POST;
        //去掉sign和action参数
        unset($param['sign']);
        unset($param['action']);
        echo "<pre>去掉不参与签名的参数：";
        print_r($param);
        echo "<pre>去掉空值：";
        foreach ($param as $k => $v) {
            if (trim($v) != '') { //必须存在 去掉空
                $param1[$k] = $v;
            }
        }
        print_r($param1);
        echo "<pre>字典序：";
        ksort($param1);
        print_r($param1);
        echo "<pre>&拼接：";
        echo $str = formatBizQueryParaMap_wl($param1);
        echo "<pre>str后面拼接appkey：";
        echo $str1 = $str . "&appkey=" . $appkey;
        echo "<pre>md5字符串str：";
        echo $md5str = md5($str1);
        echo "<pre>转化成大写得到sign：";
        echo strtoupper($md5str);
    }

    private function get_config($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $webconfig = Db::table("webconfig")
            ->where("clientkeynum='$clientkeynum'")
            ->field("background_login")
            ->find();
        $plat_config = Db::table("plat_client")
            ->where("keynum='$clientkeynum'")
            ->field("is_open_card, is_show_banner, is_first_category")
            ->find();
        $data = [
            'background_login'  => $webconfig['background_login'],
            'is_open_card'      => $plat_config['is_open_card'],
            'is_show_banner'      => $plat_config['is_show_banner'],
            'is_first_category'      => $plat_config['is_first_category'],
        ];
        if (!empty($webconfig)) {
            $return_arr["sta"] = '1';
            $return_arr["msg"] = "获取成功！";
            $return_arr["data"] = $data;
            $this->send_back($return_arr);
            die;
        }
        $return_arr["sta"] = '0';
        $return_arr["msg"] = "获取失败！";
        $this->send_back($return_arr);
        die;
    }

    // 卡号密码登录，返回卡号信息
    private function card_login($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, true);
        $is_qrcode = $bodyarray['is_qrcode'];
        $member_id = $bodyarray['member_id'];
        if ($is_qrcode == '1') {
            $code = $bodyarray['code'];
            if (empty($code)) {
                $return_arr['sta'] = "0";
                $return_arr['msg'] = "卡号标识不可为空！";
                $this->send_back($return_arr);
            }
            $client_is_qrcode = db('plat_client')
                ->where("keynum='$clientkeynum'")
                ->value("is_qrcode");
            if ($client_is_qrcode != '1') {
                $return_arr['sta'] = "0";
                $return_arr['msg'] = "未开启二维码登录功能！";
                $this->send_back($return_arr);
            }
            $where = "code='$code' and is_del='0'";
        } else {
            $cardnum = $bodyarray['cardnum'];
            $cardpwd = $bodyarray['cardpwd'];
            if (empty($cardnum) or empty($cardpwd)) {
                $return_arr['sta'] = "0";
                $return_arr['msg'] = "卡号或密码不可为空！";
                $this->send_back($return_arr);
            }
            $cardpwd = encrypt($cardpwd);
            Log::error('pwd:'.$cardpwd);
            $where = "cardnum='$cardnum' and cardpwd='$cardpwd' and is_del='0'";
        }
        //查卡号密码信息是否正确
        $cardinfo = Db::table('client_ycard')
            ->where($where)
            ->where("clientkeynum='$clientkeynum'")
            ->find();
        if (empty($cardinfo)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "请检查卡号密码是否正确";
            $this->send_back($return_arr);
        }
        $client_is_open_card = db('plat_client')
            ->where("keynum='$clientkeynum'")
            ->value("is_open_card");
        if ($client_is_open_card == 1 && $cardinfo['is_bind_member'] == 1 && $member_id != $cardinfo['member_id']){
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "对不起，此卡已被其他人绑定";
            $this->send_back($return_arr);
        }


        //检查卡的状态
        if ($cardinfo['status'] != 3) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "对不起，此卡未开通";
            $this->send_back($return_arr);
        }
        if ($cardinfo['yu_money'] <= 0) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "对不起，此卡剩余额度为0";
            $this->send_back($return_arr);
        }
        if ($cardinfo['yu_exchange_num'] <= 0) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "对不起，此卡剩余兑换次数为0";
            $this->send_back($return_arr);
        }
        //检查是否过期了
        if (time() < $cardinfo['begin_dui']) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "该卡还没到开始兑换时间";
            $this->send_back($return_arr);
        }
        if (time() > $cardinfo['end_dui']) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "该卡已过期";
            $this->send_back($return_arr);
        }
        // 返回面值，剩余金额，总兑换次数，已兑换次数，卡型id，卡号
        $data['cardnum'] = $cardinfo['cardnum'];
        $data['kai_money'] = $cardinfo['kai_money'];
        $data['yu_money'] = $cardinfo['yu_money'];
        $data['kai_exchange_num'] = $cardinfo['kai_exchange_num'];
        $data['yu_exchange_num'] = $cardinfo['yu_exchange_num'];
        $data['cardtype_id'] = $cardinfo['cardtype_id'];
        $data['member_id'] = $cardinfo['member_id'];
        $data['is_bind_member'] = $cardinfo['is_bind_member'];
        $data['end_time'] = date('Y-m-d', $cardinfo['end_dui']);
        $return_arr['sta'] = "1";
        $return_arr['msg'] = "卡号密码登录成功";
        $return_arr['data'] = $data;
        $this->send_back($return_arr);
    }

    private function get_openid($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        //业务参数
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        if (empty($bodyarray)) {
            $return_arr["sta"] = '0';
            $return_arr["msg"] = "body参数异常!";
            $this->send_back($return_arr);
            die;
        }
        //业务必须参数 校验
        if (!isset($bodyarray["wx_code"])) {
            $return_arr["sta"] = '0';
            $return_arr["msg"] = "微信code不可为空！";
            $this->send_back($return_arr);
            die;
        }
        $wx_config = Db::table("plat_weixin_set")->where("basekeynum='$clientkeynum'")->find();
        $url = "https://api.weixin.qq.com/sns/jscode2session?appid=" . $wx_config["appid"] .
            "&secret=" . $wx_config["appsecret"] . "&js_code=" . $bodyarray["wx_code"] . "&grant_type=authorization_code";
        $rs = file_get_contents($url);
        $rs = json_decode($rs, 1);
        if (isset($rs["openid"])) {
            $return_arr["sta"] = '1';
            $return_arr["msg"] = "获取成功！";
            $return_arr["openid"] = $rs["openid"];
            $this->send_back($return_arr);
            die;
        } else {
            $return_arr["sta"] = '0';
            $return_arr["msg"] = "获取失败！";
            $return_arr["rs"] = $rs;
            $this->send_back($return_arr);
            die;
        }
    }

    // 拿微信唯一标识获取会员信息
    private function member_login($request)
    {
        // 平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $openid = $bodyarray['openid'];
        if (empty($openid)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "微信openid不可为空";
            $this->send_back($return_arr);
        }
        $nickname = $bodyarray['nickname'];
        if (empty($nickname)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "微信昵称不可为空";
            $this->send_back($return_arr);
        }
        $headimgurl = $bodyarray['headimgurl'];
        if (empty($headimgurl)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "微信头像不可为空";
            $this->send_back($return_arr);
        }
        $where = "openid='$openid' and clientkeynum='$clientkeynum'";
        $find = Db::table('client_member')->where($where)->find();
        if ($find) {
            $update["name"] = $nickname;
            $update["headimg"] = $headimgurl;
            Db::table("client_member")->where($where)->update($update);
        } else {
            $add["name"] = $nickname;
            $add["integral"] = 0;
            $add["add_time"] = time();
            $add["integralallin"] = 0;
            $add["integralallout"] = 0;
            $add["openid"] = $openid;
            $add["headimg"] = $headimgurl;
            $add["state"] = 1;
            $add["reg_from"] = 1;
            $add["keynum"] = create_guid();
            $add["clientkeynum"] = $clientkeynum;
            Db::table("client_member")->insertGetId($add);
        }
        $user_info = Db::table('client_member')->where($where)
            ->field("id, name, openid, headimg, phone")->find();
        $return_arr['sta'] = "1";
        $return_arr['msg'] = "登录成功";
        $return_arr['data'] = $user_info;
        $this->send_back($return_arr);
    }

    /**
     * 获取绑定该用户的所有卡号
     * @param $request
     * @return void
     */
    private function get_card_list($request)
    {
        //平台客户的keynum
        $basekeynum = $this->clientkeynum;
        $body = $request['body'];
        $params = json_decode($body, 1);
        $member_id = $params['member_id'];
        $page = $params['page'] ?? 1;
        $page_size = $params['page_size'] ?? 10;
        $status = $params['status'] ?? 1;

        // 验证该平台是否允许绑定用户
        $plat_client_info = PlatClient::getInfoByBaseKeyNum($basekeynum);
        if (empty($plat_client_info) || $plat_client_info['is_open_card'] != 1) {
            $return_arr = [
                'sta' => 0,
                'msg' => '当前平台未注册或暂不支持查看绑定的卡号'
            ];
            $this->send_back($return_arr);
        }

        if (empty($member_id)) {
            $return_arr = [
                'sta' => 0,
                'msg' => '参数错误'
            ];
            $this->send_back($return_arr);
        }
        $where = [
            'clientkeynum' => ['=', $basekeynum],
            'member_id' => $member_id,
            'is_bind_member' => 1
        ];
        $list = ClientYcardModel::where($where);
        $count = ClientYcardModel::where($where);
        if ($status == 2) {
            $where = [
                'yu_money' => ['>', 0],
                'status' => ['=', 3],
                'begin_dui' => ['<', time()],
                'end_dui' => ['>', time()]
            ];
            $list = $list->where($where);
            $count = $count->where($where);
        } elseif($status == 3) {
            $list = $list->where(function ($query) {
                $query->whereOr('yu_money', '<=', 0)
                    ->whereOr('begin_dui', '>', time())
                    ->whereOr('end_dui', '<', time())
                    ->whereOr('status', 'in', '0,1,2,4,5,-1');
            });
            $count = $count->where(function ($query){
                $query->whereOr('yu_money', '<=', 0)
                    ->whereOr('begin_dui', '>', time())
                    ->whereOr('end_dui', '<', time())
                    ->whereOr('status', 'in', '0,1,2,4,5,-1');
            });
        }
        $list = $list->page($page, $page_size)->order('bind_time', 'desc')->select();
        $count = $count->count();

        foreach ($list as $k => $v) {
            $list[$k]['cardpwd'] = decrypt($v['cardpwd']);
        }
        $return_arr = [
            'sta' => 1,
            'msg' => '请求成功',
            'data' => $list,
            'count' => $count
        ];
        $this->send_back($return_arr);
    }

    /**
     * 卡号绑定用户
     * @param $request
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function card_bind_member($request)
    {
        //平台客户的keynum
        $basekeynum = $this->clientkeynum;
        $body = $request['body'];
        $params = json_decode($body, 1);
        $member_id = $params['member_id'];
        Log::error('member_id'.$member_id);
        $cardnum = $params['cardnum'];
        $cardpwd = $params['cardpwd'];

        // 验证该平台是否允许绑定用户

        $plat_client_info = PlatClient::getInfoByBaseKeyNum($basekeynum);
        if (empty($plat_client_info) || $plat_client_info['is_open_card'] != 1) {
            $return_arr = [
                'sta' => 0,
                'msg' => '当前平台未注册或暂不支持绑定卡号'
            ];
            $this->send_back($return_arr);
        }

        if (empty($cardnum)) {
            $return_arr = [
                'sta' => 0,
                'msg' => '请选择卡号'
            ];
            $this->send_back($return_arr);
        }

        if (empty($cardpwd)) {
            $return_arr = [
                'sta' => 0,
                'msg' => '请输入卡号密码'
            ];
            $this->send_back($return_arr);
        }

        if (empty($member_id)) {
            $return_arr = [
                'sta' => 0,
                'msg' => '参数错误'
            ];
            $this->send_back($return_arr);
        }

        $where['clientkeynum'] = $basekeynum;

        $card_info = ClientYcardModel::getInfoByNum($cardnum, $where);

        if (empty($card_info) || $card_info['is_bind_member'] == 1) {
            $return_arr = [
                'sta' => 0,
                'msg' => '该卡号不存在或者已被绑定'
            ];
            $this->send_back($return_arr);
        }

        if (encrypt($cardpwd) != $card_info['cardpwd']) {
            $return_arr = [
                'sta' => 0,
                'msg' => '卡号密码错误'
            ];
            $this->send_back($return_arr);
        }



        $member_info = ClientMember::getInfoById($member_id, $where);
        if (empty($member_info) || empty($member_info['phone'])) {
            $return_arr = [
                'sta' => 0,
                'msg' => '未查询到用户信息或该用户未绑定手机号'
            ];
            $this->send_back($return_arr);
        }

        $result = ClientYcardModel::bindMember($member_id, $cardnum, $where);

        if ($result) {
            $return_arr = [
                'sta' => 1,
                'msg' => '绑定成功'
            ];
            $this->send_back($return_arr);
        }

        $return_arr = [
            'sta' => 0,
            'msg' => '绑定失败，请刷新重试'
        ];
        $this->send_back($return_arr);

    }

    /**
     * 小程序用户绑定手机号
     * @param $request
     * @return void
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function member_bind_phone($request)
    {
        //平台客户的keynum
        $basekeynum = $this->clientkeynum;
        $body = $request['body'];
        $params = json_decode($body, 1);
        $code = $params['code'];
        $member_id = $params['member_id'];
        if (empty($code)) {
            Log::error('code:' . $code);
            $return_arr = [
                'sta' => 0,
                'msg' => 'code参数错误'
            ];
            $this->send_back($return_arr);
        }

        // 获取客户平台下的微信配置
        $plate_wechat_config = PlatWeixinSet::getConfigByBaseKeyNum($basekeynum);
        $config = [
            'appid' => $plate_wechat_config['appid'],
            'app_secret' => $plate_wechat_config['appsecret'],
        ];

        $data = (new MiniProgram($config))->setAccessToken()->getUserPhone($code);

        $data = json_decode($data, true);

        if (!empty($data) && isset($data['errcode']) && $data['errcode'] == 0) {
            // 请求成功 绑定手机号
            $where = ['clientkeynum' => $basekeynum];
            $member_info = ClientMember::getInfoById($member_id, $where);
            if (empty($member_info)) {
                $return_arr = [
                    'sta' => 0,
                    'msg' => '未查询到用户信息'
                ];
                $this->send_back($return_arr);
            }
            $where['id'] = $member_id;
            $result = ClientMember::bindPhone($where, $data['phone_info']['phoneNumber']);
            $return_arr = [
                'sta' => 1,
                'msg' => '绑定成功'
            ];
            $this->send_back($return_arr);
        }
        Log::error('request wechat fail, info:' . json_encode($data));
        $return_arr = [
            'sta' => 0,
            'msg' => '请求微信服务器失败'
        ];
        $this->send_back($return_arr);

    }

    // 根据卡型id 获取卡型下分类
    private function get_classify($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $where = ['basekeynum' => $clientkeynum];
        $list = GoodsClassifyModel::getList($where);
        $list = $this->getCateInfo($list, 0);
        $return_arr['sta'] = "1";
        $return_arr['msg'] = "获取成功";
        $return_arr['data'] = $list;
        $this->send_back($return_arr);
    }

    private function getCateInfo($cateInfo,$pid=0)
    {
        $info = [];
        foreach($cateInfo as $k => $v){
            if($v['pid'] == $pid){
                $son = $this->getCateInfo($cateInfo,$v['id']);
                $v['son'] = $son;
                $info[] = $v;
            }
        }
        return $info;
    }


    // 根据卡型分类id 获取卡型下商品
    private function get_goods($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $classify_id = $bodyarray['classify_id'];
        $page = $bodyarray['page'] ?? 1;
        $page_size = $bodyarray['page_size'] ?? 10;
        if (empty($classify_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "卡型分类ID不可为空";
            $this->send_back($return_arr);
        }

        $classify_where = [
            'basekeynum' => $clientkeynum,
            'id' => $classify_id
        ];

        $classify_info = GoodsClassifyModel::getInfo($classify_where);

        if (empty($classify_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "分类不存在";
            $this->send_back($return_arr);
        }

        if ($classify_info['pid'] == 0) {
            // 一级分类 查询子分类
            $list = GoodsModel::where('goodsclassify', 'in', GoodsClassifyModel::where(['basekeynum' => $clientkeynum, 'pid' => $classify_info['id']])
                ->column('id'))
                ->order('o', 'asc')
                ->page($page, $page_size)
                ->select();
        } else {
            $list = GoodsModel::where('goodsclassify', '=', $classify_id)
                ->order('o', 'asc')
                ->page($page, $page_size)
                ->select();
        }

        $return_arr['sta'] = "1";
        $return_arr['msg'] = "获取成功";
        $return_arr['data'] = $list;
        $this->send_back($return_arr);
    }

    // 根据商品id 获取商品详情
    private function get_goods_detail($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $goods_id = $bodyarray['goods_id'];
        if (empty($goods_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "商品ID不可为空";
            $this->send_back($return_arr);
        }
        $goodsinfo = db('goods')->where("id='" . $goods_id . "' and clientkeynum='$clientkeynum' and status='1'")->find();
        if (empty($goodsinfo)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "商品信息不存在!!";
            $this->send_back($return_arr);
        } else {
            $return_arr['sta'] = "1";
            $return_arr['msg'] = "获取成功";
            $return_arr['data'] = $goodsinfo;
            $this->send_back($return_arr);
        }

    }

    // 商品id 加入购物车
    private function add_cart($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $goods_id = $bodyarray['goods_id'];
        if (empty($goods_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "卡型商品ID不可为空";
            $this->send_back($return_arr);
        }
        $info = db('goods')
            ->where("id='$goods_id' and clientkeynum='$clientkeynum' and status='1'")
            ->find();
        if (empty($info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "商品信息不存在!!";
            $this->send_back($return_arr);
        }
        $num = $bodyarray["num"];
        if (!is_numeric($num)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "加入购物车数量错误";
            $this->send_back($return_arr);
        }
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }
        $where = "goods_id='" . $info['id'] . "' and member_id='$member_id'";
        $find = Db::table("shopping_cart")->where($where)->find();
        if ($find) {
            $update["num"] = $find["num"] + $num;
            if ($update["num"] < 1) {
                $return_arr['sta'] = "0";
                $return_arr['msg'] = "商品数量最少为1!";
                $this->send_back($return_arr);
            }
            $rs = Db::table("shopping_cart")
                ->where($where)
                ->update($update);
        } else {
            $add["member_id"] = $member_id;
            $add["goods_id"] = $info['id'];
            $add["num"] = $num;
            if ($add["num"] < 1) {
                $return_arr['sta'] = "0";
                $return_arr['msg'] = "商品数量最少为1!";
                $this->send_back($return_arr);
            }
            $add["price"] = $info['price'];
            $add["goods_id"] = $goods_id;
            $rs = Db::table("shopping_cart")->insert($add);
        }
        if (!$rs) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "加入购物车失败!";
            $this->send_back($return_arr);
        }
        // 查一下当前勾选的总价
        $check_all_price = 0;
        $all_num = 0;
        $checked_cart = Db::table("shopping_cart")->where("member_id='$member_id' and is_check='1'")->field("price,num")->select();
        foreach ($checked_cart as $key => $value) {
            $check_all_price += $value['price'] * $value['num'];
            $all_num += $value['num'];
        }
        $return_arr["sta"] = 1;
        $return_arr["msg"] = "操作成功";
        $return_arr["check_all_price"] = $check_all_price;
        $return_arr["all_num"] = $all_num;
        $this->send_back($return_arr);
    }

    // 购物车id 触发是否勾选
    private function cart_check($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $cart_id = $bodyarray["cart_id"];
        if (empty($cart_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "购物车ID不可为空!";
            $this->send_back($return_arr);
        }
        $find = Db::table("shopping_cart")->where("id='$cart_id'")->find();
        if (empty($find)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "购物车信息不存在!";
            $this->send_back($return_arr);
        }
        if (
            $find["is_check"] == 1
        ) {
            $update["is_check"] = 0;
        } else {
            $update["is_check"] = 1;
        }
        $rs = Db::table("shopping_cart")->where("id='$cart_id'")->update($update);
        if (empty($rs)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "操作失败!";
            $this->send_back($return_arr);
        }
        $member_id = $find['member_id'];
        // 查一下当前勾选的总价
        $check_all_price = 0;
        $checked_cart = Db::table("shopping_cart")->where("member_id='$member_id' and is_check='1'")->field("price,num")->select();
        foreach ($checked_cart as $key => $value) {
            $check_all_price += $value['price'] * $value['num'];
        }
        $return_arr["sta"] = 1;
        $return_arr["msg"] = "操作成功";
        $return_arr["check_all_price"] = $check_all_price;
        $this->send_back($return_arr);
    }

    // 删除购物车商品
    private function cart_checkall($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }
        $is_check = $bodyarray["is_check"];
        $update["is_check"] = $is_check;
        $rs = Db::table("shopping_cart")->where("member_id='$member_id'")->update($update);
        if (empty($rs)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "操作失败!";
            $this->send_back($return_arr);
        }
        // 查一下当前勾选的总价
        $check_all_price = 0;
        $checked_cart = Db::table("shopping_cart")->where("member_id='$member_id' and is_check='1'")->field("price,num")->select();
        foreach ($checked_cart as $key => $value) {
            $check_all_price += $value['price'] * $value['num'];
        }
        $return_arr["sta"] = 1;
        $return_arr["msg"] = "操作成功";
        $return_arr["check_all_price"] = $check_all_price;
        $this->send_back($return_arr);
    }

    // 获取购物车内所有商品以及总价
    private function cart_list($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }
        $shopping_cart = Db::table("shopping_cart")->where("member_id='" . $member_info["id"] . "'")->select();
        $all_price = 0;
        $all_num = 0;
        foreach ($shopping_cart as $key => $value) {
            $goodsinfo = db('goods')->where("id='" . $value['goods_id'] . "' and clientkeynum='$clientkeynum' and status='1'")->find();
            if (empty($goodsinfo)) {
                unset($shopping_cart[$key]);
                continue;
            }
            $shopping_cart[$key]['goodsname'] = $goodsinfo['goodsname'];
            $shopping_cart[$key]['goodsimg'] = $goodsinfo['goodsimg'];
            $shopping_cart[$key]['price'] = $goodsinfo['price'];
            $shopping_cart[$key]['market_price'] = $goodsinfo['market_price'];
            $all_num += $value['num'];
            if ($value["is_check"]) {
                $all_price += $goodsinfo["price"] * $value["num"];
            }
        }
        $return_arr["sta"] = 1;
        $return_arr["msg"] = "操作成功";
        $return_arr["data"] = $shopping_cart;
        $return_arr["check_all_price"] = $all_price;
        $return_arr['all_num'] = $all_num;
        $this->send_back($return_arr);
    }

    // 删除购物车商品
    private function del_cart($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $cart_id = $bodyarray["cart_id"];
        if (empty($cart_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "购物车ID不可为空!";
            $this->send_back($return_arr);
        }
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $rs = Db::table("shopping_cart")->where("member_id='" . $member_id . "' and id='$cart_id'")->delete();
        if (empty($rs)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "操作失败!";
            $this->send_back($return_arr);
        }
        $return_arr["sta"] = 1;
        $return_arr["msg"] = "操作成功";
        $this->send_back($return_arr);
    }

    // 根据会员id 获取历史订单的地址库(去重)
    private function get_address($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $arr = array();
        $address_md5 = db('client_order_info')->where("memberid='$member_id'")->distinct(true)->field('all_address_info_md5')->select();
        foreach ($address_md5 as $key => $value) {
            $order_address_info = db('client_order_info')
                ->where("all_address_info_md5='" . $value['all_address_info_md5'] . "'")
                ->field("name,phone,province,city,area,address")
                ->find();
            $arr[] = $order_address_info;
        }
        $return_arr["sta"] = 1;
        $return_arr["msg"] = "操作成功";
        $return_arr["data"] = $arr;
        $this->send_back($return_arr);
    }

    // 添加收货地址
    private function add_address($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }

        $name = trim($bodyarray['name']);
        $phone = trim($bodyarray['phone']);
        $province = trim($bodyarray['province']);
        $city = trim($bodyarray['city']);
        $area = trim($bodyarray['area']);
        $detail = trim($bodyarray['detail']);
        //常规业务逻辑
        if ($name == '') {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "对不起，收货人姓名不能为空！";
            $this->send_back($return_arr);
        }
        if ($phone == '') {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "对不起，收货人手机号不能为空！";
            $this->send_back($return_arr);
        }
        //php手机号正则校验
        if (!preg_match('/^0?(1|1|1|1|1)[0-9]{10}$/', $phone)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "您输入的手机号格式不正确！";
            $this->send_back($return_arr);
        }
        if ($province == '' || $city == '' || $area == '') {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "对不起，请选择收货人地址！";
            $this->send_back($return_arr);
        }
        if ($province == '请选择' || $city == '请选择' || $area == '请选择') {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "对不起，请选择收货人地址！";
            $this->send_back($return_arr);
        }
        if ($detail == '') {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "对不起，收货人详细地址不能为空！";
            $this->send_back($return_arr);
        }

        $data = [
            'basekeynum' => $clientkeynum,
            'member_id' => $member_id,
            'member_name' => $name,
            'phone' => $phone,
            'detail' => $detail,
            'province' => $province,
            'city' => $city,
            'area' => $area,
            'is_default' => $bodyarray['is_default'],
            'add_time' => date('Y-m-d H:i:s'),
        ];
        try {
            Db::startTrans();
            if ($bodyarray['is_default'] == 1) {
                // 设置其他地址为非默认
                Address::where(['basekeynum' => $clientkeynum, 'member_id' => $member_id])
                    ->update(['is_default' => 2]);
            }
            Address::add($data);

            Db::commit();
            $return_arr['sta'] = "1";
            $return_arr['msg'] = '创建成功';
            $this->send_back($return_arr);
        } catch (\Exception $e) {
            Db::rollback();
            $return_arr['sta'] = "0";
            $return_arr['msg'] = $e->getMessage();
            $this->send_back($return_arr);
        }


    }

    // 收货地址列表
    private function address_list($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }

        $where = [
            'basekeynum' => $clientkeynum,
            'member_id' => $member_id
        ];

        $list = Address::getList($where);

        $return_arr['sta'] = "1";
        $return_arr['msg'] = "请求成功";
        $return_arr['data'] = $list;
        $this->send_back($return_arr);
    }

    // 收货地址设置默认地址
    private function address_set_default($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }
        $address_id = $bodyarray['address_id'];
        if (empty($address_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "收货地址id不可为空!";
            $this->send_back($return_arr);
        }
        $where = [
            'basekeynum' => $clientkeynum,
            'id' => $address_id,
            'member_id' => $member_id
        ];
        $address_info = Address::getInfo($where);
        if (empty($address_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "收货地址不存在!";
            $this->send_back($return_arr);
        }
        try {
            Db::startTrans();
            Address::where(['member_id' => $member_id])->update(['is_default' => 2]);
            Address::where(['member_id' => $member_id, 'id' => $address_id])->update(['is_default' => 1]);
            Db::commit();
            $return_arr['sta'] = "1";
            $return_arr['msg'] = "设置成功!";
            $this->send_back($return_arr);
        }catch (\Exception $e) {
            Db::rollback();
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "设置失败!";
            $this->send_back($return_arr);
        }
    }

    private function del_address($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }

        $address_id = $bodyarray['address_id'];

        if (empty($address_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "收货地址ID不可为空!";
            $this->send_back($return_arr);
        }

        $where = [
            'basekeynum' => $clientkeynum,
            'member_id' => $member_id,
            'id' => $address_id
        ];
        $res = Address::del($where);

        if ($res) {
            $return_arr['sta'] = "1";
            $return_arr['msg'] = "删除成功!";
            $this->send_back($return_arr);
        } else {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "删除失败!";
            $this->send_back($return_arr);
        }
    }

    private function card_use_log($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }
        $cardnum = $bodyarray['cardnum'];
        if (empty($cardnum)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "卡号不可为空!";
            $this->send_back($return_arr);
        }
        $where = [
            'member_id' => $member_id,
            'basekeynum' => $clientkeynum,
            'cardnum' => $cardnum
        ];
        $list = ClientCardUseLog::getList($where);
        $return_arr['sta'] = "1";
        $return_arr['msg'] = "获取成功！";
        $return_arr['data'] = $list;
        $this->send_back($return_arr);
    }

    // 提交订单，插入订单，储值卡扣款,，返回成功单号
    private function add_order($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }
        // 购物车所有勾选的东西
        $checked_cart = Db::table("shopping_cart")->where("member_id='$member_id' and is_check='1'")->select();
        if (empty($checked_cart)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "购物车内容为空!";
            $this->send_back($return_arr);
        }

        // 计算购物车商品总价
        $check_all_price = 0;
        $goodsinfo_str = "";
        foreach ($checked_cart as $key => $v) {
            $goodsinfo = db('goods')->where("id='" . $v['goods_id'] . "' and clientkeynum='$clientkeynum' and status='1'")->find();
            if (empty($goodsinfo)) {
                $return_arr['sta'] = "0";
                $return_arr['msg'] = "对不起，部分商品状态错误，不可下单";
                $this->send_back($return_arr);
            }
            $check_all_price += $goodsinfo['price'] * $v['num'];
            $checked_cart[$key]['goodssku'] = $goodsinfo['goodssku'];
            $checked_cart[$key]['goodsname'] = $goodsinfo['goodsname'];
            $checked_cart[$key]['supplier_id'] = $goodsinfo['supplier_id'];
            $goodsinfo_str .= $goodsinfo['goodsname'] . "(" . $v['num'] . ")" . "+";
        }
        $goodsinfo_str = trim($goodsinfo_str, "+");
        $check_all_price = round($check_all_price, 2);

        $cardnum = $bodyarray["cardnum"];
        $pay_price = $check_all_price;
        // 是否有使用储值卡 使用的话扣除储值卡金额
        if (!empty($cardnum)) {
            $where = [
                'cardnum' => ['in' , $cardnum],
                'is_del' => ['=' , 0],
                'is_bind_member' => ['=', 1],
                'member_id' => ['=', $member_id],
                'clientkeynum' => ['=', $clientkeynum],
            ];
            //查卡号密码信息是否正确
            $cardinfo = Db::table('client_ycard')
                ->where($where)
                ->select();
            if (empty($cardinfo)) {
                $return_arr['sta'] = "0";
                $return_arr['msg'] = "未查询到卡号信息!";
                $this->send_back($return_arr);
            }

            if (count($cardinfo) != count(explode(',', $cardnum))) {
                $return_arr['sta'] = "0";
                $return_arr['msg'] = "使用卡失败，其中有不存在的卡!";
                $this->send_back($return_arr);
            }

            // 检查卡状态
            $card_price = 0;
            $card_update = [];
            foreach ($cardinfo as $v) {
                if ($v['status'] != 3) {
                    $return_arr['sta'] = "0";
                    $return_arr['msg'] = "对不起，卡 " . $v['cardnum'] . " 未开通";
                    $this->send_back($return_arr);
                }
                if ($v['yu_money'] <= 0) {
                    $return_arr['sta'] = "0";
                    $return_arr['msg'] = "对不起，卡 " . $v['cardnum'] . " 剩余额度为0";
                    $this->send_back($return_arr);
                }
                if ($v['yu_exchange_num'] <= 0) {
                    $return_arr['sta'] = "0";
                    $return_arr['msg'] = "对不起，卡 " . $v['cardnum'] . " 剩余兑换次数为0";
                    $this->send_back($return_arr);
                }
                //检查是否过期了
                if (time() < $v['begin_dui']) {
                    $return_arr['sta'] = "0";
                    $return_arr['msg'] = "卡 " . $v['cardnum'] . " 还没到开始兑换时间";
                    $this->send_back($return_arr);
                }
                if (time() > $v['end_dui']) {
                    $return_arr['sta'] = "0";
                    $return_arr['msg'] = "卡 " . $v['cardnum'] . " 已过期";
                    $this->send_back($return_arr);
                }

                // 卡可以正常使用，扣除金额
                if ($pay_price - $v['yu_money'] <= 0) {
                    // 此时，卡内的金额足够完成此订单 结束循环
                    $use_price = $pay_price;
                    $card_price += $pay_price;
                    $card_update[] = [
                        'id' => $v['id'],
                        'cardnum' => $v['cardnum'],
                        'yu_money' => $v['yu_money'] - $pay_price,
                        'after_money' => $v['yu_money'],
                        'use_money' => $use_price
                    ];
                    $pay_price = 0;

                    break;
                } else {
                    $use_price = $v['yu_money'];
                    $pay_price -= $v['yu_money'];
                    $card_price += $v['yu_money'];
                    $card_update[] = [
                        'id' => $v['id'],
                        'cardnum' => $v['cardnum'],
                        'yu_money' => 0,
                        'after_money' => $v['yu_money'],
                        'use_money' => $use_price
                    ];
                }
            }
        }

        $remark = trim($bodyarray['remark']);

        // 查找收货地址
        $address_id = $bodyarray["address_id"];
        if (empty($address_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "收货地址ID不可为空!";
            $this->send_back($return_arr);
        }
        $address_where = [
            'id' => $address_id,
            'basekeynum' => $clientkeynum
        ];
        $address_info = Address::getInfo($address_where);
        if (empty($address_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "收货地址信息不存在!";
            $this->send_back($return_arr);
        }

        $order_keynum = create_guid();
        // 启动事务
        Db::startTrans();
        try {
            $card_total_money = 0;
            if (!empty($card_update)) {
                // 更新卡号表余额
                $card_total_money = array_sum(array_column($card_update,'use_money'));
                foreach ($card_update as $v) {
                    ClientYcardModel::where(['id' => $v['id'],'clientkeynum' => $clientkeynum])
                        ->update(['yu_money' => $v['yu_money']]);
                }
            }
            //订单表
            $order_sn = 'D' . create_order_sn();
            $order_info['order_sn'] = $order_sn;
            $order_info['name'] = $address_info['member_name'];
            $order_info['phone'] = $address_info['phone'];
            $order_info['province'] = $address_info['province'];
            $order_info['city'] = $address_info['city'];
            $order_info['area'] = $address_info['area'];
            $order_info['address'] = $address_info['detail'];
            $order_info['add_time'] = time();
            $order_info['order_status'] = '0';
            $order_info['add_time'] = time();
            $order_info['goodname'] = $goodsinfo_str;
            $order_info['keynum'] = $order_keynum;
            $order_info['clientkeynum'] = $clientkeynum;
            $order_info['goods_total_money'] = $check_all_price;
            $order_info['order_total_money'] = $check_all_price;
            $order_info['card_total_money'] = $card_total_money;
            $order_info['card_money'] = $card_price;
            $order_info['pay_money'] = $pay_price;
            $order_info['pay_status'] = 1;
            $order_info['pay_type'] = 1;
            if ($pay_price > 0) {
                $order_info['pay_status'] = 1;
            } else {
                $order_info['pay_status'] = 2;
            }
            $order_info['remark'] = $remark;
            $order_info['memberid'] = $member_id;
            $order_info['all_address_info_md5'] = md5($address_info['member_name'] . $address_info['phone'] . $address_info['province']
                . $address_info['city'] . $address_info['area'] . $address_info['detail']);
            $order_id = Db::table('client_order_info')->insertGetId($order_info);

            //手动抛出异常，如果insert的sql出错也会把异常抛出到catch里面
            if (!$order_id) {
                throw new \Exception('错误信息:5001,订单表插入失败！');
            }
            // 插入订单详情表
            $add_order_detail_all = array();
            foreach ($checked_cart as $key => $value) {
                $add_order_detail = array();
                $add_order_detail['order_sn'] = $order_sn;
                $add_order_detail['goodsid'] = $value['goods_id'];
                $add_order_detail['goodsku'] = $value['goodssku'];
                $add_order_detail['goodsname'] = $value['goodsname'];
                $add_order_detail['goodsintegral'] = $value['price'];
                $add_order_detail['number'] = $value['num'];
                $add_order_detail['supplier_id'] = $value['supplier_id'];
                $add_order_detail['clientkeynum'] = $clientkeynum;
                $add_order_detail_all[] = $add_order_detail;
            }
            $insert_detail = db('client_order_orderdetail')->insertAll($add_order_detail_all);
            if (!$insert_detail) {
                throw new \Exception('错误信息:5002,订单详情表插入失败！');
            }

            // 增加卡号记录
            if (!empty($card_update)) {
                $add_ycard_log = [];
                foreach ($card_update as $v) {
                    $add_ycard_log[] = [
                        'cardnum' => $v['cardnum'],
                        'actioin' => '消费',
                        'operator' => $member_id,
                        'operator_time' => time(),
                        'content' => "会员下单消费,订单号:" . $order_sn . " 消费前余额:" . $v['after_money']
                            . " 本次消费金额" . $check_all_price . " 消费后余额:" . $v['yu_money'],
                        'clientkeynum' => $clientkeynum
                    ];
                }
                db('client_ycard_log')->insertAll($add_ycard_log);

                foreach ($card_update as $k => $v) {
                    unset($card_update[$k]['id']);
                    $card_update[$k]['order_sn'] = $order_sn;
                    $card_update[$k]['member_id'] = $member_id;
                    $card_update[$k]['basekeynum'] = $clientkeynum;
                    $card_update[$k]['add_time'] = date('Y-m-d H:i:s');
                    if ($order_info['pay_status'] == 2) {
                        $card_update[$k]['status'] = 1;
                    }
                }

                $clientCardUseLogModel = new ClientCardUseLog();
                $clientCardUseLogModel->saveAll($card_update);
            }

            // 删除购物车
            Db::table("shopping_cart")->where("member_id='$member_id' and is_check='1'")->delete();
            //订单日志
            $order_log['order_sn'] = $order_sn;
            $order_log['action_user'] = $member_id;
            $order_log['action_note'] = '接口下单';
            $order_log['add_time'] = time();
            $order_log['clientkeynum'] = $clientkeynum;
            $log_id = Db::table('client_order_log')->insertGetId($order_log);
            if (!$order_id) {
                throw new \Exception('错误信息:5003,新增订单日志失败！');
            }
            // 把会员改为有订单
            $save_member["has_order"] = 1;
            db('client_member')->where("id='$member_id' and clientkeynum='$clientkeynum'")->update($save_member);
            Db::commit();
            $return_arr['sta'] = $order_info['pay_status'];
            $return_arr['msg'] = "下单成功！";
            $return_arr['data']['order_sn'] = $order_sn;
            $this->send_back($return_arr);
        } catch (\Exception $e) {
            // 回滚事务。
            Db::rollback();
            $msg = $e->getMessage();
            logRes('订单提交失败！异常错误信息：' . $msg . ' in line:' . $e->getLine(), 'order');
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "下单失败！";
            $this->send_back($return_arr);
        }
    }

    /**
     * 订单支付
     * @param $request
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function pay_order($request) {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }
        $order_sn = $bodyarray['order_sn'];
        $where = [
            'memberid' => $member_id,
            'order_sn' => $order_sn,
            'clientkeynum' => $clientkeynum
        ];
        $order_info = ClientOrderInfo::getInfo($where);
        if ($order_info['pay_money'] > 0) {
            // 支付
            $wechat_set = PlatWeixinSet::getConfigByBaseKeyNum($clientkeynum);
            $pay_body = $clientkeynum . '-' . '购买商品';
            $pay_detail = $order_info['goodname'];
            $no = "PR" . date('YmdH') . mt_rand(0000000,9999999) . date('is'); // 生成唯一支付单号
            $payment_data = [
                'member_id' => $order_info['memberid'],
                'basekeynum' => $order_info['clientkeynum'],
                'order_sn' => $order_info['order_sn'],
                'pay_no' => $no,
                'add_time' => time(),
                'money' => $order_info['pay_money'],
                'pay_status' => 0
            ];
            PaymentRecord::add($payment_data);

            $pay = new PayV2(
                $wechat_set['appid'],
                $member_info['openid'],
                $wechat_set['mchid'],
                $wechat_set['key'],
                $no,
                $pay_body,
                $order_info['pay_money'] * 100,
                $pay_detail
            );
            $result = $pay->pay();

            $return_arr['sta'] = "1";
            $return_arr['msg'] = "请求成功!";
            $return_arr['data'] = $result;
            $this->send_back($return_arr);

        } else {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "调用支付接口失败!";
            $this->send_back($return_arr);
        }
    }

    private function check_order_status($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }
        $order_sn = $bodyarray['order_sn'];

        $where = [
            'clientkeynum' => $clientkeynum,
            'order_sn' => $order_sn,
            'memberid' => $member_id
        ];
        $order_info = ClientOrderInfo::getInfo($where);
        if (!empty($order_info)) {
            $return_arr['sta'] = "1";
            $return_arr['msg'] = "请求成功!";
            $return_arr['data'] = [
                'status' => $order_info->getData()['pay_status']
            ];
            $this->send_back($return_arr);
        } else {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "订单信息不存在!";
            $this->send_back($return_arr);
        }
    }

    // 订单列表
    private function order_list($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }
        $where = [
            'memberid' => $member_id,
            'clientkeynum' => $clientkeynum
        ];
        // 订单号，商品名，下单时间，状态，数量，金额，商品图片
        $orderlist = db('client_order_info')
            ->where($where)
            ->field('order_sn,add_time,order_status,order_total_money,pay_status,pay_type,pay_money')
            ->order('add_time','desc')
            ->select();
        foreach ($orderlist as $key => $value) {
            $order_detail = db('client_order_orderdetail')->where("order_sn='" . $value['order_sn'] . "'")->select();
            $all_num = 0;
            $goodsinfo_all = array();
            foreach ($order_detail as $k => $v) {
                $all_num += $v['number'];
                $goodsimg = db('goods')->where("id='" . $v['goodsid'] . "'")->value('goodsimg');
                $goodsinfo_one['goodsname'] = $v['goodsname'];
                $goodsinfo_one['goodsimg'] = $goodsimg;
                $goodsinfo_all[] = $goodsinfo_one;
            }
            $orderlist[$key]['all_num'] = $all_num;
            $orderlist[$key]['goodsinfo'] = $goodsinfo_all;
            $orderlist[$key]['add_time'] = date('Y-m-d H:i:s', $value['add_time']);
            $orderlist[$key]['order_status'] = get_order_status($value['order_status']);
        }
        $return_arr['sta'] = "1";
        $return_arr['msg'] = "获取成功！";
        $return_arr['data'] = $orderlist;
        $this->send_back($return_arr);
    }

    // 根据订单号获取订单详情
    private function order_detail($request)
    {
        //平台客户的keynum
        $clientkeynum = $this->clientkeynum;
        $body = $request['body'];
        $bodyarray = json_decode($body, 1);
        $member_id = $bodyarray["member_id"];
        if (empty($member_id)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员ID不可为空!";
            $this->send_back($return_arr);
        }
        $where = "id='$member_id' and clientkeynum='$clientkeynum'";
        $member_info = Db::table('client_member')->where($where)->find();
        if (empty($member_info)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "会员信息不存在!";
            $this->send_back($return_arr);
        }
        $order_sn = $bodyarray["order_sn"];
        if (empty($order_sn)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "订单号不可为空!";
            $this->send_back($return_arr);
        }
        $orderinfo = db('client_order_info')
            ->where("memberid='$member_id' and order_sn='$order_sn' and clientkeynum='$clientkeynum'")
            ->find();
        if (empty($orderinfo)) {
            $return_arr['sta'] = "0";
            $return_arr['msg'] = "订单信息不存在!";
            $this->send_back($return_arr);
        }
        $order_detail = db('client_order_orderdetail')->where("order_sn='" . $orderinfo['order_sn'] . "'")->select();
        $all_num = 0;
        $goodsinfo_all = array();
        foreach ($order_detail as $k => $v) {
            if (empty($v['shipping_name'])) {
                $v['shipping_name'] = "暂无";
            }
            if (empty($v['shipping_num'])) {
                $v['shipping_num'] = "暂无";
            }
            $all_num += $v['number'];
            $goodsimg = db('goods')->where("id='" . $v['goodsid'] . "'")->value('goodsimg');
            $goodsinfo_one['goodsname'] = $v['goodsname'];
            $goodsinfo_one['goodsimg'] = $goodsimg;
            $goodsinfo_one['shipping_name'] = $v['shipping_name'];
            $goodsinfo_one['shipping_num'] = $v['shipping_num'];
            $goodsinfo_one['number'] = $v['number'];
            $goodsinfo_one['goodsintegral'] = $v['goodsintegral'];
            $goodsinfo_one['shipping_time'] = $v['shipping_time'];
            $wuliu_content = db('plat_order_wuliu')
                ->where("orderid='" . $v['id'] . "' and express='" . $v['shipping_name'] . "' and expressnum='" . $v['shipping_num'] . "' and clientkeynum='$clientkeynum'")
                ->value("wuliu_content");
            $goodsinfo_one['wuliu_content'] = json_decode($wuliu_content, 1);
            $goodsinfo_all[] = $goodsinfo_one;
        }
        $orderinfo['all_num'] = $all_num;
        $orderinfo['goodsinfo'] = $goodsinfo_all;
        $orderinfo['add_time'] = date('Y-m-d H:i:s', $orderinfo['add_time']);
        $orderinfo['order_status'] = get_order_status($orderinfo['order_status']);
        $card_info_where = [
            'member_id' => $member_id,
            'basekeynum' => $clientkeynum,
            'order_sn' => $orderinfo['order_sn']
        ];
        $orderinfo['card_info'] = ClientCardUseLog::getList($card_info_where);
        $return_arr['sta'] = "1";
        $return_arr['msg'] = "获取成功！";
        $return_arr['data'] = $orderinfo;
        $this->send_back($return_arr);
    }

}
