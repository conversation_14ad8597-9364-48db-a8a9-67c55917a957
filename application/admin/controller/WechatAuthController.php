<?php
namespace app\admin\controller;

use think\Controller;
use think\Request;
use think\Db;
use think\facade\Session;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Env;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelLow;
use Endroid\QrCode\RoundBlockSizeMode\RoundBlockSizeModeMargin;

/**
 * 微信网页授权登录控制器
 * 用于后台管理系统的微信扫码登录功能
 */
class WechatAuthController extends Controller
{
    /**
     * 发起微信网页授权
     * 生成微信授权链接，重定向到微信授权页面
     */
    public function authorize()
    {
        try {
            $redirect_url = input('redirect_url', ''); // 登录成功后的跳转地址
            
            // 获取微信开放平台配置
            $config = $this->getWechatConfig();
            if (!$config) {
                return $this->silentRedirect(url('admin/login/index'), '微信登录功能未配置或已关闭');
            }
            
            // 生成state参数，用于防CSRF攻击和携带回调信息
            $state = $this->generateState($redirect_url);
            
            // 构建微信授权URL
            $auth_url = $this->buildAuthUrl($config['wechat_open_appid'], $state);
            
            // 记录授权发起日志
            $this->recordLog(null, null, 'authorize', '发起微信授权', [
                'redirect_url' => $redirect_url,
                'state' => $state
            ]);
            
            // 跳转到微信授权页面
            return redirect($auth_url);
            
        } catch (\Exception $e) {
            Log::error('微信授权发起失败: ' . $e->getMessage());
            return $this->silentRedirect(url('admin/login/index'), '微信授权发起失败，请重试');
        }
    }
    
    /**
     * 微信授权回调处理
     * 接收微信回调，处理用户登录或绑定流程
     */
    public function callback()
    {
        try {
            $code = input('code');
            $state = input('state');
            
            if (empty($code)) {
                // 用户拒绝授权，静默重定向到登录页
                return $this->silentRedirect(url('admin/login/index'), '用户取消了微信授权');
            }
            
            // 验证state参数
            $state_data = $this->validateState($state);
            if (!$state_data) {
                return $this->showErrorWithRedirect('授权状态验证失败，请重新授权', url('admin/login/index'), 3);
            }
            
            // 获取微信配置
            $config = $this->getWechatConfig();
            if (!$config) {
                return $this->showErrorWithRedirect('微信登录功能配置异常', url('admin/login/index'), 3);
            }

            
            // 通过code换取access_token和用户信息
            $token_data = $this->getAccessToken($config, $code);
            if (!$token_data) {
                return $this->showErrorWithRedirect('微信授权信息获取失败，请重试', url('admin/login/index'), 3);
            }
            
            $openid = $token_data['openid'];
            $unionid = $token_data['unionid'] ?? '';
            
            // 获取用户详细信息（如果scope包含userinfo）
            $user_info = null;
            if (isset($token_data['scope']) && strpos($token_data['scope'], 'snsapi_userinfo') !== false) {
                $user_info = $this->getUserInfo($token_data['access_token'], $openid);
            }
            
            // 保存授权状态到临时表
            $this->saveAuthState($state, $openid, $unionid, $user_info, $state_data['redirect_url']);
            
            // 检查是否已绑定账号
            $bound_account = $this->checkAccountBinding($openid, $unionid);
            
            // 检查是否是二维码登录
            $is_qrcode = isset($state_data['is_qrcode']) && $state_data['is_qrcode'] == 1;
            
            if ($bound_account) {
                // 已绑定，直接登录
                if ($is_qrcode) {
                    // 二维码登录，标记登录成功
                    return $this->processQrcodeLogin($bound_account, $openid, $state, $state_data['redirect_url']);
                } else {
                    // 普通授权登录
                    return $this->processDirectLogin($bound_account, $openid, $state_data['redirect_url']);
                }
            } else {
                // 未绑定，统一跳转到绑定页面
                return $this->redirectToBinding($state);
            }
            
        } catch (\Exception $e) {
            Log::error('微信授权回调失败: ' . $e->getMessage());
            return $this->showErrorWithRedirect('授权处理异常，请重试', url('admin/login/index'), 3);
        }
    }
    
    /**
     * 显示账号绑定页面
     */
    public function bind()
    {
        $state = input('state', '');
        if (empty($state)) {
            return $this->silentRedirect(url('admin/login/index'), '参数错误');
        }
        
        // 验证state并获取用户信息
        $auth_state = $this->getAuthStateByState($state);
        if (!$auth_state) {
            return $this->showErrorWithRedirect('授权状态已过期，请重新授权', url('admin/login/index'), 3);
        }
        
        $user_info = json_decode($auth_state['user_info'], true);
        
        // 显示绑定页面
        $this->assign('state', $state);
        $this->assign('user_info', $user_info);
        $this->assign('nickname', $user_info['nickname'] ?? '微信用户');
        $this->assign('avatar', $user_info['headimgurl'] ?? '');
        
        return $this->fetch('wechat_bind');
    }
    
    /**
     * 处理账号绑定
     */
    public function doBind()
    {
        try {
            $state = input('state', '');
            $accountname = input('accountname', '');
            $password = input('password', '');
            
            if (empty($state) || empty($accountname) || empty($password)) {
                return json(['code' => 400, 'msg' => '参数不完整']);
            }
            
            // 获取授权状态
            $auth_state = $this->getAuthStateByState($state);
            if (!$auth_state) {
                return json(['code' => 400, 'msg' => '授权状态已过期，请重新授权']);
            }
            
            // 验证账号密码
            $account = $this->validateAccount($accountname, $password);
            if (!$account) {
                return json(['code' => 400, 'msg' => '账号或密码错误']);
            }
            
            // 检查该账号是否已绑定其他微信
            $existing_bind = Db::table('plat_third_auth')
                ->where('account_id', $account['account_id'])
                ->where('auth_type', 'wechat')
                ->where('status', 1)
                ->find();
            
            if ($existing_bind) {
                return json(['code' => 400, 'msg' => '该账号已绑定其他微信，请先解绑']);
            }
            
            // 开启事务
            Db::startTrans();
            try {
                // 创建绑定记录
                $user_info = json_decode($auth_state['user_info'], true);
                $bind_data = [
                    'account_id' => $account['account_id'],
                    'auth_type' => 'wechat',
                    'third_openid' => $auth_state['openid'],
                    'third_unionid' => $auth_state['unionid'],
                    'third_nickname' => $user_info['nickname'] ?? '',
                    'third_avatar' => $user_info['headimgurl'] ?? '',
                    'auth_info' => $auth_state['user_info'],
                    'bind_time' => date('Y-m-d H:i:s'),
                    'status' => 1
                ];
                
                Db::table('plat_third_auth')->insert($bind_data);
                
                // 标记授权状态为已使用
                Db::table('plat_wechat_auth_state')
                    ->where('state', $state)
                    ->update(['is_used' => 1]);
                
                // 记录绑定日志
                $this->recordLog($account['account_id'], $auth_state['openid'], 'bind', '绑定成功', [
                    'account_name' => $account['accountname']
                ]);
                
                Db::commit();
                
                // 执行登录
                $this->doLogin($account);
                
                return json([
                    'code' => 200, 
                    'msg' => '绑定成功',
                    'data' => [
                        'redirect_url' => $auth_state['redirect_url'] ?: url('admin/index/index')
                    ]
                ]);
                
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
            
        } catch (\Exception $e) {
            Log::error('微信账号绑定失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '绑定失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 解绑微信
     */
    public function unbind()
    {
        try {
            $account_id = Session::get('cn_accountinfo.account_id');
            if (!$account_id) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }
            
            // 查找绑定记录
            $bind_record = Db::table('plat_third_auth')
                ->where('account_id', $account_id)
                ->where('auth_type', 'wechat')
                ->where('status', 1)
                ->find();
            
            if (!$bind_record) {
                return json(['code' => 400, 'msg' => '当前账号未绑定微信']);
            }
            
            // 更新绑定状态
            Db::table('plat_third_auth')
                ->where('id', $bind_record['id'])
                ->update(['status' => 0, 'update_time' => date('Y-m-d H:i:s')]);
            
            // 记录解绑日志
            $this->recordLog($account_id, $bind_record['third_openid'], 'unbind', '解绑成功');
            
            return json(['code' => 200, 'msg' => '解绑成功']);
            
        } catch (\Exception $e) {
            Log::error('微信解绑失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '解绑失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取微信开放平台配置
     */
    private function getWechatConfig()
    {
        $config = Db::table('plat_system_set')->where('id', 1)->find();
        
        if (!$config || 
            empty($config['wechat_open_appid']) || 
            empty($config['wechat_open_app_secret']) ||
            $config['wechat_login_enabled'] != 1) {
            return false;
        }
        
        return $config;
    }
    
    /**
     * 生成state参数
     */
    private function generateState($redirect_url = '')
    {
        $state = md5(uniqid() . time() . rand(1000, 9999));
        
        // 将state信息保存到临时表
        $expire_time = date('Y-m-d H:i:s', time() + 600); // 10分钟有效期
        
        Db::table('plat_wechat_auth_state')->insert([
            'state' => $state,
            'redirect_url' => $redirect_url,
            'expire_time' => $expire_time,
            'is_used' => 0
        ]);
        
        return $state;
    }
    
    /**
     * 构建微信授权URL
     */
    private function buildAuthUrl($appid, $state)
    {
        $redirect_uri = urlencode($_SERVER['HTTP_HOST'] . '/admin.php/wechat_auth/callback');
        
        return "https://open.weixin.qq.com/connect/qrconnect" .
               "?appid=" . $appid .
               "&redirect_uri=" . $redirect_uri .
               "&response_type=code" .
               "&scope=snsapi_login" .
               "&state=" . $state .
               "#wechat_redirect";
    }
    
    /**
     * 验证state参数
     */
    private function validateState($state)
    {
        if (empty($state)) {
            return false;
        }
        
        $state_record = Db::table('plat_wechat_auth_state')
            ->where('state', $state)
            ->where('is_used', 0)
            ->where('expire_time', '>', date('Y-m-d H:i:s'))
            ->find();
        
        return $state_record;
    }
    
    /**
     * 通过code换取access_token
     */
    private function getAccessToken($config, $code)
    {
        $url = "https://api.weixin.qq.com/sns/oauth2/access_token" .
               "?appid=" . $config['wechat_open_appid'] .
               "&secret=" . $config['wechat_open_app_secret'] .
               "&code=" . $code .
               "&grant_type=authorization_code";
        
        $result = $this->httpGet($url);
        $data = json_decode($result, true);
        
        if (isset($data['errcode'])) {
            Log::error('获取微信access_token失败: ' . $data['errmsg']);
            return false;
        }
        
        return $data;
    }
    
    /**
     * 获取用户详细信息
     */
    private function getUserInfo($access_token, $openid)
    {
        $url = "https://api.weixin.qq.com/sns/userinfo" .
               "?access_token=" . $access_token .
               "&openid=" . $openid .
               "&lang=zh_CN";
        
        $result = $this->httpGet($url);
        $data = json_decode($result, true);
        
        if (isset($data['errcode'])) {
            Log::error('获取微信用户信息失败: ' . $data['errmsg']);
            return null;
        }
        
        return $data;
    }
    
    /**
     * 保存授权状态
     */
    private function saveAuthState($state, $openid, $unionid, $user_info, $redirect_url)
    {
        Db::table('plat_wechat_auth_state')
            ->where('state', $state)
            ->update([
                'openid' => $openid,
                'unionid' => $unionid,
                'user_info' => json_encode($user_info),
                'redirect_url' => $redirect_url
            ]);
    }
    
    /**
     * 检查账号绑定情况
     */
    private function checkAccountBinding($openid, $unionid = '')
    {
        // 先通过openid查找
        $bind_record = Db::table('plat_third_auth')
            ->where('third_openid', $openid)
            ->where('auth_type', 'wechat')
            ->where('status', 1)
            ->find();
        
        if ($bind_record) {
            // 获取账号信息
            $account = Db::table('plat_account')
                ->where('account_id', $bind_record['account_id'])
                ->where('isdel', 0)
                ->find();
            
            return $account;
        }
        
        // 如果有unionid，通过unionid查找
        if ($unionid) {
            $bind_record = Db::table('plat_third_auth')
                ->where('third_unionid', $unionid)
                ->where('auth_type', 'wechat')
                ->where('status', 1)
                ->find();
            
            if ($bind_record) {
                $account = Db::table('plat_account')
                    ->where('account_id', $bind_record['account_id'])
                    ->where('isdel', 0)
                    ->find();
                
                return $account;
            }
        }
        
        return false;
    }
    
    /**
     * 处理直接登录
     */
    private function processDirectLogin($account, $openid, $redirect_url = '')
    {
        // 执行登录
        $this->doLogin($account);
        
        // 更新最后登录时间
        Db::table('plat_third_auth')
            ->where('third_openid', $openid)
            ->where('auth_type', 'wechat')
            ->update(['last_login_time' => date('Y-m-d H:i:s')]);
        
        // 记录登录日志
        $this->recordLog($account['account_id'], $openid, 'login', '微信登录成功');
        
        // 跳转
        $redirect_url = $redirect_url ?: url('admin/index/index');
        return redirect($redirect_url);
    }
    
    /**
     * 跳转到绑定页面
     */
    private function redirectToBinding($state)
    {
        return redirect(url('admin/wechat_auth/bind', ['state' => $state]));
    }
    
    /**
     * 根据state获取授权状态
     */
    private function getAuthStateByState($state)
    {
        return Db::table('plat_wechat_auth_state')
            ->where('state', $state)
            ->where('is_used', 0)
            ->where('expire_time', '>', date('Y-m-d H:i:s'))
            ->find();
    }
    
    /**
     * 验证账号密码
     */
    private function validateAccount($accountname, $password)
    {
        $account = Db::table('plat_account')
            ->where('accountname', $accountname)
            ->where('isdel', 0)
            ->find();
        
        if (!$account) {
            return false;
        }
        
        // 验证密码（这里需要根据实际的密码加密方式来验证）
        if (md5($password) !== $account['accountpassword']) {
            return false;
        }
        
        return $account;
    }
    
    /**
     * 执行登录
     */
    private function doLogin($account)
    {
        // 参考LoginController中的登录逻辑，保持一致性

        Log::info("微信登录账号信息: " . json_encode($account));
        
        // 清空该账号的登录失败次数
        Db::table("plat_account")
            ->where("accountname", $account['accountname'])
            ->update(['cn_try_count' => 0]);
        
        // 清空登录失败ip记录
        $attack_ip = $_SERVER['REMOTE_ADDR'];
        Db::table('plat_attack_log')->where("attack_ip", $attack_ip)->delete();
        
        // 设置session（与LoginController保持一致）
        session("cn_login_flag", 'wechat'); // 标识为微信登录
        session("cn_accountinfo", $account);
        
        // 记录登录日志（使用系统现有的addloginlog函数）
        addloginlog("微信登录成功", $account['accountname']);
    }
    
    /**
     * 记录操作日志
     */
    private function recordLog($account_id, $openid, $action, $result, $extra_data = [])
    {
        $data = [
            'account_id' => $account_id,
            'openid' => $openid,
            'action' => $action,
            'login_ip' => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
            'is_success' => 1,
            'error_msg' => $result,
            'extra_data' => json_encode($extra_data)
        ];
        
        Db::table('plat_wechat_login_log')->insert($data);
    }
    
    /**
     * HTTP GET请求
     */
    private function httpGet($url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }

    /**
     * 获取微信登录二维码
     * 用于在登录页面直接显示二维码
     */
    public function getQrcode()
    {
        try {
            // 获取微信开放平台配置
            $config = $this->getWechatConfig();
            if (!$config) {
                return json(['code' => 400, 'msg' => '微信登录功能未配置或已关闭']);
            }
            
            $redirect_url = input('redirect_url', url('admin/index/index'));
            
            // 生成唯一的state参数
            $state = $this->generateQrcodeState($redirect_url);
            
            // 构建微信授权URL
            $auth_url = $this->buildAuthUrl($config['wechat_open_appid'], $state);
            
            // 生成二维码图片（仅base64，不保存文件）
            $qrcode_result = $this->generateQrcodeImage($auth_url);
            
            // 记录二维码生成日志
            $this->recordLog(null, null, 'qrcode_generate', '生成登录二维码', [
                'state' => $state,
                'redirect_url' => $redirect_url
            ]);
            
            return json([
                'code' => 200,
                'msg' => '成功',
                'data' => [
                    'state' => $state,
                    'qrcode_url' => $auth_url,
                    'qrcode_base64' => $qrcode_result['base64'],
                    'expire_time' => 300 // 5分钟过期
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('生成微信登录二维码失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '生成二维码失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 检查二维码扫描状态
     * 前端轮询此接口检查是否已扫码登录
     */
    public function checkQrcodeStatus()
    {
        try {
            $state = input('state', '');
            if (empty($state)) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }
            
            // 检查授权状态
            $auth_state = Db::table('plat_wechat_auth_state')
                ->where('state', $state)
                ->where('expire_time', '>', date('Y-m-d H:i:s'))
                ->find();
            
            if (!$auth_state) {
                return json(['code' => 400, 'msg' => '二维码已过期', 'status' => 'expired']);
            }
            
            if ($auth_state['is_used']) {
                // 已扫码，检查是否已绑定账号并登录成功
                if (isset($auth_state['login_success']) && $auth_state['login_success']) {
                    return json([
                        'code' => 200,
                        'msg' => '登录成功',
                        'status' => 'success',
                        'data' => [
                            'redirect_url' => $auth_state['redirect_url'] ?: url('admin/index/index')
                        ]
                    ]);
                } else {
                    // 需要绑定账号或正在处理绑定
                    return json([
                        'code' => 200,
                        'msg' => '需要绑定账号',
                        'status' => 'need_bind',
                        'data' => [
                            'bind_url' => url('admin/wechat_auth/bind', ['state' => $state])
                        ]
                    ]);
                }
            }
            
            // 等待扫码
            return json(['code' => 200, 'msg' => '等待扫码', 'status' => 'waiting']);
            
        } catch (\Exception $e) {
            Log::error('检查二维码状态失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '检查状态失败']);
        }
    }

    /**
     * 生成二维码专用的state参数
     */
    private function generateQrcodeState($redirect_url = '')
    {
        $state = 'qr_' . md5(uniqid() . time() . rand(1000, 9999));
        
        // 将state信息保存到临时表，二维码5分钟过期
        $expire_time = date('Y-m-d H:i:s', time() + 300); // 5分钟有效期
        
        Db::table('plat_wechat_auth_state')->insert([
            'state' => $state,
            'redirect_url' => $redirect_url,
            'expire_time' => $expire_time,
            'is_used' => 0,
            'login_success' => 0,
            'is_qrcode' => 1
        ]);
        
        return $state;
    }

    /**
     * 处理二维码登录成功后的绑定和登录
     */
    private function processQrcodeLogin($account, $openid, $state, $redirect_url)
    {

        // 执行登录
        $this->doLogin($account);

        // 标记授权状态为已使用
        Db::table('plat_wechat_auth_state')
            ->where('state', $state)
            ->update(['is_used' => 1, 'login_success' => 1]);

        // 记录登录日志
        $this->recordLog($account['account_id'], $openid, 'qrcode_login', '二维码登录成功');

        // 跳转
        $redirect_url = $redirect_url ?: url('admin/index/index');
        return redirect($redirect_url);
    }



    /**
     * 生成二维码图片（仅返回base64，不保存文件）
     * @param string $content 二维码内容（微信授权URL）
     * @return array 返回二维码base64数据
     */
    private function generateQrcodeImage($content)
    {
        try {
            $writer = new PngWriter();
            
            // 创建QR码
            $qrCode = QrCode::create($content)
                ->setEncoding(new Encoding('UTF-8'))
                ->setErrorCorrectionLevel(new ErrorCorrectionLevelLow())
                ->setSize(200)
                ->setMargin(10)
                ->setRoundBlockSizeMode(new RoundBlockSizeModeMargin())
                ->setForegroundColor(new Color(0, 0, 0))
                ->setBackgroundColor(new Color(255, 255, 255));
            
            $result = $writer->write($qrCode);
            
            // 直接获取二维码的base64编码，不保存文件
            $base64 = 'data:image/png;base64,' . base64_encode($result->getString());
            
            return [
                'base64' => $base64
            ];
            
        } catch (\Exception $e) {
            Log::error('生成二维码图片失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取微信登录配置信息
     * 供前端使用微信官方内嵌登录
     */
    public function getWechatLoginConfig()
    {
        try {
            $config = $this->getWechatConfig();
            if (!$config) {
                return json(['code' => 400, 'msg' => '微信登录功能未配置或已关闭']);
            }
            
            // 获取重定向地址
            $redirect_url = input('redirect_url', url('admin/index/index'));
            
            // 生成state参数（使用与authorize方法相同的逻辑）
            $state = $this->generateQrcodeState($redirect_url);
            
            // 构建回调地址
            $protocol = request()->isSsl() ? 'https' : 'http';
            $host = request()->host();
            $callback_url = $protocol . '://' . $_SERVER['HTTP_HOST'] . url('admin/wechat_auth/callback');
            
            // 返回前端需要的完整配置信息
            return json([
                'code' => 200,
                'data' => [
                    'appid' => $config['wechat_open_appid'],
                    'redirect_uri' => urlencode($callback_url),
                    'scope' => 'snsapi_login',
                    'response_type' => 'code',
                    'state' => $state // 返回后端生成的state
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取微信登录配置失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败']);
        }
    }

    /**
     * 显示友好错误页面并自动跳转
     * @param string $message 错误信息
     * @param string $redirect_url 跳转地址
     * @param int $wait_seconds 等待秒数
     */
    private function showErrorWithRedirect($message, $redirect_url, $wait_seconds = 3)
    {
        // 将错误信息存储到session，供登录页面显示
        session('wechat_error_msg', $message);
        
        // 构建自动跳转的HTML页面
        $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>微信授权</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            background: white;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 90%;
        }
        .error-icon {
            font-size: 60px;
            color: #ff6b6b;
            margin-bottom: 20px;
        }
        .error-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 15px;
            font-weight: 600;
        }
        .error-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .countdown {
            font-size: 14px;
            color: #999;
        }
        .btn-return {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            margin-top: 15px;
        }
        .btn-return:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <div class="error-title">微信授权异常</div>
        <div class="error-message">' . htmlspecialchars($message) . '</div>
        <div class="countdown">
            <span id="countdown">' . $wait_seconds . '</span> 秒后自动返回登录页面
        </div>
        <a href="' . $redirect_url . '" class="btn-return">立即返回</a>
    </div>
    
    <script>
        let countdown = ' . $wait_seconds . ';
        const countdownElement = document.getElementById("countdown");
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = "' . $redirect_url . '";
            }
        }, 1000);
    </script>
</body>
</html>';
        
        echo $html;
        exit;
    }
    
    /**
     * 静默重定向（不显示任何错误信息）
     * @param string $redirect_url 跳转地址
     * @param string $error_msg 可选的错误信息，存储到session
     */
    private function silentRedirect($redirect_url, $error_msg = '')
    {
        if ($error_msg) {
            session('wechat_error_msg', $error_msg);
        }
        
        return redirect($redirect_url);
    }
} 