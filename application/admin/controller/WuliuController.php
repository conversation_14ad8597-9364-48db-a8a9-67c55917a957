<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright ( c ) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use think\facade\Session;
use think\facade\Request;
use think\Db;
use think\Tree;
use think\facade\Cache;

class WuliuController extends CnController
{

    /**
     * 初始化方法
     */

    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }


    public function orderlist()
    {
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = session('cn_accountinfo.basekeynum');
        $status = $param["status"];
        $this->assign("status", $status);
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/orderlist?status=' . $status, 0);
        return $this->fetch('orderlist');
    }
    public function ajax_get_orderlist()
    {

        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();
        $param = $request->param();
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        //计算记录偏移量
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $where = "1=1  and  clientkeynum='$basekeynum'";
        if ($keyword) {
            $where .= " and $field like '%$keyword%'";
        }
        $status = $param["status"];
        if (!empty($status)) {
            $where .= " and status='$status'";
        }
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/orderlist?status=' . $status, 1);
        $list = Db::table('plat_order_wuliu')->where($where)->order("create_time desc")->limit($offset . ',' . $pagesize)->select();
        $count = Db::table('plat_order_wuliu')->where($where)->count();
        foreach ($list as $key => &$value) {
            $order_info = db('v_client_order_orderdetail')->where("id='" . $value["orderid"] . "' and clientkeynum='$basekeynum'")->find();
            $value["goodsname"] = $order_info["goodsname"];
            $value["name"] = $order_info["name"];
            $value["phone"] = $order_info["phone"];
            $value["address"] = $order_info["province"] . $order_info["city"] . $order_info["area"] . $order_info["address"];
            $value["order_sn"] = $order_info["order_sn"];
        }
        $rtdata['data'] = $list;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        echo json_encode($rtdata);
        die;
    }

    public function ajax_dingyue()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();
        $param = $request->param();
        $alldata = json_decode($param["alldata"], 1);
        foreach ($alldata as $key => $value) {
            wuliu_dingyue($value["orderid"], $basekeynum);
        }
        $msg['sta'] = '1';
        $msg['msg'] = "操作成功";
        echo json_encode($msg);
        die;
    }
}
