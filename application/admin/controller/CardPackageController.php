<?php
namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\Request;
use think\Collection;

/**
 * 套餐卡套餐管理控制器
 */
class CardPackageController extends Controller
{
    /**
     * 套餐列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $where = [];
            $name = input('name', '');
            $status = input('status', '');
            
            if ($name) {
                $where[] = ['name', 'like', "%{$name}%"];
            }
            
            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }
            
            $list = model('CardPackage')
                ->where($where)
                ->order('id', 'desc')
                ->paginate(input('limit', 15));
            
            // 为每个套餐获取商品规格信息
            $data = $list->items();
            foreach ($data as &$item) {
                // 获取套餐关联的商品规格
                $productSpecs = Db::name('card_package_product')->alias('a')
                    ->join('product_inventory b', 'a.product_inventory_id = b.id')
                    ->join('products c', 'b.product_id = c.id')
                    ->field('c.title as product_name, b.title as spec_values, a.quantity')
                    ->where('a.package_id', $item['id'])
                    ->order('a.sort', 'asc')
                    ->select();
                
                // 格式化商品规格信息，多个商品规格用换行分隔
                $specsArray = [];
                foreach ($productSpecs as $spec) {
                    $specsArray[] = $spec['product_name'] . '(' . $spec['spec_values'] . ') x' . $spec['quantity'];
                }
                $item['product_specs'] = implode("\n", $specsArray);
            }
                
            return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $data]);
        }
        
        return $this->fetch();
    }
    
    /**
     * 添加套餐
     */
    public function add()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        if (request()->isAjax()) {
            $data = input('post.');
            $model = new \app\admin\model\CardPackage;
            
            // 开启事务
            $model->startTrans();
            try {
                // 新增套餐
                $model->save([
                    'clientkeynum' => $clientkeynum,
                    'name' => $data['name'],
                    'price' => $data['price'],
                    // 'validity_days' => $data['validity_days'],
                    'description' => $data['description'],
                    'status' => $data['status'],
                    'add_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ]);
                $packageId = $model->id;
                
                // 处理关联商品
                if (isset($data['products']) && !empty($data['products'])) {
                    $productData = [];
                    foreach ($data['products'] as $product) {
                        $productData[] = [
                            'clientkeynum' => $clientkeynum,
                            'package_id' => $packageId,
                            'product_inventory_id' => $product['product_inventory_id'],
                            'quantity' => $product['quantity'],
                            'sort' => $product['sort'],
                            'add_time' => date('Y-m-d H:i:s'),
                            'update_time' => date('Y-m-d H:i:s')
                        ];
                    }
                    model('CardPackageProduct')->saveAll($productData);
                }
                
                $model->commit();
                return json(['code' => 0, 'msg' => '添加成功']);
            } catch (\Exception $e) {
                $model->rollback();
                return json(['code' => 1, 'msg' => '添加失败：' . $e->getMessage()]);
            }
        }
        
        // 获取所有商品
        $products = model('ProductInventory')
            ->alias('a')
            ->join('products b', 'a.product_id = b.id')
            ->field('a.id, b.title as name, a.title as spec_values')
            ->where('b.state', 1)
            ->select();
            
        $this->assign('products', $products);
        
        return $this->fetch();
    }
    
    /**
     * 编辑套餐
     */
    public function edit($id)
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        // 处理表单提交
        if (request()->isAjax()) {
            $data = input('post.');
            $model = new \app\admin\model\CardPackage;
            
            // 开启事务
            $model->startTrans();
            try {
                // 更新套餐
                $model->where('id', $data['id'])->update([
                    'name' => $data['name'],
                    'price' => $data['price'],
                    // 'validity_days' => $data['validity_days'],
                    'description' => $data['description'],
                    'status' => $data['status'],
                    'update_time' => date('Y-m-d H:i:s')
                ]);
                
                // 处理关联商品
                if (isset($data['products'])) {
                    // 先删除原有关联
                    model('CardPackageProduct')->where('package_id', $data['id'])->delete();
                    
                    // 添加新关联
                    if (!empty($data['products'])) {
                        $productData = [];
                        foreach ($data['products'] as $product) {
                            $productData[] = [
                                'clientkeynum' => $clientkeynum,
                                'package_id' => $data['id'],
                                'product_inventory_id' => $product['product_inventory_id'],
                                'quantity' => $product['quantity'],
                                'sort' => $product['sort'],
                                'add_time' => date('Y-m-d H:i:s'),
                                'update_time' => date('Y-m-d H:i:s')
                            ];
                        }
                        model('CardPackageProduct')->saveAll($productData);
                    }
                }
                
                $model->commit();
                return json(['code' => 0, 'msg' => '更新成功']);
            } catch (\Exception $e) {
                $model->rollback();
                return json(['code' => 1, 'msg' => '更新失败：' . $e->getMessage()]);
            }
        }
        
        // 清除模板缓存
        \think\facade\View::clear();
        
        // 获取套餐信息 - 不使用clientkeynum过滤，避免找不到数据
        $info = Db::name('card_package')->where('id', $id)->find();
        if (!$info) {
            $this->error('套餐不存在');
        }
        
        // 获取关联商品 - 不使用clientkeynum过滤，避免找不到数据
        $packageProducts = Db::name('card_package_product')->alias('a')
            ->join('product_inventory b', 'a.product_inventory_id = b.id')
            ->join('products c', 'b.product_id = c.id')
            ->field('a.product_inventory_id, a.quantity, c.title as name, b.title as spec_values')
            ->where('a.package_id', $id)
            ->select();
        
        
        // 获取所有商品
        $products = Db::name('product_inventory')->alias('a')
            ->join('products b', 'a.product_id = b.id')
            ->field('a.id, b.title as name, a.title as spec_values')
            ->where('b.state', 1)
            ->select();
        
        // 将数据直接赋值给模板
        $this->assign([
            'info' => $info,
            'packageProducts' => $packageProducts,
            'products' => $products
        ]);
        
        // 使用编辑专用模板
        return $this->fetch('edit');
    }
    
    /**
     * 删除套餐
     */
    public function delete()
    {
        $id = input('id');
        
        // 检查是否有关联的卡型
        $relationCount = model('CardTypePackageRelation')->where('package_id', $id)->count();
        if ($relationCount > 0) {
            return json(['code' => 1, 'msg' => '该套餐已关联卡型，不能删除']);
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 删除套餐
            model('CardPackage')->where('id', $id)->delete();
            
            // 删除关联商品
            model('CardPackageProduct')->where('package_id', $id)->delete();
            
            Db::commit();
            return json(['code' => 0, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 更新套餐状态
     */
    public function updateStatus()
    {
        $id = input('id');
        $status = input('status');
        
        $result = model('CardPackage')
            ->where('id', $id)
            ->update([
                'status' => $status,
                'update_time' => date('Y-m-d H:i:s')
            ]);
            
        if ($result) {
            return json(['code' => 0, 'msg' => '状态更新成功']);
        } else {
            return json(['code' => 1, 'msg' => '状态更新失败']);
        }
    }
    
    /**
     * 获取商品列表
     */
    public function getProducts()
    {
        $keyword = input('keyword', '');
        
        $where = [];
        if ($keyword) {
            $where[] = ['b.title', 'like', "%{$keyword}%"];
        }
        
        $products = model('ProductInventory')
            ->alias('a')
            ->join('products b', 'a.product_id = b.id')
            ->field('a.id, b.title as name, a.title as spec_values')
            // ->where('a.status', 1)
            ->where($where)
            ->select();
            
        return json(['code' => 0, 'msg' => 'success', 'data' => $products]);
    }
} 