<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright ( c ) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use app\admin\model\ClientCardUseLog;
use app\admin\model\ClientMember;
use app\admin\model\ClientOrderDetail;
use app\admin\model\ClientOrderInfo;
use app\admin\model\Order;
use app\admin\model\OrderDetail;
use app\admin\model\Shop;
use app\api\model\OrderLog;
use think\facade\Request;
use Spatie\SimpleExcel\SimpleExcelWriter;

class OrderController extends CnController
{
    /**
     * 初始化方法
     */

    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    public function order_logs()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/order_list', 0);
        $id = Request()->param('id');
        if (empty($id)) error_tips('参数错误');
        $this->assign('id', $id);
        return $this->fetch();

    }

    public function ajax_order_logs()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/order_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = Request()->param('id');

        $order = Order::where(['clientkeynum' => $basekeynum, 'id' => $id])->find();
        if (empty($order)) fail(-1 ,'订单不存在');

        $page = Request()->param('page', 1);
        $limit = Request()->param('limit', 10);

        $where = ['clientkeynum' => $basekeynum, 'order_no' => $order['order_no']];

        $list = OrderLog::where($where)->page($page, $limit)->order('id', 'desc')->select();
        $count = OrderLog::where($where)->count();

        success(0, '请求成功', $list, $count);

    }

    public function order_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/order_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $shop_enum = Shop::where(['clientkeynum' => $basekeynum])->field('id, title')->select();;
        $this->assign('shop_enum', $shop_enum);
        $member_id = Request::instance()->param('memberid');
        $this->assign('member_id', $member_id);
        return $this->fetch();
    }

    public function ajax_order_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/order_list', 1);
        $page = Request::instance()->request('page', 1);
        $limit = Request::instance()->request('limit', 10);
        $type = Request::instance()->param('type1');
        $basekeynum = session('cn_accountinfo.basekeynum');
        $where = ['clientkeynum' => $basekeynum];

        $field = Request::instance()->param('field');
        $keyword = Request::instance()->param('keyword');
        $is_pay = Request::instance()->param('is_pay');
        $member_id = Request::instance()->param('member_id');
        $shop_id = Request::instance()->param('shop_id');
        $start_date = Request::instance()->param('start_date');
        $end_date = Request::instance()->param('end_date');

//        if (!empty($field) && !empty($keyword)) {
//            $where[] = [$field, 'like', "%$keyword%"];
//        }

//        if (!empty($is_pay) && in_array($is_pay, [1, 2])) {
//            $where['pay_status'] = ['=', $is_pay];
//        }

        $model = Order::where($where)->where('status', '>', 0)->order('id', 'desc');

        if (!empty($member_id)) {
            $model = $model->where('user_id', $member_id);
        }

        if (!empty($field) && !empty($keyword)) {
            $model = $model->where($field, 'like', "%$keyword%");
        }

        if (!empty($type)) {
            $model = $model->where('type', $type);
        }

        if (!empty($shop_id)) {
            $model = $model->where('shop_id', $shop_id);
        }
        
        // 添加日期范围筛选
        if (!empty($start_date)) {
            $model = $model->where('add_time', '>=', $start_date . ' 00:00:00');
        }
        
        if (!empty($end_date)) {
            $model = $model->where('add_time', '<=', $end_date . ' 23:59:59');
        }

        $countModel = $model;
        $count = $countModel->count();

        $list = $model->page($page, $limit)->select();

        foreach ($list as $k => &$item) {
            $item['username'] = ClientMember::where(['id' => $item['user_id'], 'clientkeynum' => $basekeynum])->value('name');
            $item['shop_name'] = Shop::where(['id' => $item['shop_id'], 'clientkeynum' => $basekeynum])->value('title');
            $list[$k]['type_text1'] = $this->getTypeTextAttr($item['type']);
            $list[$k]['status_text1'] = $this->getStatusTextAttr($item['type'], $item['status']);
            $list[$k]['pay_type_text'] = $this->getPayTypeTextAttr($item['pay_type']);
        }

        success(0, '请求成功', $list, $count);
    }

    public function getPayTypeTextAttr($value)
    {
        $arr = [
            1 => '微信支付',
            2 => '储值卡支付',
            3 => '组合支付',
            4 => '线下支付'
        ];
        return $arr[$value];
    }

    private function getTypeTextAttr($value)
    {
        $arr = [
            1 => '自提订单',
            2 => '配送订单',
            3 => '结算台订单'
        ];
        return $arr[$value];
    }

    private function getStatusTextAttr($type, $value)
    {
        if ($type == 2) {
            $arr = [
                0=>'待支付',
                1=>'待审核',
                2=>'已接单',
                3=>'正在配送',
                4=>'已完成',
                -1=> '已取消'
            ];
            return $arr[$value];
        }else if ($type == 1) {
            $arr = [
                0 =>'待支付',
                1 => '待核销',
                100 => '已完成',
                -1 => '已取消',
                2 => '已核销'
            ];
            return $arr[$value];
        } else if ($type == 3) {
            $arr = [
                0 =>'待支付',
                1 => '待核销',
                100 => '已完成',
                -1 => '已取消',
                2 => '已核销'
            ];
            return $arr[$value];
        }
    }

    public function ajax_order_detail_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/order_list', 1);
        $id = Request::instance()->param('id', 0);
        $order_no = Request::instance()->param('order_no', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $where = [
            'clientkeynum' => $basekeynum,
            'order_no' => $order_no
        ];

        $order = Order::where($where)->find();

        if (empty($order)) {
            fail(-1, '订单不存在');
        }

        $list = OrderDetail::where(['clientkeynum' => $basekeynum, 'order_no' => $order['order_no']])->select();
        
        // 处理订单详情，添加计量商品标识
        foreach ($list as &$item) {
            // 解析商品JSON数据
            if (is_string($item['product_json'])) {
                $product_data = json_decode($item['product_json'], true);
                $item['product_json'] = $product_data;
            } else {
                $product_data = $item['product_json'];
            }
            
            if (is_string($item['inventory_json'])) {
                $inventory_data = json_decode($item['inventory_json'], true);
                $item['inventory_json'] = $inventory_data;
            } else {
                $inventory_data = $item['inventory_json'];
            }
            
            // 添加商品基本信息
            $item['product_title'] = $product_data['title'] ?? '';
            $item['inventory_title'] = $inventory_data['title'] ?? '';
            
            // 判断是否为计量商品
            $item['is_weight_product'] = !empty($item['actual_weight']);
            
            // 如果已经有display_info，确保它的格式正确
            if (!isset($item['display_info']) || !is_array($item['display_info'])) {
                if ($item['is_weight_product']) {
                    // 计量商品显示信息
                    $item['display_info'] = [
                        'type' => '计量商品',
                        'weight' => $item['actual_weight'],
                        'unit_price' => $item['weight_unit_price'],
                        'total_price' => $item['amount']
                    ];
                } else {
                    // 普通商品显示信息
                    $item['display_info'] = [
                        'type' => '普通商品',
                        'quantity' => $item['number'] . '件',
                        'unit_price' => $item['price'],
                        'total_price' => $item['amount']
                    ];
                }
            }
        }
        
        success(0, '请求成功', $list);
    }

    public function order_detail()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/audit_order', 0);
        $order_sn = Request::instance()->param('order_sn', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $where = [
            'clientkeynum' => $basekeynum,
            'order_sn' => $order_sn
        ];
        $info = ClientOrderInfo::getInfo($where);
        $list = ClientOrderDetail::getList($where);
        unset($where['clientkeynum']);
        $where['basekeynum'] = $basekeynum;
        $card_use_log = ClientCardUseLog::getList($where);
        $info['member_info'] = ClientMember::getInfoById($info['memberid'], ['clientkeynum' => $basekeynum]);
        $info['order_status'] = get_order_status($info['order_status']);

        $goods_total_price = 0;
        foreach ($list as $v) {
            $goods_total_price += $v['goodsintegral'] * $v['number'];
        }

        $card_total_money = 0;
        if (!empty($card_use_log)) {
            $card_use_log_array = $card_use_log->toArray();
            $card_total_money = array_sum(array_column($card_use_log_array, 'use_money'));
        }

        $this->assign('card_total_money', $card_total_money);
        $this->assign('goods_total_price', $goods_total_price);
        $this->assign('info', $info);
        $this->assign('list', $list);
        $this->assign('card_use_log', $card_use_log);
        return $this->fetch();
    }

    /**
     * 导出订单详情
     */
    public function export_order_detail()
    {
        // 权限校验
        check_auth(request()->controller() . '/order_list', 0);
        
        // 获取筛选条件
        $basekeynum = session('cn_accountinfo.basekeynum');
        $field = Request::instance()->param('field');
        $keyword = Request::instance()->param('keyword');
        $type = Request::instance()->param('type1');
        $member_id = Request::instance()->param('member_id');
        $shop_id = Request::instance()->param('shop_id');
        $start_date = Request::instance()->param('start_date');
        $end_date = Request::instance()->param('end_date');
        $product_id = Request::instance()->param('product_id');
        $inventory_id = Request::instance()->param('inventory_id');
        
        // 构建查询条件
        $where = ['clientkeynum' => $basekeynum];
        $model = Order::where($where)->order('id', 'desc')->where('status', '>', 0);

        if (!empty($member_id)) {
            $model = $model->where('user_id', $member_id);
        }

        if (!empty($field) && !empty($keyword)) {
            $model = $model->where($field, 'like', "%$keyword%");
        }

        if (!empty($type)) {
            $model = $model->where('type', $type);
        }

        if (!empty($shop_id)) {
            $model = $model->where('shop_id', $shop_id);
        }
        
        if (!empty($start_date)) {
            $model = $model->where('add_time', '>=', $start_date . ' 00:00:00');
        }
        
        if (!empty($end_date)) {
            $model = $model->where('add_time', '<=', $end_date . ' 23:59:59');
        }
        
        // 获取订单列表
        $orders = $model->select();
        
        if ($orders->isEmpty()) {
            return json(['code' => -1, 'msg' => '未找到符合条件的订单']);
        }
        
        // 准备导出数据
        $export_data = [];
        
        foreach ($orders as $order) {
            // 获取订单详情
            $detailQuery = OrderDetail::where(['clientkeynum' => $basekeynum, 'order_no' => $order['order_no']]);
            
            // 如果存在商品ID筛选
            if (!empty($product_id)) {
                $detailQuery = $detailQuery->where('product_json', 'like', '%"id":' . $product_id . '%');
            }
            
            // 如果存在规格ID筛选
            if (!empty($inventory_id)) {
                $detailQuery = $detailQuery->where('inventory_json', 'like', '%"id":' . $inventory_id . '%');
            }
            
            $details = $detailQuery->select();
            
            if ($details->isEmpty()) {
                continue;
            }
            
            // 获取用户和店铺信息
            $username = ClientMember::where(['id' => $order['user_id'], 'clientkeynum' => $basekeynum])->value('name');
            $shop_name = Shop::where(['id' => $order['shop_id'], 'clientkeynum' => $basekeynum])->value('title');
            
            foreach ($details as $item) {
                // 解析JSON数据
                $product_data = json_decode($item['product_json'], true);
                $inventory_data = json_decode($item['inventory_json'], true);
                
                // 判断是否为计量商品
                $is_weight_product = !empty($item['actual_weight']);
                
                // 构建导出数据
                $export_row = [
                    '订单号' => $order['order_no'],
                    '门店名称' => $shop_name,
                    '用户名称' => $username,
                    '订单类型' => $this->getTypeTextAttr($order['type']),
                    '订单状态' => $this->getStatusTextAttr($order['type'], $order['status']),
                    '支付方式' => $this->getPayTypeTextAttr($order['pay_type']),
                    '下单时间' => $order['add_time'],
                    '商品名称' => $product_data['title'] ?? '',
                    '规格名称' => $inventory_data['title'] ?? '',
                    '商品类型' => $is_weight_product ? '计量商品' : '普通商品',
                ];
                
                if ($is_weight_product) {
                    $export_row['重量/数量'] = $item['actual_weight'] . 'kg';
                    $export_row['单价'] = $item['weight_unit_price'] . '元/kg';
                } else {
                    $export_row['重量/数量'] = $item['number'] . '件';
                    $export_row['单价'] = $inventory_data['price'] . '元/件';
                }
                
                $export_row['商品总价'] = $item['amount'];
                $export_row['订单总价'] = $order['price'];
                $export_row['运费'] = $order['shipping_price'];
                $export_row['储值卡支付'] = $order['card_price'];
                $export_row['在线支付'] = $order['real_price'];
                $export_row['备注'] = $order['remark'];
                
                $export_data[] = $export_row;
            }
        }
        
        if (empty($export_data)) {
            return json(['code' => -1, 'msg' => '没有可导出的订单详情数据']);
        }
        
        // 生成文件名
        $filename = 'order_details_' . date('YmdHis') . '.xlsx';
        $filepath = \think\facade\Env::get('runtime_path') . 'temp/' . $filename;
        
        // 确保目录存在
        if (!is_dir(\think\facade\Env::get('runtime_path') . 'temp/')) {
            mkdir(\think\facade\Env::get('runtime_path') . 'temp/', 0777, true);
        }
        
        // 使用SimpleExcel导出数据
        $writer = SimpleExcelWriter::create($filepath);
        $writer->addRows($export_data);
        
        // 下载文件
        return download($filepath, $filename);
    }

    /**
     * 订单详情表查询列表
     */
    public function order_detail_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/order_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $shop_enum = Shop::where(['clientkeynum' => $basekeynum])->field('id, title')->select();
        $this->assign('shop_enum', $shop_enum);
        return $this->fetch();
    }

    /**
     * 获取订单统计数据
     */
    public function ajax_order_stats()
    {
        // 权限校验
        check_auth(request()->controller() . '/order_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        // 获取筛选条件
        $field = Request::instance()->param('field');
        $keyword = Request::instance()->param('keyword');
        $type = Request::instance()->param('type1');
        $member_id = Request::instance()->param('member_id');
        $shop_id = Request::instance()->param('shop_id');
        $start_date = Request::instance()->param('start_date');
        $end_date = Request::instance()->param('end_date');
        
        // 构建查询条件
        $where = ['clientkeynum' => $basekeynum];
        $model = Order::where($where)->where('status', '>', 0);

        if (!empty($member_id)) {
            $model = $model->where('user_id', $member_id);
        }

        if (!empty($field) && !empty($keyword)) {
            $model = $model->where($field, 'like', "%$keyword%");
        }

        if (!empty($type)) {
            $model = $model->where('type', $type);
        }

        if (!empty($shop_id)) {
            $model = $model->where('shop_id', $shop_id);
        }
        
        if (!empty($start_date)) {
            $model = $model->where('add_time', '>=', $start_date . ' 00:00:00');
        }
        
        if (!empty($end_date)) {
            $model = $model->where('add_time', '<=', $end_date . ' 23:59:59');
        }
        
        // 计算统计数据
        $stats = [
            'total_shipping_price' => $model->sum('shipping_price'),
            'total_card_price' => $model->sum('card_price'),
            'total_real_price' => $model->sum('real_price'),
            'total_offline_price' => $model->sum('offline_price'),
            'total_price' => $model->sum('price'),
            'order_count' => $model->count(),
            'total_product_total' => $model->sum('product_total'),
            'total_discount_amount' => $model->sum('discount_amount'),
        ];
        
        success(0, '请求成功', $stats);
    }

    /**
     * 获取订单详情数据（分页）
     */
    public function ajax_order_detail_list_page()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/order_list', 1);
        $page = Request::instance()->request('page', 1);
        $limit = Request::instance()->request('limit', 10);
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        // 获取筛选条件
        $field = Request::instance()->param('field');
        $keyword = Request::instance()->param('keyword');
        $shop_id = Request::instance()->param('shop_id');
        $type = Request::instance()->param('type1');
        $start_date = Request::instance()->param('start_date');
        $end_date = Request::instance()->param('end_date');
        $product_id = Request::instance()->param('product_id');
        $inventory_id = Request::instance()->param('inventory_id');
        
        // 构建订单查询条件
        $orderWhere = ['clientkeynum' => $basekeynum];
        $orderModel = Order::where($orderWhere)->where('status', '>', 0);
        
        if (!empty($field) && !empty($keyword)) {
            $orderModel = $orderModel->where($field, 'like', "%$keyword%");
        }
        
        if (!empty($shop_id)) {
            $orderModel = $orderModel->where('shop_id', $shop_id);
        }
        
        if (!empty($type)) {
            $orderModel = $orderModel->where('type', $type);
        }
        
        if (!empty($start_date)) {
            $orderModel = $orderModel->where('add_time', '>=', $start_date . ' 00:00:00');
        }
        
        if (!empty($end_date)) {
            $orderModel = $orderModel->where('add_time', '<=', $end_date . ' 23:59:59');
        }
        
        // 获取符合条件的订单ID
        $orderIds = $orderModel->column('order_no');
        
        if (empty($orderIds)) {
            success(0, '请求成功', [], 0);
            return;
        }
        
        // 查询订单详情
        $detailModel = OrderDetail::where(['clientkeynum' => $basekeynum])
            ->where('order_no', 'in', $orderIds);
        
        // 如果存在商品ID筛选
        if (!empty($product_id)) {
            $detailModel = $detailModel->where('product_json', 'like', '%"id":' . $product_id . '%');
        }
        
        // 如果存在规格ID筛选
        if (!empty($inventory_id)) {
            $detailModel = $detailModel->where('inventory_json', 'like', '%"id":' . $inventory_id . '%');
        }
        
        // 计算总数
        $count = $detailModel->count();
        
        // 获取分页数据
        $list = $detailModel->page($page, $limit)->order('id', 'desc')->select();
        
        // 处理订单详情数据
        foreach ($list as &$item) {
            // 获取订单信息
            $order = Order::where(['clientkeynum' => $basekeynum, 'order_no' => $item['order_no']])->find();
            
            if ($order) {
                $item['shop_name'] = Shop::where(['id' => $order['shop_id'], 'clientkeynum' => $basekeynum])->value('title');
                $item['username'] = ClientMember::where(['id' => $order['user_id'], 'clientkeynum' => $basekeynum])->value('name');
                $item['order_type'] = $this->getTypeTextAttr($order['type']);
                $item['order_status'] = $this->getStatusTextAttr($order['type'], $order['status']);
                $item['pay_type'] = $this->getPayTypeTextAttr($order['pay_type']);
                $item['add_time'] = $order['add_time'];
            }
            
            // 解析商品JSON数据
            $product_data = $item['product_json'];
            $inventory_data = $item['inventory_json'];
            
            // 添加商品基本信息
            $item['product_title'] = $item['product_json']['title'] ?? '';
            $item['inventory_title'] = $item['product_json']['title'] ?? '';
            $item['product_type'] = $item['product_json']['product_type'] ?? '';
            
            // 判断是否为计量商品
            $item['is_weight_product'] = !empty($item['actual_weight']);
            
            if ($item['is_weight_product']) {
                // 计量商品显示信息
                $item['product_type'] = '计量商品';
                $item['quantity'] = $item['actual_weight'] . 'kg';
                $item['unit_price'] = $item['weight_unit_price'] . '元/kg';
            } else {
                // 普通商品显示信息
                $item['product_type'] = '普通商品';
                $item['quantity'] = $item['number'] . '件';
                $item['unit_price'] = $item['price'] . '元/件';
            }
        }
        
        // 修改为只返回表格数据，不包含统计信息
        return json(['code' => 0, 'msg' => 'success', 'count' => $count, 'data' => $list]);
    }

    /**
     * 获取订单详情统计数据
     * 为订单明细列表页提供统计卡片数据
     */
    public function getOrderDetailStatistics()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/order_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');
        
        // 获取筛选条件
        $field = Request::instance()->param('field');
        $keyword = Request::instance()->param('keyword');
        $shop_id = Request::instance()->param('shop_id');
        $type = Request::instance()->param('type1');
        $start_date = Request::instance()->param('start_date');
        $end_date = Request::instance()->param('end_date');
        $product_id = Request::instance()->param('product_id');
        $inventory_id = Request::instance()->param('inventory_id');
        
        // 构建联表查询
        $query = OrderDetail::alias('od')
            ->join('order o', 'od.order_no = o.order_no AND od.clientkeynum = o.clientkeynum')
            ->where('od.clientkeynum', $basekeynum)
            ->where('o.status', '>', 0);
        
        // 应用筛选条件
        if (!empty($field) && !empty($keyword)) {
            $query = $query->where('o.' . $field, 'like', "%$keyword%");
        }
        
        if (!empty($shop_id)) {
            $query = $query->where('o.shop_id', $shop_id);
        }
        
        if (!empty($type)) {
            $query = $query->where('o.type', $type);
        }
        
        if (!empty($start_date)) {
            $query = $query->where('o.add_time', '>=', $start_date . ' 00:00:00');
        }
        
        if (!empty($end_date)) {
            $query = $query->where('o.add_time', '<=', $end_date . ' 23:59:59');
        }
        
        // 商品和规格筛选条件
        if (!empty($product_id)) {
            $query = $query->where('od.product_json', 'like', '%"id":' . $product_id . '%');
        }
        
        if (!empty($inventory_id)) {
            $query = $query->where('od.inventory_json', 'like', '%"id":' . $inventory_id . '%');
        }
        
        // 获取所有符合条件的订单详情
        $details = $query->select();
        
        if (empty($details)) {
            success(0, '请求成功', [
                'total_amount' => 0,
                'normal_product_count' => 0,
                'weight_product_kg' => 0,
                'total_weight_kg' => 0
            ]);
            return;
        }
        
        // 统计数据
        $stats = [
            'total_amount' => 0,            // 总金额
            'normal_product_count' => 0,    // 普通商品销售件数
            'weight_product_kg' => 0,       // 计量商品销售斤数（千克）
            'total_weight_kg' => 0          // 总销售斤数（千克）
        ];
        
        foreach ($details as $detail) {
            // 累加金额
            $stats['total_amount'] += $detail['amount'];
            
            // 解析商品JSON
            $product_data = is_array($detail['product_json']) ? 
                $detail['product_json'] : 
                json_decode($detail['product_json'], true);
            
            // 判断是否为计量商品
            $is_weight_product = $product_data['product_type'] == 2;
            
            if ($is_weight_product) {
                // 计量商品，累加重量
                $weight = floatval($detail['actual_weight']);
                $stats['weight_product_kg'] += $weight;
                $stats['total_weight_kg'] += $weight;
            } else {
                // 普通商品，累加数量
                $number = intval($detail['number']);
                $stats['normal_product_count'] += $number;
            }
        }
        
        // 四舍五入保留小数位数
        $stats['total_amount'] = round($stats['total_amount'], 2);
        $stats['weight_product_kg'] = round($stats['weight_product_kg'], 2);
        $stats['total_weight_kg'] = round($stats['total_weight_kg'] + $stats['normal_product_count'], 2);
        
        success(0, '请求成功', $stats);
    }

    /**
     * 履约监控页面
     * 监控各店铺不同状态的订单数量
     */
    public function fulfillment_monitor()
    {
        // 权限校验
        check_auth(request()->controller() . '/order_list', 0);
        
        // 获取店铺列表供筛选
        $basekeynum = session('cn_accountinfo.basekeynum');
        $shop_enum = Shop::where(['clientkeynum' => $basekeynum])->field('id, title')->select();
        
        // 默认选中当天日期
        $today = date('Y-m-d');
        $this->assign('today', $today);
        $this->assign('shop_enum', $shop_enum);
        return $this->fetch();
    }

    /**
     * 获取履约监控统计数据
     * 根据筛选条件返回各店铺各状态的订单数量
     */
    public function ajax_fulfillment_stats()
    {
        // 权限校验
        check_auth(request()->controller() . '/order_list', 1);
        
        $basekeynum = session('cn_accountinfo.basekeynum');
        $shop_id = Request::instance()->param('shop_id');
        $start_date = Request::instance()->param('start_date');
        $end_date = Request::instance()->param('end_date');
        
        // 基础查询条件 - 只保留客户标识
        $base_where = [
            'clientkeynum' => $basekeynum,
            'status' => ['<>', 0], // 非待支付状态
        ];
        
        // 获取店铺列表
        $shop_list = Shop::where(['clientkeynum' => $basekeynum])
            ->field('id, title')
            ->select();
            
        // 准备返回数据结构
        $shop_stats = [];
        $delivery_stats_by_shop = [];
        $pickup_stats_by_shop = [];
        
        // 总体统计数据
        $total_stats = [
            'delivery' => [
                'waiting_review' => 0,
                'accepted' => 0,
                'delivering' => 0,
                'completed' => 0,
                'total' => 0
            ],
            'pickup' => [
                'waiting_verify' => 0,
                'verified' => 0,
                'completed' => 0,
                'total' => 0
            ]
        ];
        
        // 按店铺统计订单状态
        foreach ($shop_list as $shop) {
            $shop_where = $base_where;
            $shop_where['shop_id'] = $shop['id'];
            
            // 配送订单状态统计 (type = 2)
            $delivery_stats = $this->getDeliveryStats($shop_where);
            $delivery_stats_by_shop[$shop['id']] = $delivery_stats;
            
            // 自提订单状态统计 (type = 1)
            $pickup_stats = $this->getPickupStats($shop_where);
            $pickup_stats_by_shop[$shop['id']] = $pickup_stats;
            
            // 累加到总统计
            foreach ($delivery_stats as $key => $value) {
                $total_stats['delivery'][$key] += $value;
            }
            
            foreach ($pickup_stats as $key => $value) {
                $total_stats['pickup'][$key] += $value;
            }
            
            // 添加店铺统计信息
            $shop_stats[] = [
                'shop_id' => $shop['id'],
                'shop_name' => $shop['title'],
                // 配送订单状态
                'delivery_waiting_review' => $delivery_stats['waiting_review'],
                'delivery_accepted' => $delivery_stats['accepted'],
                'delivery_delivering' => $delivery_stats['delivering'],
                'delivery_completed' => $delivery_stats['completed'],
                'delivery_recent_completed' => $delivery_stats['recent_completed'],
                'delivery_total' => $delivery_stats['total'],
                // 自提订单状态
                'pickup_waiting_verify' => $pickup_stats['waiting_verify'],
                'pickup_verified' => $pickup_stats['verified'],
                'pickup_preparing' => $pickup_stats['preparing'],
                'pickup_completed' => $pickup_stats['completed'],
                'pickup_recent_completed' => $pickup_stats['recent_completed'],
                'pickup_total' => $pickup_stats['total'],
                // 总计
                'total_count' => $delivery_stats['total'] + $pickup_stats['total']
            ];
        }
        
        // 按订单总量排序（从高到低）
        usort($shop_stats, function($a, $b) {
            return $b['total_count'] - $a['total_count'];
        });
        
        // 组装返回数据
        $data = [
            'delivery_stats' => $total_stats['delivery'],
            'pickup_stats' => $total_stats['pickup'],
            'shop_stats' => $shop_stats,
            'delivery_stats_by_shop' => $delivery_stats_by_shop,
            'pickup_stats_by_shop' => $pickup_stats_by_shop
        ];
        
        success(0, '请求成功', $data);
    }
    
    /**
     * 获取配送订单统计
     * @param array $where 基础查询条件
     * @return array 配送订单各状态统计
     */
    private function getDeliveryStats($where)
    {
        $where['type'] = 2; // 配送订单
        
        // 基础查询
        $baseQuery = Order::where($where);
        $total = $baseQuery->count();
        
        // 待审核订单
        $waiting_review = Order::where($where)->where('status', 1)->count();
        
        // 已接单订单
        $accepted = Order::where($where)->where('status', 2)->count();
        
        // 配送中订单
        $delivering = Order::where($where)->where('status', 3)->count();
        
        // 已完成订单
        $completed = Order::where($where)->where('status', 4)->count();
        
        // 查询今日完成的订单（状态为100，且在今日更新的）
        $today_start = date('Y-m-d 00:00:00');
        $today_end = date('Y-m-d 23:59:59');
        $recent_completed = Order::where($where)
            ->where('status', 100)
            ->where('update_time', 'between', [$today_start, $today_end])
            ->count();
        
        return [
            'total' => $total,
            'waiting_review' => $waiting_review,
            'accepted' => $accepted,
            'delivering' => $delivering,
            'completed' => $completed,
            'recent_completed' => $recent_completed
        ];
    }
    
    /**
     * 获取自提订单统计
     * @param array $where 基础查询条件
     * @return array 自提订单各状态统计
     */
    private function getPickupStats($where)
    {
        $where['type'] = 1; // 自提订单
        
        // 基础查询
        $baseQuery = Order::where($where);
        $total = $baseQuery->count();
        
        // 待审核订单
        $waiting_verify = Order::where($where)->where('status', 1)->count();
        
        // 已接单订单
        $verified = Order::where($where)->where('status', 2)->count();
        
        // 配货中订单
        $preparing = Order::where($where)->where('status', 3)->count();
        
        // 已完成订单
        $completed = Order::where($where)->where('status', 100)->count();
        
        // 查询今日完成的订单（状态为100，且在今日更新的）
        $today_start = date('Y-m-d 00:00:00');
        $today_end = date('Y-m-d 23:59:59');
        $recent_completed = Order::where($where)
            ->where('status', 100)
            ->where('update_time', 'between', [$today_start, $today_end])
            ->count();
        
        return [
            'total' => $total,
            'waiting_verify' => $waiting_verify,
            'verified' => $verified,
            'preparing' => $preparing,
            'completed' => $completed,
            'recent_completed' => $recent_completed
        ];
    }

    
}
