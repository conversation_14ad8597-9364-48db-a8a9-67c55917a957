<?php
/**
 * 微信支付回调类
 * @date 2023-6-13
 * <AUTHOR>
 */

namespace app\admin\controller;


use app\admin\model\ClientCardUseLog;
use app\admin\model\ClientOrderInfo;
use app\admin\model\PaymentRecord;
use app\admin\model\PlatWeixinSet;
use think\Controller;
use think\Db;
use think\Exception;
use think\facade\Log;
use think\facade\Request;
use think\response\Json;

class CallbackController extends Controller
{
    /**
     * 微信小程序回调
     */
    public function wxcallback()
    {
        $request = file_get_contents('php://input');
        Log::info('input' . $request);
        $temp  = json_encode(simplexml_load_string($request, 'SimpleXMLElement', LIBXML_NOCDATA));
        $data = json_decode($temp, true);
        Log::info('data:' . json_encode($data));
        $payment_where = ['pay_no' => $data['out_trade_no']];
        $payment_info = PaymentRecord::getInfo($payment_where);
        Log::info('order_info:' . json_encode($payment_info));
        if (empty($payment_info)) Json::create('FAIL','',500);

        $wechat_set = PlatWeixinSet::getConfigByBaseKeyNum($payment_info['basekeynum']);
        Log::info('wechat_set:' . $wechat_set);

        $order_where = ['order_sn' => $payment_info['order_sn']];
        $order_info = PaymentRecord::getInfo($order_where);

        $sign = $data['sign'];
        unset($data['sign']);
        $verify_sign = $this->buildSign($data, $wechat_set['key']);
        if ($verify_sign == $sign) {
            // 验签成功
            try {
                Db::startTrans();
                // 修改订单状态
                $order_info->pay_status = 2;
                $order_info->pay_money = $data['total_fee'] / 100;
                $order_info->save();
                // 修改卡号折扣记录状态
                ClientCardUseLog::where(['order_sn' => $order_info['order_sn']])->update(['status' => 1]);
                // 修改支付订单状态
                $payment_update_data = [
                    'pay_time' => strtotime($data['time_end']),
                    'pay_money' => $data['total_fee'] / 100,
                    'pay_status' => 1,
                    'transaction_id' => $data['transaction_id']
                ];
                PaymentRecord::where($payment_where)->update($payment_update_data);
                Db::commit();
                Json::create('SUCCESS');
            } catch (\Exception $e) {
                Db::rollback();
                Log::error('callback error msg:' . $e->getMessage());
                Log::error('callback error trace:' . $e->getTrace());
                Log::error('callback error traceString:' . $e->getTraceAsString());
                Log::error('callback error line:' . $e->getLine());
                Json::create('FAIL','',500);
            }
        } else {
            Json::create('FAIL','',500);
        }
    }

    /**
     * 生成签名
     * @param $data
     * @param $v2key
     * @return string
     */
    public function buildSign($data, $v2key)
    {
        $sign_str = '';
        foreach ($data as $k => $v) {
            $sign_str .= "$k=$v&";
        }
        $sign_str .= "key=$v2key";
        $signature = strtoupper(md5($sign_str));
        return $signature;
    }


}
