<?php
/**
 * 食谱标签管理控制器
 * @date 2024-10-31
 */

namespace app\admin\controller;

use app\admin\model\RecipeTag;
use think\Db;
use think\facade\Request;

class RecipeTagController extends CnController
{
    /**
     * 标签列表页面
     * @return mixed
     */
    public function tag_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/tag_list', 0);
        
        return $this->fetch();
    }

    /**
     * 获取标签列表数据
     */
    public function ajax_tag_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/tag_list', 1);
        
        $page = Request::instance()->param('page', 1);
        $page_size = Request::instance()->param('page_size', 10);
        $name = Request::instance()->param('name', '');
        $status = Request::instance()->param('status', '');
        
        $where = [];
        
        if (!empty($name)) {
            $where['name'] = ['like', "%{$name}%"];
        }
        
        if ($status !== '') {
            $where['status'] = $status;
        }
        
        $list = RecipeTag::getList($where, $page, $page_size);
        $count = RecipeTag::getCount($where);
        
        // 获取每个标签关联的食谱数量
        foreach ($list as &$item) {
            $recipe_count = Db::table('recipe_tag_relation')->where('tag_id', $item['id'])->count();
            $item['recipe_count'] = $recipe_count;
        }
        
        success(0, '请求成功', $list, $count);
    }

    /**
     * 添加标签页面
     * @return mixed
     */
    public function add_tag()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/tag_list', 0);
        
        return $this->fetch();
    }

    /**
     * 添加标签处理
     */
    public function ajax_add_tag()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/tag_list', 1);
        
        $params = Request::instance()->param();
        
        // 检查标签名称是否已存在
        $exists = RecipeTag::where('name', $params['name'])->find();
        if ($exists) {
            fail(-1, '标签名称已存在');
        }
        
        $data = [
            'name' => $params['name'],
            'status' => isset($params['status']) ? $params['status'] : 1
        ];
        
        $res = RecipeTag::add($data);
        if ($res) {
            success(0, '添加成功');
        } else {
            fail(-1, '添加失败');
        }
    }

    /**
     * 编辑标签页面
     * @return mixed
     */
    public function edit_tag()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/tag_list', 0);
        
        $id = Request::instance()->param('id');
        if (empty($id)) {
            error_tips('NOT FOUND');
        }
        
        $info = RecipeTag::getInfoById($id);
        if (empty($info)) {
            error_tips('NOT FOUND');
        }
        
        $this->assign('info', $info);
        
        return $this->fetch();
    }

    /**
     * 编辑标签处理
     */
    public function ajax_edit_tag()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/tag_list', 1);
        
        $id = Request::instance()->param('id');
        if (empty($id)) {
            fail(-1, 'NOT FOUND');
        }
        
        $params = Request::instance()->param();
        
        // 检查标签名称是否已存在（排除当前标签）
        $exists = RecipeTag::where('name', $params['name'])->where('id', '<>', $id)->find();
        if ($exists) {
            fail(-1, '标签名称已存在');
        }
        
        $data = [
            'name' => $params['name'],
            'status' => isset($params['status']) ? $params['status'] : 1,
            'update_time' => date('Y-m-d H:i:s')
        ];
        
        $where = ['id' => $id];
        
        if (RecipeTag::edit($where, $data)) {
            success(0, '编辑成功');
        } else {
            fail(-1, '编辑失败');
        }
    }

    /**
     * 删除标签
     */
    public function del_tag()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/tag_list', 1);
        
        $id = Request::instance()->param('id');
        if (empty($id)) {
            fail(-1, '请选择要删除的标签');
        }
        
        // 检查是否有关联的食谱
        $recipe_count = Db::table('recipe_tag_relation')->where('tag_id', $id)->count();
        if ($recipe_count > 0) {
            fail(-1, '该标签下有关联的食谱，不能删除');
        }
        
        if (RecipeTag::del($id)) {
            success(0, '删除成功');
        } else {
            fail(-1, '删除失败');
        }
    }

    /**
     * 批量添加标签
     */
    public function ajax_batch_add_tags()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/tag_list', 1);
        
        $tags = Request::instance()->param('tags', '');
        if (empty($tags)) {
            fail(-1, '请输入标签');
        }
        
        // 将标签字符串按逗号或空格分割成数组
        $tag_array = preg_split('/[,，\s]+/', $tags);
        $tag_array = array_filter($tag_array); // 过滤空值
        
        if (empty($tag_array)) {
            fail(-1, '请输入有效的标签');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            $success_count = 0;
            $exist_count = 0;
            
            foreach ($tag_array as $tag_name) {
                $tag_name = trim($tag_name);
                if (empty($tag_name)) {
                    continue;
                }
                
                // 检查标签是否已存在
                $exists = RecipeTag::where('name', $tag_name)->find();
                if ($exists) {
                    $exist_count++;
                    continue;
                }
                
                // 添加标签
                $data = [
                    'name' => $tag_name,
                    'status' => 1
                ];
                
                $res = RecipeTag::add($data);
                if ($res) {
                    $success_count++;
                }
            }
            
            // 提交事务
            Db::commit();
            
            if ($success_count > 0) {
                $message = "成功添加{$success_count}个标签";
                if ($exist_count > 0) {
                    $message .= "，{$exist_count}个标签已存在";
                }
                success(0, $message);
            } else {
                if ($exist_count > 0) {
                    fail(-1, "所有标签都已存在");
                } else {
                    fail(-1, "添加失败");
                }
            }
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            fail(-1, '添加失败：' . $e->getMessage());
        }
    }
} 