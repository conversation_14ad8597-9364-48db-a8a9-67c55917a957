<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/10/23 17:17
 */

namespace app\admin\controller;

use app\admin\model\Banner;
use think\facade\Request;

class BannerController extends CnController
{
    public function edit_banner()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/banner_list', 0);
        $id = Request::instance()->param('id');
        $basekeynum = session("cn_accountinfo.basekeynum");
        if (empty($id)) {
            error_tips('NOT FOUND');
        }
        $where = ['basekeynum' => $basekeynum];
        $info = Banner::getInfoById($id, $where);

        if (empty($info)) {
            error_tips('NOT FOUND');
        }

        $this->assign('info', $info);

        return $this->fetch();
    }

    public function ajax_edit_banner()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/banner_list', 1);
        $id = Request::instance()->param('id');
        $basekeynum = session("cn_accountinfo.basekeynum");
        if (empty($id)) {
            error_tips('NOT FOUND');
        }
        $where = ['basekeynum' => $basekeynum];
        $info = Banner::getInfoById($id, $where);

        if (empty($info)) {
            error_tips('NOT FOUND');
        }
        $where['id'] = $id;
        $params = Request::instance()->param();
        if (Banner::edit($where, $params)) {
            success();
        } else {
            fail(-1, '操作失败');
        }
    }

    /**
     * 删除轮播图
     */
    public function del_banner()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/banner_list', 1);
        $id = Request::instance()->param('id');
        if (empty($id)) fail(-1, '错误，请选择要删除的行');
        $basekeynum = session("cn_accountinfo.basekeynum");
        $where = ['basekeynum' => $basekeynum];
        if (Banner::del($id, $where)) {
            success();
        } else {
            fail(-1, '数据已被删除或不存在');
        }
    }

    /**
     * 添加轮播图view
     * @return mixed
     */
    public function add_banner()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/banner_list', 0);
        return $this->fetch();
    }

    /**
     * 添加轮播图api
     */
    public function ajax_add_banner()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/banner_list', 1);
        $params = Request::instance()->param();
        $basekeynum = session("cn_accountinfo.basekeynum");
        $data = [
            'basekeynum'    => $basekeynum,
            'title'         => $params['title'],
            'status'        => $params['status'],
            'image'         => $params['image'],
            'sort'          => $params['sort'],
            'add_time'      => date('Y-m-d H:i:s'),
            'content'       => $params['content']
        ];
        $res = Banner::add($data);
        if ($res) {
            success(0, '操作成功');
        } else {
            fail(-1, '操作失败');
        }
    }

    /**
     * 轮播图列表view
     * @return mixed
     */
    public function banner_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/banner_list', 0);

        return $this->fetch();
    }

    /**
     * 轮播图列表api
     */
    public function ajax_banner_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/banner_list', 1);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $page = Request::instance()->param('page', 1);
        $page_size = Request::instance()->param('page_size', 10);

        $where = [
            'basekeynum' => $basekeynum,
            'status' => 1
        ];

        $list = Banner::getList($where, $page, $page_size);
        $count = Banner::getCount($where);

        success(0, '请求成功', $list, $count);
    }
}
