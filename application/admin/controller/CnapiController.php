<?php

namespace app\admin\controller;

use think\Db;
use think\Controller;
use think\facade\Session;
use think\facade\Request;
use think\facade\Cache;
use think\facade\Config;

class CnapiController extends Controller
{
    public function wuliu_callback()
    {
        $body = file_get_contents('php://input');
        $body_arr = json_decode($body, 1);
        $com = $body_arr["com"];
        $num = $body_arr["num"];
        $data = $body_arr["data"];
        $state = $body_arr["state"];

        $save["wuliu_content"] = json_encode($data, JSON_UNESCAPED_UNICODE);
        $save["back_log"] = $body;
        if ($state == '1') {
            $save["status"] = 1;
        } elseif ($state == '0') {
            $save["status"] = 2;
        } elseif ($state == '3') {
            $save["status"] = 3;
        } elseif ($state == '14') {
            $save["status"] = 4;
        }
        $save["lasttime"] = time();
        $rs = db("plat_order_wuliu")->where("expressnum='$num' and status not in (0,99)")->update($save);
        if ($rs) {
            $msg["code"] = 200;
            $msg["msg"] = "接收成功";
            $msg["data"] = "接收成功";
            echo json_encode($msg);
            die;
        } else {
            $msg["code"] = 201;
            $msg["msg"] = "接收失败";
            $msg["data"] = "接收失败";
            echo json_encode($msg);
            die;
        }
    }
}
