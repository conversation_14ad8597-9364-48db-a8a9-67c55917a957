<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright ( c ) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use app\admin\model\Banner;
use app\admin\model\ExpressInfoModel;
use app\admin\model\Order;
use think\facade\Session;
use think\facade\Request;
use think\Db;
use think\Tree;
use think\facade\Cache;
use app\admin\model\Shop;
use app\admin\model\Product;

class PlatController extends CnController
{
    protected $gj_password;

    /**
     * 初始化方法
     */

    public function initialize()
    {
        $this->gj_password = "bdx608!";
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    //短信基本设置页面礼赠帮

    public function sms_set_lizengbang()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        if (session('cn_accountinfo.basekeynum') != '平台') {
            check_auth(request()->controller() . '/sms_set_lizengbang', 0);
        }
        $request = Request::instance();
        $param = $request->param();
        if (session('cn_accountinfo.basekeynum') == '平台') {
            $keynum = $param['keynum'];
            $this->assign("is_plat", "1");
        } else {
            $keynum = session('cn_accountinfo.basekeynum');
            $this->assign("is_plat", "0");
        }
        // //获取登录账号等级
        // $account_level = get_login_account_level();
        // $this->assign("account_level", $account_level);

        // //获取短信账号基本信息
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            $duanxin_info = get_duanxin_lizengbang_info($keynum);
            echo json_encode($duanxin_info);
            die;
        }
        $this->assign('keynum', $keynum);
        $info = Db::table('plat_lizengbang_sms_set')->where("basekeynum='$keynum'")->find();
        $this->assign('info', $info);
        return $this->fetch('sms_set_lizengbang');
    }

    public function ajax_sms_lizengbang_set()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        if (session('cn_accountinfo.basekeynum') != '平台') {
            check_auth(request()->controller() . '/sms_set_lizengbang', 1);
        }
        $request = Request::instance();
        $param = $request->param();
        $keynum = isset($param['keynum']) ? $param['keynum'] : '';

        if ($param['sms_status_flag'] == '1') {
            if (isset($param['sms_status']) &&  $param['sms_status'] == 'on') {
                $param['sms_status'] = 1;
            } else {
                $param['sms_status'] = 0;
            }
        }
        if ($param['code_status_flag'] == '1') {
            if (isset($param['code_status']) &&  $param['code_status'] == 'on') {
                $param['code_status'] = 1;
            } else {
                $param['code_status'] = 0;
            }
        }

        //去掉flag标记字段
        unset($param['sms_status_flag']);
        unset($param['code_status_flag']);
        try {

            //需要对数组里的每一项trim下
            $param = array_map('trim', $param);

            $info = Db::table('plat_lizengbang_sms_set')->where("basekeynum='$keynum'")->find();
            if ($info) {
                //去掉keynum，防止出错更新了keynum
                unset($param['keynum']);
                $param['up_time'] = time();
                $rs = Db::table('plat_lizengbang_sms_set')->where("basekeynum='$keynum'")->update($param);
            } else {
                unset($param['keynum']);
                $param['basekeynum'] = $keynum;
                $rs = Db::table('plat_lizengbang_sms_set')->insert($param);
            }
            addoperatelog('修改短信基本设置参数', json_encode($param, JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $r['msg'] = $msg;
            $r['sta'] = 0;
            echo json_encode($r);
            die;
        }


        if (!$rs) {
            $rt['sta'] = 0;
            $rt['msg'] = '保存失败';
            echo json_encode($rt);
            die;
        }

        if ($rs) {
            $r['msg'] = '保存成功';
            $r['sta'] = 1;
            echo json_encode($r);
            die;
        }
    }




    /**
     * 添加修改平台客户页面
     */

    public function add_customer()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/customerlist', 0);
        $request = Request::instance();
        $param = $request->param();
        $keynum = isset($param['keynum']) ? $param['keynum'] : '';
        //平台客户的keynum
        if ($keynum) {
            $plat_clientinfo = Db::table('plat_client')->where("keynum ='$keynum'")->find();
            $plat_accountinfo = Db::table('plat_account')->where("keynum ='$keynum'")->find();
            if ($plat_clientinfo['end_time'] != '') {
                $plat_clientinfo['end_time'] = date('Y-m-d', $plat_clientinfo['end_time']);
            }
            $this->assign('clientinfo', $plat_clientinfo);
            $this->assign('accountinfo', $plat_accountinfo);
        }
        return $this->fetch('add_customer');
    }

    /*
    * 新增平台客户
    */

    public function ajax_add_customer()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/customerlist', 1);
        $request = Request::instance();
        $param = $request->param();

        $keynum = isset($param['keynum']) ? $param['keynum'] : '';
        $companyname = isset($param['companyname']) ? $param['companyname'] : '';
        $clientnum = isset($param['clientnum']) ? $param['clientnum'] : '';
        $accountname = isset($param['accountname']) ? $param['accountname'] : '';
        $weburl = isset($param['weburl']) ? $param['weburl'] : '';
        $system_name = isset($param['system_name']) ? $param['system_name'] : '';
        $glurl = isset($param['glurl']) ? $param['glurl'] : '';

        //判断账号必须只能是字母+数字
        if (!ctype_alnum($accountname)) {
            $data['sta'] = 0;
            $data['msg'] = '账号必须是数字或者字母的组合！';
            echo json_encode($data);
            die;
        }

        //判断编号必须只能是字母+数字
        if (!ctype_alnum($clientnum)) {
            $data['sta'] = 0;
            $data['msg'] = '编号必须是数字或者字母的组合！';
            echo json_encode($data);
            die;
        }

        $accountname = $clientnum . '-' . $accountname;
        //如果keynum为空则是新增
        if ($keynum == '') {
            if (Db::table('plat_account')->where("accountname = '$accountname'")->find()) {
                $data['sta'] = 0;
                $data['msg'] = '该用户已存在';
                echo json_encode($data);
                die;
            }
            $linkman = $param['linkman'];
            $linktel = $param['linktel'];
            $accountpassword = '111111';
            $endtime = date('Y-m-d H:i:s', mktime(0, 0, 0, date('m'), date('d') + 1, date('Y')) - 1);
            $remark = $param['remark'];
            //向大客户表添加数据 palt_client表
            $data['system_name'] = $system_name;
            $data['keynum'] = create_guid();
            $data['clientnum'] = $clientnum;
            $data['companyname'] = $companyname;
            $data['linkman'] = $linkman;
            $data['linktel'] = $linktel;
            $data['remark'] = $remark;
            $data['end_time'] = strtotime($endtime);
            $data['merchantnumber'] = '1';
            $data['cre_time'] = time();

            //向account 表添加数据
            $data1['accountname'] = $accountname;
            $data1['accountpassword'] = password($accountpassword);
            $data1['keynum'] = $data['keynum'];
            $data1['accountrealname'] = $linkman;
            $data1['accountphone'] = $linktel;
            $data1['tablename'] = 'plat_client';
            $data1['basekeynum'] = $data['keynum'];
            $data1['parent_keynum'] = session('cn_accountinfo.keynum');
            $data1['parent_basekeynum'] = session('cn_accountinfo.basekeynum');
            $data1['allpath_keynum'] = session('cn_accountinfo.basekeynum') . ',' . session('cn_accountinfo.keynum') . ',' . $data['keynum'];
            $data1['allpath_basekeynum'] = session('cn_accountinfo.basekeynum') . ',' . $data['keynum'];
            $data1['parent_baseid'] = session('cn_accountinfo.account_id');
            $data1['glurl'] = $glurl;
            $data1['weburl'] = $weburl;
            //平台客户表添加记录
            Db::table('plat_client')->insert($data);
            //account表添加记录
            Db::table('plat_account')->insert($data1);

            //系统基本设置表
            $data3['keynum'] = create_guid();
            $data3['titlecn'] = $system_name . '系统';
            // $data3['copyright'] = '©'.date( 'Y' ).'All rights reserved'.$companyname;
            $data3['copyright'] = '©' . date('Y') . 'All rights reserved' . $companyname . ' 版权所有';
            $data3['basekeynum'] = $data['keynum'];
            Db::table('plat_config')->insert($data3);

            //添加组织机构
            $org['basekeynum'] = $data['keynum'];
            $org['keynum'] = create_guid();
            //  $org['orgnum'] = getRandomString( 6 );
            $org['orgnum'] = 'zb';
            $org['orgname'] = '总部';
            $org['orgleadername'] = $linkman;
            $org['allpathkeynum'] = $org['keynum'];
            $org['o'] = 0;

            $aid = Db::table('plat_org')->insertGetId($org);
            Db::table('plat_org')->where("org_id=$aid")->update(['allpathid' => $aid]);

            //添加角色
            $role['basekeynum'] = $data['keynum'];
            $role['keynum'] = create_guid();
            $role['role_num'] = 'gly';
            $role['role_name'] = '管理员';
            $role['o'] = 0;
            Db::table('plat_role')->insert($role);

            // 添加应用
            $application['clientkeynum'] = $data['keynum'];
            $application['name'] = $system_name;
            $application['reamark'] = $system_name;
            $application['add_time'] = time();
            $application['appkey'] = create_guid();
            $application['appid'] = substr(0, 8, create_guid());
            Db::table('client_application')->insert($application);

            $rt['sta'] = 1;
            $rt['msg'] = '添加成功';
            addoperatelog('添加平台客户成功', json_encode($param, JSON_UNESCAPED_UNICODE));
            echo json_encode($rt);
            die;
        } else {

            //修改的时候不能有重复的账号
            $account_info = Db::table('plat_account')->where("accountname='$accountname' and  keynum!='$keynum'")->find();
            if (!empty($account_info)) {
                $data['sta'] = 0;
                $data['msg'] = '该用户已存在';
                echo json_encode($data);
                die;
            }
            //不等于空则是修改
            $linkman = $param['linkman'];
            $linktel = $param['linktel'];
            $remark = $param['remark'];

            //向大客户表添加数据 palt_client表
            $data['clientnum'] = $clientnum;

            $data['companyname'] = $companyname;
            $data['linkman'] = $linkman;
            $data['linktel'] = $linktel;
            $data['remark'] = $remark;
            $data['system_name'] = $system_name;
            //向account 表添加数据
            $data1['accountname'] = $accountname;

            //$data1['accountrealname'] = $linkman;
            $data1['accountphone'] = $linktel;
            $data1['tablename'] = 'plat_client';
            $data1['glurl'] = $glurl;
            $data1['weburl'] = $weburl;

            //客户表修改记录
            Db::table('plat_client')->where("keynum='$keynum'")->update($data);
            //account表修改记录
            Db::table('plat_account')->where("keynum ='$keynum'")->update($data1);

            $rt['sta'] = 1;
            $rt['msg'] = '修改成功';
            addoperatelog('添加平台客户成功', json_encode($param, JSON_UNESCAPED_UNICODE));
            echo json_encode($rt);
            die;
        }
    }

    /**
     * 客户列表
     */
    public function customerlist()
    {
        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/customerlist', 1);
            $field = isset($param['field']) ? $param['field'] : '';
            $keyword = isset($param['keyword']) ? $param['keyword'] : '';
            $order = isset($param['order']) ? $param['order'] : 'DESC';
            $where = '1 = 1 ';
            if ($keyword) {
                $where .= "and  companyname  like '%$keyword%' ";
            }
            //默认按照时间降序
            $orderby = 'cre_time  desc';
            $pagesize = $param['limit'];
            $p = isset($param['page']) ? intval($param['page']) : '1';
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量
            $count = Db::table('plat_client')->where($where)->count();
            $list = Db::table('plat_client')->where($where)->order($orderby)->limit($offset . ',' . $pagesize)->select();
            foreach ($list as $key => $value) {
                $accout_info = Db::table('plat_account')->where("keynum='{$value["keynum"]}'")->find();

                $list[$key]['glurl'] = $accout_info['glurl'];
                $list[$key]['accountname'] = $accout_info['accountname'];
                $list[$key]['card_count'] = Db::table('client_ycard')->where(['clientkeynum' => $value['keynum']])->count();
                $list[$key]['today_order'] = Order::where(['clientkeynum' => $value['keynum']])
                    ->where( 'add_time' , '>=', date('Y-m-d'))
                    ->where('status', '>', 0)
                    ->count();
                $list[$key]['3_day_order'] = Order::where(['clientkeynum' => $value['keynum']])
                    ->where( 'add_time', '>=', date('Y-m-d', strtotime('-3 day')))
                    ->where('status', '>', 0)
                    ->count();
                $list[$key]['7_day_order'] = Order::where(['clientkeynum' => $value['keynum']])
                    ->where( 'add_time', '>=', date('Y-m-d', strtotime('-7 day')))
                    ->where('status', '>', 0)
                    ->count();
                $list[$key]['30_day_order'] = Order::where(['clientkeynum' => $value['keynum']])
                    ->where( 'add_time', '>=', date('Y-m-d', strtotime('-30 day')))
                    ->where('status', '>', 0)
                    ->count();
                $list[$key]['order_count'] = Order::where(['clientkeynum' => $value['keynum']])
                    ->where('status', '>', 0)->count();
                $list[$key]['shop_count'] = Shop::where(['clientkeynum' => $value['keynum']])->count();
                $list[$key]['product_count'] = Product::where(['clientkeynum' => $value['keynum']])->count();
            }
            $rtdata['count'] = $count;
            $rtdata['data'] = $list;
            $rtdata['msg'] = '';
            $rtdata['code'] = 0;
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/customerlist', 0);
            return $this->fetch('customerlist');
        }
    }

    // 删除客户
    public function customer_del()
    {
        $request = Request::instance();
        $param = $request->param();
        check_auth(request()->controller() . '/customerlist', 1);
        $keynum = isset($param['keynum']) ? $param['keynum'] : '';
        $password = $param["password"];
        if ($password != $this->gj_password) {
            $rt["sta"] = 0;
            $rt["msg"] = "高级密码错误";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        // 通过事务操作
        Db::startTrans();
        try {
            db('plat_client')->where("keynum='$keynum'")->delete();
            db('plat_account')->where("basekeynum='$keynum'")->delete();
            db('plat_config')->where("basekeynum='$keynum'")->delete();
            db('plat_org')->where("basekeynum='$keynum'")->delete();
            db('plat_role')->where("basekeynum='$keynum'")->delete();
            // 提交事务
            Db::commit();
            $return_arr['sta'] = 1;
            $return_arr['msg'] = '操作成功';
            echo json_encode($return_arr);
            die;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $return_arr['sta'] = 0;
            $return_arr['msg'] = '操作失败';
            echo json_encode($return_arr);
            die;
        }
    }
    /*
    * 用户授权
    */

    public function customer_authorize()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/customer_authorize', 0);
        $list = Db::table('plat_client')->select();
        //print_r( $list );
        // $html = "<select name='keynum' lay-filter='kehu'>";
        foreach ($list as $key => $value) {
            $list[$key]['end_time'] = date('Y-m-d', $value['end_time']);
        //     $html .= "<option value='" . $value['keynum'] . "'>" . $value['companyname'] . '->' . $value['glname'] . '(' . date('Y-m-d', $value['end_time']) . ')</option>';
        }
        // $html .= '</select>';
        $this->assign('info', $list[0]);
        $this->assign('list', $list);
        return $this->fetch('customer_authorize');
    }

    public function ajax_customer_authorize()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/customer_authorize', 1);
        $request = Request::instance();
        $param = $request->param();
        //print_r( $param );
        // die;
        $keynum = isset($param['keynum']) ? $param['keynum'] : '';
        $where = "keynum='$keynum'";
        $info = Db::table('plat_client')->where($where)->find();
        //print_r( $info );
        // die;
        $save['end_time'] = strtotime($param['end_time']);
        $save['merchantnumber'] = isset($param['merchantnumber']) ? $param['merchantnumber'] : '';
        Db::table('plat_client')->where($where)->update($save);
        //记录日志
        $log['clientkeynum'] = $keynum;
        $log['old_end_time'] = $info['end_time'];
        $log['old_merchantnumber'] = $info['merchantnumber'];
        $log['new_end_time'] = strtotime($param['end_time']);
        $log['new_merchantnumber'] = isset($param['merchantnumber']) ? $param['merchantnumber'] : '';
        $log['money'] = $param['money'];
        $log['time'] = time();
        $log['remark'] = $param['remark'];
        Db::table('plat_client_authorize_log')->insert($log);
        $return_arr['sta'] = 1;
        $return_arr['msg'] = '操作成功';
        echo json_encode($return_arr);
        die;
    }

    /*
    * 获取平台客户信息
    */

    public function get_clientinfo()
    {
        $request = Request::instance();
        $param = $request->param();
        $keynum = isset($param['keynum']) ? $param['keynum'] : '';
        $info = Db::table('plat_client')->where("keynum='$keynum'")->find();
        $info['end_time'] = date('Y-m-d', $info['end_time']);
        echo json_encode($info);
    }

    //短信基本设置页面

    public function sms_set()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/sms_set', 0);
        $request = Request::instance();
        $param = $request->param();
        if (session('cn_accountinfo.basekeynum') == '平台') {
            $keynum = $param['keynum'];
        } else {
            $keynum = session('cn_accountinfo.basekeynum');
        }
        //获取短信账号基本信息
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            $duanxin_info = get_duanxin_info($keynum);
            echo json_encode($duanxin_info);
            die;
        }
        $this->assign('keynum', $keynum);
        $info = Db::table('plat_sms_set')->where("basekeynum='$keynum'")->find();
        $this->assign('info', $info);
        return $this->fetch('sms_set');
    }

    public function ajax_sms_set()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/sms_set', 1);
        $request = Request::instance();
        $param = $request->param();
        $keynum = isset($param['keynum']) ? $param['keynum'] : '';
        $data['url'] = "http://vplat.qiwubang.com"; //isset($param['url']) ? $param['url'] : '';
        $data['username'] = isset($param['username']) ? $param['username'] : '';
        $data['password'] = isset($param['password']) ? $param['password'] : '';
        $data['up_time'] = time();

        if (isset($param['sms_status']) &&  $param['sms_status'] == 'on') {
            $data['sms_status'] = 1;
        } else {
            $data['sms_status'] = 0;
        }

        if (isset($param['add_order_status']) && $param['add_order_status'] == 'on') {
            $data['add_order_status'] = 1;
        } else {
            $data['add_order_status'] = 0;
        }
        $data['add_order_content'] = isset($param['add_order_content']) ? $param['add_order_content'] : '';

        if (isset($param['code_status']) &&  $param['code_status'] == 'on') {
            $data['code_status'] = 1;
        } else {
            $data['code_status'] = 0;
        }
        $data['code_content'] = isset($param['code_content']) ? $param['code_content'] : '';

        if (isset($param['delivery_status']) &&  $param['delivery_status'] == 'on') {
            $data['delivery_status'] = 1;
        } else {
            $data['delivery_status'] = 0;
        }
        $data['delivery_content'] = isset($param['delivery_content']) ? $param['delivery_content'] : '';

        if (isset($param['member_balance_status']) &&  $param['member_balance_status'] == 'on') {
            $data['member_balance_status'] = 1;
        } else {
            $data['member_balance_status'] = 0;
        }
        $data['member_balance_content'] = isset($param['member_balance_content']) ? $param['member_balance_content'] : '';

        $info = Db::table('plat_sms_set')->where("basekeynum='$keynum'")->find();
        if ($info) {
            $rs = Db::table('plat_sms_set')->where("basekeynum='$keynum'")->update($data);
        } else {
            $data['basekeynum'] = $keynum;
            $rs = Db::table('plat_sms_set')->insert($data);
        }

        addoperatelog('修改短信基本设置参数', json_encode($param, JSON_UNESCAPED_UNICODE));
        if ($rs) {
            $r['msg'] = '保存成功';
            $r['sta'] = 1;
            echo json_encode($r);
            die;
        }
    }

    //短信基本设置页面

    public function sms_set_ali()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/sms_set_ali', 0);
        $request = Request::instance();
        $param = $request->param();
        if (session('cn_accountinfo.basekeynum') == '平台') {
            $keynum = $param['keynum'];
        } else {
            $keynum = session('cn_accountinfo.basekeynum');
        }
        $this->assign('keynum', $keynum);
        $info = Db::table('plat_sms_set_ali')->where("basekeynum='$keynum'")->find();
        $this->assign('info', $info);
        return $this->fetch('sms_set_ali');
    }

    public function ajax_sms_set_ali()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/sms_set_ali', 1);
        $request = Request::instance();
        $param = $request->param();
        $keynum = isset($param['keynum']) ? $param['keynum'] : '';
        $data['accesskeyid'] = isset($param['accesskeyid']) ? $param['accesskeyid'] : '';
        $data['accesskeysecret'] = isset($param['accesskeysecret']) ? $param['accesskeysecret'] : '';
        $data['signname'] = isset($param['signname']) ? $param['signname'] : '';
        $data['templatecode'] = isset($param['templatecode']) ? $param['templatecode'] : '';
        $data['up_time'] = time();

        if (isset($param['sms_status']) &&  $param['sms_status'] == 'on') {
            $data['sms_status'] = 1;
        } else {
            $data['sms_status'] = 0;
        }
        if (isset($param['code_status']) &&  $param['code_status'] == 'on') {
            $data['code_status'] = 1;
        } else {
            $data['code_status'] = 0;
        }
        $info = Db::table('plat_sms_set_ali')->where("basekeynum='$keynum'")->find();
        if ($info) {
            $rs = Db::table('plat_sms_set_ali')->where("basekeynum='$keynum'")->update($data);
        } else {
            $data['basekeynum'] = $keynum;
            $rs = Db::table('plat_sms_set_ali')->insert($data);
        }
        addoperatelog('修改阿里云短信基本设置参数', json_encode($param, JSON_UNESCAPED_UNICODE));
        if ($rs) {
            $r['msg'] = '保存成功';
            $r['sta'] = 1;
            echo json_encode($r);
            die;
        }
    }

    //短信发送日志
    public function  sms_log_list()
    {

        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/sms_log_list', 1);
            //平台客户的keynum
            //$basekeynum = session( 'cn_accountinfo.basekeynum' );
            $basekeynum = $param['basekeynum'];
            $p = $param['page'] ? $param['page'] : 1;
            $pagesize = $param['limit'];
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量
            $id = isset($param['id']) ? $param['id'] : '';
            $words = isset($param['words']) ? $param['words'] : '';
            $phonenum = isset($param['phonenum']) ? $param['phonenum'] : '';
            $where = "1 = 1  and clientkeynum='$basekeynum' ";
            if ($id) {
                $where .= "and  id  = '$id' ";
            }
            if ($words) {
                $where .= "and  words  like '%$words%' ";
            }
            if ($phonenum) {
                $where .= "and  phonenum  like '%$phonenum%' ";
            }
            //默认按照时间降序
            $count = Db::table('plat_sms_log')->where($where)->count();
            $list = Db::table('plat_sms_log')->where($where)->order('id', 'desc')->limit($offset . ',' . $pagesize)->select();
            $rtdata['data'] = $list;
            $rtdata['count'] = $count;
            $rtdata['code'] = 0;
            $rtdata['msg'] = '';
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/sms_log_list', 0);
            if (session('cn_accountinfo.basekeynum') == '平台') {
                $basekeynum = $param['keynum'];
            } else {
                $basekeynum = session('cn_accountinfo.basekeynum');
            }
            $this->assign('basekeynum', $basekeynum);
            return  $this->fetch('sms_log_list');
        }
    }

    //严重错误日志 系统会通过定时任务轮训发送把日志内容发送给系统管理员
    public function  warn_log_list()
    {

        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            //check_auth( request()->controller(). '/warn_log_list', 1 );
            //平台客户的keynum
            $basekeynum = session('cn_accountinfo.basekeynum');
            $p = $param['page'] ? $param['page'] : 1;
            $pagesize = $param['limit'];
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量
            $id = isset($param['id']) ? $param['id'] : '';
            $warn_content = isset($param['warn_content']) ? $param['warn_content'] : '';
            $where = "1 = 1  and basekeynum='$basekeynum' ";
            if ($id) {
                $where .= "and  id  = '$id' ";
            }
            if ($warn_content) {
                $where .= "and  warn_content  like '%$warn_content%' ";
            }

            //默认按照时间降序
            $count = Db::table('plat_warn_log')->where($where)->count();
            $list = Db::table('plat_warn_log')->where($where)->order('id', 'desc')->limit($offset . ',' . $pagesize)->select();
            $rtdata['data'] = $list;
            $rtdata['count'] = $count;
            $rtdata['code'] = 0;
            $rtdata['msg'] = '';
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            //check_auth( request()->controller(). '/warn_log_list', 0 );
            return  $this->fetch('warn_log_list');
        }
    }

    /*
    * 用户授权记录
    */

    public function authorize_log()
    {

        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/authorize_log', 1);
            $p = $param['page'] ? $param['page'] : 1;
            $pagesize = $param['limit'];
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量
            $where = '1=1';
            $keyword = isset($param['keyword']) ? $param['keyword'] : '';
            $order =  isset($param['order']) ? $param['order'] : 'time desc';
            if ($keyword != '') {
                $where .= " and clientkeynum=(select keynum from plat_client where (companyname like '%" . trim($keyword) . "%'))";
            }
            $count =  Db::table('plat_client_authorize_log')->where($where)->count();
            $list =  Db::table('plat_client_authorize_log')->where($where)->order($order)->limit($offset, $pagesize)->select();
            foreach ($list as $key => $value) {
                $list[$key]['clientinfo'] =  Db::table('plat_client')->where("keynum='" . $value['clientkeynum'] . "'")->find();
                $list[$key]['old_end_time'] = date('Y-m-d H:i:s', $value['old_end_time']);
                $list[$key]['new_end_time'] = date('Y-m-d H:i:s', $value['new_end_time']);
                $list[$key]['time'] = date('Y-m-d H:i:s', $value['time']);
            }
            $rtdata['count'] = $count;
            $rtdata['data'] = $list;
            $rtdata['msg'] = '';
            $rtdata['code'] = 0;
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/authorize_log', 0);
            return $this->fetch('authorize_log');
        }
    }

    /**
     * 获取当前节点下面的组织结构
     */

    public function get_org_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/orglist', 1);
        $request = Request::instance();
        $param = $request->param();
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $p = isset($param['page']) ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        //计算记录偏移量
        $keynum = isset($param['keynum']) ? $param['keynum'] : '';
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $where = "1=1  and keynum != '$keynum' and basekeynum='$basekeynum' ";
        if ($keynum != '') {
            $where .= " and FIND_IN_SET('$keynum',allpathkeynum)";
        }
        if ($field == 'orgnum') {
            $where .= " and orgnum  like '%$keyword%' ";
        } else if ($field == 'orgname') {
            $where .= " and orgname  like '%$keyword%' ";
        }
        $plat_orglist = Db::table('plat_org')->where($where)->order('O asc ')->limit($offset . ',' . $pagesize)->select();
        $count = Db::table('plat_org')->where($where)->order('O asc ')->limit($offset . ',' . $pagesize)->count();
        $rtdata['data'] = $plat_orglist;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        echo json_encode($rtdata);
        die;
    }

    /*
    * 添加组织
    */

    public function addorg()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/orglist', 0);
        return $this->fetch('addorg');
    }

    /**
     * 添加组织
     */

    public function ajax_addorg()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/orglist', 1);
        $request = Request::instance();
        $param = $request->param();
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');

        $keynum = $param['keynum'];
        $orgnum = $param['orgnum'];
        $orgname = $param['orgname'];
        $orgleadername = $param['orgleadername'];
        $o = $param['o'];
        $orgaddress = $param['orgaddress'];
        $remark = $param['remark'];

        //取出传过来的组织信息
        $plat_orginfo = Db::table('plat_org')->where("keynum='$keynum'")->find();

        $data['keynum'] = create_guid();
        $data['parentkeynum'] = $plat_orginfo['keynum'];
        $data['parentid'] = $plat_orginfo['org_id'];
        $data['orgnum'] = $orgnum;
        $data['orgname'] = $orgname;
        $data['orgleadername'] = $orgleadername;
        $data['orgaddress'] = $orgaddress;
        $data['allpathkeynum'] = isset($plat_orginfo['allpathkeynum']) ? $plat_orginfo['allpathkeynum'] . ',' . $data['keynum'] : $data['keynum'];
        $data['allpathtext'] = $plat_orginfo['orgname'] . ',' . $data['orgname'];
        $data['o'] = $o;
        $data['remark'] = $remark;
        $data['basekeynum'] = $basekeynum;

        $plat_orgid = Db::table('plat_org')->insertGetId($data);
        $data1['allpathid'] = isset($plat_orginfo['allpathid']) ? $plat_orginfo['allpathid'] . ',' . $plat_orgid : $plat_orgid;
        Db::table('plat_org')->where('org_id', $plat_orgid)->update($data1);
        addoperatelog('添加组织机构' . $orgname, json_encode($param, JSON_UNESCAPED_UNICODE));
        $rt['sta'] = 1;
        $rt['msg'] = '添加成功';
        echo json_encode($rt);
        die;
    }

    /**
     * 删除组织
     */

    public function ajax_delorgs()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/orglist', 1);
        $request = Request::instance();
        $param = $request->param();
        $keynums = $param['keynums'];

        //把传过来的逗号拼接的字符串切成数组
        $arrkeynumlist = explode(',', $keynums);
        $where['keynum'] = array('in', $arrkeynumlist);
        Db::table('plat_org')->where($where)->delete();
        addoperatelog('删除组织机构', json_encode($param, JSON_UNESCAPED_UNICODE));
        $rt['sta'] = 1;
        $rt['msg'] = '删除成功';
        echo json_encode($rt);
        die;
    }

    /**
     * 修改根节点页面
     */

    public function eroot_org()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/orglist', 0);
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];
        $plat_orginfo = Db::table('plat_org')->where("keynum ='$keynum' ")->find();
        $this->assign('info', $plat_orginfo);
        return $this->fetch('eroot_org');
    }

    /**
     * 修改根节点ajax保存
     */

    public function ajax_eroot_org()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/orglist', 1);
        $request = Request::instance();
        $param = $request->param();

        $keynum = $param['keynum'];
        $orgnum = $param['orgnum'];
        $orgname = $param['orgname'];
        $orgleadername = $param['orgleadername'];
        $o = $param['o'];
        $orgaddress = $param['orgaddress'];
        $remark = $param['remark'];

        $data['orgnum'] = $orgnum;
        $data['orgname'] = $orgname;
        $data['orgleadername'] = $orgleadername;
        $data['orgaddress'] = $orgaddress;
        $data['o'] = $o;
        $data['remark'] = $remark;
        Db::table('plat_org')->where('keynum', $keynum)->update($data);
        addoperatelog('修改组织机构根节点', json_encode($param, JSON_UNESCAPED_UNICODE));
        $rt['sta'] = 1;
        $rt['msg'] = '修改成功！';
        echo json_encode($rt);
        die;
    }

    /**
     * 修改普通节点页面
     */

    public function edit_org()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/orglist', 0);
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];
        $plat_orginfo = Db::table('plat_org')->where("keynum ='$keynum' ")->find();
        $this->assign('info', $plat_orginfo);
        return $this->fetch('edit_org');;
    }

    /**
     * 修改组织机构ajax保存
     */

    public function ajax_edit_org()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/orglist', 1);
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];
        $orgnum = $param['orgnum'];
        $orgname = $param['orgname'];
        $orgleadername = $param['orgleadername'];
        $o = $param['o'];
        $orgaddress = $param['orgaddress'];
        $remark = $param['remark'];

        $data['orgnum'] = $orgnum;
        $data['orgname'] = $orgname;
        $data['orgleadername'] = $orgleadername;
        $data['orgaddress'] = $orgaddress;
        $data['o'] = $o;
        $data['remark'] = $remark;
        addoperatelog('修改组织机构', json_encode($param, JSON_UNESCAPED_UNICODE));
        Db::table('plat_org')->where('keynum', $keynum)->update($data);
        $rt['sta'] = 1;
        $rt['msg'] = '修改成功！';
        echo json_encode($rt);
        die;
    }

    /**
     * 角色列表
     */

    public function rolelist()
    {

        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/rolelist', 1);
            //平台客户的keynum
            $basekeynum = session('cn_accountinfo.basekeynum');
            $keyword = isset($param['keyword']) ? $param['keyword'] : '';
            $order = isset($param['order']) ? $param['order'] : 'DESC';
            $where = " 1 = 1  and basekeynum='$basekeynum' ";
            if ($keyword) {
                $where .= "and  role_name  like '%$keyword%' ";
            }

            $p = isset($param['page']) ? intval($param['page']) : '1';
            $pagesize = $param['limit'];
            #每页数量
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量

            $count = Db::table('plat_role')->where($where)->count();
            $list = Db::table('plat_role')->where($where)->order('o')->limit($offset . ',' . $pagesize)->select();
            $rtdata['data'] = $list;
            $rtdata['count'] = $count;
            $rtdata['code'] = 0;
            $rtdata['msg'] = '';
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/rolelist', 0);
            return $this->fetch('rolelist');
        }
    }

    /**
     * 角色设置权限页面
     */

    public function pur_form()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/rolelist', 0);
        return $this->fetch('pur_form');
    }

    /**
     * 修改添加角色页面
     */

    public function role_form()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/rolelist', 1);
        $request = Request::instance();
        $param = $request->param();
        $keynum = isset($param['keynum']) ? $param['keynum'] : '';
        if ($keynum) {
            $plat_roleinfo = Db::table('plat_role')->where("keynum='$keynum'")->find();
            $this->assign('info', $plat_roleinfo);
        }
        return $this->fetch('role_form');
    }

    /**
     * 修改添加角色ajax方法
     */

    public function ajax_addedit_role()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/rolelist', 1);
        $request = Request::instance();
        $param = $request->param();
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');

        $menumidstr = $param['menumidstr'];
        $keynum = $param['keynum'];
        $role_num = $param['role_num'];
        $role_name = $param['role_name'];
        $o = $param['o'];
        $remark = $param['remark'];

        $data['role_num'] = $role_num;
        $data['role_name'] = $role_name;
        $data['o'] = $o;
        $data['remark'] = $remark;
        $data['basekeynum'] = $basekeynum;

        if ($keynum) {
            $data['purviewlist_id'] = $menumidstr;
            Db::table('plat_role')->where('keynum', $keynum)->update($data);

            $rt['sta'] = 1;
            $rt['msg'] = '修改成功!';
            echo json_encode($rt);
            addoperatelog('修改角色', json_encode($param, JSON_UNESCAPED_UNICODE));
            die;
        } else {
            $data['purviewlist_id'] = $menumidstr;
            $data['keynum'] = create_guid();
            Db::table('plat_role')->insert($data);

            $rt['sta'] = 1;
            $rt['msg'] = '新增成功!';
            echo json_encode($rt);
            addoperatelog('新增角色', json_encode($param, JSON_UNESCAPED_UNICODE));
            die;
        }
    }

    /**
     * 删除角色
     */

    public function role_del()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/rolelist', 1);
        $request = Request::instance();
        $param = $request->param();
        $keynums = isset($param['keynums']) ? $param['keynums'] : false;
        if ($keynums) {
            if (is_array($keynums)) {
                $map['keynum'] = array('in', $keynums);
            } else {
                $map = "keynum='$keynums'";
            }
            if (Db::table('plat_role')->where($map)->delete()) {
                $return_arr['sta'] = 1;
                $return_arr['msg'] = '操作成功';
                addoperatelog('删除角色', json_encode($param, JSON_UNESCAPED_UNICODE));
            } else {
                $return_arr['sta'] = 0;
                $return_arr['msg'] = '操作失败';
            }
        } else {
            $return_arr['sta'] = 0;
            $return_arr['msg'] = '操作失败';
        }
        echo json_encode($return_arr);
        die;
    }

    /**
     * 角色菜单json
     */

    public function role_menu_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/rolelist', 1);
        $request = Request::instance();
        $param = $request->param();
        //取出当前角色已经勾选的菜单id放入到数组里面
        $keynum = $param['keynum'];
        $plat_roleinfo = Db::table('plat_role')->where(" keynum='$keynum' ")->find();
        $purviewlist_ids = $plat_roleinfo['purviewlist_id'];
        $purviewlist_idarr = explode(',', $purviewlist_ids);

        $where['is_show'] = '1';
        //登录者的菜单权限
        $menulist = Db::table('plat_menu')->where($where)->whereIn('menu_id', session('cn_accountinfo.menulist'))->order('o asc')->select();
        $arrlist = $this->arrayPidProcess($menulist, array(), '0', '3', $purviewlist_idarr);
        echo json_encode($arrlist);
        die;
    }

    //查询数据整理递归函数（无限制级别）

    public function arrayPidProcess($data, $res = array(), $pid = '0', $endlevel = '0', $purviewlist_idarr)
    {
        foreach ($data as $k => $v) {
            if ($v['pid'] == $pid) {
                $wlarr['id'] = $v['menu_id'];
                $wlarr['text'] = $v['menu_name'];

                if ($endlevel != '0') {
                    if ($v['level'] == $endlevel) {
                        if (in_array($v['menu_id'], $purviewlist_idarr)) {
                            $wlarr['checked'] = 1;
                        } else {
                            unset($wlarr['checked']);
                        }
                        $res[] = $wlarr;
                    } else {
                        $child = $this->arrayPidProcess($data, array(), $v['menu_id'], $endlevel, $purviewlist_idarr);
                        $wlarr['children'] = $child;
                        $res[] = $wlarr;
                    }
                } else {
                    $child = $this->arrayPidProcess($data, array(), $v['menu_id'], '', $purviewlist_idarr);
                    if ($child == '' || $child == null || empty($child)) {
                        if (in_array($v['menu_id'], $purviewlist_idarr)) {
                            $wlarr['checked'] = 1;
                        } else {
                            unset($wlarr['checked']);
                        }
                        $res[] = $wlarr;
                    } else {
                        $wlarr['children'] = $child;
                        $res[] = $wlarr;
                    }
                }
            }
        }
        return $res;
    }

    /**
     * ajax编辑角色菜单
     */

    public function ajax_eidt_role_menu()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/rolelist', 1);
        $request = Request::instance();
        $param = $request->param();
        //角色的keynum
        $keynum = $param['keynum'];
        $menumidstr = $param['menumidstr'];

        $data['purviewlist_id'] = $menumidstr;
        $flag = Db::table('plat_role')->where('keynum', $keynum)->update($data);
        addoperatelog('编辑角色菜单权限', json_encode($param, JSON_UNESCAPED_UNICODE));
        $rt['sta'] = 1;
        $rt['msg'] = '编辑成功！';
        echo json_encode($rt);
        die;
    }

    /**
     * 用户列表页面
     */

    public function adminlist()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/adminlist', 0);
        return $this->fetch('adminlist');
    }

    /**
     * 获取当前节点下面的账号列表
     */

    public function get_admin_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/adminlist', 1);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();
        $param = $request->param();

        $keynum = $param['keynum'];
        //组织机构的keynum
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        //计算记录偏移量
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $where = "1=1  and  a.basekeynum='$basekeynum' ";

        if ($keynum != '') {
            $where .= " and FIND_IN_SET ('$keynum',o.allpathkeynum) ";
        }
        if ($field == 'accountname') {
            $where .= " and a.accountname  like '%$keyword%' ";
        } else if ($field == 'accountrealname') {
            $where .= " and a.accountrealname  like '%$keyword%' ";
        } else if ($field == 'accountphone') {
            $where .= " and a.accountphone  like '%$keyword%' ";
        }

        $plat_account_org_adminlist = Db::table('plat_account')
            ->field('a.*,o.keynum as keynum_org,o.orgname')
            ->alias('a')
            ->join('plat_org o', 'a.orgkeynum = o.keynum')
            ->where($where)->select();

        foreach ($plat_account_org_adminlist as $k => $v) {
            //取出角色选中的值
            $keynum = $v['keynum'];
            $rolekeylist = Db::table('plat_account')->where('keynum', $keynum)->value('rolekeylist');
            $rolekeylist_arr = explode(',', $rolekeylist);
            $plat_rolealllist_checked =  Db::table('plat_role')->whereIn('keynum', $rolekeylist_arr)->order('o desc')->select();
            $role_names = '';
            foreach ($plat_rolealllist_checked as $k1 => $v1) {
                $role_names .= $v1['role_name'] . '|';
            }
            $plat_account_org_adminlist[$k]['rolenames'] = substr($role_names, 0, -1) ? substr($role_names, 0, -1) : '暂无';
            
            // 获取微信绑定信息
            $wechat_bind_info = Db::table('plat_third_auth')
                ->where('account_id', $v['account_id'])
                ->where('auth_type', 'wechat')
                ->where('status', 1)
                ->find();
            $plat_account_org_adminlist[$k]['has_wechat_bind'] = empty($wechat_bind_info) ? 0 : 1;
        }

        $count = Db::table('plat_account')
            ->field('a.*,o.keynum as keynum_org,o.orgname')
            ->alias('a')
            ->join('plat_org o', 'a.orgkeynum = o.keynum')
            ->where($where)->count();

        $rtdata['data'] = $plat_account_org_adminlist;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        echo json_encode($rtdata);
        die;
    }

    /**
     * 获取某个账号信息
     */

    public function get_account_info()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/adminlist', 1);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];
        //账号的keynum
        $where = "1=1 and  a.keynum='$keynum' ";
        $plat_account_org_account_info = Db::table('plat_account')
            ->field('a.*,o.keynum as keynum_org,o.orgname')
            ->alias('a')
            ->join('plat_org o', 'a.orgkeynum = o.keynum')
            ->where($where)->find();

        //取出角色选中的值
        $rolekeylist = Db::table('plat_account')->where('keynum', $keynum)->value('rolekeylist');
        $rolekeylist_arr = explode(',', $rolekeylist);
        $plat_rolealllist_checked =  Db::table('plat_role')->where(array('keynum' => array('IN', $rolekeylist_arr)))->order('o desc')->select();
        $role_names = '';
        foreach ($plat_rolealllist_checked as $k1 => $v1) {
            $role_names .= $v1['role_name'] . '|';
        }
        $plat_account_org_account_info['rolenames'] = substr($role_names, 0, -1) ? substr($role_names, 0, -1) : '暂无';

        $rtdata['sta'] = '1';
        $rtdata['msg'] = '获取成功';
        $rtdata['info'] = $plat_account_org_account_info;
        echo json_encode($rtdata);
        die;
    }

    /**
     * 删除账号
     */

    public function ajax_deladmins()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/adminlist', 1);
        $request = Request::instance();
        $param = $request->param();
        $keynums = $param['keynums'];
        //把传过来的逗号拼接的字符串切成数组
        $arrkeynumlist = explode(',', $keynums);
        // $where['keynum'] = array('in', $arrkeynumlist);
        Db::table('plat_account')->whereIn('keynum', $arrkeynumlist)->delete();
        addoperatelog('删除系统用户', json_encode($param, JSON_UNESCAPED_UNICODE));
        $rt['sta'] = 1;
        $rt['msg'] = '删除成功';
        echo json_encode($rt);
        die;
    }

    /**
     * 修改普通节点页面
     */

    public function edit_admin()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/adminlist', 0);
        $request = Request::instance();
        $param = $request->param();
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        //取出来唯一编号，根据是那个表来获取编号
        $tablename = Db::table('plat_account')->where("basekeynum= '$basekeynum' ")->value('tablename');
        if ($tablename == 'plat_client') {
            $bianhao = Db::table('plat_client')->where("keynum= '$basekeynum' ")->value('clientnum');
        } else if ($tablename == 'client_customer') {
            $bianhao = Db::table('client_customer')->where("keynum= '$basekeynum' ")->value('customer_code');
        } else if ($tablename == 'client_supplier') {
            $bianhao = Db::table('client_supplier')->where("keynum= '$basekeynum' ")->value('supplier_code');
        }
        $front = $bianhao;
        $front = empty($front) == false ? $front . '-' : '';
        $this->assign('front', $front);
        //页面需要编号拼接账号

        $keynum = $param['keynum'];
        $plat_accountinfo =  Db::table('plat_account')->where("keynum ='$keynum' ")->find();
        $this->assign('info', $plat_accountinfo);

        //获取分类
        $cntree = Db::table('plat_org')->field('org_id as id, parentid as  pid, orgname as  name')->order('o desc')->where("basekeynum='$basekeynum'")->select();
        $tree = new Tree($cntree);
        $str = "<option value=\$id \$selected>\$spacer\$name</option>";
        //生成的形式
        $cntree = $tree->get_tree(0, $str, $plat_accountinfo['orgid']);
        $this->assign('cntree', $cntree);
        //导航
        //获取角色信息
        //取出该账户可选的角色列表
        $plat_rolealllist =  Db::table('plat_role')->where("basekeynum= '$basekeynum' ")->order('o desc')->select();
        foreach ($plat_rolealllist as $k => $v) {
            $arr['name'] = $v['role_name'];
            $arr['value'] = $v['keynum'];
            $role_json[] = $arr;
        }
        if (empty($role_json)) {
            $role_json1 = '[]';
        } else {
            $role_json1 = json_encode($role_json);
        }
        $this->assign('role_json', $role_json1);
        //取出角色选中的值
        $rolekeylist = Db::table('plat_account')->where('keynum', $keynum)->value('rolekeylist');
        $rolekeylist_arr = explode(',', $rolekeylist);
        $plat_rolealllist_checked =  Db::table('plat_role')->whereIn('keynum', $rolekeylist_arr)->order('o desc')->select();
        foreach ($plat_rolealllist_checked as $k1 => $v1) {
            $arr1['name'] = $v1['role_name'];
            $arr1['value'] = $v1['keynum'];
            $role_json_checked[] = $arr1;
        }
        $this->assign('role_json_checked', json_encode($role_json_checked));
        return $this->fetch('edit_admin');
    }

    /**
     * 修改账号操作
     */

    public function ajax_edit_admin()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/adminlist', 1);
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];

        //当前登陆者的basekeynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        //账号前面拼接上平台客户编号
        //取出来唯一编号，根据是那个表来获取编号
        $tablename = Db::table('plat_account')->where("basekeynum= '$basekeynum' ")->value('tablename');
        if ($tablename == 'plat_client') {
            $bianhao = Db::table('plat_client')->where("keynum= '$basekeynum' ")->value('clientnum');
        } else if ($tablename == 'client_customer') {
            $bianhao = Db::table('client_customer')->where("keynum= '$basekeynum' ")->value('customer_code');
        } else if ($tablename == 'client_supplier') {
            $bianhao = Db::table('client_supplier')->where("keynum= '$basekeynum' ")->value('supplier_code');
        }

        $front = $bianhao;
        $front = empty($front) == false ? $front . '-' : '';
        //账号前面拼接上平台客户编号 结束
        //判断账号必须只能是字母+数字
        if (!ctype_alnum($param['accountname'])) {
            $data['sta'] = 0;
            $data['msg'] = '账号必须是数字或者字母的组合！';
            echo json_encode($data);
            die;
        }
        // $front = "";
        $accountname = $front . $param['accountname'];
        //判断账号不能重复
        $count = Db::table('plat_account')->where("keynum !='$keynum' and  accountname='$accountname'")->count();
        if ($count >= 1) {
            $rt['sta'] = '0';
            $rt['msg'] = '该账号已经存在！';
            echo json_encode($rt);
            die;
        }
        $accountpassowrd = trim($param['accountpassowrd']);
        $accountrealname = $param['accountrealname'];
        $accountphone = $param['accountphone'];
        $o = $param['o'];
        $remark = $param['remark'];
        $org_id = $param['org_id'];
        //通过部门节点取出部门信息
        $plat_orginfo = Db::table('plat_org')->where("org_id='$org_id'")->find();
        $data['orgkeynum'] = $plat_orginfo['keynum'];
        $data['orgid'] = $plat_orginfo['org_id'];

        $data['accountname'] = $accountname;
        if ($accountpassowrd != '') {
            $data['accountpassword'] = password($accountpassowrd);
        }
        $data['accountrealname'] = $accountrealname;
        $data['accountphone'] = $accountphone;
        $data['o'] = $o;
        $data['remark'] = $remark;
        Db::table('plat_account')->where('keynum', $keynum)->update($data);
        addoperatelog('修改系统用户', json_encode($param, JSON_UNESCAPED_UNICODE));

        //修改角色信息
        $rolelist = $param['rolelist'];
        $rolekeynums = "";
        $role_ids = "";
        foreach ($rolelist as $k => $v) {
            $rolekeynums .= $v['value'] . ',';
            $role_id = Db::table('plat_role')->where('keynum', $v['value'])->value('role_id');
            $role_ids .= $role_id . ',';
        }
        $rolekeynums = substr($rolekeynums, 0, -1);
        $role_ids = substr($role_ids, 0, -1);
        $datarole['rolekeylist'] = $rolekeynums;
        $datarole['roleidlist'] = $role_ids;
        Db::table('plat_account')->where("keynum='$keynum'")->update($datarole);

        $rt['sta'] = 1;
        $rt['msg'] = '修改成功！';
        echo json_encode($rt);
        die;
    }

    /**
     * 新增账号页面
     */

    public function add_admin()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/adminlist', 0);
        $request = Request::instance();
        $param = $request->param();
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        //取出来唯一编号，根据是那个表来获取编号
        $tablename = Db::table('plat_account')->where("basekeynum= '$basekeynum' ")->value('tablename');
        if ($tablename == 'plat_client') {
            $bianhao = Db::table('plat_client')->where("keynum= '$basekeynum' ")->value('clientnum');
        } else if ($tablename == 'client_customer') {
            $bianhao = Db::table('client_customer')->where("keynum= '$basekeynum' ")->value('customer_code');
        } else if ($tablename == 'client_supplier') {
            $bianhao = Db::table('client_supplier')->where("keynum= '$basekeynum' ")->value('supplier_code');
        }
        $front = $bianhao;
        $front = empty($front) == false ? $front . '-' : '';
        $this->assign('front', $front);
        //页面需要编号拼接账号

        //这个keynum是组织的keynum
        $keynum = $param['keynum'];
        $plat_orginfo = Db::table('plat_org')->where("keynum ='$keynum' ")->find();
        //获取分类
        $cntree = Db::table('plat_org')->field('org_id as id, parentid as  pid, orgname as  name')->order('o desc')->where("basekeynum='$basekeynum'")->select();
        $tree = new Tree($cntree);
        $str = "<option value=\$id \$selected>\$spacer\$name</option>";
        //生成的形式
        $cntree = $tree->get_tree(0, $str, $plat_orginfo['org_id']);
        $this->assign('cntree', $cntree);
        //导航

        //获取角色信息
        //取出该账户可选的角色列表
        $plat_rolealllist =  Db::table('plat_role')->where("basekeynum= '$basekeynum' ")->order('o desc')->select();
        foreach ($plat_rolealllist as $k => $v) {
            $arr['name'] = $v['role_name'];
            $arr['value'] = $v['keynum'];
            $role_json[] = $arr;
        }
        if (empty($role_json)) {
            $role_json1 = '[]';
        } else {
            $role_json1 = json_encode($role_json, JSON_UNESCAPED_UNICODE);
        }
        $this->assign('role_json', $role_json1);
        return $this->fetch('add_admin');
    }

    /**
     * 新增账号操作
     */

    public function ajax_add_admin()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/adminlist', 1);
        $request = Request::instance();
        $param = $request->param();

        //当前登陆者的basekeynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        //账号前面拼接上平台客户编号
        //取出来唯一编号，根据是那个表来获取编号
        $tablename = Db::table('plat_account')->where("basekeynum= '$basekeynum' ")->value('tablename');
        if ($tablename == 'plat_client') {
            $bianhao = Db::table('plat_client')->where("keynum= '$basekeynum' ")->value('clientnum');
        } else if ($tablename == 'client_customer') {
            $bianhao = Db::table('client_customer')->where("keynum= '$basekeynum' ")->value('customer_code');
        } else if ($tablename == 'client_supplier') {
            $bianhao = Db::table('client_supplier')->where("keynum= '$basekeynum' ")->value('supplier_code');
        } else if ($tablename == 'shop') {
            $bianhao = Db::table('shop')->where("keynum= '$basekeynum' ")->value('code');
        }
        $front = $bianhao;
        $front = empty($front) == false ? $front . '-' : '';
        //账号前面拼接上平台客户编号 结束
        //判断账号必须只能是字母+数字
        if (!ctype_alnum($param['accountname'])) {
            $data['sta'] = 0;
            $data['msg'] = '账号必须是数字或者字母的组合！';
            echo json_encode($data);
            die;
        }
        // $front = "";
        $accountname = $front . $param['accountname'];
        //判断账号不能重复
        $plat_accountinfo = Db::table('plat_account')->where("accountname='$accountname'")->find();
        if ($plat_accountinfo['keynum']) {
            $rt['sta'] = 0;
            $rt['msg'] = '该账号已经存在！';
            echo json_encode($rt);
            die;
        }
        $accountpassowrd = trim($param['accountpassowrd']);
        $accountrealname = $param['accountrealname'];
        $accountphone = $param['accountphone'];
        $o = $param['o'];
        $remark = $param['remark'];
        $org_id = $param['org_id'];
        //通过部门节点取出部门信息
        $plat_orginfo = Db::table('plat_org')->where("org_id='$org_id'")->find();

        $data['keynum'] = create_guid();
        $data['basekeynum'] = $basekeynum;
        $data['orgkeynum'] = $plat_orginfo['keynum'];
        $data['orgid'] = $plat_orginfo['org_id'];
        $data['accountname'] = $accountname;
        $data['accountpassword'] = password($accountpassowrd);
        $data['accountrealname'] = $accountrealname;
        $data['accountphone'] = $accountphone;
        $data['tablename'] = 'plat_admin';
        $data['o'] = $o;
        $data['remark'] = $remark;

        //获取添加者的account信息
        $keynum = session('cn_accountinfo.keynum');
        $adderbaseinfo = Db::table('plat_account')->where("keynum='$basekeynum'")->find();
        $adderinfo = Db::table('plat_account')->where("keynum='$keynum'")->find();

        $data['basekeynum'] =  $adderbaseinfo['keynum'] ? $adderbaseinfo['keynum'] : '平台';
        $data['parent_basekeynum'] = $adderbaseinfo['parent_basekeynum'] ? $adderbaseinfo['parent_basekeynum'] : '无';
        if ($data['parent_basekeynum'] == '平台') {
            $data['parent_baseid'] = '0';
        } else {
            $data['parent_baseid'] = $adderbaseinfo['parent_baseid'];
        }
        $data['allpath_basekeynum'] = $adderbaseinfo['allpath_basekeynum'] ? $adderbaseinfo['allpath_basekeynum'] : '平台';
        $data['parent_keynum'] = $adderinfo['keynum'];
        $data['allpath_keynum'] = $adderinfo['allpath_keynum'] . ',' . $data['keynum'];
        Db::table('plat_account')->insert($data);

        //向plat_admin表插入数据
        $data1['keynum'] = $data['keynum'];
        $data1['basekeynum'] = $basekeynum;
        $data1['accountname'] = $accountname;
        $data1['realname'] = '';
        $data1['linkphone'] = '';
        $data1['email'] = '';
        $data1['qq'] = '';
        $data1['linkaddress'] = '';
        Db::table('plat_admin')->insert($data1);
        addoperatelog('添加系统用户', json_encode($param, JSON_UNESCAPED_UNICODE));

        //修改角色信息
        $rolelist = $param['rolelist'];
        $rolekeynums = "";
        $role_ids = "";
        foreach ($rolelist as $k => $v) {
            $rolekeynums .= $v['value'] . ',';
            $role_id = Db::table('plat_role')->where('keynum', $v['value'])->value('role_id');
            $role_ids .= $role_id . ',';
        }
        $rolekeynums = substr($rolekeynums, 0, -1);
        $role_ids = substr($role_ids, 0, -1);
        $datarole['rolekeylist'] = $rolekeynums;
        $datarole['roleidlist'] = $role_ids;
        $accountkeynum = $data['keynum'];
        Db::table('plat_account')->where("keynum='$accountkeynum'")->update($datarole);

        $rt['sta'] = 1;
        $rt['msg'] = '添加成功';
        echo json_encode($rt);
        die;
    }

    /**
     * 编辑角色页面
     */

    public function edit_accountrole()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/adminlist', 0);
        $request = Request::instance();
        $param = $request->param();
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        //取出该账户可选的角色列表
        $plat_rolealllist =  Db::table('plat_role')->where("basekeynum= '$basekeynum' ")->order('o desc')->select();
        $this->assign('plat_rolealllist', $plat_rolealllist);

        //选择的账号已经存在的角色keynum
        $keynum = $param['keynum'];
        $plat_accountinfo = Db::table('plat_account')->where("keynum='$keynum'")->find();
        $this->assign('accountrolekeylist', $plat_accountinfo['rolekeylist']);
        return  $this->fetch('edit_accountrole');
    }

    /**
     * ajax修改账号的角色
     */

    public function ajax_edit_accountrole()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/adminlist', 1);
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];
        $rolekeynums = $param['rolekeynums'];
        $role_ids = $param['role_ids'];

        $data['rolekeylist'] = $rolekeynums;
        $data['roleidlist'] = $role_ids;

        Db::table('plat_account')->where("keynum='$keynum'")->update($data);

        addoperatelog('修改用户角色', json_encode($param, JSON_UNESCAPED_UNICODE));
        $rt['sta'] = 1;
        $rt['msg'] = '修改成功';
        echo json_encode($rt);
        die;
    }

    /**
     * 账户登录日志列表
     */

    public function loginrecordlist()
    {

        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/loginrecordlist', 1);
            //平台客户的keynum
            $basekeynum = session('cn_accountinfo.basekeynum');
            $p = $param['page'] ? $param['page'] : 1;
            $pagesize = $param['limit'];
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量
            $accountname = isset($param['accountname']) ? $param['accountname'] : '';
            $where = "1 = 1  and basekeynum='$basekeynum' ";
            if ($accountname) {
                $where .= "and  accountname  like '%$accountname%' ";
            }
            //默认按照时间降序
            $count = Db::table('plat_accountloginlog')->where($where)->count();
            $list = Db::table('plat_accountloginlog')->where($where)->order('login_id', 'desc')->limit($offset . ',' . $pagesize)->select();
            $rtdata['data'] = $list;
            $rtdata['count'] = $count;
            $rtdata['code'] = 0;
            $rtdata['msg'] = '';
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/loginrecordlist', 0);
            return  $this->fetch('loginrecordlist');
        }
    }

    /**
     * 账户操作日志列表
     */

    public function operaterecordlist()
    {

        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/operaterecordlist', 1);
            //平台客户的keynum
            $basekeynum = session('cn_accountinfo.basekeynum');
            $accountname = isset($param['accountname']) ? $param['accountname'] : '';
            $where = "1 = 1  and basekeynum='$basekeynum' ";
            if ($accountname) {
                $where .= "and  accountname  like '%$accountname%' ";
            }
            $p = isset($param['page']) ? intval($param['page']) : '1';
            $pagesize = $param['limit'];
            #每页数量
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量
            $count = Db::table('plat_accountoperationlog')->where($where)->count();
            $list = Db::table('plat_accountoperationlog')->where($where)->order('operate_id', 'desc')->limit($offset . ',' . $pagesize)->select();
            $rtdata['data'] = $list;
            $rtdata['count'] = $count;
            $rtdata['code'] = 0;
            $rtdata['msg'] = '';
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/operaterecordlist', 0);
            return $this->fetch('operaterecordlist');
        }
    }

    /**
     * 菜单列表
     */

    public function menulist()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/menulist', 0);
        return $this->fetch('menulist');
    }

    /**
     * 获取当前节点菜单账号列表
     */

    public function get_menu_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/menulist', 1);

        $request = Request::instance();
        $param = $request->param();
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        //计算记录偏移量

        $tree_menu_id = isset($param['tree_menu_id']) ? $param['tree_menu_id'] : '';
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $where = '1=1 ';
        if ($tree_menu_id != '') {
            $where .= " and FIND_IN_SET('$tree_menu_id',path_id)";
        }
        if ($field == 'menu_name') {
            $where .= " and menu_name  like '%$keyword%' ";
        } else if ($field == 'orgname') {
            $where .= " and url  like '%$keyword%' ";
        } else if ($field == 'level') {
            $where .= " and level  = '$keyword' ";
        }
        $plat_menulist =  Db::table('plat_menu')->where($where)->order('O asc ')->limit($offset . ',' . $pagesize)->select();
        $count =  Db::table('plat_menu')->where($where)->order('O asc ')->limit($offset . ',' . $pagesize)->count();

        $rtdata['data'] = $plat_menulist;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        echo json_encode($rtdata);
        die;
    }

    /**
     * 给菜单添加path_id
     */

    public function ajax_add_menu_pathid()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/menulist', 1);
        $request = Request::instance();
        $param = $request->param();
        $plat_menulist = Db::table('plat_menu')->select();
        foreach ($plat_menulist  as $key => $row) {
            $level = $row['level'];
            $pid = $row['pid'];
            $menu_id = $row['menu_id'];
            if ($level == '1') {
                $path_id = $menu_id;
            } else if ($level == '2') {
                $path_id = $pid . ',' . $menu_id;
            } else if ($level == '3') {
                $plat_menuinfo = Db::table('plat_menu')->where("menu_id='$pid'")->find();
                $path_id = $plat_menuinfo['pid'] . ',' . $pid . ',' . $menu_id;
            } else {
                $rtdata['sta'] = 0;
                $rtdata['msg'] = 'menu_id等于' . $menu_id . '的菜单添加path_id失败。';
                echo json_encode($rtdata);
                die;
            }
            $data['path_id'] = $path_id;
            Db::table('plat_menu')->where('menu_id', $menu_id)->update($data);
        }
        $rtdata['sta'] = 1;
        $rtdata['msg'] = 'path_id批量添加成功！';
        addoperatelog('给菜单添加path_id', json_encode($param, JSON_UNESCAPED_UNICODE));
        echo json_encode($rtdata);
        die;
    }

    /**
     * 编辑菜单页面
     */

    public function edit_menu()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/menulist', 0);
        $request = Request::instance();
        $param = $request->param();
        $menu_id = $param['menu_id'];
        $plat_menuinfo = Db::table('plat_menu')->where("menu_id ='$menu_id' ")->find();
        $this->assign('info', $plat_menuinfo);
        return $this->fetch('edit_menu');
    }

    /**
     * 修改菜单ajax保存
     */

    public function ajax_edit_menu()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/menulist', 1);
        $request = Request::instance();
        $param = $request->param();
        $menu_id = isset($param['menu_id']) ? $param['menu_id'] : '';
        $menu_name = isset($param['menu_name']) ? $param['menu_name'] : '';
        $url = isset($param['url']) ? $param['url'] : '';
        $icon = isset($param['icon']) ? $param['icon'] : '';
        $level = isset($param['level']) ? $param['level'] : '';
        $is_show = isset($param['is_show']) ? $param['is_show'] : '';
        $is_changyong = isset($param['is_changyong']) ? $param['is_changyong'] : '';
        $o = isset($param['o']) ? $param['o'] : '';
        $remark = isset($param['remark']) ? $param['remark'] : '';
        $is_divider_before = isset($param['is_divider_before']) ? $param['is_divider_before'] : '';
        $data['menu_name'] = $menu_name;
        $data['url'] = $url;
        $data['icon'] = $icon;
        $data['level'] = $level;
        $data['is_show'] = $is_show;
        $data['is_changyong'] = $is_changyong;
        $data['o'] = $o;
        $data['remark'] = $remark;
        $data['is_divider_before'] = $is_divider_before;
        Db::table('plat_menu')->where('menu_id', $menu_id)->update($data);
        $rt['sta'] = 1;
        $rt['msg'] = '修改成功！';
        addoperatelog('修改菜单ajax保存', json_encode($param, JSON_UNESCAPED_UNICODE));
        echo json_encode($rt);
        die;
    }

    /**
     * 添加菜单页面
     */

    public function add_menu()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/menulist', 0);
        return $this->fetch('add_menu');
    }
    /**
     * ajax添加菜单
     */

    public function ajax_add_menu()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/menulist', 1);
        $request = Request::instance();
        $param = $request->param();
        $tree_menu_id = isset($param['tree_menu_id']) ? $param['tree_menu_id'] : '';
        $menu_name = isset($param['menu_name']) ? $param['menu_name'] : '';
        $url = isset($param['url']) ? $param['url'] : '';
        $icon = isset($param['icon']) ? $param['icon'] : '';
        $level = isset($param['level']) ? $param['level'] : '';
        $is_show = isset($param['is_show']) ? $param['is_show'] : '';
        $o = isset($param['o']) ? $param['o'] : '';
        $remark = isset($param['remark']) ? $param['remark'] : '';
        $is_changyong = isset($param['is_changyong']) ? $param['is_changyong'] : '0';
        $is_divider_before = isset($param['is_divider_before']) ? $param['is_divider_before'] : '0';

        //取出传过来的组织信息
        $plat_menufo = Db::table('plat_menu')->where("menu_id='$tree_menu_id'")->find();

        $data['pid'] = $plat_menufo['menu_id'];
        $data['level'] = $level;
        $data['menu_name'] = $menu_name;
        $data['url'] = $url;
        $data['o'] = $o;
        $data['icon'] = $icon;
        $data['remark'] = $remark;
        $data['is_show'] = $is_show;
        $data['is_changyong'] = $is_changyong;
        $data['is_divider_before'] = $is_divider_before;

        $plat_menuid = Db::table('plat_menu')->insertGetId($data);
        $data1['path_id'] = $plat_menufo['path_id'] . ',' . $plat_menuid;
        Db::table('plat_menu')->where('menu_id', $plat_menuid)->update($data1);
        addoperatelog('ajax添加菜单', json_encode($param, JSON_UNESCAPED_UNICODE));
        $rt['sta'] = 1;
        $rt['msg'] = '添加成功';
        echo json_encode($rt);
        die;
    }

    /**
     * 删除菜单
     */

    public function ajax_delmenus()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/menulist', 1);
        $request = Request::instance();
        $param = $request->param();
        $menu_ids = $param['menu_ids'];

        //把传过来的逗号拼接的字符串切成数组
        $arrmenu_ids = explode(',', $menu_ids);
        $where['menu_id'] = array('in', $arrmenu_ids);

        Db::table('plat_menu')->where($where)->delete();
        addoperatelog('ajax删除菜单', json_encode($param, JSON_UNESCAPED_UNICODE));
        $rt['sta'] = 1;
        $rt['msg'] = '删除成功';
        echo json_encode($rt);
        die;
    }

    /**
     * 清理菜单的redis缓存
     */
    public  function ajax_clear_menu_cache()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/menulist', 1);
        $request = Request::instance();
        $param = $request->param();
        //清理tag为cn_menu的记录
        Cache::clear('cn_menu');
        $rt['sta'] = 1;
        $rt['msg'] = '清理成功！';
        echo json_encode($rt);
        addoperatelog('清理菜单的redis缓存', json_encode($param, JSON_UNESCAPED_UNICODE));
        die;
    }

    /*
    * 重置页面
    */

    public function  password()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        //check_auth( request()->controller(). '/customerlist', 0 );
        return $this->fetch('password');
    }

    /*
    * 重置密码
    */

    public function ajax_reset_password()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        //check_auth( request()->controller(). '/customerlist', 1 );
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];
        $save['accountpassword'] = password($_POST['password']);
        Db::table('plat_account')->where('keynum', $keynum)->update($save);
        $return_arr['sta'] = 1;
        $return_arr['msg'] = '操作成功';
        addoperatelog('ajax重置系统用户密码', json_encode($param, JSON_UNESCAPED_UNICODE));
        $return_arr['url'] = url('customerlist');
        echo json_encode($return_arr);
    }

    /**
     * 获取菜单无限极分类树
     */

    public function get_menu_treedata()
    {
        $plat_menulist = Db::table('plat_menu')->field('menu_id as id ,pid as `parentId`,menu_name as name')->select();
        $new = Get_Tree($plat_menulist, 0);
        echo json_encode($new);
        die;
    }

    /**
     * 获取菜单无限极分类树new
     */

    public function get_menu_treedata_new()
    {

        $plat_orglist =  Db::table('plat_menu')->field('menu_id as id ,pid as `parentId`,menu_name as name ,path_id as allpath,level as level')->order('o')->select();

        echo json_encode($plat_orglist);
    }

    /**
     * 平台基本设置
     */

    public function platconfig()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/platconfig', 0);
        //平台客户的keynum
        //当前登陆者的basekeynum
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $basekeynum = session('cn_accountinfo.keynum');
        //账号前面拼接上平台客户编号
        //取出来唯一编号，根据是那个表来获取编号
        $tablename = Db::table('plat_account')->where("basekeynum= '$basekeynum' ")->value('tablename');
        $this->assign('tablename', $tablename);
        $info = Db::table('plat_config')->where("basekeynum='$basekeynum'")->find();
        $this->assign('info', $info);
        return $this->fetch('platconfig');
    }

    /**
     * 保存设置
     */

    public function ajax_saveplatconfig()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/platconfig', 1);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $request = Request::instance();
        $param = $request->param();
        //先查询有没有没有则新增
        $info = Db::table('plat_config')->where('basekeynum', $basekeynum)->find();
        if (empty($info)) {
            $data['keynum'] = create_guid();
            $data['titlecn'] = $param['titlecn'];
            $data['copyright'] = $param['copyright'];
            $data['background_image'] = $param['background_image'];
            $data['basekeynum'] = $basekeynum;
            Db::table('plat_config')->insert($data);
            $rt['sta'] = 1;
            $rt['msg'] = '新增成功';
            echo json_encode($rt);
            addoperatelog('新增系统基本设置', json_encode($param, JSON_UNESCAPED_UNICODE));
            die;
        } else {
            Db::table('plat_config')->where('basekeynum', $basekeynum)->update($param);

            $rt['sta'] = 1;
            $rt['msg'] = '保存成功';
            echo json_encode($rt);
            addoperatelog('修改系统基本设置', json_encode($param, JSON_UNESCAPED_UNICODE));
            die;
        }
    }

    /**
     * 组织机构列表
     */

    public function orglist()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/orglist', 0);
        return $this->fetch('orglist');
    }

    /**
     * 获取组织机构无限极分类树
     */

    public function get_org_treedata()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $plat_orglist = Db::table('plat_org')->field('org_id as id ,parentid as `parentId`,orgname as name ,keynum as keynum')->where("basekeynum='$basekeynum'")->order('O asc ')->select();
        $new = Get_Tree($plat_orglist, '');
        echo json_encode($new);
    }

    /**
     * 获取组织机构无限极分类树new
     */

    public function get_org_treedata_new()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $plat_orglist = Db::table('plat_org')->field('org_id as id ,parentid as `parentId`,orgname as name ,keynum as keynum')->where("basekeynum='$basekeynum'")->order('O asc ')->select();
        echo json_encode($plat_orglist);
    }

    /**
     * 客户系统管理
     */

    public function customer_systemlist()
    {

        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/customer_systemlist', 1);
            $sitename = isset($param['keyword']) ? $param['keyword'] : '';
            $order = isset($param['o']) ? $param['o'] : 'DESC';
            $where = ' 1 = 1  ';
            if ($sitename) {
                $where .= "and  sitename  like '%$sitename%' ";
            }

            $p = isset($param['page']) ? intval($param['page']) : '1';
            $pagesize = $param['limit'];
            #每页数量
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量

            $count = Db::table('plat_site')->where($where)->count();
            $list = Db::table('plat_site')->where($where)->order('o')->limit($offset . ',' . $pagesize)->select();
            $rtdata['data'] = $list;
            $rtdata['count'] = $count;
            $rtdata['code'] = 0;
            $rtdata['msg'] = '';
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/customer_systemlist', 0);
            return $this->fetch('customer_systemlist');
        }
    }

    /**
     * 给客户系统设置菜单页面
     */

    public function site_pur_form()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/customer_systemlist', 0);
        return $this->fetch('site_pur_form');
    }

    /**
     * 系统菜单
     */

    public function site_menu_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/customer_systemlist', 1);
        $request = Request::instance();
        $param = $request->param();
        //取出当前角色已经勾选的菜单id放入到数组里面
        $keynum = $param['keynum'];
        $plat_siteinfo = Db::table('plat_site')->where(" keynum='$keynum' ")->find();
        $purviewlist_ids = $plat_siteinfo['purviewlist_id'];
        $purviewlist_idarr = explode(',', $purviewlist_ids);

        $where['is_show'] = '1';
        $menulist = Db::table('plat_menu')->where($where)->order('o asc')->select();
        $arrlist = $this->arrayPidProcess($menulist, array(), '0', '3', $purviewlist_idarr);
        echo json_encode($arrlist);
        die;
    }

    /**
     * ajax编辑系统菜单
     */

    public function ajax_eidt_site_menu()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/customer_systemlist', 1);
        $request = Request::instance();
        $param = $request->param();
        //角色的keynum
        $keynum = $param['keynum'];
        $menumidstr = $param['menumidstr'];

        $data['purviewlist_id'] = $menumidstr;
        $flag = Db::table('plat_site')->where('keynum', $keynum)->update($data);

        addoperatelog('编辑系统菜单权限', json_encode($param, JSON_UNESCAPED_UNICODE));
        $rt['sta'] = 1;
        $rt['msg'] = '编辑成功！';
        echo json_encode($rt);
        die;
    }

    //总平台基本设置

    public function system_set()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/system_set', 0);
        $info = Db::table('plat_system_set')->where("id ='1' ")->find();
        $this->assign('info', $info);
        return $this->fetch('system_set');
    }

    //修改方法

    public function ajax_system_set()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/system_set', 1);
        $request = Request::instance();
        $param = $request->param();
        //print_r( $param );
        $data['client_version'] = isset($param['client_version']) ? $param['client_version'] : '';
        $data['client_service_time'] = isset($param['client_service_time']) ? $param['client_service_time'] : '';
        $data['client_service_phone'] = isset($param['client_service_phone']) ? $param['client_service_phone'] : '';
        $data['client_service_qrcode'] = isset($param['client_service_qrcode']) ? $param['client_service_qrcode'] : '';
        $data['client_service_desc'] = isset($param['client_service_desc']) ? $param['client_service_desc'] : '';
        //$data['client_author_words'] = isset( $param['client_author_words'] )?$param['client_author_words']:'';
        $data['client_author_words'] = isset($param['editorcontent']) ? $param['editorcontent'] : '';
        $data['ali_accesskeyid'] = isset($param['ali_accesskeyid']) ? $param['ali_accesskeyid'] : '';
        $data['ali_accesskeysecret'] = isset($param['ali_accesskeysecret']) ? $param['ali_accesskeysecret'] : '';
        $data['ali_endpoint'] = isset($param['ali_endpoint']) ? $param['ali_endpoint'] : '';
        $data['ali_bucket'] = isset($param['ali_bucket']) ? $param['ali_bucket'] : '';
        $data['ali_bucket_url'] = isset($param['ali_bucket_url']) ? $param['ali_bucket_url'] : '';
        $data['lizengbang_api_url'] = isset($param['lizengbang_api_url']) ? $param['lizengbang_api_url'] : '';
        
        $data['mod_time'] = time();
        $rs = Db::table('plat_system_set')->where("id='1'")->update($data);
        addoperatelog('编辑总平台基本设置', json_encode($param, JSON_UNESCAPED_UNICODE));
        if ($rs) {
            $r['msg'] = '修改成功';
            $r['sta'] = 1;
            echo json_encode($r);
            die;
        } else {
            $r['msg'] = '修改失败';
            $r['sta'] = 0;
            echo json_encode($r);
            die;
        }
    }

    //关于我们设置

    public function aboutus()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/aboutus', 0);
        $info = Db::table('plat_system_set')->where("id ='1' ")->find();
        $this->assign('info', $info);
        return $this->fetch('aboutus');
    }

    //关于我们设置方法

    public function ajax_aboutus()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/aboutus', 1);
        $request = Request::instance();
        $param = $request->param();

        $data['aboutus1'] = isset($param['editorcontent1']) ? $param['editorcontent1'] : '';
        $data['aboutus2'] = isset($param['editorcontent2']) ? $param['editorcontent2'] : '';
        $data['mod_time'] = time();
        $rs = Db::table('plat_system_set')->where("id='1'")->update($data);

        $tpl1 = '<div class="layui-card-header">关于我们</div><div class="layui-card-body layui-text layadmin-about"><blockquote class="layui-elem-quote" style="border: none;">' . $data['aboutus1'] . '</blockquote><p>© 2020   真诚软件版权所有</p></div>';

        $tpl2 = '<div class="layui-card-header">关于我们</div><div class="layui-card-body layui-text layadmin-about"><blockquote class="layui-elem-quote" style="border: none;">' . $data['aboutus2'] . '</blockquote><p>© 2020   真诚软件版权所有</p></div>';

        file_put_contents(ROOT_PATH . 'public__STATIC__/admin/tpl/system/aboutus1.html', $tpl1);
        file_put_contents(ROOT_PATH . 'public__STATIC__/admin/tpl/system/aboutus2.html', $tpl2);
        $rs = 1;
        addoperatelog('编辑关于我们设置', json_encode($param, JSON_UNESCAPED_UNICODE));
        if ($rs) {
            $r['msg'] = '修改成功';
            $r['sta'] = 1;
            echo json_encode($r);
            die;
        }
    }

    //快递100基本设置页面

    public function kuaidi100_set()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/kuaidi100_set', 0);
        $request = Request::instance();
        $param = $request->param();
        if (session('cn_accountinfo.basekeynum') == '平台') {
            $keynum = $param['keynum'];
        } else {
            $keynum = session('cn_accountinfo.basekeynum');
        }
        $this->assign('keynum', $keynum);
        $info = Db::table('plat_kuaidi100_set')->where("basekeynum='$keynum'")->find();
        $this->assign('info', $info);
        return $this->fetch('kuaidi100_set');
    }

    public function ajax_kuaidi100_set()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/kuaidi100_set', 1);
        $request = Request::instance();
        $param = $request->param();
        $keynum = isset($param['keynum']) ? $param['keynum'] : '';
        $data['callbackurl'] = isset($param['callbackurl']) ? $param['callbackurl'] : '';
        $data['customer'] = isset($param['customer']) ? $param['customer'] : '';
        $data['key'] = isset($param['key']) ? $param['key'] : '';
        $data['up_time'] = time();

        if (isset($param['check_status']) &&  $param['check_status'] == 'on') {
            $data['check_status'] = 1;
        } else {
            $data['check_status'] = 0;
        }
        if (isset($param['subscribe_status']) &&  $param['subscribe_status'] == 'on') {
            $data['subscribe_status'] = 1;
        } else {
            $data['subscribe_status'] = 0;
        }
        $info = Db::table('plat_kuaidi100_set')->where("basekeynum='$keynum'")->find();
        if ($info) {
            $rs = Db::table('plat_kuaidi100_set')->where("basekeynum='$keynum'")->update($data);
        } else {
            $data['basekeynum'] = $keynum;
            $rs = Db::table('plat_kuaidi100_set')->insert($data);
        }
        addoperatelog('修改快递100基本设置参数', json_encode($param, JSON_UNESCAPED_UNICODE));
        if ($rs) {
            $r['msg'] = '保存成功';
            $r['sta'] = 1;
            echo json_encode($r);
            die;
        }
    }

    //短信发送日志
    public function  kuaidi100_tuisong_log_list()
    {

        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/kuaidi100_tuisong_log_list', 1);
            //平台客户的keynum
            //$basekeynum = session( 'cn_accountinfo.basekeynum' );
            $basekeynum = $param['basekeynum'];
            $p = $param['page'] ? $param['page'] : 1;
            $pagesize = $param['limit'];
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量
            $id = isset($param['id']) ? $param['id'] : '';
            $words = isset($param['words']) ? $param['words'] : '';
            $order_sn = isset($param['order_sn']) ? $param['order_sn'] : '';
            $where = "1 = 1  and clientkeynum='$basekeynum' ";
            if ($id) {
                $where .= "and  id  = '$id' ";
            }
            if ($words) {
                $where .= "and  words  like '%$words%' ";
            }
            if ($order_sn) {
                $where .= "and  order_sn  like '%$order_sn%' ";
            }
            //默认按照时间降序
            $count = Db::table('plat_kuaidi100_tuisong_log')->where($where)->count();
            $list = Db::table('plat_kuaidi100_tuisong_log')->where($where)->order('id', 'desc')->limit($offset . ',' . $pagesize)->select();
            $rtdata['data'] = $list;
            $rtdata['count'] = $count;
            $rtdata['code'] = 0;
            $rtdata['msg'] = '';
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/kuaidi100_tuisong_log_list', 0);
            if (session('cn_accountinfo.basekeynum') == '平台') {
                $basekeynum = $param['keynum'];
            } else {
                $basekeynum = session('cn_accountinfo.basekeynum');
            }
            $this->assign('basekeynum', $basekeynum);
            return  $this->fetch('kuaidi100_tuisong_log_list');
        }
    }

    //系统定时任务查询

    public function system_cron_tpl()
    {
        $request = Request::instance();
        $param = $request->param();
        //平台客户的keynum
        check_auth(request()->controller() . '/system_cron_tpl', 0);
        if (session('cn_accountinfo.basekeynum') == '平台') {
            $basekeynum = $param['keynum'];
        } else {
            $basekeynum = session('cn_accountinfo.basekeynum');
        }
        $where = "basekeynum='$basekeynum' ";
        $list = Db::table('plat_cron_tpl')->where($where)->select();
        $this->assign('list', $list);
        return $this->fetch();
    }

    //定时任务日志
    public function  system_cron_log_list()
    {

        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/system_cron_log_list', 1);
            //平台客户的keynum
            //$basekeynum = session( 'cn_accountinfo.basekeynum' );
            $basekeynum = $param['basekeynum'];
            $p = $param['page'] ? $param['page'] : 1;
            $pagesize = $param['limit'];
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量
            $id = isset($param['id']) ? $param['id'] : '';
            $words = isset($param['words']) ? $param['words'] : '';
            $action = isset($param['action']) ? $param['action'] : '';
            $where = "1 = 1  and clientkeynum='$basekeynum' ";
            if ($id) {
                $where .= "and  id  = '$id' ";
            }
            if ($words) {
                $where .= "and  words  like '%$words%' ";
            }
            if ($action) {
                $where .= "and  action  like '%$action%' ";
            }
            //默认按照时间降序
            $count = Db::table('plat_cron_log')->where($where)->count();
            $list = Db::table('plat_cron_log')->where($where)->order('id', 'desc')->limit($offset . ',' . $pagesize)->select();
            $rtdata['data'] = $list;
            $rtdata['count'] = $count;
            $rtdata['code'] = 0;
            $rtdata['msg'] = '';
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/system_cron_log_list', 0);
            if (session('cn_accountinfo.basekeynum') == '平台') {
                $basekeynum = $param['keynum'];
            } else {
                $basekeynum = session('cn_accountinfo.basekeynum');
            }
            $this->assign('basekeynum', $basekeynum);
            return  $this->fetch('system_cron_log_list');
        }
    }

    //清理这个平台客户下面的redis缓存

    public function redis_cache_clear()
    {
        //平台客户的keynum
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = session('cn_accountinfo.basekeynum');
        if ($basekeynum == '平台') {
            $flag = Cache::clear();
            $flag = Cache::clear($basekeynum);
        } else {
            $flag = Cache::clear($basekeynum);
        }
        if ($flag == true) {
            $r['sta'] = 1;
            $r['msg'] = '清理redis缓存成功！';
            addoperatelog('清理redis缓存成功！', json_encode($param, JSON_UNESCAPED_UNICODE));
            echo json_encode($r);
            die;
        } else {
            $r['sta'] = 0;
            $r['msg'] = '清理redis缓存失败！';
            addoperatelog('清理redis缓存失败！', json_encode($param, JSON_UNESCAPED_UNICODE));
            echo json_encode($r);
            die;
        }
    }

    //敬请期待开发中

    public function developing()
    {
        return $this->fetch();
    }

    /**
     * 高级密码管理页面
     */
    public function super_password()
    {
        // 检查权限
        check_auth(request()->controller() . '/super_password', 0);

        // 获取当前客户信息
        $baseKeyNum = session('cn_accountinfo.basekeynum');
        $clientInfo = \app\admin\model\PlatClient::getInfoByBaseKeyNum($baseKeyNum);

        $this->assign('clientinfo', $clientInfo);
        return $this->fetch('super_password');
    }

    /**
     * 保存高级密码
     */
    public function ajax_save_super_password()
    {
        // 检查权限
        check_auth(request()->controller() . '/super_password', 1);

        $request = Request::instance();
        $param = $request->param();

        $oldPassword = isset($param['old_password']) ? $param['old_password'] : '';
        $superPassword = isset($param['super_password']) ? $param['super_password'] : '';
        $baseKeyNum = session('cn_accountinfo.basekeynum');

        // 获取当前客户信息
        $clientInfo = \app\admin\model\PlatClient::getInfoByBaseKeyNum($baseKeyNum);
        $savedPassword = isset($clientInfo['super_password']) ? $clientInfo['super_password'] : '';

        // 检查旧密码
        if (!empty($savedPassword)) {
            // 如果数据库中已有密码，需要验证旧密码
            if (empty($oldPassword)) {
                $data['sta'] = 0;
                $data['msg'] = '请输入旧密码';
                echo json_encode($data);
                die;
            }

            if (!password_verify($oldPassword, $savedPassword)) {
                $data['sta'] = 0;
                $data['msg'] = '旧密码不正确';
                echo json_encode($data);
                die;
            }
        }

        // 如果密码不为空，进行加密处理
        if (!empty($superPassword)) {
            $superPassword = password_hash($superPassword, PASSWORD_DEFAULT);
        }

        // 更新数据库
        $result = \app\admin\model\PlatClient::where(['keynum' => $baseKeyNum])->update([
            'super_password' => $superPassword
        ]);

        if ($result !== false) {
            $data['sta'] = 1;
            $data['msg'] = '保存成功';
            addoperatelog('修改高级密码成功', json_encode(['keynum' => $baseKeyNum], JSON_UNESCAPED_UNICODE));
        } else {
            $data['sta'] = 0;
            $data['msg'] = '保存失败';
        }

        echo json_encode($data);
        die;
    }

    /**
     * 验证高级密码
     */
    public function ajax_check_super_password()
    {
        $request = Request::instance();
        $param = $request->param();

        $inputPassword = isset($param['password']) ? $param['password'] : '';
        $baseKeyNum = session('cn_accountinfo.basekeynum');

        // 获取数据库中的高级密码
        $clientInfo = \app\admin\model\PlatClient::getInfoByBaseKeyNum($baseKeyNum);
        $savedPassword = isset($clientInfo['super_password']) ? $clientInfo['super_password'] : '';

        // 如果数据库中没有保存密码，则任何输入都视为正确
        if (empty($savedPassword)) {
            $data['sta'] = 1;
            $data['msg'] = '验证成功';
        } else {
            // 验证密码
            if (password_verify($inputPassword, $savedPassword)) {
                $data['sta'] = 1;
                $data['msg'] = '验证成功';
            } else {
                $data['sta'] = 0;
                $data['msg'] = '高级密码验证失败';
            }
        }

        echo json_encode($data);
        die;
    }

    public function wx_set()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];
        $this->assign('keynum', $keynum);
        $info = Db::table('plat_wechat_set')->where("basekeynum='$keynum'")->find();
        $this->assign('info', $info);
        return $this->fetch('wx_set');
    }

    public function shop_wx_set()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];
        $this->assign('keynum', $keynum);
        $info = Db::table('shop_wechat_set')->where("basekeynum='$keynum'")->find();
        $this->assign('info', $info);
        return $this->fetch('shop_wx_set');
    }

    public function ajax_shop_wx_set()
    {
        //平台客户的keynum
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = $param["keynum"];
        $where = "basekeynum='$basekeynum'";
        $find = Db::table("shop_wechat_set")->where($where)->find();
        $data = $param;
        unset($data['keynum']);
        $data["basekeynum"] = $basekeynum;
        if ($find) {
            $rs = Db::table("shop_wechat_set")->where($where)->update($data);
        } else {
            $rs = Db::table("shop_wechat_set")->insert($data);
        }
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    public function choose_one_config()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];
        $this->assign('keynum', $keynum);
        $info = Db::table('choose_one_config')->where("basekeynum='$keynum'")->find();
        $this->assign('info', $info);
        return $this->fetch('choose_one_config');
    }

    public function ajax_choose_one_config()
    {
        //平台客户的keynum
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = $param["keynum"];
        $where = "basekeynum='$basekeynum'";
        $find = Db::table("choose_one_config")->where($where)->find();
        $data = $param;
        unset($data['keynum']);
        $data["basekeynum"] = $basekeynum;
        if ($find) {
            $rs = Db::table("choose_one_config")->where($where)->update($data);
        } else {
            $rs = Db::table("choose_one_config")->insert($data);
        }
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    public function ajax_wx_set()
    {
        //平台客户的keynum
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = $param["keynum"];
        $where = "basekeynum='$basekeynum'";
        $find = Db::table("plat_wechat_set")->where($where)->find();
        $data = $param;
        unset($data['keynum']);
        $data["basekeynum"] = $basekeynum;
        if ($find) {
            $rs = Db::table("plat_wechat_set")->where($where)->update($data);
        } else {
            $rs = Db::table("plat_wechat_set")->insert($data);
        }
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    public function gx_set()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];
        $this->assign('keynum', $keynum);
        $info = Db::table('plat_client')->where("keynum='$keynum'")->find();
        $this->assign('info', $info);
        return $this->fetch('gx_set');
    }

    public function ajax_gx_set()
    {
        //平台客户的keynum
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = $param["keynum"];
        $where = "keynum='$basekeynum'";

        // if (isset($param['is_qrcode']) &&  $param['is_qrcode'] == 'on') {
        //     $data['is_qrcode'] = 1;
        // } else {
        //     $data['is_qrcode'] = 0;
        // }

        // if (isset($param['is_open_card']) &&  $param['is_open_card'] == 'on') {
        //     $data['is_open_card'] = 1;
        // } else {
        //     $data['is_open_card'] = 0;
        // }

        // if (isset($param['is_show_banner']) &&  $param['is_show_banner'] == 'on') {
        //     $data['is_show_banner'] = 1;
        // } else {
        //     $data['is_show_banner'] = 0;
        // }

        // if (isset($param['is_first_category']) &&  $param['is_first_category'] == 'on') {
        //     $data['is_first_category'] = 0;
        // } else {
        //     $data['is_first_category'] = 1;
        // }

        if (isset($param['is_open_card_self_pickup']) &&  $param['is_open_card_self_pickup'] == 'on') {
            $data['is_open_card_self_pickup'] = 0;
        } else {
            $data['is_open_card_self_pickup'] = 1;
        }

        $rs = Db::table("plat_client")->where($where)->update($data);
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    public function ajax_yuankong_login()
    {
        //平台客户的keynum
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = $param["keynum"];
        $password = $param["password"];
        if ($password != $this->gj_password) {
            $rt["sta"] = 0;
            $rt["msg"] = "高级密码错误";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        $url = db('plat_account')->where("keynum='$basekeynum'")->value("glurl");
        if (empty($url)) {
            $rt["sta"] = 0;
            $rt["msg"] = "获取链接错误";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        $ts = time();
        $url = "http://" . $url . "/admin.php/login/auto_login?keynum=" . $basekeynum .
            "&ts=" . $ts . "&sign=" . cre_aotologin_sign($basekeynum, $ts, "");
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        $rt["url"] = $url;
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    public function webconfig()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/webconfig', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');
        $info = db('webconfig')->where("clientkeynum='$basekeynum'")->find();
        $this->assign('info', $info);
        return $this->fetch('webconfig');
    }

    public function ajax_set_webconfig()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/webconfig', 0);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $request = Request::instance();
        $param = $request->param();
        $has = db('webconfig')->where("clientkeynum='$basekeynum'")->value("id");
        // 新增
        if ($has) {
            // 修改
            $save = $param;
            $rs = Db::table("webconfig")->where("clientkeynum='$basekeynum'")->update($save);
        } else {
            $add = $param;
            $add["clientkeynum"] = $basekeynum;
            $rs = Db::table("webconfig")->insert($add);
        }
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        addoperatelog('修改前台基本设置', json_encode($param, JSON_UNESCAPED_UNICODE));
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    public function wuliu_set()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];
        $this->assign('keynum', $keynum);
        $info = Db::table('wuliu_config')->where("clientkeynum='$keynum'")->find();
        $this->assign('info', $info);
        return $this->fetch('wuliu_set');
    }

    public function ajax_wuliu_set()
    {
        //平台客户的keynum
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = $param["keynum"];
        $where = "clientkeynum='$basekeynum'";
        $data = $param;
        $data["clientkeynum"] = $basekeynum;
        $data["join_url"] = "http://vplat.qiwubang.com";
        if (isset($param['status']) &&  $param['status'] == 'on') {
            $data['status'] = 1;
        } else {
            $data['status'] = 0;
        }
        $info = Db::table('wuliu_config')->where("clientkeynum='$basekeynum'")->find();
        if ($info) {
            $rs = Db::table("wuliu_config")->where($where)->update($data);
        } else {
            $rs = Db::table("wuliu_config")->insert($data);
        }
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    /**
     * 打印机配置
     */
    public function printer_config()
    {
        $request = Request::instance();
        $param = $request->param();
        $keynum = $param['keynum'];
        $this->assign('keynum', $keynum);

        // 获取易联云平台配置
        $info = Db::table('yly_platform_config')->where("clientkeynum='$keynum'")->find();
        $this->assign('info', $info);

        return $this->fetch('printer_config');
    }

    /**
     * 保存打印机配置
     */
    public function ajax_printer_config()
    {
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = $param["keynum"];
        $where = "clientkeynum='$basekeynum'";

        $find = Db::table("yly_platform_config")->where($where)->find();
        $data = $param;
        unset($data['keynum']);
        $data["clientkeynum"] = $basekeynum;

        // 状态处理
        // if (isset($param['status']) && $param['status'] == 'on') {
        //     $data['status'] = 1;
        // } else {
        //     $data['status'] = 0;
        // }

        if ($find) {
            $rs = Db::table("yly_platform_config")->where($where)->update($data);
        } else {
            $data['created_at'] = date('Y-m-d H:i:s');
            $rs = Db::table("yly_platform_config")->insert($data);
        }

        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }

        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    /**
     * 客户统一配置页面
     */
    public function customer_settings()
    {
        // 如果不是平台账号则需要校验权限
        if (session('cn_accountinfo.basekeynum') != '平台') {
            check_auth(request()->controller() . '/customer_settings', 0);
        }
        
        $request = Request::instance();
        $param = $request->param();
        
        // 获取客户keynum
        if (session('cn_accountinfo.basekeynum') == '平台') {
            $keynum = isset($param['keynum']) ? $param['keynum'] : '';
            $this->assign("is_plat", "1");
        } else {
            $keynum = session('cn_accountinfo.basekeynum');
            $this->assign("is_plat", "0");
        }
        
        $this->assign('keynum', $keynum);
        return $this->fetch('customer_settings');
    }

    
    /**
     * 微信登录日志列表
     */
    public function wechat_login_logs()
    {
        //权限校验
        check_auth(request()->controller() . '/wechat_login_logs', 0);
        
        $page = input('page', 1);
        $limit = input('limit', 20);
        $action = input('action', '');
        $start_date = input('start_date', '');
        $end_date = input('end_date', '');
        
        $where = [];
        if ($action) {
            $where['log.action'] = $action;
        }
        if ($start_date) {
            $where[] = ['log.create_time', '>=', $start_date . ' 00:00:00'];
        }
        if ($end_date) {
            $where[] = ['log.create_time', '<=', $end_date . ' 23:59:59'];
        }
        
        $query = Db::table('plat_wechat_login_log')
            ->alias('log')
            ->leftJoin('plat_account account', 'log.account_id = account.account_id')
            ->field('log.*, account.accountname, account.accountrealname')
            ->where($where)
            ->order('log.create_time DESC');
        
        $total = $query->count();
        $logs = $query->page($page, $limit)->select();
        
        $this->assign([
            'logs' => $logs,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'action' => $action,
            'start_date' => $start_date,
            'end_date' => $end_date
        ]);
        
        return $this->fetch('wechat_login_logs');
    }
    
    /**
     * 微信绑定管理
     */
    public function wechat_bindings()
    {
        //权限校验
        check_auth(request()->controller() . '/wechat_bindings', 0);
        
        $page = input('page', 1);
        $limit = input('limit', 20);
        $keyword = input('keyword', '');
        
        $where = [['auth.auth_type', '=', 'wechat']];
        if ($keyword) {
            $where[] = ['account.accountname|account.accountrealname|auth.third_nickname', 'like', '%' . $keyword . '%'];
        }
        
        $query = Db::table('plat_third_auth')
            ->alias('auth')
            ->leftJoin('plat_account account', 'auth.account_id = account.account_id')
            ->field('auth.*, account.accountname, account.accountrealname')
            ->where($where)
            ->order('auth.bind_time DESC');
        
        $total = $query->count();
        $bindings = $query->page($page, $limit)->select();
        
        $this->assign([
            'bindings' => $bindings,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'keyword' => $keyword
        ]);
        
        return $this->fetch('wechat_bindings');
    }
    
    /**
     * 解绑微信账号
     */
    public function ajax_unbind_wechat()
    {
        //权限校验
        check_auth(request()->controller() . '/adminlist', 1);
        
        $account_id = input('account_id', 0);
        if (!$account_id) {
            return json(['sta' => 0, 'msg' => '参数错误']);
        }
        
        $bind_record = Db::table('plat_third_auth')->where('account_id', $account_id)->find();
        if (!$bind_record) {
            return json(['sta' => 0, 'msg' => '绑定记录不存在']);
        }
        
        try {
            // 更新绑定状态
            $result = Db::table('plat_third_auth')
                ->where('account_id', $account_id)
                ->where('auth_type', 'wechat')
                ->delete();
            
            if ($result) {
                // 记录操作日志
                addoperatelog('解绑微信账号', '管理员解绑了用户ID:' . $bind_record['account_id'] . ' 的微信账号');
                
                // 记录微信登录日志
                Db::table('plat_wechat_login_log')->insert([
                    'account_id' => $bind_record['account_id'],
                    'openid' => $bind_record['third_openid'],
                    'action' => 'unbind',
                    'login_ip' => request()->ip(),
                    'user_agent' => request()->header('User-Agent'),
                    'is_success' => 1,
                    'error_msg' => '管理员操作解绑',
                    'extra_data' => json_encode(['operator' => session('cn_accountinfo.accountname')])
                ]);
                
                return json(['sta' => 1, 'msg' => '解绑成功']);
            } else {
                return json(['sta' => 0, 'msg' => '解绑失败']);
            }
        } catch (\Exception $e) {
            return json(['sta' => 0, 'msg' => '解绑失败：' . $e->getMessage()]);
        }
    }

    
    /**
     * 微信开放平台配置页面
     */
    public function wechat_config()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/wechat_config', 0);
        $info = Db::table('plat_system_set')->where("id ='1' ")->find();
        $this->assign('info', $info);
        return $this->fetch('wechat_config');
    }

    /**
     * 保存微信开放平台配置
     */
    public function ajax_wechat_config()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/wechat_config', 1);
        $request = Request::instance();
        $param = $request->param();
        
        // 微信开放平台配置
        $data['wechat_open_appid'] = isset($param['wechat_open_appid']) ? trim($param['wechat_open_appid']) : '';
        $data['wechat_open_app_secret'] = isset($param['wechat_open_app_secret']) ? trim($param['wechat_open_app_secret']) : '';
        $data['wechat_login_enabled'] = isset($param['wechat_login_enabled']) ? intval($param['wechat_login_enabled']) : 0;
        $data['mod_time'] = time();
        
        $rs = Db::table('plat_system_set')->where("id='1'")->update($data);
        addoperatelog('编辑微信开放平台配置', json_encode($param, JSON_UNESCAPED_UNICODE));
        
        if ($rs) {
            $r['msg'] = '保存成功';
            $r['sta'] = 1;
            echo json_encode($r);
            die;
        } else {
            $r['msg'] = '保存失败';
            $r['sta'] = 0;
            echo json_encode($r);
            die;
        }
    }

    
    /**
     * 获取微信AccessToken
     * @param string $appId 小程序/公众号AppID
     * @param string $appSecret 小程序/公众号AppSecret
     * @return string AccessToken
     */
    private function getAccessToken($appId, $appSecret)
    {
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appId}&secret={$appSecret}";
        $response = file_get_contents($url);
        $data = json_decode($response, true);
        
        if (isset($data['access_token'])) {
            return $data['access_token'];
        }
        
        return '';
    }

    /**
     * 获取微信账号基本信息
     * @param string $accessToken 访问令牌
     * @return array 账号信息
     */
    private function getWechatAccountInfo($accessToken)
    {
        $info = [
            'status' => false,
            'message' => '',
            'principal' => '未知主体',
            'auth_status' => '未认证',
            'expire_date' => '',
            'expire_timestamp' => 0,
            'is_expire_warn' => false,
            'qrcode_url' => '',
        ];

        // 调用微信接口获取账号信息
        $url = "https://api.weixin.qq.com/cgi-bin/account/getaccountbasicinfo?access_token={$accessToken}";
        $response = file_get_contents($url);
        $data = json_decode($response, true);

        // 检查是否有错误
        if (isset($data['errcode']) && $data['errcode'] != 0) {
            $info['message'] = $data['errmsg'] ?? '获取微信账号信息失败';
            return $info;
        }

        // 解析数据
        $info['status'] = true;
        $info['principal'] = $data['principal_name'] ?? '未知主体';
        $info['auth_status'] = $this->getWechatStatusText($data['wx_verify_info']['qualification_verify'] ?? false);
        
        // 处理到期时间
        if (isset($data['wx_verify_info']['annual_review_end_time'])) {
            $expireTime = $data['wx_verify_info']['annual_review_end_time'];
            $info['expire_timestamp'] = $expireTime;
            $info['expire_date'] = date('Y-m-d H:i:s', $expireTime);
            
            // 检查是否即将到期（30天内）
            if ($expireTime < time() + (30 * 24 * 60 * 60)) {
                $info['is_expire_warn'] = true;
            }
        }

        return $info;
    }

    /**
     * 获取微信认证状态文本
     * @param bool $isVerified 是否已认证
     * @return string 状态文本
     */
    private function getWechatStatusText($isVerified)
    {
        $statusMap = [
            true => '已认证',
            false => '未认证'
        ];
        return $statusMap[$isVerified] ?? '未知状态';
    }

    /**
     * 生成小程序码
     * @param string $accessToken 访问令牌
     * @param string $appId 小程序AppID
     * @return string 小程序码URL
     */
    private function generateMiniCode($accessToken, $appId)
    {
        // 调用微信API
        $url = "https://api.weixin.qq.com/wxa/getwxacode?access_token={$accessToken}";
        $postData = json_encode([
            'path'  => 'pages/index/index',
            'width' => 430
        ]);
        
        // 发送请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url); 
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $result = curl_exec($ch);
        curl_close($ch);
        
        // 检查返回结果是否为JSON（表示出错）
        $jsonResult = json_decode($result, true);
        if (isset($jsonResult['errcode'])) {
            return '';
        }
        
        // 保存二维码
        $savePath = './uploads/qrcodes/';
        if (!is_dir($savePath)) {
            mkdir($savePath, 0755, true);
        }
        
        $filename = $appId . '.png';
        $filePath = $savePath . $filename;
        file_put_contents($filePath, $result);
        
        return '/uploads/qrcodes/' . $filename;
    }

    /**
     * 微信监控列表
     */
    public function wechat_monitor()
    {
        // 只加载页面视图，数据通过AJAX异步获取
        return $this->fetch();
    }

    /**
     * 获取微信监控信息
     * @param array $config 微信配置
     * @return array 监控信息
     */
    private function getWechatMonitorInfo($config, $forceRefresh = false)
    {
        if (empty($config['appid']) || empty($config['appsecret'])) {
            return [
                'status' => '配置错误',
                'message' => 'AppID或AppSecret未配置',
                'principal' => '未知',
                'auth_status' => '未知',
                'expire_date' => '未知',
                'is_expire_warn' => false,
                'qrcode_url' => ''
            ];
        }
        
        // 检查是否有缓存数据且不强制刷新
        if (!$forceRefresh && !empty($config['wechat_monitor_info'])) {
            $cachedInfo = json_decode($config['wechat_monitor_info'], true);
            if (!empty($cachedInfo)) {
                return $cachedInfo;
            }
        }

        // 获取AccessToken
        $accessToken = $this->getAccessToken($config['appid'], $config['appsecret']);
        if (empty($accessToken)) {
            return [
                'status' => '连接失败',
                'message' => '获取AccessToken失败',
                'principal' => '未知',
                'auth_status' => '未知',
                'expire_date' => '未知',
                'is_expire_warn' => false,
                'qrcode_url' => ''
            ];
        }

        // 获取账号信息
        $accountInfo = $this->getWechatAccountInfo($accessToken);
        
        // 生成小程序码
        $qrcodeUrl = $this->generateMiniCode($accessToken, $config['appid']);
        
        // 构建监控信息
        $monitorInfo = [
            'status' => $accountInfo['status'] ? '正常' : '异常',
            'message' => $accountInfo['message'] ?? '',
            'principal' => $accountInfo['principal'] ?? '未知',
            'auth_status' => $accountInfo['auth_status'] ?? '未知状态',
            'expire_date' => $accountInfo['expire_date'] ?? '未知',
            'is_expire_warn' => $accountInfo['is_expire_warn'] ?? false,
            'qrcode_url' => $qrcodeUrl ?: '',
            'check_time' => date('Y-m-d H:i:s')
        ];
        
        // 更新数据库
        $this->updateWechatMonitorInfo($config['id'], $monitorInfo, $config['type'] ?? 'plat');
        
        return $monitorInfo;
    }
    
    /**
     * 更新微信监控信息到数据库
     * @param int $configId 配置ID
     * @param array $monitorInfo 监控信息
     * @param string $type 配置类型，plat或shop
     */
    private function updateWechatMonitorInfo($configId, $monitorInfo, $type = 'plat')
    {
        // 更新数据
        $updateData = [
            'wechat_monitor_info' => json_encode($monitorInfo, JSON_UNESCAPED_UNICODE),
        ];
        
        // 根据类型更新对应表
        if ($type == 'plat') {
            db('plat_wechat_set')->where('id', $configId)->update($updateData);
        } else if ($type == 'shop') {
            db('shop_wechat_set')->where('id', $configId)->update($updateData);
        }
    }

    /**
     * AJAX获取微信监控列表
     */
    public function ajax_get_wechat_monitor_list()
    {
        $type = input('type', 'plat'); // plat或shop
        $list = [];
        
        // 平台管理员查询所有客户的配置
        $clients = db('plat_client')->select();
        
        foreach ($clients as $client) {
            $clientkeynum = $client['keynum'];
            $clientName = $client['companyname'] ?: '未命名客户';
            $configs = [];
            
            // 根据类型获取配置
            if ($type == 'plat') {
                $configs = db('plat_wechat_set')
                    ->where('basekeynum', $clientkeynum)
                    ->select();
                
                foreach ($configs as $config) {
                    // 添加类型标记
                    $config['type'] = 'plat';
                    
                    // 获取缓存的微信监控信息
                    $monitorInfo = $this->getWechatMonitorInfo($config);
                    
                    // 计算到期剩余天数
                    $daysLeft = null;
                    $expireDate = $monitorInfo['expire_date'] ?? '';
                    if ($expireDate && $expireDate !== '未知' && $expireDate !== '检测失败') {
                        $expireTime = strtotime($expireDate);
                        $currentTime = time();
                        if ($expireTime !== false) {
                            $daysLeft = floor(($expireTime - $currentTime) / (24 * 3600));
                        }
                    }
                    
                    $list[] = [
                        'id' => $config['id'],
                        'type' => $type,
                        'client_name' => $clientName,
                        'client_keynum' => $clientkeynum,
                        'appid' => $config['appid'] ?? '',
                        'status' => $monitorInfo['status'] ?? '检测失败',
                        'principal' => $monitorInfo['principal'] ?? '检测失败',
                        'auth_status' => $monitorInfo['auth_status'] ?? '检测失败',
                        'expire_date' => $monitorInfo['expire_date'] ?? '检测失败',
                        'is_expire_warn' => $monitorInfo['is_expire_warn'] ?? false,
                        'check_time' => $monitorInfo['check_time'] ?? date('Y-m-d H:i:s'),
                        'qrcode_url' => $monitorInfo['qrcode_url'] ?? '',
                        'days_left' => $daysLeft,
                    ];
                }
            } else if ($type == 'shop') {
                $configs = db('shop_wechat_set')
                    ->alias('s')
                    ->where('s.basekeynum', $clientkeynum)
                    ->field('s.*')
                    ->select();
                
                foreach ($configs as $config) {
                    // 添加类型标记
                    $config['type'] = 'shop';
                    
                    // 获取缓存的微信监控信息
                    $monitorInfo = $this->getWechatMonitorInfo($config);
                    
                    // 计算到期剩余天数
                    $daysLeft = null;
                    $expireDate = $monitorInfo['expire_date'] ?? '';
                    if ($expireDate && $expireDate !== '未知' && $expireDate !== '检测失败') {
                        $expireTime = strtotime($expireDate);
                        $currentTime = time();
                        if ($expireTime !== false) {
                            $daysLeft = floor(($expireTime - $currentTime) / (24 * 3600));
                        }
                    }
                    
                    $list[] = [
                        'id' => $config['id'],
                        'type' => $type,
                        'client_name' => $clientName,
                        'client_keynum' => $clientkeynum,
                        'appid' => $config['appid'] ?? '',
                        'status' => $monitorInfo['status'] ?? '检测失败',
                        'principal' => $monitorInfo['principal'] ?? '检测失败',
                        'auth_status' => $monitorInfo['auth_status'] ?? '检测失败',
                        'expire_date' => $monitorInfo['expire_date'] ?? '检测失败',
                        'is_expire_warn' => $monitorInfo['is_expire_warn'] ?? false,
                        'check_time' => $monitorInfo['check_time'] ?? date('Y-m-d H:i:s'),
                        'qrcode_url' => $monitorInfo['qrcode_url'] ?? '',
                        'days_left' => $daysLeft,
                    ];
                }
            }
        }
        
        return json(['code' => 0, 'msg' => 'success', 'data' => $list]);
    }

    /**
     * 刷新微信监控信息
     */
    public function ajax_refresh_wechat_monitor()
    {
        $id = input('id', 0);
        $type = input('type', '');
        $clientkeynum = input('client_keynum', '');
        
        if (empty($type) || !in_array($type, ['plat', 'shop'])) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        $config = null;
        
        // 根据类型获取配置
        if ($type == 'plat') {
            $config = db('plat_wechat_set')->where('id', $id)->find();
            $config['type'] = 'plat'; // 添加类型标记
        } else if ($type == 'shop') {
            $config = db('shop_wechat_set')
                ->alias('s')
                ->where('s.id', $id)
                ->field('s.*')
                ->find();
            $config['type'] = 'shop'; // 添加类型标记
        }
        
        if (!$config) {
            return json(['code' => 1, 'msg' => '配置不存在']);
        }
        
        // 获取客户名称
        $client = db('plat_client')->where('keynum', $clientkeynum)->find();
        $clientName = $client ? $client['title'] : '未命名客户';
        
        // 强制刷新获取微信监控信息
        $monitorInfo = $this->getWechatMonitorInfo($config, true);
        
        // 计算到期剩余天数
        $daysLeft = null;
        $expireDate = $monitorInfo['expire_date'] ?? '';
        if ($expireDate && $expireDate !== '未知' && $expireDate !== '检测失败') {
            $expireTime = strtotime($expireDate);
            $currentTime = time();
            if ($expireTime !== false) {
                $daysLeft = floor(($expireTime - $currentTime) / (24 * 3600));
            }
        }
        
        $result = [
            'id' => $config['id'],
            'type' => $type,
            'client_name' => $clientName,
            'client_keynum' => $clientkeynum,
            'appid' => $config['xcx_appid'] ?? '',
            'status' => $monitorInfo['status'] ?? '检测失败',
            'principal' => $monitorInfo['principal'] ?? '检测失败',
            'auth_status' => $monitorInfo['auth_status'] ?? '检测失败',
            'expire_date' => $monitorInfo['expire_date'] ?? '检测失败',
            'is_expire_warn' => $monitorInfo['is_expire_warn'] ?? false,
            'check_time' => date('Y-m-d H:i:s'),
            'qrcode_url' => $monitorInfo['qrcode_url'] ?? '',
            'days_left' => $daysLeft,
        ];
        
        return json(['code' => 0, 'msg' => 'success', 'data' => $result]);
    }

    public function self_unbind_wechat()
    {
        $account_id = session('cn_accountinfo.account_id');
        $result = db('plat_third_auth')->where('account_id', $account_id)->where('auth_type', 'wechat')->where('status', 1)->delete();
        return json(['code' => 0, 'msg' => 'success']);
    }



}
