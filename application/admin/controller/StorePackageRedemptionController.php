<?php
namespace app\admin\controller;

use app\admin\model\Shop;
use think\Controller;
use think\Db;
use think\facade\Request;

/**
 * 门店平台套餐卡兑换管理控制器
 */
class StorePackageRedemptionController extends CnController
{
    /**
     * 初始化方法
     */
    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    /**
     * 待发货列表页面
     */
    public function pendingList()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        // check_auth(request()->controller() . '/pendingList', 0);
        
        return $this->fetch();
    }

    /**
     * 待发货列表数据获取
     */
    public function ajaxPendingList()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        // check_auth(request()->controller() . '/pendingList', 1);
        
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.basekeynum');

        // 获取当前门店
        $shop = Shop::where('keynum', $basekeynum)->find();
        if (empty($shop)) {
            return json(['code' => 1, 'msg' => '门店信息获取失败']);
        }
        $shop_id = $shop['id'];

        
        $where = [];
        $card_no = input('card_no', '');
        $user_name = input('user_name', '');
        $start_time = input('start_time', '');
        $end_time = input('end_time', '');
        
        $where[] = ['a.clientkeynum', '=', $clientkeynum];
        $where[] = ['a.status', '=', 1]; // 已兑换
        $where[] = ['a.type', '=', 2]; // 快递配送
        $where[] = ['a.shop_id', '=', $shop_id]; // 分配给当前门店的订单
        
        if ($card_no) {
            $where[] = ['b.card_no', 'like', "%{$card_no}%"];
        }
        
        if ($user_name) {
            $where[] = ['c.name', 'like', "%{$user_name}%"];
        }
        
        if ($start_time && $end_time) {
            $where[] = ['a.redemption_time', 'between', [$start_time, $end_time]];
        } else if ($start_time) {
            $where[] = ['a.redemption_time', '>=', $start_time];
        } else if ($end_time) {
            $where[] = ['a.redemption_time', '<=', $end_time];
        }
        
        $list = Db::name('card_package_redemption')->alias('a')
            ->join('card b', 'a.card_id = b.id', 'left')
            ->join('client_member c', 'a.user_id = c.id', 'left')
            ->join('shop d', 'a.shop_id = d.id', 'left')
            ->field('a.*, b.card_no, c.name as user_name, d.title as shop_name')
            ->where($where)
            ->order('a.redemption_time', 'desc')
            ->paginate(input('limit', 15));
            
        $data = $list->items();
        foreach ($data as &$item) {
            // 获取套餐信息
            $packages = Db::name('card_package_redemption_detail')->alias('rd')
                ->join('card_package cp', 'rd.package_id = cp.id', 'left')
                ->where('rd.redemption_id', $item['id'])
                ->column('cp.name');
            $item['package_names'] = implode('、', $packages);
            
            // 获取商品信息
            $products = Db::name('card_package_redemption_product')->alias('rp')
                ->join('product_inventory pi', 'rp.product_inventory_id = pi.id', 'left')
                ->join('products p', 'pi.product_id = p.id', 'left')
                ->where('rp.redemption_id', $item['id'])
                ->column('p.title');
            $item['product_names'] = implode('、', $products);
        }
            
        return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $data]);
    }

    /**
     * 已发货列表页面
     */
    public function shippedList()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        // check_auth(request()->controller() . '/shippedList', 0);
        
        return $this->fetch();
    }

    /**
     * 已发货列表数据获取
     */
    public function ajaxShippedList()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        // check_auth(request()->controller() . '/shippedList', 1);
        
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.basekeynum');

        // 获取当前门店
        $shop = Shop::where('keynum', $basekeynum)->find();
        if (empty($shop)) {
            return json(['code' => 1, 'msg' => '门店信息获取失败']);
        }
        $shop_id = $shop['id'];
        
        $where = [];
        $card_no = input('card_no', '');
        $user_name = input('user_name', '');
        $express_company = input('express_company', '');
        $express_no = input('express_no', '');
        $start_time = input('start_time', '');
        $end_time = input('end_time', '');
        
        $where[] = ['a.clientkeynum', '=', $clientkeynum];
        $where[] = ['a.status', '=', 2]; // 已发货
        $where[] = ['a.type', '=', 2]; // 快递配送
        $where[] = ['a.shop_id', '=', $shop_id]; // 当前门店的订单
        
        if ($card_no) {
            $where[] = ['b.card_no', 'like', "%{$card_no}%"];
        }
        
        if ($user_name) {
            $where[] = ['c.name', 'like', "%{$user_name}%"];
        }
        
        if ($express_company) {
            $where[] = ['a.express_company', 'like', "%{$express_company}%"];
        }
        
        if ($express_no) {
            $where[] = ['a.express_no', 'like', "%{$express_no}%"];
        }
        
        if ($start_time && $end_time) {
            $where[] = ['a.express_time', 'between', [$start_time, $end_time]];
        } else if ($start_time) {
            $where[] = ['a.express_time', '>=', $start_time];
        } else if ($end_time) {
            $where[] = ['a.express_time', '<=', $end_time];
        }
        
        $list = Db::name('card_package_redemption')->alias('a')
            ->join('card b', 'a.card_id = b.id', 'left')
            ->join('client_member c', 'a.user_id = c.id', 'left')
            ->join('shop d', 'a.shop_id = d.id', 'left')
            ->field('a.*, b.card_no, c.name as user_name, d.title as shop_name')
            ->where($where)
            ->order('a.express_time', 'desc')
            ->paginate(input('limit', 15));
            
        $data = $list->items();
        foreach ($data as &$item) {
            // 获取套餐信息
            $packages = Db::name('card_package_redemption_detail')->alias('rd')
                ->join('card_package cp', 'rd.package_id = cp.id', 'left')
                ->where('rd.redemption_id', $item['id'])
                ->column('cp.name');
            $item['package_names'] = implode('、', $packages);
            
            // 获取商品信息
            $products = Db::name('card_package_redemption_product')->alias('rp')
                ->join('product_inventory pi', 'rp.product_inventory_id = pi.id', 'left')
                ->join('products p', 'pi.product_id = p.id', 'left')
                ->where('rp.redemption_id', $item['id'])
                ->column('p.title');
            $item['product_names'] = implode('、', $products);
        }
            
        return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $data]);
    }

    /**
     * 查看兑换详情
     */
    public function detail($id)
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        // check_auth(request()->controller() . '/pendingList', 0);
        
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.basekeynum');

        // 获取当前门店
        $shop = Shop::where('keynum', $basekeynum)->find();
        if (empty($shop)) {
            return json(['code' => 1, 'msg' => '门店信息获取失败']);
        }
        $shop_id = $shop['id'];
        // 获取兑换主表信息
        $info = Db::name('card_package_redemption')->alias('a')
            ->join('card b', 'a.card_id = b.id', 'left')
            ->join('client_member c', 'a.user_id = c.id', 'left')
            ->field('a.*, b.card_no, c.name as user_name')
            ->where('a.id', $id)
            ->where('a.clientkeynum', $clientkeynum)
            ->where('a.shop_id', $shop_id)
            ->find();
            
        if (!$info) {
            $this->error('兑换记录不存在或无权限查看');
        }
        
        $info['status_text'] = $this->getStatusText($info['status']);
        $info['type_text'] = $this->getTypeText($info['type']);
        
        // 获取兑换套餐明细
        $details = Db::name('card_package_redemption_detail')->alias('a')
            ->join('card_package b', 'a.package_id = b.id', 'left')
            ->field('a.*, b.name as package_name, b.price as package_price, b.validity_days')
            ->where('a.redemption_id', $id)
            ->where('a.clientkeynum', $clientkeynum)
            ->select();
            
        // 获取兑换商品
        $products = Db::name('card_package_redemption_product')->alias('a')
            ->join('product_inventory b', 'a.product_inventory_id = b.id', 'left')
            ->join('products c', 'b.product_id = c.id', 'left')
            ->field('a.*, c.title as product_name, b.title as spec_values, IFNULL(b.price, 0) as product_price')
            ->where('a.redemption_id', $id)
            ->where('a.clientkeynum', $clientkeynum)
            ->select();
            
        $this->assign('info', $info);
        $this->assign('details', $details);
        $this->assign('products', $products);
        
        return $this->fetch();
    }

    /**
     * 发货操作
     */
    public function ship()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        // check_auth(request()->controller() . '/pendingList', 0);
        
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.basekeynum');

        // 获取当前门店
        $shop = Shop::where('keynum', $basekeynum)->find();
        if (empty($shop)) {
            return json(['code' => 1, 'msg' => '门店信息获取失败']);
        }
        $shop_id = $shop['id'];
        $id = input('id');
        
        // 获取兑换记录
        $redemption = Db::name('card_package_redemption')
            ->where('id', $id)
            ->where('clientkeynum', $clientkeynum)
            ->where('shop_id', $shop_id)
            ->where('status', 1) // 已兑换状态
            ->where('type', 2) // 快递配送
            ->find();
            
        if (!$redemption) {
            $this->error('兑换记录不存在或不可发货');
        }
        
        // 获取兑换信息
        $info = Db::name('card_package_redemption')->alias('a')
            ->join('card b', 'a.card_id = b.id', 'left')
            ->join('client_member c', 'a.user_id = c.id', 'left')
            ->field('a.*, b.card_no, c.name as user_name')
            ->where('a.id', $id)
            ->where('a.clientkeynum', $clientkeynum)
            ->find();
            
        $this->assign('info', $info);
        
        return $this->fetch();
    }

    /**
     * 更新快递信息
     */
    public function updateExpress()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        // check_auth(request()->controller() . '/pendingList', 1);
        // 
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.basekeynum');

        // 获取当前门店
        $shop = Shop::where('keynum', $basekeynum)->find();
        if (empty($shop)) {
            return json(['code' => 1, 'msg' => '门店信息获取失败']);
        }
        $shop_id = $shop['id'];
        $operator = session('cn_accountinfo.accountname'); // 操作人员
        $operator_id = session('cn_accountinfo.account_id'); // 操作人员ID
        
        $id = input('id');
        $data = input('post.');
        
        // 验证兑换记录
        $redemption = Db::name('card_package_redemption')
            ->where('id', $id)
            ->where('clientkeynum', $clientkeynum)
            ->where('shop_id', $shop_id)
            ->where('status', 1) // 已兑换状态
            ->where('type', 2) // 快递配送
            ->find();
            
        if (!$redemption) {
            return json(['code' => 1, 'msg' => '兑换记录不存在或不可发货']);
        }
        
        // 验证快递信息
        if (empty($data['express_company']) || empty($data['express_no'])) {
            return json(['code' => 1, 'msg' => '快递公司和快递单号不能为空']);
        }
        
        // 获取兑换的商品列表
        $products = Db::name('card_package_redemption_product')->alias('a')
            ->join('product_inventory pi', 'a.product_inventory_id = pi.id', 'left')
            ->join('products p', 'pi.product_id = p.id', 'left')
            ->field('a.*, p.id as product_id, p.title as product_name, p.product_type, pi.title as spec_values')
            ->where('a.redemption_id', $id)
            ->where('a.clientkeynum', $clientkeynum)
            ->select();
            
        // 验证库存是否足够
        foreach ($products as $product) {
            // 查询当前门店的库存记录
            $inventory = Db::name('shop_product_inventory')
                ->where('shop_id', $shop_id)
                ->where('product_id', $product['product_id'])
                ->where('inventory_id', $product['product_inventory_id'])
                ->where('clientkeynum', $clientkeynum)
                ->find();
                
            if (!$inventory) {
                return json(['code' => 1, 'msg' => '商品"' . $product['product_name'] . ' ' . $product['spec_values'] . '"在当前门店无库存记录']);
            }
            
            // 根据商品类型验证库存
            if ($product['product_type'] == 2) {
                // 计量商品：验证重量库存
                $currentStock = $inventory['weight_stock'] ?? 0;
                $requestQuantity = isset($product['weight']) ? $product['weight'] : $product['quantity'];
                $stockUnit = $inventory['stock_unit'] ?? 'kg';
            } else {
                // 普通商品：验证数量库存
                $currentStock = $inventory['stock'] ?? 0;
                $requestQuantity = $product['quantity'];
                $stockUnit = '件';
            }
            
            if ($requestQuantity > $currentStock) {
                $productFullName = $product['product_name'];
                if (!empty($product['spec_values'])) {
                    $productFullName .= ' (' . $product['spec_values'] . ')';
                }
                
                $productType = $product['product_type'] == 2 ? '计量商品' : '普通商品';
                return json(['code' => 1, 'msg' => $productFullName . ' (' . $productType . ') 库存不足，当前库存: ' . $currentStock . $stockUnit . '，需要数量: ' . $requestQuantity . $stockUnit]);
            }
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 更新快递信息
            $updateData = [
                'express_company' => $data['express_company'],
                'express_no' => $data['express_no'],
                'express_time' => date('Y-m-d H:i:s'),
                'express_status' => 1, // 已发货
                'update_time' => date('Y-m-d H:i:s'),
                'status' => 2, // 已发货
                'express_operator_id' => $operator_id,
                'express_operator' => $operator
            ];
            
            if (isset($data['express_remark'])) {
                $updateData['express_remark'] = $data['express_remark'];
            }
            
            $result = Db::name('card_package_redemption')
                ->where('id', $id)
                ->where('clientkeynum', $clientkeynum)
                ->where('shop_id', $shop_id)
                ->update($updateData);
                
            if (!$result) {
                throw new \Exception('快递信息更新失败');
            }
            
            // 创建出库单 - 使用统一的编号规则
            $orderNo = 'CPROUT' . date('YmdHis') . mt_rand(1000, 9999);
            
            // 准备出库单数据
            $outStockData = [
                'order_no' => $orderNo,
                'order_type' => 2, // 出库单
                'business_type' => 1, // 销售出库
                'shop_id' => $shop_id,
                'status' => 3, // 已完成状态
                'remark' => '套餐卡兑换发货自动出库，兑换ID：' . $id,
                'clientkeynum' => $clientkeynum,
                'created_by' => $operator,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'completed_at' => date('Y-m-d H:i:s'),
                'completed_by' => $operator,
                'related_order_no' => 'CPR' . $id
            ];
            
            // 插入出库单主表
            $outStockId = Db::name('inventory_order')->insertGetId($outStockData);
            
            if (!$outStockId) {
                throw new \Exception('创建出库单失败');
            }
            
            // 准备出库单明细数据
            $outStockDetails = [];
            $totalAmount = 0;
            
            foreach ($products as $product) {
                // 根据商品类型处理数量
                if ($product['product_type'] == 2) {
                    $quantity = isset($product['weight']) ? $product['weight'] : $product['quantity'];
                } else {
                    $quantity = $product['quantity'];
                }
                
                // 获取商品价格信息 - 优先使用规格价格
                $productInfo = Db::name('products')->where('id', $product['product_id'])->find();
                $inventoryInfo = Db::name('product_inventory')->where('id', $product['product_inventory_id'])->find();
                $price = $inventoryInfo && $inventoryInfo['price'] > 0 ? $inventoryInfo['price'] : ($productInfo['price'] ?? 0);
                $totalPrice = $price * $quantity;
                $totalAmount += $totalPrice;
                
                // 准备明细数据
                $detailData = [
                    'order_id' => $outStockId,
                    'order_no' => $orderNo,
                    'order_type' => 2, // 出库单
                    'product_id' => $product['product_id'],
                    'inventory_id' => $product['product_inventory_id'],
                    'quantity' => $quantity,
                    'price' => $price,
                    'total_price' => $totalPrice,
                    'shop_id' => $shop_id,
                    'clientkeynum' => $clientkeynum,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $outStockDetails[] = $detailData;
            }
            
            // 更新出库单总金额
            Db::name('inventory_order')->where('id', $outStockId)->update([
                'total_amount' => $totalAmount
            ]);
            
            // 批量插入出库单明细
            if (!empty($outStockDetails)) {
                $detailResult = Db::name('inventory_order_detail')->insertAll($outStockDetails);
                if (!$detailResult) {
                    throw new \Exception('创建出库单明细失败');
                }
            }
            
            // 扣减库存并记录库存日志
            foreach ($products as $product) {
                // 查询当前门店的库存记录
                $inventory = Db::name('shop_product_inventory')
                    ->where('shop_id', $shop_id)
                    ->where('product_id', $product['product_id'])
                    ->where('inventory_id', $product['product_inventory_id'])
                    ->where('clientkeynum', $clientkeynum)
                    ->find();
                
                // 根据商品类型扣减不同的库存字段
                if ($product['product_type'] == 2) {
                    // 计量商品：扣减重量库存
                    $requestQuantity = isset($product['weight']) ? $product['weight'] : $product['quantity'];
                    $beforeStock = $inventory['weight_stock'];
                    $afterStock = $beforeStock - $requestQuantity;
                    
                    // 更新库存
                    $updateStock = Db::name('shop_product_inventory')
                        ->where('id', $inventory['id'])
                        ->update([
                            'weight_stock' => $afterStock,
                            'update_time' => date('Y-m-d H:i:s')
                        ]);
                } else {
                    // 普通商品：扣减数量库存
                    $requestQuantity = $product['quantity'];
                    $beforeStock = $inventory['stock'];
                    $afterStock = $beforeStock - $requestQuantity;
                    
                    // 更新库存
                    $updateStock = Db::name('shop_product_inventory')
                        ->where('id', $inventory['id'])
                        ->update([
                            'stock' => $afterStock,
                            'update_time' => date('Y-m-d H:i:s')
                        ]);
                }
                
                if (!$updateStock) {
                    throw new \Exception('商品"' . $product['product_name'] . ' ' . $product['spec_values'] . '"库存扣减失败');
                }
                
                // 记录库存日志 - 使用统一的change_type
                $logData = [
                    'clientkeynum' => $clientkeynum,
                    'product_id' => $product['product_id'],
                    'inventory_id' => $product['product_inventory_id'],
                    'shop_id' => $shop_id,
                    'change_type' => 5, // 5表示销售出库
                    'change_quantity' => -$requestQuantity, // 出库为负数
                    'before_quantity' => $beforeStock,
                    'after_quantity' => $afterStock,
                    'order_no' => $orderNo, // 使用出库单号
                    'remark' => '套餐卡兑换发货扣减库存，兑换ID：' . $id,
                    'operator' => $operator,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                // 插入库存日志
                Db::name('inventory_log')->insert($logData);
                
                // 同时记录到shop_product_inventory_log表，保持两个日志表的一致性
                Db::name('shop_product_inventory_log')->insert([
                    'clientkeynum' => $clientkeynum,
                    'shop_id' => $shop_id,
                    'product_inventory_id' => $product['product_inventory_id'],
                    'type' => 2, // 减库存
                    'quantity' => $requestQuantity,
                    'before_stock' => $beforeStock,
                    'after_stock' => $afterStock,
                    'remark' => '套餐卡兑换发货扣减库存，出库单号：' . $orderNo,
                    'operator_id' => $operator_id,
                    'operator' => $operator,
                    'add_time' => date('Y-m-d H:i:s')
                ]);
            }
            
            // 记录操作日志
            Db::name('card_package_redemption_log')->insert([
                'clientkeynum' => $clientkeynum,
                'redemption_id' => $id,
                'operator_id' => $operator_id,
                'operator' => $operator,
                'operation' => '发货处理',
                'content' => '发货处理，快递公司: ' . $data['express_company'] . 
                             ', 快递单号: ' . $data['express_no'] . 
                             (isset($data['express_remark']) ? ', 备注: ' . $data['express_remark'] : '') .
                             '，系统已自动创建出库单：' . $orderNo,
                'add_time' => date('Y-m-d H:i:s')
            ]);
            
            Db::commit();
            return json(['code' => 0, 'msg' => '发货成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '发货失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        $statusArr = [
            0 => '已取消',
            1 => '已兑换',
            2 => '已发货',
            100 => '已完成'
        ];
        
        return isset($statusArr[$status]) ? $statusArr[$status] : '未知状态';
    }
    
    /**
     * 获取类型文本
     */
    private function getTypeText($type)
    {
        $typeArr = [
            1 => '到店兑换',
            2 => '快递配送'
        ];
        
        return isset($typeArr[$type]) ? $typeArr[$type] : '未知类型';
    }
} 