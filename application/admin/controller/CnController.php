<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright (c) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use think\Db;
use think\Controller;
use think\facade\Session;
use think\facade\Request;
use think\facade\Cache;

class CnController extends Controller
{
    //初始化方法
    public function init()
    {
        /**
         * 过滤掉不需要检测的控制器
         */
        $controller = request()->controller();
        if (in_array($controller, array("Login", "Cn"))) {
            return true;
        }
        //检测是否登录
        $flag = $this->check_login();
        $url = url("login/index");
        if (!$flag) {
            header("Location: {$url}");
            die;
        }
        //重新拉取账号信息
        $account_id = session("cn_accountinfo.account_id");
        $plat_accountinfo = Db::table("plat_account")->where("account_id='$account_id'")->find();
        session("cn_accountinfo", $plat_accountinfo);

        //如果登陆者是plat_admin表
        if (session("cn_accountinfo.tablename") == 'plat_admin') {
            $menulist = "";
            //角色表
            $rolekeylist =  session("cn_accountinfo.rolekeylist");
            if ($rolekeylist != '') {  //登录的账号有角色列表的时候
                //把账号表里面存的角色keynum通过,切成数组
                $arrrolekeylist = explode(',', $rolekeylist);
                $where[] = array('keynum', 'in', $arrrolekeylist);
                $where[] = ['isdel', '=', 0];
                $plat_role = Db::table('plat_role');
                $plat_rolelist = $plat_role->where($where)->select();
                foreach ($plat_rolelist as $key => $row) {
                    $menulist .= $row['purviewlist_id'] . ",";
                }
                $menulist = substr($menulist, 0, -1);
            }
            //如果account表有菜单的id则拼接上,总平台超级管理员只是通过这个字段定义菜单权限
            if (session("cn_accountinfo.purviewlist")) {
                $menulist = $menulist . "," . session("cn_accountinfo.purviewlist");
            }
            $menulist_str_old = $menulist;
        }


        //如果登录者是平台客户plat_client表总账号
        if (session("cn_accountinfo.tablename") == 'plat_client') {
            //客户系统表
            //找到客户对应的系统
            $cn_accountkeynum = session("cn_accountinfo.keynum");
            $cn_plat_clientinfo = Db::table('plat_client')->where("keynum='$cn_accountkeynum'")->find();
            $cn_sitekeynum = $cn_plat_clientinfo['sitekeynum'];
            if ($cn_sitekeynum != '') {

                $cn_plat_siteinfo = Db::table('plat_site')->where("keynum='$cn_sitekeynum'")->find();
                $menulist_str_old = $cn_plat_siteinfo['purviewlist_id'];
            }
        }


        //如果登录者是平台客户下一级别总账号 客户
        if (session("cn_accountinfo.tablename") == 'shop') {
            //客户系统表
            //找到客户对应的系统
            // $cn_accountkeynum = session("cn_accountinfo.keynum");
            // $cn_plat_clientinfo = Db::table('welfare')->where("keynum='$cn_accountkeynum'")->find();
            // $cn_sitekeynum = $cn_plat_clientinfo['sitekeynum'];
            // if ($cn_sitekeynum != '') {
            //     $cn_plat_siteinfo = Db::table('plat_site')->where("keynum='$cn_sitekeynum'")->find();
            //     $menulist_str_old = $cn_plat_siteinfo['purviewlist_id'];
            // }
            $cn_plat_siteinfo = Db::table('plat_site')->where("site_id='6'")->find();
            $menulist_str_old = $cn_plat_siteinfo['purviewlist_id'];
        }


        $basekeynum = session("cn_accountinfo.basekeynum");

        //获取系统菜单
        $this->get_menu($menulist_str_old);

        //获取系统标题和版权

        $cn_webconfig = Db::table('plat_config')->where("basekeynum='$basekeynum'")->find();
        $this->assign("cn_webconfig",  $cn_webconfig);

        //获取总平台基本设置存入session
        $cn_system_set_info = Db::table('plat_system_set')->where("id='1'")->find();
        session("cn_system_set_info", $cn_system_set_info);
        //后台顶部预警是否有新的未读
        $warn_count = Db::table('plat_warn_log')->where(" basekeynum='$basekeynum' and is_read='0'")->count();
        $this->assign("warn_count",  $warn_count);

        $plat_config = Db::table("plat_config")->where("basekeynum", $basekeynum)->find();
        $this->assign("plat_config", $plat_config);
    }



    //获取系统菜单
    public function  get_menu($menulist_str_old)
    {

        //需要放行的方法
        if ($this->check_pass()) {
            return true;
        }

        $basekeynum = session("cn_accountinfo.basekeynum");
        //兼容下权限开始和结束出现"，"的情况
        $menulist_str = $menulist_str_old;
        $menulist_arr = explode(',', $menulist_str); //用逗号拆成数组
        $menulist_arr = array_filter($menulist_arr); //去掉空数组
        $menulist_str = implode(",", $menulist_arr); //在把数组转成逗号拼接字符串
        session("cn_accountinfo.menulist", $menulist_str); //把处理之后的菜单字符串存入session,共其他地方使用
        if (trim($menulist_str) == '') {
            error_tips("当前登录用户没有菜单操作权限，请联系管理员授权！");
            die;
        }
        //根据where条件缓存
        $where_plat_menu = " is_show=1  and menu_id in ( $menulist_str ) ";
        $menulist = Db::table('plat_menu')->where($where_plat_menu)->order('o asc')->select();

        // 获取平台基本配置 过滤掉一些个性化定制后不需要的菜单
        $plat_config = Db::table("plat_client")
            ->where("keynum='$basekeynum'")
            ->field("is_open_card, is_show_banner, is_first_category")
            ->find();

        if ($plat_config['is_show_banner'] != 1) {
            foreach ($menulist as $k => $v) {
                if ($v['menu_id'] == 1246) {
                    unset($menulist[$k]);
                    break;
                }
            }
        }

        if ($plat_config['is_open_card'] != 1) {
            foreach ($menulist as $k => $v) {
                if ($v['menu_id'] == 1203 || $v['pid'] == 1203 || $v['menu_id'] == 1237 || $v['menu_id'] == 1236) {
                    unset($menulist[$k]);
                }
            }
        }


        $menulist_arr = Get_Menu_Tree($menulist, "");
        if (empty($menulist_arr)) {
            error_tips("当前登录用户没有此菜单操作权限，请联系管理员授权！");
            die;
        }
        $this->assign('menulist_arr', $menulist_arr[0]['children']);
    }



    /**
     * 判断账号是否登录
     */
    public function check_login()
    {
        //需要放行的方法
        if ($this->check_pass()) {
            return true;
        }
        $flag = false;
        if (session('cn_accountinfo') != '') {
            $flag = true;
        }
        return $flag;
    }



    /**
     * 检查当前方法是否被放行
     */
    public function check_pass()
    {
        $fc = request()->action();
        if (in_array($fc, array("checktool"))) {
            return true;
        } else {
            return  false;
        }
    }


    /**
     * 退出系统 清除session
     */
    public function logout()
    {
        session("cn_accountinfo", null);
        $rt['sta'] = "1";
        $rt['msg'] = "退出成功，正在跳转。";
        echo json_encode($rt);
        die;
    }


    /**
     * 修改密码
     */
    public function ajax_changepassword()
    {
        $request = Request::instance();
        $param = $request->param();
        $oldpassword = trim($param['oldpassword']);
        $newpassword = trim($param['newpassword']);
        $re_newpassword = trim($param['re_newpassword']);
        //先验证旧密码是否正确
        $accountname = session("cn_accountinfo.accountname");
        $plat_account = Db::table('plat_account');

        $encryptoldpassword = password($oldpassword);
        $plat_accountinfo = $plat_account->where(" accountname='$accountname' and  accountpassword='$encryptoldpassword'")->find();
        if (!$plat_accountinfo['keynum']) {
            $rt['sta'] = "0";
            $rt['msg'] = "旧密码不正确!";
            echo json_encode($rt);
            die;
        }
        //新密码两次是否一致
        if ($newpassword != $re_newpassword) {
            $rt['sta'] = "0";
            $rt['msg'] = "新密码和确认密码不匹配！";
            echo json_encode($rt);
            die;
        }
        //更新会员账号密码
        $data['accountpassword'] = password($newpassword);
        Db::table('plat_account')->where("accountname", $accountname)->update($data);
        //清空会员信息
        session("cn_accountinfo", null);
        $rt['sta'] = "1";
        $rt['msg'] = "密码修改成功！";
        echo json_encode($rt);
        die;
    }
}
