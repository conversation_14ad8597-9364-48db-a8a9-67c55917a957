<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright ( c ) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use app\admin\model\Order;
use app\admin\model\Shop;
use app\api\model\OrderLog;
use think\facade\Session;
use think\facade\Request;
use think\Db;
use think\Tree;
use think\facade\Cache;

class SorderController extends CnController
{
    /**
     * 初始化方法
     */

    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    public function orderlist()
    {
        $request = Request::instance();
        $param = $request->param();
        $status = $param['status'];
        if ($status != '') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/orderlist?status=' . $status, 0);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/orderlist', 0);
        }

        return $this->fetch('orderlist');
    }

    public function ajax_get_order_list()
    {
        $request = Request::instance();
        $param = $request->param();
        $status = $param['status'];
        if ($status != '') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/orderlist?status=' . $status, 1);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/orderlist', 1);
        }
        $basekeynum = session('cn_accountinfo.keynum');
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        //计算记录偏移量
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $where = "1=1  and  shop_id='$shop_id' and status <> 0";
        if ($status !== '') {
            $where .= " and status='$status'";
        }
        if ($keyword) {
            $where .= " and $field like '%$keyword%'";
        }
        $starttime = $param['starttime'];
        $endtime = $param['endtime'];
        if ($starttime && $endtime) {
            $where .= " and add_time>='$starttime' and add_time<='$endtime'";
        }
        $list = Db::table('order')->where($where)->where('status', '>', 0)->order('id desc')->limit($offset . ',' . $pagesize)->select();
        $count = Db::table('order')->where($where)->where('status', '>', 0)->count();
        foreach ($list as $key => &$value) {
            $value['status'] = $this->get_order_status($value['status']);
            $value['type'] = $this->get_order_type($value['type']);
            $address = json_decode($value['address_json'], true);
            $value['name'] = $address['consignee'];
            $value['phone'] = $address['phone'];
            $value['address'] = $address['province'] . $address['city'] . $address['area'] . $address['address'];
            $value['is_free_shipping_free'] = $value['is_free_shipping_free'] == 1 ? '是' : '否';
            $order_detail = Db::table('order_detail')->where('order_no', $value['order_no'])->select();
            $goodsinfo = "";
            foreach ($order_detail as $k => &$v) {
                $goods = json_decode($v['product_json'], true);
                $spec = json_decode($v['inventory_json'], true);
                $goodsinfo .= $goods['title'] . '【' . $spec['title'] . '】' . "(".$v['number'].")" . '+';
            }
            $value['goodsinfo'] = rtrim($goodsinfo, '+');
            $value['pay_type_text'] = $this->getPayTypeTextAttr($value['pay_type']);
        }
        $rtdata['data'] = $list;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        echo json_encode($rtdata);
        die;
    }


    public function getPayTypeTextAttr($value)
    {
        $arr = [
            1 => '微信支付',
            2 => '储值卡支付',
            3 => '组合支付',
            4 => '线下支付'
        ];
        return $arr[$value];
    }

    public function get_order_status($status)
    {
        $status_arr = [
            // '0' => '待支付',
            '1' => '待审核',
            '2' => '已接单',
            '3' => '配送中',
            '100' => '已完成',
            '-1' => '已取消',
            4 => '已完成'
        ];
        if (!isset($status_arr[$status])) {
            return '未知';
        }
        return $status_arr[$status];
    }

    public function get_order_type($type)
    {
        $type_arr = [
            '1' => '自提订单',
            '2' => '配送订单',
            '3' => '结算台订单',
        ];
        if (!isset($type_arr[$type])) {
            return '未知';
        }
        return $type_arr[$type];
    }

    public function order_logs()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
//        check_auth(request()->controller() . '/orderlist', 0);
        $id = Request()->param('id');
        if (empty($id)) error_tips('参数错误');
        $this->assign('id', $id);
        return $this->fetch();

    }

    public function ajax_order_logs()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
//        check_auth(request()->controller() . 'orderlist', 1);
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.parent_basekeynum');
        $shop_id = Shop::where(['keynum' => $basekeynum])->value('id');
        $id = Request()->param('id');

        $order = Order::where(['clientkeynum' => $clientkeynum, 'id' => $id, 'shop_id' => $shop_id])->find();
        if (empty($order)) fail(-1 ,'订单不存在');

        $page = Request()->param('page', 1);
        $page_size = Request()->param('page_size', 10);

        $where = ['clientkeynum' => $clientkeynum, 'order_no' => $order['order_no'], 'shop_id' => $shop_id];

        $list = OrderLog::where($where)->page($page, $page_size)->order('id', 'desc')->select();
        $count = OrderLog::where($where)->count();

        success(0, '请求成功', $list, $count);

    }

    /**
     * 获取订单统计数据
     */
    public function ajax_order_stats()
    {
        // 权限校验
        $request = Request::instance();
        $param = $request->param();
        $status = $param['status'];
        if ($status != '') {
            check_auth(request()->controller() . '/orderlist?status=' . $status, 1);
        } else {
            check_auth(request()->controller() . '/orderlist', 1);
        }
        
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.parent_basekeynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        
        // 获取筛选条件
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $starttime = isset($param['starttime']) ? $param['starttime'] : '';
        $endtime = isset($param['endtime']) ? $param['endtime'] : '';
        // 支持新的日期参数名称
        $start_date = isset($param['start_date']) ? $param['start_date'] : '';
        $end_date = isset($param['end_date']) ? $param['end_date'] : '';
        $type1 = isset($param['type1']) ? $param['type1'] : '';
        
        // 构建查询条件
        $where = "1=1 and shop_id='$shop_id' and status <> 0";
        if ($status !== '') {
            $where .= " and status='$status'";
        }
        if ($keyword) {
            $where .= " and $field like '%$keyword%'";
        }
        // 支持新旧两种日期参数
        if ($starttime && $endtime) {
            $where .= " and add_time>='$starttime' and add_time<='$endtime'";
        } elseif ($start_date && $end_date) {
            $where .= " and add_time>='$start_date' and add_time<='$end_date'";
        }
        // 支持订单类型筛选
        if ($type1 && $type1 != '0') {
            $where .= " and type='$type1'";
        }
        
        // 计算统计数据
        $model = Db::table('order')->where($where)->where('status', '>', 0);
        
        $stats = [
            'total_shipping_price' => $model->sum('shipping_price') ?: 0,
            'total_card_price' => $model->sum('card_price') ?: 0,
            'total_real_price' => $model->sum('real_price') ?: 0,
            'total_offline_price' => $model->sum('offline_price') ?: 0,
            'total_price' => $model->sum('price') ?: 0,
            'order_count' => $model->count() ?: 0,
            'total_product_total' => $model->sum('product_total') ?: 0,
            'total_discount_amount' => $model->sum('discount_amount') ?: 0,
        ];
        
        $rtdata['data'] = $stats;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '请求成功';
        echo json_encode($rtdata);
        die;
    }

    /**
     * 获取订单详情列表
     */
    public function ajax_order_detail_list()
    {
        // 权限校验
        check_auth(request()->controller() . '/orderlist', 1);
        
        $request = Request::instance();
        $param = $request->param();
        $order_no = $param['order_no'];
        
        if (empty($order_no)) {
            echo json_encode(['code' => 1, 'msg' => '订单号不能为空', 'data' => [], 'count' => 0]);
            die;
        }
        
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        
        // 验证订单是否属于当前店铺
        $order = Db::table('order')->where('order_no', $order_no)->where('shop_id', $shop_id)->find();
        if (empty($order)) {
            echo json_encode(['code' => 1, 'msg' => '订单不存在', 'data' => [], 'count' => 0]);
            die;
        }
        
        // 获取订单详情
        $order_details = Db::table('order_detail')->where('order_no', $order_no)->select();
        
        $list = [];
        foreach ($order_details as $detail) {
            $product_json = json_decode($detail['product_json'], true);
            $inventory_json = json_decode($detail['inventory_json'], true);
            
            // 判断是否为计量商品
            $is_weight_product = !empty($inventory_json['is_weight']) && $inventory_json['is_weight'] == 1;
            
            $item = [
                'id' => $detail['id'],
                'order_no' => $detail['order_no'],
                'product_title' => $product_json['title'] ?? '',
                'inventory_title' => $inventory_json['title'] ?? '',
                'inventory_json' => $inventory_json,
                'display_info' => [
                    'type' => $is_weight_product ? '计量商品' : '普通商品',
                    'quantity' => $detail['number'],
                    'weight' => $detail['weight'] ?? 0,
                    'unit_price' => $detail['price'],
                    'total_price' => $detail['amount']
                ]
            ];
            
            $list[] = $item;
        }
        
        $rtdata = [
            'code' => 0,
            'msg' => '',
            'data' => $list,
            'count' => count($list)
        ];
        
        echo json_encode($rtdata);
        die;
    }
}
