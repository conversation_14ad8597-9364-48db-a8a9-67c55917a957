<?php

namespace app\admin\controller;

use app\admin\model\Shop;
use think\Request;
use think\Db;

class SconfigController extends CnController
{

    /**
     * 初始化方法
     */
    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    public function webconfig()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/webconfig', 0);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.basekeynum');
        //平台客户的keynum
        $info = Db::table('shop_config')->where("clientkeynum ='$clientkeynum' and keynum = '$basekeynum'")->find();
        $this->assign('info', $info);
        return $this->fetch('webconfig');
    }

    public function ajax_add_webconfig(Request $request)
    {
        check_auth(request()->controller() . '/webconfig', 0);
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $param = $request->param();
        $where = "clientkeynum='$clientkeynum' and keynum = '$basekeynum'";
        $find = Db::table("shop_config")->where($where)->find();
        $data = $param;
        $data["clientkeynum"] = $clientkeynum;
        $data["keynum"] = $basekeynum;
        if ($find) {
            $rs = Db::table("shop_config")->where($where)->update($data);
        } else {
            $rs = Db::table("shop_config")->insert($data);
        }
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        addoperatelog('修改配送基本设置', json_encode($param,JSON_UNESCAPED_UNICODE));
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    /**
     * 店铺配送能力配置页面
     */
    public function deliveryCapacity()
    {
        // 权限校验
        check_auth(request()->controller() . '/deliveryCapacity', 0);
        
        // 获取平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        
        // 获取店铺列表
        $shops = Db::table('shop')->where("clientkeynum = '$basekeynum'")->select();
        
        // 默认选中第一个店铺
        $shopId = request()->param('shop_id', isset($shops[0]) ? $shops[0]['id'] : 0);
        
        // 获取当前店铺的配送能力配置
        $capacityList = Db::table('shop_delivery_capacity')
            ->where("shop_id = $shopId AND basekeynum = '$basekeynum'")
            ->order('delivery_time_range')
            ->select();
        
        $this->assign('shops', $shops);
        $this->assign('shopId', $shopId);
        $this->assign('capacityList', $capacityList);
        
        return $this->fetch('delivery_capacity');
    }

    /**
     * 添加或编辑店铺配送能力
     */
    public function saveDeliveryCapacity(Request $request)
    {
        // 权限校验
        check_auth(request()->controller() . '/deliveryCapacity', 1);
        
        // 获取平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        
        // 获取表单数据
        $param = $request->param();
        $id = isset($param['id']) ? intval($param['id']) : 0;
        $shopId = isset($param['shop_id']) ? intval($param['shop_id']) : 0;
        $timeRange = isset($param['delivery_time_range']) ? $param['delivery_time_range'] : '';
        $maxCapacity = isset($param['max_capacity']) ? intval($param['max_capacity']) : 0;
        $status = isset($param['status']) ? intval($param['status']) : 1;
        
        // 表单验证
        if (empty($shopId)) {
            return json(['sta' => 0, 'msg' => '请选择店铺']);
        }
        
        if (empty($timeRange)) {
            return json(['sta' => 0, 'msg' => '请输入配送时间段']);
        }
        
        if ($maxCapacity <= 0) {
            return json(['sta' => 0, 'msg' => '最大配送量必须大于0']);
        }
        
        // 检查时间段格式是否正确（如：9:00-10:00）
        if (!preg_match('/^\d{1,2}:\d{2}-\d{1,2}:\d{2}$/', $timeRange)) {
            return json(['sta' => 0, 'msg' => '时间段格式不正确，正确格式如：9:00-10:00']);
        }
        
        // 检查该店铺下是否已有相同时间段
        if ($id == 0) {  // 新增时才检查
            $existTimeRange = Db::table('shop_delivery_capacity')
                ->where("shop_id = $shopId AND basekeynum = '$basekeynum' AND delivery_time_range = '$timeRange'")
                ->find();
            if ($existTimeRange) {
                return json(['sta' => 0, 'msg' => '该时间段已存在，不可重复添加']);
            }
        }
        
        // 准备数据
        $data = [
            'shop_id' => $shopId,
            'basekeynum' => $basekeynum,
            'delivery_time_range' => $timeRange,
            'max_capacity' => $maxCapacity,
            'status' => $status,
            'update_time' => date('Y-m-d H:i:s'),
            'operator_id' => session('cn_accountinfo.id')
        ];
        
        // 保存数据
        if ($id > 0) {
            // 更新
            $result = Db::table('shop_delivery_capacity')->where('id', $id)->update($data);
            $operateLog = '修改店铺配送能力配置';
        } else {
            // 新增
            $data['add_time'] = date('Y-m-d H:i:s');
            $result = Db::table('shop_delivery_capacity')->insert($data);
            $operateLog = '添加店铺配送能力配置';
        }
        
        if ($result === false) {
            return json(['sta' => 0, 'msg' => '操作失败']);
        }
        
        // 记录操作日志
        addoperatelog($operateLog, json_encode($param, JSON_UNESCAPED_UNICODE));
        
        return json(['sta' => 1, 'msg' => '操作成功']);
    }

    /**
     * 获取店铺配送能力列表（AJAX）
     */
    public function getDeliveryCapacityList(Request $request)
    {
        // 权限校验
        check_auth(request()->controller() . '/deliveryCapacity', 1);
        
        // 获取平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        
        $shopId = $request->param('shop_id', 0);
        
        if (empty($shopId)) {
            return json(['sta' => 0, 'msg' => '请选择店铺']);
        }
        
        // 获取数据
        $list = Db::table('shop_delivery_capacity')
            ->where("shop_id = $shopId AND basekeynum = '$basekeynum'")
            ->order('delivery_time_range')
            ->select();
        
        // 获取店铺信息
        $shop = Db::table('shop')->where('id', $shopId)->find();
        
        return json([
            'sta' => 1, 
            'data' => [
                'list' => $list,
                'shop' => $shop
            ]
        ]);
    }

    /**
     * 删除店铺配送能力设置
     */
    public function deleteDeliveryCapacity(Request $request)
    {
        // 权限校验
        check_auth(request()->controller() . '/deliveryCapacity', 1);
        
        $id = $request->param('id', 0);
        
        if (empty($id)) {
            return json(['sta' => 0, 'msg' => '参数错误']);
        }
        
        // 删除数据
        $result = Db::table('shop_delivery_capacity')->where('id', $id)->delete();
        
        if ($result === false) {
            return json(['sta' => 0, 'msg' => '删除失败']);
        }
        
        // 记录操作日志
        addoperatelog('删除店铺配送能力配置', json_encode(['id' => $id], JSON_UNESCAPED_UNICODE));
        
        return json(['sta' => 1, 'msg' => '删除成功']);
    }

    /**
     * 批量更新店铺配送能力状态
     */
    public function batchUpdateStatus(Request $request)
    {
        // 权限校验
        check_auth(request()->controller() . '/deliveryCapacity', 1);
        
        $ids = $request->param('ids');
        $status = $request->param('status', 0);
        
        if (empty($ids) || !is_array($ids)) {
            return json(['sta' => 0, 'msg' => '请选择要操作的记录']);
        }
        
        // 更新状态
        $result = Db::table('shop_delivery_capacity')
            ->where('id', 'in', $ids)
            ->update(['status' => $status, 'update_time' => date('Y-m-d H:i:s')]);
        
        if ($result === false) {
            return json(['sta' => 0, 'msg' => '操作失败']);
        }
        
        // 记录操作日志
        $statusText = $status == 1 ? '启用' : '禁用';
        addoperatelog("批量{$statusText}店铺配送能力配置", json_encode(['ids' => $ids, 'status' => $status], JSON_UNESCAPED_UNICODE));
        
        return json(['sta' => 1, 'msg' => '操作成功']);
    }
}
