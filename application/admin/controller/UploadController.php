<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright (c) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use OSS\OssClient;
use OSS\Core\OssException;
use think\Db;
use think\Request;

class UploadController extends CnController
{
    public function upload(Request $request)
    {

        $basekeynum = session('cn_accountinfo.basekeynum');
        $info = Db::table('plat_client')->where('keynum', $basekeynum)->find();
        if (isset($_FILES['file'])) {
            $Img = $this->saveimg($_FILES['file']);
            success(200, '', ['url' => $Img]);
        } else {
            fail(-1, '上传失败');
        }
    }

    public function index($type = null)
    {
    }

    public function uploadpic()
    {
        $Img = input('param.Img');
        $Path = null;
        if (isset($_FILES['img'])) {
            $Img = $this->saveimg($_FILES['img']);
        }
        $BackCall = input('param.BackCall');
        $Width = input('param.Width');
        $Height = input('param.Height');
        if (!$BackCall) {
            $Width = input('param.BackCall');
        }
        if (!$Width) {
            $Width =  input('param.Width');
        }
        if (!$Height) {
            $Width = input('param.Height');
        }
        $this->assign('Width', $Width);
        $this->assign('BackCall', $BackCall);
        $this->assign('Img', $Img);
        $this->assign('Height', $Height);
        return $this->fetch("uploadpic");
    }

    public function uploadvideo()
    {
        $Video = input('param.Video');
        $Path = null;
        if (isset($_FILES['video'])) {
            $Video = $this->savevideo($_FILES['video']);
        }
        $BackCall = input('param.BackCall');
        $Width = input('param.Width');
        $Height = input('param.Height');
        if (!$BackCall) {
            $Width = input('param.BackCall');
        }
        if (!$Width) {
            $Width = input('param.Width');
        }
        if (!$Height) {
            $Width = input('param.Height');
        }
        $this->assign('Width', $Width);
        $this->assign('BackCall', $BackCall);
        $this->assign('Video', $Video);
        $this->assign('Height', $Height);
        return $this->fetch("uploadvideo");
    }




    private function saveimg($file)
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $info = Db::table('plat_client')->where('keynum', $basekeynum)->find();
        $uptypes = array(
            'image/jpeg',
            'image/jpg',
            'image/jpeg',
            'image/png',
            'image/pjpeg',
            'image/gif',
            'image/bmp',
            'image/x-png',
            'image/x-icon'
        );
        $max_file_size = 2000000;     //上传文件大小限制, 单位BYTE
        $destination_folder = 'static/upload/' . $info['clientnum'] . '/images/' . date('Ym') . '/'; //上传文件路径
        if ($max_file_size < $file["size"]) {
            echo "文件太大!";
            return null;
        }
        if (!in_array($file["type"], $uptypes)) {
            $name = $file["name"];
            $type = $file["type"];
            echo "<script>alert('{$name}文件类型不符!{$type}')</script>";
            return null;
        }
        $filename = $file["tmp_name"];
        $image_size = getimagesize($filename);
        $pinfo = pathinfo($file["name"]);
        $ftype = $pinfo['extension'];
        //不重复名字的文件
        $imgname = create_guid() . "." . $ftype;
        $destination = $destination_folder . $imgname;
        //上传到阿里云oss
        $arr = uploadFileToAliOss($filename, $destination);
        if ($arr['sta'] != '1') {
            return  $arr['msg'];
            die;
        }
        return  $arr['url'];
        die;
        /*
        if (!is_dir($destination_folder)) {
            mkdir($destination_folder,0777,true);
        }
        if (!move_uploaded_file($filename, $destination)) {
            return null;
        }
        return "/" . $destination;
        */
    }

    private function savevideo($file)
    {
        $uptypes = array(
            'video/mp4',
        );
        $max_file_size = 20000000000000;     //上传文件大小限制, 单位BYTE
        $destination_folder = 'static/upload/videos/' . date('Ym') . '/'; //上传文件路径
        if ($max_file_size < $file["size"]) {
            echo "文件太大!";
            return null;
        }
        if (!in_array($file["type"], $uptypes)) {
            $name = $file["name"];
            $type = $file["type"];
            echo "<script>alert('{$name}文件类型不符!{$type}')</script>";
            return null;
        }

        if (!file_exists($destination_folder)) {
            mkdir($destination_folder, 0777, true);
        }
        $filename = $file["tmp_name"];
        $image_size = getimagesize($filename);
        $pinfo = pathinfo($file["name"]);
        $ftype = $pinfo['extension'];
        $videoname = create_guid() . "." . $ftype;
        $destination = $destination_folder . $videoname;
        if (!move_uploaded_file($filename, $destination)) {
            return null;
        }
        return "/" . $destination;
    }







    public function batchpic()
    {
        $number = 1;
        $ImgStr = input('param.Img');
        $ImgStr = trim($ImgStr, '|');
        $Img = array();
        if (strlen($ImgStr) > 1) {
            $Img = explode('|', $ImgStr);
        }
        if (is_array($Img)) {
            $number = count($Img);
        }
        $Path = null;
        if (isset($_FILES['uploadimg'])) {
            if ($number < 5) {
                foreach ($_FILES['uploadimg']['name'] as $key => $value) {
                    $fileinfo = array(
                        'name' => $_FILES['uploadimg']['name'][$key],
                        'type' => $_FILES['uploadimg']['type'][$key],
                        'tmp_name' => $_FILES['uploadimg']['tmp_name'][$key],
                        'error' => $_FILES['uploadimg']['error'][$key],
                        'size' => $_FILES['uploadimg']['size'][$key],
                    );

                    $filename = $this->saveimg($fileinfo);
                    if ($filename) {
                        array_push($Img, $filename);
                    }
                }
            } else {
                echo "<script>alert('最多只能上传五张图片')</script>";
                die;
            }
        }
        $ImgStr = implode("|", $Img);
        $BackCall = input('param.BackCall');
        $Width = input('param.u');
        $Height = input('param.Height');
        if (!$BackCall) {
            $Width = input('param.BackCall');
        }
        if (!$Width) {
            $Width = input('param.Width');
        }
        if (!$Height) {
            $Width = input('param.Height');
        }
        $this->assign('Width', $Width);
        $this->assign('BackCall', $BackCall);
        $this->assign('ImgStr', $ImgStr);
        $this->assign('Img', $Img);
        $this->assign('Height', $Height);
        return $this->fetch("batchpic");
    }
}
