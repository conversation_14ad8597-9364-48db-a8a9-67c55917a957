<?php

namespace app\admin\controller;

use app\admin\model\YlyShopPrinterConfig;
use app\admin\model\Shop;
use app\common\service\YlyPrintService;
use think\Request;
use think\Controller;

/**
 * 打印机管理控制器
 * Class YlyPrinterController
 * @package app\admin\controller
 */
class YlyPrinterController extends Controller
{
    /**
     * 打印机列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $params = request()->param();
            $clientkeynum = session('cn_accountinfo.clientkeynum');
            
            $condition = [];
            
            // 搜索条件
            if (!empty($params['shop_id'])) {
                $condition['shop_id'] = $params['shop_id'];
            }
            
            if (!empty($params['printer_sn'])) {
                $condition['printer_sn'] = ['like', '%' . $params['printer_sn'] . '%'];
            }
            
            if (isset($params['status']) && $params['status'] !== '') {
                $condition['status'] = $params['status'];
            }
            
            $list = YlyShopPrinterConfig::getConfigList($clientkeynum, $condition);
            
            return json([
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $list->items()
            ]);
        }
        
        return $this->fetch();
    }
    
    /**
     * 添加打印机配置
     */
    public function add()
    {
        if (request()->isPost()) {
            $params = request()->param();
            $clientkeynum = session('cn_accountinfo.clientkeynum');
            
            // 验证数据
            $errors = YlyShopPrinterConfig::validateConfig($params);
            if (!empty($errors)) {
                return json(['code' => 0, 'msg' => implode('，', $errors)]);
            }
            
            $data = [
                'clientkeynum' => $clientkeynum,
                'shop_id' => intval($params['shop_id']),
                'printer_sn' => trim($params['printer_sn']),
                'printer_key' => trim($params['printer_key']),
                'printer_name' => trim($params['printer_name'] ?? ''),
                'print_copies' => intval($params['print_copies'] ?? 1),
                'auto_print' => intval($params['auto_print'] ?? 1),
                'status' => intval($params['status'] ?? 1)
            ];
            
            try {
                $result = YlyShopPrinterConfig::create($data);
                if ($result) {
                    return json(['code' => 1, 'msg' => '添加成功']);
                } else {
                    return json(['code' => 0, 'msg' => '添加失败']);
                }
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '添加失败: ' . $e->getMessage()]);
            }
        }
        
        return $this->fetch();
    }
    
    /**
     * 编辑打印机配置
     */
    public function edit()
    {
        $id = request()->param('id');
        
        if (request()->isPost()) {
            $params = request()->param();
            $clientkeynum = session('cn_accountinfo.clientkeynum');
            
            // 验证数据
            $params['id'] = $id;
            $errors = YlyShopPrinterConfig::validateConfig($params);
            if (!empty($errors)) {
                return json(['code' => 0, 'msg' => implode('，', $errors)]);
            }
            
            $data = [
                'shop_id' => intval($params['shop_id']),
                'printer_sn' => trim($params['printer_sn']),
                'printer_key' => trim($params['printer_key']),
                'printer_name' => trim($params['printer_name'] ?? ''),
                'print_copies' => intval($params['print_copies'] ?? 1),
                'auto_print' => intval($params['auto_print'] ?? 1),
                'status' => intval($params['status'] ?? 1)
            ];
            
            try {
                $result = YlyShopPrinterConfig::where([
                    'id' => $id,
                    'clientkeynum' => $clientkeynum
                ])->update($data);
                
                if ($result !== false) {
                    return json(['code' => 1, 'msg' => '修改成功']);
                } else {
                    return json(['code' => 0, 'msg' => '修改失败']);
                }
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '修改失败: ' . $e->getMessage()]);
            }
        }
        
        $clientkeynum = session('cn_accountinfo.clientkeynum');
        $info = YlyShopPrinterConfig::where([
            'id' => $id,
            'clientkeynum' => $clientkeynum
        ])->find();
        
        if (!$info) {
            $this->error('记录不存在');
        }
        
        $this->assign('info', $info);
        return $this->fetch();
    }
    
    /**
     * 删除打印机配置
     */
    public function delete()
    {
        $id = request()->param('id');
        $clientkeynum = session('cn_accountinfo.clientkeynum');
        
        if (empty($id)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        try {
            $result = YlyShopPrinterConfig::where([
                'id' => $id,
                'clientkeynum' => $clientkeynum
            ])->delete();
            
            if ($result) {
                return json(['code' => 1, 'msg' => '删除成功']);
            } else {
                return json(['code' => 0, 'msg' => '删除失败']);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 批量操作
     */
    public function batch()
    {
        $action = request()->param('action');
        $ids = request()->param('ids');
        $clientkeynum = session('cn_accountinfo.clientkeynum');
        
        if (empty($action) || empty($ids)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        $ids = is_array($ids) ? $ids : explode(',', $ids);
        
        try {
            switch ($action) {
                case 'enable':
                    $result = YlyShopPrinterConfig::where([
                        'id' => ['in', $ids],
                        'clientkeynum' => $clientkeynum
                    ])->update(['status' => 1]);
                    break;
                    
                case 'disable':
                    $result = YlyShopPrinterConfig::where([
                        'id' => ['in', $ids],
                        'clientkeynum' => $clientkeynum
                    ])->update(['status' => 0]);
                    break;
                    
                case 'delete':
                    $result = YlyShopPrinterConfig::where([
                        'id' => ['in', $ids],
                        'clientkeynum' => $clientkeynum
                    ])->delete();
                    break;
                    
                default:
                    return json(['code' => 0, 'msg' => '未知操作']);
            }
            
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '操作成功']);
            } else {
                return json(['code' => 0, 'msg' => '操作失败']);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取统计信息
     */
    public function statistics()
    {
        $clientkeynum = session('cn_accountinfo.clientkeynum');
        $statistics = YlyShopPrinterConfig::getStatistics($clientkeynum);
        
        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => $statistics
        ]);
    }
    
    /**
     * 导出配置
     */
    public function export()
    {
        $clientkeynum = session('cn_accountinfo.clientkeynum');
        
        // 获取所有配置
        $list = YlyShopPrinterConfig::where('clientkeynum', $clientkeynum)
            ->order('created_at desc')
            ->select();
        
        // 导出数据处理
        $exportData = [];
        foreach ($list as $item) {
            $exportData[] = [
                '门店ID' => $item['shop_id'],
                '打印机序列号' => $item['printer_sn'],
                '打印机昵称' => $item['printer_name'],
                '打印份数' => $item['print_copies'],
                '自动打印' => $item['auto_print'] ? '开启' : '关闭',
                '状态' => $item['status'] ? '启用' : '禁用',
                '打印次数' => $item['print_count'],
                '最后打印时间' => $item['last_print_time'],
                '创建时间' => $item['created_at']
            ];
        }
        
        // 这里需要根据实际项目的Excel导出类进行处理
        // 示例返回JSON格式，实际应该返回Excel文件
        return json([
            'code' => 1,
            'msg' => '导出成功',
            'data' => $exportData
        ]);
    }
    
    /**
     * 测试打印机
     */
    public function testPrint(Request $request)
    {
        if (!$request->isPost()) {
            $this->error('请求方式错误');
        }
        
        $id = $request->param('id', 0);
        $printerConfig = YlyShopPrinterConfig::find($id);
        
        if (!$printerConfig) {
            $this->error('打印机配置不存在');
        }
        
        $clientkeynum = $this->getClientkeynum();
        
        try {
            $printService = new YlyPrintService($clientkeynum);
            $result = $printService->testPrinter($printerConfig['printer_sn']);
            
            if ($result['success']) {
                $this->success($result['message']);
            } else {
                $this->error($result['message']);
            }
        } catch (\Exception $e) {
            $this->error('测试失败: ' . $e->getMessage());
        }
    }
    
    } 