<?php
namespace app\admin\controller;

use app\admin\model\InventoryOrder;
use app\admin\model\InventoryOrderDetail;
use app\admin\model\InventoryLog;
use think\Request;
use think\Db;

/**
 * 店铺端库存管理控制器
 * Class SinventoryController
 * @package app\admin\controller
 */
class SinventoryController extends CnController
{
    /**
     * 初始化方法
     */
    public function initialize()
    {
        $this->init();
        // 每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    /**
     * 入库单列表页面
     */
    public function index()
    {
        // 权限校验
        check_auth(request()->controller() . '/index', 0);
        return $this->fetch();
    }

    /**
     * 出库单列表页面
     */
    public function outstock()
    {
        // 权限校验
        check_auth(request()->controller() . '/outstock', 0);
        return $this->fetch();
    }
    
    /**
     * 获取入库单列表
     */
    public function getInStockList()
    {
        // 权限校验
        check_auth(request()->controller() . '/index', 1);
        
        // 获取请求参数
        $page = input('page', 1, 'intval');
        $limit = input('limit', 15, 'intval');
        $order_no = input('order_no', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $date_range = input('date_range', '');
        
        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');
        
        // 构建查询条件
        $where = [
            ['order_type', '=', 1], // 只查询入库单
            ['shop_id', '=', $shop_id] // 只查询当前门店的单据
        ];
        
        if (!empty($order_no)) {
            $where[] = ['order_no', 'like', "%{$order_no}%"];
        }
        if (!empty($business_type)) {
            $where[] = ['business_type', '=', $business_type];
        }
        if ($status !== '') {
            $where[] = ['status', '=', $status];
        }
        if (!empty($date_range)) {
            $dates = explode(' - ', $date_range);
            if (count($dates) == 2) {
                $where[] = ['created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
            }
        }

        // 获取列表数据
        $model = new InventoryOrder();
        $data = $model->getList($where, $page, $limit);

        // 处理数据
        foreach ($data['list'] as &$item) {
            // 格式化单据类型
            $item['order_type_text'] = $this->getOrderTypeText($item['order_type']);

            // 格式化业务类型
            $item['business_type_text'] = $this->getBusinessTypeText($item['order_type'], $item['business_type']);

            // 格式化状态
            $item['status_text'] = $this->getStatusText($item['status']);
        }

        return json(['code' => 0, 'msg' => 'success', 'count' => $data['count'], 'data' => $data['list']]);
    }

    /**
     * 获取出库单列表
     */
    public function getOutStockList()
    {
        // 权限校验
        check_auth(request()->controller() . '/outstock', 1);
        
        // 获取请求参数
        $page = input('page', 1, 'intval');
        $limit = input('limit', 15, 'intval');
        $order_no = input('order_no', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $date_range = input('date_range', '');
        
        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');
        
        // 构建查询条件
        $where = [
            ['order_type', '=', 2], // 查询出库单和销售出库单
            ['shop_id', '=', $shop_id] // 只查询当前门店的单据
        ];
        
        if (!empty($order_no)) {
            $where[] = ['order_no', 'like', "%{$order_no}%"];
        }
        if (!empty($business_type)) {
            $where[] = ['business_type', '=', $business_type];
        }
        if ($status !== '') {
            $where[] = ['status', '=', $status];
        }
        if (!empty($date_range)) {
            $dates = explode(' - ', $date_range);
            if (count($dates) == 2) {
                $where[] = ['created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
            }
        }

        // 获取列表数据
        $model = new InventoryOrder();
        $data = $model->getList($where, $page, $limit);

        // 处理数据
        foreach ($data['list'] as &$item) {
            // 格式化单据类型
            $item['order_type_text'] = $this->getOrderTypeText($item['order_type']);

            // 格式化业务类型
            $item['business_type_text'] = $this->getBusinessTypeText($item['order_type'], $item['business_type']);

            // 格式化状态
            $item['status_text'] = $this->getStatusText($item['status']);
        }

        return json(['code' => 0, 'msg' => 'success', 'count' => $data['count'], 'data' => $data['list']]);
    }
    
    /**
     * 获取单据类型文本
     * @param int $order_type 单据类型
     * @return string
     */
    private function getOrderTypeText($order_type)
    {
        $types = [
            1 => '入库单',
            2 => '出库单',
            3 => '销售出库单'
        ];

        return isset($types[$order_type]) ? $types[$order_type] : '未知类型';
    }

    /**
     * 获取业务类型文本
     * @param int $order_type 单据类型
     * @param int $business_type 业务类型
     * @return string
     */
    private function getBusinessTypeText($order_type, $business_type)
    {
        $types = [
            1 => [ // 入库
                1 => '采购入库',
                2 => '调拨入库',
                3 => '退货入库',
                4 => '其他入库'
            ],
            2 => [ // 出库
                1 => '销售出库',
                2 => '调拨出库',
                3 => '报损出库',
                4 => '其他出库'
            ],
            3 => [ // 销售出库
                1 => '销售出库'
            ]
        ];

        if (isset($types[$order_type]) && isset($types[$order_type][$business_type])) {
            return $types[$order_type][$business_type];
        } else {
            return '未知类型';
        }
    }

    /**
     * 获取状态文本
     * @param int $status 状态
     * @return string
     */
    private function getStatusText($status)
    {
        $statuses = [
            0 => '草稿',
            1 => '已提交',
            2 => '已审核',
            3 => '已完成',
            -1 => '已取消'
        ];

        return isset($statuses[$status]) ? $statuses[$status] : '未知状态';
    }

    /**
     * 添加入库单页面
     */
    public function addInStock()
    {
        // 权限校验
        check_auth(request()->controller() . '/addInStock', 0);
        
        if (request()->isPost()) {
            // 获取表单数据
            $data = input('post.');

            // 参数验证
            if (empty($data['order_type']) || $data['order_type'] != 1) {
                return json(['code' => 1, 'msg' => '单据类型错误']);
            }
            if (empty($data['business_type'])) {
                return json(['code' => 1, 'msg' => '请选择业务类型']);
            }
            if (empty($data['supplier_name'])) {
                return json(['code' => 1, 'msg' => '请输入供应商名称']);
            }
            if (empty($data['details']) || !is_array($data['details'])) {
                return json(['code' => 1, 'msg' => '请添加商品明细']);
            }

            // 获取当前门店ID
            $basekeynum = session('cn_accountinfo.keynum');
            $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');
            
            // 设置门店ID
            $data['shop_id'] = $shop_id;

            // 检查商品是否都有商品ID
            $productIds = [];
            foreach ($data['details'] as $detail) {
                if (empty($detail['product_id'])) {
                    return json(['code' => 1, 'msg' => '请选择商品']);
                }
                if (empty($detail['quantity']) || $detail['quantity'] <= 0) {
                    return json(['code' => 1, 'msg' => '商品数量必须大于0']);
                }
                
                // 检查重复商品规格
                $inventoryKey = $detail['product_id'] . '_' . $detail['inventory_id'];
                if (in_array($inventoryKey, $productIds)) {
                    return json(['code' => 1, 'msg' => '不允许添加重复的商品规格，请合并数量']);
                }
                $productIds[] = $inventoryKey;
            }

            // 设置相关字段
            $data['clientkeynum'] = session('cn_accountinfo.basekeynum');
            $data['created_by'] = session('cn_accountinfo.accountname');

            // 开启事务
            Db::startTrans();
            try {
                // 添加库存单
                $model = new InventoryOrder();
                $details = $data['details'];
                unset($data['select'], $data['details']);
                // 移除关联单号相关的处理
                if (isset($data['related_order_no'])) {
                    unset($data['related_order_no']);
                }

                $id = $model->add($data);
                if (!$id) {
                    throw new \Exception('添加库存单失败');
                }

                // 获取订单编号
                $order = $model->where('id', $id)->find();

                // 添加库存单明细
                $detailModel = new InventoryOrderDetail();
                $result = $detailModel->addBatch($id, $order['order_no'], $data['order_type'], $details, $shop_id, '');
                if (!$result) {
                    throw new \Exception('添加库存单明细失败');
                }

                Db::commit();
                return json(['code' => 0, 'msg' => '添加成功', 'id' => $id]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // 返回添加页面数据
        return $this->fetch('add_in_stock');
    }

    /**
     * 添加出库单页面
     */
    public function addOutStock()
    {
        // 权限校验
        check_auth(request()->controller() . '/addOutStock', 0);
        
        if (request()->isPost()) {
            // 获取表单数据
            $data = input('post.');

            // 参数验证
            if (empty($data['order_type']) || !in_array($data['order_type'], [2, 3])) {
                return json(['code' => 1, 'msg' => '单据类型错误']);
            }
            if ($data['order_type'] == 2 && empty($data['business_type'])) {
                return json(['code' => 1, 'msg' => '请选择业务类型']);
            }
            if ($data['order_type'] == 3 && empty($data['member_id'])) {
                return json(['code' => 1, 'msg' => '请输入会员ID']);
            }
            if (empty($data['details']) || !is_array($data['details'])) {
                return json(['code' => 1, 'msg' => '请添加商品明细']);
            }

            // 获取当前门店ID
            $basekeynum = session('cn_accountinfo.keynum');
            $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');
            
            // 设置门店ID
            $data['shop_id'] = $shop_id;

            // 检查商品是否都有商品ID并验证库存
            $productIds = [];
            foreach ($data['details'] as $detail) {
                if (empty($detail['product_id'])) {
                    return json(['code' => 1, 'msg' => '请选择商品']);
                }
                if (empty($detail['quantity']) || $detail['quantity'] <= 0) {
                    return json(['code' => 1, 'msg' => '商品数量必须大于0']);
                }
                
                // 检查重复商品规格
                $inventoryKey = $detail['product_id'] . '_' . $detail['inventory_id'];
                if (in_array($inventoryKey, $productIds)) {
                    return json(['code' => 1, 'msg' => '不允许添加重复的商品规格，请合并数量']);
                }
                $productIds[] = $inventoryKey;
                
                // 验证库存是否足够
                $stockValidation = $this->validateStockForOutbound($detail['product_id'], $detail['inventory_id'], $detail['quantity'], $shop_id);
                if (!$stockValidation['success']) {
                    return json(['code' => 1, 'msg' => $stockValidation['message']]);
                }
            }

            // 设置相关字段
            $data['clientkeynum'] = session('cn_accountinfo.basekeynum');
            $data['created_by'] = session('cn_accountinfo.accountname');

            // 如果是销售出库单，设置默认业务类型
            if ($data['order_type'] == 3) {
                $data['business_type'] = 1; // 销售出库
            }

            // 开启事务
            Db::startTrans();
            try {
                // 添加库存单
                $model = new InventoryOrder();
                $details = $data['details'];
                unset($data['select'], $data['details']);
                // 移除关联单号相关的处理
                if (isset($data['related_order_no'])) {
                    unset($data['related_order_no']);
                }
                
                $id = $model->add($data);
                if (!$id) {
                    throw new \Exception('添加库存单失败');
                }

                // 获取订单编号
                $order = $model->where('id', $id)->find();

                // 添加库存单明细
                $detailModel = new InventoryOrderDetail();
                $result = $detailModel->addBatch($id, $order['order_no'], $data['order_type'], $details, $shop_id, '');
                if (!$result) {
                    throw new \Exception('添加库存单明细失败');
                }

                Db::commit();
                return json(['code' => 0, 'msg' => '添加成功', 'id' => $id]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // 返回添加页面数据
        return $this->fetch('add_out_stock');
    }

    /**
     * 编辑入库单
     */
    public function editInStock()
    {
        // 权限校验
        check_auth(request()->controller() . '/editInStock', 0);
        
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return $this->error('参数错误');
        }

        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');

        // 获取库存单详情
        $model = new InventoryOrder();
        $data = $model->getDetail($id);
        if (!$data) {
            return $this->error('库存单不存在');
        }

        // 检查是否为当前门店的库存单
        if ($data['order']['shop_id'] != $shop_id) {
            return $this->error('您无权编辑其他门店的库存单');
        }

        // 检查单据类型
        if ($data['order']['order_type'] != 1) {
            return $this->error('单据类型错误');
        }

        // 只允许编辑草稿状态的库存单
        if ($data['order']['status'] != 0) {
            return $this->error('只能编辑草稿状态的库存单');
        }

        if (request()->isPost()) {
            // 获取表单数据
            $postData = input('post.');

            // 参数验证
            if (empty($postData['details']) || !is_array($postData['details'])) {
                return json(['code' => 1, 'msg' => '请添加商品明细']);
            }

            // 检查商品规格是否有重复
            $productIds = [];
            foreach ($postData['details'] as $detail) {
                if (empty($detail['product_id'])) {
                    return json(['code' => 1, 'msg' => '请选择商品']);
                }
                if (empty($detail['quantity']) || $detail['quantity'] <= 0) {
                    return json(['code' => 1, 'msg' => '商品数量必须大于0']);
                }
                
                // 检查重复商品规格
                $inventoryKey = $detail['product_id'] . '_' . $detail['inventory_id'];
                if (in_array($inventoryKey, $productIds)) {
                    return json(['code' => 1, 'msg' => '不允许添加重复的商品规格，请合并数量']);
                }
                $productIds[] = $inventoryKey;
            }

            // 开启事务
            Db::startTrans();
            try {
                // 更新库存单
                $model->where('id', $id)->update([
                    'supplier_name' => $postData['supplier_name'] ?? '',
                    'remark' => $postData['remark'] ?? ''
                ]);

                // 删除原有明细
                $detailModel = new InventoryOrderDetail();
                $detailModel->removeByOrderId($id);

                // 添加新明细
                $result = $detailModel->addBatch($id, $data['order']['order_no'], $data['order']['order_type'], $postData['details'], $shop_id, '');
                if (!$result) {
                    throw new \Exception('更新库存单明细失败');
                }

                Db::commit();
                return json(['code' => 0, 'msg' => '更新成功']);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // 返回编辑页面数据
        $this->assign([
            'order' => $data['order'],
            'details' => $data['details']
        ]);
        return $this->fetch('edit_in_stock');
    }

    /**
     * 编辑出库单
     */
    public function editOutStock()
    {
        // 权限校验
        check_auth(request()->controller() . '/editOutStock', 0);
        
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return $this->error('参数错误');
        }

        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');

        // 获取库存单详情
        $model = new InventoryOrder();
        $data = $model->getDetail($id);
        if (!$data) {
            return $this->error('库存单不存在');
        }

        // 检查是否为当前门店的库存单
        if ($data['order']['shop_id'] != $shop_id) {
            return $this->error('您无权编辑其他门店的库存单');
        }

        // 检查单据类型
        if (!in_array($data['order']['order_type'], [2, 3])) {
            return $this->error('单据类型错误');
        }

        // 只允许编辑草稿状态的库存单
        if ($data['order']['status'] != 0) {
            return $this->error('只能编辑草稿状态的库存单');
        }

        if (request()->isPost()) {
            // 获取表单数据
            $postData = input('post.');

            // 参数验证
            if (empty($postData['details']) || !is_array($postData['details'])) {
                return json(['code' => 1, 'msg' => '请添加商品明细']);
            }

            // 检查商品规格是否有重复并验证库存
            $productIds = [];
            foreach ($postData['details'] as $detail) {
                if (empty($detail['product_id'])) {
                    return json(['code' => 1, 'msg' => '请选择商品']);
                }
                if (empty($detail['quantity']) || $detail['quantity'] <= 0) {
                    return json(['code' => 1, 'msg' => '商品数量必须大于0']);
                }
                
                // 检查重复商品规格
                $inventoryKey = $detail['product_id'] . '_' . $detail['inventory_id'];
                if (in_array($inventoryKey, $productIds)) {
                    return json(['code' => 1, 'msg' => '不允许添加重复的商品规格，请合并数量']);
                }
                $productIds[] = $inventoryKey;
                
                // 验证库存是否足够
                $stockValidation = $this->validateStockForOutbound($detail['product_id'], $detail['inventory_id'], $detail['quantity'], $shop_id);
                if (!$stockValidation['success']) {
                    return json(['code' => 1, 'msg' => $stockValidation['message']]);
                }
            }

            // 开启事务
            Db::startTrans();
            try {
                // 更新字段
                $updateData = [
                    'remark' => $postData['remark'] ?? ''
                ];

                // 如果是销售出库单，可以更新会员ID
                if ($data['order']['order_type'] == 3) {
                    $updateData['member_id'] = $postData['member_id'] ?? 0;
                }

                // 更新库存单
                $model->where('id', $id)->update($updateData);

                // 删除原有明细
                $detailModel = new InventoryOrderDetail();
                $detailModel->removeByOrderId($id);

                // 添加新明细
                $result = $detailModel->addBatch($id, $data['order']['order_no'], $data['order']['order_type'], $postData['details'], $shop_id, '');
                if (!$result) {
                    throw new \Exception('更新库存单明细失败');
                }

                Db::commit();
                return json(['code' => 0, 'msg' => '更新成功']);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // 返回编辑页面数据
        $this->assign([
            'order' => $data['order'],
            'details' => $data['details']
        ]);
        return $this->fetch('edit_out_stock');
    }

    /**
     * 查看库存单详情
     */
    public function detail()
    {
        // 权限校验
        check_auth(request()->controller() . '/index', 0);
        
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return $this->error('参数错误');
        }

        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');

        // 获取库存单详情
        $model = new InventoryOrder();
        $data = $model->getDetail($id);
        if (!$data) {
            return $this->error('库存单不存在');
        }

        // 检查是否为当前门店的库存单
        if ($data['order']['shop_id'] != $shop_id) {
            return $this->error('您无权查看其他门店的库存单');
        }

        // 处理数据
        $data['order']['order_type_text'] = $this->getOrderTypeText($data['order']['order_type']);
        $data['order']['business_type_text'] = $this->getBusinessTypeText($data['order']['order_type'], $data['order']['business_type']);
        $data['order']['status_text'] = $this->getStatusText($data['order']['status']);

        // 获取关联门店
        if ($data['order']['shop_id']) {
            $shop = Db::name('shop')->where([['id', '=', $data['order']['shop_id']]])->field('id, title')->find();
            $data['order']['title'] = $shop ? $shop['title'] : '';
        } else {
            $data['order']['title'] = '平台';
        }

        // 获取关联会员
        if ($data['order']['member_id']) {
            $member = Db::name('member')->where([['id', '=', $data['order']['member_id']]])->field('id, nickname, mobile')->find();
            $data['order']['member_name'] = $member ? ($member['nickname'] ?: $member['mobile']) : '';
        } else {
            $data['order']['member_name'] = '';
        }

        $this->assign('data', $data);
        return $this->fetch();
    }

    /**
     * 删除库存单
     */
    public function delete()
    {
        // 权限校验
        check_auth(request()->controller() . '/delete', 1);
        
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');

        // 获取库存单
        $model = new InventoryOrder();
        $order = $model->where('id', $id)->find();
        
        // 检查库存单是否存在
        if (!$order) {
            return json(['code' => 1, 'msg' => '库存单不存在']);
        }
        
        // 检查是否为当前门店的库存单
        if ($order['shop_id'] != $shop_id) {
            return json(['code' => 1, 'msg' => '您无权删除其他门店的库存单']);
        }

        // 删除库存单
        $result = $model->remove($id);
        if ($result) {
            return json(['code' => 0, 'msg' => '删除成功']);
        } else {
            return json(['code' => 1, 'msg' => '只能删除草稿状态的库存单']);
        }
    }

    /**
     * 提交库存单
     */
    public function submit()
    {
        // 权限校验
        check_auth(request()->controller() . '/index', 1);
        
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');

        // 获取库存单
        $model = new InventoryOrder();
        $order = $model->where('id', $id)->where('status', 0)->find();
        if (!$order) {
            return json(['code' => 1, 'msg' => '库存单不存在或状态不是草稿']);
        }
        
        // 检查是否为当前门店的库存单
        if ($order['shop_id'] != $shop_id) {
            return json(['code' => 1, 'msg' => '您无权操作其他门店的库存单']);
        }

        // 获取明细 - 添加clientkeynum过滤确保数据隔离
        $clientkeynum = session('cn_accountinfo.clientkeynum');
        $detailWhere = [['order_id', '=', $id]];
        if (!empty($clientkeynum)) {
            $detailWhere[] = ['clientkeynum', '=', $clientkeynum];
        }
        $details = Db::name('inventory_order_detail')->where($detailWhere)->select();
        if (empty($details)) {
            return json(['code' => 1, 'msg' => '库存单没有明细，不能提交']);
        }

        // 如果是出库单或销售出库单，需要验证库存是否足够
        if (in_array($order['order_type'], [2, 3])) {
            // 检查商品规格是否有重复并验证库存
            $productIds = [];
            foreach ($details as $detail) {
                // 检查重复商品规格
                $inventoryKey = $detail['product_id'] . '_' . $detail['inventory_id'];
                if (in_array($inventoryKey, $productIds)) {
                    return json(['code' => 1, 'msg' => '不允许添加重复的商品规格，请编辑订单合并数量']);
                }
                $productIds[] = $inventoryKey;
                
                // 验证库存是否足够
                $stockValidation = $this->validateStockForOutbound($detail['product_id'], $detail['inventory_id'], $detail['quantity'], $order['shop_id']);
                if (!$stockValidation['success']) {
                    return json(['code' => 1, 'msg' => $stockValidation['message']]);
                }
            }
        } else {
            // 入库单也要检查是否有重复商品规格
            $productIds = [];
            foreach ($details as $detail) {
                // 检查重复商品规格
                $inventoryKey = $detail['product_id'] . '_' . $detail['inventory_id'];
                if (in_array($inventoryKey, $productIds)) {
                    return json(['code' => 1, 'msg' => '不允许添加重复的商品规格，请编辑订单合并数量']);
                }
                $productIds[] = $inventoryKey;
            }
        }

        // 更新状态为已提交
        $result = $model->complete($id, session('cn_accountinfo.accountname'));
        if ($result) {
            return json(['code' => 0, 'msg' => '提交成功']);
        } else {
            return json(['code' => 1, 'msg' => '提交失败']);
        }
    }

    /**
     * 取消库存单
     */
    public function cancel()
    {
        // 权限校验
        check_auth(request()->controller() . '/cancel', 1);
        
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');

        // 获取库存单
        $model = new InventoryOrder();
        $order = $model->where('id', $id)->whereIn('status', [0, 1])->find();
        if (!$order) {
            return json(['code' => 1, 'msg' => '库存单不存在或状态不允许取消']);
        }
        
        // 检查是否为当前门店的库存单
        if ($order['shop_id'] != $shop_id) {
            return json(['code' => 1, 'msg' => '您无权操作其他门店的库存单']);
        }

        // 更新状态为已取消
        $result = $model->updateStatus($id, -1);
        if ($result) {
            return json(['code' => 0, 'msg' => '取消成功']);
        } else {
            return json(['code' => 1, 'msg' => '取消失败']);
        }
    }

    /**
     * 库存日志列表
     */
    public function log()
    {
        // 权限校验
        check_auth(request()->controller() . '/log', 0);
        
        if (request()->isAjax()) {
            // 获取请求参数
            $page = input('page', 1, 'intval');
            $limit = input('limit', 15, 'intval');
            $product_id = input('product_id', 0, 'intval');
            $inventory_id = input('inventory_id', 0, 'intval');
            $change_type = input('change_type', 0, 'intval');
            $date_range = input('date_range', '');

            // 获取当前门店ID
            $basekeynum = session('cn_accountinfo.keynum');
            $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');

            // 构建查询条件
            $where = [['l.shop_id', '=', $shop_id]]; // 只查询当前门店的日志
            
            if ($product_id > 0) {
                $where[] = ['l.product_id', '=', $product_id];
            }
            if ($inventory_id > 0) {
                $where[] = ['l.inventory_id', '=', $inventory_id];
            }
            if ($change_type > 0) {
                $where[] = ['l.change_type', '=', $change_type];
            }
            if (!empty($date_range)) {
                $dates = explode(' - ', $date_range);
                if (count($dates) == 2) {
                    $where[] = ['l.created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
                }
            }

            // 获取列表数据
            $count = Db::name('inventory_log')->alias('l')
                ->join('products p', 'p.id = l.product_id', 'LEFT')
                ->join('product_inventory pi', 'pi.id = l.inventory_id', 'LEFT')
                ->where($where)
                ->count();

            $list = Db::name('inventory_log')->alias('l')
                ->join('products p', 'p.id = l.product_id', 'LEFT')
                ->join('product_inventory pi', 'pi.id = l.inventory_id', 'LEFT')
                ->where($where)
                ->field('l.*, p.title as product_name, pi.title as inventory_name')
                ->order('l.id', 'desc')
                ->page($page, $limit)
                ->select();

            // 获取门店信息
            $shops = Db::name('shop')->column('title', 'id');

            // 处理数据
            foreach ($list as &$item) {
                // 处理门店名称
                $item['shop_name'] = $item['shop_id'] > 0 ? ($shops[$item['shop_id']] ?? '未知门店') : '平台';
                
                // 处理变动类型文本
                $item['change_type_text'] = $this->getChangeTypeText($item['change_type']);
                
                // 处理变动数量
                $item['quantity_before'] = $item['before_quantity'];
                $item['quantity_change'] = $item['change_quantity'];
                $item['quantity_after'] = $item['after_quantity'];
                
                // 处理操作人
                $item['created_by'] = $item['operator'];
            }

            // 计算库存统计数据
            $totalInStock = Db::name('inventory_log')->alias('l')
                ->where($where)
                ->whereIn('l.change_type', [1, 3, 6, 9]) // 入库、调拨入、退货、其他入库
                ->sum('l.change_quantity');

            $totalOutStock = Db::name('inventory_log')->alias('l')
                ->where($where)
                ->whereIn('l.change_type', [2, 4, 5, 10]) // 出库、调拨出、销售、其他出库
                ->sum('l.change_quantity');

            // 取绝对值，因为出库的change_quantity是负数
            $totalOutStock = abs($totalOutStock);
            
            // 计算普通商品和计量商品的分类统计
            $normalInStock = Db::name('inventory_log')->alias('l')
                ->join('products p', 'p.id = l.product_id')
                ->where($where)
                ->where('p.product_type', 'in', [1, 3]) // 普通商品
                ->whereIn('l.change_type', [1, 3, 6, 9]) // 入库类型
                ->sum('l.change_quantity');
                
            $normalOutStock = Db::name('inventory_log')->alias('l')
                ->join('products p', 'p.id = l.product_id')
                ->where($where)
                ->where('p.product_type', 'in', [1, 3]) // 普通商品
                ->whereIn('l.change_type', [2, 4, 5, 10]) // 出库类型
                ->sum('l.change_quantity');
            
            // 取绝对值
            $normalOutStock = abs($normalOutStock);
                
            $weightInStock = Db::name('inventory_log')->alias('l')
                ->join('products p', 'p.id = l.product_id')
                ->where($where)
                ->where('p.product_type', '=', 2) // 计量商品
                ->whereIn('l.change_type', [1, 3, 6, 9]) // 入库类型
                ->sum('l.change_quantity');
                
            $weightOutStock = Db::name('inventory_log')->alias('l')
                ->join('products p', 'p.id = l.product_id')
                ->where($where)
                ->where('p.product_type', '=', 2) // 计量商品
                ->whereIn('l.change_type', [2, 4, 5, 10]) // 出库类型
                ->sum('l.change_quantity');
                
            // 取绝对值
            $weightOutStock = abs($weightOutStock);
            
            // 计算差值
            $difference = round($totalInStock - $totalOutStock, 2);
            $normalDifference = round($normalInStock - $normalOutStock, 2);
            $weightDifference = round($weightInStock - $weightOutStock, 2);

            $stats = [
                'total_in_stock' => $totalInStock,
                'total_out_stock' => $totalOutStock,
                'difference' => $difference,
                'normal_in_stock' => $normalInStock,
                'normal_out_stock' => $normalOutStock,
                'weight_in_stock' => $weightInStock,
                'weight_out_stock' => $weightOutStock,
                'normal_difference' => $normalDifference,
                'weight_difference' => $weightDifference
            ];

            return json(['code' => 0, 'msg' => 'success', 'count' => $count, 'data' => $list, 'stats' => $stats]);
        }

        return $this->fetch();
    }



    /**
     * 获取库存变动类型文本
     * @param int $change_type 变动类型
     * @return string
     */
    private function getChangeTypeText($change_type)
    {
        $types = [
            1 => '入库',
            2 => '出库',
            3 => '调拨入',
            4 => '调拨出',
            5 => '销售',
            6 => '退货',
            7 => '盘点',
            8 => '其他',
            9 => '其他入库',
            10 => '其他出库'
        ];

        return isset($types[$change_type]) ? $types[$change_type] : '未知类型';
    }

    /**
     * 商品库存列表
     */
    public function stock()
    {
        // 权限校验
        check_auth(request()->controller() . '/stock', 0);
        
        if (request()->isAjax()) {
            // 获取请求参数
            $page = input('page', 1, 'intval');
            $limit = input('limit', 15, 'intval');
            $product_name = input('product_name', '');
            $product_id = input('product_id', 0, 'intval'); // 新增：商品ID筛选
            $inventory_id = input('inventory_id', 0, 'intval'); // 新增：规格ID筛选
            $stock_status = input('stock_status', ''); // 库存状态：1-有库存，2-无库存

            // 获取当前门店ID
            $basekeynum = session('cn_accountinfo.keynum');
            $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');

            // 构建查询条件
            $where[] = ['spi.shop_id', '=', $shop_id]; // 只查询当前门店的库存
            
            if (!empty($product_name)) {
                $where[] = ['p.title', 'like', "%{$product_name}%"];
            }
            
            // 新增：根据商品ID筛选
            if ($product_id > 0) {
                $where[] = ['spi.product_id', '=', $product_id];
            }
            
            // 新增：根据规格ID筛选
            if ($inventory_id > 0) {
                $where[] = ['spi.inventory_id', '=', $inventory_id];
            }
            
            // 库存状态筛选的条件，先不加入where数组，后面单独处理
            $stock_condition = '';
            if ($stock_status == 1) {
                // 有库存：普通商品stock>0 或 计量商品weight_stock>0
                $stock_condition = '((p.product_type = 1 AND spi.stock > 0) OR (p.product_type = 2 AND spi.weight_stock > 0))';
            } elseif ($stock_status == 2) {
                // 无库存：普通商品stock<=0 或 计量商品weight_stock<=0
                $stock_condition = '((p.product_type = 1 AND spi.stock <= 0) OR (p.product_type = 2 AND spi.weight_stock <= 0))';
            }

            // 构建查询对象
            $countQuery = Db::name('shop_product_inventory')->alias('spi')
                ->join('products p', 'p.id = spi.product_id')
                ->join('product_inventory pi', 'pi.id = spi.inventory_id', 'LEFT')
                ->where($where);

            $listQuery = Db::name('shop_product_inventory')->alias('spi')
                ->join('products p', 'p.id = spi.product_id')
                ->join('product_inventory pi', 'pi.id = spi.inventory_id', 'LEFT')
                ->where($where);

            // 如果有库存状态筛选条件，使用whereRaw添加
            if (!empty($stock_condition)) {
                $countQuery->whereRaw($stock_condition);
                $listQuery->whereRaw($stock_condition);
            }

            // 获取列表数据
            $count = $countQuery->count();

            $list = $listQuery->field('spi.*, p.title as product_name, p.cover as product_thumb, p.product_type, 
                        pi.title as inventory_name, COALESCE(pi.weight_unit, "kg") as weight_unit, pi.sn as inventory_sn')
                ->order('spi.id', 'desc')
                ->page($page, $limit)
                ->select();

            // 获取门店信息
            $shops = Db::name('shop')->column('title', 'id');

            // 处理门店名称和库存信息
            foreach ($list as &$item) {
                $item['title'] = $item['shop_id'] > 0 ? ($shops[$item['shop_id']] ?? '未知门店') : '平台';
                
                // 如果没有规格名称，显示默认规格
                if (empty($item['inventory_name'])) {
                    $item['inventory_name'] = '默认规格';
                }
                
                // 根据商品类型处理库存显示
                $item['product_type'] = $item['product_type'] ?? 1;
                $item['product_type_text'] = $item['product_type'] == 2 ? '计量商品' : '普通商品';
                
                if ($item['product_type'] == 2) {
                    // 计量商品：显示重量库存
                    $item['current_stock'] = $item['weight_stock'] ?? 0;
                    $item['stock_unit'] = $item['weight_unit'] ?? 'kg';
                    $item['stock_display'] = $item['current_stock'] . $item['stock_unit'];
                    $item['warning_stock_display'] = ($item['warning_stock'] ?? 0) . $item['stock_unit'];
                } else {
                    // 普通商品：显示数量库存
                    $item['current_stock'] = $item['stock'] ?? 0;
                    $item['stock_unit'] = 'pcs';
                    $item['stock_display'] = $item['current_stock'] . '件';
                    $item['warning_stock_display'] = ($item['warning_stock'] ?? 0) . '件';
                }
            }

            return json(['code' => 0, 'msg' => 'success', 'count' => $count, 'data' => $list]);
        }

        // 获取门店库存统计数据
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');
        
        // 统计数据
        $stats = Db::name('shop_product_inventory')->alias('spi')
            ->join('products p', 'p.id = spi.product_id')
            ->where([['spi.shop_id', '=', $shop_id]])
            ->field([
                'count(distinct spi.product_id) as total_products',
                'sum(case when p.product_type IN (1, 3) then spi.stock else 0 end) as total_normal_stock',
                'sum(case when p.product_type = 2 then spi.weight_stock else 0 end) as total_weight_stock',
                'count(distinct case when p.product_type IN (1, 3) then spi.product_id end) as normal_product_count',
                'count(distinct case when p.product_type = 2 then spi.product_id end) as weight_product_count'
            ])
            ->find();
            
        // 格式化统计数据
        $total_normal_stock = round($stats['total_normal_stock'] ?? 0, 2);
        $total_weight_stock = round($stats['total_weight_stock'] ?? 0, 2);
        
        // 验证计量商品总量，如果不存在计量商品则设置为0
        if ($stats['weight_product_count'] == 0) {
            $total_weight_stock = 0;
        }
        
        $stockStats = [
            'total_products' => $stats['total_products'] ?? 0,
            'total_normal_stock' => $total_normal_stock,
            'total_weight_stock' => $total_weight_stock,
            'normal_product_count' => $stats['normal_product_count'] ?? 0,
            'weight_product_count' => $stats['weight_product_count'] ?? 0,
            'total_stock_value' => $total_normal_stock + $total_weight_stock
        ];
        
        $this->assign('stockStats', $stockStats);

        return $this->fetch();
    }

    /**
     * 获取商品列表
     */
    public function getProducts()
    {
        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');
        
        // 查询商品数据
        $products = Db::name('products')
            ->field('id, title as product_name')
            // ->where('status', 1)
            ->order('id desc')
            ->select();
        
        return json(['code' => 0, 'msg' => 'success', 'data' => $products]);
    }
    
    /**
     * 获取商品规格列表
     */
    public function getInventories()
    {
        $product_id = input('product_id', 0, 'intval');
        if ($product_id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 查询规格数据
        $inventories = Db::name('product_inventory')
            ->field('id, title, price')
            ->where('product_id', $product_id)
            ->order('id asc')
            ->select();
        
        // 如果没有规格数据，添加一个默认规格
        if (empty($inventories)) {
            // 获取商品价格
            $product = Db::name('products')->field('price')->where([['id', '=', $product_id]])->find();
            $inventories = [
                [
                    'id' => 0,
                    'title' => '默认规格',
                    'price' => $product ? $product['price'] : 0.00
                ]
            ];
        }
        
        return json(['code' => 0, 'msg' => 'success', 'data' => $inventories]);
    }

    /**
     * 获取商品列表，支持按门店查询库存
     * 用于库存单据选择商品时的接口
     * 只返回在shop_product和shop_product_inventory中都存在的商品
     * @param Request $request
     * @return \think\response\Json
     */
    public function getList(Request $request)
    {
        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.parent_basekeynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');
        
        // 获取参数
        $keyword = $request->param('keyword', '');
        $page = $request->param('page', 1);
        $limit = $request->param('limit', 20);
        $has_stock = $request->param('has_stock', 0); // 是否只查询有库存的商品
        
        // 构建查询条件 - 必须联合shop_product和shop_product_inventory
        $where = [
            ['p.clientkeynum', '=', $clientkeynum],
            ['p.state', '=', 1], // 只查询上架的商品
            ['sp.shop_id', '=', $shop_id], // 门店商品表必须存在
            ['sp.status', '=', 1], // 门店商品必须上架
            ['spi.shop_id', '=', $shop_id], // 门店库存表必须存在
        ];
        
        // 关键词搜索
        // if (!empty($keyword)) {
        //     $where[] = ['p.title', 'like', "%{$keyword}%"];
        // }
        
        // 联合查询 - 只返回在shop_product和shop_product_inventory中都存在的商品
        $query = Db::name('products')->alias('p')
            ->join('shop_product sp', 'sp.product_id = p.id')
            ->join('shop_product_inventory spi', 'spi.product_id = p.id AND spi.shop_id = sp.shop_id')
            ->join('product_inventory pi', 'pi.id = spi.inventory_id', 'LEFT')
            ->field('p.id, p.title, p.price, p.cover, p.product_type, 
                     spi.inventory_id, spi.stock, spi.weight_stock, spi.stock_unit,
                     COALESCE(pi.title, "默认规格") as spec_title, 
                     COALESCE(pi.price, p.price) as spec_price,
                     COALESCE(pi.weight_unit, "kg") as weight_unit')
            ->group('p.id, spi.inventory_id'); // 按商品ID和规格ID分组
            


        if(!empty($keyword)){
            $query->where(function($query) use ($keyword){
                $query->where('p.title', 'like', "%{$keyword}%")
                ->whereOr('pi.sn', '=', "{$keyword}");
            });
        }

        $query->where($where);

        // 如果只查询有库存的商品，使用whereRaw处理复杂条件
        // if ($has_stock) {
        //     // 根据商品类型查询不同的库存
        //     $query->whereRaw('((p.product_type = 1 AND spi.stock > 0) OR (p.product_type = 2 AND spi.weight_stock > 0))');
        // }
            
        // 获取商品总数（按商品规格组合计算）
        $count = $query->count();
        
        // 获取分页数据
        $list = $query->order('p.id DESC, spi.inventory_id ASC')
            ->page($page, $limit)
            ->select();

        $list = Db::query($list);
        
        
        // 处理结果数据
        $result = [];
        foreach ($list as $item) {
            $is_weight_product = ($item['product_type'] ?? 1) == 2;
            $stock_quantity = 0;
            $stock_unit = 'pcs';
            $product_type_text = '普通商品';
            
            // 根据商品类型返回不同的库存信息
            if ($is_weight_product) {
                $stock_quantity = $item['weight_stock'] ?? 0;
                $stock_unit = $item['weight_unit'] ?? 'kg';
                $product_type_text = '计量商品';
            } else {
                $stock_quantity = $item['stock'] ?? 0;
                $stock_unit = 'pcs';
                $product_type_text = '普通商品';
            }
            
            $result[] = [
                'id' => $item['id'],
                'name' => $item['title'],
                'price' => $item['spec_price'],
                'spec_info' => $item['spec_title'],
                'stock_quantity' => $stock_quantity,
                'stock_unit' => $stock_unit,
                'inventory_id' => $item['inventory_id'],
                'cover' => $item['cover'],
                'product_type' => $item['product_type'] ?? 1,
                'product_type_text' => $product_type_text,
                'is_weight_product' => $is_weight_product,
                'weight_unit' => $item['weight_unit'] ?? 'kg'
            ];
        }
        
        return json([
            'code' => 0, 
            'msg' => 'success', 
            'count' => $count, 
            'data' => $result
        ]);
    }

    /**
     * 验证出库库存是否足够
     * @param int $product_id 商品ID
     * @param int $inventory_id 规格ID
     * @param float $quantity 出库数量
     * @param int $shop_id 门店ID
     * @return array ['success' => bool, 'message' => string]
     */
    private function validateStockForOutbound($product_id, $inventory_id, $quantity, $shop_id)
    {
        // 查询商品类型
        $product = Db::name('products')->where([['id', '=', $product_id]])->field('title, product_type')->find();
        if (!$product) {
            return ['success' => false, 'message' => '商品不存在'];
        }
        
        // 查询库存信息
        $inventory = Db::name('shop_product_inventory')->where([
            ['product_id', '=', $product_id],
            ['inventory_id', '=', $inventory_id],
            ['shop_id', '=', $shop_id]
        ])->find();
        
        if (!$inventory) {
            return ['success' => false, 'message' => '库存记录不存在'];
        }
        
        // 根据商品类型判断库存是否足够
        if ($product['product_type'] == 2) {
            // 计量商品
            if ($inventory['weight_stock'] < $quantity) {
                return ['success' => false, 'message' => $product['title'] . ' 库存不足，当前库存：' . $inventory['weight_stock'] . 'kg'];
            }
        } else {
            // 普通商品
            if ($inventory['stock'] < $quantity) {
                return ['success' => false, 'message' => $product['title'] . ' 库存不足，当前库存：' . $inventory['stock'] . '件'];
            }
        }
        
        return ['success' => true, 'message' => '库存充足'];
    }
    
    /**
     * 搜索商品
     * 用于自动完成搜索商品
     */
    public function searchProduct()
    {
        $keyword = input('keyword', '');
        if (empty($keyword)) {
            return json(['code' => 0, 'msg' => 'success', 'data' => []]);
        }

        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');

        // 搜索商品 - 只搜索当前门店有库存的商品
        $products = Db::name('products')->alias('p')
            ->join('shop_product_inventory spi', 'p.id = spi.product_id')
            ->where('spi.shop_id', $shop_id)
            ->where(function($query) use ($keyword) {
                $query->where('p.title', 'like', "%{$keyword}%")
                      ->whereOr('p.id', 'like', "%{$keyword}%");
            })
            ->field('p.id, p.title as name')
            ->group('p.id')
            ->limit(20)
            ->select();

        return json(['code' => 0, 'msg' => 'success', 'data' => $products]);
    }

    /**
     * 根据商品ID获取规格列表
     */
    public function getInventoryByProduct()
    {
        $product_id = input('product_id', 0, 'intval');
        if ($product_id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');

        // 获取商品规格 - 只获取当前门店有库存的规格
        $inventories = Db::name('product_inventory')->alias('pi')
            ->join('shop_product_inventory spi', 'pi.id = spi.inventory_id AND spi.product_id = pi.product_id')
            ->where('pi.product_id', $product_id)
            ->where('spi.shop_id', $shop_id)
            ->field('pi.id, pi.title as name')
            ->select();

        // 如果没有规格，返回默认规格
        if (empty($inventories)) {
            $inventories = [
                [
                    'id' => 0,
                    'name' => '默认规格'
                ]
            ];
        }

        return json(['code' => 0, 'msg' => 'success', 'data' => $inventories]);
    }

    /**
     * 获取门店库存统计数据
     * 参考InventoryController的getStockStats方法，只统计当前门店数据
     */
    public function getStockStats()
    {
        // 权限校验
        check_auth(request()->controller() . '/stock', 1);
        
        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');
        
        // 获取筛选参数
        $product_type = input('product_type', '');
        $product_id = input('product_id', 0, 'intval');
        $inventory_id = input('inventory_id', 0, 'intval');
        $stock_status = input('stock_status', '');
        
        // 构建查询条件
        $where = [
            ['spi.shop_id', '=', $shop_id] // 只统计当前门店的数据
        ];
        
        if ($product_type !== '') {
            $where[] = ['p.product_type', '=', $product_type];
        }
        if ($product_id > 0) {
            $where[] = ['spi.product_id', '=', $product_id];
        }
        if ($inventory_id > 0) {
            $where[] = ['spi.inventory_id', '=', $inventory_id];
        }
        
        // 构建基础查询对象
        $query = Db::name('shop_product_inventory')->alias('spi')
            ->join('products p', 'p.id = spi.product_id')
            ->where($where);
            
        // 处理库存状态筛选
        if ($stock_status == 1) {
            // 有库存：普通商品stock>0 或 计量商品weight_stock>0
            $query->whereRaw('((p.product_type = 1 AND spi.stock > 0) OR (p.product_type = 2 AND spi.weight_stock > 0) OR (p.product_type = 3 AND spi.stock > 0))');
        } elseif ($stock_status == 2) {
            // 无库存：普通商品stock<=0 或 计量商品weight_stock<=0
            $query->whereRaw('((p.product_type = 1 AND spi.stock <= 0) OR (p.product_type = 2 AND spi.weight_stock <= 0) OR (p.product_type = 3 AND spi.stock <= 0))');
        }
        
        // 统计数据 - 使用distinct方法准确统计商品数量
        $stats = $query->field([
            'count(distinct spi.product_id) as total_products',
            'sum(case when p.product_type IN (1, 3) then spi.stock else 0 end) as total_normal_stock',
            'sum(case when p.product_type = 2 then spi.weight_stock else 0 end) as total_weight_stock',
            'count(distinct case when p.product_type IN (1, 3) then spi.product_id end) as normal_product_count',
            'count(distinct case when p.product_type = 2 then spi.product_id end) as weight_product_count'
        ])->find();
        
        // 格式化统计数据
        $total_normal_stock = round($stats['total_normal_stock'] ?? 0, 2);
        $total_weight_stock = round($stats['total_weight_stock'] ?? 0, 2);
        
        // 验证计量商品总量，如果不存在计量商品则设置为0
        if ($stats['weight_product_count'] == 0) {
            $total_weight_stock = 0;
        }
        
        $stockStats = [
            'total_products' => $stats['total_products'] ?? 0,
            'total_normal_stock' => $total_normal_stock,
            'total_weight_stock' => $total_weight_stock,
            'normal_product_count' => $stats['normal_product_count'] ?? 0,
            'weight_product_count' => $stats['weight_product_count'] ?? 0,
            'total_stock_value' => $total_normal_stock + $total_weight_stock // 添加总库存值
        ];
        
        return json(['code' => 0, 'msg' => 'success', 'data' => $stockStats]);
    }

    /**
     * 导出门店库存数据
     * 参考InventoryController的导出功能，只导出当前门店数据
     */
    public function export_stock()
    {
        // 权限校验
        check_auth(request()->controller() . '/stock', 0);
        
        // 获取当前门店ID和门店名称
        $basekeynum = session('cn_accountinfo.keynum');
        $shop = Db::name('shop')->where([['keynum', '=', $basekeynum]])->field('id, title')->find();
        if (!$shop) {
            return json(['code' => -1, 'msg' => '未找到当前门店信息']);
        }
        $shop_id = $shop['id'];
        $shop_name = $shop['title'];
        
        // 获取筛选参数
        $product_id = input('product_id', 0, 'intval');
        $inventory_id = input('inventory_id', 0, 'intval');
        $stock_status = input('stock_status', '');
        
        // 构建查询条件
        $where = [
            ['spi.shop_id', '=', $shop_id] // 只查询当前门店的库存
        ];
        
        if ($product_id > 0) {
            $where[] = ['spi.product_id', '=', $product_id];
        }
        if ($inventory_id > 0) {
            $where[] = ['spi.inventory_id', '=', $inventory_id];
        }
        
        // 构建查询对象
        $query = Db::name('shop_product_inventory')->alias('spi')
            ->join('products p', 'p.id = spi.product_id')
            ->join('product_inventory pi', 'pi.id = spi.inventory_id', 'LEFT')
            ->where($where);

        // 库存状态筛选
        if ($stock_status == 1) {
            // 有库存：普通商品stock>0 或 计量商品weight_stock>0
            $query->whereRaw('((p.product_type = 1 AND spi.stock > 0) OR (p.product_type = 2 AND spi.weight_stock > 0) OR (p.product_type = 3 AND spi.stock > 0))');
        } elseif ($stock_status == 2) {
            // 无库存：普通商品stock<=0 或 计量商品weight_stock<=0
            $query->whereRaw('((p.product_type = 1 AND spi.stock <= 0) OR (p.product_type = 2 AND spi.weight_stock <= 0) OR (p.product_type = 3 AND spi.stock <= 0))');
        }

        // 获取库存数据
        $list = $query->field('spi.*, p.title as product_name, p.product_type, 
                        pi.title as inventory_name, COALESCE(pi.weight_unit, "kg") as weight_unit, pi.sn as inventory_sn')
            ->order('spi.id', 'desc')
            ->select();

        if (empty($list)) {
            return json(['code' => -1, 'msg' => '没有可导出的库存数据']);
        }

        // 准备导出数据
        $export_data = [];
        
        foreach ($list as $item) {
            // 处理商品类型
            $product_type_text = '普通商品';
            if ($item['product_type'] == 2) {
                $product_type_text = '计量商品';
            } elseif ($item['product_type'] == 3) {
                $product_type_text = '赠品';
            }
            
            // 处理库存显示
            if ($item['product_type'] == 2) {
                // 计量商品：显示重量库存
                $stock_display = ($item['weight_stock'] ?? 0) . ($item['weight_unit'] ?? 'kg');
                $current_stock = $item['weight_stock'] ?? 0;
            } else {
                // 普通商品：显示数量库存
                $stock_display = ($item['stock'] ?? 0) . '件';
                $current_stock = $item['stock'] ?? 0;
            }
            
            // 库存状态
            $stock_status = $current_stock > 0 ? '有库存' : '无库存';
            
            // 构建导出数据行
            $export_row = [
                'ID' => $item['id'],
                '门店名称' => $shop_name,
                '商品名称' => $item['product_name'] ?: '',
                '商品规格' => $item['inventory_name'] ?: '默认规格',
                '商品条码' => $item['inventory_sn'] ?: '',
                '商品类型' => $product_type_text,
                '当前库存' => $stock_display,
                '库存状态' => $stock_status,
                '预警库存' => $item['warning_stock'] ?? 0,
                '创建时间' => $item['add_time'] ?: '',
                '更新时间' => $item['update_time'] ?: ''
            ];
            
            $export_data[] = $export_row;
        }
        
        if (empty($export_data)) {
            return json(['code' => -1, 'msg' => '没有可导出的库存数据']);
        }
        
        // 生成文件名
        $filename = $shop_name . '_库存列表_' . date('YmdHis') . '.xlsx';
        $filepath = \think\facade\Env::get('runtime_path') . 'temp/' . $filename;
        
        // 确保目录存在
        if (!is_dir(\think\facade\Env::get('runtime_path') . 'temp/')) {
            mkdir(\think\facade\Env::get('runtime_path') . 'temp/', 0777, true);
        }
        
        // 使用SimpleExcel导出数据
        try {
            $writer = \Spatie\SimpleExcel\SimpleExcelWriter::create($filepath);
            $writer->addRows($export_data);
            
            // 下载文件
            return download($filepath, $filename);
        } catch (\Exception $e) {
            return json(['code' => -1, 'msg' => '导出失败：' . $e->getMessage()]);
        }
    }

    /**
     * 出入库子单查询页面
     */
    public function detailList()
    {
        // 权限校验
        check_auth(request()->controller() . '/detailList', 0);
        return $this->fetch('detail_list');
    }

    /**
     * 获取出入库子单详情列表
     */
    public function getDetailList()
    {
        // 权限校验
        check_auth(request()->controller() . '/detailList', 1);
        
        // 获取请求参数
        $page = input('page', 1, 'intval');
        $limit = input('limit', 15, 'intval');
        $order_no = input('order_no', '');
        $order_type = input('order_type', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $product_id = input('product_id', '');
        $inventory_id = input('inventory_id', '');
        $is_manual = input('is_manual', '');
        $date_range = input('date_range', '');
        
        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');
        
        // 构建查询条件
        $where = [
            ['o.shop_id', '=', $shop_id] // 只查询当前门店的单据
        ];
        
        if (!empty($order_no)) {
            $where[] = ['o.order_no', 'like', "%{$order_no}%"];
        }
        if ($order_type !== '') {
            $where[] = ['o.order_type', '=', $order_type];
        }
        if (!empty($business_type)) {
            $where[] = ['o.business_type', '=', $business_type];
        }
        if ($status !== '') {
            $where[] = ['o.status', '=', $status];
        }
        if (!empty($product_id)) {
            $where[] = ['d.product_id', '=', $product_id];
        }
        if (!empty($inventory_id)) {
            $where[] = ['d.inventory_id', '=', $inventory_id];
        }
        if ($is_manual !== '') {
            $where[] = ['o.is_manual', '=', $is_manual];
        }
        if (!empty($date_range)) {
            $dates = explode(' - ', $date_range);
            if (count($dates) == 2) {
                $where[] = ['o.created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
            }
        }

        // 查询详情数据
        $query = Db::table('inventory_order_detail')->alias('d')
            ->join('inventory_order o', 'o.id = d.order_id')
            ->join('products p', 'p.id = d.product_id')
            ->leftJoin('product_inventory i', 'i.id = d.inventory_id')
            ->where($where)
            ->field([
                'd.id',
                'o.id as order_id',
                'o.order_no',
                'o.order_type',
                'o.business_type', 
                'o.status',
                'o.supplier_name',
                'o.created_by',
                'o.created_at',
                'd.product_id',
                'd.inventory_id',
                'd.quantity',
                'd.price',
                'd.amount',
                'd.clientkeynum',
                'p.title as product_name',
                'p.product_type',
                'i.title as spec_info',
                'i.weight_unit'
            ]);

        // 获取总数
        $count = $query->count();

        // 获取分页数据
        $list = $query->order('o.created_at desc, d.id desc')
            ->page($page, $limit)
            ->select();

        // 统计数据
        $stats = $this->calculateDetailStats($where);

        // 处理数据
        foreach ($list as &$item) {
            // 格式化单据类型
            $item['order_type_text'] = $this->getOrderTypeText($item['order_type']);

            // 格式化业务类型
            $item['business_type_text'] = $this->getBusinessTypeText($item['order_type'], $item['business_type']);

            // 格式化状态
            $item['status_text'] = $this->getStatusText($item['status']);

            // 格式化商品类型
            $item['product_type_text'] = $this->getProductTypeText($item['product_type']);

            // 格式化数量显示
            if ($item['product_type'] == 2) { // 计量商品
                $item['quantity_display'] = $item['quantity'] . ($item['weight_unit'] ?: 'kg');
            } else {
                $item['quantity_display'] = $item['quantity'] . 'pcs';
            }
        }

        return json(['code' => 0, 'msg' => 'success', 'count' => $count, 'data' => $list, 'stats' => $stats]);
    }

    /**
     * 计算详情统计数据
     */
    private function calculateDetailStats($where)
    {

        // 初始化统计结果
        $stats = [
            'total_in_stock' => 0,      // 总入库数量
            'normal_in_stock' => 0,     // 普通商品入库数量
            'weight_in_stock' => 0,     // 计量商品入库数量
            'total_out_stock' => 0,     // 总出库数量
            'normal_out_stock' => 0,    // 普通商品出库数量
            'weight_out_stock' => 0,    // 计量商品出库数量
            'difference' => 0,          // 总差值
            'normal_difference' => 0,   // 普通商品差值
            'weight_difference' => 0    // 计量商品差值
        ];

         // 计算总入库和总出库数量（基于当前筛选条件）
        $totalInStock = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id', 'LEFT')
            ->join('inventory_order o', 'o.order_no = iod.order_no')
            ->where($where)
            ->where('o.order_type', '=', 1)
            ->sum('iod.quantity');

        $totalOutStock = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id', 'LEFT')
            ->join('inventory_order o', 'o.order_no = iod.order_no')
            ->where($where)
            ->whereIn('o.order_type', [2, 3])
            ->sum('iod.quantity');
    
        // 计算差值（入库 - 出库）
        $stats['difference'] = round($totalInStock - $totalOutStock, 2);
    
     // 区分普通商品和计量商品的统计
        $normalInStock = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id')
            ->join('inventory_order o', 'o.order_no = iod.order_no')
            ->where($where)
            ->where('o.order_type', '=', 1)
            ->where('p.product_type', 'in', [1, 3]) // 普通商品
            ->sum('iod.quantity');
        
        $normalOutStock = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id')
            ->join('inventory_order o', 'o.order_no = iod.order_no')
            ->where($where)
            ->whereIn('o.order_type', [2, 3])
            ->where('p.product_type', 'in', [1, 3]) // 普通商品
            ->sum('iod.quantity');
            
        $weightInStock = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id')
            ->join('inventory_order o', 'o.order_no = iod.order_no')
            ->where($where)
            ->where('o.order_type', '=', 1)
            ->where('p.product_type', '=', 2) // 计量商品
            ->sum('iod.quantity');
            
        $weightOutStock = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id')
            ->join('inventory_order o', 'o.order_no = iod.order_no')
            ->where($where)
            ->whereIn('o.order_type', [2, 3])
            ->where('p.product_type', '=', 2) // 计量商品
            ->sum('iod.quantity');
    

        // 计算差值
        $stats['normal_difference'] = round($normalInStock - $normalOutStock, 2);
        $stats['weight_difference'] = round($weightInStock - $weightOutStock, 2);
        $stats['difference'] = round($totalInStock - $totalOutStock, 2);
        $stats['total_in_stock'] = $totalInStock;
        $stats['total_out_stock'] = $totalOutStock;
        $stats['normal_in_stock'] = $normalInStock;
        $stats['normal_out_stock'] = $normalOutStock;
        $stats['weight_in_stock'] = $weightInStock;
        $stats['weight_out_stock'] = $weightOutStock;

        return $stats;
    }

    /**
     * 获取商品类型文本
     */
    private function getProductTypeText($product_type)
    {
        $types = [
            1 => '普通商品',
            2 => '计量商品',
            3 => '赠品'
        ];

        return isset($types[$product_type]) ? $types[$product_type] : '未知类型';
    }

    /**
     * 导出出入库子单详情列表
     */
    public function exportDetailList()
    {
        // 权限校验
        check_auth(request()->controller() . '/detailList', 1);
        
        // 获取请求参数（与getDetailList方法相同的参数）
        $order_no = input('order_no', '');
        $order_type = input('order_type', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $product_id = input('product_id', '');
        $inventory_id = input('inventory_id', '');
        $is_manual = input('is_manual', '');
        $date_range = input('date_range', '');
        
        // 获取当前门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where([['keynum', '=', $basekeynum]])->value('id');
        
        // 构建查询条件
        $where = [
            ['o.shop_id', '=', $shop_id]
        ];
        
        if (!empty($order_no)) {
            $where[] = ['o.order_no', 'like', "%{$order_no}%"];
        }
        if ($order_type !== '') {
            $where[] = ['o.order_type', '=', $order_type];
        }
        if (!empty($business_type)) {
            $where[] = ['o.business_type', '=', $business_type];
        }
        if ($status !== '') {
            $where[] = ['o.status', '=', $status];
        }
        if (!empty($product_id)) {
            $where[] = ['d.product_id', '=', $product_id];
        }
        if (!empty($inventory_id)) {
            $where[] = ['d.inventory_id', '=', $inventory_id];
        }
        if ($is_manual !== '') {
            $where[] = ['o.is_manual', '=', $is_manual];
        }
        if (!empty($date_range)) {
            $dates = explode(' - ', $date_range);
            if (count($dates) == 2) {
                $where[] = ['o.created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
            }
        }

        // 查询所有数据
        $list = Db::table('inventory_order_detail')->alias('d')
            ->join('inventory_order o', 'o.id = d.order_id')
            ->join('products p', 'p.id = d.product_id')
            ->leftJoin('product_inventory i', 'i.id = d.inventory_id')
            ->where($where)
            ->field([
                'o.order_no',
                'o.order_type',
                'o.business_type',
                'o.status',
                'o.supplier_name',
                'o.created_by',
                'o.created_at',
                'd.quantity',
                'd.price',
                'd.amount',
                'd.clientkeynum',
                'p.title as product_name',
                'p.product_type',
                'i.title as spec_info',
                'i.weight_unit'
            ])
            ->order('o.created_at desc, d.id desc')
            ->select();

        // 处理导出数据
        $export_data = [];
        foreach ($list as $item) {
            // 格式化数量显示
            if ($item['product_type'] == 2) { // 计量商品
                $quantity_display = $item['quantity'] . ($item['weight_unit'] ?: 'kg');
            } else {
                $quantity_display = $item['quantity'] . 'pcs';
            }

            $export_data[] = [
                '单据编号' => $item['order_no'],
                '单据类型' => $this->getOrderTypeText($item['order_type']),
                '业务类型' => $this->getBusinessTypeText($item['order_type'], $item['business_type']),
                '商品名称' => $item['product_name'],
                '规格名称' => $item['spec_info'] ?: '-',
                '商品类型' => $this->getProductTypeText($item['product_type']),
                '数量/重量' => $quantity_display,
                '单价' => '￥' . $item['price'],
                '金额' => '￥' . $item['amount'],
                '状态' => $this->getStatusText($item['status']),
                '相关单位' => $item['supplier_name'] ?: '-',
                '创建人' => $item['created_by'],
                '创建时间' => $item['created_at']
            ];
        }

        // 生成文件名和路径
        $filename = '门店出入库子单详情_' . date('Y-m-d_H-i-s') . '.xlsx';
        $filepath = env('root_path') . 'public' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . $filename;

        // 确保目录存在
        $upload_dir = env('root_path') . 'public' . DIRECTORY_SEPARATOR . 'uploads';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        // 使用SimpleExcel导出数据
        try {
            $writer = \Spatie\SimpleExcel\SimpleExcelWriter::create($filepath);
            $writer->addRows($export_data);
            
            // 下载文件
            return download($filepath, $filename);
        } catch (\Exception $e) {
            return json(['code' => -1, 'msg' => '导出失败：' . $e->getMessage()]);
        }
    }
} 