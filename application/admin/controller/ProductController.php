<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/9/20 上午10:17
 */

namespace app\admin\controller;

use app\admin\model\OrderDetail;
use app\admin\model\Product;
use app\admin\model\ProductCategory;
use app\admin\model\ProductInventory;
use app\api\model\ProductSpecAttr;
use app\api\model\ShopProductInventory;
use app\api\model\SkuSpec;
use app\api\model\UserCart;
use think\Db;
use think\Request;

class ProductController extends CnController
{

    /**
     * 初始化方法
     */
    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    public function category_list()
    {
        check_auth(request()->controller() . '/category_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');

        return $this->fetch();
    }

    public function ajax_category_list(Request $request)
    {
        check_auth(request()->controller() . '/category_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');

        $page = $request->param('page', 1);
        $page_size = $request->param('page_size', 10);

        $where = ['clientkeynum' => $basekeynum];

        $list = ProductCategory::withCount('products')->where($where)->order('sort', 'asc')->page($page, $page_size)->select();
        $count = ProductCategory::where($where)->count();

        success(0, '请求成功', $list, $count);

    }

    public function add_category(Request $request)
    {
        check_auth(request()->controller() . '/category_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');

        $id = $request->param('id', 0);
        if(!empty($id)) {

            $info = ProductCategory::where(['clientkeynum' => $basekeynum])->get($id);
            $this->assign('info', $info);
        }

        return $this->fetch();

    }

    public function ajax_add_category(Request $request)
    {
        check_auth(request()->controller() . '/category_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');

        $id = $request->param('id', null);
        $title = $request->param('title');
        $sort = $request->param('sort', 0);
        if (empty($title)) fail(-1, '请输入分类名称');

        if (ProductCategory::checkTitleExist($title, $id)) fail(-1, '当前分类已存在');

        if (empty($id)) {
            $model = new ProductCategory();
            $model->created_at = date('Y-m-d H:i:s');
            $model->updated_at = date('Y-m-d H:i:s');
        } else {
            $model = ProductCategory::where(['clientkeynum' => $basekeynum])->get($id);
            $model->updated_at = date('Y-m-d H:i:s');
        }

        $model->title = $title;
        $model->sort = $sort;
        $model->clientkeynum = $basekeynum;
        if ($model->save()) {
            success();
        } else {
            fail();
        }

    }

    public function del_category(Request $request)
    {
        check_auth(request()->controller() . '/category_list', 1);
        $basekeynum = session('cn_accountinfo.basekeynum');

        $id = $request->param('id');
        if (empty($id)) fail(-1, '参数错误');

        // 查询分类下是否有商品
        $hasProduct = Product::where(['category_id' => $id, 'clientkeynum' => $basekeynum])->find();
        if ($hasProduct) fail(-1, '该分类下有商品，无法删除');

        if (ProductCategory::where(['clientkeynum' => $basekeynum, 'id' => $id])->delete()) {
            success();
        } else {
            fail();
        }
    }

    public function get_product_spec(Request $request)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $request->param('product_id', 0);
        if(empty($id)){
            fail(-1, '参数错误，未查询到当前商品信息');
        }

        $product = Product::where(['clientkeynum' => $basekeynum, 'id' => $id])->find();
        if (empty($product)) {
            fail(-1, '未查询到当前商品信息');
        }
        $skuData = Product::makeSkuTableData($product);

        success(200, '1', $skuData['specData']);

    }

    public function get_product_spec_attr(Request $request)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $request->param('product_id', 0);
        if(empty($id)){
            fail(-1, '参数错误，未查询到当前商品信息');
        }

        $product = Product::where(['clientkeynum' => $basekeynum, 'id' => $id])->find();
        if (empty($product)) {
            fail(-1, '未查询到当前商品信息');
        }
        $skuData = Product::makeSkuTableData($product);

        success(200, '1', $skuData['skuData']);

    }



    public function edit_product(Request $request)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $request->param('id', 0);
        if(empty($id)){
            error_tips("参数错误，未查询到当前商品信息");
            die;
        }

        $product = Product::where(['clientkeynum' => $basekeynum, 'id' => $id])->find();
        if (empty($product)) {
            error_tips("未查询到当前商品信息");
            die;
        }
        $this->assign('product', $product);
        $category_list = ProductCategory::where(['clientkeynum' => $basekeynum])
            ->field('id, title')
            ->order('sort', 'desc')
            ->select();

        // 商品类型选项
        $product_types = [
            Product::PRODUCT_TYPE_NORMAL => '普通商品',
            Product::PRODUCT_TYPE_WEIGHT => '计量商品'
        ];

        $this->assign('category_list', $category_list);
        $this->assign('product_types', $product_types);

        // 获取SKU数据用于编辑页面回显
        $skuData = Product::makeSkuTableData($product);
        $this->assign('spec_data', json_encode($skuData['specData'], JSON_UNESCAPED_UNICODE));
        $this->assign('sku_data', json_encode($skuData['skuData'], JSON_UNESCAPED_UNICODE));

        return $this->fetch();

    }


    public function add_product()
    {
//        check_auth(request()->controller() . '/product_list', 0);
        $basekeynum = session('cn_accountinfo.basekeynum');

        $category_list = ProductCategory::where(['clientkeynum' => $basekeynum])
            ->field('id, title')
            ->order('sort', 'desc')
            ->select();

        // 商品类型选项
        $product_types = [
            Product::PRODUCT_TYPE_NORMAL => '普通商品',
            Product::PRODUCT_TYPE_WEIGHT => '计量商品'
        ];

        $this->assign('category_list', $category_list);
        $this->assign('product_types', $product_types);

        return $this->fetch();
    }

    public function ajax_add_product(Request $request)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $params = $request->param();
        $id = $params['product_id'];
        $productModel = null;
        if (!empty($id)) {
            $productModel = Product::where(['clientkeynum' => $basekeynum, 'id' => $id])->find();
        }

        $res = Product::addOrEdit($params, $productModel);

        if ($res == null) {
            addoperatelog('商品列表',json_encode($params));
            success();
        } else {
            fail(-1, $res);
        }
    }

    public function product_list(Request $request)
    {
        $status = $request->param('status', null);
        $this->assign('status', $status);
        return $this->fetch();
    }

    public function ajax_product_list(Request $request)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');

        $page = $request->param('page', 1);
        $page_size = $request->param('limit', 10);
        $field = $request->param('field', null);
        $keyword = $request->param('keyword', null);
        $status = $request->param('status', -1);
        $product_type = $request->param('product_type', -1);

        $where[] = [
            'clientkeynum', '=', $basekeynum,
        ];

        if (!empty($field) && !empty($keyword)) {
            $where[] = [$field, 'like', "%{$keyword}%"];
        }

        if ($status != -1) {
            $where[] = ['state', '=', $status];
        }

        if ($product_type != -1) {
            $where[] = ['product_type', '=', $product_type];
        }

        $list = Product::where($where)->with(['category'])->page($page, $page_size)->order('id', 'desc')->select();
        $count = Product::where($where)->count();

        // 添加商品类型显示
        foreach ($list as &$item) {
            $item['product_type_text'] = $item['product_type'] == Product::PRODUCT_TYPE_WEIGHT ? '计量商品' : '普通商品';
        }


        success(0, '请求成功', $list, $count);

    }

    public function ajax_product_inventory_list(Request $request)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');

        $id = $request->param('id', null);
        if (empty($id)) fail(-1, '参数错误');

        $list = ProductInventory::where(['clientkeynum' => $basekeynum, 'product_id' => $id])->select();
        success(0, '', $list);
    }

    public function del_product(Request $request)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $request->param('id', null);
        if (empty($id)) {
            fail(-1, '参数错误');
        }

        $info = Product::where(['id' => $id, 'clientkeynum' => $basekeynum])->find();

        if (empty($info)) fail(-1, '未查询到该商品');
        // 查询这个商品是否有订单
        $hasOrder = OrderDetail::where('product_json', 'like', '%"id":' . $id . '%')->where('clientkeynum', $basekeynum)->find();
        if ($hasOrder) fail(-1, '该商品已有订单，无法删除,请进行下架操作');
        // 查询这个商品是否有库存
        $hasInventory = ShopProductInventory::where(['product_id' => $id, 'clientkeynum' => $basekeynum])->find();
        if ($hasInventory) fail(-1, '该商品已有库存，无法删除,请进行下架操作');

        try{
            Db::startTrans();
            UserCart::where(['product_id' => $id, 'clientkeynum' => $basekeynum])->delete();
            Product::where(['id' => $id, 'clientkeynum' => $basekeynum])->delete();
            SkuSpec::where(['product_id' => $id, 'clientkeynum' => $basekeynum])->delete();
            ProductSpecAttr::where(['product_id' => $id, 'clientkeynum' => $basekeynum])->delete();
            ProductInventory::where(['product_id' => $id, 'clientkeynum' => $basekeynum])->delete();
            ShopProductInventory::where(['product_id' => $id, 'clientkeynum' => $basekeynum])->delete();
            Db::commit();
            success(0, '删除成功');
        } catch(\Exception $e) {
            Db::rollback();
            fail(-1, $e->getMessage());
        }

    }



    public function ajax_add_product_spec(Request $request)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');

        $title = $request->param('title', null);

        if (empty($title)) fail(-1,'标题必填');

        $has = SkuSpec::where(['spec_name' => $title])->find();

        if ($has) {
             success(200, '成功', ['id' => $has['id']]);
        } else {
            $data = ['spec_name' => $title, 'created_at' => date('Y-m-d H:i:s')];
            $model = SkuSpec::create($data);
             success(200, '成功', ['id' => $model['id']]);

        }

    }

    public function ajax_add_product_spec_attr(Request $request)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');

        $title = $request->param('title', null);
        $spec_id = $request->param('spec_id', null);

        if ($title == null || $spec_id == null) {
            fail(-1, '必填参数不能为空');
        }

        $has = ProductSpecAttr::where(['spec_id' => $spec_id, 'attr_value' => $title])->find();

        if (!empty($has)) {
            success(200, '成功', ['id' => $has['id']]);
        } else {
            $data = [
                'spec_id' => $spec_id,
                'attr_value' => $title,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $model = ProductSpecAttr::create($data);
            success(200, '成功', ['id' => $model['id']]);
        }


    }

    /**
     * 获取商品列表，支持按店铺查询库存
     * @param Request $request
     * @return void
     */
    public function getList(Request $request)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $keyword = $request->param('keyword', '');
        $limit = $request->param('limit', 20);
        $shop_id = $request->param('shop_id', 0);
        $has_stock = $request->param('has_stock', 0);

        // 构建基础查询条件
        $where = [
            ['p.clientkeynum', '=', $basekeynum],
            ['p.state', '=', 1] // 只查询上架的商品
        ];

        // 关键词搜索
        if (!empty($keyword)) {
            $where[] = ['p.title', 'like', "%{$keyword}%"];
        }

        // 基础查询
        $query = Db::name('products')->alias('p')
            ->field('p.id, p.title, p.price, 0 as inventory_id')
            ->where($where);

        // 如果指定了店铺，关联查询库存
        if (!empty($shop_id)) {
            $query = Db::name('products')->alias('p')
                ->field('p.id, p.title, p.price, pi.id as inventory_id')
                ->leftJoin('shop_product_inventory pi', 'p.id = pi.product_id AND pi.shop_id = ' . $shop_id)
                ->where($where);
        }

        // 获取数据
        $list = $query->limit($limit)->select();
        // 查询出商品  查询规格

        $result = [];
        foreach ($list as $item) {
            // 查询商品下的规格 与 规格信息
            $inventory = ProductInventory::where(['product_id' => $item['id']])->select();


            if (!empty($inventory)) {
                foreach ($inventory as $inventoryItem) {
                    // 查询 店铺规格信息
                    $shopInventory = ShopProductInventory::where(['inventory_id' => $inventoryItem['id']])
                        ->where('shop_id', $shop_id);

                    if ($has_stock) {
                        $shopInventory = $shopInventory->where('stock', '>', 0);
                    }

                    $shopInventory = $shopInventory->find();

                    if ($has_stock && $shopInventory['stock'] <= 0) {
                        continue;
                    }


                    $result[] = [
                        'id' => $item['id'],
                        'name' => $item['title'],
                        'price' => $inventoryItem['price'],
                        'spec_info' => $inventoryItem['title'],
                        'stock_quantity' => $shopInventory['stock'] ?? 0,
                        'inventory_id' => $inventoryItem['id'],
                    ];
                }
            }
        }
        $count = count($list);

        success(0, '请求成功', $result, $count);
    }

}
