<?php
namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\Request;

/**
 * 卡套餐兑换管理控制器
 */
class CardPackageRedemptionController extends Controller
{

    /**
     * 分配门店操作
     * 将快递配送订单分配给指定门店进行处理
     */
    public function assignShop()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $operator = session('cn_accountinfo.accountname');
        $operator_id = session('cn_accountinfo.account_id');
        
        if (request()->isAjax()) {
            $id = input('id');
            $shop_id = input('shop_id');
            $remark = input('remark', '');
            
            if (!$id || !$shop_id) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            
            // 获取门店信息
            $shop = Db::name('shop')->where('id', $shop_id)->where('clientkeynum', $clientkeynum)->find();
            if (!$shop) {
                return json(['code' => 1, 'msg' => '门店不存在']);
            }
            
            // 获取兑换记录
            $redemption = Db::name('card_package_redemption')->where('id', $id)->where('clientkeynum', $clientkeynum)->find();
            if (!$redemption) {
                return json(['code' => 1, 'msg' => '兑换记录不存在']);
            }
            
            // 验证状态
            if ($redemption['status'] != 1) {
                return json(['code' => 1, 'msg' => '只有已兑换状态的记录才能分配门店']);
            }
            
            // 更新门店信息
            $result = Db::name('card_package_redemption')
                ->where('id', $id)
                ->where('clientkeynum', $clientkeynum)
                ->update([
                    'shop_id' => $shop_id,
                    'assign_time' => date('Y-m-d H:i:s'),
                    'assign_operator_id' => $operator_id,
                    'assign_operator' => $operator,
                    'assign_remark' => $remark,
                    'update_time' => date('Y-m-d H:i:s')
                ]);
                
            if ($result) {
                // 记录操作日志
                Db::name('card_package_redemption_log')->insert([
                    'clientkeynum' => $clientkeynum,
                    'redemption_id' => $id,
                    'operator_id' => $operator_id,
                    'operator' => $operator,
                    'operation' => '分配门店',
                    'content' => '分配给门店: ' . $shop['title'] . ($remark ? ', 备注: ' . $remark : ''),
                    'add_time' => date('Y-m-d H:i:s')
                ]);
                
                return json(['code' => 0, 'msg' => '门店分配成功']);
            } else {
                return json(['code' => 1, 'msg' => '门店分配失败']);
            }
        }
        
        $id = input('id');
        $redemption = Db::name('card_package_redemption')->alias('a')
            ->join('card b', 'a.card_id = b.id', 'left')
            ->join('client_member c', 'a.user_id = c.id', 'left')
            ->field('a.*, b.card_no, c.name as user_name')
            ->where('a.id', $id)
            ->where('a.clientkeynum', $clientkeynum)
            ->find();
            
        if (!$redemption) {
            $this->error('兑换记录不存在');
        }
        
        // 获取可选门店列表
        $shops = Db::name('shop')->where('clientkeynum', $clientkeynum)->select();
        $this->assign('shops', $shops);
        $this->assign('info', $redemption);
        
        return $this->fetch();
    }

    /**
     * 门店待发货统计
     * 显示各门店待处理的快递配送订单数量
     */
    public function shopPendingStatistics()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        if (request()->isAjax()) {
            // 查询各门店待发货数量
            $statistics = Db::name('shop')->alias('s')
                ->join('card_package_redemption r', 's.id = r.shop_id', 'left')
                ->field('s.id, s.title as shop_name, COUNT(IF(r.status = 1 AND r.type = 2, 1, NULL)) as pending_count')
                ->where('s.clientkeynum', $clientkeynum)
                ->group('s.id')
                ->order('pending_count', 'desc')
                ->select();
                
            // 获取未分配门店的订单数量
            $unassignedCount = Db::name('card_package_redemption')
                ->where('clientkeynum', $clientkeynum)
                ->where('type', 2) // 快递配送
                ->where('status', 1) // 已兑换
                ->where('shop_id', 'null')
                ->whereOr('shop_id', 0)
                ->count();
                
            return json([
                'code' => 0, 
                'msg' => 'success', 
                'data' => [
                    'statistics' => $statistics,
                    'unassigned_count' => $unassignedCount
                ]
            ]);
        }
        
        return $this->fetch();
    }

    /**
     * 已发货列表查询
     * 查询已发货的订单列表
     */
    public function shippedList()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        if (request()->isAjax()) {
            $where = [];
            $shop_id = input('shop_id', '');
            $card_no = input('card_no', '');
            $user_name = input('user_name', '');
            $express_company = input('express_company', '');
            $express_no = input('express_no', '');
            $start_time = input('start_time', '');
            $end_time = input('end_time', '');
            
            $where[] = ['a.clientkeynum', '=', $clientkeynum];
            $where[] = ['a.status', '=', 2]; // 已发货
            $where[] = ['a.type', '=', 2]; // 快递配送
            
            if ($shop_id) {
                $where[] = ['a.shop_id', '=', $shop_id];
            }
            
            if ($card_no) {
                $where[] = ['b.card_no', 'like', "%{$card_no}%"];
            }
            
            if ($user_name) {
                $where[] = ['c.name', 'like', "%{$user_name}%"];
            }
            
            if ($express_company) {
                $where[] = ['a.express_company', 'like', "%{$express_company}%"];
            }
            
            if ($express_no) {
                $where[] = ['a.express_no', 'like', "%{$express_no}%"];
            }
            
            if ($start_time && $end_time) {
                $where[] = ['a.express_time', 'between', [$start_time, $end_time]];
            } else if ($start_time) {
                $where[] = ['a.express_time', '>=', $start_time];
            } else if ($end_time) {
                $where[] = ['a.express_time', '<=', $end_time];
            }
            
            $list = Db::name('card_package_redemption')->alias('a')
                ->join('card b', 'a.card_id = b.id', 'left')
                ->join('client_member c', 'a.user_id = c.id', 'left')
                ->join('shop d', 'a.shop_id = d.id', 'left')
                ->field('a.*, b.card_no, c.name as user_name, d.title as shop_name')
                ->where($where)
                ->order('a.express_time', 'desc')
                ->paginate(input('limit', 15));
            
            $items = $list->items();
            // 为每个记录添加套餐和商品信息
            foreach ($items as &$item) {
                $item = $this->addPackageAndProductInfo($item);
            }
                
            return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $items]);
        }
        
        // 获取门店列表
        $shops = Db::name('shop')->where('clientkeynum', $clientkeynum)->select();
        $this->assign('shops', $shops);
        
        return $this->fetch();
    }

    /**
     * 待发货列表
     * 查询待发货的订单列表
     */
    public function pendingShipment()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        if (request()->isAjax()) {
            $where = [];
            $shop_id = input('shop_id', '');
            $card_no = input('card_no', '');
            $user_name = input('user_name', '');
            $start_time = input('start_time', '');
            $end_time = input('end_time', '');
            
            $where[] = ['a.clientkeynum', '=', $clientkeynum];
            $where[] = ['a.status', '=', 1]; // 已兑换
            $where[] = ['a.type', '=', 2]; // 快递配送
            
            if ($shop_id) {
                $where[] = ['a.shop_id', '=', $shop_id];
            }
            
            if ($card_no) {
                $where[] = ['b.card_no', 'like', "%{$card_no}%"];
            }
            
            if ($user_name) {
                $where[] = ['c.name', 'like', "%{$user_name}%"];
            }
            
            if ($start_time && $end_time) {
                $where[] = ['a.redemption_time', 'between', [$start_time, $end_time]];
            } else if ($start_time) {
                $where[] = ['a.redemption_time', '>=', $start_time];
            } else if ($end_time) {
                $where[] = ['a.redemption_time', '<=', $end_time];
            }
            
            $list = Db::name('card_package_redemption')->alias('a')
                ->join('card b', 'a.card_id = b.id', 'left')
                ->join('client_member c', 'a.user_id = c.id', 'left')
                ->join('shop d', 'a.shop_id = d.id', 'left')
                ->field('a.*, b.card_no, c.name as user_name, d.title as shop_name')
                ->where($where)
                ->order('a.redemption_time', 'desc')
                ->paginate(input('limit', 15));
            
            $items = $list->items();
            // 为每个记录添加套餐和商品信息
            foreach ($items as &$item) {
                $item = $this->addPackageAndProductInfo($item);
            }
                
            return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $items]);
        }
        
        // 获取门店列表
        $shops = Db::name('shop')->where('clientkeynum', $clientkeynum)->select();
        $this->assign('shops', $shops);
        
        return $this->fetch();
    }

    /**
     * 等待分配门店列表
     * 查询尚未分配门店的快递订单
     */
    public function unassignedList()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        if (request()->isAjax()) {
            $where = [];
            $card_no = input('card_no', '');
            $user_name = input('user_name', '');
            $start_time = input('start_time', '');
            $end_time = input('end_time', '');
            
            $where[] = ['a.clientkeynum', '=', $clientkeynum];
            $where[] = ['a.status', '=', 1]; // 已兑换
            $where[] = ['a.type', '=', 2]; // 快递配送
            $where[] = ['a.shop_id', 'null']; // 未分配门店
            
            if ($card_no) {
                $where[] = ['b.card_no', 'like', "%{$card_no}%"];
            }
            
            if ($user_name) {
                $where[] = ['c.name', 'like', "%{$user_name}%"];
            }
            
            if ($start_time && $end_time) {
                $where[] = ['a.redemption_time', 'between', [$start_time, $end_time]];
            } else if ($start_time) {
                $where[] = ['a.redemption_time', '>=', $start_time];
            } else if ($end_time) {
                $where[] = ['a.redemption_time', '<=', $end_time];
            }
            
            $list = Db::name('card_package_redemption')->alias('a')
                ->join('card b', 'a.card_id = b.id', 'left')
                ->join('client_member c', 'a.user_id = c.id', 'left')
                ->field('a.*, b.card_no, c.name as user_name')
                ->where($where)
                ->whereOr(function ($query) use ($clientkeynum) {
                    $query->where('a.clientkeynum', $clientkeynum)
                        ->where('a.status', 1)
                        ->where('a.type', 2)
                        ->where('a.shop_id', 0);
                })
                ->order('a.redemption_time', 'desc')
                ->paginate(input('limit', 15));
            
            $items = $list->items();
            // 为每个记录添加套餐和商品信息
            foreach ($items as &$item) {
                $item = $this->addPackageAndProductInfo($item);
            }
                
            return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $items]);
        }
        
        return $this->fetch();
    }

    /**
     * 批量分配门店
     * 批量将订单分配给指定门店
     */
    public function batchAssignShop()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $operator = session('cn_accountinfo.accountname');
        $operator_id = session('cn_accountinfo.account_id');
        
        if (request()->isAjax()) {
            $ids = input('ids');
            $shop_id = input('shop_id');
            $remark = input('remark', '');
            
            if (empty($ids) || !$shop_id) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            
            // 获取门店信息
            $shop = Db::name('shop')->where('id', $shop_id)->where('clientkeynum', $clientkeynum)->find();
            if (!$shop) {
                return json(['code' => 1, 'msg' => '门店不存在']);
            }
            
            // 开启事务
            Db::startTrans();
            try {
                $id_array = explode(',', $ids);
                $success_count = 0;
                
                foreach ($id_array as $id) {
                    // 获取兑换记录
                    $redemption = Db::name('card_package_redemption')
                        ->where('id', $id)
                        ->where('clientkeynum', $clientkeynum)
                        ->where('status', 1) // 已兑换
                        ->where('type', 2) // 快递配送
                        ->find();
                        
                    if (!$redemption) {
                        continue;
                    }
                    
                    // 更新门店信息
                    $result = Db::name('card_package_redemption')
                        ->where('id', $id)
                        ->where('clientkeynum', $clientkeynum)
                        ->update([
                            'shop_id' => $shop_id,
                            'assign_time' => date('Y-m-d H:i:s'),
                            'assign_operator_id' => $operator_id,
                            'assign_operator' => $operator,
                            'assign_remark' => $remark,
                            'update_time' => date('Y-m-d H:i:s')
                        ]);
                        
                    if ($result) {
                        $success_count++;
                        
                        // 记录操作日志
                        Db::name('card_package_redemption_log')->insert([
                            'clientkeynum' => $clientkeynum,
                            'redemption_id' => $id,
                            'operator_id' => $operator_id,
                            'operator' => $operator,
                            'operation' => '批量分配门店',
                            'content' => '分配给门店: ' . $shop['title'] . ($remark ? ', 备注: ' . $remark : ''),
                            'add_time' => date('Y-m-d H:i:s')
                        ]);
                    }
                }
                
                Db::commit();
                return json(['code' => 0, 'msg' => "成功分配 {$success_count} 条记录"]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '批量分配失败: ' . $e->getMessage()]);
            }
        }
        
        $ids = input('ids');
        
        // 获取可选门店列表
        $shops = Db::name('shop')->where('clientkeynum', $clientkeynum)->select();
        $this->assign('shops', $shops);
        $this->assign('ids', $ids);
        
        return $this->fetch();
    }

    /**
     * 兑换记录列表
     */
    public function index()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        if (request()->isAjax()) {
            $where = [];
            $card_no = input('card_no', '');
            $user_name = input('user_name', '');
            $status = input('status', '');
            $type = input('type', '');
            $start_time = input('start_time', '');
            $end_time = input('end_time', '');
            
            $where[] = ['a.clientkeynum', '=', $clientkeynum];
            
            if ($card_no) {
                $where[] = ['b.card_no', 'like', "%{$card_no}%"];
            }
            
            if ($user_name) {
                $where[] = ['c.name', 'like', "%{$user_name}%"];
            }
            
            if ($status !== '') {
                $where[] = ['a.status', '=', $status];
            }
            
            if ($type !== '') {
                $where[] = ['a.type', '=', $type];
            }
            
            if ($start_time && $end_time) {
                $where[] = ['a.redemption_time', 'between', [$start_time, $end_time]];
            } else if ($start_time) {
                $where[] = ['a.redemption_time', '>=', $start_time];
            } else if ($end_time) {
                $where[] = ['a.redemption_time', '<=', $end_time];
            }
            
            $list = Db::name('card_package_redemption')->alias('a')
                ->join('card b', 'a.card_id = b.id', 'left')
                ->join('client_member c', 'a.user_id = c.id', 'left')
                ->join('shop d', 'a.shop_id = d.id', 'left')
                ->field('a.*, b.card_no, c.name as user_name, d.title as shop_name, b.type_id as card_type_id')
                ->where($where)
                ->order('a.id', 'desc')
                ->paginate(input('limit', 15));
            
            $items = $list->items();
            foreach ($items as &$item) {
                $item['status_text'] = $this->getStatusText($item['status']);
                $item['type_text'] = $this->getTypeText($item['type']);
                $item['card_type_name'] = Db::name('card_package')->where('id', $item['card_type_id'])->value('name');
                // 添加套餐和商品信息
                $item = $this->addPackageAndProductInfo($item);
            }
                
            return json(['code' => 0, 'msg' => 'success', 'count' => $list->total(), 'data' => $items]);
        }
        
        return $this->fetch();
    }
    
    /**
     * 查看兑换详情
     */
    public function detail($id)
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        // 获取兑换主表信息
        $info = Db::name('card_package_redemption')->alias('a')
            ->join('card b', 'a.card_id = b.id', 'left')
            ->join('client_member c', 'a.user_id = c.id', 'left')
            ->join('shop d', 'a.shop_id = d.id', 'left')
            ->field('a.*, b.card_no, c.name as user_name, d.title as shop_name')
            ->where('a.id', $id)
            ->where('a.clientkeynum', $clientkeynum)
            ->find();
            
        if (!$info) {
            $this->error('兑换记录不存在');
        }
        
        $info['status_text'] = $this->getStatusText($info['status']);
        $info['type_text'] = $this->getTypeText($info['type']);
        
        // 获取兑换套餐明细
        $details = Db::name('card_package_redemption_detail')->alias('a')
            ->join('card_package b', 'a.package_id = b.id', 'left')
            ->field('a.*, b.name as package_name, b.price as package_price, b.validity_days')
            ->where('a.redemption_id', $id)
            ->where('a.clientkeynum', $clientkeynum)
            ->select();
            
        // 获取兑换商品
        $products = Db::name('card_package_redemption_product')->alias('a')
            ->join('product_inventory b', 'a.product_inventory_id = b.id', 'left')
            ->join('products c', 'b.product_id = c.id', 'left')
            ->field('a.*, c.title as product_name, b.title as spec_values, IFNULL(b.price, 0) as product_price')
            ->where('a.redemption_id', $id)
            ->where('a.clientkeynum', $clientkeynum)
            ->select();
            
        $this->assign('info', $info);
        $this->assign('details', $details);
        $this->assign('products', $products);
        
        return $this->fetch();
    }
    
    /**
     * 更新兑换状态
     */
    public function updateStatus()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $id = input('id');
        $status = input('status');
        $remark = input('remark', '');
        
        $model = new \app\admin\model\CardPackageRedemption;
        $result = $model->updateRedemptionStatus($id, $status, $remark);
        
        if ($result) {
            return json(['code' => 0, 'msg' => '状态更新成功']);
        } else {
            return json(['code' => 1, 'msg' => '状态更新失败']);
        }
    }
    
    /**
     * 更新快递信息
     */
    public function updateExpress()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $id = input('id');
        $data = input('post.');
        
        $updateData = [
            'express_company' => isset($data['express_company']) ? $data['express_company'] : '',
            'express_no' => isset($data['express_no']) ? $data['express_no'] : '',
            'express_time' => date('Y-m-d H:i:s'),
            'express_status' => 1, // 已发货
            'update_time' => date('Y-m-d H:i:s'),
            'status' => 2 // 已发货
        ];
        
        if (isset($data['express_remark'])) {
            $updateData['express_remark'] = $data['express_remark'];
        }
        
        $result = Db::name('card_package_redemption')
            ->where('id', $id)
            ->where('clientkeynum', $clientkeynum)
            ->update($updateData);
            
        if ($result) {
            return json(['code' => 0, 'msg' => '快递信息更新成功']);
        } else {
            return json(['code' => 1, 'msg' => '快递信息更新失败']);
        }
    }
    
    /**
     * 导出兑换记录
     */
    public function export()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $card_no = input('card_no', '');
        $user_name = input('user_name', '');
        $status = input('status', '');
        $type = input('type', '');
        $start_time = input('start_time', '');
        $end_time = input('end_time', '');
        
        $where = [];
        $where[] = ['a.clientkeynum', '=', $clientkeynum];
        
        if ($card_no) {
            $where[] = ['b.card_no', 'like', "%{$card_no}%"];
        }
        
        if ($user_name) {
            $where[] = ['c.name', 'like', "%{$user_name}%"];
        }
        
        if ($status !== '') {
            $where[] = ['a.status', '=', $status];
        }
        
        if ($type !== '') {
            $where[] = ['a.type', '=', $type];
        }
        
        if ($start_time && $end_time) {
            $where[] = ['a.redemption_time', 'between', [$start_time, $end_time]];
        } else if ($start_time) {
            $where[] = ['a.redemption_time', '>=', $start_time];
        } else if ($end_time) {
            $where[] = ['a.redemption_time', '<=', $end_time];
        }
        
        $list = Db::name('card_package_redemption')->alias('a')
            ->join('card b', 'a.card_id = b.id', 'left')
            ->join('client_member c', 'a.user_id = c.id', 'left')
            ->join('shop d', 'a.shop_id = d.id', 'left')
            ->field('a.*, b.card_no, c.name as user_name, d.name as shop_name')
            ->where($where)
            ->order('a.id', 'desc')
            ->select();
            
        foreach ($list as &$item) {
            $item['status_text'] = $this->getStatusText($item['status']);
            $item['type_text'] = $this->getTypeText($item['type']);
        }
        
        $title = '卡套餐兑换记录';
        $header = ['兑换ID', '卡号', '用户', '兑换时间', '状态', '类型', '门店', '联系人', '电话', '地址', '快递公司', '快递单号', '备注'];
        $data = [];
        
        foreach ($list as $item) {
            $address = '';
            if (!empty($item['province'])) {
                $address .= $item['province'];
            }
            if (!empty($item['city'])) {
                $address .= $item['city'];
            }
            if (!empty($item['area'])) {
                $address .= $item['area'];
            }
            if (!empty($item['address'])) {
                $address .= $item['address'];
            }
            
            $data[] = [
                'id' => $item['id'],
                'card_no' => $item['card_no'],
                'user_name' => $item['user_name'],
                'redemption_time' => $item['redemption_time'],
                'status_text' => $item['status_text'],
                'type_text' => $item['type_text'],
                'shop_name' => $item['shop_name'],
                'name' => $item['name'],
                'phone' => $item['phone'],
                'address' => $address,
                'express_company' => $item['express_company'],
                'express_no' => $item['express_no'],
                'remark' => $item['remark']
            ];
        }
        
        $this->exportExcel($title, $header, $data);
    }
    
    /**
     * 导出Excel
     */
    private function exportExcel($title, $header, $data)
    {
        include_once \think\facade\Env::get('extend_path') . 'PHPExcel/Classes/PHPExcel.php';
        
        $objPHPExcel = new \PHPExcel();
        $objSheet = $objPHPExcel->getActiveSheet();
        $objSheet->setTitle($title);
        
        // 设置表头
        for ($i = 0; $i < count($header); $i++) {
            $objSheet->setCellValue(chr(65 + $i) . '1', $header[$i]);
            $objSheet->getStyle(chr(65 + $i) . '1')->getFont()->setBold(true);
        }
        
        // 填充数据
        $row = 2;
        foreach ($data as $item) {
            $col = 0;
            foreach ($item as $value) {
                $objSheet->setCellValue(chr(65 + $col) . $row, $value);
                $col++;
            }
            $row++;
        }
        
        // 输出Excel文件
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $title . '_' . date('YmdHis') . '.xls"');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $objWriter->save('php://output');
        exit;
    }
    
    /**
     * 添加兑换记录（门店操作）
     */
    public function add()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $operator = session('cn_accountinfo.accountname');
        $operator_id = session('cn_accountinfo.account_id');
        
        if (request()->isAjax()) {
            $data = input('post.');
            $model = new \app\admin\model\CardPackageRedemption;
            
            // 开启事务
            $model->startTrans();
            try {
                // 验证卡信息
                $card = Db::name('card')->where('card_no', $data['card_no'])->where('clientkeynum', $clientkeynum)->find();
                if (!$card) {
                    return json(['code' => 1, 'msg' => '卡号不存在']);
                }
                
                if ($card['status'] != 1) {
                    return json(['code' => 1, 'msg' => '卡状态不正确，只有已激活的卡才能兑换']);
                }
                
                // 获取卡关联的套餐
                $packages = Db::name('card_type_package_relation')->alias('a')
                    ->join('card_package b', 'a.package_id = b.id')
                    ->field('b.*')
                    ->where('a.type_id', $card['type_id'])
                    ->where('a.clientkeynum', $clientkeynum)
                    ->select();
                
                if (empty($packages)) {
                    return json(['code' => 1, 'msg' => '该卡未关联任何套餐']);
                }
                
                // 创建兑换主表记录
                $redemptionData = [
                    'clientkeynum' => $clientkeynum,
                    'card_id' => $card['id'],
                    'user_id' => $card['user_id'] ? $card['user_id'] : ($card['customer_id'] ? $card['customer_id'] : 0),
                    'redemption_time' => date('Y-m-d H:i:s'),
                    'status' => 1, // 已兑换
                    'type' => $data['type'],
                    'shop_id' => $data['type'] == 1 ? $data['shop_id'] : null,
                    'operator_id' => $operator_id,
                    'remark' => isset($data['remark']) ? $data['remark'] : '',
                    'add_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ];
                
                // 如果是快递配送，添加收货信息
                if ($data['type'] == 2) {
                    $redemptionData['name'] = isset($data['name']) ? $data['name'] : '';
                    $redemptionData['phone'] = isset($data['phone']) ? $data['phone'] : '';
                    $redemptionData['postal_code'] = isset($data['postal_code']) ? $data['postal_code'] : '';
                    $redemptionData['province'] = isset($data['province']) ? $data['province'] : '';
                    $redemptionData['city'] = isset($data['city']) ? $data['city'] : '';
                    $redemptionData['area'] = isset($data['area']) ? $data['area'] : '';
                    $redemptionData['address'] = isset($data['address']) ? $data['address'] : '';
                }
                
                $model->data($redemptionData);
                $model->save();
                $redemptionId = $model->id;
                
                // 添加兑换套餐明细
                $detailsData = [];
                foreach ($packages as $package) {
                    // 计算有效期
                    $validStartTime = date('Y-m-d H:i:s');
                    $validEndTime = date('Y-m-d H:i:s', strtotime("+{$package['validity_days']} days"));
                    
                    $detailsData[] = [
                        'clientkeynum' => $clientkeynum,
                        'redemption_id' => $redemptionId,
                        'package_id' => $package['id'],
                        'status' => 1, // 已兑换
                        'valid_start_time' => $validStartTime,
                        'valid_end_time' => $validEndTime,
                        'remark' => '',
                        'add_time' => date('Y-m-d H:i:s'),
                        'update_time' => date('Y-m-d H:i:s')
                    ];
                    
                    // 获取套餐关联的商品
                    $packageProducts = Db::name('card_package_product')
                        ->where('package_id', $package['id'])
                        ->where('clientkeynum', $clientkeynum)
                        ->select();
                    
                    // 添加兑换商品
                    if (!empty($packageProducts)) {
                        $productsData = [];
                        foreach ($packageProducts as $product) {
                            $productsData[] = [
                                'clientkeynum' => $clientkeynum,
                                'redemption_id' => $redemptionId,
                                'product_inventory_id' => $product['product_inventory_id'],
                                'quantity' => $product['quantity'],
                                'add_time' => date('Y-m-d H:i:s'),
                                'update_time' => date('Y-m-d H:i:s')
                            ];
                        }
                        
                        if (!empty($productsData)) {
                            Db::name('card_package_redemption_product')->insertAll($productsData);
                        }
                    }
                }
                
                if (!empty($detailsData)) {
                    Db::name('card_package_redemption_detail')->insertAll($detailsData);
                }
                
                // 更新卡状态为已使用
                Db::name('card')->where('id', $card['id'])->update([
                    'status' => 2, // 已使用
                    'update_time' => date('Y-m-d H:i:s')
                ]);
                
                // 记录卡状态变更
                Db::name('card_status_record')->insert([
                    'clientkeynum' => $clientkeynum,
                    'card_id' => $card['id'],
                    'old_status' => $card['status'],
                    'new_status' => 2, // 已使用
                    'operator_id' => $operator_id,
                    'operator_name' => $operator,
                    'operation_time' => date('Y-m-d H:i:s'),
                    'reason' => '套餐兑换',
                    'remark' => '套餐兑换消费',
                    'add_time' => date('Y-m-d H:i:s')
                ]);
                
                $model->commit();
                return json(['code' => 0, 'msg' => '兑换成功']);
            } catch (\Exception $e) {
                $model->rollback();
                return json(['code' => 1, 'msg' => '兑换失败：' . $e->getMessage()]);
            }
        }
        
        // 获取门店列表
        $shops = Db::name('shop')->where('clientkeynum', $clientkeynum)->select();
        $this->assign('shops', $shops);
        
        return $this->fetch();
    }
    
    /**
     * 获取卡信息
     */
    public function getCardInfo()
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $card_no = input('card_no');
        
        $card = Db::name('card')->alias('a')
            ->join('card_package_type b', 'a.type_id = b.id', 'left')
            ->field('a.*, b.name as type_name')
            ->where('a.card_no', $card_no)
            ->where('a.clientkeynum', $clientkeynum)
            ->find();
            
        if (!$card) {
            return json(['code' => 1, 'msg' => '卡号不存在']);
        }
        
        if ($card['status'] != 1) {
            return json(['code' => 1, 'msg' => '卡状态不正确，只有已激活的卡才能兑换']);
        }
        
        // 获取卡关联的套餐
        $packages = Db::name('card_type_package_relation')->alias('a')
            ->join('card_package b', 'a.package_id = b.id')
            ->field('b.*')
            ->where('a.type_id', $card['type_id'])
            ->where('a.clientkeynum', $clientkeynum)
            ->select();
            
        if (empty($packages)) {
            return json(['code' => 1, 'msg' => '该卡未关联任何套餐']);
        }
        
        // 获取所有套餐关联的商品
        foreach ($packages as &$package) {
            $products = Db::name('card_package_product')->alias('a')
                ->join('product_inventory b', 'a.product_inventory_id = b.id')
                ->join('products c', 'b.product_id = c.id')
                ->field('a.product_inventory_id, a.quantity, c.title as product_name, b.title as spec_values')
                ->where('a.package_id', $package['id'])
                ->where('a.clientkeynum', $clientkeynum)
                ->select();
                
            $package['products'] = $products;
        }
        
        $data = [
            'card' => $card,
            'packages' => $packages
        ];
        
        return json(['code' => 0, 'msg' => 'success', 'data' => $data]);
    }
    
    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        $statusArr = [
            0 => '已取消',
            1 => '已兑换',
            2 => '已发货',
            100 => '已完成'
        ];
        
        return isset($statusArr[$status]) ? $statusArr[$status] : '未知状态';
    }
    
    /**
     * 获取类型文本
     */
    private function getTypeText($type)
    {
        $typeArr = [
            1 => '到店兑换',
            2 => '快递配送'
        ];
        
        return isset($typeArr[$type]) ? $typeArr[$type] : '未知类型';
    }
    
    /**
     * 为兑换记录添加套餐和商品信息
     */
    private function addPackageAndProductInfo($item)
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        // 获取套餐信息
        $packages = Db::name('card_package_redemption_detail')->alias('a')
            ->join('card_package b', 'a.package_id = b.id', 'left')
            ->field('b.name as package_name, b.price as package_price, b.validity_days')
            ->where('a.redemption_id', $item['id'])
            ->where('a.clientkeynum', $clientkeynum)
            ->select();
            
        // 获取商品信息
        $products = Db::name('card_package_redemption_product')->alias('a')
            ->join('product_inventory b', 'a.product_inventory_id = b.id', 'left')
            ->join('products c', 'b.product_id = c.id', 'left')
            ->field('a.quantity, c.title as product_name, b.title as spec_values')
            ->where('a.redemption_id', $item['id'])
            ->where('a.clientkeynum', $clientkeynum)
            ->select();
            
        // 格式化套餐信息
        $packageNames = [];
        foreach ($packages as $package) {
            $packageNames[] = $package['package_name'];
        }
        $item['package_names'] = implode('、', $packageNames);
        
        // 格式化商品信息 - 每行一个商品
        $productNames = [];
        foreach ($products as $product) {
            $productName = $product['product_name'];
            if (!empty($product['spec_values'])) {
                $productName .= '(' . $product['spec_values'] . ')';
            }
            $productName .= ' x' . $product['quantity'];
            $productNames[] = $productName;
        }
        $item['product_names'] = implode('<br>', $productNames);
        
        return $item;
    }
} 