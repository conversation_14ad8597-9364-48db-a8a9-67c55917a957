<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright ( c ) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use app\admin\model\ProductInventory;
use app\api\model\InventoryLog;
use app\api\model\InventoryOrder;
use app\api\model\InventoryOrderDetail;
use think\facade\Session;
use think\facade\Request;
use think\Db;
use think\Tree;
use think\facade\Cache;

class SgoodsController extends CnController
{
    /**
     * 初始化方法
     */

    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    public function noaudit()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/noaudit', 0);
        $this->assign("type", 1);
        return $this->fetch('goods_list');
    }

    // 每日获取一下商品上下架状态进行更新
    public function ajax_get_noaudit_list()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $request = Request::instance();
        $param = $request->param();
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/noaudit', 1);
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        //计算记录偏移量
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $where = "1=1  and  shop_id='$shop_id' and is_audit='0' and state='1'";
        if ($keyword) {
            $where .= " and $field like '%$keyword%'";
        }
        if ($param["id"] && $param["id"] != "-1") {
            $where .= " and category_id='" . $param["id"] . "'";
        }
        $order_by = "id desc";
        $list = Db::view('shop_product_detail')->where($where)->order($order_by)->limit($offset . ',' . $pagesize)->select();
        $count = Db::view('shop_product_detail')->where($where)->count();
        $rtdata['data'] = $list;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        echo json_encode($rtdata);
        die;
    }

    public function up_goods()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/up_goods', 0);
        $this->assign("type", 2);
        return $this->fetch('goods_list');
    }

    // 每日获取一下商品上下架状态进行更新
    public function ajax_get_up_goods_list()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $request = Request::instance();
        $param = $request->param();
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/up_goods', 1);
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        //计算记录偏移量
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $where = "1=1  and  shop_id='$shop_id' and is_audit='1' and status='1' and state='1'";
        if ($keyword) {
            $where .= " and $field like '%$keyword%'";
        }
        if ($param["id"] && $param["id"] != "-1") {
            $where .= " and category_id='" . $param["id"] . "'";
        }
        $order_by = "id desc";
        $list = Db::view('shop_product_detail')->where($where)->order($order_by)->limit($offset . ',' . $pagesize)->select();
        $count = Db::view('shop_product_detail')->where($where)->count();
        $rtdata['data'] = $list;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        echo json_encode($rtdata);
        die;
    }

    public function down_goods()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/down_goods', 0);
        $this->assign("type", 3);
        return $this->fetch('goods_list');
    }

    // 每日获取一下商品上下架状态进行更新
    public function ajax_get_down_goods_list()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $request = Request::instance();
        $param = $request->param();
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/up_goods', 1);
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        //计算记录偏移量
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $where = "1=1  and  shop_id='$shop_id' and is_audit='1' and status='0' and state='1'";
        if ($keyword) {
            $where .= " and $field like '%$keyword%'";
        }
        if ($param["id"] && $param["id"] != "-1") {
            $where .= " and category_id='" . $param["id"] . "'";
        }
        $order_by = "id desc";
        $list = Db::view('shop_product_detail')->where($where)->order($order_by)->limit($offset . ',' . $pagesize)->select();
        $count = Db::view('shop_product_detail')->where($where)->count();
        $rtdata['data'] = $list;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        echo json_encode($rtdata);
        die;
    }

    public function stock_log()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/stock_log', 0);
        return $this->fetch('stock_log');
    }

    // 每日获取一下商品上下架状态进行更新
    public function ajax_get_stock_log_list()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $request = Request::instance();
        $param = $request->param();
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/up_goods', 1);
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1);
        //计算记录偏移量
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $where = "1=1  and  shop_id='$shop_id'";
        if ($keyword) {
            if($field== 'product_name'){
                $product_id = Db::view('shop_product_detail')
                ->where("title like '%$keyword%' and shop_id='$shop_id'")->value('product_id');
                $where .= " and product_id='$product_id'";
            }elseif($field== 'spec_name'){
                $spec_id = Db::view('shop_product_inventory_detail')
                ->where("title like '%$keyword%' and shop_id='$shop_id'")->value('inventory_id');
                $where .= " and spec_id='$spec_id'";
            }
        }
        $begin_time = $param['begin_time'];
        $end_time = $param['end_time'];
        if ($begin_time && $end_time) {
            $where .= " and time>='$begin_time' and time<='$end_time'";
        }
        $order_by = "id desc";
        $list = Db::table('shop_product_stock_log')->where($where)->order($order_by)->limit($offset . ',' . $pagesize)->select();
        foreach ($list as $key => $value) {
            $product_id = $value['product_id'];
            $spec_id = $value['spec_id'];
            $product_name = Db::view('shop_product_detail')
            ->where("product_id='$product_id' and shop_id='$shop_id'")->value('title');
            $spec_name = Db::view('shop_product_inventory_detail')
            ->where("product_id='$product_id' and inventory_id='$spec_id' and shop_id='$shop_id'")->value('title');
            $list[$key]['product_name'] = $product_name;
            $list[$key]['spec_name'] = $spec_name;
        }
        $count = Db::table('shop_product_stock_log')->where($where)->count();
        $rtdata['data'] = $list;
        $rtdata['count'] = $count;
        $rtdata['code'] = 0;
        $rtdata['msg'] = '';
        echo json_encode($rtdata);
        die;
    }

    //获取地址树

    public function get_area_treedata()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $list[0]['id'] = -1;
        $list[0]['name'] = '全部';
        $list[0]['keynum'] = '';
        $category_id = Db::view('shop_product_detail')
            ->where("shop_id='$shop_id' and state='1'")
            ->column("category_id");
        $where[] = ['id', 'in', $category_id];
        $category_list = Db::table('product_category')
            ->where($where)
            ->field('id as id ,title as name ')
            ->order('sort asc')
            ->select();
        $alllist = array_merge($list, $category_list);
        foreach ($alllist as $k => $v) {
            if ($v['id'] != -1) {
                $alllist[$k]['parentId'] = -1;
                $alllist[$k]['allpathid'] = '-1,' . $v['id'];
            } else {
                $alllist[$k]['parentId'] = '';
                $alllist[$k]['allpathid'] = '-1';
            }
        }
        echo json_encode($alllist);
    }

    public function ajax_xiajia_goods()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $request = Request::instance();
        $param = $request->param();
        $alldata = json_decode($param["alldata"], 1);
        $ids = "";
        foreach ($alldata as $key => $value) {
            $ids .= $value["id"] . ",";
        }
        $ids = trim($ids, ",");
        if (empty($ids)) {
            $rt["sta"] = 0;
            $rt["msg"] = "请选择要操作的数据";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        $where = "shop_id='$shop_id' and status='1' and id in ($ids)";
        $save["status"] = 0;
        $rs = Db::table("shop_product")->where($where)->update($save);
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        addoperatelog('标记商品到下架', $ids);
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    public function ajax_shangjia_goods()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $request = Request::instance();
        $param = $request->param();
        $alldata = json_decode($param["alldata"], 1);
        $ids = "";
        foreach ($alldata as $key => $value) {
            $ids .= $value["id"] . ",";
        }
        $ids = trim($ids, ",");
        if (empty($ids)) {
            $rt["sta"] = 0;
            $rt["msg"] = "请选择要操作的数据";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        $where = "shop_id='$shop_id' and status='0' and id in ($ids)";
        $save["status"] = 1;
        $rs = Db::table("shop_product")->where($where)->update($save);
        if (!$rs) {
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        addoperatelog('标记商品到上架', $ids);
        $rt["sta"] = 1;
        $rt["msg"] = "操作成功";
        echo json_encode($rt, JSON_UNESCAPED_UNICODE);
        die;
    }

    public function ajax_audit_up()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $request = Request::instance();
        $param = $request->param();
        $alldata = json_decode($param["alldata"], 1);
        $ids = "";
        foreach ($alldata as $key => $value) {
            $ids .= $value["id"] . ",";
        }
        $ids = trim($ids, ",");
        if (empty($ids)) {
            $rt["sta"] = 0;
            $rt["msg"] = "请选择要操作的数据";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        Db::startTrans();
        try {
            $where = "shop_id='$shop_id' and is_audit='0' and id in ($ids)";
            $goods_list = Db::view("shop_product_detail")->where($where)->select();
            foreach ($goods_list as $k => $v) {
                $product_id = $v["product_id"];
                $product_inventory = Db::table('product_inventory')->where('product_id', $product_id)->select();
                if (empty($product_inventory)) {
                    throw new \Exception($v["title"] . "商品规格未设置");
                }
                foreach ($product_inventory as $kk => $vv) {
                    $has = Db::table("shop_product_inventory")
                        ->where("shop_id='$shop_id' and product_id='$product_id' and inventory_id='$vv[id]'")->find();
                    if ($has) {
                        continue;
                    }
                    $add = [];
                    $add["shop_id"] = $shop_id;
                    $add["product_id"] = $product_id;
                    $add["inventory_id"] = $vv["id"];
                    $add["stock"] = 0;
                    $add["add_time"] = date("Y-m-d H:i:s");
                    Db::table("shop_product_inventory")->insert($add);
                }
            }
            $save["status"] = 1;
            $save["is_audit"] = 1;
            $save["audit_time"] = date("Y-m-d H:i:s");
            Db::table("shop_product")->where($where)->update($save);

            addoperatelog('审核商品到上架', $ids);
            Db::commit();
            $rt["sta"] = 1;
            $rt["msg"] = "操作成功";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        } catch (\Exception $e) {
            Db::rollback();
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败:" . $e->getMessage();
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
    }

    public function ajax_audit_down()
    {
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $request = Request::instance();
        $param = $request->param();
        $alldata = json_decode($param["alldata"], 1);
        $ids = "";
        foreach ($alldata as $key => $value) {
            $ids .= $value["id"] . ",";
        }
        $ids = trim($ids, ",");
        if (empty($ids)) {
            $rt["sta"] = 0;
            $rt["msg"] = "请选择要操作的数据";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
        Db::startTrans();
        try {
            $where = "shop_id='$shop_id' and is_audit='0' and id in ($ids)";
            $goods_list = Db::view("shop_product_detail")->where($where)->select();
            foreach ($goods_list as $k => $v) {
                $product_id = $v["product_id"];
                $product_inventory = Db::table('product_inventory')->where('product_id', $product_id)->select();
                if (empty($product_inventory)) {
                    throw new \Exception($v["title"] . "商品规格未设置");
                }
                foreach ($product_inventory as $kk => $vv) {
                    $has = Db::table("shop_product_inventory")
                        ->where("shop_id='$shop_id' and product_id='$product_id' and inventory_id='$vv[id]'")->find();
                    if ($has) {
                        continue;
                    }
                    $add = [];
                    $add["shop_id"] = $shop_id;
                    $add["product_id"] = $product_id;
                    $add["inventory_id"] = $vv["id"];
                    $add["stock"] = 0;
                    $add["add_time"] = date("Y-m-d H:i:s");
                    Db::table("shop_product_inventory")->insert($add);
                }
            }
            $save["status"] = 0;
            $save["is_audit"] = 1;
            $save["audit_time"] = date("Y-m-d H:i:s");
            Db::table("shop_product")->where($where)->update($save);

            addoperatelog('审核商品到下架', $ids);
            Db::commit();
            $rt["sta"] = 1;
            $rt["msg"] = "操作成功";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        } catch (\Exception $e) {
            Db::rollback();
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败:" . $e->getMessage();
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
    }

    public function detail(){
        $request = Request::instance();
        $param = $request->param();
        $id = $param['id'];
        $info = Db::view('shop_product_detail')->where('id', $id)->find();
        if (!$info) {
            error_tips("商品数据不存在");
        }
        $info['banner'] = array_filter(explode("|", $info['banner']));
        $info['content'] = array_filter(explode("|", $info['content']));
        $this->assign('goods_info', $info);
        $product_id = $info['product_id'];
        $shop_id = $info['shop_id'];
        $where = "shop_id='$shop_id' and product_id='$product_id'";
        // 查询当前商品的所有规格信息

        $inventory_list = ProductInventory::where(['product_id' => $product_id])->select();


        if (empty($inventory_list)) {
            error_tips("商品规格库存数据不存在");
        }

        $this->assign('inventory_list', $inventory_list);
        return $this->fetch('detail');
    }

    public function save_stock()
    {
        $request = Request::instance();
        $param = $request->param();
        $id = $param['id'];
        $info = Db::view('shop_product_detail')->where('id', $id)->find();
        if (!$info) {
            error_tips("商品数据不存在");
        }
        $info['banner'] = array_filter(explode("|", $info['banner']));
        $info['content'] = array_filter(explode("|", $info['content']));
        $this->assign('goods_info', $info);
        $product_id = $info['product_id'];
        $shop_id = $info['shop_id'];
        $where = "shop_id='$shop_id' and product_id='$product_id'";
        // 查询当前商品的所有规格信息

        $inventory_list = ProductInventory::where(['product_id' => $product_id])->select();


        if (empty($inventory_list)) {
            error_tips("商品规格库存数据不存在");
        }
        // 循环查询库存
        foreach ($inventory_list as $key => $value) {
            $inventory_list[$key]['stock'] = Db::table('shop_product_inventory')
                ->where(['shop_id' => $shop_id, 'product_id' => $product_id, 'inventory_id' => $value['id']])
                ->value('stock') ?? 0;
        }

//        $shop_product_inventory = Db::view('shop_product_inventory_detail')->where($where)->select();
//        if (empty($shop_product_inventory)) {
//            error_tips("商品规格库存数据不存在");
//        }
        $this->assign('inventory_list', $inventory_list);
        return $this->fetch('save_stock');
    }

    public function ajax_save_stock()
    {
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = session('cn_accountinfo.keynum');
        $shop_id = Db::table('shop')->where("keynum='$basekeynum'")->value('id');
        $clientkeynum = Db::table('shop')->where("keynum='$basekeynum'")->value('clientkeynum');
        $kucun = $param['kucun'];
        
        // 准备创建入库单的明细数据
        $inventory_details = [];
        
        Db::startTrans();
        try {
            foreach ($kucun as $key => $value) {
                $where = "shop_id='$shop_id' and product_id='{$param['id']}' and inventory_id = $key";
                $old_info = Db::table("shop_product_inventory")->where($where)->find();
                
                // 获取商品和规格信息，用于记录和显示
                $product_info = Db::table('products')->where(['id' => $param['id']])->find();
                $inventory_info = Db::table('product_inventory')->where(['id' => $key])->find();
                
                if (!$old_info) {
                    // 插入新库存
                    $add_info["shop_id"] = $shop_id;
                    $add_info["product_id"] = $param['id'];
                    $add_info["inventory_id"] = $key;
                    $add_info["stock"] = $value;
                    $add_info["add_time"] = date("Y-m-d H:i:s");
                    $add_info['clientkeynum'] = $clientkeynum;
                    Db::table("shop_product_inventory")->insert($add_info);
                    
                    // 记录库存日志（新方式）
                    $inventory_log = [
                        'clientkeynum' => $clientkeynum,
                        'shop_id' => $shop_id,
                        'product_id' => $param['id'],
                        'inventory_id' => $key,
                        'before_quantity' => 0,
                        'change_quantity' => $value,
                        'after_quantity' => $value,
                        'change_type' => 1, // 入库
                        'related_no' => 'MANU' . date('YmdHis') . mt_rand(1000, 9999),
                        'operator' => session('cn_accountinfo.accountname'),
                        'remark' => "手动添加库存：{$product_info['title']} - {$inventory_info['title']}，数量：{$value}",
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                    
                    $inventoryLogModel = new InventoryLog();
                    $inventoryLogModel->add($inventory_log);
                    
                    // 插入库存变更日志（旧方式，保留兼容）
                    $add_log["shop_id"] = $shop_id;
                    $add_log["product_id"] = $param['id'];
                    $add_log["spec_id"] = $key;
                    $add_log["new_stock"] = $value;
                    $add_log["old_stock"] = 0;
                    $add_log["time"] = date("Y-m-d H:i:s");
                    $add_log["operator"] = session('cn_accountinfo.accountname');
                    Db::table("shop_product_stock_log")->insert($add_log);
                    
                    // 准备入库单明细数据
                    if ($value > 0) {
                        $inventory_details[] = [
                            'product_id' => $param['id'],
                            'inventory_id' => $key,
                            'quantity' => $value,
                            'price' => $product_info['price'] ?? 0,
                            'discount' => 0,
                            'amount' => ($product_info['price'] ?? 0) * $value,
                            'product' => $product_info,
                            'inventory' => $inventory_info
                        ];
                    }
                    
                    addoperatelog('新增商品库存', json_encode($param, JSON_UNESCAPED_UNICODE));
                    continue;
                }
                
                if ($old_info['stock'] == $value) {
                    continue;
                }
                
                if ($value < 0) {
                    throw new \Exception("库存不能小于0");
                }
                
                // 计算库存变动量
                $change_quantity = $value - $old_info['stock'];
                
                // 记录库存日志（新方式）
                $inventory_log = [
                    'clientkeynum' => $clientkeynum,
                    'shop_id' => $shop_id,
                    'product_id' => $param['id'],
                    'inventory_id' => $key,
                    'before_quantity' => $old_info['stock'],
                    'change_quantity' => abs($change_quantity),
                    'after_quantity' => $value,
                    'change_type' => $change_quantity > 0 ? 1 : 2, // 1-入库 2-出库
                    'related_no' => 'MANU' . date('YmdHis') . mt_rand(1000, 9999),
                    'operator' => session('cn_accountinfo.accountname'),
                    'remark' => "手动调整库存：{$product_info['title']} - {$inventory_info['title']}，从{$old_info['stock']}调整为{$value}",
                    'created_at' => date('Y-m-d H:i:s'),
                ];
                
                $inventoryLogModel = new InventoryLog();
                $inventoryLogModel->add($inventory_log);
                
                // 插入库存变更日志（旧方式，保留兼容）
                $add_log["shop_id"] = $shop_id;
                $add_log["product_id"] = $old_info['product_id'];
                $add_log["spec_id"] = $old_info['inventory_id'];
                $add_log["new_stock"] = $value;
                $add_log["old_stock"] = $old_info['stock'];
                $add_log["time"] = date("Y-m-d H:i:s");
                $add_log["operator"] = session('cn_accountinfo.accountname');
                Db::table("shop_product_stock_log")->insert($add_log);
                
                // 准备入库单明细数据（如果是增加库存）
                if ($change_quantity > 0) {
                    $inventory_details[] = [
                        'product_id' => $param['id'],
                        'inventory_id' => $key,
                        'quantity' => $change_quantity,
                        'price' => $product_info['price'] ?? 0,
                        'discount' => 0,
                        'amount' => ($product_info['price'] ?? 0) * $change_quantity,
                        'product' => $product_info,
                        'inventory' => $inventory_info
                    ];
                }
                
                // 变更库存
                Db::table("shop_product_inventory")
                    ->where($where)
                    ->update(['stock' => $value, 'update_time' => date("Y-m-d H:i:s")]);
            }
            
            // 如果有库存增加，创建入库单
            if (!empty($inventory_details)) {
                $this->createInventoryInOrder($shop_id, $clientkeynum, 'MANU' . date('YmdHis'), $inventory_details);
            }
            
            addoperatelog('修改商品库存', json_encode($param, JSON_UNESCAPED_UNICODE));
            Db::commit();
            $rt["sta"] = 1;
            $rt["msg"] = "操作成功";
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        } catch (\Exception $e) {
            Db::rollback();
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败:" . $e->getMessage();
            echo json_encode($rt, JSON_UNESCAPED_UNICODE);
            die;
        }
    }
    
    /**
     * 创建入库单及明细
     * 
     * @param int $shop_id 商店ID
     * @param string $clientkeynum 客户端密钥
     * @param string $related_no 关联单号
     * @param array $inventory_details 入库明细数组
     * @return bool 创建结果
     */
    private function createInventoryInOrder($shop_id, $clientkeynum, $related_no, $inventory_details)
    {
        if (empty($inventory_details)) {
            return false;
        }
        
        // 创建入库单主表数据
        $inventoryOrderData = [
            'order_no' => 'II' . date('YmdHis') . mt_rand(1000, 9999), // 自动生成入库单号
            'clientkeynum' => $clientkeynum,
            'shop_id' => $shop_id,
            'order_type' => 1, // 1-入库
            'business_type' => 7, // 7-盘点入库
            'related_order_no' => $related_no, // 关联单号
            'status' => 2, // 已审核状态
            'remark' => "手动调整库存入库单",
            'created_by' => session('cn_accountinfo.accountname'),
            'reviewed_by' => session('cn_accountinfo.accountname'),
            'reviewed_time' => date('Y-m-d H:i:s'),
            'completed_time' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
        ];
        
        try {
            // 开启事务
            Db::startTrans();
            
            // 创建入库单主表记录
            $inventoryOrder = new InventoryOrder();
            $inventoryOrderResult = $inventoryOrder->save($inventoryOrderData);
            
            if (!$inventoryOrderResult) {
                throw new \Exception('创建入库单失败');
            }
            
            $total_amount = 0;
            $details = [];
            
            // 准备所有入库单明细数据
            foreach ($inventory_details as $detail) {
                $product = $detail['product'];
                $inventory = $detail['inventory'];
                
                $detail_data = [
                    'order_id' => $inventoryOrder->id,
                    'order_no' => $inventoryOrderData['order_no'],
                    'order_type' => 1, // 1-入库
                    'product_id' => $detail['product_id'],
                    'inventory_id' => $detail['inventory_id'],
                    'quantity' => $detail['quantity'],
                    'price' => $detail['price'],
                    'discount' => $detail['discount'],
                    'amount' => $detail['amount'],
                    'shop_id' => $shop_id,
                    'remark' => "商品：{$product['title']} - {$inventory['title']}，数量：{$detail['quantity']}",
                    'created_at' => date('Y-m-d H:i:s'),
                    'clientkeynum' => $clientkeynum,
                ];
                
                $details[] = $detail_data;
                $total_amount += $detail['amount'];
            }
            
            // 批量创建入库单明细
            $detailModel = new InventoryOrderDetail;
            $detailResult = $detailModel->saveAll($details);
            
            if (!$detailResult) {
                throw new \Exception('创建入库单明细失败');
            }
            
            // 更新入库单主表的总金额
            InventoryOrder::where('id', $inventoryOrder->id)->update([
                'total_amount' => $total_amount,
                'actual_amount' => $total_amount,
            ]);
            
            // 提交事务
            Db::commit();
            
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return false;
        }
    }
}
