<?php
/**
 * 食谱分类管理控制器
 * @date 2024-10-31
 */

namespace app\admin\controller;

use app\admin\model\RecipeCategory;
use think\facade\Request;

class RecipeCategoryController extends CnController
{
    /**
     * 分类列表页面
     * @return mixed
     */
    public function category_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/category_list', 0);
        
        return $this->fetch();
    }

    /**
     * 获取分类列表数据
     */
    public function ajax_category_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/category_list', 1);
        
        $page = Request::instance()->param('page', 1);
        $page_size = Request::instance()->param('page_size', 10);
        $name = Request::instance()->param('name', '');
        $status = Request::instance()->param('status', '');
        
        $where = [];
        
        if (!empty($name)) {
            $where['name'] = ['like', "%{$name}%"];
        }
        
        if ($status !== '') {
            $where['status'] = $status;
        }
        
        $list = RecipeCategory::getList($where, $page, $page_size);
        $count = RecipeCategory::getCount($where);
        
        // 获取父分类名称
        foreach ($list as &$item) {
            if ($item['parent_id'] > 0) {
                $parent = RecipeCategory::getInfoById($item['parent_id']);
                $item['parent_name'] = $parent ? $parent['name'] : '';
            } else {
                $item['parent_name'] = '顶级分类';
            }
        }
        
        success(0, '请求成功', $list, $count);
    }

    /**
     * 添加分类页面
     * @return mixed
     */
    public function add_category()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/category_list', 0);
        
        // 获取所有分类，用于选择父分类
        $categories = RecipeCategory::getAllCategories();
        $this->assign('categories', $categories);
        
        return $this->fetch();
    }

    /**
     * 添加分类处理
     */
    public function ajax_add_category()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/category_list', 1);
        
        $params = Request::instance()->param();
        
        $data = [
            'name' => $params['name'],
            'parent_id' => isset($params['parent_id']) ? $params['parent_id'] : 0,
            'sort' => isset($params['sort']) ? $params['sort'] : 0,
            'status' => isset($params['status']) ? $params['status'] : 1
        ];
        
        $res = RecipeCategory::add($data);
        if ($res) {
            success(0, '添加成功');
        } else {
            fail(-1, '添加失败');
        }
    }

    /**
     * 编辑分类页面
     * @return mixed
     */
    public function edit_category()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/category_list', 0);
        
        $id = Request::instance()->param('id');
        if (empty($id)) {
            error_tips('NOT FOUND');
        }
        
        $info = RecipeCategory::getInfoById($id);
        if (empty($info)) {
            error_tips('NOT FOUND');
        }
        
        $this->assign('info', $info);
        
        // 获取所有分类，用于选择父分类
        $categories = RecipeCategory::getAllCategories();
        // 过滤掉当前分类及其子分类，防止循环引用
        $filtered_categories = [];
        foreach ($categories as $category) {
            if ($category['id'] != $id) {
                $filtered_categories[] = $category;
            }
        }
        $this->assign('categories', $filtered_categories);
        
        return $this->fetch();
    }

    /**
     * 编辑分类处理
     */
    public function ajax_edit_category()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/category_list', 1);
        
        $id = Request::instance()->param('id');
        if (empty($id)) {
            fail(-1, 'NOT FOUND');
        }
        
        $params = Request::instance()->param();
        
        // 防止将自己设为自己的父级
        if (isset($params['parent_id']) && $params['parent_id'] == $id) {
            fail(-1, '不能将分类设为自己的父级');
        }
        
        $data = [
            'name' => $params['name'],
            'parent_id' => isset($params['parent_id']) ? $params['parent_id'] : 0,
            'sort' => isset($params['sort']) ? $params['sort'] : 0,
            'status' => isset($params['status']) ? $params['status'] : 1,
            'update_time' => date('Y-m-d H:i:s')
        ];
        
        $where = ['id' => $id];
        
        if (RecipeCategory::edit($where, $data)) {
            success(0, '编辑成功');
        } else {
            fail(-1, '编辑失败');
        }
    }

    /**
     * 删除分类
     */
    public function del_category()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/category_list', 1);
        
        $id = Request::instance()->param('id');
        if (empty($id)) {
            fail(-1, '请选择要删除的分类');
        }
        
        // 检查是否有子分类
        $child_count = RecipeCategory::where('parent_id', $id)->count();
        if ($child_count > 0) {
            fail(-1, '该分类下有子分类，不能删除');
        }
        
        // 检查是否有关联的食谱
        $recipe_count = \app\admin\model\Recipe::where('category_id', $id)->count();
        if ($recipe_count > 0) {
            fail(-1, '该分类下有食谱，不能删除');
        }
        
        if (RecipeCategory::del($id)) {
            success(0, '删除成功');
        } else {
            fail(-1, '删除失败');
        }
    }

    /**
     * 获取分类树形结构
     */
    public function ajax_category_tree()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/category_list', 1);
        
        $tree = RecipeCategory::getCategoryTree();
        
        success(0, '请求成功', $tree);
    }
} 