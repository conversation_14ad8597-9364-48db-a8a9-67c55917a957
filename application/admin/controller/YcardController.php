<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright ( c ) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use app\admin\model\CardCustomer;
use app\admin\model\CardDelayLog;
use app\admin\model\CardSale;
use app\admin\model\CardStatusChangeLogModel;
use app\admin\model\CardStatusChange;
use app\admin\model\CardType;
use app\admin\model\CardTypeChangeLog;
use app\admin\model\CardTypeChangeDetailLog;
use app\admin\model\ClientCardUseLog;
use app\admin\model\ClientYcardModel;
use app\admin\model\ClientYcardPucardModel;
use Spatie\SimpleExcel\SimpleExcelWriter;
use think\Exception;
use think\facade\Log;
use think\facade\Session;
use think\facade\Request;
use think\Db;
use think\facade\Env;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelLow;
use Endroid\QrCode\Writer\PngWriter;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\RoundBlockSizeMode\RoundBlockSizeModeMargin;


class YcardController extends CnController
{
    /**
     * 初始化方法
     */

    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    /**
     * 用户绑定的卡号列表
     * @return mixed
     */
    public function bind_card_list()
    {
        check_auth('Member/member_list', 0);
        $member_id = Request::instance()->param('memberid');
        $this->assign('member_id', $member_id);
        return $this->fetch();
    }

    public function ajax_bind_card_list()
    {
        check_auth('Member/member_list', 1);
        $member_id = Request::instance()->param('member_id');
        $page = Request::instance()->param('page', 1);
        $page_size = Request::instance()->param('page_size', 10);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $where = ['clientkeynum' => $basekeynum];
        $list = ClientYcardModel::getMemberBindCardList($member_id, $where, $page, $page_size);
        $count = ClientYcardModel::getMemberBindCardCount($member_id, $where);
        success(0, '请求成功', $list, $count);
    }

    /**
     * 添加预览信息
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function ajax_add_preview_info()
    {
        check_auth(request()->controller() . '/cardlist', 1);
        $cardnum = Request::instance()->param('cardnum');
        $type = Request::instance()->param('type');
        if (empty($cardnum)) fail(-1, '请输入卡号');
        $basekeynum = session('cn_accountinfo.basekeynum');
        $where = ['clientkeynum' => $basekeynum];
        Log::error('cardnum' . $cardnum);
        $cardnum = explode("\n", $cardnum);
        $customer_id = Request::instance()->param('customer_id');

        if (in_array($type, ['change_card_status', 'card_delay'])) {
            if (empty($customer_id)) fail(-1, '请选择客户');
            $where['customer_id'] = $customer_id;
        }

        //        if ($type == 'change_card_status') {
        //            $status = Request::instance()->param('status');
        //            $where['status'] = ['<>', ClientYcardModel::getRealStatus($status)];
        //            $where['status '] = ['not in', '1, -1'];
        //        }
        //
        //        if ($type == 'card_delay') {
        //            $where['status'] = 3;
        //        }


        $list = ClientYcardModel::getListByCardNumbers($cardnum, $where);

        if ($type == 'change_card_status' && !empty($list)) {
            $fail_list = [];
            foreach ($list as $v) {
                if (!CardStatusChange::checkStatus($v['status'], $type)) {
                    $fail_list[] = $v['cardnum'];
                }
            }
            if (!empty($fail_list)) fail(1, implode(',', $fail_list) . '卡号状态不可更改');
        }

        //        $diff_list = array_diff($cardnum, array_column($list, 'cardnum'));
        Log::error('list:' . json_encode($list));
        $msg = '请求成功';
        //        if (!empty($diff_list)) {
        //            $msg = '已过滤 ' . implode('-', $diff_list) . ' 不符合状态的卡号';
        //        }

        if ($list) {
            success(0, $msg, $list);
        }
        fail(-1, '未查询到卡号信息或当前查询卡号状态不正确');
    }


    /**
     * 导出卡号延期详情
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function export_card_delay_detail()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_delay', 0);
        $params = Request::instance()->param();
        $no = $params['no'];

        if (empty($no)) fail();

        $info = CardDelayLog::getInfoByNo($no);

        if (empty($info)) fail(-1, '数据不存在或已被删除');

        $list = ClientYcardModel::getListByCardNumbers(explode(',', $info['cardnum']));

        $export[] = array(
            '批次单号', '卡号', '客户', '销售', '延期时间', '状态', '操作时间'
        );

        $result = [];
        foreach ($list as $v) {
            $result[1] = $info["no"];
            $result[2] = $v["cardnum"];
            $result[3] = $info["operator"];
            $result[4] = $v["customer_name"];
            $result[5] = $info["delay_time"];
            $result[7] = $v["status"];
            $result[8] = $v["add_time"];
            $export[] = $result;
        }
        $xls_name = "卡号延期记录详情" . date('Y-m-d');

        // 使用SimpleExcelWriter导出
        try {
            SimpleExcelWriter::streamDownload($xls_name . '.xlsx')
                ->addRows($export)
                ->toBrowser();
        } catch (\Exception $e) {
            // 如果SimpleExcel不可用，使用原来的create_xls函数
            create_xls($export, $xls_name);
        }
    }

    /**
     * 导出卡号延期列表
     * @return void
     * @throws Exception
     */
    public function export_card_delay()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_delay', 0);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $params      = Request::instance()->param();
        $operator               = $params['operator'];
        $customer               = $params['customer'];
        $start_time             = $params['start_time'];
        $end_time               = $params['end_time'];

        $where['basekeynum']    = $basekeynum;

        if (!empty($operator)) {
            $where['operator'] = $operator;
        }

        if (!empty($customer)) {
            $customer_info = CardCustomer::getInfoByName($customer);
            if (!empty($customer_info)) {
                $where['customer_id'] = $customer_info['id'];
            }
        }

        if (!empty($start_time)) {
            $where[' add_time'] = ['>', $start_time];
        }

        if (!empty($end_time)) {
            $where['add_time'] = ['<', $end_time];
        }

        $count = CardDelayLog::getCount($where);
        if ($count > 10000) {
            error_tips("单次最大可导出1万条数据,请缩小搜索范围");
            die;
        }
        $export[] = array(
            '批次单号', '卡号', '操作人', '客户', '延期时间', '备注', '操作时间'
        );
        $export_list = CardDelayLog::getExportAll($where);
        if (empty($export_list)) fail(-1, '未查询到需要导出的数据');

        $result = [];
        foreach ($export_list as $v) {
            $result[1] = $v["no"];
            $result[2] = $v["cardnum"];
            $result[3] = $v["operator"];
            $result[4] = $v["customer_name"];
            $result[5] = $v["delay_time"];
            $result[7] = $v["remarks"];
            $result[8] = $v["add_time"];
            $export[] = $result;
        }
        $xls_name = "卡号延期记录" . date('Y-m-d');

        // 使用SimpleExcelWriter导出
        try {
            SimpleExcelWriter::streamDownload($xls_name . '.xlsx')
                ->addRows($export)
                ->toBrowser();
        } catch (\Exception $e) {
            // 如果SimpleExcel不可用，使用原来的create_xls函数
            create_xls($export, $xls_name);
        }
    }

    /**
     * 延期时间详情api
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function ajax_card_delay_detail()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_delay', 1);
        $params = Request::instance()->param();
        $no = $params['no'];

        if (empty($no)) fail();

        $info = CardDelayLog::getInfoByNo($no);

        if (empty($info)) fail(-1, '数据不存在或已被删除');

        $list = ClientYcardModel::getListByCardNumbers(explode(',', $info['cardnum']));

        foreach ($list as $k => $v) {
            $list[$k]['delay_time'] = $info['delay_time'];
        }

        success(0, '请求成功', $list, count($list));
    }

    /**
     * 卡延期详情
     * @return mixed
     */
    public function card_delay_detail()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_delay', 0);
        $params = Request::instance()->param();
        $no = $params['no'];
        if (empty($no)) fail();
        $this->assign('no', $no);
        return $this->fetch();
    }

    /**
     * 卡号延期api
     * @return void
     */
    public function ajax_add_card_delay()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_delay', 1);
        $params             = Request::instance()->param();
        $basekeynum         = session("cn_accountinfo.basekeynum");
        $operator           = session('cn_accountinfo.accountname');
        $delay_time         = $params['delay_time'];
        $card_nums          = $params['card_nums'];
        $remarks            = $params['remarks'];
        $customer_id        = $params['customer_id'];

        if (!empty($delay_time)) {
            $delay_time .= ' 23:59:59';
        }

        $where['clientkeynum'] = $basekeynum;
        $where['customer_id'] = $customer_id;

        $card_list = ClientYcardModel::getListByCardNumbers(explode(',', $card_nums), $where);
        // 预设数组 判断过期时间是否大于延期时间
        $fail_list = [];
        $status_fail_list = [];
        foreach ($card_list as $v) {
            if ($v['end_dui'] > $delay_time) {
                $fail_list[] = $v['cardnum'];
            }

            if (!in_array($v['status'], ['已销售', '已开卡'])) {
                $status_fail_list = $v['cardnum'];
            }
        }

        if (!empty($fail_list)) fail(1, '卡号 ' . implode(',', $fail_list) . ' 过期时间大于要延期的时间');

        if (!empty($status_fail_list)) fail(1, '卡号 ' . implode(',', $fail_list) . ' 的当前状态不可延期');

        try {
            Db::startTrans();
            $no = CardDelayLog::createNo();
            $data = [
                'basekeynum'    => $basekeynum,
                'no'            => $no,
                'cardnum'       => $card_nums,
                'operator'      => $operator,
                'customer_id'   => $customer_id,
                'delay_time'    => $delay_time,
                'remarks'       => $remarks,
                'add_time'      => date('Y-m-d H:i:s'),
            ];
            $res = CardDelayLog::add($data);
            if (!$res) throw new \Exception('新增延期记录失败');

            $update_res = ClientYcardModel::batchDelayCard($card_nums, $delay_time);
            Log::error($update_res);
            if (!$update_res) throw new \Exception('更改卡过期时间失败');

            Db::commit();
            success(0, '延期成功！');
        } catch (\Exception $e) {
            Db::rollback();
            Log::error($e->getMessage());
            Log::error('error file:' . $e->getFile() . ' line:' . $e->getLine());
            fail(-1, '请求失败');
        }
    }

    /**
     * 卡号延期预览数据 api
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function get_card_delay_preview()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_status_change_list', 0);
        $basekeynum             = session("cn_accountinfo.basekeynum");
        $customer_id        = $_POST['customer_id'];
        $sale_number        = $_POST['sale_number']; //销售单号
        $begin_cardnumber   = $_POST['begin_cardnumber'];
        $end_cardnumber     = $_POST['end_cardnumber'];

        // 拼接查询条件
        $where = [
            'clientkeynum'      => $basekeynum,
            'customer_id'       => $customer_id,
        ];
        if (!empty($sale_number)) {
            // 优先根据销售单号查询
            $pucard_info = ClientYcardPucardModel::getInfoByPiciNumber($sale_number, $where);
            if (empty($pucard_info)) fail(1, '未查询到销售信息');
            $card_list = ClientYcardModel::getListByPuId($pucard_info['id'], $where);


            if (!empty($card_list)) {
                success(0, '生成预览成功', $card_list);
            } else fail(1, '未查询到卡信息！');
        } else if (!empty($begin_cardnumber) && !empty($end_cardnumber)) {
            // 根据卡号范围查询
            $begin_cardnumber_arr = preg_split('@(\d+)@', $begin_cardnumber, -1, PREG_SPLIT_DELIM_CAPTURE);
            $begin_cardnumber_word = $begin_cardnumber_arr[0];
            $begin_cardnumber_number = $begin_cardnumber_arr[1];

            $end_cardnumber_arr = preg_split('@(\d+)@', $end_cardnumber, -1, PREG_SPLIT_DELIM_CAPTURE);
            $end_cardnumber_word = $end_cardnumber_arr[0];
            $end_cardnumber_number = $end_cardnumber_arr[1];
            if ($end_cardnumber_word != $begin_cardnumber_word) {
                fail(1, '开始卡号和结束卡号开头字母不一致');
            }
            if ($end_cardnumber_number < $begin_cardnumber_number) {
                fail(1, '结束卡号不可小于或等于开始卡号');
            }
            if ($end_cardnumber - $begin_cardnumber > 1000) {
                fail(1, '操作数据量太大，输入1000条数据以内的范围');
            }
            $cardnumber_arr = [];
            for ($i = $begin_cardnumber_number; $i <= $end_cardnumber_number; $i++) {
                $cardnumber_arr[] = $end_cardnumber_word . $this->dispRepair($i, strlen($begin_cardnumber_number), '0');
            }

            $card_list = ClientYcardModel::getListByCardNumbers($cardnumber_arr, $where);

            if (!empty($card_list)) {
                success(0, '生成预览成功', $card_list);
            }
        } else fail(1, '请求失败，请输入销售单号或输入卡号范围');
    }

    /**
     * 卡号延期view
     * @return mixed
     */
    public function add_card_delay()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_delay', 0);
        $customer_list = CardCustomer::getEnumByBaseKeyNum();
        $this->assign('customer_list', $customer_list);
        return $this->fetch();
    }

    /**
     * 卡号延期列表api
     * @return void
     */
    public function ajax_card_delay()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_delay', 1);
        $params = Request::instance()->param();
        $basekeynum = session("cn_accountinfo.basekeynum");
        $p                      = $params['page'] ?: 1;
        $pagesize               = $params['limit'];
        $start_time             = $params['start_time'];
        $end_time               = $params['end_time'];
        $field                  = $params['end_time'];
        $keyword                = $params['end_time'];

        $where['basekeynum']    = $basekeynum;

        if (!empty($start_time)) {
            $where[' add_time'] = ['>', $start_time];
        }

        if (!empty($end_time)) {
            $where['add_time'] = ['<', $end_time];
        }

        if (!empty($field) && !empty($keyword)) {
            if ($field == 'customer_id') {
                $where['customer_id'] = ['=', CardCustomer::where(['name' => $keyword])->value('id')];
            }
        }

        $list = CardDelayLog::getList($where, $p, $pagesize);
        $count = CardDelayLog::getCount($where);

        success(0, '请求成功', $list, $count);
    }

    /**
     * 卡号延期列表view
     * @return mixed
     */
    public function card_delay()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_delay', 0);
        return $this->fetch();
    }

    /**
     * 导出卡状态变更详细数据
     * @return void
     * @throws Exception
     */
    public function export_card_status_change_log()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_status_change_list', 1);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $param      = Request::instance()->param();
        $no      = $param['no'];


        $where['basekeynum'] = ['=', $basekeynum];
        $where['change_no']  = ['=', $no];

        $count = CardStatusChangeLogModel::getCount($where);
        if ($count > 1000) {
            error_tips("单次最大可导出1万条数据,请缩小搜索范围");
            die;
        }
        $export[] = array(
            '批次单号', '卡号', '操作人', '客户', '销售', '状态', '备注', '操作时间'
        );
        $export_list = CardStatusChangeLogModel::getExportAll($where);
        if (empty($export_list)) fail(-1, '未查询到需要导出的数据');

        $result = [];
        foreach ($export_list as $v) {
            $result[1] = $v["change_no"];
            $result[2] = $v["card_name"];
            $result[3] = $v["operator"];
            $result[4] = $v["customer_name"];
            $result[5] = $v["sale_name"];
            $result[6] = $v["type"];
            $result[7] = $v["remarks"];
            $result[8] = $v["add_time"];
            $export[] = $result;
        }
        $xls_name = "卡号状态详细记录" . date('Y-m-d');

        // 使用SimpleExcelWriter导出
        try {
            SimpleExcelWriter::streamDownload($xls_name . '.xlsx')
                ->addRows($export)
                ->toBrowser();
        } catch (\Exception $e) {
            // 如果SimpleExcel不可用，使用原来的create_xls函数
            create_xls($export, $xls_name);
        }
    }

    /**
     * 导出卡状态变更数据
     * @return void
     * @throws Exception
     */
    public function export_card_status_change()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_status_change_list', 1);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $param      = Request::instance()->param();
        $field      = $param['field'];
        $keyword    = $param['keyword'];
        $start_time = $param['start_time'];
        $end_time   = $param['end_time'];


        $where['basekeynum'] = ['=', $basekeynum];

        if (!empty($field)) {
            if ($field == 'custom') {
                $customer_info          = CardCustomer::getInfoByName($keyword);
                $where['customer_id']   = $customer_info['id'];
            } elseif ($field == 'market') {
                $sale_info              = CardSale::getInfoByName($keyword);
                $where['sale_id']       = $sale_info['id'];
            }
        }

        if (!empty($start_time)) {
            $where[' add_time'] = ['>', $start_time];
        }

        if (!empty($end_time)) {
            $where['add_time'] = ['<', $end_time];
        }

        $count = CardStatusChange::getCount($where);
        if ($count > 1000) {
            error_tips("单次最大可导出1万条数据,请缩小搜索范围");
            die;
        }
        $export[] = array(
            '批次单号', '操作人', '客户', '销售', '状态', '卡号', '备注', '操作时间'
        );
        $export_list = CardStatusChange::getExportAll($where);
        if (empty($export_list)) fail(-1, '未查询到需要导出的数据');

        $result = [];
        foreach ($export_list as $v) {
            $result[1] = $v["no"];
            $result[2] = $v["operator"];
            $result[3] = $v["customer_name"];
            $result[4] = $v["sale_name"];
            $result[5] = $v["status"];
            $result[6] = $v["cardnum"];
            $result[7] = $v["remarks"];
            $result[8] = $v["add_time"];
            $export[] = $result;
        }
        $xls_name = "卡号状态记录" . date('Y-m-d');

        // 使用SimpleExcelWriter导出
        try {
            SimpleExcelWriter::streamDownload($xls_name . '.xlsx')
                ->addRows($export)
                ->toBrowser();
        } catch (\Exception $e) {
            // 如果SimpleExcel不可用，使用原来的create_xls函数
            create_xls($export, $xls_name);
        }
    }

    /**
     * 卡状态变更view
     * @return mixed
     */
    public function card_status_change_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_status_change_list', 0);
        return $this->fetch();
    }

    /**
     * 卡状态变更api
     * @return void
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function ajax_card_status_change_list()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_status_change_list', 1);
        $param                  = Request::instance()->param();
        $p                      = $param['page'] ?: 1;
        $pagesize               = $param['limit'];
        $basekeynum             = session('cn_accountinfo.basekeynum');
        $where['basekeynum']    = $basekeynum;

        $field = $param['field'];
        $keyword = $param['keyword'];
        $start_time = $param['start_time'];
        $end_time = $param['end_time'];

        if (!empty($field)) {
            if ($field == 'custom') {
                $customer_info          = CardCustomer::getInfoByName($keyword);
                $where['customer_id']   = $customer_info['id'];
            } elseif ($field == 'market') {
                $sale_info              = CardSale::getInfoByName($keyword);
                $where['sale_id']       = $sale_info['id'];
            }
        }

        if (!empty($start_time)) {
            $where[' add_time'] = ['>', $start_time];
        }

        if (!empty($end_time)) {
            $where['add_time'] = ['<', $end_time];
        }

        $list = CardStatusChange::getList($where, $p, $pagesize);
        $count = CardStatusChange::getCount($where);
        success(0, '请求成功', $list, $count);
    }

    /**
     * 卡状态变更记录列表 view
     * @return mixed
     */
    public function card_status_change_detail()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_status_change_list', 0);
        $no = Request::instance()->param('no');
        if (empty($no)) {
            echo '无数据';
            die;
        }
        $this->assign('no', $no);


        return $this->fetch();
    }

    /**
     * 卡状态变更记录列表api
     * @return void
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function ajax_card_status_change_detail()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_status_change_list', 1);
        $param                  = Request::instance()->param();
        $p                      = $param['page'] ? $param['page'] : 1;
        $pagesize               = $param['limit'];
        $no                     = $param['no'];
        $basekeynum             = session('cn_accountinfo.basekeynum');
        $where['basekeynum']    = $basekeynum;
        $where['change_no']     = $no;
        $list = CardStatusChangeLogModel::getList($where, $p, $pagesize);
        $count = CardStatusChangeLogModel::getCount($where);
        success(0, '请求成功', $list, $count);
    }

    /**
     * 更改卡状态view
     * @return void
     */
    public function change_card_status()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_status_change_list', 0);
        $customer_list = CardCustomer::getEnumByBaseKeyNum();
        $this->assign('customer_list', $customer_list);
        return $this->fetch();
    }

    /**
     * 更改卡状态api
     * @return void
     */
    public function ajax_change_card_status()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_status_change_list', 1);
        $params         = Request::instance()->param();
        $card_nums      = $params['card_nums'];
        $customer_id    = $params['customer_id'];
        $remarks        = $params['remarks'];
        $title          = $params['title'];
        $operator       = session('cn_accountinfo.accountname');
        $basekeynum     = session('cn_accountinfo.basekeynum');
        $type           = $params['type'];

        $customer_info  = CardCustomer::getInfoById($customer_id);
        $sale_id        = $customer_info['sale_id'];


        // 判断当前卡号状态是否可以修改
        $card_num_arr = explode(',', $card_nums);
        $card_arr = ClientYcardModel::getListByCardNumbers($card_num_arr);

        $no = CardStatusChange::createNo();


        // 预设卡表更新id
        $update_ids = [];
        // 预设新增卡状态变化日志列表
        $change_status_insert_data = [];
        // 预设卡状态不可更新数据列表
        $fail_card_list = [];
        foreach ($card_arr as $k => $v) {

            if (!in_array($v['status'], ['已销售', '已开卡', '已关卡'])) {
                $fail_card_list[] = $v['cardnum'];
                unset($card_arr[$k]);
                continue;
            }
            $update_ids[] = $v['id'];

            $change_status_insert_data[] = [
                'basekeynum'    => $basekeynum,
                'card_id'       => $v['id'],
                'change_no'     => $no,
                'customer_id'   => $customer_id,
                'sale_id'       => $sale_id,
                'operator'      => $operator,
                'title'         => $title,
                'type'          => ClientYcardModel::getRealStatus($type),
                'remarks'       => $remarks,
                'add_time'      => date('Y-m-d H:i:s'),
            ];
        }

        // 有状态不对的数据，返回错误
        if (!empty($fail_card_list)) {
            Log::error('fail_card_list' . json_encode($fail_card_list));
            //            fail(1, '操作失败，' . implode('-', $fail_card_list) . ' 卡状态不正确！');
        }

        // 开启事务
        try {
            Db::startTrans();
            // 插入卡状态变更表
            $data = [
                'basekeynum'        => $basekeynum,
                'no'                => $no,
                'customer_id'       => $customer_id,
                'sale_id'           => $sale_id,
                'status'            => ClientYcardModel::getRealStatus($type),
                'cardnum'           => $card_nums,
                'operator'          => $operator,
                'remarks'           => $remarks,
                'add_time'          => date('Y-m-d H:i:s'),
            ];

            $id = CardStatusChange::add($data);

            if (!$id) throw new \Exception('记录卡状态更新失败');

            // 这里用db类 因为模型底层的批量更新会有事务提交
            $where[] = ['id','in', implode(',', $update_ids)];
            $change_status_result = DB::table('client_ycard')
                ->where($where)
                ->update(['status' => ClientYcardModel::getRealStatus($type)]);
            if (!$change_status_result) throw new \Exception('更新卡状态失败！');
            $change_status_insert_result = CardStatusChangeLogModel::batchAdd($change_status_insert_data);
            if (!$change_status_insert_result) throw new \Exception('记录卡状态更新失败！');
            DB::commit();
            success(0, '操作成功');
        } catch (\Exception $e) {
            DB::rollback();
            fail(1, $e->getMessage());
        }
    }

    /**
     * 更改卡状态预览数据 api
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function get_card_change_preview()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/card_status_change_list', 0);
        $basekeynum             = session("cn_accountinfo.basekeynum");
        $customer_id        = $_POST['customer_id'];
        $sale_number        = $_POST['sale_number']; //销售单号
        $begin_cardnumber   = $_POST['begin_cardnumber'];
        $end_cardnumber     = $_POST['end_cardnumber'];
        $type               = $_POST['type'];

        // 拼接查询条件
        $where = [
            'clientkeynum'      => $basekeynum,
            'customer_id'       => $customer_id,
        ];
        if (!empty($sale_number)) {
            // 优先根据销售单号查询
            $pucard_info = ClientYcardPucardModel::getInfoByPiciNumber($sale_number, $where);
            if (empty($pucard_info)) fail(1, '未查询到销售信息');
            $card_list = ClientYcardModel::getListByPuId($pucard_info['id'], $where);
            // 预设不可更改状态列表
            $fail_list = [];
            if (!empty($card_list)) {
                foreach ($card_list as $v) {
                    if (!CardStatusChange::checkStatus($v['status'], $type)) {
                        $fail_list[] = $v['cardnum'];
                    }
                }
                if (!empty($fail_list)) fail(1, implode(',', $fail_list) . '卡号状态不可更改');

                success(0, '生成预览成功', $card_list);
            } else fail(1, '未查询到卡信息！');
        } else if (!empty($begin_cardnumber) && !empty($end_cardnumber)) {
            // 根据卡号范围查询
            $begin_cardnumber_arr = preg_split('@(\d+)@', $begin_cardnumber, -1, PREG_SPLIT_DELIM_CAPTURE);
            $begin_cardnumber_word = $begin_cardnumber_arr[0];
            $begin_cardnumber_number = $begin_cardnumber_arr[1];

            $end_cardnumber_arr = preg_split('@(\d+)@', $end_cardnumber, -1, PREG_SPLIT_DELIM_CAPTURE);
            $end_cardnumber_word = $end_cardnumber_arr[0];
            $end_cardnumber_number = $end_cardnumber_arr[1];
            if ($end_cardnumber_word != $begin_cardnumber_word) {
                fail(1, '开始卡号和结束卡号开头字母不一致');
            }
            if ($end_cardnumber_number < $begin_cardnumber_number) {
                fail(1, '结束卡号不可小于或等于开始卡号');
            }
            if ($end_cardnumber - $begin_cardnumber > 1000) {
                fail(1, '操作数据量太大，输入1000条数据以内的范围');
            }
            $cardnumber_arr = [];
            for ($i = $begin_cardnumber_number; $i <= $end_cardnumber_number; $i++) {
                $cardnumber_arr[] = $end_cardnumber_word . $this->dispRepair($i, strlen($begin_cardnumber_number), '0');
            }

            $card_list = ClientYcardModel::getListByCardNumbers($cardnumber_arr, $where);

            // 预设不可更改状态列表
            $fail_list = [];
            if (!empty($card_list)) {
                foreach ($card_list as $v) {
                    if (!CardStatusChange::checkStatus($v['status'], $type)) {
                        $fail_list[] = $v['cardnum'];
                    }
                }
                if (!empty($fail_list)) fail(1, implode(',', $fail_list) . '卡号状态不可更改');
                success(0, '生成预览成功', $card_list);
            }
        } else fail(1, '请求失败，请输入销售单号或输入卡号范围');
    }


    /**
     * 销售员列表
     */

    public function sale_list()
    {

        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/sale_list', 1);
            //平台客户的keynum
            $basekeynum = session('cn_accountinfo.basekeynum');
            $keyword = isset($param['keyword']) ? $param['keyword'] : '';
            $order = isset($param['order']) ? $param['order'] : 'DESC';
            $where = " 1 = 1  and clientkeynum='$basekeynum' ";
            if ($keyword) {
                $where .= "and  name  like '%$keyword%' ";
            }
            $p = isset($param['page']) ? intval($param['page']) : '1';
            $pagesize = $param['limit'];
            #每页数量
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量
            $count = Db::table('card_sale')->where($where)->count();
            $list = Db::table('card_sale')->where($where)->order('id desc')->limit($offset . ',' . $pagesize)->select();
            $rtdata['data'] = $list;
            $rtdata['count'] = $count;
            $rtdata['code'] = 0;
            $rtdata['msg'] = '';
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/sale_list', 0);
            return $this->fetch('sale_list');
        }
    }

    public function add_sale()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/sale_list', 0);
        $request = Request::instance();
        $param = $request->param();
        $id = isset($param['id']) ? $param['id'] : '';
        if ($id) {
            $info = Db::table('card_sale')->where("id='$id'")->find();
            $this->assign('info', $info);
        }
        return $this->fetch('add_sale');
    }

    public function ajax_add_sale()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/sale_list', 1);
        $request = Request::instance();
        $param = $request->param();
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $param["id"];
        $data = $param;
        $data["clientkeynum"] = $basekeynum;
        if ($id) {
            $has = Db::table('card_sale')->where("name='" . $data['name'] . "' and clientkeynum='basekeynum' and id!='$id'")->value("id");
            if ($has) {
                $rt['sta'] = 0;
                $rt['msg'] = '名称重复!';
                echo json_encode($rt);
                die;
            }
            Db::table('card_sale')->where('id', $id)->update($data);
            $rt['sta'] = 1;
            $rt['msg'] = '修改成功!';
            echo json_encode($rt);
            addoperatelog('修改销售', json_encode($param, JSON_UNESCAPED_UNICODE));
            die;
        } else {
            $has = Db::table('card_sale')->where("name='" . $data['name'] . "' and clientkeynum='basekeynum'")->value("id");
            if ($has) {
                $rt['sta'] = 0;
                $rt['msg'] = '名称重复!';
                echo json_encode($rt);
                die;
            }
            Db::table('card_sale')->insert($data);
            $rt['sta'] = 1;
            $rt['msg'] = '新增成功!';
            echo json_encode($rt);
            addoperatelog('新增销售', json_encode($param, JSON_UNESCAPED_UNICODE));
            die;
        }
    }

    public function sale_del()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/sale_list', 1);
        $request = Request::instance();
        $param = $request->param();
        $id = isset($param['id']) ? $param['id'] : false;
        if ($id) {
            if (is_array($id)) {
                $map['id'] = array('in', $id);
            } else {
                $map = "id='$id'";
            }
            if (Db::table('card_sale')->where($map)->delete()) {
                $return_arr['sta'] = 1;
                $return_arr['msg'] = '操作成功';
                addoperatelog('删除销售', json_encode($param, JSON_UNESCAPED_UNICODE));
            } else {
                $return_arr['sta'] = 0;
                $return_arr['msg'] = '操作失败';
            }
        } else {
            $return_arr['sta'] = 0;
            $return_arr['msg'] = '操作失败';
        }
        echo json_encode($return_arr);
        die;
    }

    public function customer_list()
    {
        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/customer_list', 1);
            //平台客户的keynum
            $basekeynum = session('cn_accountinfo.basekeynum');
            $keyword = isset($param['keyword']) ? $param['keyword'] : '';
            $order = isset($param['order']) ? $param['order'] : 'DESC';
            $where = " 1 = 1  and clientkeynum='$basekeynum' ";
            if ($keyword) {
                $where .= "and  name  like '%$keyword%' ";
            }
            $p = isset($param['page']) ? intval($param['page']) : '1';
            $pagesize = $param['limit'];
            #每页数量
            $offset = $pagesize * ($p - 1);
            //计算记录偏移量
            $count = Db::table('card_customer')->where($where)->count();
            $list = Db::table('card_customer')->where($where)->order('id desc')->limit($offset . ',' . $pagesize)->select();
            foreach ($list as $key => &$value) {
                $value['sale_name'] = Db::table('card_sale')->where("id='" . $value["sale_id"] . "'")->value("name");
            }
            $rtdata['data'] = $list;
            $rtdata['count'] = $count;
            $rtdata['code'] = 0;
            $rtdata['msg'] = '';
            echo json_encode($rtdata);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/customer_list', 0);
            return $this->fetch('customer_list');
        }
    }

    public function customer_del()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/customer_list', 1);
        $request = Request::instance();
        $param = $request->param();
        $id = isset($param['id']) ? $param['id'] : false;
        if ($id) {
            if (is_array($id)) {
                $map['id'] = array('in', $id);
            } else {
                $map = "id='$id'";
            }
            if (Db::table('card_customer')->where($map)->delete()) {
                $return_arr['sta'] = 1;
                $return_arr['msg'] = '操作成功';
                addoperatelog('删除档案客户', json_encode($param, JSON_UNESCAPED_UNICODE));
            } else {
                $return_arr['sta'] = 0;
                $return_arr['msg'] = '操作失败';
            }
        } else {
            $return_arr['sta'] = 0;
            $return_arr['msg'] = '操作失败';
        }
        echo json_encode($return_arr);
        die;
    }

    public function add_customer()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/customer_list', 0);
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = isset($param['id']) ? $param['id'] : '';
        if ($id) {
            $info = Db::table('card_customer')->where("id='$id'")->find();
            $this->assign('info', $info);
        }
        // 获取一下销售列表
        $sale_list = Db::table('card_sale')->where("clientkeynum='$basekeynum'")
            ->order('id desc')->field("id,name")->select();
        $this->assign("sale_list", $sale_list);
        return $this->fetch('add_customer');
    }

    public function ajax_add_customer()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/customer_list', 1);
        $request = Request::instance();
        $param = $request->param();
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $param["id"];
        $data = $param;
        $data["clientkeynum"] = $basekeynum;
        if ($id) {
            $has = Db::table('card_customer')->where("name='" . $data['name'] . "' and clientkeynum='basekeynum' and id!='$id'")->value("id");
            if ($has) {
                $rt['sta'] = 0;
                $rt['msg'] = '名称重复!';
                echo json_encode($rt);
                die;
            }
            Db::table('card_customer')->where('id', $id)->update($data);
            $rt['sta'] = 1;
            $rt['msg'] = '修改成功!';
            echo json_encode($rt);
            addoperatelog('修改档案客户', json_encode($param, JSON_UNESCAPED_UNICODE));
            die;
        } else {
            $has = Db::table('card_customer')->where("name='" . $data['name'] . "' and clientkeynum='basekeynum'")->value("id");
            if ($has) {
                $rt['sta'] = 0;
                $rt['msg'] = '名称重复!';
                echo json_encode($rt);
                die;
            }
            Db::table('card_customer')->insert($data);
            $rt['sta'] = 1;
            $rt['msg'] = '新增成功!';
            echo json_encode($rt);
            addoperatelog('新增档案客户', json_encode($param, JSON_UNESCAPED_UNICODE));
            die;
        }
    }

    public function cardtype_list()
    {
        $request = Request::instance();
        $param = $request->param();
        if (!empty($param['ajax']) && $param['ajax'] == 'ajax') {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/cardtype_list', 1);
            //平台客户的keynum
            $basekeynum             = session('cn_accountinfo.basekeynum');
            $keyword                = isset($param['keyword']) ? $param['keyword'] : '';
            $order                  = isset($param['order']) ? $param['order'] : 'DESC';
            $where['clientkeynum']  = ['=', $basekeynum];
            $p                      = isset($param['page']) ? intval($param['page']) : '1';
            $pagesize               = $param['limit'];
            if ($keyword) {
                $where['name']      = ['like', "%$keyword%"];
            }
            //计算记录偏移量
            $count = CardType::getCount($where);
            $list = CardType::getList($where, $p, $pagesize);
            success(0, '', $list, $count);
        } else {
            //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
            check_auth(request()->controller() . '/cardtype_list', 0);
            return $this->fetch('cardtype_list');
        }
    }

    public function cardtype_del()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/cardtype_list', 1);
        $request = Request::instance();
        $param = $request->param();
        $id = isset($param['id']) ? $param['id'] : false;
        if ($id) {
            if (is_array($id)) {
                $map['id'] = array('in', $id);
            } else {
                $map = "id='$id'";
            }
            if (Db::table('client_card_type')->where($map)->delete()) {
                $return_arr['sta'] = 1;
                $return_arr['msg'] = '操作成功';
                addoperatelog('删除档案卡型', json_encode($param, JSON_UNESCAPED_UNICODE));
            } else {
                $return_arr['sta'] = 0;
                $return_arr['msg'] = '操作失败';
            }
        } else {
            $return_arr['sta'] = 0;
            $return_arr['msg'] = '操作失败';
        }
        echo json_encode($return_arr);
        die;
    }


    public function add_cardtype()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/cardtype_list', 0);
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = isset($param['id']) ? $param['id'] : '';
        if ($id) {
            $info = Db::table('client_card_type')->where("id='$id'")->find();
            $this->assign('info', $info);
        }
        return $this->fetch('add_cardtype');
    }

    public function ajax_add_cardtype()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/cardtype_list', 1);
        $request = Request::instance();
        $param = $request->param();
        //平台客户的keynum
        $basekeynum = session('cn_accountinfo.basekeynum');
        $id = $param["id"];
        $data = $param;
        $data["clientkeynum"] = $basekeynum;
        if ($id) {
            $has = Db::table('client_card_type')->where("name='" . $data['name'] . "' and clientkeynum='basekeynum' and id!='$id'")->value("id");
            if ($has) {
                $rt['sta'] = 0;
                $rt['msg'] = '名称重复!';
                echo json_encode($rt);
                die;
            }
            Db::table('client_card_type')->where('id', $id)->update($data);
            $rt['sta'] = 1;
            $rt['msg'] = '修改成功!';
            echo json_encode($rt);
            addoperatelog('修改档案卡型', json_encode($param, JSON_UNESCAPED_UNICODE));
            die;
        } else {
            $has = Db::table('client_card_type')->where("name='" . $data['name'] . "' and clientkeynum='basekeynum'")->value("id");
            if ($has) {
                $rt['sta'] = 0;
                $rt['msg'] = '名称重复!';
                echo json_encode($rt);
                die;
            }
            Db::table('client_card_type')->insert($data);
            $rt['sta'] = 1;
            $rt['msg'] = '新增成功!';
            echo json_encode($rt);
            addoperatelog('新增档案卡型', json_encode($param, JSON_UNESCAPED_UNICODE));
            die;
        }
    }

    public function cardlist()
    {
        $request = Request::instance();
        $keynum = session("cn_accountinfo.basekeynum");
        $param = $request->param();
        if (!empty($param["ajax"]) && $param["ajax"] == "ajax") {
            //权限校验,第二个参数是否ajax,1是ajax，0不是ajax
            check_auth(request()->controller() . '/cardlist', 1);
            $piciname = isset($param['keyword']) ? $param['keyword'] : '';
            $where = " piciname  like '%$piciname%' and  clientkeynum='$keynum'";
            $p = isset($param['page']) ? intval($param['page']) : '1';
            $pagesize = $param["limit"]; #每页数量
            $offset = $pagesize * ($p - 1); //计算记录偏移量
            $count = Db::table('client_ycard_batch')->where($where)->count();
            $list = Db::table('client_ycard_batch')->where($where)->order("id desc")->limit($offset . ',' . $pagesize)->select();
            $rtdata["data"] = $list;
            $rtdata["count"] = $count;
            $rtdata["code"] = 0;
            $rtdata["msg"] = "";
            echo json_encode($rtdata);
        } else {
            //权限校验,第二个参数是否ajax,1是ajax，0不是ajax
            check_auth(request()->controller() . '/cardlist', 0);
            return $this->fetch('cardlist');
        }
    }

    //新建批次
    public function ajax_add_card()
    {
        //权限校验,第二个参数是否ajax,1是ajax，0不是ajax
        check_auth(request()->controller() . '/cardlist', 1);
        $request = Request::instance();
        $param = $request->param();
        // print_r($param);die;
        $cardnumberlist = array_filter(explode(',', $param['cardlist']));
        $count = count($cardnumberlist);
        $keynum = session("cn_accountinfo.basekeynum");
        //echo $count;die;
        $add_card_batch['time'] = time();
        $add_card_batch['piciname'] = $param['piciname'];
        $add_card_batch['content'] = $param['content'];
        $add_card_batch['card_num'] = $count;
        $add_card_batch['clientkeynum'] = $keynum;
        $add_card_batch['name'] = session::get("cn_accountinfo.accountname");
        $if_have_password = explode("(", $cardnumberlist[0]);
        if ($if_have_password[1]) {
            $add_card_batch['begin_cardnumber'] = $if_have_password[0];
        } else {
            $add_card_batch['begin_cardnumber'] = $cardnumberlist[0];
        }
        $if_have_password = explode("(", end($cardnumberlist));
        if ($if_have_password[1]) {
            $add_card_batch['end_cardnumber'] = $if_have_password[0];
        } else {
            $add_card_batch['end_cardnumber'] = end($cardnumberlist);
        }
        $find = Db::table('client_ycard_batch')
            ->where("begin_cardnumber='" . $cardnumberlist[0] . "' and end_cardnumber='" . end($cardnumberlist) . "' ")
            ->find();
        if ($find) {
            $msg['sta'] = 0;
            $msg['msg'] = "请勿生成重复卡号";
            echo json_encode($msg);
            die;
        }
        $batch_id = Db::table('client_ycard_batch')->insertGetId($add_card_batch);

        if ($batch_id) {
            foreach ($cardnumberlist as $key => $value) {
                $if_have_password = explode("(", $value);
                if ($if_have_password[1]) {
                    $cardnumber = $if_have_password[0];
                } else {
                    $cardnumber = $value;
                }
                if ($if_have_password[1]) {
                    $password = explode(")", $if_have_password[1])[0];
                } else {
                    $password = mt_rand(100000, 999999);
                }
                // $qrcode = $this->generateQrCode($cardnumber . '-' . $password);
                Log::info($password);
                $piciname = $param['piciname'];
                $add_cardnumber['cardnum'] = $cardnumber;
                $add_cardnumber['cardpwd'] = encrypt($password);
                // $add_cardnumber['qrcode'] = $qrcode['qrcode_url'];
                $add_cardnumber['status'] = 5;
                $add_cardnumber['add_time'] = time();
                $add_cardnumber['piciname'] = $piciname;
                $add_cardnumber['batch_id'] = $batch_id;
                $add_cardnumber['is_del'] = 1;
                $add_cardnumber['clientkeynum'] = $keynum;
                $add_cardnumber['code'] = randCode(32);
                // 默认不生成qrcode让用户自己操作生成qrcode
                //                $add_cardnumber['qrcode'] = create_wx_qrcode($add_cardnumber['code'], $cardnumber);
                Db::table('client_ycard')->insert($add_cardnumber);
                $add_ycard_log["cardnum"] = $cardnumber;
                $add_ycard_log["action"] = "导入生成卡号";
                $add_ycard_log["operator"] = session('cn_accountinfo.accountname');
                $add_ycard_log["operator_time"] = time();
                $add_ycard_log["content"] = "导入生成卡号";
                $add_ycard_log["clientkeynum"] = $keynum;
                Db::table('client_ycard_log')->insert($add_ycard_log);
            }
        }
        $msg['sta'] = 1;
        $msg['msg'] = "操作成功";
        echo json_encode($msg);
        die;
    }


    /**
     * 生成二维码并获取相关信息
     * @param string $content 二维码内容
     * @return array 返回二维码相关信息
     */
    private function generateQrCode($content)
    {
        $writer = new PngWriter();

        // 创建QR码
        $qrCode = QrCode::create($content)
            ->setEncoding(new Encoding('UTF-8'))
            ->setErrorCorrectionLevel(new ErrorCorrectionLevelLow())
            ->setSize(300)
            ->setMargin(10)
            ->setRoundBlockSizeMode(new RoundBlockSizeModeMargin())
            ->setForegroundColor(new Color(0, 0, 0))
            ->setBackgroundColor(new Color(255, 255, 255));

        $result = $writer->write($qrCode);

        // 生成文件路径
        $uri = "public/uploads/qrcode/{$content}.png";
        $result->saveToFile(Env::get('root_path').$uri);

        // 上传到阿里云OSS
        $ossResult = uploadFileToAliOss(Env::get('ROOT_PATH') . $uri, $uri);

        // 获取二维码的base64编码
        $base64 = base64_encode(file_get_contents(Env::get('root_path').$uri));

        return [
            'qrcode_url' => $ossResult['url'],
            'qrcode_value' => $base64,
            'qrcode_expire_time' => date('Y-m-d H:i:s', time() + 300) // 5分钟后过期
        ];
    }

    public function ajax_add_card1()
    {
        //权限校验,第二个参数是否ajax,1是ajax，0不是ajax
        check_auth(request()->controller() . '/cardlist', 1);
        $request = Request::instance();
        $param = $request->param();
        $keynum = session("cn_accountinfo.basekeynum");
        $piciname = $param['piciname'];
        $start_cardnum = $param['start_cardnum'];
        $number = $param['number'];
        $content = $param['content'];
        // 根据输入的开始卡号和数量，生成一个卡号列表，逗号拼接形式的
        $cardnumberlist = $this->create_cardnumber($start_cardnum, $number);
        // 帮我生成函数 create_cardnumber
        $count = count($cardnumberlist);
        // 开启事务
        Db::startTrans();
        try {
            $add_card_batch['time'] = time();
            $add_card_batch['piciname'] = $param['piciname'];
            $add_card_batch['content'] = $param['content'];
            $add_card_batch['card_num'] = $count;
            $add_card_batch['clientkeynum'] = $keynum;
            $add_card_batch['name'] = session::get("cn_accountinfo.accountname");
            $add_card_batch['begin_cardnumber'] = $cardnumberlist[0];
            $add_card_batch['end_cardnumber'] = end($cardnumberlist);
            $find = Db::table('client_ycard_batch')
            ->where("begin_cardnumber='" . $cardnumberlist[0] . "' and end_cardnumber='" . end($cardnumberlist) . "' ")
            ->find();
            if ($find) {
                $msg['sta'] = 0;
                $msg['msg'] = "请勿生成重复卡号";
                echo json_encode($msg);
                die;
            }
            // 插入批次数据
            $batch_id = Db::table('client_ycard_batch')->fetchSql(false)->insertGetId($add_card_batch);
            if (!$batch_id) {
                throw new \Exception("添加卡号批次失败");
            }

            // 输出 SQL 语句
            $sql = Db::table('client_ycard_batch')->getLastSql();

            // 检查插入是否成功
            $batch_inserted = Db::table('client_ycard_batch')->where('id', $batch_id)->find();
            if (empty($batch_inserted)) {
                throw new \Exception("插入批次数据失败");
            }
            foreach ($cardnumberlist as $key => $value) {
                $cardnumber = $value;
                $password = rand(100000, 999999);
                // $qrcode = $this->generateQrCode($value . '-' . $password);
                $piciname = $param['piciname'];
                $add_cardnumber = array();
                $add_cardnumber['cardnum'] = $cardnumber;
                $add_cardnumber['cardpwd'] = encrypt(strval($password));
                $add_cardnumber['status'] = 5;
                // $add_cardnumber['qrcode'] = $qrcode['qrcode_url'];
                $add_cardnumber['add_time'] = time();
                $add_cardnumber['piciname'] = $piciname;
                $add_cardnumber['batch_id'] = $batch_id;
                $add_cardnumber['is_del'] = 1;
                $add_cardnumber['clientkeynum'] = $keynum;
                $add_cardnumber['code'] = randCode(32);
                $rs = Db::table('client_ycard')->insert($add_cardnumber);
                if (!$rs) {
                    throw new \Exception("添加卡号失败");
                }
                $add_ycard_log = array();
                $add_ycard_log["cardnum"] = $cardnumber;
                $add_ycard_log["action"] = "生成卡号";
                $add_ycard_log["operator"] = session('cn_accountinfo.accountname');
                $add_ycard_log["operator_time"] = time();
                $add_ycard_log["content"] = "生成卡号";
                $add_ycard_log["clientkeynum"] = $keynum;
                // print_r($add_ycard_log);die;
                $rs = Db::table('client_ycard_log')->insert($add_ycard_log);
                if (!$rs) {
                    throw new \Exception("添加卡号日志失败");
                }
            }

            // 检查所有操作是否成功
            $batch_data = Db::table('client_ycard_batch')->where('id', $batch_id)->find();
            if (empty($batch_data)) {
                throw new \Exception("批次数据未插入成功");
            }

            $card_data_count = Db::table('client_ycard')->where('batch_id', $batch_id)->count();
            if ($card_data_count !== $count) {
                throw new \Exception("卡号数据插入不完整");
            }

            Db::commit();
            $msg['sta'] = 1;
            $msg['msg'] = "操作成功";
            echo json_encode($msg);
            die;
        }
        catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg['sta'] = 0;
            $msg['msg'] = "操作失败" . $e->getMessage();
            echo json_encode($msg);
            die;
        }
    }
    /**
     * 根据输入的开始卡号和数量，生成一个卡号列表
     *
     * @param string $start_cardnum 起始卡号
     * @param int $number 卡号数量
     * @return array 卡号列表
     */
    private function create_cardnumber($start_cardnum, $number)
    {
        $cardnumbers = [];
        $current_cardnum = $start_cardnum;

        // 先将起始卡号加入数组
        $cardnumbers[] = $current_cardnum;

        // 从 start_cardnum + 1 开始递增
        for ($i = 1; $i < $number; $i++) {
            $current_cardnum++;
            $cardnumbers[] = $current_cardnum;
        }

        return $cardnumbers;
    }
    //excel上传
    public function ajax_add_cardbatch()
    {
        // 定义路径
        $rootPath = Env::get('root_path');
        ini_set("memory_limit", "368M");
        set_time_limit(3600);
        //引入文件
        // \think\Loader::import('PHPExcel.PHPExcel');
        $objPHPExcel = new \PHPExcel();
        //获取表单上传文件
        $file = request()->file('file');
        $path = $rootPath . 'public' . DIRECTORY_SEPARATOR  . 'uploads' . DIRECTORY_SEPARATOR  . 'excel';
        $info = $file->move($path);
        $keynum = session("cn_accountinfo.basekeynum");
        //数据为空返回错误
        if (empty($info)) {
            $msg['sta'] = -1;
            $msg['msg'] = "导入失败";
            echo json_encode($msg);
            die;
        }
        //获取文件名
        $exclePath = $info->getSaveName();
        //上传文件的地址
        $filename = $rootPath . 'public' . DIRECTORY_SEPARATOR  . 'uploads' . DIRECTORY_SEPARATOR  . 'excel' . DIRECTORY_SEPARATOR  . $exclePath;
        $arr = excelToArray($filename);
        foreach ($arr as $key => $value) {
            if ($value[0] == '') {
                unset($arr[$key]);
            }
        }
        $arr_len = count($arr, 1);
        if ($arr_len == '0') {
            $msg['sta'] = '0';
            $msg['msg'] = "excel为空";
            echo json_encode($msg);
            die;
        }
        $error = 0;
        $errormsg = "";
        foreach ($arr as $key => $value) {
            $cardnum = $value['0'];
            $find = DB::table('client_ycard')->where("cardnum='$cardnum' and clientkeynum='$keynum'")->find();
            if ($find) {
                $error++;
                $errormsg .= $cardnum . "重复,请重新填写 ";
            } else {
                if ($value[1]) {
                    $list[] = $cardnum . "(" . $value[1] . ")";
                } else {
                    $list[] = $cardnum;
                }
            }
        }
        $count = count($list);
        $newlist = array_slice($list, rand_z($count - 5), 5);
        if ($error > 0) {
            $msg['sta'] = 0;
            $msg['msg'] = $errormsg;
            $msg['list'] = $newlist;
            $msg['all_cardlist'] = $list;
            $msg['count'] = $count;
            echo json_encode($msg);
            die;
        } else {
            $msg['sta'] = 1;
            $msg['msg'] = "生成成功";
            $msg['list'] = $newlist;
            $msg['all_cardlist'] = $list;
            $msg['count'] = $count;
            echo json_encode($msg);
            die;
        }
    }
    //导入批次卡页面  下载卡号
    public function download_cardbatch()
    {
        $id = $_GET['id'];
        $admin_password = $_GET['admin_password'] ?? '';

        // 验证密码
        if (empty($admin_password)) {
            echo '请先验证高级密码';
            return;
        }

        // 再次验证密码是否正确
        $basekeynum = session('cn_accountinfo.basekeynum');
        $batch_info = Db::table('client_ycard_batch')
            ->where(['id' => $id, 'clientkeynum' => $basekeynum])
            ->find();

        if (empty($batch_info)) {
            echo '批次信息不存在';
            return;
        }

        // 获取当前登录管理员信息
        $adminId = session('cn_accountinfo.keynum');
        $adminName = session('cn_accountinfo.accountname');
        $clientInfo = \think\Db::name('plat_client')->where('keynum', $adminId)->find();

        if (!$clientInfo) {
            return json(['code' => 1, 'msg' => '平台信息不存在']);
        }

        // 验证密码
        if (!password_verify($admin_password, $clientInfo['super_password'])) {
            \think\facade\Log::error('管理员[' . $adminName . ']导出卡密批次时高级密码验证失败');
            return json(['code' => 1, 'msg' => '高级密码验证失败']);
        }

        $cardnumber_list = Db::table('client_ycard')->where("batch_id='$id'")->select();
        $batchinfo = Db::table('client_ycard_batch')->where("id='$id'")->find();
        $xls_name = $batchinfo['begin_cardnumber'] . "-" . $batchinfo["end_cardnumber"] . ".xlsx";

        $rows = [];
        foreach ($cardnumber_list as $key => $value) {
            $rows[] = [
                $value['cardnum'],
                decrypt($value['cardpwd']),
                $value['cardnum'] . '-' . decrypt($value['cardpwd']),
                ycard_status($value['status'])
            ];
        }

        // https://github.com/spatie/simple-excel 文档
        SimpleExcelWriter::streamDownload($xls_name)
            ->noHeaderRow()
            ->addRow(['卡号', '卡密', '二维码内容', '状态'])
            ->addRows($rows)
            ->toBrowser();
//        $this->download_cardbatch_2($cardnumber_list, $xls_name);
    }

    public function download_cardbatch_2($cardnumber_list, $filename)
    {
        $daochu = array();
        foreach ($cardnumber_list as $key => $value) {
            foreach ($value as $k => $v) {
                switch ($k) {
                    case "cardnum":
                        $r[0] = $value["cardnum"];
                        break;
                    case "cardpwd":
                        $password = decrypt($value['cardpwd']);
                        $r[1] = $password;
                        break;
                    case "status":
                        $r[2] = ycard_status($value['status']);
                        break;
                        // case "qrcode":
                        //     $r[3] = "." . $value['qrcode'];
                        //     break;
                    default:
                        break;
                }
            }
            ksort($r);
            $daochu[] = $r;
        }
        $daochu = array_filter($daochu);
        outExcel($daochu, $filename, 0);
    }

    /**
     * 下载批次二维码文件
     * @return void
     */
    public function download_cardbatch_qrcode()
    {
        $id = $_GET['id'];
        $admin_password = $_GET['admin_password'] ?? '';

        // 验证密码
        if (empty($admin_password)) {
            echo '请先验证高级密码';
            return;
        }

        // 再次验证密码是否正确
        $basekeynum = session('cn_accountinfo.basekeynum');
        $batch_info = Db::table('client_ycard_batch')
            ->where(['id' => $id, 'clientkeynum' => $basekeynum])
            ->find();

        if (empty($batch_info)) {
            echo '批次信息不存在';
            return;
        }

        // 验证密码
        $system_password = config('admin.download_password', 'admin123'); // 默认密码
        $admin_info = session('cn_accountinfo');
        if (isset($admin_info['download_password']) && !empty($admin_info['download_password'])) {
            $system_password = $admin_info['download_password'];
        }

        if ($admin_password !== $system_password) {
            echo '密码验证失败';
            return;
        }

        $cardnumber_list = Db::table('client_ycard')->where("batch_id='$id'")->select();
        $batchinfo = Db::table('client_ycard_batch')->where("id='$id'")->find();

        if (empty($cardnumber_list)) {
            echo '未找到卡号数据';
            return;
        }

        $zip_filename = $batchinfo['begin_cardnumber'] . "-" . $batchinfo["end_cardnumber"] . "_qrcode.zip";

        // 创建临时目录存放二维码文件
        $temp_dir = Env::get('root_path') . 'runtime/temp/qrcode_' . time();
        if (!is_dir($temp_dir)) {
            mkdir($temp_dir, 0777, true);
        }

        $files = [];
        foreach ($cardnumber_list as $key => $value) {
            if (!empty($value['qrcode'])) {
                // 如果二维码URL存在，尝试下载到临时目录
                $qrcode_content = file_get_contents($value['qrcode']);
                if ($qrcode_content !== false) {
                    $qrcode_filename = $value['cardnum'] . '.png';
                    $qrcode_path = $temp_dir . '/' . $qrcode_filename;
                    file_put_contents($qrcode_path, $qrcode_content);
                    $files[] = $qrcode_path;
                }
            }
        }

        if (empty($files)) {
            echo '未找到二维码文件';
            return;
        }

        // 创建ZIP文件并下载
        $this->createZipAndDownload($files, $zip_filename);

        // 清理临时文件
        foreach ($files as $file) {
            if (file_exists($file)) {
                unlink($file);
            }
        }
        rmdir($temp_dir);
    }

    /**
     * 创建ZIP文件并下载
     * @param array $files 文件路径数组
     * @param string $zip_filename ZIP文件名
     */
    private function createZipAndDownload($files, $zip_filename)
    {
        $zip = new \ZipArchive();
        $zip_path = Env::get('root_path') . 'runtime/temp/' . $zip_filename;

        if ($zip->open($zip_path, \ZipArchive::CREATE) !== TRUE) {
            echo "无法创建ZIP文件";
            return;
        }

        foreach ($files as $file) {
            if (file_exists($file)) {
                $zip->addFile($file, basename($file));
            }
        }

        $zip->close();

        // 下载文件
        if (file_exists($zip_path)) {
            header('Content-Type: application/zip');
            header('Content-Disposition: attachment; filename="' . $zip_filename . '"');
            header('Content-Length: ' . filesize($zip_path));
            readfile($zip_path);
            unlink($zip_path); // 删除临时ZIP文件
        }
    }

    //卡号状态查询
    public function cardstatus()
    {
        //权限校验,第二个参数是否ajax,1是ajax，0不是ajax
        check_auth(request()->controller() . '/cardstatus', 0);
        $request = Request::instance();
        $param = $request->param();
        $pu_id = $param["pu_id"];
        $this->assign("pu_id", $pu_id);
        $tui_id = $param["tui_id"];
        $this->assign("tui_id", $tui_id);
        return $this->fetch("cardstatus");
    }
    public function ajax_selectcard()
    {
        check_auth(request()->controller() . '/cardstatus', 1);
        $keynum = session("cn_accountinfo.basekeynum");
        $request = Request::instance();
        $param = $request->param();
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1); //计算记录偏移量
        $where = "clientkeynum='$keynum'";
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $start_card = isset($param['start_card']) ? $param['start_card'] : '';
        $end_card = isset($param['end_card']) ? $param['end_card'] : '';

        // 获取排序参数
        $sort_field = isset($param['sort_field']) ? $param['sort_field'] : '';
        $sort_order = isset($param['sort_order']) ? $param['sort_order'] : 'desc';

        // print_r($param);die;
        if ($field == 'cardnum') {
            if ($start_card != '') {
                if ($end_card == '') {
                    $end_card = $start_card;
                }
                $start_card = fenge($start_card);
                $end_card = fenge($end_card);
                $card_list = "";
                for ($i = $start_card["num"]; $i <= $end_card["num"]; $i++) {
                    $card_list .= "'" . $start_card["word"] . $i . "',";
                }
                $card_list = substr($card_list, 0, strlen($card_list) - 1);
                $where .= " and cardnum in ($card_list)";
            }
        } else if ($field == 'status') {
            $where .= " and status  = '$keyword' ";
        } else if ($field == 'end_dui') {
            // $tom = strtotime("$keyword+1 day");
            // $keyword = strtotime($keyword);
            $start_time_2 = isset($param['start_time_2']) ? $param['start_time_2'] : '';
            if ($start_time_2 != '') {
                if ($keyword == '') {
                    $keyword = $start_time_2;
                }
            }
            $start_time_2 = strtotime($start_time_2);
            $keyword = strtotime($keyword);
            $where .= " and begin_dui>$start_time_2 and end_dui<=$keyword";
            //$where .= " and end_dui between $keyword and $tom";
        } else if ($field == 'operator') {
            $where .= " and operator like '%$keyword%' ";
        } else if ($field == 'piciname') {
            $where .= " and piciname like '%$keyword%' ";
        } elseif ($field == 'gq_card') {
            //是否过期
            if ($keyword == 1) {
                //已过期
                $where .= " and end_dui<" . time();
            } else {
                //未过期
                $where .= " and end_dui>" . time();
            }
        } elseif ($field == 'member_phone') {
            $uid = Db::table("client_member")->where("phone like '%$keyword%' and clientkeynum='$keynum'")->value("id");
            $where .= " and uid='$uid'";
        } elseif ($field == 'market') {
            $sale_id = Db::table("card_sale")->where("name like '%$keyword%' and clientkeynum='$keynum'")->value("id");
            $where .= " and sale_id='$sale_id'";
        } elseif ($field == 'customer') {
            $customer_id = Db::table("card_customer")->where("name like '%$keyword%' and clientkeynum='$keynum'")->value("id");
            $where .= " and customer_id='$customer_id'";
        } elseif ($field == 'client_card_type') {
            $cardtype_id = Db::table("card_type")->where("name like '%$keyword%' and clientkeynum='$keynum'")->value("id");
            $where .= " and cardtype_id='$cardtype_id'";
        }
        $pu_id = $param["pu_id"];
        if (!empty($pu_id)) {
            $where .= " and pu_id='$pu_id'";
        }
        $tui_id = $param["tui_id"];
        if (!empty($tui_id)) {
            $where .= " and tui_id='$tui_id'";
        }
        $status_type = $param["status_type"];
        if ($status_type != '') {
            $where .= " and status='$status_type'";
        }
        session("ajax_selectcard_where", $where);

        // 构建排序条件
        $order = 'id desc'; // 默认排序
        $usePHPSort = false; // 是否使用PHP排序
        $phpSortField = ''; // PHP排序字段
        $phpSortOrder = 'desc'; // PHP排序方向

        if (!empty($param['fields']) && !empty($param['order'])) {
            // 这是LayUI表格发送的排序参数
            $sort_field = $param['fields'];
            $sort_order = $param['order'] === 'desc' ? 'desc' : 'asc';

            // 调试日志
            Log::info('排序参数: ' . json_encode(['field' => $sort_field, 'order' => $sort_order]));

            // 处理特殊字段的排序
            switch ($sort_field) {
                case 'kai_money':
                case 'yu_money':
                    // 这些是数据库中存在的字段，直接用SQL排序
                    $order = "$sort_field $sort_order";
                    break;
                case 'consume':
                    // consume是计算字段，需要在PHP中排序
                    $usePHPSort = true;
                    $phpSortField = $sort_field;
                    $phpSortOrder = $sort_order;
                    break;
                case 'begin_dui':
                case 'end_dui':
                    // 时间字段需要特殊处理
                    $order = "$sort_field $sort_order";
                    break;
                default:
                    $order = "id desc"; // 默认排序
            }
        }

        // echo $where;die;
        // 如果是PHP排序，先获取全部数据，不分页
        if ($usePHPSort) {
            $client_card = Db::table('client_ycard')
                ->where($where)
                ->select();
        } else {
            $client_card = Db::table('client_ycard')
                ->where($where)
                ->order($order)
                ->limit($offset . ',' . $pagesize)
                ->select();
        }

        foreach ($client_card as $k => $v) {
            if ($v['end_dui'] != '') {
                $client_card[$k]['end_dui'] = date('Y-m-d', $v['end_dui']);
                $client_card[$k]['begin_dui'] = date('Y-m-d', $v['begin_dui']);
            }
            $client_card[$k]['addtime'] = date('Y-m-d H:i:s', $v['add_time']);
            $client_card[$k]['status'] = ycard_status($v['status']);
            if ($v['last_memberid'] != '') {
                $uid = $v['last_memberid'];
                $client_card[$k]['last_memberid'] = Db::table('client_member')
                    ->where("id=$uid")
                    ->value("name");
            } else {
                $client_card[$k]['last_memberid'] = '';
            }
            $client_card[$k]['customer_name'] = Db::table('card_customer')
                ->where("id='" . $v['customer_id'] . "' and clientkeynum='$keynum'")->value("name");
            $client_card[$k]['sale_name'] = Db::table('card_sale')
                ->where("id='" . $v['sale_id'] . "' and clientkeynum='$keynum'")->value("name");
            $client_card[$k]['cardtype_name'] = Db::table('client_card_type')
                ->where("id='" . $v['cardtype_id'] . "' and clientkeynum='$keynum'")->value("name");
            $client_card[$k]['qrcode'] = trim($v['qrcode'], ".");
            $client_card[$k]['consume'] = ClientCardUseLog::where([
                'cardnum' => $v['cardnum'],
                'status' => 1
            ])->sum('use_money');
            $client_card[$k]['refund'] = ClientCardUseLog::where([
                'cardnum' => $v['cardnum'],
                'status' => 2
            ])->sum('use_money');
            $client_card[$k]['consume'] = bcsub($client_card[$k]['consume'], $client_card[$k]['refund'], 2);
            if(!empty($v['card_level_id'])){
                $client_card[$k]['card_level'] = Db::table('client_card_level')
                    ->where('id', $v['card_level_id'])
                    ->value('title');
            }else{
                $client_card[$k]['card_level'] = '';
            }
        }

        // 如果需要PHP排序，在这里进行排序
        if ($usePHPSort) {
            // 使用usort进行自定义排序
            usort($client_card, function($a, $b) use ($phpSortField, $phpSortOrder) {
                if ($phpSortOrder === 'desc') {
                    return $b[$phpSortField] <=> $a[$phpSortField]; // 降序
                } else {
                    return $a[$phpSortField] <=> $b[$phpSortField]; // 升序
                }
            });

            // 手动分页
            $count = count($client_card);
            $client_card = array_slice($client_card, $offset, $pagesize);
        } else {
            $count = Db::table('client_ycard')
                ->where($where)
                ->count();
        }
        $card_money = Db::table('client_ycard')
            ->where($where)
            ->sum("kai_money");
        $card_money2 = Db::table('client_ycard')
            ->where($where)
            ->sum("yu_money");
        $total_consume = ClientCardUseLog::where(['status' => 1])
            ->whereIn('cardnum', ClientYcardModel::where($where)->column('cardnum'))
            ->sum('use_money');
        $total_refund = ClientCardUseLog::where(['status' => 2])
            ->whereIn('cardnum', ClientYcardModel::where($where)->column('cardnum'))
            ->sum('use_money');
        $total_consume = bcsub($total_consume, $total_refund, 2);
        $rtdata['data'] = $client_card;
        $rtdata['count'] = $count;
        $rtdata["code"] = 0;
        $rtdata["msg"] = "";
        $rtdata["kai_money"] = $card_money;
        $rtdata["yu_money"] = $card_money2;
        $rtdata["total_consume"] = $total_consume;
        $rtdata["card_count"] = $count;
        echo json_encode($rtdata);
        die;
    }

    public function export_selectcard()
    {
        $keynum = session("cn_accountinfo.basekeynum");
        $param = Request::instance()->param();
        $where = session("ajax_selectcard_where");
        $count = Db::table('client_ycard')
            ->where($where)
            ->count();
        if ($count > 30000) {
            error_tips("单次最大可导出3万条数据,请缩小搜索范围");
            die;
        }
        $export = array(
            array('卡号', '销售员', '客户', '卡型', '卡号状态', '起兑时间', '止兑时间',
            '批次名称', '最后使用会员', '面值', '余额', '生成时间', '生成时操作人', '生成时备注')
        );
        $client_card = Db::table('client_ycard')
            ->where($where)
            ->order('id asc')
            ->select();
        foreach ($client_card as $k => $v) {
            if ($v['end_dui'] != '') {
                $client_card[$k]['end_dui'] = date('Y-m-d', $v['end_dui']);
                $client_card[$k]['begin_dui'] = date('Y-m-d', $v['begin_dui']);
            }
            $client_card[$k]['addtime'] = date('Y-m-d H:i:s', $v['add_time']);
            $client_card[$k]['status'] = ycard_status($v['status']);
            if ($v['last_memberid'] != '') {
                $uid = $v['last_memberid'];
                $client_card[$k]['last_memberid'] = Db::table('client_member')
                    ->where("id=$uid")
                    ->value("name");
            } else {
                $client_card[$k]['last_memberid'] = '';
            }
            $client_card[$k]['customer_name'] = Db::table('card_customer')
                ->where("id='" . $v['customer_id'] . "' and clientkeynum='$keynum'")->value("name");
            $client_card[$k]['sale_name'] = Db::table('card_sale')
                ->where("id='" . $v['sale_id'] . "' and clientkeynum='$keynum'")->value("name");
            $client_card[$k]['cardtype_name'] = Db::table('client_card_type')
                ->where("id='" . $v['cardtype_id'] . "' and clientkeynum='$keynum'")->value("name");

            $row = array(
                $client_card[$k]["cardnum"],
                $client_card[$k]["sale_name"],
                $client_card[$k]["customer_name"],
                $client_card[$k]["cardtype_name"],
                $client_card[$k]["status"],
                $client_card[$k]["begin_dui"],
                $client_card[$k]["end_dui"],
                $client_card[$k]["piciname"],
                $client_card[$k]["last_memberid"],
                $client_card[$k]["kai_money"],
                $client_card[$k]["yu_money"],
                $client_card[$k]["addtime"],
                $client_card[$k]["operator"],
                $client_card[$k]["content"]
            );
            $export[] = $row;
        }

        $xls_name = "卡号状态查询" . date('Y-m-d');

        // 使用SimpleExcelWriter导出
        try {
            SimpleExcelWriter::streamDownload($xls_name . '.xlsx')
                ->addRows($export)
                ->toBrowser();
        } catch (\Exception $e) {
            // 如果SimpleExcel不可用，使用原来的create_xls函数
            create_xls($export, $xls_name);
        }
    }

    public function pu_card()
    {
        check_auth(request()->controller() . '/pu_card', 0);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $request = Request::instance();
        $param = $request->param();
        // 把所有卡型列出来
        $cardtype_list = Db::table('client_card_type')->where("clientkeynum='$basekeynum'")->field("id,name")->select();
        $card_level = Db::table('client_card_level')->where("clientkeynum='$basekeynum'")->field("id,title")->select();
        $this->assign("cardtype_list", $cardtype_list);
        $this->assign("card_level", $card_level);
        return $this->fetch();
    }

    public function get_customer_info()
    {
        check_auth(request()->controller() . '/pu_card', 0);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $request = Request::instance();
        $param = $request->param();
        $customer_name = $param["customer_name"];
        $customer_info = Db::table('card_customer')->where("name='$customer_name' and clientkeynum='$basekeynum'")->find();
        $customer_info["sale_name"] = Db::table('card_sale')->where("id='" . $customer_info["sale_id"] . "'")->value("name");
        $rs = $customer_info;
        $rs["sta"] = 1;
        echo json_encode($rs);
        die;
    }

    public function ajax_card_customer()
    {
        $basekeynum = session("cn_accountinfo.basekeynum");
        // 获取所有客户
        $customer_list = Db::table('card_customer')->where("clientkeynum='$basekeynum'")->field("id,name")->select();
        $customer_list_new = array();
        foreach ($customer_list as $key => $value) {
            $customer_list_one["value"] = $value["name"];
            $customer_list_one["label"] = $value["name"];
            $customer_list_new[] = $customer_list_one;
        }
        echo json_encode($customer_list_new);
    }

    public function get_card_type_info()
    {
        $request = Request::instance();
        $param = $request->param();
        $cardtype_id = $param["cardtype_id"];
        $basekeynum = session("cn_accountinfo.basekeynum");
        // 获取所有客户
        $card_type_info = Db::table('client_card_type')->where("clientkeynum='$basekeynum' and id='$cardtype_id'")->find();
        if ($card_type_info) {
            $card_type_info["sta"] = 1;
            echo json_encode($card_type_info);
            die;
        }
        $rt['sta'] = 0;
        $rt['msg'] = '卡型不存在!';
        echo json_encode($rt);
        die;
    }

    public function get_pu_card_mingxi()
    {
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = session("cn_accountinfo.basekeynum");
        $cardtype_id = $param["cardtype_id"];
        $card_level_id = isset($param["card_level_id"]) ? $param["card_level_id"] : ''; // 添加获取卡等级ID的代码
        // 获取卡型
        $card_type_info = Db::table('client_card_type')->where("clientkeynum='$basekeynum' and id='$cardtype_id'")->find();
        if (empty($card_type_info)) {
            $rt['sta'] = 0;
            $rt['msg'] = '卡型不存在!';
            echo json_encode($rt);
            die;
        }
        $market_price = $card_type_info["market_price"];
        $begin_card_str = $param["begin_card"];
        $end_card_str = $param["end_card"];

        // 提取卡号的字母部分和数字部分
        $begin_cardnumber_arr = preg_split('@(\d+)@', $begin_card_str, -1, PREG_SPLIT_DELIM_CAPTURE);
        $begin_cardnumber_word = $begin_cardnumber_arr[0];
        $begin_cardnumber_number = $begin_cardnumber_arr[1];

        $end_cardnumber_arr = preg_split('@(\d+)@', $end_card_str, -1, PREG_SPLIT_DELIM_CAPTURE);
        $end_cardnumber_word = $end_cardnumber_arr[0];
        $end_cardnumber_number = $end_cardnumber_arr[1];

        $begin_card = fenge($begin_card_str);
        $end_card = fenge($end_card_str);
        $card_list = "";
        $cardnum_count = 0;
        // 获取原始数字部分的长度，用于保留前导零
        $original_num_length = strlen($begin_cardnumber_number);
        for ($i = $begin_card["num"]; $i <= $end_card["num"]; $i++) {
            // 使用dispRepair保留前导零，确保保持与原始卡号相同位数的前导零
            $card_number = $begin_card["word"] . $this->dispRepair($i, $original_num_length, '0');
            $card_list .= "'" . $card_number . "',";
            $cardnum_count++;
        }
        Log::info($cardnum_count);
        $card_list = substr($card_list, 0, strlen($card_list) - 1);
        if (empty($card_list)) {
            $rt['sta'] = 0;
            $rt['msg'] = '卡号段不存在!';
            echo json_encode($rt);
            die;
        }
        // 获取卡号段是否都是未销售或已退卡重新销售
        $cardnum_count_sql = Db::table('client_ycard')
            ->where("clientkeynum='$basekeynum' and status in (5,2) and cardnum in ($card_list)")
            ->count();
        Log::info($cardnum_count_sql);
        if ($cardnum_count_sql != $cardnum_count) {
            $rt['sta'] = 0;
            $rt['msg'] = '卡号段含有状态不符卡号!';
            echo json_encode($rt);
            die;
        }
        $zhekou = $param["zhekou"];
        $end_price = $param["end_price"];
        $type = $param["type"];
        if ($type == '1') {
            $end_price = $market_price * ($zhekou / 100);
        } else {
            $zhekou = (round($end_price / $market_price, 2)) * 100;
        }
        $rs["sta"] = 1;
        $rs["zhekou"] = round($zhekou, 2);
        $rs["end_price"] = round($end_price, 2);
        $rs["all_price"] = round($end_price * $cardnum_count_sql, 2);
        echo json_encode($rs);
        die;
    }


    public function ajax_pu_card()
    {
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = session("cn_accountinfo.basekeynum");
        $customer_name = $param["customer_id"];
        $linkman = $param["linkman"];
        $linktel = $param["linktel"];
        $sale_name = $param["sale_name"];
        $begin_time = strtotime($param["begin_time"] . ' 00:00:00');
        $end_time = strtotime($param["end_time"] . ' 23:59:59');
        $remark = $param["content"];
        $cardtype_id = $param["cardtype_id"];
        $card_level_id = $param["card_level_id"]; // 添加获取卡等级ID的代码
        $begin_card = $param["begin_card"];
        $end_card = $param["end_card"];
        $market_price = $param["market_price"];
        $zhekou = $param["zhekou"];
        $end_price = $param["end_price"];
        $all_price = $param["all_price"];

        $is_auto_open = $param['is_auto_open'];

        // 通过事务操作
        Db::startTrans();
        try {
            $where = "clientkeynum='$basekeynum'";
            // 判断销售是否存在
            $sale_info = Db::table('card_sale')->where("name='$sale_name'")->where($where)->find();
            if (empty($sale_info)) {
                $add_sale["clientkeynum"] = $basekeynum;
                $add_sale["name"] = $sale_name;
                $sale_id = Db::table('card_sale')->insertGetId($add_sale);
            } else {
                $sale_id = $sale_info["id"];
            }
            // 判断档案客户是否存在

            $card_customer_info = Db::table('card_customer')->where("name='$customer_name'")->where($where)->fetchsql(false)->find();
            if (empty($card_customer_info)) {
                // 执行新增客户的操作
                $add_customer["clientkeynum"] = $basekeynum;
                $add_customer["name"] = $customer_name;
                $add_customer["linkman"] = $linkman;
                $add_customer["linktel"] = $linktel;
                $add_customer["sale_id"] = $sale_id;
                $customer_id = Db::table('card_customer')->insertGetId($add_customer);
            } else {
                $customer_id = $card_customer_info["id"];
            }
            // 插入销售主表
            $add_pucard["clientkeynum"] = $basekeynum;
            $add_pucard["customer_id"] = $customer_id;
            $add_pucard["sale_id"] = $sale_id;
            $add_pucard["begin_time"] = $begin_time;
            $add_pucard["end_time"] = $end_time;
            $add_pucard["remark"] = $remark;
            $add_pucard["opeartor"] = session('cn_accountinfo.accountname');
            $add_pucard["operator_time"] = time();
            $num = mt_rand(1000, 9999);
            $prince_number = "pc" . date("Ymd") . "$num";
            $add_pucard["pici_number"] = $prince_number;
            $add_pucard["type"] = 1;
            $add_pucard["real_tui_money"] = array_sum($all_price);
            $add_pucard["uncleared_money"] = array_sum($all_price);
            $pucard_id = Db::table('client_ycard_pucard')->insertGetId($add_pucard);

            // 循环卡型卡号段，进行销售，自动开卡等操作
            foreach ($cardtype_id as $key => $value) {
                // 获取一下卡型信息
                $card_type_info = Db::table('client_card_type')->where("id='$value'")->where($where)->find();
                if (empty($card_type_info)) {
                    $msg = "卡型信息不存在!";
                    throw new \Exception($msg);
                }
                $add_pucard_son["clientkeynum"] = $basekeynum;
                $add_pucard_son["pucard_id"] = $pucard_id;
                $add_pucard_son["cardtype_id"] = $value;
                $add_pucard_son["card_level_id"] = $card_level_id[$key]; // 添加卡等级ID
                $add_pucard_son["begin_card"] = $begin_card[$key];
                $add_pucard_son["end_card"] = $end_card[$key];
                $add_pucard_son["market_price"] = $market_price[$key];
                $add_pucard_son["zhekou"] = $zhekou[$key];
                $add_pucard_son["end_price"] = $end_price[$key];
                $add_pucard_son["all_price"] = $all_price[$key];
                $add_pucard_son["operator"] = session('cn_accountinfo.accountname');
                $add_pucard_son["operator_time"] = time();

                // 重新校验卡号段
                $begin_card_arr = fenge($begin_card[$key]);
                $end_card_arr = fenge($end_card[$key]);
                $card_list = "";
                $cardnum_count = 0;
                for ($i = $begin_card_arr["num"]; $i <= $end_card_arr["num"]; $i++) {
                    $card_list .= "'" . $begin_card_arr["word"] . $i . "',";
                    $cardnum_count++;
                }
                $card_list = substr($card_list, 0, strlen($card_list) - 1);
                if (empty($card_list)) {
                    $msg = $begin_card[$key] . "-" . $end_card[$key] . "卡号段不存在!";
                    throw new \Exception($msg);
                }
                // 获取卡号段是否都是未销售或已退卡重新销售
                $where_ycard = "clientkeynum='$basekeynum' and status in (5,2) and cardnum in ($card_list)";
                $cardnum_count_sql = Db::table('client_ycard')
                    ->where($where_ycard)
                    ->count();
                if ($cardnum_count_sql != $cardnum_count) {
                    $msg = $begin_card[$key] . "-" . $end_card[$key] . "卡号段含有状态不符卡号!";
                    throw new \Exception($msg);
                }
                $add_pucard_son["cardnum"] = $card_list;
                $add_pucard_son["cardnum_count"] = $cardnum_count_sql;
                Db::table('client_ycard_pucard_son')->insert($add_pucard_son);
                $cardnum_arr = Db::table('client_ycard')
                    ->where($where_ycard)
                    ->column("cardnum");
                // 执行销售操作
                $update = array();
                $update['status'] = 1;
                $update['customer_id'] = $customer_id;
                $update['sale_id'] = $sale_id;
                $update['pu_id'] = $pucard_id;
                $update['cardtype_id'] = $value;
                $update['card_level_id'] = $card_level_id[$key]; // 添加卡等级ID
                $update['kai_money'] = $card_type_info["to_balance"];
                $update['yu_money'] = $card_type_info["to_balance"];
                $update['kai_exchange_num'] = $card_type_info["max_exchange_num"];
                $update['yu_exchange_num'] = $card_type_info["max_exchange_num"];
                $update['begin_dui'] = $begin_time;
                $update['end_dui'] = $end_time;
                $update['is_del'] = 0;
                Db::table('client_ycard')
                    ->where($where_ycard)
                    ->update($update);
                // 插入卡号日志
                $add_ycard_log_all = array();
                foreach ($cardnum_arr as $ck => $cv) {
                    $add_ycard_log["cardnum"] = $cv;
                    $add_ycard_log["action"] = "销售";
                    $add_ycard_log["operator"] = session('cn_accountinfo.accountname');
                    $add_ycard_log["operator_time"] = time();
                    $add_ycard_log["content"] = "后台进行销售";
                    $add_ycard_log["clientkeynum"] = $basekeynum;
                    $add_ycard_log_all[] = $add_ycard_log;
                }
                Db::table('client_ycard_log')->insertAll($add_ycard_log_all);
            }

            /**
             * 同步财务记录
             * @date 2023-4-25
             * <AUTHOR>
             */
            //            if ($param['is_record_finance'] == 1) {
            //                $finance_where = [
            //                    'basekeynum' => ['=', $basekeynum],
            //                    'balance' => ['>', 0]
            //                ];
            //                $finance_list = FinanceModel::getListByCustomerId($customer_id, $finance_where);
            //                $operator = session('cn_accountinfo.accountname');
            //                if (empty($finance_list)) throw new \Exception('该客户已无可用余额！');
            //                $total_price = array_sum($all_price);
            //                if (array_sum(array_column($finance_list,'balance')) < $total_price) {
            //                    throw new \Exception('该客户可用余额不足！');
            //                }
            //                foreach ($finance_list as $v) {
            //                    $diff_price = $v['balance'] - $total_price;
            //                    if ($diff_price > 0){
            //                        // 可一次性结清
            //                        $finance_data = [
            //                            'basekeynum' => $basekeynum,
            //                            'finance_id' => $v['id'],
            //                            'no' => $prince_number,
            //                            'operator' => $operator,
            //                            'money' => $total_price,
            //                            'status' => 1,
            //                            'remarks' => '销售自动记录',
            //                            'add_time' => date('Y-m-d H:i:s'),
            //                        ];
            //                        FinanceDetailModel::add($finance_data);
            //                        $v['balance'] -= $total_price;
            //                        $v['income'] += $total_price;
            //                        $v->save();
            //                        break;
            //                    } else {
            //                        $finance_data = [
            //                            'basekeynum' => $basekeynum,
            //                            'finance_id' => $v['id'],
            //                            'no' => $prince_number,
            //                            'operator' => $operator,
            //                            'money' => $v['balance'],
            //                            'status' => 1,
            //                            'remarks' => '销售自动记录',
            //                            'add_time' => date('Y-m-d H:i:s'),
            //                        ];
            //                        FinanceDetailModel::add($finance_data);
            //                        $total_price -= $v['balance'];
            //                        $v['income'] += $v['balance'];
            //                        $v['balance'] = 0;
            //                        $v->save();
            //                    }
            //                }
            //            }

            /**
             * 记录卡状态变更
             * @date 2023-4-28
             * <AUTHOR>
             */
            $change_no = CardStatusChange::createNo();
            // 插入卡状态变更表
            $data = [
                'basekeynum'        => $basekeynum,
                'no'                => $change_no,
                'customer_id'       => $customer_id,
                'sale_id'           => $sale_id,
                'status'            => 1,
                'cardnum'           => implode(',', $cardnum_arr),
                'operator'          => session('cn_accountinfo.accountname'),
                'remarks'           => $remark,
                'add_time'          => date('Y-m-d H:i:s'),
            ];

            $id = CardStatusChange::add($data);

            if (!$id) throw new \Exception('记录卡状态更新失败');

            $card_where['clientkeynum'] = ['=', $basekeynum];
            $card_list = ClientYcardModel::getListByCardNumbers($cardnum_arr, $card_where);
            // 预设批量插入卡号状态更新记录列表
            $card_status_change_list = [];
            foreach ($card_list as $v) {
                $card_status_change_list[] = [
                    'basekeynum'    => $basekeynum,
                    'change_no'     => $change_no,
                    'card_id'       => $v['id'],
                    'customer_id'   => $v['customer_id'],
                    'sale_id'       => $v['sale_id'],
                    'operator'      => session('cn_accountinfo.accountname'),
                    'type'          => 1,
                    'remarks'       => '销售操作自动更新',
                    'add_time'      => date('Y-m-d H:i:s'),
                ];
            }

            $change_status_result = CardStatusChangeLogModel::batchAdd($card_status_change_list);
            if (!$change_status_result) throw new \Exception('记录状态变更失败！');

            // 自动开卡操作
            if ($is_auto_open == 1) {
                $no = CardStatusChange::createNo();
                // 插入卡状态变更表
                $data = [
                    'basekeynum'        => $basekeynum,
                    'no'                => $no,
                    'customer_id'       => $customer_id,
                    'sale_id'           => $sale_id,
                    'status'            => 3,
                    'cardnum'           => implode(',', $cardnum_arr),
                    'operator'          => session('cn_accountinfo.accountname'),
                    'remarks'           => $remark,
                    'add_time'          => date('Y-m-d H:i:s'),
                ];

                $id = CardStatusChange::add($data);

                if (!$id) throw new \Exception('记录卡状态更新失败');

                // 这里用db类 因为模型底层的批量更新会有事务提交
                // print_r($cardnum_arr);die;
                $where_change[] = ['cardnum', 'in', $cardnum_arr];
                $change_status_result = Db::table('client_ycard')
                    ->where($where_change)
                    ->update(['status' => 3]);
                if (!$change_status_result) throw new \Exception('更新卡状态失败！' . db()->getLastSql());
                foreach ($card_status_change_list as $k => $v) {
                    $card_status_change_list[$k]['status'] = 3;
                    $card_status_change_list[$k]['change_no'] = $no;
                }
                $change_status_insert_result = CardStatusChangeLogModel::batchAdd($card_status_change_list);
                if (!$change_status_insert_result) throw new \Exception('记录卡状态更新失败！');
            }

            Db::commit();
            $rt["sta"] = 1;
            $rt["msg"] = "操作成功";
            echo json_encode($rt);
            die;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            Log::error(json_encode($e->getTrace()));
            Log::error('errormsg:' . $e->getMessage());
            $msg = $e->getMessage();
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败:" . $msg;
            echo json_encode($rt);
            die;
        }
    }

    public function pu_card_list()
    {
        check_auth(request()->controller() . '/pu_card_list', 0);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $request = Request::instance();
        $param = $request->param();
        return $this->fetch();
    }

    public function ajax_pucard_list()
    {
        check_auth(request()->controller() . '/cardstatus', 1);
        $keynum = session("cn_accountinfo.basekeynum");
        $request = Request::instance();
        $param = $request->param();
        $p = $param['page'] ? $param['page'] : 1;
        $pagesize = $param['limit'];
        $offset = $pagesize * ($p - 1); //计算记录偏移量
        $where = "clientkeynum='$keynum' and type='1'";
        $field = isset($param['field']) ? $param['field'] : '';
        $keyword = isset($param['keyword']) ? $param['keyword'] : '';
        $start_time_2 = isset($param['start_time_2']) ? $param['start_time_2'] : '';
        $end_time_2 = isset($param['end_time_2']) ? $param['end_time_2'] : '';
        if (!empty($start_time_2) and !empty($end_time_2)) {
            $start_time_2 = strtotime($start_time_2);
            $end_time_2 = strtotime($end_time_2);
            $where .= " and operator_time>$start_time_2 and operator_time<=$end_time_2";
        }
        if ($field == 'customer_name') {
            $customer_id = Db::table('card_customer')->where("name like '%$keyword%' and clientkeynum='$keynum'")->value("id");
            $where .= " and customer_id='$customer_id'";
        } elseif ($field == 'sale_name') {
            $sale_id = Db::table('card_sale')->where("name like '%$keyword%' and clientkeynum='$keynum'")->value("id");
            $where .= " and sale_id='$sale_id'";
        } elseif ($field == 'cardnum') {
            $pu_id = Db::table('client_ycard')->where("cardnum like '%$keyword%' and clientkeynum='$keynum'")->value("pu_id");
            $where .= " and id='$pu_id'";
        }
        session("ajax_pucard_list_where", $where);
        $pucard_list = Db::table('client_ycard_pucard')
            ->where($where)
            ->order('id desc')
            ->limit($offset . ',' . $pagesize)
            ->select();
        foreach ($pucard_list as $k => $v) {
            $pucard_list[$k]['operator_time'] = date('Y-m-d H:i:s', $v['operator_time']);
            $pucard_list[$k]['begin_time'] = date('Y-m-d H:i:s', $v['begin_time']);
            $pucard_list[$k]['end_time'] = date('Y-m-d H:i:s', $v['end_time']);
            $pucard_list[$k]['customer_name'] = Db::table('card_customer')
                ->where("id='" . $v['customer_id'] . "' and clientkeynum='$keynum'")->value("name");
            $pucard_list[$k]['sale_name'] = Db::table('card_sale')
                ->where("id='" . $v['sale_id'] . "' and clientkeynum='$keynum'")->value("name");
            // $all_price =
            //     Db::table('client_ycard_pucard_son')->where("pucard_id='" . $v['id'] . "' and clientkeynum='$keynum'")->column("all_price");
            $pucard_list[$k]['all_price'] = $v['real_tui_money'];
            $all_cardnum_count =
                Db::table('client_ycard_pucard_son')->where("pucard_id='" . $v['id'] . "' and clientkeynum='$keynum'")->column("cardnum_count");
            $pucard_list[$k]['all_cardnum_count'] = array_sum($all_cardnum_count);
        }
        $count = Db::table('client_ycard_pucard')
            ->where($where)
            ->count();
        $rtdata['data'] = $pucard_list;
        $rtdata['count'] = $count;
        $rtdata["code"] = 0;
        $rtdata["msg"] = "";
        echo json_encode($rtdata);
        die;
    }

    public function pu_card_select()
    {
        check_auth(request()->controller() . '/pu_card_list', 0);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $request = Request::instance();
        $param = $request->param();
        $id = $param["id"];
        $pucard_info = Db::table('client_ycard_pucard')->where("id='$id' and clientkeynum='$basekeynum'")->find();
        $pucard_info["customer_info"] = Db::table('card_customer')->where("id='" . $pucard_info['customer_id'] . "'")->find();
        $pucard_info["sale_name"] = Db::table('card_sale')->where("id='" . $pucard_info['sale_id'] . "'")->value("name");
        $pucard_info["begin_time"] = date("Y-m-d H:i:s", $pucard_info["begin_time"]);
        $pucard_info["end_time"] = date("Y-m-d H:i:s", $pucard_info["end_time"]);
        $this->assign("pucard_info", $pucard_info);
        $pucard_son_list = Db::table('client_ycard_pucard_son')->where("pucard_id='$id' and clientkeynum='$basekeynum'")->select();
        foreach ($pucard_son_list as $k => $v) {
            $pucard_son_list[$k]["card_leve_title"] = Db::table('client_card_level')
            ->where(['clientkeynum' => $basekeynum, 'id' => $v['card_level_id']])->value('title');
        }
        $this->assign("pucard_son_list", $pucard_son_list);
        // 把所有卡型列出来
        $cardtype_list = Db::table('client_card_type')->where("clientkeynum='$basekeynum'")->field("id,name")->select();
        $this->assign("cardtype_list", $cardtype_list);
        return $this->fetch();
    }

    public function export_pu_card_list()
    {
        $keynum = session("cn_accountinfo.basekeynum");
        $param = Request::instance()->param();
        $where = session("ajax_pucard_list_where");
        $count = Db::table('client_ycard_pucard')
            ->where($where)
            ->count();
        if ($count > 10000) {
            error_tips("单次最大可导出1万条数据,请缩小搜索范围");
            die;
        }
        $daochu[] = array(
            '销售单号', '操作人', '操作时间', '客户', '销售', '销售卡号总数', '销售单总额', '可兑开始时间', '可兑结束时间', '备注'
        );
        $pucard_list = Db::table('client_ycard_pucard')
            ->where($where)
            ->order('id desc')
            ->select();
        foreach ($pucard_list as $k => $v) {
            $pucard_list[$k]['operator_time'] = date('Y-m-d H:i:s', $v['operator_time']);
            $pucard_list[$k]['begin_time'] = date('Y-m-d H:i:s', $v['begin_time']);
            $pucard_list[$k]['end_time'] = date('Y-m-d H:i:s', $v['end_time']);
            $pucard_list[$k]['customer_name'] = Db::table('card_customer')
                ->where("id='" . $v['customer_id'] . "' and clientkeynum='$keynum'")->value("name");
            $pucard_list[$k]['sale_name'] = Db::table('card_sale')
                ->where("id='" . $v['sale_id'] . "' and clientkeynum='$keynum'")->value("name");
            $all_price =
                Db::table('client_ycard_pucard_son')->where("pucard_id='" . $v['id'] . "' and clientkeynum='$keynum'")->column("all_price");
            $pucard_list[$k]['all_price'] = array_sum($all_price);
            $all_cardnum_count =
                Db::table('client_ycard_pucard_son')->where("pucard_id='" . $v['id'] . "' and clientkeynum='$keynum'")->column("cardnum_count");
            $pucard_list[$k]['all_cardnum_count'] = array_sum($all_cardnum_count);

            $r[1] = $pucard_list[$k]["pici_number"];
            $r[2] = $pucard_list[$k]["opeartor"];
            $r[11] = $pucard_list[$k]["operator_time"];
            $r[12] = $pucard_list[$k]["customer_name"];
            $r[13] = $pucard_list[$k]["sale_name"];
            $r[3] = $pucard_list[$k]["all_cardnum_count"];
            $r[4] = $pucard_list[$k]["all_price"];
            $r[6] = $pucard_list[$k]["begin_time"];
            $r[14] = $pucard_list[$k]["end_time"];
            $r[7] = $pucard_list[$k]["remark"];
            // ksort($r);
            $daochu[] = $r;
        }
        $xls_name = "销售单查询" . date('Y-m-d');

        // 使用SimpleExcelWriter导出
        try {
            SimpleExcelWriter::streamDownload($xls_name . '.xlsx')
                ->addRows($daochu)
                ->toBrowser();
        } catch (\Exception $e) {
            // 如果SimpleExcel不可用，使用原来的create_xls函数
            create_xls($daochu, $xls_name);
        }
    }

    public function export_pu_card_list_son()
    {
        $keynum = session("cn_accountinfo.basekeynum");
        $param = Request::instance()->param();
        $where = session("ajax_pucard_list_where");
        $pu_id = Db::table('client_ycard_pucard')
            ->where($where)
            ->column("id");
        $where = array();
        $where[] = array("pucard_id",'in', $pu_id);
        $count = Db::table('client_ycard_pucard_son')
            ->where($where)
            ->count();
        if ($count > 10000) {
            error_tips("单次最大可导出1万条数据,请缩小搜索范围");
            die;
        }
        $daochu[] = array(
            '销售单号', '操作人', '操作时间', '客户', '销售', '可兑开始时间', '可兑结束时间', '备注', '卡型', '开始卡号', '结束卡号', '卡型销售价', '折扣', '最终价格(单价)', '合计'
        );
        $pucard_list = Db::table('client_ycard_pucard_son')
            ->where($where)
            ->order('id desc')
            ->select();
        foreach ($pucard_list as $k => $v) {
            $pucard_id = $v['pucard_id'];
            $pucard_info = Db::table('client_ycard_pucard')->where("id='$pucard_id'")->find();

            $pucard_info['operator_time'] = date('Y-m-d H:i:s', $pucard_info['operator_time']);
            $pucard_info['begin_time'] = date('Y-m-d H:i:s', $pucard_info['begin_time']);
            $pucard_info['end_time'] = date('Y-m-d H:i:s', $pucard_info['end_time']);
            $pucard_info['customer_name'] = Db::table('card_customer')
                ->where("id='" . $pucard_info['customer_id'] . "' and clientkeynum='$keynum'")->value("name");
            $pucard_info['sale_name'] = Db::table('card_sale')
                ->where("id='" . $pucard_info['sale_id'] . "' and clientkeynum='$keynum'")->value("name");
            $r = array();
            $r[] = $pucard_info["pici_number"];
            $r[] = $pucard_info["opeartor"];
            $r[] = $pucard_info["operator_time"];
            $r[] = $pucard_info["customer_name"];
            $r[] = $pucard_info["sale_name"];
            $r[] = $pucard_info["begin_time"];
            $r[] = $pucard_info["end_time"];
            $r[] = $pucard_info["remark"];
            $cardtype_id = $v['cardtype_id'];
            $r[] = Db::table('client_card_type')->where("id='$cardtype_id'")->value("name");
            $r[] = $v["begin_card"];
            $r[] = $v["end_card"];
            $r[] = $v["market_price"];
            $r[] = $v["zhekou"];
            $r[] = $v["end_price"];
            $r[] = $v["all_price"];
            // ksort($r);
            $daochu[] = $r;
        }
        $xls_name = "销售单子表查询" . date('Y-m-d');

        // 使用SimpleExcelWriter导出
        try {
            SimpleExcelWriter::streamDownload($xls_name . '.xlsx')
                ->addRows($daochu)
                ->toBrowser();
        } catch (\Exception $e) {
            // 如果SimpleExcel不可用，使用原来的create_xls函数
            create_xls($daochu, $xls_name);
        }
    }

    public function export_pu_card_list_cardnum()
    {
        $keynum = session("cn_accountinfo.basekeynum");
        $param = Request::instance()->param();
        $where = session("ajax_pucard_list_where");
        $pu_id = Db::table('client_ycard_pucard')
            ->where($where)
            ->column("id");
        $where = array();
        $where[] = array("pucard_id",'in', $pu_id);
        $count = Db::table('client_ycard_pucard_son')
            ->where($where)
            ->count();
        if ($count > 10000) {
            error_tips("单次最大可导出1万条数据,请缩小搜索范围");
            die;
        }
        $daochu[] = array(
            '销售单号', '操作人', '操作时间', '客户', '销售', '可兑开始时间', '可兑结束时间', '备注', '卡型', '卡号'
        );
        $pucard_list = Db::table('client_ycard_pucard_son')
            ->where($where)
            ->order('id desc')
            ->select();
        foreach ($pucard_list as $k => $v) {
            $cardnum = array_filter(explode(',', $v['cardnum']));
            $pucard_id = $v['pucard_id'];
            $pucard_info = Db::table('client_ycard_pucard')->where("id='$pucard_id'")->find();

            $pucard_info['operator_time'] = date('Y-m-d H:i:s', $pucard_info['operator_time']);
            $pucard_info['begin_time'] = date('Y-m-d H:i:s', $pucard_info['begin_time']);
            $pucard_info['end_time'] = date('Y-m-d H:i:s', $pucard_info['end_time']);
            $pucard_info['customer_name'] = Db::table('card_customer')
                ->where("id='" . $pucard_info['customer_id'] . "' and clientkeynum='$keynum'")->value("name");
            $pucard_info['sale_name'] = Db::table('card_sale')
                ->where("id='" . $pucard_info['sale_id'] . "' and clientkeynum='$keynum'")->value("name");
            foreach ($cardnum as $ck => $cv) {
                $cv = trim($cv, "'");
                $r = array();
                $r[] = $pucard_info["pici_number"];
                $r[] = $pucard_info["opeartor"];
                $r[] = $pucard_info["operator_time"];
                $r[] = $pucard_info["customer_name"];
                $r[] = $pucard_info["sale_name"];
                $r[] = $pucard_info["begin_time"];
                $r[] = $pucard_info["end_time"];
                $r[] = $pucard_info["remark"];
                $cardtype_id = $v['cardtype_id'];
                $r[] = Db::table('client_card_type')->where("id='$cardtype_id'")->value("name");
                $r[] = $cv;
                $daochu[] = $r;
            }
        }
        $xls_name = "销售单卡号查询" . date('Y-m-d');

        // 使用SimpleExcelWriter导出
        try {
            SimpleExcelWriter::streamDownload($xls_name . '.xlsx')
                ->addRows($daochu)
                ->toBrowser();
        } catch (\Exception $e) {
            // 如果SimpleExcel不可用，使用原来的create_xls函数
            create_xls($daochu, $xls_name);
        }
    }

    public function tui_card()
    {
        check_auth(request()->controller() . '/tui_card', 0);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $request = Request::instance();
        $param = $request->param();
        $customer_list = Db::table('card_customer')->where("clientkeynum='$basekeynum'")->field("id,name")->select();
        $this->assign("customer_list", $customer_list);
        return $this->fetch();
    }

    // 生成退卡预览
    public function get_tui_card()
    {
        $keynum = session("cn_accountinfo.basekeynum");
        $customer_id = $_POST['customer_id'];
        $sale_number = $_POST['sale_number'];
        if (empty($sale_number)) {
            $begin_cardnumber = $_POST['begin_cardnumber'];
            $end_cardnumber = $_POST['end_cardnumber'];
            $begin_cardnumber_arr = preg_split('@(\d+)@', $begin_cardnumber, -1, PREG_SPLIT_DELIM_CAPTURE);
            $begin_cardnumber_word = $begin_cardnumber_arr[0];
            $begin_cardnumber_number = $begin_cardnumber_arr[1];

            $end_cardnumber_arr = preg_split('@(\d+)@', $end_cardnumber, -1, PREG_SPLIT_DELIM_CAPTURE);
            $end_cardnumber_word = $end_cardnumber_arr[0];
            $end_cardnumber_number = $end_cardnumber_arr[1];
            if ($end_cardnumber_word != $begin_cardnumber_word) {
                $msg['sta'] = 0;
                $msg['msg'] = "开始卡号和结束卡号开头字母不一致";
                echo json_encode($msg);
                die;
            }
            if ($end_cardnumber_number < $begin_cardnumber_number) {
                $msg['sta'] = 0;
                $msg['msg'] = "结束卡号不可小于或等于开始卡号";
                echo json_encode($msg);
                die;
            }
            $error = 0;
            $errormsg = "";
            for ($i = $begin_cardnumber_number; $i <= $end_cardnumber_number; $i++) {
                $word = $end_cardnumber_word;
                $cardnumber = $end_cardnumber_word . $this->dispRepair($i, strlen($begin_cardnumber_number), '0');
                $find = Db::table('client_ycard')
                    ->where("cardnum='$cardnumber' and clientkeynum='$keynum' and customer_id='$customer_id' and kai_money=yu_money")->find();
                if (!$find) {
                    $error++;
                    $errormsg .= $cardnumber . "卡号不存在或不属于当前选择客户,或剩余余额不等于开卡金额<br />";
                } elseif ($find['status'] != '1' && $find['status'] != '3') {
                    $error++;
                    $errormsg .= $cardnumber . "卡号状态:" . ycard_status($find['status']) . " 不可退卡<br />";
                } else {
                    $list[] = $cardnumber;
                }
            }
        } else {
            $error = 0;
            $errormsg = "";
            $pucard_info = Db::table('client_ycard_pucard')
                ->where("pici_number='$sale_number' and clientkeynum='$keynum'")->find();
            if (empty($pucard_info)) {
                $msg['sta'] = 0;
                $msg['msg'] = "销售单号错误，销售单不存在";
                echo json_encode($msg);
                die;
            }
            $pucard_son_list = Db::table('client_ycard_pucard_son')
                ->where("pucard_id='" . $pucard_info["id"] . "' and clientkeynum='$keynum'")->select();
            foreach ($pucard_son_list as $key => $value) {
                $cardnum_arr = array_filter(explode(",", $value["cardnum"]));
                foreach ($cardnum_arr as $ck => $cv) {
                    $cv = trim($cv, "'");
                    $find = Db::table('client_ycard')
                        ->where("cardnum='$cv' and clientkeynum='$keynum' and customer_id='$customer_id' and kai_money=yu_money")
                        ->field("status")->find();
                    if (!$find) {
                        $error++;
                        $errormsg .= $cv . "卡号不存在或不属于当前选择客户,或剩余余额不等于开卡金额<br />";
                    } elseif ($find['status'] != '1' && $find['status'] != '3') {
                        $error++;
                        $errormsg .= $cv . "卡号状态:" . ycard_status($find['status']) . " 不可退卡<br />";
                    } else {
                        $list[] = $cv;
                    }
                }
            }
        }

        $cardtype = array();
        foreach ($list as $key => $value) {
            $cardtype_id = Db::table('client_ycard')
                ->where("cardnum='$value' and clientkeynum='$keynum' and customer_id='$customer_id'")->value("cardtype_id");
            $cardtype_info = Db::table('client_card_type')->where("id='$cardtype_id'")->find();
            if (empty($cardtype_info)) {
                $cardtype_info = array();
                $cardtype_info["name"] = "暂无";
                $cardtype_info["market_price"] = 0;
            }
            $cardtype[$cardtype_info["name"]]["num"] += 1;
            $cardtype[$cardtype_info["name"]]["info"] = $cardtype_info;
            $cardtype[$cardtype_info["name"]]["cardnum"] .= $value . "<br />";
        }
        $all_money = 0;
        foreach ($cardtype as $ck => &$cv) {
            $cv['cardnum'] = trim($cv['cardnum'], ",");
            $cv['all_money'] = $cv["num"] * $cv['info']['market_price'];
            $all_money += $cv['all_money'];
        }
        if ($error > 0) {
            $msg['sta'] = 0;
            $msg['msg'] = $errormsg;
            $msg['list'] = $list;
            $msg['cardtype'] = $cardtype;
            $msg['all_money'] = $all_money;
            echo json_encode($msg);
            die;
        } else {
            $msg['sta'] = 1;
            $msg['msg'] = "生成成功";
            $msg['list'] = $list;
            $msg['cardtype'] = $cardtype;
            $msg['all_money'] = $all_money;
            echo json_encode($msg);
            die;
        }
    }

    public function ajax_tui_card()
    {
        check_auth(request()->controller() . '/tui_card', 0);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $request = Request::instance();
        $param = $request->param();
        $customer_id = $param["customer_id"];
        $sale_id = Db::table('card_customer')->where("id='$customer_id'")->value("sale_id");

        $cardnumberlist = array_filter(explode(',', $param['cardnumberlist']));
        $cardnum_str = "";
        foreach ($cardnumberlist as $key => $value) {
            $cardnum_str .= "'" . $value . "'" . ",";
        }
        $cardnum_str = trim($cardnum_str, ",");
        $caount = count($cardnumberlist);

        Db::startTrans();
        try {
            // 执行开卡操作 只有未开卡或已关卡的可以开卡
            // 生成开卡主表
            $num = mt_rand(1000, 9999);
            $prince_number = "yq" . date("Ymd") . "$num";
            $add_tuicard['opeartor'] = session('cn_accountinfo.accountname');
            $add_tuicard['piciname'] = $param["piciname"];
            $add_tuicard['remark'] = $param["content"];
            $add_tuicard['pici_number'] = $prince_number;
            $add_tuicard['operator_time'] = time();
            $add_tuicard['open_num'] = $caount;
            $add_tuicard['clientkeynum'] = $basekeynum;
            $add_tuicard['customer_id'] = $customer_id;
            $add_tuicard['sale_id'] = $sale_id;
            $add_tuicard["cardnum"] = $cardnum_str;
            $add_tuicard["real_tui_money"] = $param["real_tui_money"];
            $add_tuicard["uncleared_money"] = $param["real_tui_money"];
            $add_tuicard["type"] = 2;
            $tuicard_id = Db::table('client_ycard_pucard')->insertGetId($add_tuicard);
            // 修改卡号范围
            $update = array();
            $update['status'] = 2;
            $update['begin_dui'] = "";
            $update['end_dui'] = "";
            $update['is_del'] = 1;
            $update['tui_id'] = $tuicard_id;
            $update['customer_id'] = "";
            $update['sale_id'] = "";
            $update['cardtype_id'] = "";
            $update['kai_money'] = "";
            $update['yu_money'] = "";
            $update['kai_exchange_num'] = "";
            $update['yu_exchange_num'] = "";
            Db::table('client_ycard')
                ->where("clientkeynum='$basekeynum' and (status='3' or  status='1') and cardnum in ($cardnum_str)")
                ->update($update);
            // 生成卡号日志
            $add_ycard_log_all = array();
            foreach ($cardnumberlist as $ck => $cv) {
                $add_ycard_log["cardnum"] = $cv;
                $add_ycard_log["action"] = "退卡";
                $add_ycard_log["operator"] = session('cn_accountinfo.accountname');
                $add_ycard_log["operator_time"] = time();
                $add_ycard_log["content"] = "后台人工退卡";
                $add_ycard_log["clientkeynum"] = $basekeynum;
                $add_ycard_log_all[] = $add_ycard_log;
            }
            Db::table('client_ycard_log')->insertAll($add_ycard_log_all);

            /**
             * 同步财务记录
             * @date 2023-4-25
             * <AUTHOR>
             */
            //            if ($param['is_record_finance'] == 1) {
            //                $finance_where = [
            //                    'basekeynum' => ['=', $basekeynum],
            //                    'balance' => ['>', 0]
            //                ];
            //                $finance_list = FinanceModel::getListByCustomerId($customer_id, $finance_where);
            //                $operator = session('cn_accountinfo.accountname');
            //                if (empty($finance_list)) throw new \Exception('该客户已无可用余额！');
            //                $total_price = $param["real_tui_money"];
            //                foreach ($finance_list as $v) {
            //                    $diff_price = $v['balance'] - $total_price;
            //                    if ($diff_price > 0){
            //                        // 可一次性结清
            //                        $finance_data = [
            //                            'basekeynum' => $basekeynum,
            //                            'finance_id' => $v['id'],
            //                            'no' => $prince_number,
            //                            'operator' => $operator,
            //                            'money' => $total_price,
            //                            'status' => 2,
            //                            'remarks' => '退卡自动记录',
            //                            'add_time' => date('Y-m-d H:i:s'),
            //                        ];
            //                        FinanceDetailModel::add($finance_data);
            //                        $v['balance'] -= $total_price;
            //                        $v['income'] += $total_price;
            //                        $v->save();
            //                        break;
            //                    } else {
            //                        $finance_data = [
            //                            'basekeynum' => $basekeynum,
            //                            'finance_id' => $v['id'],
            //                            'no' => $prince_number,
            //                            'operator' => $operator,
            //                            'money' => $v['balance'],
            //                            'status' => 2,
            //                            'remarks' => '退卡自动记录',
            //                            'add_time' => date('Y-m-d H:i:s'),
            //                        ];
            //                        FinanceDetailModel::add($finance_data);
            //                        $total_price -= $v['balance'];
            //                        $v['balance'] = 0;
            //                        $v['income'] += $v['balance'];
            //                        $v->save();
            //                    }
            //                }
            //            }

            /**
             * 记录卡状态变更
             * @date 2023-4-28
             * <AUTHOR>
             */
            $change_no = CardStatusChange::createNo();
            // 插入卡状态变更表
            $data = [
                'basekeynum'        => $basekeynum,
                'no'                => $change_no,
                'customer_id'       => $customer_id,
                'sale_id'           => $sale_id,
                'status'            => 2,
                'cardnum'           => $cardnum_str,
                'operator'          => session('cn_accountinfo.accountname'),
                'remarks'           => $add_tuicard['remark'],
                'add_time'          => date('Y-m-d H:i:s'),
            ];

            $id = CardStatusChange::add($data);

            if (!$id) throw new \Exception('记录卡状态更新失败');

            $card_list = ClientYcardModel::getListByCardNumbers($cardnumberlist);
            // 预设批量插入卡号状态更新记录列表
            $card_status_change_list = [];
            foreach ($card_list as $v) {
                $card_status_change_list[] = [
                    'basekeynum'    => $basekeynum,
                    'change_no'     => $change_no,
                    'card_id'       => $v['id'],
                    'customer_id'   => $v['customer_id'],
                    'sale_id'       => $v['sale_id'],
                    'operator'      => session('cn_accountinfo.accountname'),
                    'title'         => '退卡操作自动更新',
                    'type'          => 2,
                    'remarks'       => '退卡操作自动更新',
                    'add_time'      => date('Y-m-d H:i:s'),
                ];
            }

            $change_status_result = CardStatusChangeLogModel::batchAdd($card_status_change_list);
            if (!$change_status_result) throw new \Exception('记录状态变更失败！');

            // 提交事务
            Db::commit();
            $rt["sta"] = 1;
            $rt["msg"] = "操作成功";
            echo json_encode($rt);
            die;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = $e->getMessage();
            $rt["sta"] = 0;
            $rt["msg"] = "操作失败:" . $msg;
            echo json_encode($rt);
            die;
        }
    }

    public function tui_card_list()
    {
        $request = Request::instance();
        $param = $request->param();
        $keynum = session("cn_accountinfo.basekeynum");
        if (!empty($param["ajax"]) && $param["ajax"] == "ajax") {
            //权限校验,第二个参数是否ajax,1是ajax，0不是ajax
            check_auth(request()->controller() . '/tui_card_list', 1);
            $p = $param['page'] ? $param['page'] : 1;
            $pagesize = $param['limit'];
            $offset = $pagesize * ($p - 1); //计算记录偏移量
            $where = "clientkeynum='$keynum' and type='2'";
            $field = isset($param['field']) ? $param['field'] : '';
            $keyword = isset($param['keyword']) ? $param['keyword'] : '';
            $start_time_2 = isset($param['start_time_2']) ? $param['start_time_2'] : '';
            $end_time_2 = isset($param['end_time_2']) ? $param['end_time_2'] : '';
            if (!empty($start_time_2) and !empty($end_time_2)) {
                $start_time_2 = strtotime($start_time_2);
                $end_time_2 = strtotime($end_time_2);
                $where .= " and operator_time>$start_time_2 and operator_time<=$end_time_2";
            }
            if ($field == 'custom') {
                $where .= " and custom like '%$keyword%'";
            } elseif ($field == 'market') {
                $where .= " and market like '%$keyword%'";
            } elseif ($field == 'cardnum') {
                $tui_id = Db::table('client_ycard')
                    ->where("cardnum like '%$keyword%' and clientkeynum='$keynum'")->value("tui_id");
                $where .= " and id='$tui_id'";
            }
            session("tui_card_list_where", $where);
            $count = Db::table('client_ycard_pucard')
                ->where($where)
                ->count();
            $list = Db::table('client_ycard_pucard')
                ->where($where)
                ->order("operator_time desc")
                ->limit($offset . ',' . $pagesize)
                ->select();
            foreach ($list as $k => $v) {
                $list[$k]['customer_name'] = Db::table('card_customer')
                    ->where("id='" . $v['customer_id'] . "' and clientkeynum='$keynum'")->value("name");
                $list[$k]['sale_name'] = Db::table('card_sale')
                    ->where("id='" . $v['sale_id'] . "' and clientkeynum='$keynum'")->value("name");
                $list[$k]['operator_time'] = date('Y-m-d H:i:s', $v['operator_time']);
            }
            $rtdata["data"] = $list;
            $rtdata["count"] = $count;
            $rtdata["code"] = 0;
            $rtdata["msg"] = "";
            echo json_encode($rtdata);
        } else {
            //权限校验,第二个参数是否ajax,1是ajax，0不是ajax
            check_auth(request()->controller() . '/tui_card_list', 0);
            return $this->fetch('tui_card_list');
        }
    }

    public function export_tui_card_list()
    {
        $keynum = session("cn_accountinfo.basekeynum");
        $param = Request::instance()->param();
        $where = session("tui_card_list_where");
        $count = Db::table('client_ycard_pucard')
            ->where($where)
            ->count();
        if ($count > 10000) {
            error_tips("单次最大可导出1万条数据,请缩小搜索范围");
            die;
        }
        $daochu[] = array(
            '批次名称', '批次号', '客户', '销售', '退卡数量', '实退金额', '操作人员', '操作时间', '备注',
        );
        $client_card = Db::table('client_ycard_pucard')
            ->where($where)
            ->order('operator_time desc')
            ->select();
        foreach ($client_card as $k => $v) {
            $client_card[$k]['customer_name'] = Db::table('card_customer')
                ->where("id='" . $v['customer_id'] . "' and clientkeynum='$keynum'")->value("name");
            $client_card[$k]['sale_name'] = Db::table('card_sale')
                ->where("id='" . $v['sale_id'] . "' and clientkeynum='$keynum'")->value("name");
            $r[1] = $client_card[$k]["piciname"];
            $r[2] = $client_card[$k]["prince_number"];
            $r[11] = $client_card[$k]["customer_name"];
            $r[12] = $client_card[$k]["sale_name"];
            $r[13] = $client_card[$k]["open_num"];
            $r[13] = $client_card[$k]["real_tui_money"];
            $r[3] = $client_card[$k]["operator"];
            $r[5] = $client_card[$k]["operator_time"];
            $r[4] = $client_card[$k]["remark"];
            $daochu[] = $r;
        }
        $xls_name = "退卡批次查询" . date('Y-m-d');

        // 使用SimpleExcelWriter导出
        try {
            SimpleExcelWriter::streamDownload($xls_name . '.xlsx')
                ->addRows($daochu)
                ->toBrowser();
        } catch (\Exception $e) {
            // 如果SimpleExcel不可用，使用原来的create_xls函数
            create_xls($daochu, $xls_name);
        }
    }


    public function export_tui_card_list_cardnum()
    {
        $keynum = session("cn_accountinfo.basekeynum");
        $param = Request::instance()->param();
        $where = session("tui_card_list_where");
        $count = Db::table('client_ycard_pucard')
            ->where($where)
            ->count();
        if ($count > 10000) {
            error_tips("单次最大可导出1万条数据,请缩小搜索范围");
            die;
        }
        $daochu[] = array(
            '批次名称', '批次号', '客户', '销售', '操作人员', '操作时间', '备注', '卡号', '实退金额'
        );
        $client_card = Db::table('client_ycard_pucard')
            ->where($where)
            ->order('operator_time desc')
            ->select();
        foreach ($client_card as $k => $v) {
            $client_card[$k]['customer_name'] = Db::table('card_customer')
                ->where("id='" . $v['customer_id'] . "' and clientkeynum='$keynum'")->value("name");
            $client_card[$k]['sale_name'] = Db::table('card_sale')
                ->where("id='" . $v['sale_id'] . "' and clientkeynum='$keynum'")->value("name");
            $cardnum = array_filter(explode(',', $v['cardnum']));
            foreach ($cardnum as $ck => $cv) {
                $cv = trim($cv, "'");
                $r = array();
                $r[] = $client_card[$k]["piciname"];
                $r[] = $client_card[$k]["prince_number"];
                $r[] = $client_card[$k]["customer_name"];
                $r[] = $client_card[$k]["sale_name"];
                $r[] = $client_card[$k]["operator"];
                $r[] = $client_card[$k]["operator_time"];
                $r[] = $client_card[$k]["remark"];
                $r[] = $cv;
                $r[] = round($client_card[$k]["real_tui_money"] / $client_card[$k]["open_num"], 2);
                $daochu[] = $r;
            }
        }
        $xls_name = "退卡批次查询_卡号" . date('Y-m-d');

        // 使用SimpleExcelWriter导出
        try {
            SimpleExcelWriter::streamDownload($xls_name . '.xlsx')
                ->addRows($daochu)
                ->toBrowser();
        } catch (\Exception $e) {
            // 如果SimpleExcel不可用，使用原来的create_xls函数
            create_xls($daochu, $xls_name);
        }
    }

    public function ajax_del_card_type_goods()
    {
        //权限校验, 第二个参数是否ajax, 1是ajax，0不是ajax
        check_auth(request()->controller() . '/cardtype_list', 0);
        $request = Request::instance();
        $param = $request->param();
        $basekeynum = session("cn_accountinfo.basekeynum");
        $ids = $param['ids'];
        $where["id"] = array('in', $ids);
        $where["clientkeynum"] = $basekeynum;
        $rs = Db::table('card_type_goods')->where($where)->delete();
        addoperatelog('删除卡型分类下商品', json_encode($param, JSON_UNESCAPED_UNICODE));
        if (!$rs) {
            $rt['sta'] = 0;
            $rt['msg'] = '删除失败';
            echo json_encode($rt);
            die;
        }
        $rt['sta'] = 1;
        $rt['msg'] = '删除成功';
        echo json_encode($rt);
        die;
    }

    public function dispRepair($str, $len, $msg, $type = '1')
    {
        $length = $len - strlen($str);
        if ($length < 1)
            return $str;
        if ($type == 1) {
            $str = str_repeat($msg, $length) . $str;
        } else {
            $str .= str_repeat($msg, $length);
        }
        return $str;
    }

    public function test()
    {
        $files = Db::table('client_ycard')->column("qrcode");
        zip_download($files);
    }

    /**
     * 验证下载密码
     * @return void
     */
    public function verify_download_password()
    {
        check_auth(request()->controller() . '/cardlist', 1);
        $request = Request::instance();
        $id = $request->param('id');
        $admin_password = $request->param('admin_password');

        if (empty($id)) {
            success(-1, '批次ID不能为空', [], 0);
            return;
        }

        if (empty($admin_password)) {
            success(-1, '请输入高级密码', [], 0);
            return;
        }

        // 验证批次是否存在
        $basekeynum = session('cn_accountinfo.basekeynum');
        $batch_info = Db::table('client_ycard_batch')
            ->where(['id' => $id, 'clientkeynum' => $basekeynum])
            ->find();

        if (empty($batch_info)) {
            success(-1, '批次信息不存在', [], 0);
            return;
        }


        $adminId = session('cn_accountinfo.keynum');
        $adminName = session('cn_accountinfo.accountname');
        $clientInfo = \think\Db::name('plat_client')->where('keynum', $adminId)->find();

        // 这里可以根据实际需求设置密码验证逻辑
        // 可以从配置文件、数据库或者固定密码中获取
        // 示例：从系统配置中获取高级密码
        // $system_password = config('admin.download_password', 'admin123'); // 默认密码

        // // 或者从数据库中获取当前登录用户的高级密码
        // $admin_info = session('cn_accountinfo');
        // if (isset($admin_info['download_password']) && !empty($admin_info['download_password'])) {
        //     $system_password = $admin_info['download_password'];
        // }

        if (!password_verify($admin_password, $clientInfo['super_password'])) {
            \think\facade\Log::error('管理员[' . $adminName . ']导出卡密批次时高级密码验证失败');
            return json(['code' => 1, 'msg' => '高级密码验证失败']);
        }

        // 验证成功
        success(0, '密码验证成功', ['batch_info' => $batch_info], 1);
    }

    /**
     * 储值卡等级修改页面
     * @return mixed
     */
    public function change_card_type()
    {
        check_auth(request()->controller() . '/change_card_type', 0);
        $customer_list = CardCustomer::getEnumByBaseKeyNum();
        $cardtype_list = CardType::getEnumByBaseKeyNum();
        $this->assign('customer_list', $customer_list);
        $this->assign('cardtype_list', $cardtype_list);
        return $this->fetch();
    }

    /**
     * 储值卡等级修改预览数据 api
     * @return void
     */
    public function get_card_type_change_preview()
    {
        check_auth(request()->controller() . '/change_card_type', 1);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $customer_id = Request::instance()->param('customer_id');
        $sale_number = Request::instance()->param('sale_number'); //销售单号
        $begin_cardnumber = Request::instance()->param('begin_cardnumber');
        $end_cardnumber = Request::instance()->param('end_cardnumber');

        // 拼接查询条件
        $where = [
            'clientkeynum' => $basekeynum,
            'customer_id' => $customer_id,
        ];

        if (!empty($sale_number)) {
            // 优先根据销售单号查询
            $pucard_info = ClientYcardPucardModel::getInfoByPiciNumber($sale_number, $where);
            if (empty($pucard_info)) fail(1, '未查询到销售信息');
            $card_list = ClientYcardModel::getListByPuId($pucard_info['id'], $where);

            if (!empty($card_list)) {
                success(0, '生成预览成功', $card_list);
            } else {
                fail(1, '未查询到卡信息！');
            }
        } else if (!empty($begin_cardnumber) && !empty($end_cardnumber)) {
            // 根据卡号范围查询
            $begin_cardnumber_arr = preg_split('@(\d+)@', $begin_cardnumber, -1, PREG_SPLIT_DELIM_CAPTURE);
            $begin_cardnumber_word = $begin_cardnumber_arr[0];
            $begin_cardnumber_number = $begin_cardnumber_arr[1];

            $end_cardnumber_arr = preg_split('@(\d+)@', $end_cardnumber, -1, PREG_SPLIT_DELIM_CAPTURE);
            $end_cardnumber_word = $end_cardnumber_arr[0];
            $end_cardnumber_number = $end_cardnumber_arr[1];

            if ($end_cardnumber_word != $begin_cardnumber_word) {
                fail(1, '开始卡号和结束卡号开头字母不一致');
            }
            if ($end_cardnumber_number < $begin_cardnumber_number) {
                fail(1, '结束卡号不可小于或等于开始卡号');
            }
            if ($end_cardnumber - $begin_cardnumber > 1000) {
                fail(1, '操作数据量太大，输入1000条数据以内的范围');
            }

            $cardnumber_arr = [];
            for ($i = $begin_cardnumber_number; $i <= $end_cardnumber_number; $i++) {
                $cardnumber_arr[] = $end_cardnumber_word . $this->dispRepair($i, strlen($begin_cardnumber_number), '0');
            }

            $card_list = ClientYcardModel::getListByCardNumbers($cardnumber_arr, $where);

            if (!empty($card_list)) {
                success(0, '生成预览成功', $card_list);
            } else {
                fail(1, '未查询到卡信息！');
            }
        } else {
            fail(1, '请求失败，请输入销售单号或输入卡号范围');
        }
    }

    /**
     * 执行储值卡等级修改 api
     * @return void
     */
    public function ajax_change_card_type()
    {
        check_auth(request()->controller() . '/change_card_type', 1);
        $params = Request::instance()->param();
        $basekeynum = session("cn_accountinfo.basekeynum");
        $operator = session('cn_accountinfo.accountname');
        $card_nums = $params['card_nums'];
        $customer_id = $params['customer_id'];
        $new_cardtype_id = $params['new_cardtype_id'];
        $remarks = $params['remarks'];

        if (empty($card_nums)) {
            fail(-1, '请选择要修改的卡号');
        }
        if (empty($new_cardtype_id)) {
            fail(-1, '请选择新的卡型');
        }

        $where = [
            'clientkeynum' => $basekeynum,
            'customer_id' => $customer_id,
        ];

        $card_list = ClientYcardModel::getListByCardNumbers(explode(',', $card_nums), $where);

        if (empty($card_list)) {
            fail(-1, '未查询到卡号信息');
        }

        // 检查卡状态是否可以修改
        $fail_list = [];
        foreach ($card_list as $v) {
            if (!in_array($v['status'], ['已销售', '已开卡'])) {
                $fail_list[] = $v['cardnum'];
            }
        }

        if (!empty($fail_list)) {
            fail(1, '卡号 ' . implode(',', $fail_list) . ' 当前状态不可修改等级');
        }

        try {
            Db::startTrans();
            $no = CardTypeChangeLog::createNo();

            // 记录主表
            $main_data = [
                'basekeynum' => $basekeynum,
                'no' => $no,
                'cardnum' => $card_nums,
                'new_cardtype_id' => $new_cardtype_id,
                'operator' => $operator,
                'customer_id' => $customer_id,
                'remarks' => $remarks,
                'add_time' => date('Y-m-d H:i:s'),
            ];

            // 记录详细表数据
            $detail_data = [];
            $update_ids = [];

            foreach ($card_list as $v) {
                $main_data['old_cardtype_id'] = $v['cardtype_id']; // 设置原卡型ID

                $detail_data[] = [
                    'basekeynum' => $basekeynum,
                    'change_no' => $no,
                    'card_id' => $v['id'],
                    'cardnum' => $v['cardnum'],
                    'old_cardtype_id' => $v['cardtype_id'],
                    'new_cardtype_id' => $new_cardtype_id,
                    'customer_id' => $customer_id,
                    'operator' => $operator,
                    'remarks' => $remarks,
                    'add_time' => date('Y-m-d H:i:s'),
                ];

                $update_ids[] = $v['id'];
            }

            // 插入主记录
            $res = CardTypeChangeLog::add($main_data);
            if (!$res) throw new \Exception('新增等级修改记录失败');

            // 插入详细记录
            $detail_res = CardTypeChangeDetailLog::batchAdd($detail_data);
            if (!$detail_res) throw new \Exception('新增等级修改详细记录失败');

            // 更新储值卡的卡型
            $where_update = [['id', 'in', $update_ids]];
            $update_res = Db::table('client_ycard')
                ->where($where_update)
                ->update(['cardtype_id' => $new_cardtype_id, 'update_time' => time()]);

            if (!$update_res) throw new \Exception('更新卡等级失败');

            Db::commit();
            success(0, '修改成功！');
        } catch (\Exception $e) {
            Db::rollback();
            Log::error($e->getMessage());
            Log::error('error file:' . $e->getFile() . ' line:' . $e->getLine());
            fail(-1, '请求失败：' . $e->getMessage());
        }
    }

    /**
     * 储值卡等级修改记录列表页面
     * @return mixed
     */
    public function card_type_change_list()
    {
        check_auth(request()->controller() . '/card_type_change_list', 0);
        return $this->fetch();
    }

    /**
     * 储值卡等级修改记录列表 api
     * @return void
     */
    public function ajax_card_type_change_list()
    {
        check_auth(request()->controller() . '/card_type_change_list', 1);
        $params = Request::instance()->param();
        $basekeynum = session("cn_accountinfo.basekeynum");
        $p = $params['page'] ?: 1;
        $pagesize = $params['limit'];
        $start_time = $params['start_time'];
        $end_time = $params['end_time'];
        $operator = $params['operator'];
        $customer = $params['customer'];

        $where['basekeynum'] = $basekeynum;

        if (!empty($start_time)) {
            $where['add_time'] = ['>=', $start_time];
        }

        if (!empty($end_time)) {
            $where['add_time'] = ['<=', $end_time . ' 23:59:59'];
        }

        if (!empty($operator)) {
            $where['operator'] = ['like', '%' . $operator . '%'];
        }

        if (!empty($customer)) {
            // 通过客户名称找到客户ID
            $customer_info = CardCustomer::where('name', 'like', '%' . $customer . '%')
                ->where('clientkeynum', $basekeynum)
                ->find();
            if ($customer_info) {
                $where['customer_id'] = $customer_info['id'];
            } else {
                $where['customer_id'] = -1; // 找不到客户则返回空结果
            }
        }

        $list = CardTypeChangeLog::getList($where, $p, $pagesize);
        $count = CardTypeChangeLog::getCount($where);

        success(0, '请求成功', $list, $count);
    }

    /**
     * 储值卡等级修改记录详情页面
     * @return mixed
     */
    public function card_type_change_detail()
    {
        check_auth(request()->controller() . '/card_type_change_list', 0);
        $params = Request::instance()->param();
        $no = $params['no'];
        if (empty($no)) fail();
        $this->assign('no', $no);
        return $this->fetch();
    }

    /**
     * 储值卡等级修改记录详情 api
     * @return void
     */
    public function ajax_card_type_change_detail()
    {
        check_auth(request()->controller() . '/card_type_change_list', 1);
        $params = Request::instance()->param();
        $no = $params['no'];
        $page = $params['page'] ?: 1;
        $pagesize = $params['limit'] ?: 20;

        if (empty($no)) {
            fail(-1, '批次号不能为空');
        }

        $list = CardTypeChangeDetailLog::getDetailByChangeNo($no);
        $count = count($list);

        // 分页处理
        $offset = ($page - 1) * $pagesize;
        $list = array_slice($list, $offset, $pagesize);

        success(0, '请求成功', $list, $count);
    }

    /**
     * 导出储值卡等级修改记录
     * @return void
     */
    public function export_card_type_change()
    {
        check_auth(request()->controller() . '/card_type_change_list', 0);
        $basekeynum = session("cn_accountinfo.basekeynum");
        $params = Request::instance()->param();
        $operator = $params['operator'];
        $customer = $params['customer'];
        $start_time = $params['start_time'];
        $end_time = $params['end_time'];

        $where['basekeynum'] = $basekeynum;

        if (!empty($operator)) {
            $where['operator'] = ['like', '%' . $operator . '%'];
        }

        if (!empty($customer)) {
            $customer_info = CardCustomer::where('name', 'like', '%' . $customer . '%')
                ->where('clientkeynum', $basekeynum)
                ->find();
            if ($customer_info) {
                $where['customer_id'] = $customer_info['id'];
            }
        }

        if (!empty($start_time)) {
            $where['add_time'] = ['>=', $start_time];
        }

        if (!empty($end_time)) {
            $where['add_time'] = ['<=', $end_time . ' 23:59:59'];
        }

        $list = CardTypeChangeLog::getList($where, 1, 10000);

        $export[] = array(
            '批次单号', '客户', '原卡型', '新卡型', '操作员', '操作时间', '备注'
        );

        foreach ($list as $v) {
            $result = [];
            $result[1] = $v["no"];
            $result[2] = $v["customer_name"];
            $result[3] = $v["old_cardtype_name"];
            $result[4] = $v["new_cardtype_name"];
            $result[5] = $v["operator"];
            $result[6] = $v["add_time"];
            $result[7] = $v["remarks"];
            $export[] = $result;
        }

        $xls_name = "储值卡等级修改记录" . date('Y-m-d');

        // 使用更现代的导出方式
        try {
            $writer = SimpleExcelWriter::create($xls_name . '.xlsx');
            foreach ($export as $row) {
                $writer->addRow($row);
            }
            $writer->toBrowser();
        } catch (\Exception $e) {
            // 如果SimpleExcel不可用，使用原来的方式
            create_xls($export, $xls_name);
        }
    }

    /**
     * 导出储值卡等级修改详情记录
     * @return void
     */
    public function export_card_type_change_detail()
    {
        check_auth(request()->controller() . '/card_type_change_list', 0);
        $params = Request::instance()->param();
        $no = $params['no'];

        if (empty($no)) fail();

        $info = CardTypeChangeLog::getInfoByNo($no);
        if (empty($info)) fail(-1, '数据不存在或已被删除');

        $list = CardTypeChangeDetailLog::getDetailByChangeNo($no);

        $export[] = array(
            '批次单号', '卡号', '客户', '原卡型', '新卡型', '操作员', '操作时间', '备注'
        );

        foreach ($list as $v) {
            $result = [];
            $result[1] = $info["no"];
            $result[2] = $v["cardnum"];
            $result[3] = $info["customer_name"];
            $result[4] = $v["old_cardtype_name"];
            $result[5] = $v["new_cardtype_name"];
            $result[6] = $v["operator"];
            $result[7] = $v["add_time"];
            $result[8] = $v["remarks"];
            $export[] = $result;
        }

        $xls_name = "储值卡等级修改记录详情" . date('Y-m-d');

        // 使用更现代的导出方式
        try {
            $writer = SimpleExcelWriter::create($xls_name . '.xlsx');
            foreach ($export as $row) {
                $writer->addRow($row);
            }
            $writer->toBrowser();
        } catch (\Exception $e) {
            // 如果SimpleExcel不可用，使用原来的方式
            create_xls($export, $xls_name);
        }
    }

    /**
     * 获取卡等级列表
     * @return void
     */
    public function get_card_levels()
    {
        check_auth(request()->controller() . '/cardstatus', 1);
        $keynum = session("cn_accountinfo.basekeynum");

        $levels = Db::table('client_card_level')
            ->where('clientkeynum', $keynum)
            ->field('id,title')
            ->select();

        success(0, '获取成功', $levels);
    }

    /**
     * 更新单张卡的等级
     * @return void
     */
    public function update_card_level()
    {
        check_auth(request()->controller() . '/cardstatus', 1);
        $keynum = session("cn_accountinfo.basekeynum");
        $request = Request::instance();
        $param = $request->param();

        $card_id = $param['card_id'];
        $cardnum = $param['cardnum'];
        $new_level_id = $param['new_level_id'];
        $reason = $param['reason'];

        if (empty($card_id) || empty($cardnum) || empty($new_level_id)) {
            fail(1, '参数不完整');
        }

        try {
            Db::startTrans();

            // 获取卡片信息
            $card_info = Db::table('client_ycard')
                ->where('id', $card_id)
                ->where('clientkeynum', $keynum)
                ->find();

            if (!$card_info) {
                throw new \Exception('卡片不存在');
            }

            // 获取新等级信息
            $new_level_info = Db::table('client_card_level')
                ->where('id', $new_level_id)
                ->where('clientkeynum', $keynum)
                ->find();

            if (!$new_level_info) {
                throw new \Exception('卡等级不存在');
            }

            // 获取旧等级信息
            $old_level_info = null;
            if (!empty($card_info['card_level_id'])) {
                $old_level_info = Db::table('client_card_level')
                    ->where('id', $card_info['card_level_id'])
                    ->find();
            }

            // 更新卡片等级
            $update_result = Db::table('client_ycard')
                ->where('id', $card_id)
                ->update([
                    'card_level_id' => $new_level_id,
                    'update_time' => time()
                ]);

            if (!$update_result) {
                throw new \Exception('更新卡等级失败');
            }

            // 记录操作日志
            $log_data = [
                'cardnum' => $cardnum,
                'action' => '修改卡等级',
                'operator' => session("cn_accountinfo.name"),
                'operator_time' => time(),
                'content' => "将卡等级从'" . ($old_level_info ? $old_level_info['title'] : '无') . "'修改为'" . $new_level_info['title'] . "'，原因：" . $reason,
                'clientkeynum' => $keynum
            ];

            $log_result = Db::table('client_ycard_log')->insert($log_data);
            if (!$log_result) {
                throw new \Exception('记录操作日志失败');
            }

            Db::commit();
            success(0, '修改成功');

        } catch (\Exception $e) {
            Db::rollback();
            fail(1, $e->getMessage());
        }
    }

    /**
     * 延期单张卡
     * @return void
     */
    public function extend_card_time()
    {
        check_auth(request()->controller() . '/cardstatus', 1);
        $keynum = session("cn_accountinfo.basekeynum");
        $request = Request::instance();
        $param = $request->param();

        $card_id = $param['card_id'];
        $cardnum = $param['cardnum'];
        $new_end_time = $param['new_end_time'];
        $reason = $param['reason'];

        if (empty($card_id) || empty($cardnum) || empty($new_end_time)) {
            fail(1, '参数不完整');
        }

        // 验证新的到期时间格式
        $new_end_timestamp = strtotime($new_end_time);
        if (!$new_end_timestamp) {
            fail(1, '日期格式不正确');
        }

        try {
            Db::startTrans();

            // 获取卡片信息
            $card_info = Db::table('client_ycard')
                ->where('id', $card_id)
                ->where('clientkeynum', $keynum)
                ->find();

            if (!$card_info) {
                throw new \Exception('卡片不存在');
            }

            // 检查新的到期时间是否合理
            if ($new_end_timestamp <= $card_info['end_dui']) {
                throw new \Exception('新的到期时间必须大于当前到期时间');
            }

            // 更新卡片到期时间
            $update_result = Db::table('client_ycard')
                ->where('id', $card_id)
                ->update([
                    'end_dui' => $new_end_timestamp,
                    'update_time' => time()
                ]);

            if (!$update_result) {
                throw new \Exception('更新到期时间失败');
            }

            // 生成延期单号
            $delay_no = 'YQ' . date('YmdHis') . rand(1000, 9999);

            // 记录延期日志
            $log_data = [
                'no' => $delay_no,
                'cardnum' => $cardnum,
                'operator' => session("cn_accountinfo.name"),
                'delay_time' => $new_end_time,
                'remarks' => "将到期时间从'" . date('Y-m-d', $card_info['end_dui']) . "'延期至'" . $new_end_time . "'，原因：" . $reason,
                'basekeynum' => $keynum,
                'add_time' => date('Y-m-d H:i:s')
            ];

            $log_result = Db::table('client_card_delay_log')->insert($log_data);
            if (!$log_result) {
                throw new \Exception('记录延期日志失败');
            }

            Db::commit();
            success(0, '延期成功');

        } catch (\Exception $e) {
            Db::rollback();
            fail(1, $e->getMessage());
        }
    }

    /**
     * 余额清零
     * @return void
     */
    public function clear_card_balance()
    {
        check_auth(request()->controller() . '/cardstatus', 1);
        $keynum = session("cn_accountinfo.basekeynum");
        $request = Request::instance();
        $param = $request->param();

        $card_id = $param['card_id'];
        $cardnum = $param['cardnum'];
        $reason = $param['reason'];

        if (empty($card_id) || empty($cardnum) || empty($reason)) {
            fail(1, '参数不完整');
        }

        try {
            Db::startTrans();

            // 获取卡片信息
            $card_info = Db::table('client_ycard')
                ->where('id', $card_id)
                ->where('clientkeynum', $keynum)
                ->find();

            if (!$card_info) {
                throw new \Exception('卡片不存在');
            }

            // 检查卡片状态是否允许清零
            if (!in_array($card_info['status'], [1, 3])) { // 1=已销售, 3=已开卡
                throw new \Exception('当前卡片状态不允许余额清零操作');
            }

            // 记录原始余额
            $original_balance = $card_info['yu_money'];

            if ($original_balance <= 0) {
                throw new \Exception('当前余额已为零，无需清零操作');
            }

            // 更新卡片余额为0
            $update_result = Db::table('client_ycard')
                ->where('id', $card_id)
                ->update([
                    'yu_money' => 0,
                    'update_time' => time()
                ]);

            if (!$update_result) {
                throw new \Exception('余额清零操作失败');
            }

            // 记录清零日志到卡使用记录表
            $clear_log_data = [
                'cardnum' => $cardnum,
                'basekeynum' => $keynum,
                'use_money' => $original_balance,
                'status' => 1,
                // 'operator' => session("cn_accountinfo.accountname"),
                'remark' => '余额清零：' . $reason . '|' . session("cn_accountinfo.accountname"),
                'add_time' => date('Y-m-d H:i:s'),
                'member_id' => $card_info['member_id'] ?? 0,
            ];

            $log_result = Db::table('client_card_use_log')->insert($clear_log_data);
            if (!$log_result) {
                throw new \Exception('记录清零日志失败');
            }

            // 记录操作日志到卡操作日志表
            $operation_log_data = [
                'cardnum' => $cardnum,
                'action' => '余额清零',
                'operator' => session("cn_accountinfo.accountname"),
                'operator_time' => time(),
                'content' => "将余额从 {$original_balance} 元清零，原因：{$reason}",
                'clientkeynum' => $keynum
            ];

            $operation_log_result = Db::table('client_ycard_log')->insert($operation_log_data);
            if (!$operation_log_result) {
                throw new \Exception('记录操作日志失败');
            }

            Db::commit();
            success(0, '余额清零成功');

        } catch (\Exception $e) {
            Db::rollback();
            fail(1, $e->getMessage());
        }
    }

    /**
     * 获取卡使用记录页面
     */
    public function get_card_usage_record()
    {
        $card_id = request()->param('card_id');
        $cardnum = request()->param('cardnum');

        if (empty($card_id) || empty($cardnum)) {
            $this->error('参数错误');
        }

        // 获取卡信息
        $basekeynum = session('cn_accountinfo.basekeynum');
        $card_info = Db::table('client_ycard')
            ->where(['id' => $card_id, 'clientkeynum' => $basekeynum])
            ->find();

        if (empty($card_info)) {
            $this->error('卡信息不存在');
        }

        $this->assign('card_info', $card_info);
        $this->assign('card_id', $card_id);
        $this->assign('cardnum', $cardnum);

        return $this->fetch('card_usage_record');
    }

    /**
     * Ajax获取卡使用记录列表
     */
    public function ajax_get_card_usage_record()
    {
        $card_id = request()->param('card_id');
        $cardnum = request()->param('cardnum');
        $page = request()->param('page', 1);
        $limit = request()->param('limit', 15);

        if (empty($card_id) || empty($cardnum)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        $basekeynum = session('cn_accountinfo.basekeynum');

        try {
            // 查询client_card_use_log表获取卡使用记录
            $query = Db::table('client_card_use_log')
                ->where('cardnum', $cardnum)
                ->where('basekeynum', $basekeynum)
                ->field('id, order_sn, yu_money, after_money, use_money, status, add_time, remark')
                ->order('add_time', 'desc');

            // 获取总记录数
            $total = $query->count();

            // 分页查询
            $offset = ($page - 1) * $limit;
            $records = $query->limit($offset, $limit)->select();

            // 格式化数据
            $data = [];
            foreach ($records as $record) {
                $data[] = [
                    'time' => $record['add_time'] ? date('Y-m-d H:i:s', strtotime($record['add_time'])) : '',
                    'type' => '卡消费',
                    'order_no' => $record['order_sn'] ?: '--',
                    'amount' => $record['use_money'] ? '-' . number_format($record['use_money'], 2) : '--',
                    'yu_money' => $record['yu_money'] ? number_format($record['yu_money'], 2) : '0.00',
                    'after_money' => $record['after_money'] ? number_format($record['after_money'], 2) : '0.00',
                    'status' => $record['status'] == 1 ? '已支付' : '退款',
                    'remark' => $record['remark'] ?: '卡消费'
                ];
            }

            return json([
                'code' => 0,
                'msg' => 'success',
                'count' => $total,
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }
}
