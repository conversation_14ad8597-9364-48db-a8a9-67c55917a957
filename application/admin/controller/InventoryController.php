<?php
namespace app\admin\controller;

use app\admin\model\InventoryOrder;
use app\admin\model\InventoryOrderDetail;
use app\admin\model\InventoryLog;
use think\Request;
use think\Db;
use think\facade\Loader;
use Spatie\SimpleExcel\SimpleExcelWriter;

/**
 * 库存管理控制器
 * Class InventoryController
 * @package app\admin\controller
 */
class InventoryController extends CnController
{
    /**
     * 入库单列表页面
     */
    public function index()
    {
        return $this->fetch();
    }

    /**
     * 出库单列表页面
     */
    public function outstock()
    {
        return $this->fetch();
    }

    /**
     * 获取入库单列表
     */
    public function getInStockList()
    {
        // 获取请求参数
        $page = input('page', 1, 'intval');
        $limit = input('limit', 15, 'intval');
        $order_no = input('order_no', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $date_range = input('date_range', '');
        $is_manual = input('is_manual', '');

        // 构建查询条件
        $where = [];
        $where[] = ['order_type', '=', 1]; // 只查询入库单
        if (!empty($order_no)) {
            $where[] = ['order_no', 'like', "%{$order_no}%"];
        }
        if (!empty($business_type)) {
            $where[] = ['business_type', '=', $business_type];
        }
        if ($status !== '') {
            $where[] = ['status', '=', $status];
        }
        if (!empty($date_range)) {
            $dates = explode(' - ', $date_range);
            if (count($dates) == 2) {
                $where[] = ['created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
            }
        }
        // 添加手动录入筛选条件
        if ($is_manual === '1') {
            $where[] = ['related_order_no', 'null'];
        } elseif ($is_manual === '0') {
            $where[] = ['related_order_no', 'not null'];
        }

        // 获取列表数据
        $model = new InventoryOrder();
        $data = $model->getList($where, $page, $limit);

        // 处理数据
        foreach ($data['list'] as &$item) {
            // 格式化单据类型
            $item['order_type_text'] = $this->getOrderTypeText($item['order_type']);

            // 格式化业务类型
            $item['business_type_text'] = $this->getBusinessTypeText($item['order_type'], $item['business_type']);

            // 格式化状态
            $item['status_text'] = $this->getStatusText($item['status']);

            // 查询店铺
            $shop = Db::name('shop')->where('id', $item['shop_id'])->find();
            $item['shop_name'] = $shop['title'];
        }

        return json(['code' => 0, 'msg' => 'success', 'count' => $data['count'], 'data' => $data['list']]);
    }

    /**
     * 获取出库单列表
     */
    public function getOutStockList()
    {
        // 获取请求参数
        $page = input('page', 1, 'intval');
        $limit = input('limit', 15, 'intval');
        $order_no = input('order_no', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $date_range = input('date_range', '');
        $is_manual = input('is_manual', '');

        // 构建查询条件
        $where = [];
        $where[] = ['order_type', '=', 2]; // 查询出库单和销售出库单
        if (!empty($order_no)) {
            $where[] = ['order_no', 'like', "%{$order_no}%"];
        }
        if (!empty($business_type)) {
            $where[] = ['business_type', '=', $business_type];
        }
        if ($status !== '') {
            $where[] = ['status', '=', $status];
        }
        if (!empty($date_range)) {
            $dates = explode(' - ', $date_range);
            if (count($dates) == 2) {
                $where[] = ['created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
            }
        }
        // 添加手动录入筛选条件
        if ($is_manual === '1') {
            $where[] = ['related_order_no', 'null'];
        } elseif ($is_manual === '0') {
            $where[] = ['related_order_no', 'not null'];
        }

        // 获取列表数据
        $model = new InventoryOrder();
        $data = $model->getList($where, $page, $limit);

        // 处理数据
        foreach ($data['list'] as &$item) {
            // 格式化单据类型
            $item['order_type_text'] = $this->getOrderTypeText($item['order_type']);

            // 格式化业务类型
            $item['business_type_text'] = $this->getBusinessTypeText($item['order_type'], $item['business_type']);

            // 格式化状态
            $item['status_text'] = $this->getStatusText($item['status']);

            // 查询店铺
            $shop = Db::name('shop')->where('id', $item['shop_id'])->find();
            $item['shop_name'] = $shop['title'];
        }

        return json(['code' => 0, 'msg' => 'success', 'count' => $data['count'], 'data' => $data['list']]);
    }

    /**
     * 添加入库单页面
     */
    public function addInStock()
    {
        if (request()->isPost()) {
            // 获取表单数据
            $data = input('post.');

            // 参数验证
            if (empty($data['order_type']) || $data['order_type'] != 1) {
                return json(['code' => 1, 'msg' => '单据类型错误']);
            }
            if (empty($data['business_type'])) {
                return json(['code' => 1, 'msg' => '请选择业务类型']);
            }
            if (empty($data['supplier_name'])) {
                return json(['code' => 1, 'msg' => '请输入供应商名称']);
            }
            if (empty($data['shop_id'])) {
                return json(['code' => 1, 'msg' => '请选择所属门店']);
            }
            if (empty($data['details']) || !is_array($data['details'])) {
                return json(['code' => 1, 'msg' => '请添加商品明细']);
            }

            // 检查商品是否都有商品ID
            $productIds = [];
            foreach ($data['details'] as $detail) {
                if (empty($detail['product_id'])) {
                    return json(['code' => 1, 'msg' => '请选择商品']);
                }
                if (empty($detail['quantity']) || $detail['quantity'] <= 0) {
                    return json(['code' => 1, 'msg' => '商品数量必须大于0']);
                }
                
                // 检查重复商品规格
                $inventoryKey = $detail['product_id'] . '_' . $detail['inventory_id'];
                if (in_array($inventoryKey, $productIds)) {
                    return json(['code' => 1, 'msg' => '不允许添加重复的商品规格，请合并数量']);
                }
                $productIds[] = $inventoryKey;
            }

            // 设置相关字段
            $data['clientkeynum'] = session('cn_accountinfo.basekeynum');
            $data['created_by'] = session('cn_accountinfo.accountname');

            // 开启事务
            Db::startTrans();
            try {
                // 添加库存单
                $model = new InventoryOrder();
                $details = $data['details'];
                unset($data['select'], $data['details']);
                // 移除关联单号相关的处理
                if (isset($data['related_order_no'])) {
                    unset($data['related_order_no']);
                }

                $id = $model->add($data);
                if (!$id) {
                    throw new \Exception('添加库存单失败');
                }

                // 获取订单编号
                $order = $model->where('id', $id)->find();

                // 添加库存单明细
                $detailModel = new InventoryOrderDetail();
                $result = $detailModel->addBatch($id, $order['order_no'], $data['order_type'], $details, $data['shop_id'], '');
                if (!$result) {
                    throw new \Exception('添加库存单明细失败');
                }

                Db::commit();
                return json(['code' => 0, 'msg' => '添加成功', 'id' => $id]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // 获取门店列表
        $shops = Db::name('shop')->field('id, title')->select();

        // 返回添加页面数据
        $this->assign('shops', $shops);
        return $this->fetch('add_in_stock');
    }

    /**
     * 添加出库单页面
     */
    public function addOutStock()
    {
        if (request()->isPost()) {
            // 获取表单数据
            $data = input('post.');

            // 参数验证
            if (empty($data['order_type']) || !in_array($data['order_type'], [2, 3])) {
                return json(['code' => 1, 'msg' => '单据类型错误']);
            }
            if ($data['order_type'] == 2 && empty($data['business_type'])) {
                return json(['code' => 1, 'msg' => '请选择业务类型']);
            }
            if ($data['order_type'] == 3 && empty($data['member_id'])) {
                return json(['code' => 1, 'msg' => '请输入会员ID']);
            }
            if (empty($data['shop_id'])) {
                return json(['code' => 1, 'msg' => '请选择所属门店']);
            }
            if (empty($data['details']) || !is_array($data['details'])) {
                return json(['code' => 1, 'msg' => '请添加商品明细']);
            }

            // 检查商品是否都有商品ID并验证库存
            $productIds = [];
            foreach ($data['details'] as $detail) {
                if (empty($detail['product_id'])) {
                    return json(['code' => 1, 'msg' => '请选择商品']);
                }
                if (empty($detail['quantity']) || $detail['quantity'] <= 0) {
                    return json(['code' => 1, 'msg' => '商品数量必须大于0']);
                }
                
                // 检查重复商品规格
                $inventoryKey = $detail['product_id'] . '_' . $detail['inventory_id'];
                if (in_array($inventoryKey, $productIds)) {
                    return json(['code' => 1, 'msg' => '不允许添加重复的商品规格，请合并数量']);
                }
                $productIds[] = $inventoryKey;
                
                // 验证库存是否足够
                $inventory = Db::name('shop_product_inventory')->alias('spi')
                    ->join('products p', 'p.id = spi.product_id')
                    ->where([
                        'spi.product_id' => $detail['product_id'],
                        'spi.inventory_id' => $detail['inventory_id'],
                        'spi.shop_id' => $data['shop_id']
                    ])
                    ->field('spi.*, p.product_type, p.title as product_name')
                    ->find();
                
                if (!$inventory) {
                    $productName = Db::name('products')->where('id', $detail['product_id'])->value('title');
                    return json(['code' => 1, 'msg' => $productName . ' 在该门店没有库存记录']);
                }
                
                // 根据商品类型验证库存
                if ($inventory['product_type'] == 2) {
                    // 计量商品：验证重量库存
                    $currentStock = $inventory['weight_stock'] ?? 0;
                    $requestQuantity = isset($detail['weight']) ? $detail['weight'] : $detail['quantity'];
                    $stockUnit = 'kg';
                } else {
                    // 普通商品：验证数量库存
                    $currentStock = $inventory['stock'] ?? 0;
                    $requestQuantity = $detail['quantity'];
                    $stockUnit = '件';
                }
                
                if ($requestQuantity > $currentStock) {
                    // 获取规格名称
                    $inventoryName = '';
                    if (!empty($detail['inventory_id'])) {
                        $inventoryName = Db::name('product_inventory')->where('id', $detail['inventory_id'])->value('title');
                    }
                    
                    $productFullName = $inventory['product_name'];
                    if (!empty($inventoryName)) {
                        $productFullName .= ' (' . $inventoryName . ')';
                    }
                    
                    $productType = $inventory['product_type'] == 2 ? '计量商品' : '普通商品';
                    return json(['code' => 1, 'msg' => $productFullName . ' (' . $productType . ') 库存不足，当前库存: ' . $currentStock . $stockUnit . '，出库数量: ' . $requestQuantity . $stockUnit]);
                }
            }

            // 设置相关字段
            $data['clientkeynum'] = session('cn_accountinfo.basekeynum');
            $data['created_by'] = session('cn_accountinfo.accountname');

            // 如果是销售出库单，设置默认业务类型
            if ($data['order_type'] == 3) {
                $data['business_type'] = 1; // 销售出库
            }

            // 开启事务
            Db::startTrans();
            try {
                // 添加库存单
                $model = new InventoryOrder();
                $details = $data['details'];
                unset($data['select'], $data['details']);
                // 移除关联单号相关的处理
                if (isset($data['related_order_no'])) {
                    unset($data['related_order_no']);
                }
                
                $id = $model->add($data);
                if (!$id) {
                    throw new \Exception('添加库存单失败');
                }

                // 获取订单编号
                $order = $model->where('id', $id)->find();

                // 添加库存单明细
                $detailModel = new InventoryOrderDetail();
                $result = $detailModel->addBatch($id, $order['order_no'], $data['order_type'], $details, $data['shop_id'], '');
                if (!$result) {
                    throw new \Exception('添加库存单明细失败');
                }

                Db::commit();
                return json(['code' => 0, 'msg' => '添加成功', 'id' => $id]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // 获取门店列表
        $shops = Db::name('shop')->field('id, title')->select();

        // 返回添加页面数据
        $this->assign('shops', $shops);
        return $this->fetch('add_out_stock');
    }

    /**
     * 编辑入库单
     */
    public function editInStock()
    {
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return $this->error('参数错误');
        }

        // 获取库存单详情
        $model = new InventoryOrder();
        $data = $model->getDetail($id);
        if (!$data) {
            return $this->error('库存单不存在');
        }

        // 检查单据类型
        if ($data['order']['order_type'] != 1) {
            return $this->error('单据类型错误');
        }

        // 只允许编辑草稿状态的库存单
        if ($data['order']['status'] != 0) {
            return $this->error('只能编辑草稿状态的库存单');
        }

        if (request()->isPost()) {
            // 获取表单数据
            $postData = input('post.');

            // 参数验证
            if (empty($postData['details']) || !is_array($postData['details'])) {
                return json(['code' => 1, 'msg' => '请添加商品明细']);
            }

            // 检查商品规格是否有重复
            $productIds = [];
            foreach ($postData['details'] as $detail) {
                if (empty($detail['product_id'])) {
                    return json(['code' => 1, 'msg' => '请选择商品']);
                }
                if (empty($detail['quantity']) || $detail['quantity'] <= 0) {
                    return json(['code' => 1, 'msg' => '商品数量必须大于0']);
                }
                
                // 检查重复商品规格
                $inventoryKey = $detail['product_id'] . '_' . $detail['inventory_id'];
                if (in_array($inventoryKey, $productIds)) {
                    return json(['code' => 1, 'msg' => '不允许添加重复的商品规格，请合并数量']);
                }
                $productIds[] = $inventoryKey;
            }

            // 开启事务
            Db::startTrans();
            try {
                // 更新库存单
                $model->where('id', $id)->update([
                    'supplier_name' => $postData['supplier_name'] ?? '',
                    'remark' => $postData['remark'] ?? ''
                ]);

                // 删除原有明细
                $detailModel = new InventoryOrderDetail();
                $detailModel->removeByOrderId($id);

                // 添加新明细
                $result = $detailModel->addBatch($id, $data['order']['order_no'], $data['order']['order_type'], $postData['details'], $data['order']['shop_id'], '');
                if (!$result) {
                    throw new \Exception('更新库存单明细失败');
                }

                Db::commit();
                return json(['code' => 0, 'msg' => '更新成功']);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // 获取门店列表
        $shops = Db::name('shop')->field('id, title')->select();

        // 返回编辑页面数据
        $this->assign([
            'order' => $data['order'],
            'details' => $data['details'],
            'shops' => $shops
        ]);
        return $this->fetch('edit_in_stock');
    }

    /**
     * 编辑出库单
     */
    public function editOutStock()
    {
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return $this->error('参数错误');
        }

        // 获取库存单详情
        $model = new InventoryOrder();
        $data = $model->getDetail($id);
        if (!$data) {
            return $this->error('库存单不存在');
        }

        // 检查单据类型
        if (!in_array($data['order']['order_type'], [2, 3])) {
            return $this->error('单据类型错误');
        }

        // 只允许编辑草稿状态的库存单
        if ($data['order']['status'] != 0) {
            return $this->error('只能编辑草稿状态的库存单');
        }

        if (request()->isPost()) {
            // 获取表单数据
            $postData = input('post.');

            // 参数验证
            if (empty($postData['details']) || !is_array($postData['details'])) {
                return json(['code' => 1, 'msg' => '请添加商品明细']);
            }

            // 检查商品规格是否有重复并验证库存
            $productIds = [];
            foreach ($postData['details'] as $detail) {
                if (empty($detail['product_id'])) {
                    return json(['code' => 1, 'msg' => '请选择商品']);
                }
                if (empty($detail['quantity']) || $detail['quantity'] <= 0) {
                    return json(['code' => 1, 'msg' => '商品数量必须大于0']);
                }
                
                // 检查重复商品规格
                $inventoryKey = $detail['product_id'] . '_' . $detail['inventory_id'];
                if (in_array($inventoryKey, $productIds)) {
                    return json(['code' => 1, 'msg' => '不允许添加重复的商品规格，请合并数量']);
                }
                $productIds[] = $inventoryKey;
                
                // 验证库存是否足够
                $inventory = Db::name('shop_product_inventory')->where([
                    'product_id' => $detail['product_id'],
                    'inventory_id' => $detail['inventory_id'],
                    'shop_id' => $data['order']['shop_id']
                ])->find();
                
                $currentStock = $inventory ? $inventory['stock'] : 0;
                if ($detail['quantity'] > $currentStock) {
                    // 获取商品名称
                    $productName = Db::name('products')->where('id', $detail['product_id'])->value('title');
                    $inventoryName = '';
                    if (!empty($detail['inventory_id'])) {
                        $inventoryName = Db::name('product_inventory')->where('id', $detail['inventory_id'])->value('title');
                    }
                    
                    $productFullName = $productName;
                    if (!empty($inventoryName)) {
                        $productFullName .= ' (' . $inventoryName . ')';
                    }
                    
                    return json(['code' => 1, 'msg' => $productFullName . ' 库存不足，当前库存: ' . $currentStock . '，出库数量: ' . $detail['quantity']]);
                }
            }

            // 开启事务
            Db::startTrans();
            try {
                // 更新字段
                $updateData = [
                    'remark' => $postData['remark'] ?? ''
                ];

                // 如果是销售出库单，可以更新会员ID
                if ($data['order']['order_type'] == 3) {
                    $updateData['member_id'] = $postData['member_id'] ?? 0;
                }

                // 更新库存单
                $model->where('id', $id)->update($updateData);

                // 删除原有明细
                $detailModel = new InventoryOrderDetail();
                $detailModel->removeByOrderId($id);

                // 添加新明细
                $result = $detailModel->addBatch($id, $data['order']['order_no'], $data['order']['order_type'], $postData['details'], $data['order']['shop_id'], '');
                if (!$result) {
                    throw new \Exception('更新库存单明细失败');
                }

                Db::commit();
                return json(['code' => 0, 'msg' => '更新成功']);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // 获取门店列表
        $shops = Db::name('shop')->field('id, title')->select();

        // 返回编辑页面数据
        $this->assign([
            'order' => $data['order'],
            'details' => $data['details'],
            'shops' => $shops
        ]);
        return $this->fetch('edit_out_stock');
    }

    /**
     * 查看库存单详情
     */
    public function detail()
    {
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return $this->error('参数错误');
        }

        // 获取库存单详情
        $model = new InventoryOrder();
        $data = $model->getDetail($id);
        if (!$data) {
            return $this->error('库存单不存在');
        }

        // 处理数据
        $data['order']['order_type_text'] = $this->getOrderTypeText($data['order']['order_type']);
        $data['order']['business_type_text'] = $this->getBusinessTypeText($data['order']['order_type'], $data['order']['business_type']);
        $data['order']['status_text'] = $this->getStatusText($data['order']['status']);

        // 获取关联门店
        if ($data['order']['shop_id']) {
            $shop = Db::name('shop')->where('id', $data['order']['shop_id'])->field('id, title')->find();
            $data['order']['title'] = $shop ? $shop['title'] : '';
        } else {
            $data['order']['title'] = '平台';
        }

        // 获取关联会员
        // if ($data['order']['member_id']) {
        //     $member = Db::name('member')->where('id', $data['order']['member_id'])->field('id, nickname, mobile')->find();
        //     $data['order']['member_name'] = $member ? ($member['nickname'] ?: $member['mobile']) : '';
        // } else {
        //     $data['order']['member_name'] = '';
        // }

        $this->assign('data', $data);
        return $this->fetch();
    }

    /**
     * 获取单据类型文本
     * @param int $order_type 单据类型
     * @return string
     */
    private function getOrderTypeText($order_type)
    {
        $types = [
            1 => '入库单',
            2 => '出库单',
            3 => '销售出库单'
        ];

        return isset($types[$order_type]) ? $types[$order_type] : '未知类型';
    }

    /**
     * 获取业务类型文本
     * @param int $order_type 单据类型
     * @param int $business_type 业务类型
     * @return string
     */
    private function getBusinessTypeText($order_type, $business_type)
    {
        $types = [
            1 => [ // 入库
                1 => '采购入库',
                2 => '调拨入库',
                3 => '退货入库',
                4 => '其他入库'
            ],
            2 => [ // 出库
                1 => '销售出库',
                2 => '调拨出库',
                3 => '报损出库',
                4 => '其他出库'
            ],
            3 => [ // 销售出库
                1 => '销售出库'
            ]
        ];

        if (isset($types[$order_type]) && isset($types[$order_type][$business_type])) {
            return $types[$order_type][$business_type];
        } else {
            return '未知类型';
        }
    }

    /**
     * 获取状态文本
     * @param int $status 状态
     * @return string
     */
    private function getStatusText($status)
    {
        $statuses = [
            0 => '草稿',
            1 => '已提交',
            2 => '已审核',
            3 => '已完成',
            -1 => '已取消'
        ];

        return isset($statuses[$status]) ? $statuses[$status] : '未知状态';
    }

    /**
     * 删除库存单
     */
    public function delete()
    {
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 删除库存单
        $model = new InventoryOrder();
        $result = $model->remove($id);
        if ($result) {
            return json(['code' => 0, 'msg' => '删除成功']);
        } else {
            return json(['code' => 1, 'msg' => '只能删除草稿状态的库存单']);
        }
    }

    /**
     * 提交库存单
     */
    public function submit()
    {
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 获取库存单
        $model = new InventoryOrder();
        $order = $model->where('id', $id)->where('status', 0)->find();
        if (!$order) {
            return json(['code' => 1, 'msg' => '库存单不存在或状态不是草稿']);
        }

        // 获取明细
        // 添加clientkeynum过滤确保数据隔离
        $clientkeynum = session('cn_accountinfo.clientkeynum');
        $detailWhere = [['order_id', '=', $id]];
        if (!empty($clientkeynum)) {
            $detailWhere[] = ['clientkeynum', '=', $clientkeynum];
        }
        $details = Db::name('inventory_order_detail')->where($detailWhere)->select();
        if (empty($details)) {
            return json(['code' => 1, 'msg' => '库存单没有明细，不能提交']);
        }

        // 如果是出库单或销售出库单，需要验证库存是否足够
        if (in_array($order['order_type'], [2, 3])) {
            // 检查商品规格是否有重复并验证库存
            $productIds = [];
            foreach ($details as $detail) {
                // 检查重复商品规格
                $inventoryKey = $detail['product_id'] . '_' . $detail['inventory_id'];
                if (in_array($inventoryKey, $productIds)) {
                    return json(['code' => 1, 'msg' => '不允许添加重复的商品规格，请编辑订单合并数量']);
                }
                $productIds[] = $inventoryKey;
                
                // 验证库存是否足够
                $inventory = Db::name('shop_product_inventory')->where([
                    'product_id' => $detail['product_id'],
                    'inventory_id' => $detail['inventory_id'],
                    'shop_id' => $order['shop_id']
                ])->find();
                
                $currentStock = $inventory ? $inventory['stock'] : 0;
                if ($detail['quantity'] > $currentStock) {
                    // 获取商品名称
                    $productName = Db::name('products')->where('id', $detail['product_id'])->value('title');
                    $inventoryName = '';
                    if (!empty($detail['inventory_id'])) {
                        $inventoryName = Db::name('product_inventory')->where('id', $detail['inventory_id'])->value('title');
                    }
                    
                    $productFullName = $productName;
                    if (!empty($inventoryName)) {
                        $productFullName .= ' (' . $inventoryName . ')';
                    }
                    
                    return json(['code' => 1, 'msg' => $productFullName . ' 库存不足，当前库存: ' . $currentStock . '，出库数量: ' . $detail['quantity']]);
                }
            }
        } else {
            // 入库单也要检查是否有重复商品规格
            $productIds = [];
            foreach ($details as $detail) {
                // 检查重复商品规格
                $inventoryKey = $detail['product_id'] . '_' . $detail['inventory_id'];
                if (in_array($inventoryKey, $productIds)) {
                    return json(['code' => 1, 'msg' => '不允许添加重复的商品规格，请编辑订单合并数量']);
                }
                $productIds[] = $inventoryKey;
            }
        }

        // 更新状态为已审核，直接跳过已提交状态
        $result = $model->complete($id, session('cn_accountinfo.accountname'));
        if ($result) {
            return json(['code' => 0, 'msg' => '审核成功']);
        } else {
            return json(['code' => 1, 'msg' => '审核失败']);
        }
    }

    /**
     * 审核并完成库存单
     */
    public function complete()
    {
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 完成库存单
        $model = new InventoryOrder();
        $result = $model->complete($id, session('cn_accountinfo.accountname'));
        if ($result) {
            return json(['code' => 0, 'msg' => '操作成功']);
        } else {
            return json(['code' => 1, 'msg' => '操作失败，库存单不存在或状态不是已审核']);
        }
    }

    /**
     * 取消库存单
     */
    public function cancel()
    {
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 获取库存单
        $model = new InventoryOrder();
        $order = $model->where('id', $id)->whereIn('status', [0, 1])->find();
        if (!$order) {
            return json(['code' => 1, 'msg' => '库存单不存在或状态不允许取消']);
        }

        // 更新状态为已取消
        $result = $model->updateStatus($id, -1);
        if ($result) {
            return json(['code' => 0, 'msg' => '取消成功']);
        } else {
            return json(['code' => 1, 'msg' => '取消失败']);
        }
    }

    /**
     * 库存日志列表
     */
    public function log()
    {
        if (request()->isAjax()) {
            // 获取请求参数
            $page = input('page', 1, 'intval');
            $limit = input('limit', 15, 'intval');
            $product_id = input('product_id', 0, 'intval');
            $inventory_id = input('inventory_id', 0, 'intval');
            $shop_id = input('shop_id', 0, 'intval');
            $change_type = input('change_type', 0, 'intval');
            $date_range = input('date_range', '');

            // 构建查询条件
            $where = [];
            if ($product_id > 0) {
                $where[] = ['l.product_id', '=', $product_id];
            }
            if ($inventory_id > 0) {
                $where[] = ['l.inventory_id', '=', $inventory_id];
            }
            if ($shop_id > 0) {
                $where[] = ['l.shop_id', '=', $shop_id];
            }
            if ($change_type > 0) {
                $where[] = ['l.change_type', '=', $change_type];
            }
            if (!empty($date_range)) {
                $dates = explode(' - ', $date_range);
                if (count($dates) == 2) {
                    $where[] = ['l.created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
                }
            }

            // 获取列表数据
            $count = Db::name('inventory_log')->alias('l')
                ->join('products p', 'p.id = l.product_id', 'LEFT')
                ->join('product_inventory pi', 'pi.id = l.inventory_id', 'LEFT')
                ->where($where)
                ->count();

            $list = Db::name('inventory_log')->alias('l')
                ->join('products p', 'p.id = l.product_id', 'LEFT')
                ->join('product_inventory pi', 'pi.id = l.inventory_id', 'LEFT')
                ->where($where)
                ->field('l.*, p.title as product_name, p.product_type, pi.title as inventory_name')
                ->order('l.id', 'desc')
                ->page($page, $limit)
                ->select();

            // 获取门店信息
            $shops = Db::name('shop')->column('title', 'id');

            // 处理数据
            foreach ($list as &$item) {
                // 处理门店名称
                $item['shop_name'] = $item['shop_id'] > 0 ? ($shops[$item['shop_id']] ?? '未知门店') : '平台';
                
                // 处理变动类型文本
                $item['change_type_text'] = $this->getChangeTypeText($item['change_type']);
                
                // 处理变动数量
                $item['quantity_before'] = $item['before_quantity'];
                $item['quantity_change'] = $item['change_quantity'];
                $item['quantity_after'] = $item['after_quantity'];
                
                // 处理操作人
                $item['created_by'] = $item['operator'];
            }

            // 检查是否有筛选条件
            $hasFilter = ($product_id > 0 || $inventory_id > 0 || $shop_id > 0 || $change_type > 0 || !empty($date_range));
            
            // 计算总入库和总出库数量
            $totalInStock = Db::name('inventory_log')->alias('l')
                ->where($where)
                ->whereIn('l.change_type', [1, 3, 6, 9]) // 入库、调拨入、退货、其他入库
                ->sum('l.change_quantity');

            $totalOutStock = Db::name('inventory_log')->alias('l')
                ->where($where)
                ->whereIn('l.change_type', [2, 4, 5, 10]) // 出库、调拨出、销售、其他出库
                ->sum('l.change_quantity');

            // 取绝对值，因为出库的change_quantity是负数
            $totalOutStock = abs($totalOutStock);
            
            // 计算普通商品和计量商品的分类统计
            $normalInStock = Db::name('inventory_log')->alias('l')
                ->join('products p', 'p.id = l.product_id')
                ->where($where)
                ->where('p.product_type', 'in', [1, 3]) // 普通商品
                ->whereIn('l.change_type', [1, 3, 6, 9]) // 入库类型
                ->sum('l.change_quantity');
                
            $normalOutStock = Db::name('inventory_log')->alias('l')
                ->join('products p', 'p.id = l.product_id')
                ->where($where)
                ->where('p.product_type', 'in', [1, 3]) // 普通商品
                ->whereIn('l.change_type', [2, 4, 5, 10]) // 出库类型
                ->sum('l.change_quantity');
            
            // 取绝对值
            $normalOutStock = abs($normalOutStock);
                
            $weightInStock = Db::name('inventory_log')->alias('l')
                ->join('products p', 'p.id = l.product_id')
                ->where($where)
                ->where('p.product_type', '=', 2) // 计量商品
                ->whereIn('l.change_type', [1, 3, 6, 9]) // 入库类型
                ->sum('l.change_quantity');
                
            $weightOutStock = Db::name('inventory_log')->alias('l')
                ->join('products p', 'p.id = l.product_id')
                ->where($where)
                ->where('p.product_type', '=', 2) // 计量商品
                ->whereIn('l.change_type', [2, 4, 5, 10]) // 出库类型
                ->sum('l.change_quantity');
                
            // 取绝对值
            $weightOutStock = abs($weightOutStock);
            
            // 计算差值
            $difference = round($totalInStock - $totalOutStock, 2);
            $normalDifference = round($normalInStock - $normalOutStock, 2);
            $weightDifference = round($weightInStock - $weightOutStock, 2);

            $result = [
                'code' => 0, 
                'msg' => 'success', 
                'count' => $count, 
                'data' => $list,
                'stats' => [
                    'total_in_stock' => $totalInStock,
                    'total_out_stock' => $totalOutStock,
                    'difference' => $difference,
                    'normal_in_stock' => $normalInStock,
                    'normal_out_stock' => $normalOutStock,
                    'weight_in_stock' => $weightInStock,
                    'weight_out_stock' => $weightOutStock,
                    'normal_difference' => $normalDifference,
                    'weight_difference' => $weightDifference
                ]
            ];

            return json($result);
        }

        // 获取门店列表
        $shops = Db::name('shop')->field('id, title')->select();
        $this->assign('shops', $shops);

        return $this->fetch();
    }

    /**
     * 获取库存变动类型文本
     * @param int $change_type 变动类型
     * @return string
     */
    private function getChangeTypeText($change_type)
    {
        $types = [
            1 => '入库',
            2 => '出库',
            3 => '调拨入',
            4 => '调拨出',
            5 => '销售',
            6 => '退货',
            7 => '盘点',
            8 => '其他'
        ];

        return isset($types[$change_type]) ? $types[$change_type] : '未知类型';
    }

    /**
     * 商品库存列表
     */
    public function stock()
    {
        if (request()->isAjax()) {
            // 获取请求参数
            $page = input('page', 1, 'intval');
            $limit = input('limit', 15, 'intval');
            $shop_id = input('shop_id', 0, 'intval');
            $product_name = input('product_name', '');
            $product_type = input('product_type', ''); // 商品类型筛选
            $stock_status = input('stock_status', ''); // 库存状态：1-有库存，2-无库存
            $product_id = input('product_id', 0, 'intval'); // 新增：商品ID筛选
            $inventory_id = input('inventory_id', 0, 'intval'); // 新增：规格ID筛选

            // 构建查询条件
            $where = [];
            if ($shop_id > 0) {
                $where[] = ['spi.shop_id', '=', $shop_id];
            }
            if (!empty($product_name)) {
                $where[] = ['p.title', 'like', "%{$product_name}%"];
            }
            if ($product_type !== '') {
                $where[] = ['p.product_type', '=', $product_type];
            }
            // 新增：根据商品ID筛选
            if ($product_id > 0) {
                $where[] = ['spi.product_id', '=', $product_id];
            }
            // 新增：根据规格ID筛选
            if ($inventory_id > 0) {
                $where[] = ['spi.inventory_id', '=', $inventory_id];
            }
            
            // 构建基础查询对象
            $baseQuery = Db::name('shop_product_inventory')->alias('spi')
                ->join('products p', 'p.id = spi.product_id')
                ->join('product_inventory pi', 'pi.id = spi.inventory_id', 'LEFT')
                ->where($where);

            // 处理库存状态筛选 - 使用原生SQL条件
            if ($stock_status == 1) {
                // 有库存：普通商品stock>0 或 计量商品weight_stock>0
                $baseQuery->whereRaw('((p.product_type IN (1, 3) AND spi.stock > 0) OR (p.product_type = 2 AND spi.weight_stock > 0))');
            } elseif ($stock_status == 2) {
                // 无库存：普通商品stock<=0 或 计量商品weight_stock<=0
                $baseQuery->whereRaw('((p.product_type IN (1, 3) AND spi.stock <= 0) OR (p.product_type = 2 AND spi.weight_stock <= 0))');
            }

            // 获取总数
            $count = $baseQuery->count();

            // 重新构建查询获取列表数据
            $listQuery = Db::name('shop_product_inventory')->alias('spi')
                ->join('products p', 'p.id = spi.product_id')
                ->join('product_inventory pi', 'pi.id = spi.inventory_id', 'LEFT')
                ->where($where);

            // 再次处理库存状态筛选
            if ($stock_status == 1) {
                $listQuery->whereRaw('((p.product_type IN (1, 3) AND spi.stock > 0) OR (p.product_type = 2 AND spi.weight_stock > 0))');
            } elseif ($stock_status == 2) {
                $listQuery->whereRaw('((p.product_type IN (1, 3) AND spi.stock <= 0) OR (p.product_type = 2 AND spi.weight_stock <= 0))');
            }

            $list = $listQuery->field('spi.*, p.title as product_name, p.cover as product_thumb, p.product_type, pi.title as inventory_name')
                ->order('spi.id', 'desc')
                ->page($page, $limit)
                ->select();

            // 获取门店信息
            $shops = Db::name('shop')->column('title', 'id');

            // 处理门店名称和商品类型
            foreach ($list as &$item) {
                $item['title'] = $item['shop_id'] > 0 ? ($shops[$item['shop_id']] ?? '未知门店') : '平台';
                // 如果没有规格名称，显示默认规格
                if (empty($item['inventory_name'])) {
                    $item['inventory_name'] = '默认规格';
                }
                
                // 添加商品类型和库存显示
                $item['product_type_text'] = $item['product_type'] == 2 ? '计量商品' : '普通商品';
                
                if ($item['product_type'] == 2) {
                    // 计量商品显示重量库存
                    $item['stock_display'] = $item['weight_stock'] . 'kg';
                    $item['stock_unit'] = 'kg';
                    $item['current_stock'] = $item['weight_stock'];
                } else {
                    // 普通商品显示数量库存
                    $item['stock_display'] = $item['stock'] . '件';
                    $item['stock_unit'] = '件';
                    $item['current_stock'] = $item['stock'];
                }
            }

            return json(['code' => 0, 'msg' => 'success', 'count' => $count, 'data' => $list]);
        }

        // 获取门店列表
        $shops = Db::name('shop')->field('id, title')->select();
        $this->assign('shops', $shops);

        // 获取总库存统计数据
        $shop_id = input('shop_id', 0, 'intval');
        $product_type = input('product_type', '');
        $where = [];
        
        if ($shop_id > 0) {
            $where[] = ['spi.shop_id', '=', $shop_id];
        }
        
        if ($product_type !== '') {
            $where[] = ['p.product_type', '=', $product_type];
        }
        
        // 统计数据 - 修改统计方法，分开统计商品数量和库存总量
        $stats = Db::name('shop_product_inventory')->alias('spi')
            ->join('products p', 'p.id = spi.product_id')
            ->where($where)
            ->field([
                'count(distinct spi.product_id) as total_products',
                'sum(case when p.product_type IN (1, 3) then spi.stock else 0 end) as total_normal_stock',
                'sum(case when p.product_type = 2 then spi.weight_stock else 0 end) as total_weight_stock',
                'count(distinct case when p.product_type IN (1, 3) then spi.product_id end) as normal_product_count',
                'count(distinct case when p.product_type = 2 then spi.product_id end) as weight_product_count'
            ])
            ->find();
            
        // 格式化统计数据
        $total_normal_stock = round($stats['total_normal_stock'] ?? 0, 2);
        $total_weight_stock = round($stats['total_weight_stock'] ?? 0, 2);
        
        // 验证计量商品总量，如果不存在计量商品则设置为0
        if ($stats['weight_product_count'] == 0) {
            $total_weight_stock = 0;
        }
        
        $stockStats = [
            'total_products' => $stats['total_products'] ?? 0,
            'total_normal_stock' => $total_normal_stock,
            'total_weight_stock' => $total_weight_stock,
            'normal_product_count' => $stats['normal_product_count'] ?? 0,
            'weight_product_count' => $stats['weight_product_count'] ?? 0,
            'total_stock_value' => $total_normal_stock + $total_weight_stock // 添加总库存值
        ];
        
        $this->assign('stockStats', $stockStats);

        return $this->fetch();
    }

    /**
     * 搜索商品
     * 用于自动完成搜索商品
     */
    public function searchProduct()
    {
        $keyword = input('keyword', '');
        if (empty($keyword)) {
            return json(['code' => 0, 'msg' => 'success', 'data' => []]);
        }

        // 搜索商品
        $products = Db::name('products')
            ->where('title', 'like', "%{$keyword}%")
            ->whereOr('id', 'like', "%{$keyword}%")
            ->field('id, title as name')
            ->limit(20)
            ->select();

        return json(['code' => 0, 'msg' => 'success', 'data' => $products]);
    }

    /**
     * 根据商品ID获取规格列表
     */
    public function getInventoryByProduct()
    {
        $product_id = input('product_id', 0, 'intval');
        if ($product_id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 获取商品规格
        $inventories = Db::name('product_inventory')
            ->where('product_id', $product_id)
            ->field('id, title as name')
            ->select();

        return json(['code' => 0, 'msg' => 'success', 'data' => $inventories]);
    }

    /**
     * 导出入库单详情
     */
    public function exportDetail()
    {
        // 权限校验
        check_auth(request()->controller() . '/index', 0);
        
        // 获取筛选条件
        $order_no = input('order_no', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $date_range = input('date_range', '');
        $is_manual = input('is_manual', '');
        
        // 构建查询条件
        $where = [];
        $where[] = ['io.order_type', '=', 1]; // 只查询入库单
        
        if (!empty($order_no)) {
            $where[] = ['io.order_no', 'like', "%{$order_no}%"];
        }
        if (!empty($business_type)) {
            $where[] = ['io.business_type', '=', $business_type];
        }
        if ($status !== '') {
            $where[] = ['io.status', '=', $status];
        }
        if (!empty($date_range)) {
            $dates = explode(' - ', $date_range);
            if (count($dates) == 2) {
                $where[] = ['io.created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
            }
        }
        // 添加手动录入筛选条件
        if ($is_manual === '1') {
            $where[] = ['io.related_order_no', '=', ''];
        } elseif ($is_manual === '0') {
            $where[] = ['io.related_order_no', '<>', ''];
        }
        
        // 直接使用连表查询获取入库单明细数据
        $details = Db::name('inventory_order_detail')->alias('iod')
            ->join('inventory_order io', 'io.id = iod.order_id')
            ->join('products p', 'p.id = iod.product_id', 'LEFT')
            ->join('product_inventory pi', 'pi.id = iod.inventory_id', 'LEFT')
            ->join('shop s', 's.id = io.shop_id', 'LEFT')
            ->where($where)
            ->field('iod.*, io.order_no, io.shop_id, io.business_type, io.status, io.created_by, io.reviewed_by, io.created_at, io.remark, io.supplier_name, io.order_type,
                    p.title as product_name, p.product_type, pi.title as inventory_name, s.title as shop_name')
            ->order('io.id DESC, iod.id ASC')
            ->select();
        
        if (empty($details)) {
            return json(['code' => -1, 'msg' => '未找到符合条件的入库单']);
        }
        
        // 准备导出数据
        $export_data = [];
        
        foreach ($details as $detail) {
            // 构建导出数据
            $export_row = [
                '单据编号' => $detail['order_no'],
                '业务类型' => $this->getBusinessTypeText($detail['order_type'], $detail['business_type']),
                '门店名称' => $detail['shop_name'] ?: '平台',
                '供应商' => $detail['supplier_name'],
                '状态' => $this->getStatusText($detail['status']),
                '创建时间' => $detail['created_at'],
                '创建人' => $detail['created_by'],
                '审核人' => $detail['reviewed_by'] ?: '未审核',
                '商品名称' => $detail['product_name'] ?: '',
                '规格' => $detail['inventory_name'] ?: '默认',
                '单位' => $detail['unit'],
                '数量' => $detail['quantity'],
                '单价' => $detail['price'],
                '金额' => $detail['amount'],
                '备注' => $detail['remark']
            ];
            
            $export_data[] = $export_row;
        }
        
        if (empty($export_data)) {
            return json(['code' => -1, 'msg' => '没有可导出的入库单详情数据']);
        }
        
        // 生成文件名
        $filename = '入库单详情_' . date('YmdHis') . '.xlsx';
        
        // 使用SimpleExcel导出数据
        $writer = \Spatie\SimpleExcel\SimpleExcelWriter::streamDownload($filename);
        $writer->addRows($export_data);
        
        exit;
    }

    /**
     * 导出出库单详情
     */
    public function exportOutStockDetail()
    {
        // 权限校验
        check_auth(request()->controller() . '/outstock', 0);
        
        // 获取筛选条件
        $id = input('id', 0, 'intval');
        $order_no = input('order_no', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $date_range = input('date_range', '');
        $is_manual = input('is_manual', '');
        
        // 构建查询条件
        $where = [];
        $where[] = ['io.order_type', '=', 2]; // 只查询出库单
        
        // 如果指定了ID，则只导出该ID的出库单
        if ($id > 0) {
            $where[] = ['io.id', '=', $id];
        } else {
            // 否则根据筛选条件查询
            if (!empty($order_no)) {
                $where[] = ['io.order_no', 'like', "%{$order_no}%"];
            }
            if (!empty($business_type)) {
                $where[] = ['io.business_type', '=', $business_type];
            }
            if ($status !== '') {
                $where[] = ['io.status', '=', $status];
            }
            if (!empty($date_range)) {
                $dates = explode(' - ', $date_range);
                if (count($dates) == 2) {
                    $where[] = ['io.created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
                }
            }
            // 添加手动录入筛选条件
            if ($is_manual === '1') {
                $where[] = ['io.related_order_no', '=', ''];
            } elseif ($is_manual === '0') {
                $where[] = ['io.related_order_no', '<>', ''];
            }
        }
        
        // 直接使用连表查询获取出库单明细数据
        $details = Db::name('inventory_order_detail')->alias('iod')
            ->join('inventory_order io', 'io.id = iod.order_id')
            ->join('products p', 'p.id = iod.product_id', 'LEFT')
            ->join('product_inventory pi', 'pi.id = iod.inventory_id', 'LEFT')
            ->join('shop s', 's.id = io.shop_id', 'LEFT')
            ->where($where)
            ->field('iod.*, io.order_no, io.shop_id, io.business_type, io.status, io.created_by, io.reviewed_by, io.created_at, io.remark, io.supplier_name, io.order_type,
                    p.title as product_name, p.product_type, pi.title as inventory_name, s.title as shop_name')
            ->order('io.id DESC, iod.id ASC')
            ->select();
        
        if (empty($details)) {
            return json(['code' => -1, 'msg' => '未找到符合条件的出库单']);
        }
        
        // 准备导出数据
        $export_data = [];
        
        foreach ($details as $detail) {
            
            
            // 构建导出数据
            $export_row = [
                '单据编号' => $detail['order_no'],
                '业务类型' => $this->getBusinessTypeText($detail['order_type'], $detail['business_type']),
                '门店名称' => $detail['shop_name'] ?: '平台',
                '相关单位' => $detail['supplier_name'],
                // '会员' => $member_name,
                '状态' => $this->getStatusText($detail['status']),
                '创建时间' => $detail['created_at'],
                '创建人' => $detail['created_by'],
                '审核人' => $detail['reviewed_by'] ?: '未审核',
                '商品名称' => $detail['product_name'] ?: '',
                '规格' => $detail['inventory_name'] ?: '默认',
                '单位' => $detail['unit'],
                '数量' => $detail['quantity'],
                '单价' => $detail['price'],
                '金额' => $detail['amount'],
                '备注' => $detail['remark']
            ];
            
            $export_data[] = $export_row;
        }
        
        if (empty($export_data)) {
            return json(['code' => -1, 'msg' => '没有可导出的出库单详情数据']);
        }
        
        // 生成文件名
        $filename = '出库单详情_' . date('YmdHis') . '.xlsx';
        
        // 使用SimpleExcel导出数据
        $writer = \Spatie\SimpleExcel\SimpleExcelWriter::streamDownload($filename);
        $writer->addRows($export_data);
        
        exit;
    }

    /**
     * 导出库存列表
     */
    public function export_stock()
    {
        // 权限校验
        check_auth(request()->controller() . '/stock', 0);
        
        // 获取请求参数
        $shop_id = input('shop_id', 0, 'intval');
        $product_type = input('product_type', ''); // 商品类型筛选
        $stock_status = input('stock_status', ''); // 库存状态：1-有库存，2-无库存
        $product_id = input('product_id', 0, 'intval'); // 商品ID筛选
        $inventory_id = input('inventory_id', 0, 'intval'); // 规格ID筛选

        // 构建查询条件
        $where = [];
        if ($shop_id > 0) {
            $where[] = ['spi.shop_id', '=', $shop_id];
        }
        if ($product_type !== '') {
            $where[] = ['p.product_type', '=', $product_type];
        }
        // 根据商品ID筛选
        if ($product_id > 0) {
            $where[] = ['spi.product_id', '=', $product_id];
        }
        // 根据规格ID筛选
        if ($inventory_id > 0) {
            $where[] = ['spi.inventory_id', '=', $inventory_id];
        }
        
        // 构建基础查询对象
        $baseQuery = Db::name('shop_product_inventory')->alias('spi')
            ->join('products p', 'p.id = spi.product_id')
            ->join('product_inventory pi', 'pi.id = spi.inventory_id', 'LEFT')
            ->join('shop s', 's.id = spi.shop_id', 'LEFT')
            ->where($where);

        // 处理库存状态筛选 - 使用原生SQL条件
        if ($stock_status == 1) {
            // 有库存：普通商品stock>0 或 计量商品weight_stock>0
            $baseQuery->whereRaw('((p.product_type = 1 AND spi.stock > 0) OR (p.product_type = 2 AND spi.weight_stock > 0))');
        } elseif ($stock_status == 2) {
            // 无库存：普通商品stock<=0 或 计量商品weight_stock<=0
            $baseQuery->whereRaw('((p.product_type = 1 AND spi.stock <= 0) OR (p.product_type = 2 AND spi.weight_stock <= 0))');
        }

        // 查询数据
        $list = $baseQuery->field('spi.*, p.title as product_name, p.cover as product_thumb, p.product_type, 
                                  pi.title as inventory_name, s.title as shop_name')
            ->order('spi.id', 'desc')
            ->select();

        if (empty($list)) {
            return json(['code' => -1, 'msg' => '未找到符合条件的库存数据']);
        }
        
        // 准备导出数据
        $export_data = [];
        
        foreach ($list as $item) {
            // 判断是否为计量商品
            $is_weight_product = ($item['product_type'] == 2);
            
            // 构建导出数据行
            $export_row = [
                'ID' => $item['id'],
                '门店名称' => $item['shop_name'],
                '商品名称' => $item['product_name'],
                '商品类型' => $is_weight_product ? '计量商品' : '普通商品',
                '规格名称' => $item['inventory_name'] ?: '默认规格',
            ];
            
            // 根据商品类型显示不同的库存信息
            if ($is_weight_product) {
                $export_row['库存数量'] = $item['weight_stock'] . 'kg';
            } else {
                $export_row['库存数量'] = $item['stock'] . '件';
            }
            
            $export_row['创建时间'] = $item['add_time'];
            $export_row['更新时间'] = $item['update_time'];
            
            $export_data[] = $export_row;
        }
        
        // 生成文件名
        $filename = 'inventory_stock_' . date('YmdHis') . '.xlsx';
        $filepath = \think\facade\Env::get('runtime_path') . 'temp/' . $filename;
        
        // 确保目录存在
        if (!is_dir(\think\facade\Env::get('runtime_path') . 'temp/')) {
            mkdir(\think\facade\Env::get('runtime_path') . 'temp/', 0777, true);
        }
        
        // 使用SimpleExcel导出数据
        $writer = SimpleExcelWriter::create($filepath);
        $writer->addRows($export_data);
        
        // 下载文件
        return download($filepath, $filename);
    }

    /**
     * 入库单详情列表页面
     */
    public function instock_detail_list()
    {
        // 获取门店列表
        $shops = Db::name('shop')->field('id, title')->select();
        $this->assign('shops', $shops);
        return $this->fetch();
    }

    /**
     * 出入库单详情列表页面
     */
    public function outstock_detail_list()
    {
        // 获取门店列表
        $shops = Db::name('shop')->field('id, title')->select();
        $this->assign('shops', $shops);
        return $this->fetch();
    }

    /**
     * 获取入库单详情列表数据
     */
    public function getInStockDetailList()
    {
        // 获取请求参数
        $page = input('page', 1, 'intval');
        $limit = input('limit', 15, 'intval');
        $order_no = input('order_no', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $shop_id = input('shop_id', '');
        $product_id = input('product_id', '');
        $inventory_id = input('inventory_id', '');
        $date_range = input('date_range', '');
        $is_manual = input('is_manual', '');

        // 构建查询条件
        $where = [];
        // 基本条件
        $where[] = ['io.order_type', '=', 1]; // 只查询入库单
        
        // 主表条件
        if (!empty($order_no)) {
            $where[] = ['io.order_no', 'like', "%{$order_no}%"];
        }
        if (!empty($business_type)) {
            $where[] = ['io.business_type', '=', $business_type];
        }
        if ($status !== '') {
            $where[] = ['io.status', '=', $status];
        }
        if (!empty($shop_id)) {
            $where[] = ['io.shop_id', '=', $shop_id];
        }
        if (!empty($date_range)) {
            $dates = explode(' - ', $date_range);
            if (count($dates) == 2) {
                $where[] = ['io.created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
            }
        }
        // 添加手动录入筛选条件
        if ($is_manual === '1') {
            $where[] = ['io.related_order_no', 'null'];
        } elseif ($is_manual === '0') {
            $where[] = ['io.related_order_no', 'not null'];
        }
        
        // 子表条件
        if (!empty($product_id)) {
            $where[] = ['iod.product_id', '=', $product_id];
        }
        if (!empty($inventory_id)) {
            $where[] = ['iod.inventory_id', '=', $inventory_id];
        }
        
        // 计算总数 - 直接从连表查询中计算
        $count = Db::name('inventory_order_detail')->alias('iod')
            ->join('inventory_order io', 'io.id = iod.order_id')
            ->where($where)
            ->count();
        
        // 获取分页数据 - 直接使用连表查询获取所有需要的数据
        $details = Db::name('inventory_order_detail')->alias('iod')
            ->join('inventory_order io', 'io.id = iod.order_id')
            ->join('products p', 'p.id = iod.product_id', 'LEFT')
            ->join('product_inventory pi', 'pi.id = iod.inventory_id', 'LEFT')
            ->join('shop s', 's.id = io.shop_id', 'LEFT')
            ->where($where)
            ->field('iod.*, io.order_no, io.shop_id, io.business_type, io.status, io.created_by, io.created_at, io.remark,
                    p.title as product_name, p.product_type, pi.title as inventory_name, s.title as shop_name')
            ->page($page, $limit)
            ->order('iod.id', 'desc')
            ->select();
        
        // 处理数据
        $list = [];
        foreach ($details as $detail) {
            // 构建列表数据
            $item = [
                'id' => $detail['id'],
                'order_id' => $detail['order_id'],
                'order_no' => $detail['order_no'],
                'shop_id' => $detail['shop_id'],
                'shop_name' => $detail['shop_name'] ?: '',
                'business_type' => $detail['business_type'],
                'business_type_text' => $this->getBusinessTypeText(1, $detail['business_type']),
                'status' => $detail['status'],
                'status_text' => $this->getStatusText($detail['status']),
                'product_id' => $detail['product_id'],
                'product_name' => $detail['product_name'] ?: '',
                'product_type' => $detail['product_type'] ?: 1,
                'product_type_text' => $detail['product_type'] == 2 ? '计量商品' : '普通商品',
                'inventory_id' => $detail['inventory_id'],
                'spec_info' => $detail['inventory_name'] ?: '默认规格',
                'quantity' => $detail['quantity'],
                'price' => $detail['price'],
                'amount' => $detail['amount'],
                'created_by' => $detail['created_by'],
                'created_at' => $detail['created_at']
            ];
            
            // 根据商品类型设置显示数量
            if ($detail['product_type'] == 2) {
                $item['quantity_display'] = $detail['quantity'] . 'kg';
            } else {
                $item['quantity_display'] = $detail['quantity'] . '件';
            }
            
            $list[] = $item;
        }
        
        return json(['code' => 0, 'msg' => 'success', 'count' => $count, 'data' => $list]);
    }

    /**
     * 获取出入库单详情列表数据
     */
    public function getOutStockDetailList()
    {
        // 获取请求参数
        $page = input('page', 1, 'intval');
        $limit = input('limit', 15, 'intval');
        $order_no = input('order_no', '');
        $stock_type = input('stock_type', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $shop_id = input('shop_id', '');
        $product_id = input('product_id', '');
        $inventory_id = input('inventory_id', '');
        $date_range = input('date_range', '');
        $is_manual = input('is_manual', '');

        // 构建查询条件
        $where = [];
        
        // 添加clientkeynum过滤条件 - 确保数据隔离
        $clientkeynum = session('cn_accountinfo.clientkeynum');
        if (!empty($clientkeynum)) {
            $where[] = ['iod.clientkeynum', '=', $clientkeynum];
        }
        
        // 根据单据类型筛选
        if (!empty($stock_type)) {
            $order_type = $stock_type === 'in' ? 1 : 2;
            if ($order_type == 1) {
                $where[] = ['io.order_type', '=', 1];
            } else {
                $where[] = ['io.order_type', 'in', [2, 3]];
            }
        }
        
        // 主表条件
        if (!empty($order_no)) {
            $where[] = ['iod.order_no', 'like', "%{$order_no}%"];
        }
        if (!empty($shop_id)) {
            $where[] = ['iod.shop_id', '=', $shop_id];
        }
        if (!empty($date_range)) {
            $dates = explode(' - ', $date_range);
            if (count($dates) == 2) {
                $where[] = ['iod.created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
            }
        }
        
        // 添加手动录入筛选条件
        if ($is_manual === '1') {
            $where[] = ['io.related_order_no', 'null'];
        } elseif ($is_manual === '0') {
            $where[] = ['io.related_order_no', 'not null'];
        }
        
        // 子表条件
        if (!empty($product_id)) {
            $where[] = ['iod.product_id', '=', $product_id];
        }
        if (!empty($inventory_id)) {
            $where[] = ['iod.inventory_id', '=', $inventory_id];
        }
        
        // 计算总数 - 直接从连表查询中计算
        $count = Db::name('inventory_order_detail')->alias('iod')
            ->join('inventory_order io', 'io.id = iod.order_id')
            ->where($where)
            ->count();
        
        // 获取分页数据 - 直接使用连表查询获取所有需要的数据
        $details = Db::name('inventory_order_detail')->alias('iod')
            ->join('inventory_order io', 'io.id = iod.order_id')
            ->join('products p', 'p.id = iod.product_id', 'LEFT')
            ->join('product_inventory pi', 'pi.id = iod.inventory_id', 'LEFT')
            ->join('shop s', 's.id = iod.shop_id', 'LEFT')
            ->where($where)
            ->field('iod.*, s.title as shop_name, p.title as product_name, pi.title as inventory_name, p.product_type')
            ->page($page, $limit)
            ->order('iod.id', 'desc')
            ->select();
        
        // 处理数据 
        $list = [];
        foreach ($details as $detail) {
            // 构建列表数据

            $item = [
                'id' => $detail['id'],
                'order_id' => $detail['order_id'],
                'order_no' => $detail['order_no'],
                'shop_id' => $detail['shop_id'],
                'shop_name' => $detail['shop_name'] ?: '',
                'order_type' => $detail['order_type'],
                'stock_type' => $this->getStockTypeText($detail['order_type']),
                'stock_type_text' => $this->getStockTypeText($detail['order_type']),
                'business_type' => $detail['business_type'],
                'business_type_text' => $this->getBusinessTypeText($detail['order_type'], $detail['business_type']),
                // 'status' => $detail['status'],
                // 'status_text' => $this->getStatusText($detail['status']),
                'product_id' => $detail['product_id'],
                'product_name' => $detail['product_name'],
                'product_type' => $detail['product_type'] ?: 1,
                'product_type_text' => $detail['product_type'] == 2 ? '计量商品' : '普通商品',
                'inventory_id' => $detail['inventory_id'],
                'spec_info' => $detail['inventory_name'],
                'quantity' => $detail['quantity'],
                'price' => $detail['price'],
                'amount' => $detail['amount'],
                'created_by' => $detail['created_by'],
                'created_at' => $detail['created_at']
            ];
            
            // 根据商品类型设置显示数量
            if ($detail['product_type'] == 2) {
                $item['quantity_display'] = $detail['quantity'] . 'kg';
            } else {
                $item['quantity_display'] = $detail['quantity'] . '件';
            }
            
            $list[] = $item;
        }
        
        // 计算总入库和总出库数量（基于当前筛选条件）
        $totalInStock = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id', 'LEFT')
            ->join('inventory_order io', 'io.order_no = iod.order_no')
            ->where($where)
            ->where('io.order_type', '=', 1)
            ->sum('iod.quantity');

        $totalOutStock = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id', 'LEFT')
            ->join('inventory_order io', 'io.order_no = iod.order_no')
            ->where($where)
            ->whereIn('io.order_type', [2, 3])
            ->sum('iod.quantity');
        
        // 计算差值（入库 - 出库）
        $difference = round($totalInStock - $totalOutStock, 2);
        
        // 区分普通商品和计量商品的统计
        $normalInStock = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id')
            ->join('inventory_order io', 'io.order_no = iod.order_no')
            ->where($where)
            ->where('io.order_type', '=', 1)
            ->where('p.product_type', 'in', [1, 3]) // 普通商品
            ->sum('iod.quantity');
            
        $normalOutStock = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id')
            ->join('inventory_order io', 'io.order_no = iod.order_no')
            ->where($where)
            ->whereIn('io.order_type', [2, 3])
            ->where('p.product_type', 'in', [1, 3]) // 普通商品
            ->sum('iod.quantity');
            
        $weightInStock = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id')
            ->join('inventory_order io', 'io.order_no = iod.order_no')
            ->where($where)
            ->where('io.order_type', '=', 1)
            ->where('p.product_type', '=', 2) // 计量商品
            ->sum('iod.quantity');
            
        $weightOutStock = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id')
            ->join('inventory_order io', 'io.order_no = iod.order_no')
            ->where($where)
            ->whereIn('io.order_type', [2, 3])
            ->where('p.product_type', '=', 2) // 计量商品
            ->sum('iod.quantity');
        
        return json([
            'code' => 0, 
            'msg' => 'success', 
            'count' => $count, 
            'data' => $list,
            'stats' => [
                'total_in_stock' => $totalInStock,
                'total_out_stock' => $totalOutStock,
                'difference' => $difference,
                'normal_in_stock' => $normalInStock,
                'normal_out_stock' => $normalOutStock,
                'weight_in_stock' => $weightInStock,
                'weight_out_stock' => $weightOutStock,
                'normal_difference' => round($normalInStock - $normalOutStock, 2),
                'weight_difference' => round($weightInStock - $weightOutStock, 2)
            ]
        ]);
    }

    private function getStockTypeText($order_type)
    {
        if ($order_type == 1) {
            return 'in';
        } else {
            return 'out';
        }
    }


    /**
     * 导出入库单详情列表
     */
    public function exportInStockDetailList()
    {
        // 权限校验
        check_auth(request()->controller() . '/index', 0);
        
        // 获取筛选条件
        $order_no = input('order_no', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $shop_id = input('shop_id', '');
        $product_id = input('product_id', '');
        $inventory_id = input('inventory_id', '');
        $date_range = input('date_range', '');
        $is_manual = input('is_manual', '');

        // 构建查询条件
        $where = [];
        // 基本条件
        $where[] = ['io.order_type', '=', 1]; // 只查询入库单
        
        // 主表条件
        if (!empty($order_no)) {
            $where[] = ['io.order_no', 'like', "%{$order_no}%"];
        }
        if (!empty($business_type)) {
            $where[] = ['io.business_type', '=', $business_type];
        }
        if ($status !== '') {
            $where[] = ['io.status', '=', $status];
        }
        if (!empty($shop_id)) {
            $where[] = ['io.shop_id', '=', $shop_id];
        }
        if (!empty($date_range)) {
            $dates = explode(' - ', $date_range);
            if (count($dates) == 2) {
                $where[] = ['io.created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
            }
        }
        // 添加手动录入筛选条件
        if ($is_manual === '1') {
            $where[] = ['io.related_order_no', 'null'];
        } elseif ($is_manual === '0') {
            $where[] = ['io.related_order_no', 'not null'];
        }
        
        // 子表条件
        if (!empty($product_id)) {
            $where[] = ['iod.product_id', '=', $product_id];
        }
        if (!empty($inventory_id)) {
            $where[] = ['iod.inventory_id', '=', $inventory_id];
        }
        
        // 直接使用连表查询获取所有需要的数据
        $details = Db::name('inventory_order_detail')->alias('iod')
            ->join('inventory_order io', 'io.id = iod.order_id')
            ->join('products p', 'p.id = iod.product_id', 'LEFT')
            ->join('product_inventory pi', 'pi.id = iod.inventory_id', 'LEFT')
            ->join('shop s', 's.id = io.shop_id', 'LEFT')
            ->where($where)
            ->field('iod.*, io.order_no, io.shop_id, io.business_type, io.status, io.created_by, io.reviewed_by, io.created_at, io.remark, io.supplier_name, io.order_type,
                    p.title as product_name, p.product_type, pi.title as inventory_name, s.title as shop_name, io.related_order_no')
            ->order('iod.id', 'desc')
            ->select();
        
        if (empty($details)) {
            return json(['code' => -1, 'msg' => '未找到符合条件的入库单详情']);
        }
        
        // 准备导出数据
        $export_data = [];
        
        foreach ($details as $detail) {
            // 构建导出数据行
            $export_row = [
                '单据编号' => $detail['order_no'],
                '业务类型' => $this->getBusinessTypeText($detail['order_type'], $detail['business_type']),
                '门店名称' => $detail['shop_name'] ?: '',
                '供应商' => $detail['supplier_name'],
                '状态' => $this->getStatusText($detail['status']),
                '商品名称' => $detail['product_name'] ?: '',
                '规格' => $detail['inventory_name'] ?: '默认规格',
                '商品类型' => $detail['product_type'] == 2 ? '计量商品' : '普通商品',
                '单位' => $detail['unit'],
                '数量' => $detail['quantity'],
                '单价' => $detail['price'],
                '金额' => $detail['amount'],
                '创建人' => $detail['created_by'],
                '创建时间' => $detail['created_at'],
                '审核人' => $detail['reviewed_by'] ?: '未审核',
                '备注' => $detail['remark']
            ];
            
            $export_data[] = $export_row;
        }
        
        if (empty($export_data)) {
            return json(['code' => -1, 'msg' => '没有可导出的入库单详情数据']);
        }
        
        // 生成文件名
        $filename = '入库单详情列表_' . date('YmdHis') . '.xlsx';
        $filepath = \think\facade\Env::get('runtime_path') . 'temp/' . $filename;
        
        // 确保目录存在
        if (!is_dir(\think\facade\Env::get('runtime_path') . 'temp/')) {
            mkdir(\think\facade\Env::get('runtime_path') . 'temp/', 0777, true);
        }
        
        // 使用SimpleExcel导出数据
        $writer = SimpleExcelWriter::create($filepath);
        $writer->addRows($export_data);
        
        // 下载文件
        return download($filepath, $filename);
    }

    /**
     * 导出出入库单详情列表
     */
    public function exportOutStockDetailList()
    {
        // 权限校验
        check_auth(request()->controller() . '/outstock', 0);
        
        // 获取筛选条件
        $order_no = input('order_no', '');
        $stock_type = input('stock_type', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $shop_id = input('shop_id', '');
        $product_id = input('product_id', '');
        $inventory_id = input('inventory_id', '');
        $date_range = input('date_range', '');
        $is_manual = input('is_manual', '');

        // 构建查询条件
        $where = [];
        
        // 根据单据类型筛选
        if (!empty($stock_type)) {
            $order_type = $stock_type === 'in' ? 1 : 2;
            $where[] = ['io.order_type', '=', $order_type];
        }
        
        // 主表条件
        if (!empty($order_no)) {
            $where[] = ['io.order_no', 'like', "%{$order_no}%"];
        }
        if (!empty($business_type)) {
            $where[] = ['io.business_type', '=', $business_type];
        }
        if ($status !== '') {
            $where[] = ['io.status', '=', $status];
        }
        if (!empty($shop_id)) {
            $where[] = ['io.shop_id', '=', $shop_id];
        }
        if (!empty($date_range)) {
            $dates = explode(' - ', $date_range);
            if (count($dates) == 2) {
                $where[] = ['io.created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
            }
        }
        // 添加手动录入筛选条件
        if ($is_manual === '1') {
            $where[] = ['io.related_order_no', 'null'];
        } elseif ($is_manual === '0') {
            $where[] = ['io.related_order_no', 'not null'];
        }
        
        // 子表条件
        if (!empty($product_id)) {
            $where[] = ['iod.product_id', '=', $product_id];
        }
        if (!empty($inventory_id)) {
            $where[] = ['iod.inventory_id', '=', $inventory_id];
        }
        
        // 直接使用连表查询获取所有需要的数据
        $details = Db::name('inventory_order_detail')->alias('iod')
            ->join('inventory_order io', 'io.id = iod.order_id')
            ->join('products p', 'p.id = iod.product_id', 'LEFT')
            ->join('product_inventory pi', 'pi.id = iod.inventory_id', 'LEFT')
            ->join('shop s', 's.id = io.shop_id', 'LEFT')
            ->where($where)
            ->field('iod.*, io.order_no, io.shop_id, io.business_type, io.status, io.created_by, io.reviewed_by, io.created_at, io.remark, io.supplier_name, io.order_type,
                    p.title as product_name, p.product_type, pi.title as inventory_name, s.title as shop_name')
            ->order('iod.id', 'desc')
            ->select();
        
        if (empty($details)) {
            $message = !empty($stock_type) ? ($stock_type === 'in' ? '入库单' : '出库单') : '出入库单';
            return json(['code' => -1, 'msg' => '未找到符合条件的' . $message . '详情']);
        }
        
        // 准备导出数据
        $export_data = [];
        
        foreach ($details as $detail) {
            // // 处理会员信息
            // $member_name = '';
            // if ($detail['member_id']) {
            //     $member_name = $detail['nickname'] ?: $detail['mobile'];
            // }
            
            // 构建导出数据行
            $export_row = [
                '单据编号' => $detail['order_no'],
                '业务类型' => $this->getBusinessTypeText($detail['order_type'], $detail['business_type']),
                '门店名称' => $detail['shop_name'] ?: '',
                '相关单位' => $detail['supplier_name'],
                // '会员' => $member_name,
                '状态' => $this->getStatusText($detail['status']),
                '商品名称' => $detail['product_name'] ?: '',
                '规格' => $detail['inventory_name'] ?: '默认规格',
                '商品类型' => $detail['product_type'] == 2 ? '计量商品' : '普通商品',
                '单位' => $detail['unit'],
                '数量' => $detail['quantity'],
                '单价' => $detail['price'],
                '金额' => $detail['amount'],
                '创建人' => $detail['created_by'],
                '创建时间' => $detail['created_at'],
                '审核人' => $detail['reviewed_by'] ?: '未审核',
                '备注' => $detail['remark']
            ];
            
            $export_data[] = $export_row;
        }
        
        if (empty($export_data)) {
            $message = !empty($stock_type) ? ($stock_type === 'in' ? '入库单' : '出库单') : '出入库单';
            return json(['code' => -1, 'msg' => '没有可导出的' . $message . '详情数据']);
        }
        
        // 生成文件名
        $title = !empty($stock_type) ? ($stock_type === 'in' ? '入库单' : '出库单') : '出入库单';
        $filename = $title . '详情列表_' . date('YmdHis') . '.xlsx';
        $filepath = \think\facade\Env::get('runtime_path') . 'temp/' . $filename;
        
        // 确保目录存在
        if (!is_dir(\think\facade\Env::get('runtime_path') . 'temp/')) {
            mkdir(\think\facade\Env::get('runtime_path') . 'temp/', 0777, true);
        }
        
        // 使用SimpleExcel导出数据
        $writer = SimpleExcelWriter::create($filepath);
        $writer->addRows($export_data);
        
        // 下载文件
        return download($filepath, $filename);
    }

    /**
     * 获取库存统计数据
     * 用于前端AJAX请求更新库存统计卡片
     */
    public function getStockStats()
    {
        // 获取筛选参数
        $shop_id = input('shop_id', 0, 'intval');
        $product_type = input('product_type', '');
        $product_id = input('product_id', 0, 'intval');
        $inventory_id = input('inventory_id', 0, 'intval');
        $stock_status = input('stock_status', '');
        
        // 构建查询条件
        $where = [];
        if ($shop_id > 0) {
            $where[] = ['spi.shop_id', '=', $shop_id];
        }
        if ($product_type !== '') {
            $where[] = ['p.product_type', '=', $product_type];
        }
        if ($product_id > 0) {
            $where[] = ['spi.product_id', '=', $product_id];
        }
        if ($inventory_id > 0) {
            $where[] = ['spi.inventory_id', '=', $inventory_id];
        }
        
        // 构建基础查询对象
        $query = Db::name('shop_product_inventory')->alias('spi')
            ->join('products p', 'p.id = spi.product_id')
            ->where($where);
            
        // 处理库存状态筛选
        if ($stock_status == 1) {
            // 有库存：普通商品stock>0 或 计量商品weight_stock>0
            $query->whereRaw('((p.product_type = 1 AND spi.stock > 0) OR (p.product_type = 2 AND spi.weight_stock > 0) OR (p.product_type = 3 AND spi.stock > 0))');
        } elseif ($stock_status == 2) {
            // 无库存：普通商品stock<=0 或 计量商品weight_stock<=0
            $query->whereRaw('((p.product_type = 1 AND spi.stock <= 0) OR (p.product_type = 2 AND spi.weight_stock <= 0) OR (p.product_type = 3 AND spi.stock <= 0))');
        }
        
        // 统计数据 - 修改为使用distinct方法更准确地统计商品数量
        $stats = $query->field([
            'count(distinct spi.product_id) as total_products',
            'sum(case when p.product_type IN (1, 3) then spi.stock else 0 end) as total_normal_stock',
            'sum(case when p.product_type = 2 then spi.weight_stock else 0 end) as total_weight_stock',
            'count(distinct case when p.product_type IN (1, 3) then spi.product_id end) as normal_product_count',
            'count(distinct case when p.product_type = 2 then spi.product_id end) as weight_product_count'
        ])->find();
        
        // 格式化统计数据
        $total_normal_stock = round($stats['total_normal_stock'] ?? 0, 2);
        $total_weight_stock = round($stats['total_weight_stock'] ?? 0, 2);
        
        // 验证计量商品总量，如果不存在计量商品则设置为0
        if ($stats['weight_product_count'] == 0) {
            $total_weight_stock = 0;
        }
        
        $stockStats = [
            'total_products' => $stats['total_products'] ?? 0,
            'total_normal_stock' => $total_normal_stock,
            'total_weight_stock' => $total_weight_stock,
            'normal_product_count' => $stats['normal_product_count'] ?? 0,
            'weight_product_count' => $stats['weight_product_count'] ?? 0,
            'total_stock_value' => $total_normal_stock + $total_weight_stock // 添加总库存值
        ];
        
        return json(['code' => 0, 'msg' => 'success', 'data' => $stockStats]);
    }

    /**
     * 导出出入库单详情列表 - 别名方法
     */
    public function exportStockDetailList()
    {
        // 权限校验
        check_auth(request()->controller() . '/outstock', 0);
        
        // 获取筛选条件
        $order_no = input('order_no', '');
        $stock_type = input('stock_type', '');
        $business_type = input('business_type', '');
        $status = input('status', '');
        $shop_id = input('shop_id', '');
        $product_id = input('product_id', '');
        $inventory_id = input('inventory_id', '');
        $date_range = input('date_range', '');
        $is_manual = input('is_manual', '');

        // 构建查询条件
        $where = [];
        
        // 根据单据类型筛选
        if (!empty($stock_type)) {
            $order_type = $stock_type === 'in' ? 1 : 2;
            if ($order_type == 1) {
                $where[] = ['iod.order_type', '=', 1];
            } else {
                $where[] = ['iod.order_type', 'in', [2, 3]];
            }
        }
        
        // 主表条件
        if (!empty($order_no)) {
            $where[] = ['iod.order_no', 'like', "%{$order_no}%"];
        }
        if (!empty($shop_id)) {
            $where[] = ['iod.shop_id', '=', $shop_id];
        }
        if (!empty($date_range)) {
            $dates = explode(' - ', $date_range);
            if (count($dates) == 2) {
                $where[] = ['iod.created_at', 'between time', [trim($dates[0]), trim($dates[1]) . ' 23:59:59']];
            }
        }
        
        // 添加手动录入筛选条件
        if ($is_manual === '1') {
            $where[] = ['io.related_order_no', 'null'];
        } elseif ($is_manual === '0') {
            $where[] = ['io.related_order_no', 'not null'];
        }
        
        // 子表条件
        if (!empty($product_id)) {
            $where[] = ['iod.product_id', '=', $product_id];
        }
        if (!empty($inventory_id)) {
            $where[] = ['iod.inventory_id', '=', $inventory_id];
        }
        
        // 获取分页数据 - 直接使用连表查询获取所有需要的数据
        $details = Db::name('inventory_order_detail')->alias('iod')
            ->join('products p', 'p.id = iod.product_id', 'LEFT')
            ->join('product_inventory pi', 'pi.id = iod.inventory_id', 'LEFT')
            ->join('shop s', 's.id = iod.shop_id', 'LEFT')
            ->join('inventory_order io', 'io.id = iod.order_id', 'LEFT')
            ->where($where)
            ->field('iod.*, s.title as shop_name, p.title as product_name, p.product_type, pi.title as inventory_name')
            ->order('iod.id', 'desc')
            ->select();
        
        if (empty($details)) {
            $message = !empty($stock_type) ? ($stock_type === 'in' ? '入库单' : '出库单') : '出入库单';
            return json(['code' => -1, 'msg' => '未找到符合条件的' . $message . '详情']);
        }
        
        // 准备导出数据
        $export_data = [];
        
        foreach ($details as $detail) {
            // 处理单据类型显示
            $stock_type_text = $detail['order_type'] == 1 ? '入库' : '出库';
            
            // 处理商品类型显示
            $product_type_text = $detail['product_type'] == 2 ? '计量商品' : '普通商品';
            
            // 处理数量/重量显示
            $quantity_display = $detail['product_type'] == 2 ? $detail['quantity'] . 'kg' : $detail['quantity'] . '件';
            
            // 构建导出数据行
            $export_row = [
                '单据编号' => $detail['order_no'],
                '门店名称' => $detail['shop_name'] ?: '',
                '单据类型' => $stock_type_text,
                '商品名称' => $detail['product_name'] ?: '',
                '规格名称' => $detail['inventory_name'] ?: '默认规格',
                '商品类型' => $product_type_text,
                '数量/重量' => $quantity_display,
                '单价' => '￥' . $detail['price'],
                '金额' => '￥' . $detail['amount'],
                '创建时间' => $detail['created_at']
            ];
            
            $export_data[] = $export_row;
        }
        
        if (empty($export_data)) {
            $message = !empty($stock_type) ? ($stock_type === 'in' ? '入库单' : '出库单') : '出入库单';
            return json(['code' => -1, 'msg' => '没有可导出的' . $message . '详情数据']);
        }
        
        // 生成文件名
        $title = !empty($stock_type) ? ($stock_type === 'in' ? '入库单' : '出库单') : '出入库单';
        $filename = $title . '详情列表_' . date('YmdHis') . '.xlsx';
        $filepath = \think\facade\Env::get('runtime_path') . 'temp/' . $filename;
        
        // 确保目录存在
        if (!is_dir(\think\facade\Env::get('runtime_path') . 'temp/')) {
            mkdir(\think\facade\Env::get('runtime_path') . 'temp/', 0777, true);
        }
        
        // 使用SimpleExcel导出数据
        $writer = SimpleExcelWriter::create($filepath);
        $writer->addRows($export_data);
        
        // 下载文件
        return download($filepath, $filename);
    }
}


