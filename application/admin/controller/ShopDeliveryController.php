<?php

namespace app\admin\controller;

use think\Request;
use think\Db;

/**
 * 门店配送能力控制器
 * 用于门店端管理配送时间段和配送能力
 */
class ShopDeliveryController extends CnController
{
    /**
     * 初始化方法
     */
    public function initialize()
    {
        $this->init();
        //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    /**
     * 门店配送能力配置页面
     */
    public function capacity()
    {
        // 权限校验
        check_auth(request()->controller() . '/capacity', 0);
        
        // 获取平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.parent_basekeynum');
        $where = ['keynum' => $basekeynum];
        $shop = Db::table('shop')->where($where)->find();
        if(empty($shop)) {
            error_tips('门店不存在');
        }
        
        // 获取当前登录用户的门店ID
        $shopId = $shop['id'];
        
        // 检查用户是否拥有门店权限
        if (!$shopId) {
            error_tips('您没有管理门店的权限');
        }
        
        // 获取门店信息
        $shop = Db::table('shop')->where("id = $shopId AND keynum = '$basekeynum'")->find();
        
        if (!$shop) {
            error_tips('门店不存在');
        }
        
        // 获取当前门店的配送能力配置
        $capacityList = Db::table('shop_delivery_capacity')
            ->where("shop_id = $shopId AND basekeynum = '$clientkeynum'")
            ->order('delivery_time_range')
            ->select();
        
        $this->assign('shop', $shop);
        $this->assign('capacityList', $capacityList);
        
        return $this->fetch('capacity');
    }

    /**
     * 添加或编辑门店配送能力
     */
    public function saveCapacity(Request $request)
    {
        // 权限校验
        check_auth(request()->controller() . '/capacity', 1);
        
        // 获取平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.parent_basekeynum');
        $where = ['keynum' => $basekeynum];
        $shop = Db::table('shop')->where($where)->find();
        if(empty($shop)) {
            $this->error('门店不存在');
        }
        
        // 获取当前登录用户的门店ID
        $shopId = $shop['id'];
        
        // 检查用户是否拥有门店权限
        if (!$shopId) {
            return json(['sta' => 0, 'msg' => '您没有管理门店的权限']);
        }
        
        // 获取表单数据
        $param = $request->param();
        $id = isset($param['id']) ? intval($param['id']) : 0;
        $timeRange = isset($param['delivery_time_range']) ? $param['delivery_time_range'] : '';
        $maxCapacity = isset($param['max_capacity']) ? intval($param['max_capacity']) : 0;
        $status = isset($param['status']) ? intval($param['status']) : 1;
        
        // 表单验证
        if (empty($timeRange)) {
            return json(['sta' => 0, 'msg' => '请输入配送时间段']);
        }
        
        if ($maxCapacity <= 0) {
            return json(['sta' => 0, 'msg' => '最大配送量必须大于0']);
        }
        
        // 检查时间段格式是否正确（如：9:00-10:00）
        if (!preg_match('/^\d{1,2}:\d{2}-\d{1,2}:\d{2}$/', $timeRange)) {
            return json(['sta' => 0, 'msg' => '时间段格式不正确，正确格式如：9:00-10:00']);
        }
        
        // 检查该店铺下是否已有相同时间段
        if ($id == 0) {  // 新增时才检查
            $existTimeRange = Db::table('shop_delivery_capacity')
                ->where("shop_id = $shopId AND basekeynum = '$clientkeynum' AND delivery_time_range = '$timeRange'")
                ->find();
            if ($existTimeRange) {
                return json(['sta' => 0, 'msg' => '该时间段已存在，不可重复添加']);
            }
        }
        
        // 准备数据
        $data = [
            'shop_id' => $shopId,
            'basekeynum' => $clientkeynum,
            'delivery_time_range' => $timeRange,
            'max_capacity' => $maxCapacity,
            'status' => $status,
            'update_time' => date('Y-m-d H:i:s'),
            'operator_id' => session('cn_accountinfo.id')
        ];
        
        // 保存数据
        if ($id > 0) {
            // 检查记录是否属于当前门店
            $checkRecord = Db::table('shop_delivery_capacity')->where('id', $id)->find();
            if (!$checkRecord || $checkRecord['shop_id'] != $shopId) {
                return json(['sta' => 0, 'msg' => '无权修改此配送时间段']);
            }
            
            // 更新
            $result = Db::table('shop_delivery_capacity')->where('id', $id)->update($data);
            $operateLog = '修改门店配送能力配置';
        } else {
            // 新增
            $data['add_time'] = date('Y-m-d H:i:s');
            $result = Db::table('shop_delivery_capacity')->insert($data);
            $operateLog = '添加门店配送能力配置';
        }
        
        if ($result === false) {
            return json(['sta' => 0, 'msg' => '操作失败']);
        }
        
        // 记录操作日志
        addoperatelog($operateLog, json_encode($param, JSON_UNESCAPED_UNICODE));
        
        return json(['sta' => 1, 'msg' => '操作成功']);
    }

    /**
     * 获取门店配送能力列表（AJAX）
     */
    public function getCapacityList(Request $request)
    {
        // 权限校验
        check_auth(request()->controller() . '/capacity', 1);
        
        // 获取平台客户的keynum
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.parent_basekeynum');

        $where = ['keynum' => $basekeynum];
        $shop = Db::table('shop')->where($where)->find();
        if(empty($shop)) {
            error_tips('门店不存在');
        }
        
        // 获取当前登录用户的门店ID
        $shopId = $shop['id'];
        
        if (empty($shopId)) {
            return json(['sta' => 0, 'msg' => '您没有管理门店的权限']);
        }
        
        // 获取数据
        $list = Db::table('shop_delivery_capacity')
            ->where("shop_id = $shopId AND basekeynum = '$clientkeynum'")
            ->order('delivery_time_range')
            ->select();
        
        // 获取门店信息
        $shop = Db::table('shop')->where('id', $shopId)->find();
        
        return json([
            'sta' => 1, 
            'data' => [
                'list' => $list,
                'shop' => $shop
            ]
        ]);
    }

    /**
     * 删除门店配送能力设置
     */
    public function deleteCapacity(Request $request)
    {
        // 权限校验
        check_auth(request()->controller() . '/capacity', 1);
        
        // 获取当前登录用户的门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.parent_basekeynum');
        $where = ['keynum' => $basekeynum];
        $shop = Db::table('shop')->where($where)->find();
        if(empty($shop)) {
            return json(['sta' => 0, 'msg' => '门店不存在']);
        }
        $shopId = $shop['id'];
        
        $id = $request->param('id', 0);
        
        if (empty($id)) {
            return json(['sta' => 0, 'msg' => '参数错误']);
        }
        
        // 检查记录是否属于当前门店
        $checkRecord = Db::table('shop_delivery_capacity')->where('id', $id)->find();
        if (!$checkRecord || $checkRecord['shop_id'] != $shopId) {
            return json(['sta' => 0, 'msg' => '无权删除此配送时间段']);
        }
        
        // 删除数据
        $result = Db::table('shop_delivery_capacity')->where('id', $id)->delete();
        
        if ($result === false) {
            return json(['sta' => 0, 'msg' => '删除失败']);
        }
        
        // 记录操作日志
        addoperatelog('删除门店配送能力配置', json_encode(['id' => $id], JSON_UNESCAPED_UNICODE));
        
        return json(['sta' => 1, 'msg' => '删除成功']);
    }

    /**
     * 更新门店配送能力状态
     */
    public function updateStatus(Request $request)
    {
        // 权限校验
        check_auth(request()->controller() . '/capacity', 1);
        
        // 获取当前登录用户的门店ID
        $basekeynum = session('cn_accountinfo.keynum');
        $clientkeynum = session('cn_accountinfo.parent_basekeynum');
        $where = ['keynum' => $basekeynum];
        $shop = Db::table('shop')->where($where)->find();
        if(empty($shop)) {
            return json(['sta' => 0, 'msg' => '门店不存在']);
        }
        $shopId = $shop['id'];
        
        $id = $request->param('id', 0);
        $status = $request->param('status', 0);
        
        if (empty($id)) {
            return json(['sta' => 0, 'msg' => '参数错误']);
        }
        
        // 检查记录是否属于当前门店
        $checkRecord = Db::table('shop_delivery_capacity')->where('id', $id)->find();
        if (!$checkRecord || $checkRecord['shop_id'] != $shopId) {
            return json(['sta' => 0, 'msg' => '无权修改此配送时间段']);
        }
        
        // 更新状态
        $result = Db::table('shop_delivery_capacity')
            ->where('id', $id)
            ->update([
                'status' => $status, 
                'update_time' => date('Y-m-d H:i:s'),
                'operator_id' => session('cn_accountinfo.id')
            ]);
        
        if ($result === false) {
            return json(['sta' => 0, 'msg' => '操作失败']);
        }
        
        // 记录操作日志
        $statusText = $status == 1 ? '启用' : '禁用';
        addoperatelog("{$statusText}门店配送能力配置", json_encode(['id' => $id, 'status' => $status], JSON_UNESCAPED_UNICODE));
        
        return json(['sta' => 1, 'msg' => '操作成功']);
    }
} 