<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright (c) 2020 01 28 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\admin\controller;

use think\facade\Session;
use think\facade\Request;
use think\Db;

class IndexController extends CnController
{
    /**
     * 初始化方法
     */
    public function initialize()
    {
        $this->init(); //每个后台控制器必须增加这个，校验未登录用户直接退出
    }

    //首页
    public function index()
    {
        return $this->fetch('index');
    }

    //控制台页面
    public function console()
    {
        $basekeynum = session("cn_accountinfo.basekeynum");
        //服务器信息开始
        $request = Request::instance();
        $param = $request->param();
        $sys_os = strtolower(substr(PHP_OS, 0, 3));
        if ($sys_os == 'win') {
            //异步获取
            if (!empty($param["ajax"]) && $param["ajax"] == "ajax") {
                //windows服务器
                include("../extend/SystemInfoWindows.php");
                $info = new  \SystemInfoWindows();
                $cpu = $info->getCpuUsage();
                $memory = $info->getMemoryUsage();
                //echo "当前系统CPU使用率：{$cpu}%，内存使用率{$memory['usage']}%";
                $return_arr['sta'] = '1';
                $return_arr['cpu'] = $cpu . "%";
                $return_arr['memory'] = $memory['usage'] . "%";
                echo json_encode($return_arr);
                die;
            }


            $path = substr(dirname(dirname(__FILE__)), 0, 2); //获取盘符
            $free = @disk_free_space($path);   //计算剩余字节数
            $total = @disk_total_space($path);   //计算整个磁盘总大小
            $total_space = getFilesize($total);
            $free_space = getFilesize($free);
            $disk_usage = round(($total_space - $free_space) / $total_space * 100);
            $this->assign("path", $path);
            $this->assign("total_space", $total_space);
            $this->assign("free_space", $free_space);
            $this->assign("disk_usage", $disk_usage);
        } else {

            //linux服务器
//            include("../extend/SystemInfoLinux.php");
//            $info = new  \SystemInfoLinux();
//            $disklistarr = $info->getDsikList();
//            //print_r($disklistarr);
//            if ($disklistarr['sta'] == '1') {
//                $this->assign("disklist", $disklistarr['rs']);
//            } else {
//                $this->assign("err_msg", $disklistarr['msg']);
//            }
        }
        $this->assign("sys_os", $sys_os);
        //服务器信息结束



        //如果是总平台登录
        if ($basekeynum == '平台') {
            //获取订单商品会员应用数量总和
            $where = "1=1";

            $all_order_count = 0; //Db::table("dgss_order_info")->where("status not in (0,99)")->count();
            $this->assign("all_order_count", $all_order_count);

            $dg_order_count = 0; //Db::table("dgss_order_info")->where("ismyself='0' and status not in (0,99)")->count();
            $this->assign("dg_order_count", $dg_order_count);

            $ddcf_order_count = 0; //Db::table("dgss_order_info")->where("ismyself='1' and status not in (0,99)")->count();
            $this->assign("ddcf_order_count", $ddcf_order_count);

            $client_member_count = 0; //db('client_member')->count();
            $this->assign("client_member_count", $client_member_count);

            $client_card_count = 0; //db('client_card')->count();
            $this->assign("client_card_count", $client_card_count);

            $client_ycard_count = 0; //db('client_ycard')->count();
            $this->assign("client_ycard_count", $client_ycard_count);
            //获取mysql版本号
            $rs = Db::query('select VERSION() as sqlversion');
            $mysql_version = $rs[0]['sqlversion'];
            $this->assign("mysql_version", $mysql_version);
            //获取web服务器
            if (PHP_SAPI == 'fpm-fcgi') {
                $http_version = "nginx + php-fpm";
            } else if (PHP_SAPI == 'cgi-fcgi') {
                $http_version = "nginx + fastcgi";
            } else if (PHP_SAPI == 'apache2handler') {
                $http_version = "apache";
            } else if (PHP_SAPI == 'cli') {
                $http_version = "php命令行";
            } else {
                $http_version = "未知";
            }
            $this->assign("keynum", $basekeynum);
            $this->assign("http_version", $http_version);
            //获取redis版本
            // $redis = new \Redis();
            // $redis->connect(config("session")['host'], config("session")['port']);
            // $redis->auth(config("session")['password']); //密码验证
            // $info = $redis->info();
            // $redis->close();
            // $this->assign("redis_version", $info['redis_version']);

            return $this->fetch('plat/customerlist');
            //平台客户以及平台客户下面的
        } else {
            $plat_accountinfo = Db::table("plat_account")->where("keynum='$basekeynum'")->find();
            if ($plat_accountinfo['tablename'] == 'plat_client') {  //平台客户页面
                //统计报表数据源
                //最近7天会员增长
                $recent_weeks = get_weeks();
                $recent_weeks_str = implode(",", $recent_weeks);
                $this->assign("recent_weeks_str", $recent_weeks_str);
                $recent_weeks_member = get_weeks_member();
                $recent_weeks_member_str = implode(",", $recent_weeks_member);
                $this->assign("recent_weeks_member_str", $recent_weeks_member_str);
                //终端访问统计
                $terminal = get_terminal();
                $terminal_name_str = implode(",", $terminal['name']);
                $terminal_visit_str = implode(",", $terminal['visit_nums']);
                $this->assign("terminal_name_str", $terminal_name_str);
                $this->assign("terminal_visit_str", $terminal_visit_str);

                //订单走势图
                //取出来最后一条订单日期
                // $info = Db::table('dgss_order_info')->where("clientkeynum='$basekeynum'")->order("add_time desc")->limit(1)->find();
                // $add_time = $info['add_time'];
                // //$add_time=strtotime("2021-02-08 10:30:00");
                // $ts = $add_time;
                // $time_arr = array();
                // for ($x = 0; $x <= 3 * 60; $x++) {
                //     $time_arr[] = "'" . date("Y-m-d H:i:s", $ts - $x) . "'";
                //     $add_time0 = $ts - $x;
                //     $nums = Db::table('dgss_order_info')->where("clientkeynum='$basekeynum'")->where("add_time='$add_time0'")->count();
                //     $data0[] = "{value:" . $nums . ", name:'" . date("Y-m-d H:i:s", $ts - $x) . "'}";
                // }
                // $time_str = implode(',', $time_arr);
                // $this->assign("order_time_str", $time_str);
                // $time_order_nums_str = implode(",", $data0);
                // $this->assign("time_order_nums_str", $time_order_nums_str);


                //流量趋势图
                $recent_visit = get_recent_visit();
                $recent_visit_hour_str = implode(",", $recent_visit['name']);
                $recent_visit_pv_nums_str = implode(",", $recent_visit['visit_pv_nums']);
                $recent_visit_uv_nums_str = implode(",", $recent_visit['visit_uv_nums']);
                $this->assign("recent_visit_hour_str", $recent_visit_hour_str);
                $this->assign("recent_visit_pv_nums_str", $recent_visit_pv_nums_str);
                $this->assign("recent_visit_uv_nums_str", $recent_visit_uv_nums_str);

                //获取订单商品会员应用数量总和
                // $where = "clientkeynum='$basekeynum'";
                // $all_order_count = Db::table("dgss_order_info")->where($where)->count();
                // $all_good_count = Db::table("dgss_goods")->where($where)->count();
                // $all_activity_count = Db::table("client_activity")->where($where)->count();
                // $all_project_count = Db::table("client_project")->where($where)->count();

                // $this->assign("all_activity_count", $all_activity_count);
                // $this->assign("all_project_count", $all_project_count);
                // $this->assign("all_order_count", $all_order_count);
                // $this->assign("all_good_count", $all_good_count);

                // 校验平台供应链的余额 如果不足最近七天消耗金额 就提示余额不足
                // $start_time = strtotime("-16 day");
                // $end_time = strtotime("-1 day");
                // $order_list = Db::table("dgss_order_info")
                //     ->where("clientkeynum='$basekeynum' and status not in (0,99) and add_time>='$start_time' and add_time<='$end_time'")
                //     ->field("dgss_return_json")
                //     ->select();
                // $order_total = 0;
                // foreach ($order_list as $ok => $ov) {
                //     $dgss_return_json = json_decode($ov["dgss_return_json"], 1);
                //     $order_total += $dgss_return_json["order"]["final_amount"];
                // }
                // $action = "order/get_channel_order_money";
                // $result_arr = send_to_dgss($action);
                // $remain_money = $result_arr["data"]["remain_money"];
                // if (empty($remain_money)) {
                //     $remain_money = 0;
                // }
                // $msg = "";
                // if ($remain_money < $order_total) {
                //     $msg = "您近15天供应链消费总额：" . $order_total . " 您当前供应链余额为：" . $remain_money . " 您当前余额不满足15天消费,请您及时处理";
                // }
                // $this->assign("msg", $msg);
                return $this->fetch('console2');
            } else {  //平台客户下面的商户
                $keynum = session("cn_accountinfo.keynum");
                //获取积分商户信息
                $plat_accountinfo = Db::table("plat_account")->where("keynum='$keynum' and basekeynum='$basekeynum' ")->find();
                $this->assign("plat_accountinfo", $plat_accountinfo);
                //获取订单商品会员应用数量总和
                // $where = "clientkeynum='$basekeynum'";
                // $all_order_count = Db::table("dgss_order_info")->where($where)->count();
                // $this->assign("all_order_count", $all_order_count);
                //平台总后台以及平台客户之外的身份登录
                return $this->fetch('console3');
            }
        }
    }
}
