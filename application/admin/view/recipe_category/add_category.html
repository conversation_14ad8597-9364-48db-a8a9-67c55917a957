<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>添加食谱分类</title>
    {include file="public/iframeheader"/}
</head>

<body>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">添加食谱分类</div>
                <div class="layui-card-body" pad15>

                    <div class="layui-form" wid100 lay-filter="">
                        <div class="layui-form-item">
                            <label class="layui-form-label">分类名称</label>
                            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                            <div class="layui-input-inline">
                                <input type="text" name="name" lay-verify="required" placeholder="请输入分类名称" autocomplete="off" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">父级分类</label>
                            <div class="layui-input-inline">
                                <select name="parent_id" lay-search>
                                    <option value="0">顶级分类</option>
                                    {volist name="categories" id="category"}
                                    <option value="{$category.id}">{$category.name}</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">排序</label>
                            <div class="layui-input-inline">
                                <input type="number" name="sort" value="0" lay-verify="number" placeholder="请输入排序" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux">数字越小越靠前</div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <input type="radio" name="status" value="1" title="正常" checked>
                                <input type="radio" name="status" value="0" title="禁用">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="submit">立即提交</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                <button type="button" class="layui-btn layui-btn-normal" id="back">返回</button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['form', 'layer'], function () {
        var $ = layui.$,
            form = layui.form,
            layer = layui.layer;

        // 表单提交
        form.on('submit(submit)', function (data) {
            var field = data.field;
            
            // 提交数据
            var load = layer.load(2, {shade: [0.1, '#fff']});
            $.post("{:url('ajax_add_category')}", field, function (res) {
                layer.close(load);
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 1});
                    setTimeout(function () {
                        location.href = '{:url("category_list")}';
                    }, 1000);
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, "json");
            
            return false;
        });

        // 返回按钮
        $('#back').click(function () {
            location.href = '{:url("category_list")}';
        });
    });
</script>

</body>

</html> 