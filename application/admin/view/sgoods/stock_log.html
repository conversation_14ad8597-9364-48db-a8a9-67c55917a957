<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}
<link rel="stylesheet" type="text/css" href="__STATIC__/cnadmin/jquery-easyui-1.5.2/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="__STATIC__/cnadmin/jquery-easyui-1.5.2/themes/icon.css">
<script type="text/javascript" src="__STATIC__/cnadmin/jquery-easyui-1.5.2/jquery.easyui.min.js"></script>

<body>
    <br />
    <div id="main">


        <div style="margin:0  auto;width:95%;">
            <input type='hidden' name='keynum'>
            <div class="demoTable layui-form">

                <div class="layui-inline">
                    <label class="layui-form-label ">查询条件：</label>
                    <div class="layui-input-block">
                        <select name="field">
                            <option value='product_name'>商品名称</option>
                            <option value='spec_name'>规格名称</option>
                        </select>
                    </div>
                </div>


                <div class='layui-inline'>
                    <input class="layui-input" name="keyword" id="demoReload" autocomplete="off">
                </div>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="开始时间" id="begin_time" name="begin_time" autocomplete="off">
                </div>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="截止时间" id="end_time" name="end_time" autocomplete="off">
                </div>
                <button class="layui-btn  layui-btn-sm" onclick="get_table()" id="reload">搜索</button>
            </div>
            </form>


            <table id="demo" lay-filter="demo">
            </table>

        </div>

        <script>
            get_table();
            function get_table() {
                var keynum = $("[name=keynum]").val();
                var field = $("[name=field]").val();
                var keyword = $("[name=keyword]").val();
                var begin_time = $("[name=begin_time]").val();
                var end_time = $("[name=end_time]").val();
                layui.use('table', function () {
                    var table = layui.table
                        , form = layui.form;
                    table.render({
                        elem: '#demo'
                        , url: "{:url('ajax_get_stock_log_list')}?field=" + field + "&keyword=" + keyword +
                            "&begin_time=" + begin_time + "&end_time=" + end_time //数据接口
                        , page: true //开启分页
                        , cols: [[ //表头
                            { type: 'numbers', title: '序号' }
                            , { field: "time", title: "操作时间" }
                            , { field: "operator", title: "备注" }
                            , { field: "product_name", title: "商品名称" }
                            , { field: 'spec_name', title: '规格名称' }
                            , { field: "old_stock", title: "变更前库存" }
                            , { field: "new_stock", title: "变更后库存" }
                            // , { fixed: 'right', title: "操作", toolbar: '#barDemo' }
                        ]]
                        , done: function (res, curr, count) {

                        }
                    })

                })
            }


            function callback(msg) {
                layer.msg(msg, { time: 1500 }, function (data) {
                    layer.closeAll();
                    get_table();
                })
            }



            //点击放大图片
            function showimg(t) {
                var src = $(t).attr("src");
                if (src == '') {
                    layer.msg("图为为空！");
                    return false;
                }
                layer.open({
                    type: 1,
                    title: false,
                    area: '516px',
                    content: '<img style="display: inline-block; width: 100%; height: 100%;" src="' + src + '">'
                });
            }

            //时间转换
            function createTime(v) {
                if (v == null || v == 0) {
                    return "暂无";
                }
                var v = v * 1000; //js的时间戳要*1000
                var date = new Date(v);
                var y = date.getFullYear();
                var m = date.getMonth() + 1;
                m = m < 10 ? '0' + m : m;
                var d = date.getDate();
                d = d < 10 ? ("0" + d) : d;
                var h = date.getHours();
                h = h < 10 ? ("0" + h) : h;
                var M = date.getMinutes();
                M = M < 10 ? ("0" + M) : M;
                var S = date.getSeconds();
                S = S < 10 ? ("0" + S) : S;
                var str = y + "-" + m + "-" + d + " " + h + ":" + M + ":" + S;
                return str;
            }
            layui.use('laydate', function () {
                var laydate = layui.laydate;

                //执行一个laydate实例
                laydate.render({
                    elem: '#begin_time' //指定元素
                });
                laydate.render({
                    elem: '#end_time' //指定元素
                });

            });
        </script>
</body>

</html>
