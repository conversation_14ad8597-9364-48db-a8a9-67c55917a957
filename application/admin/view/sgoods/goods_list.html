<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}


<link rel="stylesheet" type="text/css" href="__STATIC__/cnadmin/jquery-easyui-1.5.2/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="__STATIC__/cnadmin/jquery-easyui-1.5.2/themes/icon.css">
<script type="text/javascript" src="__STATIC__/cnadmin/jquery-easyui-1.5.2/jquery.easyui.min.js"></script>

<body>
    <br />
    <div id="main">
        <div style="float:left;width:15%;">
            <div class="easyui-panel" style="padding:5px;border:0px solid #fff;">
                <ul id="tree1" class="easyui-tree">
                </ul>
            </div>
        </div>

        <div style="float:right;width:85%;">
            <input type='hidden' name='keynum'>
            <input type='hidden' name='id'>
            <div class="demoTable layui-form">


                <div class="layui-inline">
                    <label class="layui-form-label ">查询条件：</label>
                    <div class="layui-input-block">
                        <select name="field">
                            <option value='title'>商品名称</option>
                        </select>
                    </div>

                </div>


                <div class='layui-inline'>
                    <input class="layui-input" name="keyword" id="demoReload" autocomplete="off">
                </div>

                <button class="layui-btn  layui-btn-sm" onclick="get_table()">搜索</button>
            </div>

            <br />

            <script type="text/html" id="test-table-toolbar-toolbarDemo">
                <div class="layui-btn-container">

                  {if condition="$type eq '1'"}
                  <button class="layui-btn layui-btn-sm" lay-event="audit_up">审核上架</button>
                  <button class="layui-btn layui-btn-sm" lay-event="audit_down">审核下架</button>
                  {elseif condition="$type eq '2'"/}
                  <button class="layui-btn layui-btn-sm" lay-event="xiajia">标记下架</button>
                  {elseif condition="$type eq '3'"/}
                  <button class="layui-btn layui-btn-sm" lay-event="shangjia">标记上架</button>
                  {/if}
                </div>
            </script>

            <table id="demo" lay-filter="demo">
            </table>
            <script type='text/html' id="barDemo">
                {if condition="$type eq '2'"}
                <!-- <a class='layui-btn layui-btn-sm' lay-event="stock">变更库存</a> -->
                {/if}
                <a class='layui-btn layui-btn-sm' lay-event="detail">详情</a>
            </script>
        </div>

        <script>
            function get_table() {
                var url;
                var type = "{$type}";
                if (type == 1) {
                    url = "{:url('ajax_get_noaudit_list')}";
                } else if (type == 2) {
                    url = "{:url('ajax_get_up_goods_list')}";
                } else if (type == 3) {
                    url = "{:url('ajax_get_down_goods_list')}";
                }
                var id = $("[name=id]").val();
                var field = $("[name=field]").val();
                var keyword = $("[name=keyword]").val();
                layui.use('table', function () {
                    var table = layui.table;
                    table.render({
                        elem: '#demo'
                        , toolbar: '#test-table-toolbar-toolbarDemo'
                        , defaultToolbar: []
                        , url: url + "?id=" + id + "&field=" + field + "&keyword=" + keyword
                        , page: true //开启分页
                        , cols: [[ //表头
                            { type: 'checkbox' }
                            , { type: 'numbers', title: '序号' }
                            , { field: 'title', title: '商品名称' }
                            // , { field: 'market_price', title: '市场价' }
                            , { field: 'price', title: '售价' }
                            , {
                                field: 'cover', title: '图片', width: 150, templet: function (item) {
                                    return '<img onclick="showimg(this);"   style=" width: 50%;height:100%" src="' + item.cover + '">';
                                }
                            }
                            , { fixed: 'right', title: "操作", toolbar: '#barDemo', fixed: 'right' }
                        ]],
                        done: function (res, curr, count) {
                            setColor('.layui-table');
                        }
                    })

                })
            }

            layui.use("table", function () {
                var table = layui.table;
                table.on('checkbox(demo)', function (obj) {
                    var checkStatus = table.checkStatus('demo');

                })
                //头工具栏事件
                table.on('toolbar(demo)', function (obj) {
                    var checkStatus = table.checkStatus(obj.config.id);
                    switch (obj.event) {
                        case 'audit_up':
                            var alldata1 = checkStatus.data;
                            var alldata = JSON.stringify(alldata1);
                            var index = layer.load();
                            $.post("{:url('ajax_audit_up')}", { alldata: alldata }, function (data) {
                                layer.closeAll('loading');
                                if (data.sta != 1) {
                                    layer.msg(data.msg);
                                } else {
                                    //关闭当前窗口
                                    layer.msg(data.msg);
                                    setTimeout(function () {
                                        get_table();
                                    }, 1500);
                                }
                            }, "json")
                            break;
                        case 'audit_down':
                            var alldata1 = checkStatus.data;
                            var alldata = JSON.stringify(alldata1);
                            var index = layer.load();
                            $.post("{:url('ajax_audit_down')}", { alldata: alldata }, function (data) {
                                layer.closeAll('loading');
                                if (data.sta != 1) {
                                    layer.msg(data.msg);
                                } else {
                                    //关闭当前窗口
                                    layer.msg(data.msg);
                                    setTimeout(function () {
                                        get_table();
                                    }, 1500);
                                }
                            }, "json")
                            break;
                        case 'xiajia':
                            var alldata1 = checkStatus.data;
                            var alldata = JSON.stringify(alldata1);
                            var index = layer.load();
                            $.post("{:url('ajax_xiajia_goods')}", { alldata: alldata }, function (data) {
                                layer.closeAll('loading');
                                if (data.sta != 1) {
                                    layer.msg(data.msg);
                                } else {
                                    //关闭当前窗口
                                    layer.msg(data.msg);
                                    setTimeout(function () {
                                        get_table();
                                    }, 1500);
                                }
                            }, "json")
                            break;
                        case 'shangjia':
                            var alldata1 = checkStatus.data;
                            var alldata = JSON.stringify(alldata1);
                            var index = layer.load();
                            $.post("{:url('ajax_shangjia_goods')}", { alldata: alldata }, function (data) {
                                layer.closeAll('loading');
                                if (data.sta != 1) {
                                    layer.msg(data.msg);
                                } else {
                                    //关闭当前窗口
                                    layer.msg(data.msg);
                                    setTimeout(function () {
                                        get_table();
                                    }, 1500);
                                }
                            }, "json")
                            break;
                    };
                });
            })

            layui.use("table", function () {
                var table = layui.table;
                var form = layui.form;
                table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                    var data = obj.data //获得当前行数据
                        , layEvent = obj.event; //获得 lay-event 对应的值
                    var id = data.id;
                    console.log(data);
                    if (layEvent === 'stock') {
                        layer.open({
                            type: 2,
                            title: "变更库存",
                            content: "{:url('save_stock')}?id=" + id,
                            maxmin: true,
                            area: ["98%", "98%"],
                            end: function () {
                                // get_table();
                            }
                        })
                    } else if (layEvent === 'detail') {
                        layer.open({
                            type: 2,
                            title: "详情",
                            content: "{:url('detail')}?id=" + id,
                            maxmin: true,
                            area: ["98%", "98%"],
                            end: function () {
                                // get_table();
                            }
                        })
                    }
                });
            })
            //点击放大图片
            function showimg(t) {
                var src = $(t).attr("src");
                if (src == '') {
                    layer.msg("图为为空！");
                    return false;
                }
                layer.open({
                    type: 1,
                    title: false,
                    area: '516px',
                    content: '<img style="display: inline-block; width: 100%; height: 100%;" src="' + src + '">'
                });
            }
            //时间转换
            function createTime(v) {
                var v = v * 1000; //js的时间戳要*1000
                var date = new Date(v);
                var y = date.getFullYear();
                var m = date.getMonth() + 1;
                m = m < 10 ? '0' + m : m;
                var d = date.getDate();
                d = d < 10 ? ("0" + d) : d;
                var h = date.getHours();
                h = h < 10 ? ("0" + h) : h;
                var M = date.getMinutes();
                M = M < 10 ? ("0" + M) : M;
                var S = date.getSeconds();
                S = S < 10 ? ("0" + S) : S;
                var str = y + "-" + m + "-" + d + " " + h + ":" + M + ":" + S;
                return str;
            }
            function setColor(tableClassName) {
                var $table = $(tableClassName).eq(1);
                //console.log($table)
                if ($table.length > 0) {
                    //遍历所有行
                    $table.find('tr').each(function () {
                        var open = $(this).find('td[data-field="is_error"]').find('div').text()
                        if (open == "是") {   //给状态为2的数据行设置背景色
                            $(this).attr('style', "background:#de806d;color:#000");
                        }
                    })
                }
            }
            function callback(msg) {
                layer.msg(msg, { time: 1500 }, function (data) {
                    layer.closeAll();
                    // get_table();
                })
            }
        </script>

        <script>
            $(function () {
                var lastNode;
                //初始化树形
                var tt = $('#tree1');
                $('#tree1').tree({
                    url: "{:url('get_area_treedata')}",
                    //绑定click事件
                    onClick: function (node) {
                        $("[name=id]").val(node.id);
                        $("[name=text]").val(node.df_order);
                        $("[name=keynum]").val(node.id);
                        $("[name=allpath]").val(node.allpath);
                        get_table();
                    }
                    , lines: true,
                    loadFilter: function (rows) {
                        return convert(rows);
                    },
                    onLoadSuccess: function (node, data) {
                        if (typeof data[0] != "undefined") {
                            if (tt.tree("getSelected") == null) {
                                var rNode = tt.tree("getRoot");
                                $("[name=root_keynum]").val(data[0].keynum);
                                if (lastNode) {
                                } else {
                                    get_table();
                                    lastNode = rNode;

                                }

                            }
                        }
                        ;
                    }
                });

            });


            function convert(rows) {
                function exists(rows, parentId) {
                    for (var i = 0; i < rows.length; i++) {
                        if (rows[i].id == parentId) return true;
                    }
                    return false;
                }

                var nodes = [];
                // get the top level nodes
                for (var i = 0; i < rows.length; i++) {
                    var row = rows[i];
                    if (!exists(rows, row.parentId)) {
                        nodes.push({
                            id: row.id,
                            text: row.name,
                            keynum: row.keynum,
                            level: row.level,
                            allpath: row.allpath,
                        });
                    }
                }

                var toDo = [];
                for (var i = 0; i < nodes.length; i++) {
                    toDo.push(nodes[i]);
                }
                while (toDo.length) {
                    var node = toDo.shift();    // the parent node
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        if (row.parentId == node.id) {
                            var child = {
                                id: row.id,
                                text: row.name,
                                keynum: row.keynum,
                                level: row.level,
                                allpath: row.allpath
                            };
                            if (node.children) {
                                node.children.push(child);
                            } else {
                                node.children = [child];
                            }
                            toDo.push(child);
                        }
                    }
                }
                return nodes;
            }



        </script>
</body>

</html>
