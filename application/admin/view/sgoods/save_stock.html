<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>审核商品</title>
  {include file="public/iframeheader"/}
  <!-- 编辑器源码文件 -->
  <script type="text/javascript" src="__STATIC__/cnadmin/ueditor/ueditor.config.js"></script>
  <script type="text/javascript" src="__STATIC__/cnadmin/ueditor/ueditor.all.js"></script>
</head>

<body>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body" pad15>



            <div class="layui-form" wid100 lay-filter="">
              <input type="hidden" name="id" value="{$goods_info['product_id']|default=''}">

              <div class="layui-form-item">
                <label class="layui-form-label">商品名称</label>
                <div class="layui-input-inline">
                  <input type="text" name="title" style="width: 400px;" value="{$goods_info['title']|default=''}"
                    class="layui-input layui-disabled" disabled>
                </div>
              </div>

<!--              <div class="layui-form-item">-->
<!--                <label class="layui-form-label">市场价</label>-->
<!--                <div class="layui-input-inline">-->
<!--                  <input type="text" name="market_price" style="width: 400px;" value="{$goods_info['market_price']|default=''}"-->
<!--                    class="layui-input layui-disabled" disabled>-->
<!--                </div>-->
<!--                <div class="layui-input-inline layui-input-company"></div>-->
<!--                <div class="layui-form-mid layui-word-aux"></div>-->
<!--              </div>-->

              <div class="layui-form-item">
                <label class="layui-form-label">售价</label>
                <div class="layui-input-inline">
                  <input type="text" name="price" style="width: 400px;" value="{$goods_info['price']|default=''}"
                    class="layui-input layui-disabled" disabled>
                </div>
                <div class="layui-input-inline layui-input-company"></div>
                <div class="layui-form-mid layui-word-aux"></div>
              </div>



              <div class="layui-form-item">
                <label class="layui-form-label">商品规格</label>
                <div class="layui-input-inline" style="width: 600px;">
                  <table class="layui-table">
                    <colgroup>
                      <col width="">
                      <col width="">
                      <col width="">
                      <col>
                    </colgroup>
                    <thead>
                      <tr>
                        <th>规格名称</th>
                        <th>规格价格</th>
                        <th>库存</th>
                      </tr>
                    </thead>
                    <tbody>
                      {foreach name="inventory_list" key="key" item="value"}
                      <tr>
                        <td>{$value["title"]}</td>
                        <td>{$value["price"]}</td>
                        <td>
                          <span
                            style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                          <input name="kucun[{$value['id']}]" value="{$value['stock']}" class="layui_input" />
                        </td>
                      </tr>
                      {/foreach}
                    </tbody>
                  </table>

                </div>
                <div class="layui-input-inline layui-input-company"></div>
                <div class="layui-form-mid layui-word-aux"></div>
              </div>


              <div class="layui-form-item">
                <label class="layui-form-label">商品封面图</label>
                <div class="layui-input-inline">
                  <img src="{$goods_info['cover']|default=''}" style="width: 200px;" name="cover" />
                </div>
                <div class="layui-input-inline layui-input-company"></div>
                <div class="layui-form-mid layui-word-aux"></div>
              </div>
<!--              <div class="layui-form-item">-->
<!--                <label class="layui-form-label">商品banner图</label>-->
<!--                <div class="layui-input-inline">-->
<!--                  {foreach name="goods_info['banner']" key="key" item="value"}-->
<!--                  <img src="{$value}" style="width: 200px;margin: 5px;" name="images" />-->
<!--                  {/foreach}-->
<!--                </div>-->
<!--                <div class="layui-input-inline layui-input-company"></div>-->
<!--                <div class="layui-form-mid layui-word-aux"></div>-->
<!--              </div>-->
<!--              <div class="layui-form-item">-->
<!--                <label class="layui-form-label">商品详情图</label>-->
<!--                <div class="layui-input-inline">-->
<!--                  {foreach name="goods_detail['content']" key="key" item="value"}-->
<!--                  <img src="{$value}" style="" name="content_images" />-->
<!--                  <br />-->
<!--                  {/foreach}-->
<!--                </div>-->
<!--                <div class="layui-input-inline layui-input-company"></div>-->
<!--                <div class="layui-form-mid layui-word-aux"></div>-->
<!--              </div>-->


              <div class="layui-form-item">
                <div class="layui-input-block">
                  <button class="layui-btn" lay-submit lay-filter="sub2">确认修改</button>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    //百度编辑器服务器统一请求接口路径 ，路由定义在\vendor\dh2y\think-ueditor\src\helper.php
    window.UEDITOR_CONFIG.serverUrl = "{:url('ueditor/index')}";
    //渲染某个元素为百度富文本编辑器
    cn_ueditor1 = UE.getEditor('container1');
  </script>

  <script>

    function callback(msg) {
      layer.msg(msg, { time: 1500 }, function (data) {
        layer.closeAll();
        window.parent.location.reload();
      })
    }
    //一般直接写在一个js文件中
    layui.use(['element', 'form', 'layedit'], function () {
      var form = layui.form;
      //监听提交1
      form.on('submit(sub2)', function (data) {
        load = layer.load(2, { shade: [0.1, '#fff'] });
        $.post("{:url('ajax_save_stock')}", data.field, function (data) {
          layer.close(load);
          if (data.sta == 1) {
            layer.msg(data.msg);
            setInterval(function () {
              window.parent.layer.closeAll();//关闭弹窗
            }, 1500);
          } else {
            layer.msg(data.msg);
          }
        }, "json");

        return false;
      });
    });
  </script>

</body>

</html>
