<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>新建</title>
    {include file="public/iframeheader"/}
    <!-- 编辑器源码文件 -->
    <script type="text/javascript" src="__STATIC__/cnadmin/ueditor/ueditor.config.js"></script>
    <script type="text/javascript" src="__STATIC__/cnadmin/ueditor/ueditor.all.js"></script>
</head>

<body>

    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body" pad15>

                        <div class="layui-form" wid100 lay-filter="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">配送范围</label>
                                <span
                                    style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                                <div class="layui-input-inline">
                                    <input type="number" name="delivery_km" style="width: 400px;"
                                        value="{$info['delivery_km']|default=''}" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">单位：公里</div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">配送费</label>
                                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                                <div class="layui-input-inline">
                                    <input type="number" name="delivery_price" style="width: 400px;" value="{$info['delivery_price']|default=''}"
                                        class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">配送费</div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">满多少包邮</label>
                                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                                <div class="layui-input-inline">
                                    <input type="number" name="baoyou_price" style="width: 400px;" value="{$info['baoyou_price']|default=''}"
                                        class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">满足包邮金额之后，包邮</div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="sub">确认保存</button>

                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>

        function callback(msg) {
            layer.msg(msg, { time: 1500 }, function (data) {
                layer.closeAll();
                window.parent.location.reload();
            })
        }
        //一般直接写在一个js文件中
        layui.use(['element', 'form', 'layedit'], function () {
            var form = layui.form;
            //监听提交1
            form.on('submit(sub)', function (data) {
                load = layer.load(2, { shade: [0.1, '#fff'] });
                $.post("{:url('ajax_add_webconfig')}", data.field, function (data) {
                    layer.close(load);
                    if (data.sta == 1) {
                        layer.msg(data.msg);
                        setInterval(function () {
                            window.location.reload();
                        }, 1500);
                    } else {
                        layer.msg(data.msg);
                    }
                }, "json");

                return false;
            });
        });
    </script>
    
</body>

</html>