<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>店铺配送能力配置</title>
    {include file="public/iframeheader"/}
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">店铺配送能力配置</div>
                    <div class="layui-card-body">
                        <form class="layui-form" lay-filter="shop-select-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label">选择店铺</label>
                                <div class="layui-input-inline" style="width: 300px;">
                                    <select name="shop_id" lay-filter="shop-select" lay-verify="required">
                                        <option value="">请选择店铺</option>
                                        {volist name="shops" id="shop"}
                                        <option value="{$shop.id}" {if condition="$shopId eq $shop.id"}selected{/if}>{$shop.title}</option>
                                        {/volist}
                                    </select>
                                </div>
                                <div class="layui-input-inline">
                                    <button type="button" class="layui-btn layui-btn-normal" id="add-capacity">添加配送时间段</button>
                                </div>
                            </div>
                        </form>

                        <table class="layui-table" id="capacity-table" lay-filter="capacity-table"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 表格操作按钮模板 -->
    <script type="text/html" id="table-operation">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </script>

    <!-- 状态开关模板 -->
    <script type="text/html" id="status-switch">
        <input type="checkbox" name="status" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="status-switch" {{ d.status == 1 ? 'checked' : '' }}>
    </script>

    <!-- 添加/编辑表单模板 -->
    <script type="text/html" id="form-tpl">
        <form class="layui-form" lay-filter="capacity-form" style="padding: 20px;">
            <input type="hidden" name="id" value="{{ d.id || '' }}">
            <input type="hidden" name="shop_id" value="{{ d.shop_id || d.currentShopId }}">
            
            <div class="layui-form-item">
                <label class="layui-form-label">配送时间段</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                <div class="layui-input-block">
                    <input type="text" name="delivery_time_range" value="{{ d.delivery_time_range || '' }}" lay-verify="required" placeholder="格式如：9:00-10:00" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">最大配送量</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                <div class="layui-input-block">
                    <input type="number" name="max_capacity" value="{{ d.max_capacity || '0' }}" lay-verify="required|number" min="1" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" {{ d.status === undefined || d.status == 1 ? 'checked' : '' }}>
                    <input type="radio" name="status" value="0" title="禁用" {{ d.status !== undefined && d.status == 0 ? 'checked' : '' }}>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="capacity-submit">确认保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </script>

    <script>
        layui.use(['table', 'form', 'layer'], function() {
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.jquery;
            
            // 当前选中的店铺ID
            var currentShopId = '{$shopId}';
            
            // 初始化表格
            var capacityTable = table.render({
                elem: '#capacity-table',
                url: '{:url("getDeliveryCapacityList")}?shop_id=' + currentShopId,
                page: true,
                cols: [[
                    {type: 'checkbox', fixed: 'left'},
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'delivery_time_range', title: '配送时间段', width: 180},
                    {field: 'max_capacity', title: '最大配送量', width: 120},
                    {field: 'status', title: '状态', width: 100, templet: '#status-switch'},
                    {field: 'add_time', title: '创建时间', width: 180},
                    {field: 'update_time', title: '更新时间', width: 180},
                    {fixed: 'right', title: '操作', toolbar: '#table-operation', width: 150}
                ]],
                parseData: function(res) {
                    return {
                        "code": res.sta === 1 ? 0 : 1,
                        "msg": res.msg,
                        "count": res.data ? res.data.list.length : 0,
                        "data": res.data ? res.data.list : []
                    };
                },
                response: {
                    statusCode: 0
                }
            });
            
            // 监听店铺选择
            form.on('select(shop-select)', function(data) {
                currentShopId = data.value;
                if (currentShopId) {
                    // 重新加载表格数据
                    capacityTable.reload({
                        url: '{:url("getDeliveryCapacityList")}?shop_id=' + currentShopId
                    });
                }
            });
            
            // 监听表格工具条
            table.on('tool(capacity-table)', function(obj) {
                var data = obj.data;
                if (obj.event === 'del') {
                    layer.confirm('确定删除该配送时间段？', function(index) {
                        $.ajax({
                            url: '{:url("deleteDeliveryCapacity")}',
                            type: 'POST',
                            data: {id: data.id},
                            success: function(res) {
                                if (res.sta === 1) {
                                    layer.msg(res.msg, {icon: 1});
                                    obj.del();
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                        layer.close(index);
                    });
                } else if (obj.event === 'edit') {
                    // 编辑
                    var editData = obj.data;
                    editData.currentShopId = currentShopId;
                    openEditForm(editData);
                }
            });
            
            // 监听开关操作
            form.on('switch(status-switch)', function(obj) {
                var id = this.value;
                var status = obj.elem.checked ? 1 : 0;
                
                $.ajax({
                    url: '{:url("batchUpdateStatus")}',
                    type: 'POST',
                    data: {ids: [id], status: status},
                    success: function(res) {
                        if (res.sta !== 1) {
                            layer.msg(res.msg, {icon: 2});
                            // 还原开关状态
                            $(obj.elem).prop('checked', !obj.elem.checked);
                            form.render('checkbox');
                        }
                    }
                });
            });
            
            // 添加配送时间段
            $('#add-capacity').click(function() {
                if (!currentShopId) {
                    layer.msg('请先选择店铺', {icon: 2});
                    return;
                }
                
                openEditForm({
                    shop_id: currentShopId,
                    currentShopId: currentShopId
                });
            });
            
            // 打开添加/编辑表单
            function openEditForm(data) {
                var title = data.id ? '编辑配送时间段' : '添加配送时间段';
                
                // 渲染模板
                var html = layui.laytpl(document.getElementById('form-tpl').innerHTML).render({
                    d: data,
                    currentShopId: currentShopId
                });
                
                layer.open({
                    type: 1,
                    title: title,
                    content: html,
                    area: ['500px', '400px'],
                    success: function() {
                        form.render();
                        
                        // 数据回显
                        if(data.id) {
                            form.val('capacity-form', {
                                "id": data.id,
                                "shop_id": data.shop_id,
                                "delivery_time_range": data.delivery_time_range,
                                "max_capacity": data.max_capacity,
                                "status": data.status.toString()
                            });
                        }
                        
                        // 监听表单提交
                        form.on('submit(capacity-submit)', function(formData) {
                            var loading = layer.load(2);
                            
                            $.ajax({
                                url: '{:url("saveDeliveryCapacity")}',
                                type: 'POST',
                                data: formData.field,
                                success: function(res) {
                                    layer.close(loading);
                                    
                                    if (res.sta === 1) {
                                        layer.msg(res.msg, {icon: 1});
                                        layer.closeAll('page');
                                        
                                        // 重新加载表格
                                        capacityTable.reload();
                                    } else {
                                        layer.msg(res.msg, {icon: 2});
                                    }
                                }
                            });
                            
                            return false;
                        });
                    }
                });
            }
            
            // 工具栏事件
            table.on('toolbar(capacity-table)', function(obj){
                var checkStatus = table.checkStatus(obj.config.id);
                
                switch(obj.event){
                    case 'batchEnable':
                        updateBatchStatus(checkStatus.data, 1);
                    break;
                    case 'batchDisable':
                        updateBatchStatus(checkStatus.data, 0);
                    break;
                };
            });
            
            // 批量更新状态
            function updateBatchStatus(data, status) {
                if (data.length === 0) {
                    layer.msg('请选择要操作的数据', {icon: 2});
                    return;
                }
                
                var ids = [];
                for (var i = 0; i < data.length; i++) {
                    ids.push(data[i].id);
                }
                
                var statusText = status === 1 ? '启用' : '禁用';
                
                layer.confirm('确定要' + statusText + '选中的' + data.length + '条数据吗？', function(index) {
                    $.ajax({
                        url: '{:url("batchUpdateStatus")}',
                        type: 'POST',
                        data: {ids: ids, status: status},
                        success: function(res) {
                            if (res.sta === 1) {
                                layer.msg(res.msg, {icon: 1});
                                capacityTable.reload();
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }
                    });
                    
                    layer.close(index);
                });
            }
        });
    </script>
</body>
</html> 