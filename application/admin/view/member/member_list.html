<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}
<link rel="stylesheet" type="text/css" href="__STATIC__/admin/jquery-easyui-1.5.2/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="__STATIC__/admin/jquery-easyui-1.5.2/themes/icon.css">
<style type="text/css">
    .layui-table-cell{
        display:table-cell;
        vertical-align: middle;
    }
</style>
<script type="text/javascript" src="__STATIC__/admin/jquery-easyui-1.5.2/jquery.easyui.min.js"></script>

<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">会员管理</div>
        <div class="layui-card-body">
            <!-- 统计卡片 -->
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md4">
                    <div class="layui-card">
                        <div class="layui-card-header" style="text-align: center;">在线支付总额</div>
                        <div class="layui-card-body">
                            <p class="layui-text-center" style="font-size: 24px; text-align: center;" id="online_payment_total">¥0.00</p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="layui-card">
                        <div class="layui-card-header" style="text-align: center;">储值卡支付总额</div>
                        <div class="layui-card-body">
                            <p class="layui-text-center" style="font-size: 24px; text-align: center;" id="card_payment_total">¥0.00</p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="layui-card">
                        <div class="layui-card-header" style="text-align: center;">用户总支付金额</div>
                        <div class="layui-card-body">
                            <p class="layui-text-center" style="font-size: 24px; text-align: center;" id="total_payment">¥0.00</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 搜索表单 -->
            <div class="layui-form layui-form-pane" style="margin-top: 15px;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">查询条件</label>
                        <div class="layui-input-inline">
                            <select name="field">
                                <option value='name'>会员昵称</option>
                                <option value='phone'>手机号</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <input class="layui-input" name="keyword" id="demoReload" autocomplete="off">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button class="layui-btn" onclick="get_table()" id="reload">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="layui-row layui-col-space15" style="margin-top: 15px;">
                <table id="demo" lay-filter="demo"></table>
            </div>
            
            <script type='text/html' id="barDemo">
                <!-- <a class='layui-btn layui-btn-sm' lay-event="set_user_level">修改会员等级</a> -->
                <a class='layui-btn layui-btn-sm' lay-event="show_dingdan">查看订单</a>
                {if $plat_client_info['is_open_card'] eq 1}
                <a class='layui-btn layui-btn-sm' lay-event="show_card_list">查看卡号</a>
                {/if}
            </script>
        </div>
    </div>
</div>

<script>
    // 页面加载时获取统计数据
    $(function() {
        getPaymentStats();
    });
    
    // 获取支付统计数据
    function getPaymentStats() {
        $.ajax({
            url: "{:url('ajax_get_member_payment_stats')}",
            type: "GET",
            dataType: "json",
            success: function(res) {
                if(res.code == 0) {
                    $("#online_payment_total").text("¥" + (res.data.online_payment_total || "0.00"));
                    $("#card_payment_total").text("¥" + (res.data.card_payment_total || "0.00"));
                    $("#total_payment").text("¥" + (res.data.total_payment || "0.00"));
                } else {
                    layer.msg(res.msg || "获取统计数据失败");
                }
            },
            error: function() {
                layer.msg("网络错误，获取统计数据失败");
            }
        });
    }
    
    get_table();

    function get_table() {
        var keynum = $("[name=keynum]").val();
        var field = $("[name=field]").val();
        var keyword = $("[name=keyword]").val();
        var has_order = $("[name=has_order]").val();
        layui.use('table', function () {
            var table = layui.table
                , form = layui.form;
            table.render({
                elem: '#demo'
                ,
                url: "{:url('ajax_get_member_list')}?field=" + field + "&keyword=" + keyword + "&has_order=" + has_order//数据接口
                ,
                page: true //开启分页
                ,
                cols: [[ //表头
                    {type: 'numbers', title: '序号'}
                    , {field: 'name', title: '会员名称'}
                    , {field: 'phone', title: '手机号'}
                    // , {field: 'level_title', title: '会员等级'}
                    , {field: 'payed_money', title: '会员在线支付金额', sort: true}
                    , {field: 'card_money', title: '会员储值卡支付金额', sort: true}
                    , {field: 'add_time', title: '注册时间'}


                    // , {field: 'balance', title: '余额'}
                    // , {field: 'add_time', title: '创建时间'}
                    , {fixed: 'right', title: "操作", toolbar: '#barDemo', width: 200}
                ]],
                done: function() {
                    // 表格加载完成后更新统计数据
                    getPaymentStats();
                }
            })
            
            // 监听排序事件
            table.on('sort(demo)', function(obj){
                table.reload('demo', {
                    initSort: obj,
                    where: {
                        field: field,
                        keyword: keyword,
                        has_order: has_order,
                        sort_field: obj.field, // 排序字段
                        sort_order: obj.type // 排序方式
                    }
                });
            });
        })
    }

    layui.use("table", function () {
        var table = layui.table;
        var form = layui.form;
        table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data // 获得当前行数据
                , layEvent = obj.event; // 获得 lay-event 对应的值
            var id = data.id;
            if (layEvent === 'show_dingdan') {
                layer.open({
                    type: 2,
                    title: "用户订单信息" ,
                    content: "{:url('Order/order_list')}?memberid=" + id,
                    maxmin: true,
                    area: ["100%", "100%"]
                })
            } else if (layEvent === 'show_card_list') {
                layer.open({
                    type: 2,
                    title: "用户储值卡信息",
                    content: "{:url('Ycard/bind_card_list')}?memberid=" + id,
                    maxmin: true,
                    area: ["100%", "100%"]
                });
            } else if (layEvent === 'set_user_level') {
                // layer.open({
                //     type: 2,
                //     title: "修改用户等级",
                //     content: "{:url('set_user_level_view')}?user_id=" + id,
                //     maxmin: true,
                //     area: ["80%", "80%"]
                // });
            }

        });

    })

    function callback(msg) {
        layer.msg(msg, {time: 1500}, function (data) {
            layer.closeAll();
            get_table();
        })
    }


    //点击放大图片
    function showimg(t) {
        var src = $(t).attr("src");
        if (src == '') {
            layer.msg("图为为空！");
            return false;
        }
        layer.open({
            type: 1,
            title: false,
            area: '516px',
            content: '<img style="display: inline-block; width: 100%; height: 100%;" src="' + src + '">'
        });
    }


    //时间转换
    function createTime(v) {
        if (v == null || v == 0) {
            return "暂无";
        }
        var v = v * 1000; //js的时间戳要*1000
        var date = new Date(v);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? '0' + m : m;
        var d = date.getDate();
        d = d < 10 ? ("0" + d) : d;
        var h = date.getHours();
        h = h < 10 ? ("0" + h) : h;
        var M = date.getMinutes();
        M = M < 10 ? ("0" + M) : M;
        var S = date.getSeconds();
        S = S < 10 ? ("0" + S) : S;
        var str = y + "-" + m + "-" + d + " " + h + ":" + M + ":" + S;
        return str;
    }
</script>

</body>

</html>
