<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>新建</title>
    {include file="public/iframeheader"/}
    <!-- 编辑器源码文件 -->
</head>

<body>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body" pad15>
                    <div class="layui-form" wid100 lay-filter="">

                        <input type="hidden" name="id" value="{$info['id']}"/>
                        <div class="layui-form-item">
                            <label class="layui-form-label">等级名称</label>
                            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                            <div class="layui-input-inline" style="width: 500px;">
                                <input type="text" name="title" style="width: 500px;"
                                       value="{$info['title']|default=''}" class="layui-input">
                            </div>
                        </div>


                        <div class="layui-form-item">
                            <label class="layui-form-label">折扣</label>
                            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                            <div class="layui-input-inline" style="width: 500px;">
                                <input type="number" name="discount" style="width: 500px;" value="{$info['discount']|default=''}"
                                       class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">排序</label>
                            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                            <div class="layui-input-inline" style="width: 500px;">
                                <input type="number" name="sort" style="width: 500px;" value="{$info['sort']|default=''}"
                                       class="layui-input">
                            </div>
                        </div>


                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="sub">确认保存</button>

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>

    function callback(msg) {
        layer.msg(msg, {time: 1500}, function (data) {
            layer.closeAll();
            window.parent.location.reload();
        })
    }

    //一般直接写在一个js文件中
    layui.use(['element', 'form', 'layedit'], function () {
        var form = layui.form;
        //监听提交1
        form.on('submit(sub)', function (data) {
            load = layer.load(2, {shade: [0.1, '#fff']});
            $.post("{:url('ajax_add_user_level')}", data.field, function (data) {
                layer.close(load);
                if (data.code == 0) {
                    layer.msg(data.msg);
                    setInterval(function () {
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    }, 1500);
                } else {
                    layer.msg(data.msg);
                }
            }, "json");

            return false;
        });
    });
</script>

</body>

</html>
