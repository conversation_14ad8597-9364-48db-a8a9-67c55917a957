<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}
<link rel="stylesheet" href="__STATIC__/admin//layui/css/layui.css">
<script type="text/javascript" src="__STATIC__/admin/layui/layui.js"></script>
<div style="margin-top:25px;">
    <input type="hidden" name="select-classify" id="select-classify">

    <div style="width: 95%;margin:auto">

        <div style="margin-top: 10px;margin-left: 25px;">
            <button class="layui-btn layuiadmin-btn-useradmin   layui-btn-sm" link="add_user_level">添加等级</button>
        </div>

        <table id="test" lay-filter="test"></table>
        <div id="page"></div>
        <script type='text/html' id="barDemo">
            <a class='layui-btn layui-btn-sm' lay-event="edit">修改</a>
            <a class='layui-btn layui-btn-sm' lay-event="del">删除</a>
        </script>
    </div>
</div>

<script type="text/javascript">
    layui.use(['table', 'jquery'], function () {
        var layer = layui.layer, $ = layui.jquery, table = layui.table;


        table.render({
            elem: '#test'
            , url: "{:url('ajax_user_level_list')}"
            , page: true //开启分页
            , cols: [[ //表头
                {field: 'id', title: 'id'},
                {field: 'title', title: '名称'},
                {field: 'discount', title: '折扣'},
                {field: 'sort', title: '排序'},
                { fixed: 'right', title: "操作", toolbar: '#barDemo', width: 260}
            ]],
        });

        table.on('tool(test)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            var id = data.id;
            if (layEvent === 'del') {
                layer.confirm('确定要删除该等级吗？', function (index) {
                    var index = layer.load();
                    $.post("{:url('del_user_level')}", { ids: data.id }, function (data) {
                        layer.msg(data.msg);
                        layer.closeAll('loading');
                        if (data.code == 0) {

                        }
                    }, "json")
                });
            } else if (layEvent === 'edit') {
                layer.open({
                    type: 2,
                    title: "修改等级",
                    content: "{:url('add_user_level')}?id=" + id,
                    maxmin: true,
                    area: ["98%", "98%"],
                    end: function () {

                    }
                })
            }
        });


    });

    //添加分类
    $("[link=add_user_level]").click(function () {
        layer.open({
            type: 2,
            title: "添加商品分类",
            content: "{:url('add_user_level')}",
            maxmin: true,
            area: ["98%", "98%"],
            end: function () {
                table.reload('test', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    },
                });
            }
        })
    });
</script>


</html>
