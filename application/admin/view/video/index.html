{include file="common/header" /}

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">视频管理</div>
        <div class="layui-card-body">
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">视频名称：</label>
                        <div class="layui-input-inline">
                            <input id="videoName" name="videoName" class="layui-input" placeholder="请输入视频名称"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" lay-filter="formSubSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button id="btnAdd" class="layui-btn icon-btn">
                            <i class="layui-icon">&#xe654;</i>上传视频
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 视频存储信息展示 -->
            <div class="layui-card" style="margin-bottom: 15px;">
                <div class="layui-card-header">视频存储信息</div>
                <div class="layui-card-body">
                    <div class="layui-row">
                        <div class="layui-col-md6">
                            <p>存储剩余：<span id="videoStorage">加载中...</span></p>
                        </div>
                        <div class="layui-col-md6">
                            <p>流量剩余：<span id="videoFlow">加载中...</span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 表格 -->
            <table class="layui-table" id="videoTable" lay-filter="videoTable"></table>
        </div>
    </div>
</div>

<!-- 操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">修改名称</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 视频播放模板 -->
<script type="text/html" id="videoTpl">
    <div style="width: 100px; text-align: center;">
        <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="play">播放</a>
    </div>
</script>

<!-- 视频播放层 -->
<script type="text/html" id="videoPlayer">
    <div style="padding: 20px;">
        <video id="player" controls style="width: 100%; max-height: 500px;">
            <source src="{{ d.playUrl }}" type="video/mp4">
            您的浏览器不支持视频播放
        </video>
    </div>
</script>

<!-- 修改名称弹窗 -->
<script type="text/html" id="editNameTpl">
    <form class="layui-form" lay-filter="formEditName" style="padding: 20px;">
        <div class="layui-form-item">
            <label class="layui-form-label">视频名称</label>
            <div class="layui-input-block">
                <input type="text" name="videoName" required lay-verify="required" placeholder="请输入视频名称" 
                       autocomplete="off" class="layui-input" value="{{ d.videoName }}">
                <input type="hidden" name="videoVid" value="{{ d.videoVid }}">
            </div>
        </div>
    </form>
</script>

<script>
    layui.use(['table', 'form', 'layer', 'admin', 'laytpl'], function () {
        var $ = layui.jquery;
        var table = layui.table;
        var form = layui.form;
        var layer = layui.layer;
        var admin = layui.admin;
        var laytpl = layui.laytpl;
        
        // 加载视频存储信息
        loadVideoStorage();
        
        // 渲染表格
        table.render({
            elem: '#videoTable',
            url: '{:url("video/getVideoList")}',
            page: true,
            limit: 10,
            cols: [[
                {type: 'numbers', title: '序号', width: 60},
                {field: 'videoName', title: '视频名称', minWidth: 200},
                {field: 'createTime', title: '创建时间', width: 180},
                {field: 'standardSize', title: '视频大小', width: 120, templet: function(d) {
                    return d.standardSize ? (d.standardSize / 1024 / 1024).toFixed(2) + 'MB' : '未知';
                }},
                {field: 'flowTotal', title: '已用流量', width: 120, templet: function(d) {
                    return d.flowTotal ? (d.flowTotal / 1024 / 1024).toFixed(2) + 'MB' : '0MB';
                }},
                {field: 'playUrl', title: '播放', width: 100, templet: '#videoTpl'},
                {title: '操作', toolbar: '#tableBar', width: 160, align: 'center'}
            ]],
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.count,
                    "data": res.data
                };
            }
        });
        
        // 工具条点击事件
        table.on('tool(videoTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            
            if (layEvent === 'edit') {
                // 修改视频名称
                showEditDialog(data);
            } else if (layEvent === 'del') {
                // 删除视频
                layer.confirm('确定要删除该视频吗？', function(index) {
                    layer.close(index);
                    deleteVideo(data.videoVid);
                });
            } else if (layEvent === 'play') {
                // 播放视频
                laytpl($('#videoPlayer').html()).render(data, function(html) {
                    layer.open({
                        type: 1,
                        title: '视频播放',
                        area: ['800px', '600px'],
                        content: html,
                        success: function() {
                            $('#player').get(0).play();
                        },
                        end: function() {
                            $('#player').get(0).pause();
                        }
                    });
                });
            }
        });
        
        // 表单搜索
        form.on('submit(formSubSearch)', function (data) {
            table.reload('videoTable', {
                where: {
                    videoName: data.field.videoName
                },
                page: {curr: 1}
            });
            return false;
        });
        
        // 添加按钮点击事件
        $('#btnAdd').click(function () {
            // 打开上传页面
            window.open('{$upload_url}', '_blank');
        });
        
        // 加载视频存储信息
        function loadVideoStorage() {
            admin.req('{:url("video/getVideoStorage")}', {}, function(res) {
                if (res.code === 0 && res.data) {
                    $('#videoStorage').html(res.data.videoStorage || '未知');
                    $('#videoFlow').html(res.data.videoFlow || '未知');
                } else {
                    $('#videoStorage').html('获取失败');
                    $('#videoFlow').html('获取失败');
                }
            }, 'get');
        }
        
        // 显示编辑弹窗
        function showEditDialog(data) {
            laytpl($('#editNameTpl').html()).render(data, function(html) {
                var editIndex = layer.open({
                    type: 1,
                    title: '修改视频名称',
                    area: ['400px', '250px'],
                    content: html,
                    btn: ['保存', '取消'],
                    yes: function() {
                        // 提交表单
                        var formData = form.val('formEditName');
                        if (!formData.videoName) {
                            layer.msg('视频名称不能为空', {icon: 2});
                            return;
                        }
                        
                        // 提交修改
                        changeVideoName(formData.videoVid, formData.videoName, editIndex);
                    }
                });
            });
        }
        
        // 修改视频名称
        function changeVideoName(videoVid, videoName, index) {
            admin.req('{:url("video/changeVideoName")}', {
                videoVid: videoVid,
                videoName: videoName
            }, function(res) {
                if (res.code === 0) {
                    layer.close(index);
                    layer.msg('修改成功', {icon: 1});
                    table.reload('videoTable');
                } else {
                    layer.msg(res.msg || '修改失败', {icon: 2});
                }
            }, 'post');
        }
        
        // 删除视频
        function deleteVideo(videoVid) {
            admin.req('{:url("video/deleteVideo")}', {
                videoVid: videoVid
            }, function(res) {
                if (res.code === 0) {
                    layer.msg('删除成功', {icon: 1});
                    table.reload('videoTable');
                    // 刷新存储信息
                    loadVideoStorage();
                } else {
                    layer.msg(res.msg || '删除失败', {icon: 2});
                }
            }, 'post');
        }
    });
</script>

{include file="common/footer" /} 