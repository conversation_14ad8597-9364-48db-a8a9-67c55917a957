<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
<br/>
<div style="margin: 20px;">


    <form class="layui-form layui-row layui-col-space16">
        <div class="layui-inline">
            <label class="layui-form-label ">门店名称：</label>
            <div class="layui-input-block">
                <input class="layui-input" name="title" autocomplete="off">
            </div>
        </div>

        <div class="layui-inline">
            <button class="layui-btn" lay-submit lay-filter="demo-table-search">搜索</button>
        </div>
    </form>

    <div class="page-content">
        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
    </div>


    <script type='text/html' id="barDemo">
        <a class='layui-btn layui-btn-sm' lay-event="reset_password">重置密码</a>
        <a class='layui-btn layui-btn-sm' lay-event="product_list">管理门店商品</a>
        <a class='layui-btn layui-btn-sm layui-bg-blue' lay-event="printer_config">门店打印配置</a>
        <a class='layui-btn layui-btn-sm' lay-event="edit">修改</a>
        <a class='layui-btn layui-btn-sm layui-bg-red' lay-event="del">删除</a>
    </script>

</div>

</body>

</html>
<script type="text/javascript">

    layui.use(['table', 'form'], function () {
        var table = layui.table;
        var form = layui.form;
        //方法级渲染
        table.render({
            elem: '#demo'
            , url: "{:url('ajax_shop_list')}"
            , cols: [[
                { type: 'checkbox' }
                , { type: 'numbers', title: '序号', width: 50 }
                , { field: 'code', title: '门店编码', width: 150, align: 'center' }
                , { field: 'clientnum', title: '登录账号', width: 150, align: 'center' }
                // , { field: 'background_image', title: '背景图', width: 250 }
                , { field: 'title', title: '门店名称', width: 150 }
                , { field: 'phone', title: '联系电话', width: 150 }
                , { field: 'address', title: '门店地址', width: 150 }
                , { field: 'add_time', title: '添加时间', width: 120 }
                , { fixed: 'right', title: "操作", toolbar: '#barDemo', width: 500, align: 'center' }
            ]]
            , page: true
        });

        table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            var id = data.id;
            if (layEvent === 'order-detail'){
                layer.open({
                    type: 2,
                    title: "订单详情",
                    content: "{:url('order_detail')}?order_sn=" + order_sn,
                    maxmin: true,
                    area: ["98%", "98%"],
                    end: function () {
                        get_table();
                    }
                })
            } else if (layEvent === 'edit') {
                layer.open({
                    type: 2,
                    title: "修改商品",
                    content: "{:url('edit_shop')}?id=" + id,
                    maxmin: true,
                    area: ["98%", "98%"],
                    end: function () {

                    }
                })
            } else if (layEvent === 'del') {
                layer.confirm('确认删除【' + data.title + '】吗？', {
                    btn: ['确定', '关闭'] //按钮
                }, function () {
                    $.post(
                        "{:url('del_shop')}",
                        {id: id},
                        function (res) {
                            if (res.code == 0) {
                                layer.msg(res.message, {icon:1});
                            } else {
                                layer.msg(res.message, {icon:2});
                            }
                        },
                        'json'
                    );
                }, function () {

                });
            } else if (layEvent === 'product_list') {
                layer.open({
                    type: 2,
                    title: "商品列表",
                    content: "{:url('shop_product_list')}?id=" + id,
                    maxmin: true,
                    area: ["98%", "98%"],
                    end: function () {

                    }
                })
            } else if (layEvent === 'printer_config') {
                layer.open({
                    type: 2,
                    title: "门店打印配置",
                    content: "{:url('shop_printer_config')}?shop_id=" + id,
                    maxmin: true,
                    area: ["80%", "80%"],
                    end: function () {
                        // 可以在这里添加配置完成后的刷新操作
                    }
                })
            } else if (layEvent === 'reset_password') {
                layer.open({
                    content: '<div id="open"><label></label><input type="password" name="pwd" class="layui-input" placeholder="请输入新密码"><input style="margin-top: 10px" type="password" name="again_pwd" class="layui-input" placeholder="请再次输入新密码"></div>',
                    btn: ['确定', '取消'],
                    yes: function (index, layero) {
                        var pwd = $("[name=pwd]").val();
                        var again_pwd=$("[name=again_pwd]").val();
                        $.post("{:url('Plat/ajax_reset_password')}", {
                            keynum: data.keynum,
                            password: pwd,
                            again_pwd:again_pwd
                        }, function (data) {
                            layer.msg(data.msg);
                        }, "json")
                    }
                })
            }

        });

        form.on('submit(demo-table-search)', function(data){
            var field = data.field; // 获得表单字段
            console.log(field);
            // 执行搜索重载
            table.reload('demo', {
                page: {
                    curr: 1 // 重新从第 1 页开始
                },
                where: field // 搜索的字段
            });
            // layer.msg('搜索成功<br>此处为静态模拟数据，实际使用时换成真实接口即可');
            return false; // 阻止默认 form 跳转
        });


        $('.demoTable .layui-btn').on('click', function () {
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });



</script>
