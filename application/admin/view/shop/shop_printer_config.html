<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>门店打印配置</title>
    {include file="public/iframeheader"/}
</head>

<body>

    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <!-- <div class="layui-card-header">门店打印机配置 - {$shop.title}</div> -->
                    <div class="layui-card-body" pad15>

                        <div class="layui-form" wid100 lay-filter="demo">
                            <input type="hidden" name="shop_id" value="{$shop_id}">
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">门店信息</label>
                                <div class="layui-input-block">
                                    <div class="layui-form-mid">{$shop.title} （{$shop.address}）</div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">打印机序列号 <span style="color: red;">*</span></label>
                                <div class="layui-input-block">
                                    <input type="text" name="printer_sn" value="{$config['printer_sn']|default=''}"
                                        class="layui-input" placeholder="请输入打印机序列号" lay-verify="required">
                                </div>
                                <div class="layui-form-mid layui-word-aux">易联云打印机的唯一序列号</div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">打印机密钥 <span style="color: red;">*</span></label>
                                <div class="layui-input-block">
                                    <input type="text" name="printer_key" value="{$config['printer_key']|default=''}"
                                        class="layui-input" placeholder="请输入打印机密钥" lay-verify="required">
                                </div>
                                <div class="layui-form-mid layui-word-aux">易联云打印机的验证密钥</div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">打印机昵称</label>
                                <div class="layui-input-block">
                                    <input type="text" name="printer_name" value="{$config['printer_name']|default=''}"
                                        class="layui-input" placeholder="请输入打印机昵称（可选）">
                                </div>
                                <div class="layui-form-mid layui-word-aux">便于识别的打印机名称</div>
                            </div>

                            
                            
                            
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="save_config">确认保存</button>
                                    <button type="button" class="layui-btn layui-btn-normal" id="test_print">测试打印</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        layui.use(['element', 'form'], function () {
            var form = layui.form;

            //监听提交
            form.on('submit(save_config)', function (data) {
                var load = layer.load(2, { shade: [0.1, '#fff'] });
                $.post("{:url('ajax_save_shop_printer_config')}", data.field, function (res) {
                    layer.close(load);
                    if (res.code == 0) {
                        layer.msg(res.message, {icon: 1}, function() {
                            // 保存成功后可以选择关闭弹窗或刷新页面
                            window.parent.layer.closeAll();
                            window.parent.location.reload();
                        });
                    } else {
                        layer.msg(res.message, {icon: 2});
                    }
                }, "json");

                return false;
            });

            // 测试打印按钮
            $('#test_print').on('click', function() {
                var formData = form.val('demo');
                console.log(formData);
                var load = layer.load(2, { shade: [0.1, '#fff'] });
                
                layer.confirm('确认要进行测试打印吗？', {
                    btn: ['确定', '取消']
                }, function(index) {
                    layer.close(index);
                    
                    $.post("{:url('test_shop_printer')}", formData, function (res) {
                        layer.close(load);
                        if (res.code == 0) {
                            layer.msg('测试打印成功！', {icon: 1});
                        } else {
                            layer.msg('测试打印失败：' + res.message, {icon: 2});
                        }
                    }, "json").fail(function() {
                        layer.close(load);
                        layer.msg('测试打印请求失败', {icon: 2});
                    });
                }, function() {
                    layer.close(load);
                });
            });
        });
    </script>

</body>

</html> 