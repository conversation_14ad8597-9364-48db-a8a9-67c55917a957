<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
<br/>
<style>
    /*.layui-table-view .layui-table td{*/
    /*    width: 85px;*/
    /*    height: 85px;*/
    /*}*/
    .laytable-cell-2-0-5 {
        height: 45px;
        line-height: 45px;
        text-align: center;
    }

    .layui-table-cell {
        height: 45px;
        line-height: 45px;
    }

</style>
<div style="margin: 20px;">


    <!--    <div class="demoTable layui-form">-->
    <!--        -->

    <!--        <button class="layui-btn  layui-btn-sm" data-type="reload" link="sub">搜索</button>-->
    <!--    </div>-->

    <div class="page-content">
        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
    </div>

    <script type="text/html" id="demo-toolbar-setRowChecked">
        <div class="layui-btn-container">f
            <button class="layui-btn layui-btn-sm" lay-event="getCheckData">添加商品(勾选)</button>
        </div>
    </script>


    <script type='text/html' id="barDemo">
        <!--    <a class='layui-btn layui-btn-sm' lay-event="product_list">管理门店商品</a>-->
        <!--    <a class='layui-btn layui-btn-sm' lay-event="edit">修改</a>-->
        <a class='layui-btn layui-btn-sm layui-bg-red' lay-event="del">删除</a>
    </script>

</div>

</body>

</html>
<script type="text/html" id="img">
    <img  src="{{d.product.cover}}" style="width: 48px;" onclick="showimg(this)" >
</script>
<script type="text/javascript">

    layui.use(['table'], function () {
        var table = layui.table;
        //方法级渲染
        table.render({
            elem: '#demo'
            , url: "{:url('ajax_shop_product_list')}?id={$id}"
            , cols: [[
                {type: 'checkbox'}
                , {type: 'numbers', title: '序号', width: 50}
                , {field: 'cover', title: '封面', toolbar: '#img'}
                , {
                    field: 'title', title: '商品名称', templet: function (d) {
                        if(d.product) {
                            return d.product.title;
                        }
                        return;
                    }
                }
                , {
                    field: 'price', title: '价格', templet: function (d) {
                        if(d.product) {
                            return d.product.price;

                        }
                        return;
                    }
                }
                , {field: 'add_time', title: '添加时间', width: 120}
                , {fixed: 'right', title: "操作", toolbar: '#barDemo'}
            ]]
            , page: true
            , toolbar: '#demo-toolbar-setRowChecked'
        });

        table.on('toolbar(demo)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
            console.log(obj.event)
            switch (obj.event) {
                case 'getCheckData':
                    layer.open({
                        type: 2,
                        title: "查看未添加商品",
                        content: "{:url('add_shop_product')}?id={$id}",
                        maxmin: true,
                        area: ["98%", "98%"],
                        end: function () {
                            table.reload('demo');
                        }
                    })
                    var data = checkStatus.data;  // 获取选中行数据
                    console.log(data);

                    break;
            }
            ;
        });

        table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            var id = data.id;
            if (layEvent === 'del') {
                layer.confirm('确认删除【' + data.product.title + '】吗？', {
                    btn: ['确定', '关闭'] //按钮
                }, function () {
                    $.post(
                        "{:url('del_shop_product')}",
                        {shop_id: data.shop_id, product_id: data.product_id},
                        function (res) {
                            if (res.code == 0) {
                                table.reload('demo');
                                layer.msg(res.msg, {icon: 1});
                            } else {
                                layer.msg(res.msg, {icon: 1});
                            }
                        },
                        'json'
                    );
                }, function () {

                });
            }
        });

        var $ = layui.$, active = {
            reload: function () {
                var keyword = $("[name=keyword]").val();
                var field = $("[name=field]").val();
                var is_pay = $("[name=is_pay]").val();
                //执行重载
                table.reload('demo', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    , where: {
                        keyword: keyword,
                        field: field,
                        is_pay: is_pay
                    }
                });
            }
        };

        $('.demoTable .layui-btn').on('click', function () {
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });

    //点击放大图片
    function showimg(t) {
        var src = $(t).attr("src");
        if (src == '') {
            layer.msg("图为为空！");
            return false;
        }
        layer.open({
            type: 1,
            title: false,
            area: '516px',
            content: '<img style="display: inline-block; width: 100%; height: 100%;" src="' + src + '">'
        });
    }

</script>
