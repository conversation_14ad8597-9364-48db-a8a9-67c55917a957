<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
<br/>
<style>
    /*.layui-table-view .layui-table td{*/
    /*    width: 85px;*/
    /*    height: 85px;*/
    /*}*/
    .laytable-cell-2-0-5 {
        height: 45px;
        line-height: 45px;
        text-align: center;
    }

    .layui-table-cell {
        height: 45px;
        line-height: 45px;
    }

</style>
<div style="margin: 20px;">


    <!--    <div class="demoTable layui-form">-->
    <!--        -->

    <!--        <button class="layui-btn  layui-btn-sm" data-type="reload" link="sub">搜索</button>-->
    <!--    </div>-->

    <div class="page-content">
        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
    </div>

    <script type="text/html" id="demo-toolbar-setRowChecked">
        <div class="layui-btn-container">f
            <button class="layui-btn layui-btn-sm" lay-event="getCheckData">选中添加</button>
        </div>
    </script>


    <!--  <script type='text/html' id="barDemo">-->
    <!--    <a class='layui-btn layui-btn-sm' lay-event="product_list">管理门店商品</a>-->
    <!--    <a class='layui-btn layui-btn-sm' lay-event="edit">修改</a>-->
    <!--    <a class='layui-btn layui-btn-sm layui-bg-red' lay-event="del">删除</a>-->
    <!--  </script>-->

</div>

</body>

</html>
<script type="text/html" id="img">
    <img  src="{{d.cover}}" style="width: 48px;" onclick="showimg(this)" >
</script>
<script type="text/javascript">

    layui.use(['table'], function () {
        var table = layui.table;
        //方法级渲染
        table.render({
            elem: '#demo'
            , url: "{:url('ajax_add_shop_product_list')}?id={$id}"
            , cols: [[
                {type: 'checkbox'}
                , {field: 'cover', title: '封面', toolbar: '#img' }
                , {field: 'title', title: '商品名称'}
                , {field: 'add_time', title: '添加时间'}
            ]]
            , page: true
            , toolbar: '#demo-toolbar-setRowChecked'
        });

        table.on('toolbar(demo)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
            console.log(obj.event)
            switch (obj.event) {
                case 'getCheckData':
                    var data = checkStatus.data;  // 获取选中行数据
                    var ids = '';
                    for (var key in data) {
                        ids += data[key].id + ',';
                    }

                    $.post(
                        "{:url('ajax_add_shop_product')}",
                        {ids:ids,id:"{$id}"},
                        function (res){
                            if (res.code == 0) {
                                // 成功
                                layer.msg('添加成功', {icon: 1});
                                setInterval(function () {
                                    window.parent.layer.closeAll();//关闭弹窗
                                }, 1500);
                            } else {
                                layer.msg(res.msg, {icon:2});
                            }
                        },
                        'json'

                    );

                    break;
            }
            ;
        });

        table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            var id = data.id;
            if (layEvent === 'order-detail') {
                layer.open({
                    type: 2,
                    title: "订单详情",
                    content: "{:url('order_detail')}?order_sn=" + order_sn,
                    maxmin: true,
                    area: ["98%", "98%"],
                    end: function () {
                        get_table();
                    }
                })
            } else if (layEvent === 'edit') {
                layer.open({
                    type: 2,
                    title: "修改商品",
                    content: "{:url('edit_shop')}?id=" + id,
                    maxmin: true,
                    area: ["98%", "98%"],
                    end: function () {

                    }
                })
            }
        });

        var $ = layui.$, active = {
            reload: function () {
                var keyword = $("[name=keyword]").val();
                var field = $("[name=field]").val();
                var is_pay = $("[name=is_pay]").val();
                //执行重载
                table.reload('demo', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    , where: {
                        keyword: keyword,
                        field: field,
                        is_pay: is_pay
                    }
                });
            }
        };

        $('.demoTable .layui-btn').on('click', function () {
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });


    });
    //点击放大图片
    function showimg(t) {
        var src = $(t).attr("src");
        if (src == '') {
            layer.msg("图为为空！");
            return false;
        }
        layer.open({
            type: 1,
            title: false,
            area: '516px',
            content: '<img style="display: inline-block; width: 100%; height: 100%;" src="' + src + '">'
        });
    }

</script>
