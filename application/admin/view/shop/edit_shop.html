<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>添加修改</title>
  {include file="public/iframeheader"/}
  <!-- 编辑器源码文件 -->
</head>
<style>

  .layui-laydate-content>.layui-laydate-list {
      padding-bottom: 0px;
      overflow: hidden;
  }
  .layui-laydate-content>.layui-laydate-list>li{
      width:50%
  }

  .merge-box .scrollbox .merge-list {
      padding-bottom: 5px;
  }
  </style>
<!-- 腾讯地图API，包含service库用于地理编码服务 -->
<script src="https://map.qq.com/api/gljs?v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77&libraries=service"></script>
<body>

<div class="layui-fluid">
  <div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-body" pad15>


          <div class="layui-form" wid100 lay-filter="">
            <input type="hidden" name="id" value="{$info['id']|default=''}">

            <div class="layui-form-item">
              <label class="layui-form-label">客户编号</label>
              <span
                      style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
              <div class="layui-input-block">
                <input type="text" name="code" value="{$info['code']}" disabled="disabled" style="width: 400px;"
                       class="layui-input" placeholder="请输入客户编号">
              </div>
              <div class="layui-input-inline layui-input-company"></div>
<!--              <div class="layui-form-mid layui-word-aux" style="margin-left: 130px;">-->
<!--                登录账号，必须是字母或者数字或者字母加数字,不支持修改,默认登录密码为111111<font-->
<!--                      color='red'>账号前缀自动增加:{$front}</font>-->
<!--              </div>-->
            </div>

            <div class="layui-form-item">
              <label class="layui-form-label">门店名称</label>
              <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
              <div class="layui-input-inline">
                <input type="text" name="title"  style="width: 400px;" lay-verify="required" value="{$info['title']}"
                       class="layui-input " placeholder="请输入门店名称">
              </div>
            </div>

            <div class="layui-form-item">
              <label class="layui-form-label">店铺logo</label>
              <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
              <div class="layui-input-block layui-btn-container ">
                  {:UpImage("cover",200,200,$info.cover)}
              </div>
              <div class="layui-input-inline layui-input-company"></div>
              <div class="layui-form-mid layui-word-aux" style="margin-left: 130px;color:#FF5722;">推荐尺寸为 400*400</div>
            </div>

            <div class="layui-form-item">
              <label class="layui-form-label">店铺背景图</label>
              <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
              <div class="layui-input-block layui-btn-container ">
                {:UpImage("background_image",200,200,$info.background_image)}
              </div>
              <div class="layui-input-inline layui-input-company"></div>
              <div class="layui-form-mid layui-word-aux" style="margin-left: 130px;color:#FF5722;">推荐尺寸为 750*86</div>
            </div>

            <div class="layui-form-item">
              <label class="layui-form-label">联系方式</label>
              <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
              <div class="layui-input-inline">
                <input type="text" name="phone" lay-verify="required" value="{$info['phone']}" class="layui-input" placeholder="请输入联系方式">
              </div>
              <div class="layui-input-inline layui-input-company"></div>
              <div class="layui-form-mid layui-word-aux"></div>
            </div>

            <div class="layui-form-item">
              <label class="layui-form-label">营业开始时间</label>
              <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
              <div class="layui-input-inline">
                <input type="text" name="open_time" value="{$info['open_time']}" class="layui-input" placeholder="请选择营业开始时间">
              </div>
            </div>

            <div class="layui-form-item">
              <label class="layui-form-label">营业结束时间</label>
              <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
              <div class="layui-input-inline">
                <input type="text" name="close_time" value="{$info['close_time']}" class="layui-input" placeholder="请选择营业结束时间">
              </div>
            </div>

            <div class="layui-form-item">
              <label class="layui-form-label">地址</label>
              <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
              <div class="layui-input-inline" style="width: 400px">
                <input type="text" name="address" lay-verify="required" value="{$info['address']}" class="layui-input" placeholder="请输入地址并搜索">
              </div>
              <div class="layui-input-inline layui-input-company"></div>
              <button class="layui-btn" id="search_address">搜索</button>
              <div class="layui-form-mid layui-word-aux"></div>
            </div>


            <div class="layui-form-item">
              <label class="layui-form-label"></label>
              <div class="layui-input-block layui-btn-container ">
                <div id="map-container" style="width: 700px;height: 400px"></div>
              </div>
            </div>

            <div class="layui-form-item layui-disabled" style="display: none">
              <label class="layui-form-label">纬度</label>
              <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
              <div class="layui-input-inline">
                <input type="hidden" name="latitude" lay-verify="required" value="{$info['lat']}"  class="layui-input">
              </div>
              <div class="layui-input-inline layui-input-company"></div>
              <div class="layui-form-mid layui-word-aux"></div>
            </div>

            <div class="layui-form-item" style="display: none">
              <label class="layui-form-label">经度</label>
              <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
              <div class="layui-input-inline">
                <input type="hidden" name="longitude" lay-verify="required"  value="{$info['lng']}" class="layui-input"  lay-verify="location">
              </div>
              <div class="layui-input-inline layui-input-company"></div>
              <div class="layui-form-mid layui-word-aux"></div>
            </div>




            <div class="layui-form-item">
              <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="sub2">确认</button>
              </div>
            </div>

          </div>

        </div>
      </div>
    </div>
  </div>
</div>

<script>
  layui.config({
    base : '/static/admin/'
  }).extend({
    tencentMap: 'map/tencentMap',
  }).use(['tencentMap'],function(){
    var $ = layui.jquery;
    var tencentMap = layui.tencentMap;


    //初始化腾讯地图，使用现有的经纬度坐标
    var existingLng = $('input[name=longitude]').val() || 116.307484;
    var existingLat = $('input[name=latitude]').val() || 39.984120;
    tencentMap.initMap('map-container', parseFloat(existingLng), parseFloat(existingLat));

    // 如果有现有坐标但没有地址，尝试获取地址
    setTimeout(function() {
        var currentAddress = $('input[name=address]').val();
        if (!currentAddress && existingLng && existingLat) {
            console.log('尝试获取现有坐标的地址信息...');
            tencentMap.reverseGeocode(parseFloat(existingLat), parseFloat(existingLng));
        }
    }, 1000); // 延迟1秒确保地图完全加载

    // 如果有现有地址，可以选择性地搜索以验证位置
    // var address = $('input[name=address]').val();
    // if (address) {
    //     tencentMap.searchMap(address);
    // }

    $("#search_address").click(function () {
      var address = $('input[name=address]').val();
      if (address == '') {
        layer.msg('请输入搜索地址');
        return false;
      }
      tencentMap.searchMap(address);
    });

    //搜索地图，绑定一个点击事件，实现搜索
    $("#search_map").click(function () {
      var address = '广东省广州市海珠区广州塔';
      tencentMap.searchMap(intactAddress);
    })
  });
</script>

<script>
  //一般直接写在一个js文件中
  layui.use(['element', 'form', 'layedit','laydate'], function () {
    var form = layui.form;
    var laydate = layui.laydate;
    laydate.render({
      elem: 'input[name=open_time]',
      type: 'time',
      format: 'HH:mm',
      ready: function(){
        formatminutes();
      }
    });
    laydate.render({
      elem: 'input[name=close_time]',
      type: 'time',
      format: 'HH:mm',
      ready: function(){
        formatminutes();
      }
    });

    function formatminutes(date){
      var aa = $(".laydate-time-list li ol")[1];
      var showtime = $($(".laydate-time-list li ol")[1]).find("li");
      for (var i = 0; i < showtime.length; i++) {
          var t00 = showtime[i].innerText;
          if (t00 != "00" && t00 != "10" && t00 != "20" && t00 != "30" && t00 != "40" && t00 != "50") {
              showtime[i].hidden = true;
          }
      }
      $($(".laydate-time-list li ol")[2]).find("li").remove();  //清空秒
    }
    form.on('submit(sub2)', function (data) {
      load = layer.load(2, {shade: [0.1, '#fff']});
      $.post("{:url('ajax_edit_shop')}", data.field, function (data) {
        layer.close(load);
        if (data.code == 0) {
            layer.msg(data.message);
            setInterval(function () {
              window.parent.layer.closeAll();//关闭弹窗
            }, 1500);
        } else {
          layer.msg(data.message);
        }
      }, "json");

      return false;
    });
  });
</script>

</body>

</html>
