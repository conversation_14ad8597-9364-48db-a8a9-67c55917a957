{include file="public/iframeheader"/}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>充值套餐管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/admin/layui/css/layui.css" media="all">
</head>
<body>
            <div style="padding: 15px;">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <!-- 搜索栏 -->
                        <div class="layui-form layui-row">
                            <div class="layui-col-md2">
                                <input type="text" name="keyword" placeholder="套餐名称" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-col-md2">
                                <select name="status">
                                    <option value="">全部状态</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="layui-col-md2">
                                <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                            <div class="layui-col-md6" style="text-align: right;">
                                <button class="layui-btn layui-btn-normal" id="addPackage">添加套餐</button>
                            </div>
                        </div>
                        
                        <!-- 数据表格 -->
                        <table class="layui-hide" id="packageTable" lay-filter="packageTable"></table>
                    </div>
                </div>
            </div>

    <script type="text/html" id="statusTpl">
        <input type="checkbox" name="status" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="statusSwitch" {{d.status == 1 ? 'checked' : ''}}>
    </script>

    <script type="text/html" id="operationTpl">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </script>

    <script>
        layui.use(['table', 'form', 'layer'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            
            // 渲染表格
            table.render({
                elem: '#packageTable',
                url: '{:url("packageList")}',
                page: true,
                cols: [[
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'name', title: '套餐名称'},
                    {field: 'recharge_amount', title: '充值金额', templet: function(d){
                        return '¥' + d.recharge_amount;
                    }},
                    // {field: 'bonus_amount', title: '赠送金额', templet: function(d){
                    //     return '¥' + d.bonus_amount;
                    // }},
                    // {field: 'total_amount', title: '到账金额', templet: function(d){
                    //     return '¥' + d.total_amount;
                    // }},
                    {field: 'sort_order', title: '排序', width: 80},
                    // {field: 'time_range', title: '活动时间'},
                    {field: 'status', title: '状态', templet: '#statusTpl'},
                    {field: 'add_time', title: '创建时间'},
                    {title: '操作', toolbar: '#operationTpl', fixed: 'right'}
                ]],
                limits: [15, 30, 50],
                limit: 15
            });
            
            // 搜索
            form.on('submit(search)', function(data){
                table.reload('packageTable', {
                    where: data.field,
                    page: {curr: 1}
                });
                return false;
            });
            
            // 状态切换
            form.on('switch(statusSwitch)', function(data){
                var status = data.elem.checked ? 1 : 0;
                $.post('{:url("packageStatus")}', {
                    id: data.value,
                    status: status
                }, function(res){
                    if(res.code !== 0){
                        layer.msg(res.msg || '操作失败');
                        data.elem.checked = !data.elem.checked;
                        form.render('checkbox');
                    } else {
                        layer.msg('状态更新成功');
                    }
                }, 'json');
            });
            
            // 添加套餐
            $('#addPackage').on('click', function(){
                layer.open({
                    type: 2,
                    title: '添加充值套餐',
                    shadeClose: false,
                    shade: 0.3,
                    maxmin: true,
                    area: ['800px', '600px'],
                    content: '{:url("packageAdd")}',
                    end: function(){
                        table.reload('packageTable');
                    }
                });
            });
            
            // 工具栏事件
            table.on('tool(packageTable)', function(obj){
                var data = obj.data;
                if(obj.event === 'edit'){
                    layer.open({
                        type: 2,
                        title: '编辑充值套餐',
                        shadeClose: false,
                        shade: 0.3,
                        maxmin: true,
                        area: ['800px', '600px'],
                        content: '{:url("packageEdit")}?id=' + data.id,
                        end: function(){
                            table.reload('packageTable');
                        }
                    });
                } else if(obj.event === 'del'){
                    layer.confirm('确定删除这个套餐吗？', function(index){
                        $.post('{:url("packageDelete")}', {
                            id: data.id
                        }, function(res){
                            if(res.code === 0){
                                layer.msg('删除成功');
                                obj.del();
                            } else {
                                layer.msg(res.msg || '删除失败');
                            }
                            layer.close(index);
                        }, 'json');
                    });
                }
            });
        });
    </script>
</body>
</html> 