{include file="public/iframeheader"/}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加充值套餐</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/admin/layui/css/layui.css" media="all">
</head>
<body>
    <div style="padding: 20px;">
        <form class="layui-form" lay-filter="packageForm">
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-form-item">
                        <label class="layui-form-label">套餐名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" lay-verify="required" placeholder="请输入套餐名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">充值金额</label>
                        <div class="layui-input-block">
                            <input type="number" name="recharge_amount" lay-verify="required|number" placeholder="0.00" step="0.01" min="0" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <!-- <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">赠送金额</label>
                        <div class="layui-input-block">
                            <input type="number" name="bonus_amount" lay-verify="number" placeholder="0.00" step="0.01" min="0" value="0" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div> -->
            </div>

            <div class="layui-row">
                <!-- <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">折扣率(%)</label>
                        <div class="layui-input-block">
                            <input type="number" name="discount_rate" lay-verify="number" placeholder="如：95" step="0.1" min="0" max="100" autocomplete="off" class="layui-input">
                            <div class="layui-form-mid layui-word-aux">可选，用于展示优惠幅度</div>
                        </div>
                    </div>
                </div> -->
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">排序</label>
                        <div class="layui-input-block">
                            <input type="number" name="sort_order" lay-verify="number" placeholder="0" value="0" min="0" autocomplete="off" class="layui-input">
                            <div class="layui-form-mid layui-word-aux">数字越小越靠前</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">开始时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="start_time" id="start_time" placeholder="选择开始时间" autocomplete="off" class="layui-input">
                            <div class="layui-form-mid layui-word-aux">可选，留空表示不限制</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">结束时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="end_time" id="end_time" placeholder="选择结束时间" autocomplete="off" class="layui-input">
                            <div class="layui-form-mid layui-word-aux">可选，留空表示不限制</div>
                        </div>
                    </div>
                </div>
            </div> -->

            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" checked>
                    <input type="radio" name="status" value="0" title="禁用">
                </div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">套餐描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入套餐描述" class="layui-textarea" rows="4"></textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="submitForm">立即提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>

    <script src="/static/admin/layui/layui.js"></script>
    <script>
        layui.use(['form', 'laydate', 'layer'], function(){
            var form = layui.form;
            var laydate = layui.laydate;
            var layer = layui.layer;
            
            // 日期时间选择器
            laydate.render({
                elem: '#start_time',
                type: 'datetime'
            });
            
            laydate.render({
                elem: '#end_time',
                type: 'datetime'
            });
            
            // 监听提交
            form.on('submit(submitForm)', function(data){
                var loadIndex = layer.load(1, {shade: [0.3, '#fff']});
                
                $.post('{:url("packageAdd")}', data.field, function(res){
                    layer.close(loadIndex);
                    if(res.code === 0){
                        layer.msg('添加成功', {icon: 1}, function(){
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg || '添加失败', {icon: 2});
                    }
                }, 'json').fail(function(){
                    layer.close(loadIndex);
                    layer.msg('网络错误', {icon: 2});
                });
                
                return false;
            });

            // 监听充值金额和赠送金额变化，自动计算到账金额
            form.on('input(recharge_amount)', function(){
                calculateTotal();
            });
            
            $('input[name="bonus_amount"]').on('input', function(){
                calculateTotal();
            });
            
            function calculateTotal(){
                var rechargeAmount = parseFloat($('input[name="recharge_amount"]').val()) || 0;
                var bonusAmount = parseFloat($('input[name="bonus_amount"]').val()) || 0;
                var totalAmount = rechargeAmount + bonusAmount;
                
                // 可以在页面上显示计算结果，这里暂时省略UI部分
                console.log('到账金额：', totalAmount);
            }
        });
    </script>
</body>
</html> 