<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>充值记录管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/admin/layui/css/layui.css" media="all">
</head>
<body>
    <div class="layui-layout layui-layout-admin">
        <div class="layui-body">
            <div style="padding: 15px;">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h2 class="header-title">充值记录管理</h2>
                    </div>
                    <div class="layui-card-body">
                        <!-- 搜索栏 -->
                        <div class="layui-form layui-row">
                            <div class="layui-col-md2">
                                <input type="text" name="keyword" placeholder="充值单号/卡号/用户" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-col-md2">
                                <select name="payment_status">
                                    <option value="">全部状态</option>
                                    <option value="0">待支付</option>
                                    <option value="1">已支付</option>
                                    <option value="2">支付失败</option>
                                    <option value="3">已退款</option>
                                </select>
                            </div>
                            <div class="layui-col-md2">
                                <select name="payment_method">
                                    <option value="">全部支付方式</option>
                                    <option value="1">微信支付</option>
                                    <option value="2">支付宝</option>
                                    <option value="3">现金</option>
                                    <option value="4">银行卡</option>
                                </select>
                            </div>
                            <div class="layui-col-md2">
                                <input type="text" name="start_time" id="start_time" placeholder="开始时间" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-col-md2">
                                <input type="text" name="end_time" id="end_time" placeholder="结束时间" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-col-md2">
                                <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                        
                        <div class="layui-row" style="margin-top: 10px;">
                            <div class="layui-col-md12" style="text-align: right;">
                                <button class="layui-btn layui-btn-normal" id="adminRecharge">管理员代充</button>
                                <button class="layui-btn layui-btn-primary" id="exportData">导出数据</button>
                            </div>
                        </div>
                        
                        <!-- 数据表格 -->
                        <table class="layui-hide" id="recordTable" lay-filter="recordTable"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="operationTpl">
        <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
    </script>

    <script src="/static/admin/layui/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'layer', 'laydate'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var laydate = layui.laydate;
            
            // 日期选择器
            laydate.render({
                elem: '#start_time',
                type: 'datetime'
            });
            
            laydate.render({
                elem: '#end_time',
                type: 'datetime'
            });
            
            // 渲染表格
            table.render({
                elem: '#recordTable',
                url: '/admin/card_recharge/recordList',
                page: true,
                cols: [[
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'recharge_no', title: '充值单号', width: 160},
                    {field: 'cardnum', title: '卡号', width: 120},
                    {field: 'user_info', title: '用户', width: 120},
                    {field: 'package_name', title: '充值套餐', width: 140},
                    {field: 'recharge_amount', title: '充值金额', width: 100, templet: function(d){
                        return '¥' + d.recharge_amount;
                    }},
                    {field: 'bonus_amount', title: '赠送金额', width: 100, templet: function(d){
                        return '¥' + d.bonus_amount;
                    }},
                    {field: 'total_amount', title: '到账金额', width: 100, templet: function(d){
                        return '¥' + d.total_amount;
                    }},
                    {field: 'payment_method_text', title: '支付方式', width: 100},
                    {field: 'payment_status_text', title: '支付状态', width: 100, templet: function(d){
                        var color = '';
                        switch(d.payment_status) {
                            case 0: color = 'orange'; break;
                            case 1: color = 'green'; break;
                            case 2: color = 'red'; break;
                            case 3: color = 'gray'; break;
                        }
                        return '<span style="color: ' + color + '">' + d.payment_status_text + '</span>';
                    }},
                    {field: 'operator_type_text', title: '操作类型', width: 100},
                    {field: 'add_time', title: '创建时间', width: 160},
                    {title: '操作', width: 100, toolbar: '#operationTpl', fixed: 'right'}
                ]],
                limits: [15, 30, 50],
                limit: 15
            });
            
            // 搜索
            form.on('submit(search)', function(data){
                table.reload('recordTable', {
                    where: data.field,
                    page: {curr: 1}
                });
                return false;
            });
            
            // 管理员代充
            $('#adminRecharge').on('click', function(){
                layer.open({
                    type: 2,
                    title: '管理员代充',
                    shadeClose: false,
                    shade: 0.3,
                    maxmin: true,
                    area: ['800px', '600px'],
                    content: '/admin/card_recharge/adminRecharge',
                    end: function(){
                        table.reload('recordTable');
                    }
                });
            });
            
            // 导出数据
            $('#exportData').on('click', function(){
                layer.msg('导出功能开发中...', {icon: 3});
            });
            
            // 工具栏事件
            table.on('tool(recordTable)', function(obj){
                var data = obj.data;
                if(obj.event === 'detail'){
                    layer.open({
                        type: 2,
                        title: '充值记录详情',
                        shadeClose: false,
                        shade: 0.3,
                        maxmin: true,
                        area: ['800px', '600px'],
                        content: '/admin/card_recharge/recordDetail?id=' + data.id
                    });
                }
            });
        });
    </script>
</body>
</html> 