  <div id="LAY_app">
    <div class="layui-layout layui-layout-admin">
      <div class="layui-header">
        <!-- 头部区域 -->
        <ul class="layui-nav layui-layout-left">
          <li class="layui-nav-item layadmin-flexible" lay-unselect>
            <a href="javascript:;" layadmin-event="flexible" title="侧边伸缩">
              <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
            </a>
          </li>

        {if condition="get_weburl(session('cn_accountinfo.basekeynum')) neq '' "}
          <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <a href="http://{:get_weburl(session("cn_accountinfo.basekeynum"))}" target="_blank" title="前台">
              <i class="layui-icon layui-icon-home"></i>
            </a>
          </li>
        {/if}


          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;" layadmin-event="refresh" title="刷新">
              <i class="layui-icon layui-icon-refresh-3"></i>
            </a>
          </li>


          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;"  title="清理缓存"  link="redis_cache_clear">
              <i class="layui-icon layui-icon-fonts-clear"></i>
            </a>
          </li>



          <!--
          <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <input type="text" placeholder="搜索..." autocomplete="off" class="layui-input layui-input-search" layadmin-event="serach" lay-action="template/search.html?keywords=">
          </li>
           -->
        </ul>

        <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right">



          <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <a href="javascript:;" layadmin-event="theme">
              <i class="layui-icon layui-icon-theme"></i>
            </a>
          </li>
          <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <a href="javascript:;" layadmin-event="note">
              <i class="layui-icon layui-icon-note"></i>
            </a>
          </li>
          <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <a href="javascript:;" layadmin-event="fullscreen">
              <i class="layui-icon layui-icon-screen-full"></i>
            </a>
          </li>
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;">
              <cite>{:session("cn_accountinfo.accountname")}</cite>
            </a>
            <dl class="layui-nav-child">
              <!--
              <dd><a lay-href="set/user/info.html">基本资料</a></dd>
            -->
              <dd><a lay-href="{:url('plat/password')}">修改密码</a></dd>
              {if condition="get_wechat_bind(session('cn_accountinfo.account_id')) eq 1"}
                <hr>
                <dd><a onclick="unbind_wechat()">解除微信绑定</a></dd>
              {/if}

              <hr>
              <dd   id="logout"  style="text-align: center;cursor:pointer;"><a>退出</a></dd>
            </dl>
          </li>


          <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <a href="javascript:;" layadmin-event="about"><i class="layui-icon layui-icon-more-vertical"></i></a>
          </li>

          <li class="layui-nav-item layui-show-xs-inline-block layui-hide-sm" lay-unselect>
            <a href="javascript:;" layadmin-event="more"><i class="layui-icon layui-icon-more-vertical"></i></a>
          </li>
        </ul>
      </div>

      <script>
        function unbind_wechat() {
          layer.confirm('您确定要解除微信绑定吗？', {
            btn: ['确定', '取消']
          }, function () {
            $.post("{:url('plat/self_unbind_wechat')}", {}, function (data) {
              if(data.code==0){
                layer.msg("解除微信绑定成功",{icon:1,time:1500});
                setTimeout(function(){
                  location.reload();
                }, 1500);
              }else{
                layer.msg(data.msg);
              }
            });
          });
        }
      </script>

