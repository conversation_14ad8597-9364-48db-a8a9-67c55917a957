<!-- 侧边菜单 -->
<div class="layui-side layui-side-menu">
  <div class="layui-side-scroll">
    <div class="layui-logo" lay-href="{:url('Index/console')}">
      <span>{$cn_webconfig['titlecn']|default="后台管理系统"}</span>
    </div>

    <ul class="layui-nav layui-nav-tree" lay-shrink="all" id="LAY-system-side-menu" lay-filter="layadmin-system-side-menu">


      {volist name="menulist_arr" id="menulist_arr0"}
      {if condition="$plat_config['is_open_api'] eq 0"}
      <li data-name="set" plat_config="{$plat_config['is_open_api']}" class="layui-nav-item" {if condition="$menulist_arr0['menu_name'] eq '网站接口' "} style="display:none" {/if}>
      <a href="javascript:;" lay-tips="{$menulist_arr0['menu_name']}" lay-direction="2">
        <i class="layui-icon  {$menulist_arr0['icon']} "></i>
        <cite>{$menulist_arr0['menu_name']}</cite>
      </a>

      {volist name="menulist_arr0['children']" id="menulist_arr1"}
      <dl class="layui-nav-child">

        {if condition="isset($menulist_arr1['is_divider_before']) && $menulist_arr1['is_divider_before'] eq 1"}
        <dd class="menu-divider"></dd>
        {/if}

        <dd class="layui-nav-itemed" >
          <a  lay-href="{:url(!empty($menulist_arr1['url'])?$menulist_arr1['url']:'Plat/developing?random='.create_guid())}">{$menulist_arr1['menu_name']}</a>

          <!--
          <dl class="layui-nav-child">
              {volist name="menulist_arr1['children']" id="menulist_arr2"}
            <dd><a lay-href="{$menulist_arr2['url']}.html">{$menulist_arr2['menu_name']}</a></dd>
            {/volist}
          </dl>
            -->
        </dd>


      </dl>
      {/volist}

      </li>
      {else/}
      <li data-name="set" class="layui-nav-item">
        <a href="javascript:;" lay-tips="{$menulist_arr0['menu_name']}" lay-direction="2">
          <i class="layui-icon  {$menulist_arr0['icon']} "></i>
          <cite>{$menulist_arr0['menu_name']}</cite>
        </a>

        {volist name="menulist_arr0['children']" id="menulist_arr1"}
        <dl class="layui-nav-child">

          {if condition="isset($menulist_arr1['is_divider_before']) && $menulist_arr1['is_divider_before'] eq 1"}
          <dd class="menu-divider"></dd>
          {/if}

          <dd class="layui-nav-itemed" >
            <a  lay-href="{:url(!empty($menulist_arr1['url'])?$menulist_arr1['url']:'Plat/developing?random='.create_guid())}">{$menulist_arr1['menu_name']}</a>

            <!--
            <dl class="layui-nav-child">
                {volist name="menulist_arr1['children']" id="menulist_arr2"}
              <dd><a lay-href="{$menulist_arr2['url']}.html">{$menulist_arr2['menu_name']}</a></dd>
              {/volist}
            </dl>
              -->
          </dd>


        </dl>
        {/volist}

      </li>
      {/if}
      {/volist}



    </ul>
  </div>
</div>

<!-- 添加分割线样式 -->
<style>
.layui-nav-child .menu-divider {
  height: 1px;
  margin: 8px 10px;
  overflow: hidden;
  background-color: rgba(255,255,255,.2);
  border-bottom: 1px solid rgba(0,0,0,.2);
  padding: 0;
  cursor: default;
}
.layui-nav-child .menu-divider:hover {
  background-color: rgba(255,255,255,.2);
}
</style>
