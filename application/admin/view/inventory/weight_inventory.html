{include file="public/iframeheader"/}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            计量商品库存管理
            <div class="layui-btn-group" style="float: right;">
                <button class="layui-btn layui-btn-sm" id="weight-inbound-btn">
                    <i class="layui-icon layui-icon-add-1"></i> 重量入库
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-normal" id="sales-stats-btn">
                    <i class="layui-icon layui-icon-chart"></i> 销售统计
                </button>
            </div>
        </div>
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">商品名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="product_name" id="product_name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">门店</label>
                        <div class="layui-input-inline">
                            <select name="shop_id" id="shop_id">
                                <option value="">全部</option>
                                {volist name="shops" id="shop"}
                                <option value="{$shop.id}">{$shop.title}</option>
                                {/volist}
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">库存状态</label>
                        <div class="layui-input-inline">
                            <select name="stock_status" id="stock_status">
                                <option value="">全部</option>
                                <option value="1">有库存</option>
                                <option value="2">无库存</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="search-btn">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button class="layui-btn layui-btn-primary" id="reset-btn">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table id="data-table" lay-filter="data-table"></table>

            <!-- 商品图片模板 -->
            <script type="text/html" id="product-thumb">
                {{# if(d.product_thumb){ }}
                <img src="{{d.product_thumb}}" style="max-width: 50px; max-height: 50px;" onclick="showBigImage(this.src)">
                {{# } else { }}
                <img src="__STATIC__/admin/img/nopic.jpg" style="max-width: 50px; max-height: 50px;">
                {{# } }}
            </script>

            <!-- 重量库存显示模板 -->
            <script type="text/html" id="weight-stock-tpl">
                {{# if(d.weight_stock && d.weight_stock > 0){ }}
                <span style="color: green; font-weight: bold;">{{d.weight_stock}}kg</span>
                {{# } else { }}
                <span style="color: red; font-weight: bold;">0kg</span>
                {{# } }}
            </script>

            <!-- 操作按钮模板 -->
            <script type="text/html" id="operation-tpl">
                <a class="layui-btn layui-btn-xs" lay-event="inbound">入库</a>
                <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="logs">库存日志</a>
                <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="sales">销售记录</a>
            </script>
        </div>
    </div>
</div>

<!-- 重量入库弹窗 -->
<div id="weight-inbound-dialog" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="inbound-form">
        <input type="hidden" name="inventory_id" id="inbound-inventory-id">
        <div class="layui-form-item">
            <label class="layui-form-label">商品信息</label>
            <div class="layui-input-block">
                <span id="inbound-product-info" style="color: #666;"></span>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">入库重量</label>
            <div class="layui-input-inline">
                <input type="number" name="weight" step="0.001" min="0.001" lay-verify="required|number" placeholder="请输入重量" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">单位：kg</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="inbound-submit">确认入库</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>

<script>
    function showBigImage(src) {
        // 显示大图
        layer.open({
            type: 1,
            title: false,
            closeBtn: 1,
            area: ['auto'],
            skin: 'layui-layer-nobg',
            shadeClose: true,
            content: '<img src="' + src + '" style="max-width: 100%; max-height: 500px;">'
        });
    }

    layui.use(['table', 'form', 'layer'], function() {
        var table = layui.table,
            form = layui.form,
            layer = layui.layer,
            $ = layui.jquery;

        // 表格实例
        var tableIns = table.render({
            elem: '#data-table',
            url: '{:url("WeightInventory/getWeightStockList")}',
            defaultToolbar: ['filter', 'exports', 'print'],
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            cols: [[
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'shop_name', title: '所属门店', width: 120},
                {field: 'product_thumb', title: '商品图片', templet: '#product-thumb', width: 100},
                {field: 'product_name', title: '商品名称', width: 200},
                {field: 'inventory_name', title: '规格名称', width: 150},
                {field: 'weight_stock', title: '重量库存', templet: '#weight-stock-tpl', width: 120},
                {field: 'stock_unit', title: '库存单位', width: 100},
                {field: 'add_time', title: '创建时间', sort: true, width: 160},
                {field: 'update_time', title: '更新时间', sort: true, width: 160},
                {fixed: 'right', title: '操作', toolbar: '#operation-tpl', width: 200}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.count,
                    "data": res.data
                };
            }
        });

        // 监听工具条
        table.on('tool(data-table)', function(obj) {
            var data = obj.data;
            if (obj.event === 'inbound') {
                // 重量入库
                $('#inbound-inventory-id').val(data.inventory_id);
                $('#inbound-product-info').text(data.product_name + ' - ' + data.inventory_name + ' (当前库存: ' + (data.weight_stock || 0) + 'kg)');
                layer.open({
                    type: 1,
                    title: '重量入库',
                    content: $('#weight-inbound-dialog'),
                    area: ['500px', '400px'],
                    btn: false,
                    success: function() {
                        form.render();
                    }
                });
            } else if (obj.event === 'logs') {
                // 库存日志
                layer.open({
                    type: 2,
                    title: '重量库存变动日志',
                    content: '{:url("WeightInventory/stockLogs")}?inventory_id=' + data.inventory_id,
                    area: ['90%', '80%']
                });
            } else if (obj.event === 'sales') {
                // 销售记录
                layer.open({
                    type: 2,
                    title: '销售记录',
                    content: '{:url("WeightInventory/salesRecords")}?product_id=' + data.product_id,
                    area: ['90%', '80%']
                });
            }
        });

        // 重量入库按钮
        $('#weight-inbound-btn').on('click', function() {
            layer.msg('请在表格中选择具体商品进行入库操作', {icon: 0});
        });

        // 销售统计按钮
        $('#sales-stats-btn').on('click', function() {
            layer.open({
                type: 2,
                title: '计量商品销售统计',
                content: '{:url("WeightInventory/salesStats")}',
                area: ['90%', '80%']
            });
        });

        // 监听入库表单提交
        form.on('submit(inbound-submit)', function(data) {
            var loadIndex = layer.load(2);
            $.ajax({
                url: '{:url("WeightInventory/weightInbound")}',
                type: 'POST',
                data: data.field,
                dataType: 'json',
                success: function(res) {
                    layer.close(loadIndex);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        layer.closeAll('page');
                        tableIns.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.close(loadIndex);
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
            return false;
        });

        // 搜索按钮点击事件
        $('#search-btn').on('click', function() {
            var param = {
                product_name: $('#product_name').val(),
                shop_id: $('#shop_id').val(),
                stock_status: $('#stock_status').val()
            };
            tableIns.reload({
                where: param,
                page: {
                    curr: 1
                }
            });
        });

        // 重置按钮点击事件
        $('#reset-btn').on('click', function() {
            $('#product_name').val('');
            $('#shop_id').val('');
            $('#stock_status').val('');
            form.render('select');
            $('#search-btn').click();
        });
    });
</script> 