{include file="public/iframeheader"/}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">入库单管理</div>
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">单据编号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="order_no" id="order_no" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">业务类型</label>
                        <div class="layui-input-inline">
                            <select name="business_type" id="business_type">
                                <option value="">全部</option>
                                <option value="1">采购入库</option>
                                <option value="2">调拨入库</option>
                                <option value="3">退货入库</option>
                                <option value="4">其他入库</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status" id="status">
                                <option value="">全部</option>
                                <option value="0">草稿</option>
                                <option value="1">已提交</option>
                                <!-- <option value="2">已审核</option> -->
                                <option value="3">已完成</option>
                                <option value="-1">已取消</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">日期范围</label>
                        <div class="layui-input-inline">
                            <input type="text" name="date_range" id="date_range" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">录入方式</label>
                        <div class="layui-input-inline">
                            <select name="is_manual" id="is_manual">
                                <option value="">全部</option>
                                <option value="1">手动录入</option>
                                <option value="0">系统生成</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="search-btn">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button class="layui-btn layui-btn-primary" id="reset-btn">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                        <button class="layui-btn layui-btn-normal" id="export-btn">
                            <i class="layui-icon layui-icon-export"></i> 导出
                        </button>
                    </div>
                </div>
            </div>

            <!-- 表格工具栏 -->
            <!-- <div class="table-toolbar">
                <div class="layui-btn-group">
                    <button class="layui-btn layui-btn-normal" id="add-btn">
                        <i class="layui-icon layui-icon-add-1"></i> 新增入库单
                    </button>
                </div>
            </div> -->

            <!-- 数据表格 -->
            <table id="data-table" lay-filter="data-table"></table>

            <!-- 行工具栏模板 -->
            <script type="text/html" id="row-toolbar">
                <div class="layui-btn-group">
                    <a class="layui-btn layui-btn-xs" lay-event="detail">查看</a>
                    <!-- <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="export">导出详情</a> -->
                    {{# if(d.status == 0){ }}
                    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
                    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="submit">提交</a>
                    {{# } }}
                    {{# if(d.status == 1){ }}
                    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="complete">审核并完成</a>
                    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="cancel">取消</a>
                    {{# } }}
                </div>
            </script>
        </div>
    </div>
</div>

<script>
    layui.use(['table', 'form', 'laydate'], function() {
        var table = layui.table,
            form = layui.form,
            laydate = layui.laydate,
            $ = layui.jquery;

        // 日期范围选择器
        laydate.render({
            elem: '#date_range',
            range: true
        });

        // 表格实例
        table.render({
            elem: '#data-table',
            url: '{:url("inventory/getInStockList")}',
            toolbar: '#toolbar',
            defaultToolbar: ['filter', 'exports', 'print'],
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            cols: [[
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'order_no', title: '单据编号', width: 200},
                {field: 'business_type_text', title: '业务类型'},
                {field: 'shop_name', title: '店铺'},
                {field: 'supplier_name', title: '相关单位'},
                {field: 'total_amount', title: '总金额', templet: function(d){
                    return '￥' + d.total_amount;
                }},
                // {field: 'related_order_no', title: '关联单号'},
                {field: 'status_text', title: '状态', templet: function(d){
                    var statusClass = '';
                    switch(d.status) {
                        case 0: statusClass = ''; break;
                        case 1: statusClass = 'layui-bg-blue'; break;
                        case 2: statusClass = 'layui-bg-green'; break;
                        case 3: statusClass = 'layui-bg-green'; break;
                        case -1: statusClass = 'layui-bg-gray'; break;
                    }
                    return '<span class="layui-badge ' + statusClass + '">' + d.status_text + '</span>';
                }},
                {field: 'created_by', title: '创建人'},
                {field: 'created_at', title: '创建时间', sort: true},
                {fixed: 'right', title: '操作', toolbar: '#row-toolbar'}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.count,
                    "data": res.data
                };
            }
        });

        // 搜索按钮点击事件
        $('#search-btn').on('click', function() {
            var param = {
                order_no: $('#order_no').val(),
                business_type: $('#business_type').val(),
                status: $('#status').val(),
                date_range: $('#date_range').val(),
                is_manual: $('#is_manual').val()
            };
            table.reload('data-table', {
                where: param,
                page: {
                    curr: 1
                }
            });
        });

        // 重置按钮点击事件
        $('#reset-btn').on('click', function() {
            $('#order_no').val('');
            $('#business_type').val('');
            $('#status').val('');
            $('#date_range').val('');
            $('#is_manual').val('');
            form.render('select');
            $('#search-btn').click();
        });

        // 导出按钮点击事件
        $('#export-btn').on('click', function() {
            var param = {
                order_no: $('#order_no').val(),
                business_type: $('#business_type').val(),
                status: $('#status').val(),
                date_range: $('#date_range').val(),
                is_manual: $('#is_manual').val()
            };
            
            // 构建导出URL
            var exportUrl = '{:url("inventory/exportDetail")}?' + 
                'order_no=' + encodeURIComponent(param.order_no) + 
                '&business_type=' + encodeURIComponent(param.business_type) + 
                '&status=' + encodeURIComponent(param.status) + 
                '&date_range=' + encodeURIComponent(param.date_range) + 
                '&is_manual=' + encodeURIComponent(param.is_manual);
            
            // 跳转到导出页面
            window.location.href = exportUrl;
        });

        // 新增按钮点击事件
        $('#add-btn').on('click', function() {
            window.location.href = '{:url("inventory/addInStock")}';
        });

        // 监听行工具条事件
        table.on('tool(data-table)', function(obj) {
            var data = obj.data;
            var event = obj.event;
            
            // 根据不同的事件执行不同的操作
            switch(event) {
                case 'detail': // 查看详情
                    window.location.href = '{:url("inventory/detail")}?id=' + data.id;
                    break;
                case 'export': // 导出详情
                    window.location.href = '{:url("inventory/exportDetail")}?id=' + data.id;
                    break;
                case 'edit': // 编辑
                    window.location.href = '{:url("inventory/editInStock")}?id=' + data.id;
                    break;
                case 'delete': // 删除
                    layer.confirm('确定要删除该入库单吗？', function(index) {
                        $.ajax({
                            url: '{:url("inventory/delete")}',
                            type: 'post',
                            data: {id: data.id},
                            success: function(res) {
                                if (res.code === 0) {
                                    layer.msg(res.msg, {icon: 1});
                                    table.reload('data-table');
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                        layer.close(index);
                    });
                    break;
                case 'submit': // 提交
                    layer.confirm('确定要提交该入库单吗？提交后不能修改', function(index) {
                        $.ajax({
                            url: '{:url("inventory/submit")}',
                            type: 'post',
                            data: {id: data.id},
                            success: function(res) {
                                if (res.code === 0) {
                                    layer.msg(res.msg, {icon: 1});
                                    table.reload('data-table');
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                        layer.close(index);
                    });
                    break;
                case 'complete': // 审核并完成
                    layer.confirm('确定要审核并完成该入库单吗？操作后库存将发生变动', function(index) {
                        $.ajax({
                            url: '{:url("inventory/complete")}',
                            type: 'post',
                            data: {id: data.id},
                            success: function(res) {
                                if (res.code === 0) {
                                    layer.msg(res.msg, {icon: 1});
                                    table.reload('data-table');
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                        layer.close(index);
                    });
                    break;
                case 'cancel': // 取消
                    layer.confirm('确定要取消该入库单吗？', function(index) {
                        $.ajax({
                            url: '{:url("inventory/cancel")}',
                            type: 'post',
                            data: {id: data.id},
                            success: function(res) {
                                if (res.code === 0) {
                                    layer.msg(res.msg, {icon: 1});
                                    table.reload('data-table');
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                        layer.close(index);
                    });
                    break;
            }
        });
    });
</script>
