{include file="public/iframeheader"/}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">重量库存变动日志</div>
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">商品名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="product_name" id="product_name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">操作类型</label>
                        <div class="layui-input-inline">
                            <select name="operation_type" id="operation_type">
                                <option value="">全部</option>
                                <option value="1">入库</option>
                                <option value="2">出库</option>
                                <option value="3">销售</option>
                                <option value="4">退货</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">时间范围</label>
                        <div class="layui-input-inline">
                            <input type="text" name="date_range" id="date_range" placeholder="选择日期范围" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="search-btn">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button class="layui-btn layui-btn-primary" id="reset-btn">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table id="data-table" lay-filter="data-table"></table>

            <!-- 操作类型模板 -->
            <script type="text/html" id="operation-type-tpl">
                {{# if(d.operation_type == 1){ }}
                <span class="layui-badge layui-bg-green">入库</span>
                {{# } else if(d.operation_type == 2){ }}
                <span class="layui-badge layui-bg-red">出库</span>
                {{# } else if(d.operation_type == 3){ }}
                <span class="layui-badge layui-bg-orange">销售</span>
                {{# } else if(d.operation_type == 4){ }}
                <span class="layui-badge layui-bg-blue">退货</span>
                {{# } }}
            </script>

            <!-- 重量变动模板 -->
            <script type="text/html" id="weight-change-tpl">
                {{# if(d.change_weight > 0){ }}
                <span style="color: green; font-weight: bold;">+{{d.change_weight}}kg</span>
                {{# } else { }}
                <span style="color: red; font-weight: bold;">{{d.change_weight}}kg</span>
                {{# } }}
            </script>
        </div>
    </div>
</div>

<script>
    layui.use(['table', 'form', 'laydate'], function() {
        var table = layui.table,
            form = layui.form,
            laydate = layui.laydate,
            $ = layui.jquery;

        // 日期范围选择器
        laydate.render({
            elem: '#date_range',
            type: 'date',
            range: true
        });

        // 表格实例
        var tableIns = table.render({
            elem: '#data-table',
            url: '{:url("WeightInventory/ajax_weight_stock_logs")}',
            defaultToolbar: ['filter', 'exports', 'print'],
            page: true,
            limit: 20,
            limits: [20, 50, 100, 200],
            cols: [[
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'shop_name', title: '门店', width: 120},
                {field: 'product_name', title: '商品名称', width: 200},
                {field: 'inventory_name', title: '规格名称', width: 150},
                {field: 'operation_type', title: '操作类型', templet: '#operation-type-tpl', width: 100},
                {field: 'change_weight', title: '重量变动', templet: '#weight-change-tpl', width: 120},
                {field: 'before_weight', title: '变动前重量', width: 120, templet: function(d) {
                    return d.before_weight + 'kg';
                }},
                {field: 'after_weight', title: '变动后重量', width: 120, templet: function(d) {
                    return d.after_weight + 'kg';
                }},
                {field: 'operator_name', title: '操作人', width: 100},
                {field: 'remark', title: '备注', width: 200},
                {field: 'add_time', title: '操作时间', sort: true, width: 160}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.count,
                    "data": res.data
                };
            }
        });

        // 搜索按钮点击事件
        $('#search-btn').on('click', function() {
            var param = {
                product_name: $('#product_name').val(),
                operation_type: $('#operation_type').val(),
                date_range: $('#date_range').val()
            };
            tableIns.reload({
                where: param,
                page: {
                    curr: 1
                }
            });
        });

        // 重置按钮点击事件
        $('#reset-btn').on('click', function() {
            $('#product_name').val('');
            $('#operation_type').val('');
            $('#date_range').val('');
            form.render('select');
            $('#search-btn').click();
        });
    });
</script> 