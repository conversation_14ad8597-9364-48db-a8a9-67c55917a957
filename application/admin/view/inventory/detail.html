
{include file="public/iframeheader"/}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">库存单详情</div>
        <div class="layui-card-body">
            <div class="layui-form" lay-filter="detail-form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">单据编号</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.order_no}" disabled class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">单据类型</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.order_type_text}" disabled class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">业务类型</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.business_type_text}" disabled class="layui-input">
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属门店</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.title}" disabled class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.status_text}" disabled class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">总金额</label>
                        <div class="layui-input-inline">
                            <input type="text" value="￥{$data.order.total_amount}" disabled class="layui-input">
                        </div>
                    </div>
                </div>
                
                {if $data.order.order_type == 1}
                <!-- 入库单特有字段 -->
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">供应商</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.supplier_name}" disabled class="layui-input">
                        </div>
                    </div>
                    <!-- <div class="layui-inline">
                        <label class="layui-form-label">关联单号</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.related_order_no}" disabled class="layui-input">
                        </div>
                    </div> -->
                </div>
                {/if}
                
                {if $data.order.order_type == 3}
                <!-- 销售出库单特有字段 -->
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">会员</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.member_name} (ID: {$data.order.member_id})" disabled class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关联单号</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.related_order_no}" disabled class="layui-input">
                        </div>
                    </div>
                </div>
                {/if}
                
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">创建人</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.created_by}" disabled class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">创建时间</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.created_at}" disabled class="layui-input">
                        </div>
                    </div>
                </div>
                
                {if $data.order.status >= 2}
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">审核人</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.reviewed_by}" disabled class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">审核时间</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.reviewed_time}" disabled class="layui-input">
                        </div>
                    </div>
                </div>
                {/if}
                
                {if $data.order.status == 3}
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">完成时间</label>
                        <div class="layui-input-inline">
                            <input type="text" value="{$data.order.completed_time}" disabled class="layui-input">
                        </div>
                    </div>
                </div>
                {/if}
                
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea disabled class="layui-textarea">{$data.order.remark}</textarea>
                    </div>
                </div>
                
                <fieldset class="layui-elem-field">
                    <legend>商品明细</legend>
                    <div class="layui-field-box">
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>商品名称</th>
                                    <th>规格</th>
                                    <th>数量</th>
                                    <th>单价</th>
                                    {if $data.order.order_type == 3}
                                    <th>折扣</th>
                                    {/if}
                                    <th>金额</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody>
                                {foreach $data.details as $detail}
                                <tr>
                                    <td>{$detail.product_name}</td>
                                    <td>{$detail.spec_info}</td>
                                    <td>{$detail.quantity}</td>
                                    <td>￥{$detail.price}</td>
                                    {if $data.order.order_type == 3}
                                    <td>￥{$detail.discount}</td>
                                    {/if}
                                    <td>￥{$detail.amount}</td>
                                    <td>{$detail.remark}</td>
                                </tr>
                                {/foreach}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="{if $data.order.order_type == 3}5{else}4{/if}" style="text-align: right;">合计：</td>
                                    <td>￥{$data.order.total_amount}</td>
                                    <td></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </fieldset>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        {if $data.order.status == 0}
                        <button type="button" class="layui-btn layui-btn-normal" onclick="submitOrder({$data.order.id})">提交</button>
                        <button type="button" class="layui-btn" onclick="editOrder({$data.order.id}, {$data.order.order_type})">编辑</button>
                        <button type="button" class="layui-btn layui-btn-danger" onclick="deleteOrder({$data.order.id})">删除</button>
                        {/if}
                        
                        {if $data.order.status == 1}
                        <button type="button" class="layui-btn layui-btn-normal" onclick="completeOrder({$data.order.id})">审核并完成</button>
                        <button type="button" class="layui-btn layui-btn-danger" onclick="cancelOrder({$data.order.id})">取消</button>
                        {/if}
                        
                        <button type="button" class="layui-btn layui-btn-primary" onclick="goBack()">返回</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['form', 'layer'], function() {
        var form = layui.form,
            layer = layui.layer,
            $ = layui.jquery;
        
        // 提交订单
        window.submitOrder = function(id) {
            layer.confirm('确定要提交该库存单吗？提交后不能修改', function(index) {
                $.ajax({
                    url: '{:url("inventory/submit")}',
                    type: 'post',
                    data: {id: id},
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function() {
                                window.location.reload();
                            }, 1500);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        };
        
        // 编辑订单
        window.editOrder = function(id, orderType) {
            var url = '';
            if (orderType == 1) {
                url = '{:url("inventory/editInStock")}?id=' + id;
            } else {
                url = '{:url("inventory/editOutStock")}?id=' + id;
            }
            window.location.href = url;
        };
        
        // 删除订单
        window.deleteOrder = function(id) {
            layer.confirm('确定要删除该库存单吗？', function(index) {
                $.ajax({
                    url: '{:url("inventory/delete")}',
                    type: 'post',
                    data: {id: id},
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function() {
                                goBack();
                            }, 1500);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        };
        
        // 审核并完成
        window.completeOrder = function(id) {
            layer.confirm('确定要审核并完成该库存单吗？操作后库存将发生变动', function(index) {
                $.ajax({
                    url: '{:url("inventory/complete")}',
                    type: 'post',
                    data: {id: id},
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function() {
                                window.location.reload();
                            }, 1500);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        };
        
        // 取消订单
        window.cancelOrder = function(id) {
            layer.confirm('确定要取消该库存单吗？', function(index) {
                $.ajax({
                    url: '{:url("inventory/cancel")}',
                    type: 'post',
                    data: {id: id},
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function() {
                                window.location.reload();
                            }, 1500);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        };
        
        // 返回上一页
        window.goBack = function() {
            var orderType = {$data.order.order_type};
            if (orderType == 1) {
                window.location.href = '{:url("inventory/index")}';
            } else {
                window.location.href = '{:url("inventory/outstock")}';
            }
        };
    });
</script>
