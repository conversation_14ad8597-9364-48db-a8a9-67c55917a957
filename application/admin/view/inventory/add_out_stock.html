{include file="public/iframeheader"/}

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">新增出库单</div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="form-add" action="">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">单据类型<span style="color: red;font-size: 16px;">*</span></label>
                        <div class="layui-input-inline">
                            <select name="order_type" lay-verify="required" lay-filter="order_type">
                                <option value="2" selected>出库单</option>
                                <option value="3">销售出库单</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item" id="business-type-container">
                    <div class="layui-inline">
                        <label class="layui-form-label">业务类型<span style="color: red;font-size: 16px;">*</span></label>
                        <div class="layui-input-inline">
                            <select name="business_type" lay-verify="required">
                                <option value="1">销售出库</option>
                                <option value="2">调拨出库</option>
                                <option value="3">报损出库</option>
                                <option value="4">其他出库</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">相关单位<span style="color: red;font-size: 16px;">*</span></label>
                        <div class="layui-input-inline">
                            <input type="text" name="supplier_name" lay-verify="required" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item" id="member-container" style="display:none;">
                    <div class="layui-inline">
                        <label class="layui-form-label">会员ID<span style="color: red;font-size: 16px;">*</span></label>
                        <div class="layui-input-inline">
                            <input type="text" name="member_id" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">所属门店<span style="color: red;font-size: 16px;">*</span></label>
                        <div class="layui-input-inline">
                            <select name="shop_id" lay-verify="required" lay-filter="shop_id">
                                <!-- <option value="0">平台</option> -->
                                {foreach $shops as $shop}
                                <option value="{$shop.id}">{$shop.title}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            商品明细<span style="color: red;font-size: 16px;">*</span>
                            <div class="layui-btn-group">
                                <button type="button" class="layui-btn layui-btn-sm" id="add-detail-btn">
                                    <i class="layui-icon">&#xe654;</i>添加一行
                                </button>
                            </div>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table" id="detail-table">
                                <thead>
                                    <tr>
                                        <th width="25%">商品</th>
                                        <th width="20%">规格</th>
                                        <th width="10%">数量</th>
                                        <th width="10%">单价</th>
                                        <th width="10%">折扣</th>
                                        <th width="10%">金额</th>
                                        <th width="15%">备注</th>
                                        <th width="10%">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 明细行将动态添加到这里 -->
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="5" style="text-align: right;">合计：</td>
                                        <td width="10%"><span id="total-amount">0.00</span></td>
                                        <td width="15%"></td>
                                        <td width="10%"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn" lay-submit lay-filter="save-btn">保存为草稿</button>
                        <button type="button" class="layui-btn layui-btn-normal" lay-submit lay-filter="submit-btn">保存并提交</button>
                        <button type="button" class="layui-btn layui-btn-primary" id="cancel-btn">取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 选择商品弹窗模板 -->
<script type="text/html" id="select-product-tpl">
    <div class="layui-form" lay-filter="form-select-product" style="padding: 20px;">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">商品名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="keyword" id="product-keyword" autocomplete="off" class="layui-input">
                </div>
                <button type="button" class="layui-btn" id="search-product-btn">搜索</button>
            </div>
        </div>
        
        <table id="product-table" lay-filter="product-table"></table>
    </div>
</script>

<script src="/static/admin/lay-module/xm-select.js"></script>

<script>
    layui.use(['form', 'table', 'layer', 'laytpl'], function() {
        var form = layui.form,
            table = layui.table,
            layer = layui.layer,
            laytpl = layui.laytpl,
            $ = layui.jquery;
        
        // 监听单据类型变化
        form.on('select(order_type)', function(data) {
            var orderType = data.value;
            if (orderType == 3) { // 销售出库单
                $('#business-type-container').hide();
                $('#member-container').show();
            } else { // 普通出库单
                $('#business-type-container').show();
                $('#member-container').hide();
            }
        });
        
        // 监听店铺选择变化，清空商品列表
        form.on('select(shop_id)', function(data) {
            // 清空商品明细表格
            $('#detail-table tbody').empty();
            // 重新计算总金额
            calculateTotalAmount();
            layer.msg('店铺已变更，请重新添加商品', {icon: 1});
        });
        
        // 表单提交处理
        form.on('submit(save-btn)', function(data) {
            saveOrder(data.field, 0);
            return false;
        });
        
        form.on('submit(submit-btn)', function(data) {
            saveOrder(data.field, 1);
            return false;
        });
        
        // 保存订单
        function saveOrder(formData, submitFlag) {
            // 销售出库单默认业务类型为销售出库
            if (formData.order_type == 3) {
                formData.business_type = 1;
            }
            
            // 获取明细
            var details = [];
            $('#detail-table tbody tr').each(function() {
                var $tr = $(this);
                var productId = $tr.data('product-id');
                if (!productId) {
                    layer.msg('请选择商品', {icon: 2});
                    return false;
                }
                
                details.push({
                    product_id: productId,
                    inventory_id: $tr.data('inventory-id') || 0,
                    quantity: $tr.find('.detail-quantity').val(),
                    price: $tr.find('.detail-price').val(),
                    discount: $tr.find('.detail-discount').val() || 0,
                    remark: $tr.find('.detail-remark').val()
                });
            });
            
            if (details.length === 0) {
                layer.msg('请至少添加一个商品明细', {icon: 2});
                return;
            }
            
            formData.details = details;
            
            // 发送请求
            $.ajax({
                url: '{:url("inventory/addOutStock")}',
                type: 'post',
                data: formData,
                success: function(res) {
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        if (submitFlag === 1) {
                            // 提交订单
                            $.ajax({
                                url: '{:url("inventory/submit")}',
                                type: 'post',
                                data: {id: res.id},
                                success: function(submitRes) {
                                    if (submitRes.code === 0) {
                                        layer.msg(submitRes.msg, {icon: 1});
                                        setTimeout(function() {
                                            window.location.href = '{:url("inventory/outstock")}';
                                        }, 1500);
                                    } else {
                                        layer.msg(submitRes.msg, {icon: 2});
                                    }
                                }
                            });
                        } else {
                            setTimeout(function() {
                                window.location.href = '{:url("inventory/outstock")}';
                            }, 1500);
                        }
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }
            });
        }
        
        // 添加明细按钮点击事件
        $('#add-detail-btn').on('click', function() {
            var index = $('#detail-table tbody tr').length;
            var html = '<tr>' +
                '<td width="25%">' +
                '<div id="product-select-' + index + '" class="xm-select-demo"></div>' +
                '</td>' +
                '<td width="20%" class="product-spec"></td>' +
                '<td width="10%"><input type="number" class="layui-input detail-quantity" value="1" min="1" style="width: 100%;" onchange="calculateRowAmount(this)"></td>' +
                '<td width="10%"><input type="number" class="layui-input detail-price" value="0" min="0" step="0.01" style="width: 100%;" onchange="calculateRowAmount(this)"></td>' +
                '<td width="10%">';
            
            var orderType = $('select[name="order_type"]').val();
            if (orderType == 3) { // 销售出库单，显示折扣字段
                html += '<input type="number" class="layui-input detail-discount" value="0" min="0" step="0.01" style="width: 100%;" onchange="calculateRowAmount(this)">';
            } else { // 普通出库单，隐藏折扣字段
                html += '<input type="hidden" class="detail-discount" value="0">';
            }
            
            html += '</td>' +
                '<td width="10%" class="detail-amount">0.00</td>' +
                '<td width="15%"><input type="text" class="layui-input detail-remark" style="width: 100%;"></td>' +
                '<td width="10%"><button type="button" class="layui-btn layui-btn-danger layui-btn-xs delete-row-btn"><i class="layui-icon layui-icon-delete"></i></button></td>' +
                '</tr>';
            
            $('#detail-table tbody').append(html);
            
            // 初始化xm-select
            var xmSelect = window.xmSelect.render({
                el: '#product-select-' + index,
                radio: true,
                filterable: true,
                filterMethod: function(val, item, index, prop){
                    if(val == item.value){ //把value相同的搜索出来
                        return true;
                    }
                    if(item.name.indexOf(val) != -1){ //名称中包含的搜索出来
                        return true;
                    }
                    return false; //不知道的就不管了
                },
                data: [],
                remoteSearch: true,
                remoteMethod: function(val, cb, show){
                    // 远程搜索方法
                    // 获取现在选中的店铺 
                    var shopId = $('select[name="shop_id"]').val();
                    $.ajax({
                        url: '{:url("product/getList")}',
                        type: 'get',
                        data: {
                            keyword: val,
                            limit: 20,
                            shop_id: shopId,
                            has_stock: 1 // 只显示有库存的商品
                        },
                        dataType: 'json',
                        success: function(res) {
                            if (res.code == 0) {
                                var options = [];
                                $.each(res.data, function(i, item) {
                                    options.push({
                                        name: item.name,
                                        value: item.inventory_id,
                                        selected: false,
                                        spec: item.spec_info || '',
                                        price: item.price || 0,
                                        stock_quantity: item.stock_quantity || 0,
                                        inventory_id: item.inventory_id || 0,
                                        product_id: item.id || 0,
                                    });
                                });
                                cb(options);
                            } else {
                                cb([]);
                            }
                        },
                        error: function(){
                            cb([]);
                        }
                    });
                },
                template: function(data){
                    console.log(data);
                    var stockInfo = '';
                    if (data.item.stock_quantity > 0) {
                        stockInfo = ' [库存: ' + data.item.stock_quantity + ']';
                    } else {
                        stockInfo = ' [无库存]';
                    }
                    return data.item.name + (data.item.spec ? ' (' + data.item.spec + ')' : '') + stockInfo;
                },
                on: function(data){
                    if(data.isAdd && data.change.length){
                        // 选择了商品
                        var $tr = $('#product-select-' + index).closest('tr');
                        var selectedItem = data.change[0];
                        $tr.data('product-id', selectedItem.product_id);
                        $tr.data('inventory-id', selectedItem.inventory_id);
                        $tr.find('.product-spec').text(selectedItem.spec || '');
                        $tr.find('.detail-price').val(selectedItem.price || 0);
                        calculateRowAmount($tr.find('.detail-quantity')[0]);
                    }
                }
            });
            
            // 绑定删除按钮点击事件
            $('.delete-row-btn').off('click').on('click', function() {
                $(this).closest('tr').remove();
                calculateTotalAmount();
            });
        });
        
        // 计算行金额
        window.calculateRowAmount = function(input) {
            var $tr = $(input).closest('tr');
            var quantity = parseFloat($tr.find('.detail-quantity').val()) || 0;
            var price = parseFloat($tr.find('.detail-price').val()) || 0;
            var discount = parseFloat($tr.find('.detail-discount').val()) || 0;
            var amount = (quantity * price - discount).toFixed(2);
            $tr.find('.detail-amount').text(amount);
            calculateTotalAmount();
        };
        
        // 计算总金额
        function calculateTotalAmount() {
            var total = 0;
            $('#detail-table tbody tr').each(function() {
                var amount = parseFloat($(this).find('.detail-amount').text()) || 0;
                total += amount;
            });
            $('#total-amount').text(total.toFixed(2));
        }
        
        // 取消按钮点击事件
        $('#cancel-btn').on('click', function() {
            window.location.href = '{:url("inventory/outstock")}';
        });
    });
</script>
