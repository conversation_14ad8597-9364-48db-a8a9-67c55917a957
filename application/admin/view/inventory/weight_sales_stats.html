{include file="public/iframeheader"/}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">计量商品销售统计</div>
        <div class="layui-card-body">
            <!-- 统计概览 -->
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header">今日销售</div>
                        <div class="layui-card-body" style="text-align: center;">
                            <div style="font-size: 24px; color: #1890ff; font-weight: bold;" id="today-sales">0</div>
                            <div style="color: #666;">总重量(kg)</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header">今日金额</div>
                        <div class="layui-card-body" style="text-align: center;">
                            <div style="font-size: 24px; color: #52c41a; font-weight: bold;" id="today-amount">￥0</div>
                            <div style="color: #666;">销售金额</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header">本月销售</div>
                        <div class="layui-card-body" style="text-align: center;">
                            <div style="font-size: 24px; color: #fa8c16; font-weight: bold;" id="month-sales">0</div>
                            <div style="color: #666;">总重量(kg)</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header">本月金额</div>
                        <div class="layui-card-body" style="text-align: center;">
                            <div style="font-size: 24px; color: #f5222d; font-weight: bold;" id="month-amount">￥0</div>
                            <div style="color: #666;">销售金额</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索区域 -->
            <div class="layui-form layui-form-pane" style="margin-top: 20px;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">商品名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="product_name" id="product_name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">门店</label>
                        <div class="layui-input-inline">
                            <select name="shop_id" id="shop_id">
                                <option value="">全部</option>
                                {volist name="shops" id="shop"}
                                <option value="{$shop.id}">{$shop.title}</option>
                                {/volist}
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">时间范围</label>
                        <div class="layui-input-inline">
                            <input type="text" name="date_range" id="date_range" placeholder="选择日期范围" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="search-btn">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button class="layui-btn layui-btn-primary" id="reset-btn">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table id="data-table" lay-filter="data-table"></table>

            <!-- 销售金额模板 -->
            <script type="text/html" id="sales-amount-tpl">
                <span style="color: #52c41a; font-weight: bold;">￥{{d.sales_amount}}</span>
            </script>

            <!-- 销售重量模板 -->
            <script type="text/html" id="sales-weight-tpl">
                <span style="color: #1890ff; font-weight: bold;">{{d.sales_weight}}kg</span>
            </script>

            <!-- 操作按钮模板 -->
            <script type="text/html" id="operation-tpl">
                <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="detail">查看详情</a>
            </script>
        </div>
    </div>
</div>

<script>
    layui.use(['table', 'form', 'laydate', 'layer'], function() {
        var table = layui.table,
            form = layui.form,
            laydate = layui.laydate,
            layer = layui.layer,
            $ = layui.jquery;

        // 日期范围选择器
        laydate.render({
            elem: '#date_range',
            type: 'date',
            range: true
        });

        // 加载统计概览数据
        function loadStatsOverview() {
            $.ajax({
                url: '{:url("WeightInventory/salesStatsOverview")}',
                type: 'GET',
                dataType: 'json',
                success: function(res) {
                    if (res.code === 0) {
                        $('#today-sales').text(res.data.today_weight || 0);
                        $('#today-amount').text('￥' + (res.data.today_amount || 0));
                        $('#month-sales').text(res.data.month_weight || 0);
                        $('#month-amount').text('￥' + (res.data.month_amount || 0));
                    }
                }
            });
        }

        // 初始加载统计概览
        loadStatsOverview();

        // 表格实例
        var tableIns = table.render({
            elem: '#data-table',
            url: '{:url("WeightInventory/ajax_sales_stats")}',
            defaultToolbar: ['filter', 'exports', 'print'],
            page: true,
            limit: 20,
            limits: [20, 50, 100, 200],
            cols: [[
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'shop_name', title: '门店', width: 120},
                {field: 'product_name', title: '商品名称', width: 200},
                {field: 'inventory_name', title: '规格名称', width: 150},
                {field: 'order_sn', title: '订单号', width: 180},
                {field: 'sales_weight', title: '销售重量', templet: '#sales-weight-tpl', width: 120},
                {field: 'unit_price', title: '单价(元/kg)', width: 120, templet: function(d) {
                    return '￥' + d.unit_price;
                }},
                {field: 'sales_amount', title: '销售金额', templet: '#sales-amount-tpl', width: 120},
                {field: 'customer_name', title: '客户', width: 100},
                {field: 'sales_time', title: '销售时间', sort: true, width: 160},
                {fixed: 'right', title: '操作', toolbar: '#operation-tpl', width: 100}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.count,
                    "data": res.data
                };
            }
        });

        // 监听工具条
        table.on('tool(data-table)', function(obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                // 查看订单详情
                layer.open({
                    type: 2,
                    title: '订单详情',
                    content: '{:url("order/order_detail")}?order_sn=' + data.order_sn,
                    area: ['90%', '80%']
                });
            }
        });

        // 搜索按钮点击事件
        $('#search-btn').on('click', function() {
            var param = {
                product_name: $('#product_name').val(),
                shop_id: $('#shop_id').val(),
                date_range: $('#date_range').val()
            };
            tableIns.reload({
                where: param,
                page: {
                    curr: 1
                }
            });
        });

        // 重置按钮点击事件
        $('#reset-btn').on('click', function() {
            $('#product_name').val('');
            $('#shop_id').val('');
            $('#date_range').val('');
            form.render('select');
            $('#search-btn').click();
        });
    });
</script> 