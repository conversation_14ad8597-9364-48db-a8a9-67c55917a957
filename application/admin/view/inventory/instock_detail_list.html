{include file="public/iframeheader"/}
<style>
    .layui-table-cell{
        height: inherit;
    }
    .date-range-container {
        display: flex;
        align-items: center;
    }
    .date-range-container .layui-form-label {
        width: auto;
        padding: 9px 5px;
    }
</style>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">入库单详情列表</div>
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">单据编号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="order_no" id="order_no" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">业务类型</label>
                        <div class="layui-input-inline">
                            <select name="business_type" id="business_type">
                                <option value="">全部</option>
                                <option value="1">采购入库</option>
                                <option value="2">调拨入库</option>
                                <option value="3">退货入库</option>
                                <option value="4">其他入库</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status" id="status">
                                <option value="">全部</option>
                                <option value="0">草稿</option>
                                <option value="1">已提交</option>
                                <option value="3">已完成</option>
                                <option value="-1">已取消</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">门店</label>
                        <div class="layui-input-inline">
                            <select name="shop_id" id="shop_id" lay-search>
                                <option value="">全部</option>
                                {foreach $shops as $shop}
                                <option value="{$shop.id}">{$shop.title}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">商品</label>
                        <div class="layui-input-inline">
                            <input type="text" name="product_name" id="product_name" autocomplete="off" class="layui-input" placeholder="搜索商品">
                            <input type="hidden" name="product_id" id="product_id">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">规格</label>
                        <div class="layui-input-inline">
                            <select name="inventory_id" id="inventory_id">
                                <option value="">全部</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">录入方式</label>
                        <div class="layui-input-inline">
                            <select name="is_manual" id="is_manual">
                                <option value="">全部</option>
                                <option value="1">手动录入</option>
                                <option value="0">系统生成</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline date-range-container">
                        <label class="layui-form-label">日期范围</label>
                        <div class="layui-input-inline">
                            <input type="text" name="date_range" id="date_range" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <button class="layui-btn" id="search-btn">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button class="layui-btn layui-btn-primary" id="reset-btn">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                        <button class="layui-btn layui-btn-normal" id="export-btn">
                            <i class="layui-icon layui-icon-export"></i> 导出
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table id="data-table" lay-filter="data-table"></table>
        </div>
    </div>
</div>

<script>
    // 配置 layui 模块路径
    layui.config({
        base: '__STATIC__/admin/js/'
    }).use(['table', 'form', 'laydate', 'autocomplete'], function() {
        var table = layui.table,
            form = layui.form,
            laydate = layui.laydate,
            autocomplete = layui.autocomplete,
            $ = layui.jquery;

        // 日期范围选择器
        laydate.render({
            elem: '#date_range',
            range: true
        });

        // 商品搜索自动完成
        autocomplete.render({
            elem: '#product_name',
            url: '{:url("inventory/searchProduct")}',
            cache: false,
            template_val: '{{d.name}}',
            template_txt: '{{d.name}} <span class="layui-badge layui-bg-gray">{{d.id}}</span>',
            onselect: function (resp) {
                $('#product_id').val(resp.id);
                // 加载该商品的规格
                loadInventoryOptions(resp.id);
            }
        });

        // 加载商品规格选项
        function loadInventoryOptions(productId) {
            if (!productId) {
                $('#inventory_id').html('<option value="">全部</option>');
                form.render('select');
                return;
            }
            
            $.ajax({
                url: '{:url("inventory/getInventoryByProduct")}',
                type: 'get',
                data: {product_id: productId},
                dataType: 'json',
                success: function(res) {
                    if (res.code === 0) {
                        var options = '<option value="">全部</option>';
                        $.each(res.data, function(index, item) {
                            options += '<option value="' + item.id + '">' + item.name + '</option>';
                        });
                        $('#inventory_id').html(options);
                        form.render('select');
                    } else {
                        layer.msg(res.msg || '获取规格失败');
                    }
                },
                error: function() {
                    layer.msg('网络错误，获取规格失败');
                }
            });
        }

        // 表格实例
        table.render({
            elem: '#data-table',
            url: '{:url("inventory/getInStockDetailList")}',
            toolbar: '#toolbar',
            defaultToolbar: ['filter', 'exports', 'print'],
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            cols: [[
                {type: 'numbers', title: '序号', width: 60},
                {field: 'order_no', title: '单据编号', width: 180},
                {field: 'shop_name', title: '门店名称', width: 120},
                {field: 'business_type_text', title: '业务类型', width: 120},
                {field: 'product_name', title: '商品名称', width: 200},
                {field: 'spec_info', title: '规格名称', width: 120},
                {field: 'product_type_text', title: '商品类型', width: 100, templet: function(d) {
                    if (d.product_type == 2) {
                        return '<span class="layui-badge layui-bg-orange">计量商品</span>';
                    } else {
                        return '<span class="layui-badge layui-bg-blue">普通商品</span>';
                    }
                }},
                {field: 'quantity_display', title: '数量/重量', width: 100},
                {field: 'price', title: '单价', width: 100, templet: function(d) {
                    return '￥' + d.price;
                }},
                {field: 'amount', title: '金额', width: 100, templet: function(d) {
                    return '￥' + d.amount;
                }},
                {field: 'status_text', title: '状态', width: 100, templet: function(d){
                    var statusClass = '';
                    switch(d.status) {
                        case 0: statusClass = ''; break;
                        case 1: statusClass = 'layui-bg-blue'; break;
                        case 2: statusClass = 'layui-bg-green'; break;
                        case 3: statusClass = 'layui-bg-green'; break;
                        case -1: statusClass = 'layui-bg-gray'; break;
                    }
                    return '<span class="layui-badge ' + statusClass + '">' + d.status_text + '</span>';
                }},
                {field: 'created_by', title: '创建人', width: 100},
                {field: 'created_at', title: '创建时间', width: 160},
                {field: 'detail', title: '操作', width: 80, templet: function(d) {
                    return '<a class="layui-btn layui-btn-xs" href="{:url("inventory/detail")}?id=' + d.order_id + '">查看</a>';
                }}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.count,
                    "data": res.data
                };
            }
        });

        // 搜索按钮点击事件
        $('#search-btn').on('click', function() {
            var param = {
                order_no: $('#order_no').val(),
                business_type: $('#business_type').val(),
                status: $('#status').val(),
                shop_id: $('#shop_id').val(),
                product_id: $('#product_id').val(),
                inventory_id: $('#inventory_id').val(),
                is_manual: $('#is_manual').val(),
                date_range: $('#date_range').val()
            };
            table.reload('data-table', {
                where: param,
                page: {
                    curr: 1
                }
            });
        });

        // 重置按钮点击事件
        $('#reset-btn').on('click', function() {
            $('#order_no').val('');
            $('#business_type').val('');
            $('#status').val('');
            $('#shop_id').val('');
            $('#product_name').val('');
            $('#product_id').val('');
            $('#inventory_id').html('<option value="">全部</option>');
            $('#is_manual').val('');
            $('#date_range').val('');
            form.render('select');
            $('#search-btn').click();
        });

        // 导出按钮点击事件
        $('#export-btn').on('click', function() {
            var param = {
                order_no: $('#order_no').val(),
                business_type: $('#business_type').val(),
                status: $('#status').val(),
                shop_id: $('#shop_id').val(),
                product_id: $('#product_id').val(),
                inventory_id: $('#inventory_id').val(),
                is_manual: $('#is_manual').val(),
                date_range: $('#date_range').val()
            };
            
            // 构建导出URL
            var exportUrl = '{:url("inventory/exportInStockDetailList")}?' + 
                'order_no=' + encodeURIComponent(param.order_no) + 
                '&business_type=' + encodeURIComponent(param.business_type) + 
                '&status=' + encodeURIComponent(param.status) + 
                '&shop_id=' + encodeURIComponent(param.shop_id) + 
                '&product_id=' + encodeURIComponent(param.product_id) + 
                '&inventory_id=' + encodeURIComponent(param.inventory_id) + 
                '&is_manual=' + encodeURIComponent(param.is_manual) + 
                '&date_range=' + encodeURIComponent(param.date_range);
            
            // 跳转到导出页面
            window.location.href = exportUrl;
        });
    });
</script> 