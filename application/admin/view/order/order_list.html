<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">订单管理</div>
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">查询条件</label>
                        <div class="layui-input-inline">
                            <select name="field">
                                <option value='order_no'>订单号</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <input class="layui-input" name="keyword" id="demoReload" autocomplete="off">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">门店</label>
                        <div class="layui-input-inline">
                            <select name="shop_id" lay-search>
                                <option value='0'>全部</option>
                                {foreach $shop_enum as $k => $val}
                                <option value="{$val.id}">{$val.title}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">订单类型</label>
                        <div class="layui-input-inline">
                            <select name="type">
                                <option value='0'>全部</option>
                                <option value='1'>自提订单</option>
                                <option value='2'>配送订单</option>
                                <option value='3'>结算台订单</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">日期范围</label>
                        <div class="layui-input-inline">
                            <input type="text" name="date_range" id="date_range" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="search">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button class="layui-btn layui-btn-primary" id="resetBtn">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                        <button class="layui-btn layui-btn-normal" id="exportBtn">
                            <i class="layui-icon layui-icon-export"></i> 导出
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="layui-row layui-col-space15" style="margin-top: 15px;">
                <!-- 第一行统计卡片 -->
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header" style="text-align: center;">订单数量</div>
                        <div class="layui-card-body">
                            <p class="layui-text-center" style="font-size: 24px; text-align: center;" id="order_count">0</p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header" style="text-align: center;">商品总数量</div>
                        <div class="layui-card-body">
                            <p class="layui-text-center" style="font-size: 24px; text-align: center;" id="total_product_total">0</p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header" style="text-align: center;">运费总额</div>
                        <div class="layui-card-body">
                            <p class="layui-text-center" style="font-size: 24px; text-align: center;" id="total_shipping_price">¥0.00</p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header" style="text-align: center;">折扣金额总数</div>
                        <div class="layui-card-body">
                            <p class="layui-text-center" style="font-size: 24px; text-align: center;" id="total_discount_amount">¥0.00</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 第二行统计卡片 -->
            <div class="layui-row layui-col-space15" style="margin-top: 15px;">
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header" style="text-align: center;">储值卡支付总额</div>
                        <div class="layui-card-body">
                            <p class="layui-text-center" style="font-size: 24px; text-align: center;" id="total_card_price">¥0.00</p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header" style="text-align: center;">在线支付总额</div>
                        <div class="layui-card-body">
                            <p class="layui-text-center" style="font-size: 24px; text-align: center;" id="total_real_price">¥0.00</p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header" style="text-align: center;">线下支付总额</div>
                        <div class="layui-card-body">
                            <p class="layui-text-center" style="font-size: 24px; text-align: center;" id="total_offline_price">¥0.00</p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header" style="text-align: center;">订单总金额</div>
                        <div class="layui-card-body">
                            <p class="layui-text-center" style="font-size: 24px; text-align: center;" id="total_price">¥0.00</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="layui-row layui-col-space15" style="margin-top: 15px;">
                <table id="demo" lay-filter="demo"></table>
            </div>

            <!-- 行工具栏模板 -->
            <script type="text/html" id="barDemo">
                <div class="layui-btn-group">
                    <a class="layui-btn layui-btn-xs" lay-event="logs">订单日志</a>
                </div>
            </script>
        </div>
    </div>
</div>

<script type="text/html" id="imgtmp">
    <img src="{{d.cover}}" style="width:108px; height:100px;">
</script>

<script type="text/javascript">
    layui.config({
        base: "__STATIC__/admin/modules/"
    }).extend({
        tableChild: 'tableChild'
    }).use(['table', 'tableChild', 'laydate'], function () {
        var table = layui.table;
        var laydate = layui.laydate;
        var $ = layui.jquery;
        var tableChild = layui.tableChild;
        
        // 日期范围选择器
        laydate.render({
            elem: '#date_range',
            range: true
        });
        
        // 获取URL参数
        var member_id = "{$member_id}";
        
        // 初始化加载统计数据
        loadOrderStats();

        // 表格实例
        var tableIns = table.render({
            elem: '#demo'
            , url: "{:url('ajax_order_list')}?member_id=" + member_id
            , cellMinWidth: 80
            , lineStyle: 'height' // 设置行高自适应
            , cols: [[
                {type: 'checkbox'},
                {title: '#', width: 50, children:[
                    {
                        title: '订单详情',
                        url: "{:url('ajax_order_detail_list')}",
                        where: function(d){
                            return {
                                order_no: d.order_no
                            }
                        },
                        height: 300,
                        cols: [[
                            {type: 'checkbox', fixed: 'left'},
                            {field: 'product_title', title: '商品名称'},
                            {field: 'inventory_title', title: '规格名称'},
                            {field: 'display_info.type', title: '商品类型', templet: function(d){
                                if(d.display_info.type === '计量商品') {
                                    return '<span class="layui-badge layui-bg-orange">计量商品</span>';
                                } else {
                                    return '<span class="layui-badge layui-bg-blue">普通商品</span>';
                                }
                            }},
                            {title: '数量/重量', templet: function(d){
                                if(d.display_info.type === '计量商品') {
                                    return d.display_info.weight + d.inventory_json.weight_unit;
                                } else {
                                    return d.display_info.quantity;
                                }
                            }},
                            {title: '单价', templet: function(d){
                                if(d.display_info.type === '计量商品') {
                                    return '￥' + d.display_info.unit_price + '/' + d.inventory_json.weight_unit;
                                } else {
                                    return '￥' + d.display_info.unit_price;
                                }
                            }},
                            {title: '小计', templet: function(d){
                                return '￥' + d.display_info.total_price;
                            }}
                        ]],done: function () {
                            tableChild.render(this);
                        }
                    }
                ]},
                {type: 'numbers', title: '序号'},
                {field: 'order_no', title: '订单号', width: 200},
                {field: 'shop_name', title: '门店名称', width: 150},
                {field: 'username', title: '用户名称', width: 150},
                {field: 'type_text1', title: '类型', width: 100},
                {field: 'status_text1', title: '状态', width: 100, templet: function(d){
                    var statusClass = '';
                    switch(d.status) {
                        case 0: statusClass = ''; break;
                        case 1: statusClass = 'layui-bg-blue'; break;
                        case 2: statusClass = 'layui-bg-green'; break;
                        case 3: statusClass = 'layui-bg-green'; break;
                        case -1: statusClass = 'layui-bg-gray'; break;
                    }
                    return '<span class="layui-badge ' + statusClass + '">' + d.status_text1 + '</span>';
                }},
                {field: 'pay_type_text', title: '支付方式', width: 100},
                {field: 'product_info', title: '商品信息', templet: function(d) {
                    return '<div style="line-height: 1.5; word-break: break-all; white-space: pre-wrap;">' + d.product_info.replace("\n\r", "<br>") + '</div>'
                    }, width: 350},
                {field: 'product_total', title: '商品数量', width: 100},
                {field: 'product_price', title: '商品价格', width: 100},
                
                {field: 'remark', title: '备注'},
                {field: 'shipping_price', title: '运费', width: 80},
                {field: 'discount_amount', title: '优惠金额', width: 100},
                {field: 'card_price', title: '储值卡支付', width: 100},
                {field: 'real_price', title: '在线支付', width: 100},
                {field: 'offline_price', title: '线下支付', width: 100},
                {field: 'price', title: '总金额', width: 100},
                {field: 'add_time', title: '下单时间', width: 180},
                {fixed: 'right', title: "操作", toolbar: '#barDemo', width: 120}
            ]]
            ,done: function (res, curr, count) {
                // 自适应商品信息列高度
                $(".layui-table-cell").css("height", "auto");
                $(".layui-table-cell").css("white-space", "normal");
                
                // 调用通用的行高调整函数
                adjustTableRowHeight();
                
                // 加载统计数据
                loadOrderStats();
                // 绑定soul-table功能
                tableChild.render(this);
                
                // 表格子表格展开/折叠时也需要调整行高
                $(document).off('click', '.layui-tableChild-arrow').on('click', '.layui-tableChild-arrow', function(){
                    setTimeout(function(){
                        adjustTableRowHeight();
                    }, 100);
                });
            }
            , page: true
            // , limit: 15
            // , limits: [15, 30, 50, 100]
           
        });
        


        // 监听工具条事件
        layui.table.on('tool(demo)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            var id = data.id;
            
            if (layEvent === 'logs') {
                layer.open({
                    type: 2,
                    title: "订单日志",
                    content: "{:url('order_logs')}?id=" + id,
                    maxmin: true,
                    area: ["98%", "98%"],
                });
            }
        });

        // 搜索按钮点击事件
        $('#search').click(function () {
            var keyword = $("[name=keyword]").val();
            var field = $("[name=field]").val();
            var status = $("[name=status]").val();
            var type1 = $("[name=type]").val();
            var shop_id = $("[name=shop_id]").val();
            var date_range = $("#date_range").val();
            var dates = date_range.split(' - ');
            var start_date = dates[0] || '';
            var end_date = dates[1] || '';
            
            // 执行重载
            table.reload('demo', {
                page: {
                    curr: 1 // 重新从第 1 页开始
                }
                , where: {
                    keyword: keyword,
                    field: field,
                    status: status,
                    type1: type1,
                    shop_id: shop_id,
                    start_date: start_date,
                    end_date: end_date
                }
            });
            
            // 重新加载统计数据
            loadOrderStats();
        });
        
        // 重置按钮点击事件
        $('#resetBtn').on('click', function() {
            $("[name=keyword]").val('');
            $("[name=field]").val('order_no');
            $("[name=status]").val('');
            $("[name=type]").val('0');
            $("[name=shop_id]").val('0');
            $("#date_range").val('');
            layui.form.render('select');
            $('#search').click();
        });
        
        // 加载订单统计数据
        function loadOrderStats() {
            var keyword = $("[name=keyword]").val();
            var field = $("[name=field]").val();
            var status = $("[name=status]").val();
            var type1 = $("[name=type]").val();
            var shop_id = $("[name=shop_id]").val();
            var date_range = $("#date_range").val();
            var dates = date_range.split(' - ');
            var start_date = dates[0] || '';
            var end_date = dates[1] || '';
            var member_id = "{$member_id}";
            
            $.ajax({
                url: "{:url('ajax_order_stats')}",
                type: 'post',
                data: {
                    keyword: keyword,
                    field: field,
                    status: status,
                    type1: type1,
                    shop_id: shop_id,
                    member_id: member_id,
                    start_date: start_date,
                    end_date: end_date
                },
                success: function(res) {
                    if (res.code == 0 && res.data) {
                        // 更新统计卡片数据
                        $('#order_count').text(res.data.order_count || 0);
                        $('#total_shipping_price').text('¥' + (res.data.total_shipping_price || 0).toFixed(2));
                        $('#total_discount_amount').text('¥' + (res.data.total_discount_amount || 0).toFixed(2));
                        $('#total_card_price').text('¥' + (res.data.total_card_price || 0).toFixed(2));
                        $('#total_real_price').text('¥' + (res.data.total_real_price || 0).toFixed(2));
                        $('#total_offline_price').text('¥' + (res.data.total_offline_price || 0).toFixed(2));
                        $('#total_price').text('¥' + (res.data.total_price || 0).toFixed(2));
                        $('#total_product_total').text(res.data.total_product_total || 0);
                    }
                },
                dataType: 'json'
            });
        }

        // 添加窗口大小改变时的表格高度调整
        $(window).resize(function() {
            adjustTableRowHeight();
        });
        
        // 抽取行高调整函数以便重用
        function adjustTableRowHeight() {
            setTimeout(function() {
                $(".layui-table-main tbody tr").each(function (index, val) {
                    var rowHeight = $(val).height();
                    // 同时调整左侧和右侧固定列的高度
                    $(".layui-table-fixed-l .layui-table-body tbody tr:eq("+index+")").height(rowHeight);
                    $(".layui-table-fixed-r .layui-table-body tbody tr:eq("+index+")").height(rowHeight);
                });
            }, 50);
        }
        
        // 导出按钮点击事件
        $('#exportBtn').click(function() {
            var keyword = $("[name=keyword]").val();
            var field = $("[name=field]").val();
            var status = $("[name=status]").val();
            var type1 = $("[name=type]").val();
            var shop_id = $("[name=shop_id]").val();
            var date_range = $("#date_range").val();
            var dates = date_range.split(' - ');
            var start_date = dates[0] || '';
            var end_date = dates[1] || '';
            var member_id = "{$member_id}";
            
            // 构建导出URL
            var exportUrl = "{:url('export_order_detail')}" + 
                "?keyword=" + encodeURIComponent(keyword) + 
                "&field=" + encodeURIComponent(field) + 
                "&status=" + encodeURIComponent(status) + 
                "&type1=" + encodeURIComponent(type1) + 
                "&shop_id=" + encodeURIComponent(shop_id) +
                "&member_id=" + encodeURIComponent(member_id) +
                "&start_date=" + encodeURIComponent(start_date) +
                "&end_date=" + encodeURIComponent(end_date);
            
            // 跳转到导出页面
            window.location.href = exportUrl;
        });
    });
</script>
