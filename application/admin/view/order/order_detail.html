<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    {include file="public/iframeheader"/}
    <link rel="stylesheet" type="text/css" href="__STATIC__/admin/jquery-easyui-1.5.2/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="__STATIC__/admin/jquery-easyui-1.5.2/themes/icon.css">
    <script type="text/javascript" src="__STATIC__/admin/jquery-easyui-1.5.2/jquery.easyui.min.js"></script>
</head>
<body>
<div class="layui-fluid">
    <input type="hidden" id="order_sn" value="{$order_sn}">
    <table class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <th colspan="4" style="text-align: center;background: #eee;">基本信息</th>
        </tr>
        </thead>
        <tbody>
            <tr>
                <td align="right">
                    <strong>订单编号</strong>
                </td>
                <td>{$info['order_sn']}</td>
                <td align="right">
                    <strong>订单状态</strong>
                </td>
                <td>{$info['order_status']}</td>
            </tr>

            <tr>
                <td align="right">
                    <strong>会员</strong>
                </td>
                <td>{$info['member_info']['name']}</td>
                <td align="right">
                    <strong>下单时间</strong>
                </td>
                <td>{$info['add_time']}</td>
            </tr>

            <tr>
                <td align="right">
                    <strong>支付状态</strong>
                </td>
                <td>{$info['pay_status']}</td>
                <td align="right">
                    <strong>支付时间</strong>
                </td>
                <td>{$info['pay_time']}</td>
            </tr>

            <tr>
                <td align="right">
                    <strong>订单总金额</strong>
                </td>
                <td style="color: red;">{$info['order_total_money']}</td>
                <td align="right">
                    <strong>审核时间</strong>
                </td>
                <td>{$info['shenhe_time']}</td>
            </tr>

            <tr>
                <td align="right">
                    <strong>卡号扣减金额</strong>
                </td>
                <td style="color: red;">{$card_total_money}</td>
                <td align="right">
                    <strong></strong>
                </td>
                <td></td>
            </tr>

            <tr>
                <td align="right">
                    <strong>在线支付金额</strong>
                </td>
                <td  style="color: red;">{$info['pay_money']}</td>
                <td align="right">
                    <strong></strong>
                </td>
                <td></td>
            </tr>
        </tbody>
    </table>

    <table class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <th colspan="4" style="text-align: center;background: #eee;">收货信息</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td align="right">
                <strong>收货人姓名</strong>
            </td>
            <td>{$info['name']}</td>
            <td align="right">
                <strong>收货人电话</strong>
            </td>
            <td>{$info['phone']}</td>
        </tr>

        <tr>
            <td align="right" >
                <strong>省市区</strong>
            </td>
            <td colspan="3">{$info['province'] . '   ' . $info['city'] . '   ' . $info['area'] . '  ' .$info['address']} </td>
        </tr>
        </tbody>
    </table>

    <table class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <th colspan="9" style="text-align: center;background: #eee;">商品信息</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td>商品名称</td>
            <td>商品类型</td>
            <td>商品价格</td>
            <td>商品数量</td>
            <td>实际重量</td>
            <td>小计</td>
            <td>发货时间</td>
            <td>快递公司</td>
            <td>快递单号</td>
        </tr>
        {foreach $list as $v}
        <tr>
            <td>{$v['goodsname']}</td>
            <td>
                {if isset($v['actual_weight']) && $v['actual_weight'] > 0}
                    <span class="layui-badge layui-bg-orange">计量商品</span>
                {else}
                    <span class="layui-badge layui-bg-blue">普通商品</span>
                {/if}
            </td>
            <td>
                {if isset($v['actual_weight']) && $v['actual_weight'] > 0}
                    ￥{$v['weight_unit_price']}/kg
                {else}
                    ￥{$v['goodsintegral']}
                {/if}
            </td>
            <td>
                {if isset($v['actual_weight']) && $v['actual_weight'] > 0}
                    1件
                {else}
                    {$v['number']}件
                {/if}
            </td>
            <td>
                {if isset($v['actual_weight']) && $v['actual_weight'] > 0}
                    <span style="color: #ff7a45; font-weight: bold;">{$v['actual_weight']}kg</span>
                {else}
                    --
                {/if}
            </td>
            <td style="color: red; font-weight: bold;">￥{$v['goodsintegral'] * $v['number']}</td>
            <td>{$v['shipping_time']}</td>
            <td>{$v['shipping_name']}</td>
            <td>{$v['shipping_num']}</td>
        </tr>
        {/foreach}
        </tbody>
    </table>
    <span>商品总金额: <font style="color:red;">￥{$goods_total_price}</font></span>

    {if $card_use_log != null}
    <table class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <th colspan="4" style="text-align: center;background: #eee;">折扣信息</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td>卡号</td>
            <td>使用前金额</td>
            <td>使用后金额</td>
            <td>本次扣减金额</td>
        </tr>
        {foreach $card_use_log as $v}
        <tr>
            <td>{$v['cardnum']}</td>
            <td>{$v['after_money']}</td>
            <td>{$v['yu_money']}</td>
            <td>{$v['use_money']}</td>
        </tr>
        {/foreach}
        </tbody>
    </table>
    <span>卡号扣减总金额: <font style="color: red">￥{$card_total_money}</font></span>
    {/if}

</div>

</body>
</html>
