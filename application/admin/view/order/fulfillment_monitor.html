{include file="public/iframeheader"}

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">

        <!-- 统计概览卡片 -->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <i class="layui-icon layui-icon-chart-screen"></i> 订单状态总览
                </div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space15">
                        <!-- 配送订单概览 -->
                        <div class="layui-col-md6">
                            <div class="layui-card">
                                <div class="layui-card-header">配送订单状态</div>
                                <div class="layui-card-body">
                                    <div class="stat-grid">
                                        <div class="stat-item">
                                            <div class="stat-value" id="delivery-waiting-review">0</div>
                                            <div class="stat-label">待审核</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value" id="delivery-accepted">0</div>
                                            <div class="stat-label">已接单</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value" id="delivery-delivering">0</div>
                                            <div class="stat-label">配送中</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value" id="delivery-completed">0</div>
                                            <div class="stat-label">已完成</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value status-count-highlight" id="delivery-recent-completed">0</div>
                                            <div class="stat-label">今日完成</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 自提订单概览 -->
                        <div class="layui-col-md6">
                            <div class="layui-card">
                                <div class="layui-card-header">自提订单状态</div>
                                <div class="layui-card-body">
                                    <div class="stat-grid">
                                        <div class="stat-item">
                                            <div class="stat-value" id="pickup-waiting-verify">0</div>
                                            <div class="stat-label">待审核</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value" id="pickup-verified">0</div>
                                            <div class="stat-label">已接单</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value" id="pickup-preparing">0</div>
                                            <div class="stat-label">配货中</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value" id="pickup-completed">0</div>
                                            <div class="stat-label">已完成</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value status-count-highlight" id="pickup-recent-completed">0</div>
                                            <div class="stat-label">今日完成</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 店铺订单状态表格 -->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <i class="layui-icon layui-icon-list"></i> 门店订单履约状态
                </div>
                <div class="layui-card-body">
                    <table id="fulfillment-stats-table" lay-filter="fulfillment-stats-table"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .layui-card-header {
        font-weight: bold;
        font-size: 16px;
    }
    .layui-table-cell {
        text-align: center;
    }
    .status-count {
        font-weight: bold;
    }
    .status-count-highlight {
        color: #FF5722;
        font-weight: bold;
    }
    .stat-grid {
        display: flex;
        justify-content: space-around;
        text-align: center;
    }
    .stat-item {
        padding: 15px;
    }
    .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #1E9FFF;
    }
    .stat-label {
        margin-top: 5px;
        color: #666;
    }
    .date-input {
        text-align: center;
    }
</style>

<script>
    layui.use(['form', 'laydate', 'table'], function() {
        var table = layui.table;
        var $ = layui.$;
        
        // 初始化履约状态表格
        var fulfillmentStatsTable = table.render({
            elem: '#fulfillment-stats-table',
            cols: [[
                {field: 'shop_name', title: '门店名称', width: 180},
                // 配送订单相关列
                {field: 'delivery_waiting_review', title: '待审核(配送)', width: 120, templet: function(d) {
                    var className = d.delivery_waiting_review > 0 ? 'status-count-highlight' : 'status-count';
                    return '<div class="' + className + '">' + (d.delivery_waiting_review || 0) + '</div>';
                }},
                {field: 'delivery_accepted', title: '已接单(配送)', width: 120, templet: function(d) {
                    var className = d.delivery_accepted > 0 ? 'status-count-highlight' : 'status-count';
                    return '<div class="' + className + '">' + (d.delivery_accepted || 0) + '</div>';
                }},
                {field: 'delivery_delivering', title: '配送中', width: 100, templet: function(d) {
                    var className = d.delivery_delivering > 0 ? 'status-count-highlight' : 'status-count';
                    return '<div class="' + className + '">' + (d.delivery_delivering || 0) + '</div>';
                }},
                // {field: 'delivery_completed', title: '已完成(配送)', width: 120, templet: function(d) {
                //     return '<div class="status-count">' + (d.delivery_completed || 0) + '</div>';
                // }},
                {field: 'delivery_recent_completed', title: '今日完成(配送)', width: 130, templet: function(d) {
                    return '<div class="status-count-highlight">' + (d.delivery_recent_completed || 0) + '</div>';
                }},
                // {field: 'delivery_total', title: '配送小计', width: 100, templet: function(d) {
                //     return '<div class="status-count">' + (d.delivery_total || 0) + '</div>';
                // }},
                // 自提订单相关列
                {field: 'pickup_waiting_verify', title: '待审核(自提)', width: 130, templet: function(d) {
                    var className = d.pickup_waiting_verify > 0 ? 'status-count-highlight' : 'status-count';
                    return '<div class="' + className + '">' + (d.pickup_waiting_verify || 0) + '</div>';
                }},
                {field: 'pickup_verified', title: '已接单(自提)', width: 130, templet: function(d) {
                    var className = d.pickup_verified > 0 ? 'status-count-highlight' : 'status-count';
                    return '<div class="' + className + '">' + (d.pickup_verified || 0) + '</div>';
                }},
                {field: 'pickup_preparing', title: '配货中(自提)', width: 130, templet: function(d) {
                    var className = d.pickup_preparing > 0 ? 'status-count-highlight' : 'status-count';
                    return '<div class="' + className + '">' + (d.pickup_preparing || 0) + '</div>';
                }},
                // {field: 'pickup_completed', title: '已完成(自提)', width: 120, templet: function(d) {
                //     return '<div class="status-count">' + (d.pickup_completed || 0) + '</div>';
                // }},
                {field: 'pickup_recent_completed', title: '今日完成(自提)', width: 130, templet: function(d) {
                    return '<div class="status-count-highlight">' + (d.pickup_recent_completed || 0) + '</div>';
                }},
                // {field: 'pickup_total', title: '自提小计', width: 100, templet: function(d) {
                //     return '<div class="status-count">' + (d.pickup_total || 0) + '</div>';
                // }}
            ]],
            limit: 100,
            text: {
                none: '暂无数据'
            }
        });
        
        // 加载数据函数
        function loadData() {
            // 显示加载中
            layer.load(2);
            
            $.ajax({
                url: '{:url("ajax_fulfillment_stats")}',
                type: 'post',
                success: function(res) {
                    // 关闭加载中
                    layer.closeAll('loading');
                    
                    if (res.code === 0) {
                        updateDashboardData(res.data);
                    } else {
                        layer.msg(res.msg);
                    }
                },
                error: function() {
                    // 关闭加载中
                    layer.closeAll('loading');
                    layer.msg('请求失败，请重试');
                },
                dataType: 'json'
            });
        }
        
        // 更新仪表盘数据
        function updateDashboardData(data) {
            // 更新配送订单状态总览
            $('#delivery-waiting-review').text(data.delivery_stats.waiting_review || 0);
            $('#delivery-accepted').text(data.delivery_stats.accepted || 0);
            $('#delivery-delivering').text(data.delivery_stats.delivering || 0);
            $('#delivery-completed').text(data.delivery_stats.completed || 0);
            $('#delivery-recent-completed').text(data.delivery_stats.recent_completed || 0);
            
            // 更新自提订单状态总览
            $('#pickup-waiting-verify').text(data.pickup_stats.waiting_verify || 0);
            $('#pickup-verified').text(data.pickup_stats.verified || 0);
            $('#pickup-preparing').text(data.pickup_stats.preparing || 0);
            $('#pickup-completed').text(data.pickup_stats.completed || 0);
            $('#pickup-recent-completed').text(data.pickup_stats.recent_completed || 0);
            
            // 直接使用后端返回的数据
            fulfillmentStatsTable.reload({
                data: data.shop_stats || []
            });
        }
        
        // 初始加载数据
        loadData();
    });
</script> 