<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}
<style>
    .layui-table-cell{
        height: inherit;
    }
    .date-range-container {
        display: flex;
        align-items: center;
    }
    .date-range-container .layui-form-label {
        width: auto;
        padding: 9px 5px;
    }
    /* 统计卡片样式 */
    .stat-card {
        text-align: center;
        padding: 15px 10px;
        border-radius: 2px;
        background-color: #fff;
        box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        margin-bottom: 15px;
    }
    .stat-card .stat-title {
        color: #666;
        font-size: 14px;
    }
    .stat-card .stat-value {
        color: #333;
        font-size: 24px;
        font-weight: 500;
        margin-top: 5px;
    }
    .stat-card .value-unit {
        font-size: 14px;
        color: #999;
    }
</style>

<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">订单明细列表</div>
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">订单号</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" name="keyword" id="demoReload" autocomplete="off">
                            <input type="hidden" name="field" value="order_no">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">门店</label>
                        <div class="layui-input-inline">
                            <select name="shop_id" lay-search>
                                <option value='0'>全部</option>
                                {foreach $shop_enum as $k => $val}
                                <option value="{$val.id}">{$val.title}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">商品</label>
                        <div class="layui-input-inline">
                            <input type="text" name="product_name" id="product_name" autocomplete="off" class="layui-input" placeholder="搜索商品">
                            <input type="hidden" name="product_id" id="product_id">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">规格</label>
                        <div class="layui-input-inline">
                            <select name="inventory_id" id="inventory_id">
                                <option value="0">全部</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">订单类型</label>
                        <div class="layui-input-inline">
                            <select name="type">
                                <option value='0'>全部</option>
                                <option value='1'>自提订单</option>
                                <option value='2'>配送订单</option>
                                <option value='3'>结算台订单</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">日期范围</label>
                        <div class="layui-input-inline">
                            <input type="text" name="date_range" id="date_range" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button class="layui-btn" id="search">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button class="layui-btn layui-btn-primary" id="reset-btn">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                        <button class="layui-btn layui-btn-normal" id="exportBtn">
                            <i class="layui-icon layui-icon-export"></i> 导出明细
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计卡片区域 -->
            <div class="layui-row layui-col-space15" id="stat-cards">
                <div class="layui-col-md3">
                    <div class="stat-card">
                        <div class="stat-title">总金额</div>
                        <div class="stat-value">￥<span id="total-amount">0.00</span></div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="stat-card">
                        <div class="stat-title">普通商品销售件数</div>
                        <div class="stat-value"><span id="normal-count">0</span><span class="value-unit"> 件</span></div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="stat-card">
                        <div class="stat-title">计量商品销售斤数</div>
                        <div class="stat-value"><span id="weight-count">0.00</span><span class="value-unit"> 斤</span></div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="stat-card">
                        <div class="stat-title">总销售数</div>
                        <div class="stat-value"><span id="total-weight">0.00</span><span class="value-unit"> </span></div>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table class="layui-hide" id="demo" lay-filter="demo"></table>
        </div>
    </div>
</div>

</body>

</html>

<script type="text/javascript">
    layui.use(['table', 'form', 'laydate', 'autocomplete'], function () {
        var table = layui.table;
        var form = layui.form;
        var laydate = layui.laydate;
        var autocomplete = layui.autocomplete;
        var $ = layui.jquery;
        
        // 初始化日期选择器
        laydate.render({
            elem: '#date_range',
            range: true
        });

        // 商品搜索自动完成
        autocomplete.render({
            elem: '#product_name',
            url: '{:url("inventory/searchProduct")}',
            cache: false,
            template_val: '{{d.name}}',
            // template_txt: '{{d.name}} <span class="layui-badge layui-bg-gray"></span>',
            onselect: function (resp) {
                $('#product_id').val(resp.id);
                // 加载该商品的规格
                loadInventoryOptions(resp.id);
            }
        });

        // 加载商品规格选项
        function loadInventoryOptions(productId) {
            if (!productId) {
                $('#inventory_id').html('<option value="0">全部</option>');
                form.render('select');
                return;
            }
            
            $.ajax({
                url: '{:url("inventory/getInventoryByProduct")}',
                type: 'get',
                data: {product_id: productId},
                dataType: 'json',
                success: function(res) {
                    if (res.code === 0) {
                        var options = '<option value="0">全部</option>';
                        $.each(res.data, function(index, item) {
                            options += '<option value="' + item.id + '">' + item.name + '</option>';
                        });
                        $('#inventory_id').html(options);
                        form.render('select');
                    } else {
                        layer.msg(res.msg || '获取规格失败');
                    }
                },
                error: function() {
                    layer.msg('网络错误，获取规格失败');
                }
            });
        }
        
        // 更新统计卡片
        function updateStatCards(stats) {
            $('#total-amount').text((stats.total_amount || 0).toFixed(2));
            $('#normal-count').text(stats.normal_product_count || 0);
            $('#weight-count').text((stats.weight_product_kg || 0).toFixed(2));
            $('#total-weight').text((stats.total_weight_kg || 0).toFixed(2));
        }

        // 加载统计卡片数据
        function loadStatistics() {
            var keyword = $("[name=keyword]").val();
            var field = $("[name=field]").val();
            var shop_id = $("[name=shop_id]").val();
            var type1 = $("[name=type]").val();
            var date_range = $("#date_range").val();
            var dates = date_range ? date_range.split(' - ') : ['', ''];
            var start_date = dates[0] || '';
            var end_date = dates[1] || '';
            var product_id = $("[name=product_id]").val();
            var inventory_id = $("[name=inventory_id]").val();
            
            $.ajax({
                url: "{:url('getOrderDetailStatistics')}",
                type: 'get',
                data: {
                    keyword: keyword,
                    field: field,
                    shop_id: shop_id,
                    type1: type1,
                    start_date: start_date,
                    end_date: end_date,
                    product_id: product_id,
                    inventory_id: inventory_id
                },
                dataType: 'json',
                success: function(res) {
                    if (res.code === 0) {
                        updateStatCards(res.data);
                    } else {
                        layer.msg(res.msg || '获取统计数据失败');
                    }
                },
                error: function() {
                    layer.msg('网络错误，获取统计数据失败');
                }
            });
        }
        
        // 表格渲染
        table.render({
            elem: '#demo'
            , url: "{:url('ajax_order_detail_list_page')}"
            , toolbar: true
            , defaultToolbar: ['filter', 'exports', 'print']
            , cols: [[
                {type: 'checkbox'}
                , {type: 'numbers', title: '序号', width: 60}
                , {field: 'order_no', title: '订单号', width: 180}
                , {field: 'shop_name', title: '门店名称', width: 120}
                , {field: 'username', title: '用户名称', width: 120}
                , {field: 'product_title', title: '商品名称', width: 200}
                , {field: 'inventory_title', title: '规格名称', width: 200}
                , {field: 'product_type', title: '商品类型', width: 100, templet: function(d) {
                    if (d.product_type == '计量商品') {
                        return '<span class="layui-badge layui-bg-orange">计量商品</span>';
                    } else if (d.product_type == '普通商品') {
                        return '<span class="layui-badge layui-bg-blue">普通商品</span>';
                    } else if (d.product_type == '赠品') {
                        return '<span class="layui-badge layui-bg-gray">赠品</span>';
                    }
                }}
                , {field: 'quantity', title: '数量/重量', width: 100}
                , {field: 'unit_price', title: '单价', width: 100}
                , {field: 'amount', title: '商品总价', width: 100, templet: function(d){
                    return '￥' + d.amount;
                }}
                , {field: 'order_type', title: '订单类型', width: 100}
                , {field: 'order_status', title: '订单状态', width: 100, templet: function(d){
                    var statusClass = '';
                    if(d.order_status.indexOf('待付款') !== -1) {
                        statusClass = 'layui-bg-orange';
                    } else if(d.order_status.indexOf('已付款') !== -1 || d.order_status.indexOf('配送中') !== -1) {
                        statusClass = 'layui-bg-blue';
                    } else if(d.order_status.indexOf('已完成') !== -1) {
                        statusClass = 'layui-bg-green';
                    } else if(d.order_status.indexOf('已取消') !== -1) {
                        statusClass = 'layui-bg-gray';
                    }
                    return '<span class="layui-badge ' + statusClass + '">' + d.order_status + '</span>';
                }}
                , {field: 'pay_type', title: '支付方式', width: 100}
                , {field: 'add_time', title: '下单时间', width: 160}
            ]]
            , page: true
            , limit: 15
            , limits: [15, 30, 50, 100]
            , response: {
                statusCode: 0
            }
            , parseData: function(res) {
                // 删除更新统计卡片的代码
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.count,
                    "data": res.data
                };
            }
            , done: function() {
                // 表格加载完成后，再加载统计卡片数据
                loadStatistics();
            }
        });

        // 搜索功能
        $('#search').click(function () {
            var keyword = $("[name=keyword]").val();
            var field = $("[name=field]").val();
            var shop_id = $("[name=shop_id]").val();
            var type1 = $("[name=type]").val();
            var date_range = $("#date_range").val();
            var dates = date_range ? date_range.split(' - ') : ['', ''];
            var start_date = dates[0] || '';
            var end_date = dates[1] || '';
            var product_id = $("[name=product_id]").val();
            var inventory_id = $("[name=inventory_id]").val();
            
            // 执行重载
            table.reload('demo', {
                page: {
                    curr: 1 // 重新从第 1 页开始
                }
                , where: {
                    keyword: keyword,
                    field: field,
                    shop_id: shop_id,
                    type1: type1,
                    start_date: start_date,
                    end_date: end_date,
                    product_id: product_id,
                    inventory_id: inventory_id
                }
            });
            
            // 同时刷新统计数据
            loadStatistics();
        });

        // 重置按钮点击事件
        $('#reset-btn').on('click', function() {
            $("[name=keyword]").val('');
            $("[name=shop_id]").val('0');
            $("[name=type]").val('0');
            $("#date_range").val('');
            $("#product_name").val('');
            $("#product_id").val('');
            $("#inventory_id").val('0');
            form.render('select');
            $('#search').click();
            
            // 已经在search.click()中调用了loadStatistics()
        });

        // 导出按钮点击事件
        $('#exportBtn').click(function() {
            var keyword = $("[name=keyword]").val();
            var field = $("[name=field]").val();
            var shop_id = $("[name=shop_id]").val();
            var type1 = $("[name=type]").val();
            var date_range = $("#date_range").val();
            var dates = date_range ? date_range.split(' - ') : ['', ''];
            var start_date = dates[0] || '';
            var end_date = dates[1] || '';
            var product_id = $("[name=product_id]").val();
            var inventory_id = $("[name=inventory_id]").val();
            
            // 构建导出URL
            var exportUrl = "{:url('export_order_detail')}" + 
                "?keyword=" + encodeURIComponent(keyword) + 
                "&field=" + encodeURIComponent(field) + 
                "&shop_id=" + encodeURIComponent(shop_id) + 
                "&type1=" + encodeURIComponent(type1) +
                "&start_date=" + encodeURIComponent(start_date) +
                "&end_date=" + encodeURIComponent(end_date) +
                "&product_id=" + encodeURIComponent(product_id) +
                "&inventory_id=" + encodeURIComponent(inventory_id);
                
            // 打开新窗口下载文件
            window.location.href = exportUrl;
        });
    });
</script> 