<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
<br/>
<div style="margin-left: 20px;">
    <div class="page-content">
        <input type="hidden" name="no" id="no" value="{$no}">
        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
    </div>
</div>

</body>

</html>
<script type="text/javascript">
    get_table();

    function get_table(){
        layui.use(['table', 'jquery'], function () {
            var no = document.getElementById("no").value;
            var table = layui.table;
            //方法级渲染
            table.render({
                elem: '#demo'
                , url: "{:url('ajax_get_list_by_puno')}?no=" + no
                , cols: [[
                    {type: 'numbers', title: '序号'}
                    , {field: 'finance_no', title: '收支单号'}
                    , {field: 'operator', title: '操作人'}
                    , {field: 'add_time', title: '操作时间'}
                    , {field: 'customer_name', title: '客户'}
                    , {field: 'no', title: '销售单号/退卡单号'}
                    , {field: 'status', title: '状态'}
                    , {field: 'money', title: '金额'}
                    , {field: 'remarks', title: '备注'}
                ]]
                , id: 'id'
                , page: false
            });

            var $ = layui.$, active = {
                reload: function () {
                    //执行重载
                    table.reload('testReload', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        }
                    });
                },
            };
        });
    }


</script>
