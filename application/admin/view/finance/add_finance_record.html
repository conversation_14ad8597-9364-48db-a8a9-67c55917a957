<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}
<script src="__STATIC__/admin/js/jquery.autocompleter.js" type="text/javascript"></script>
<style>

    .autocompleter-item strong {
        background: #f9de8f;
        text-shadow: 0 1px 0 #ffffff;
    }

    .autocompleter-item span {
        color: #bbbbbb;
    }


    .autocompleter-hint span {
        color: transparent;
    }
</style>

<body>
<br />
<form class="layui-form" action="">

    <div class="layui-form-item">
        <label class="layui-form-label">客户</label>
        <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
        <div class="layui-input-inline" style="width: 400px;">
            <select name="customer_id"  lay-filter="select-customer" lay-verify="required" lay-search>
                <option value="">请选择</option>
                {foreach name='customer_list' key='key' item='value'}
                <option value="{$value['id']}" selected> {$value["name"]}</option>
                {/foreach}
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">类型</label>
        <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>
        <div class="layui-input-inline">
            <input type="radio" name="status" class="status" value="1" lay-filter="demo-radio-filter" required lay-verify="required" title="收入" checked>
            <input type="radio" name="status" class="status" value="2" lay-filter="demo-radio-filter" required lay-verify="required" title="支出">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">金额</label>
        <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>
        <div class="layui-input-inline" style="width: 400px;">
            <input type="text" name="money" required lay-verify="required" placeholder="请输入金额" autocomplete="off" class="layui-input">          </div>
        <div class="layui-form-mid layui-word-aux"></div>
    </div>

    <div class="layui-form-item layui-form-text">
        <label class="layui-form-label">备注说明</label>
        <div class="layui-input-inline" style="width: 400px;">
            <textarea name="remarks" placeholder="请输入内容" class="layui-textarea"></textarea>
        </div>
    </div>

    <div class="layui-form-item" id="sub">
        <div class="layui-input-block">
            <button class="layui-btn  layui-btn-sm" lay-submit lay-filter="formDemo">添加</button>
        </div>
    </div>
</form>
<script>

    //Demo
    layui.use('form', function () {
        var form = layui.form;


        form.on('submit(formDemo)', function (data) {
            var index = layer.load(0, {
                shade: [0.4, 'black'] //0.1透明度的白色背景
            });
            $.post("{:url('ajax_add_finance_record')}", data.field, function (data) {
                if (data.code == 0) {
                    layer.msg(data.msg, {time: 1500}, function () {
                        window.location.href = "{:url('add_finance_record')}";
                    });
                } else {
                    layer.close(index);
                    layer.msg(data.msg);
                }
            }, "json");
            return false;
        });

        // form.on('select(select-customer)', function(data){
        //     var elem = data.elem; // 获得 select 原始 DOM 对象
        //     var value = data.value; // 获得被选中的值
        //     var othis = data.othis; // 获得 select 元素被替换后的 jQuery 对象
        //
        //     layer.msg(this.innerHTML + ' 的 value: '+ value); // this 为当前选中 <option> 元素对象
        // });
    });

</script>


</body>

</html>
