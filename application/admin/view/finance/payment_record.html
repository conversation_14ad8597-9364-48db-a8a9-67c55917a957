<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
<br />
<div style="margin: 20px;">
    <div class="page-content" >
        <div class="sk">支付总金额：<span id="pay_total_money"></span></div>

        <div class="demoTable layui-form">

            <div class="layui-inline">
                <label class="layui-form-label ">查询条件：</label>
                <div class="layui-input-block">
                    <select name="field">
                        <option value='order_sn'>订单号</option>
                        <option value='transaction_id'>微信商户单号</option>
                    </select>
                </div>
            </div>

            <div class='layui-inline'>
                <input class="layui-input" name="keyword" id="demoReload" autocomplete="off">
            </div>

            <label class="layui-inline">时间筛选：</label>
            <div class='layui-inline' id="time">
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="开始时间" id="start_time" name="start_time"
                           autocomplete="off">
                </div>
                <span>-</span>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="截止时间" id="end_time" name="end_time"
                           autocomplete="off">
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label ">是否支付：</label>
                <div class="layui-input-block">
                    <select name="is_pay">
                        <option value='0'>全部</option>
                        <option value='1'>未支付</option>
                        <option value='2'>已支付</option>
                    </select>
                </div>
            </div>
            <button class="layui-btn  layui-btn-sm" onclick="get_table()" id="reload">搜索</button>
        </div>

        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-sm" lay-event='show-detail'>查看详情</a>
        </script>
    </div>
</div>

</body>

</html>
<script type="text/javascript">

    get_table();

    function get_table() {
        var start_time = $("[name=start_time]").val();
        var end_time = $("[name=end_time]").val();
        var is_pay = $("[name=is_pay]").val();
        var field = $("[name=field]").val();
        var keyword = $("[name=keyword]").val();

        //初始化时间标签
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#end_time' //指定元素
            });
            laydate.render({
                elem: '#start_time' //指定元素
            });

        });
        layui.use('table', function () {

            var table = layui.table;
            //方法级渲染
            table.render({
                elem: '#demo'
                , url: "{:url('ajax_payment_record')}?start_time=" + start_time + "&end_time="
                    + end_time + "&field=" + field + "&keyword=" + keyword + "&is_pay=" + is_pay
                , cols: [[
                    { type: 'numbers', title: '序号' }
                    , { field: 'pay_no', title: '支付单号', width: 240 }
                    , { field: 'order_sn', title: '订单单号', width: 240 }
                    , { field: 'transaction_id', title: '微信商户单号', width: 240 }
                    , { field: 'pay_status', title: '支付状态' }
                    , { field: 'money', title: '应付金额' }
                    , { field: 'pay_money', title: '实付金额' }
                    , { field: 'pay_time', title: '支付时间' }
                    , { fixed: 'right', title: "操作", toolbar: '#barDemo', width: 120 }
                ]]
                , id: 'testReload'
                , page: true
                , parseData: function(res){
                    $('#order_total_money').html(res.append.order_total_money);
                    $('#card_total_money').html(res.append.card_total_money);
                    $('#pay_total_money').html(res.append.pay_total_money);
                }
            });

            table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                var data = obj.data //获得当前行数据
                    , layEvent = obj.event; //获得 lay-event 对应的值
                var order_sn = data.order_sn;
                if (layEvent === 'order-detail'){
                    layer.open({
                        type: 2,
                        title: "订单详情",
                        content: "{:url('order_detail')}?order_sn=" + order_sn,
                        maxmin: true,
                        area: ["98%", "98%"],
                        end: function () {
                            get_table();
                        }
                    })
                }

            });

            var $ = layui.$, active = {
                reload: function () {
                    var start_time = $("[name=start_time]").val();
                    var end_time = $("[name=end_time]").val();
                    var is_pay = $("[name=is_pay]").val();
                    var field = $("[name=field]").val();
                    var keyword = $("[name=keyword]").val();
                    //执行重载
                    table.reload('testReload', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        }
                        , where: {
                            start_time: start_time,
                            end_time: end_time,
                            is_pay: is_pay,
                            field: field,
                            keyword: keyword
                        }
                    });
                },
            };
        });
    }

    function createTime(v) {
        var v = v * 1000; //js的时间戳要*1000
        var date = new Date(v);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? '0' + m : m;
        var d = date.getDate();
        d = d < 10 ? ("0" + d) : d;
        var h = date.getHours();
        h = h < 10 ? ("0" + h) : h;
        var M = date.getMinutes();
        M = M < 10 ? ("0" + M) : M;
        var S = date.getSeconds();
        S = S < 10 ? ("0" + S) : S;
        var str = y + "-" + m + "-" + d + " " + h + ":" + M + ":" + S;
        return str;
    }
</script>
