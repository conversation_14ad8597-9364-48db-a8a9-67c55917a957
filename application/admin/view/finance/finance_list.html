<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
<br />
<div style="margin-left: 20px;">
    <div class="page-content">

        <div class="demoTable layui-form">


            <div class="layui-inline">
                <label class="layui-form-label ">筛选客户：</label>
                <div class="layui-input-block">
                    <select name="customer_id">
                        <option value="0" selected>全部</option>
                        {foreach name="customer_list" key="key" item="value"}
                        <option value="{$value['id']}">{$value['name']}</option>
                        {/foreach}
                    </select>
                </div>
            </div>

            <label class="layui-inline">时间筛选：</label>
            <div class='layui-inline' id="time">
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="开始时间" id="start_time" name="start_time"
                           autocomplete="off">
                </div>
                <span>-</span>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="截止时间" id="end_time" name="end_time"
                           autocomplete="off">
                </div>
            </div>
            <button class="layui-btn  layui-btn-sm" onclick="get_table()" id="reload">搜索</button>
            <button class="layui-btn  layui-btn-sm" onclick="export_list()" id="export">导出execl</button>
        </div>

        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-sm" lay-event='show-detail'>查看详情</a>
        </script>
    </div>
</div>

</body>

</html>
<script type="text/javascript">

    get_table();

    function get_table() {
        var customer_id = $("[name=customer_id]").val();
        // var status = $("[name=status]").val();
        var start_time = $("[name=start_time]").val();
        var end_time = $("[name=end_time]").val();

        //初始化时间标签
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#end_time' //指定元素
            });
            laydate.render({
                elem: '#start_time' //指定元素
            });

        });
        layui.use('table', function () {

            var table = layui.table;
            //方法级渲染
            table.render({
                elem: '#demo'
                , url: "{:url('ajax_finance_list')}?customer_id=" + customer_id + "&start_time=" + start_time + "&end_time="
                    + end_time
                , cols: [[
                    { type: 'numbers', title: '序号' }
                    , { field: 'no', title: '收支单号' }
                    , { field: 'customer_id', title: '客户名称' }
                    , { field: 'operator', title: '操作人' }
                    , { field: 'money', title: '总金额' }
                    , { field: 'income', title: '已结清' }
                    , { field: 'balance', title: '余额' }
                    , { field: 'status', title: '类型' }
                    , { field: 'remarks', title: '备注' }
                    , { field: 'add_time', title: '操作时间' }
                    , { fixed: 'right', title: "操作", toolbar: '#barDemo', width: 100}
                ]]
                , id: 'testReload'
                , page: true
            });

            //头工具栏事件
            table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                var data = obj.data //获得当前行数据
                    , layEvent = obj.event; //获得 lay-event 对应的值
                var id = data.id;
                if (layEvent === 'show-detail') {
                    layer.open({
                        type: 2,
                        title: "财务详情",
                        content: "{:url('finance_detail_list')}?id=" + id,
                        area: ["99%", "99%"]
                    })
                }
            });

            var $ = layui.$, active = {
                reload: function () {
                    var customer_id = $("[name=customer_id]").val();
                    var start_time = $("[name=start_time]").val();
                    var end_time = $("[name=end_time]").val();
                    //执行重载
                    table.reload('testReload', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        }
                        , where: {
                            customer_id: customer_id,
                            start_time: start_time,
                            end_time: end_time

                        }
                    });
                },
            };



            $('.demoTable .layui-btn').on('click', function () {
                var type = $(this).data('type');
                active[type] ? active[type].call(this) : '';
            });
        });
    }


    function export_list(){
        var customer_id = $("[name=customer_id]").val();
        // var status = $("[name=status]").val();
        var start_time = $("[name=start_time]").val();
        var end_time = $("[name=end_time]").val();

        window.location.href = "{:url('export_finance_list')}?customer_id=" + customer_id + "&start_time=" + start_time + "&end_time="
            + end_time;

    }

    function createTime(v) {
        var v = v * 1000; //js的时间戳要*1000
        var date = new Date(v);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? '0' + m : m;
        var d = date.getDate();
        d = d < 10 ? ("0" + d) : d;
        var h = date.getHours();
        h = h < 10 ? ("0" + h) : h;
        var M = date.getMinutes();
        M = M < 10 ? ("0" + M) : M;
        var S = date.getSeconds();
        S = S < 10 ? ("0" + S) : S;
        var str = y + "-" + m + "-" + d + " " + h + ":" + M + ":" + S;
        return str;
    }
</script>
