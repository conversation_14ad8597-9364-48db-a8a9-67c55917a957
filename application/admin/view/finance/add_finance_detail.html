<!DOCTYPE html>
{include file="public/iframeheader"/}
<link rel="stylesheet" type="text/css" href="__STATIC__/admin/jquery-easyui-1.5.2/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="__STATIC__/admin/jquery-easyui-1.5.2/themes/icon.css">
<script type="text/javascript" src="__STATIC__/admin/jquery-easyui-1.5.2/jquery.easyui.min.js"></script>


<body>
<br/>
<div style="margin-left: 20px;">

    <form class="layui-form" action="">

        <input type="hidden" name="finance_id" value="{$finance_id}">

        <div class="layui-form-item">
            <label class="layui-form-label">类型</label>
            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>
            <div class="layui-input-inline">
                <input type="radio" name="status" class="status" value="1" lay-filter="demo-radio-filter" required lay-verify="required" title="收入" checked>
                <input type="radio" name="status" class="status" value="2" lay-filter="demo-radio-filter" required lay-verify="required" title="支出">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" id="data-title">销售单号</label>
            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>
            <div class="layui-input-inline">
                <input type="text" name="no" id="no" required lay-verify="required" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">金额</label>
            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>
            <div class="layui-input-inline">
                <input type="text" name="money" required lay-verify="required" placeholder="￥" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注说明</label>
            <div class="layui-input-block" style="width:40%;">
                <textarea name="remarks" placeholder="" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn  layui-btn-sm" lay-submit lay-filter="sub">立即提交</button>
            </div>
        </div>
    </form>


<script>
    layui.use('form', function () {
        var form = layui.form;

        // 监听单选框
        form.on('radio(demo-radio-filter)', function(data){
            console.log(data);
            var elem = data.elem; // 获得 radio 原始 DOM 对象
            var value = elem.value; // 获得 radio 值

            if (value == '1') {
                $('#data-title').html('销售单号');
            } else if(value == '2') {
                $('#data-title').html('退卡单号');
            }


        });

        //监听提交
        form.on('submit(sub)', function (data) {
            load = layer.load(2, {shade: [0.1, '#fff']});
            $.post("{:url('ajax_add_finance_detail')}", data.field, function (data) {
                layer.close(load);
                if (data.code == 1) {
                    layer.msg(data.msg, {time: 1500}, function () {
                        window.location.href = "{:url('sale_list')}";
                    });
                } else {
                    layer.msg(data.msg);
                }
            }, "json");

            return false;
        });
    });
    //鼠标失去焦点事件
    $(document).ready(function () {
        $("#no").blur(function () {
            var no = $(this).val();
            var status = $('input[name="status"]:checked').val();

            $.post("{:url('preview_price')}", {
                no: no,
                status: status,
            }, function (data) {
                console.log(data);
                if (data.code == 0) {
                    if (data.data != null) {
                        $('input[name="money"]').val(data.data.real_tui_money);
                    } else {
                        $('input[name="money"]').val(0.00);
                    }
                }

            }, "json");
        })
    });
</script>

</div>
</body>

</html>
