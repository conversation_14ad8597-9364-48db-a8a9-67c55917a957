<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}
<style>
    .layui-col-md6 {
        width: 20%;
        margin-left: 60px;
        background-color: #1e9fff;
        color: white;
    }

    .layui-col-md6 .layui-card-header {
        background-color: #1e9fff;
        text-align: center;
        color: white;
        font-size: 20px;

    }

    .layui-col-md6 .layui-card-body {
        background-color: #1e9fff;
        text-align: center;
    }
</style>
<body>
<br/>
<div style="margin-left: 20px;">
    <div class="layui-bg-gray" style="padding: 16px;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">总金额</div>
                    <div class="layui-card-body">
                        ￥{$info['money']}
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">已结清</div>
                    <div class="layui-card-body">
                        ￥{$info['income']}
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">剩余</div>
                    <div class="layui-card-body">
                        ￥{$info['balance']}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div>
        <div class="layui-inline">
            <label class="layui-form-label">客户未结清账单</label>
            <div class="layui-input-inline">
                <table class="layui-hide" id="add-data" lay-filter="add-data"></table>
            </div>
            <br>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label"></label>
                <div class="layui-input-inline" style="width: 400px;">
                    <span>当前结算总金额:<span style="color: red">￥</span><span style="color: red;font-weight: bold;" id="price">0</span></span>
                </div>
            </div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block" style="width:40%;">
                <textarea name="remarks" id="remarks" placeholder="" class="layui-textarea"></textarea>
            </div>
        </div>

        <div class="layui-form-item" style="padding-top: 20px;">
            <div class="layui-input-block">
                <button type="submit" id="submit-add-data" class="layui-btn" lay-submit lay-filter="demo1">立即提交</button>
            </div>
        </div>
    </div>

    <div class="page-content">
        <!--        <div style="margin-top: 10px;margin-left: 25px;">-->
        <!--            <button class="layui-btn layuiadmin-btn-useradmin   layui-btn-sm" id="add-finance-detail">新增</button>-->
        <!--        </div>-->
        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
    </div>
</div>

</body>
<script type="text/javascript" src="__STATIC__/admin/layui-2.8/src/layui.js"></script>
</html>
<script type="text/javascript">
    get_table();

    function callback(msg) {
        layer.msg(msg, {time: 1500}, function (data) {
            layer.closeAll();
            window.parent.location.reload();
        })
    }

    function get_table() {
        layui.use('table', function () {

            var table = layui.table;
            //方法级渲染
            table.render({
                elem: '#demo'
                , url: "{:url('ajax_finance_detail_list')}?id=" + {$id}
                , cols: [[
                    {type: 'numbers', title: '序号'}
                    , {field: 'finance_no', title: '收支单号'}
                    , {field: 'operator', title: '操作人'}
                    , {field: 'add_time', title: '操作时间'}
                    , {field: 'no', title: '销售单号/退卡单号'}
                    , {field: 'status', title: '类型'}
                    , {field: 'money', title: '金额'}
                    , {field: 'customer_name', title: '客户'}
                    , {field: 'remarks', title: '备注'}
                ]]
                , id: 'testReload'
                , page: true
            });

            var $ = layui.$, active = {
                reload: function () {
                    //执行重载
                    table.reload('testReload', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        }
                    });
                },
            };
        });
    }

    layui.use('table', function () {

        var table = layui.table;
        //方法级渲染
        table.render({
            elem: '#add-data'
            , cols: [[
                {type: 'checkbox', fixed: 'left'}
                , {field: 'pici_number', title: '销售单号/退卡单号'}
                , {field: 'real_tui_money', title: '总金额'}
                , {field: 'uncleared_money', title: '未结清'}
                , {field: 'money', title: '此次结算金额', edit:'text',color:'green'
                    // 2023-5-19 注释 后期无用后删除
                    // templet: function (item) {
                    // console.log(item);
                    //     return '<div class="layui-input-inline">\n' +
                    //         '<input type="text" class="layui-input" id="son_price_' + item.id +'" name="son_price_' + item.id +
                    //         ' " placeholder="￥" value="'+ item.money +'" autocomplete="off" lay-verify="number"' +
                    //         ' onchange="price_change(' + item.LAY_INDEX + ')">\n' +
                    //         '</div>';
                    // }
            }
            ]]
            , width: 800
            , data: {$uncleared_list}
            , height: 240
            , limit: {$uncleared_list}.length
        });

        // 复选框事件
        table.on('checkbox(add-data)', function(obj){
            var money = $('#price').html();
            console.log(money);
            if (obj.checked) {
                money = parseFloat(money) + parseFloat(obj.data.money);
            } else {
                money = parseFloat(money) - parseFloat(obj.data.money);
            }
            console.log(money)
            $('#price').html(money);
        });

        // 单元格普通编辑事件
        table.on('edit(add-data)', function(obj){
            var value = obj.value // 得到修改后的值
            var money = obj.money; // 得到字段
            var status = table.checkStatus('add-data');
            console.log(obj);
            for(let key in status.data){
                if (status.data[key].id == obj.data.id){
                    var new_money = $('#price').html();
                    new_money = parseFloat(new_money) - parseFloat(obj.oldValue) + parseFloat(value);
                    $('#price').html(new_money);
                }
            }
            // 更新数据中对应的字段
            var update = {};
            update[money] = value;
            obj.update(update);
        });

        $('#submit-add-data').click(function(){
            // 获取表格中选中的数据
            var status = table.checkStatus('add-data');
            var total_price = $('#price').html();
            var id = {$id};
            var remarks = $('#remarks').val();
            if (status.length <= 0) {
                layer.msg('请选择需要结算的数据！');
                return false;
            }

            if (total_price < 0) {
                layer.msg('请输入结算金额');
                return false;
            }
            var data = [];
            for (var key in status.data) {
                data.push({
                    no: status.data[key].pici_number,
                    money: status.data[key].money,
                });
            }

            var request_data = {
                finance_id: id,
                total_price: total_price,
                data: data,
                remarks: remarks
            };


            $.post("{:url('ajax_add_finance_detail')}"
                , request_data
                , function (data) {
                    if (data.code == 0) {
                        layer.msg(data.msg);
                        setInterval(function () {
                            window.location = "{:url('finance_detail_list')}?id=" + id;
                        }, 1500);
                    } else {
                        layer.msg(data.msg);
                    }
                }, "json");



            return false;

        });

    });


    // 2023-5-19 注释 后期无用后删除
    // /**
    //  * @param index 客户未结账单里面的第几条
    //  */
    // function price_change(index){
    //     layui.use('table', function(){
    //
    //         var data = layui.table.cache['add-data'];
    //         // 如果该数据为选中状态
    //         console.log(1111);
    //         console.log(data[index]);
    //         if (data[index].LAY_CHECKED) {
    //             var money = $('input[name="price"]').val();
    //             console.log($('#son_price_' + data[index].id).val());
    //             money = parseFloat(money) + parseFloat($('#son_price_' + data[index].id).val());
    //             $('input[name="price"]').val(money);
    //         }
    //     });
    // }


</script>
