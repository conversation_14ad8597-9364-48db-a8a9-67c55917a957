<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>添加食谱</title>
    {include file="public/iframeheader"/}
</head>

<body>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body" pad15>

                    <div class="layui-form" wid100 lay-filter="">

                        <div class="layui-form-item">
                            <label class="layui-form-label">食谱标题</label>
                            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                            <div class="layui-input-inline">
                                <input type="text" name="title" style="width: 400px;" lay-verify="required"
                                       class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">所属分类</label>
                            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                            <div class="layui-input-inline" style="width: 400px;">
                                <select name="category_id" lay-verify="required" lay-search>
                                    <option value="">请选择分类</option>
                                    {volist name="categories" id="category"}
                                    <option value="{$category.id}">{$category.name}</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">内容类型</label>
                            <div class="layui-input-block">
                                <input type="radio" name="content_type" value="1" title="图文" checked lay-filter="content_type">
                                <input type="radio" name="content_type" value="2" title="视频" lay-filter="content_type">
                            </div>
                        </div>

                        <!-- <div class="layui-form-item">
                            <label class="layui-form-label">难度</label>
                            <div class="layui-input-block">
                                <input type="radio" name="difficulty" value="1" title="简单" checked>
                                <input type="radio" name="difficulty" value="2" title="中等">
                                <input type="radio" name="difficulty" value="3" title="困难">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">烹饪时间(分钟)</label>
                            <div class="layui-input-inline">
                                <input type="number" name="cooking_time" value="0" lay-verify="number" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">适合人数</label>
                            <div class="layui-input-inline">
                                <input type="number" name="serving_size" value="0" lay-verify="number" class="layui-input">
                            </div>
                        </div> -->

                        <div class="layui-form-item">
                            <label class="layui-form-label">封面图片</label>
                            <div class="layui-input-block layui-btn-container">
                                {:UpImage("cover_image",200,200,"")}
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">食谱描述</label>
                            <div class="layui-input-block">
                                <textarea name="description" placeholder="请输入食谱描述" class="layui-textarea"></textarea>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">标签</label>
                            <div class="layui-input-block">
                                <div id="demo1" class="xm-select-demo"></div>
                            </div>
                        </div>

                        <!-- 视频内容 -->
                        <div class="layui-form-item video-content" style="display: none;">
                            <label class="layui-form-label">选择视频</label>
                            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                            <div class="layui-input-inline" style="width: 400px;">
                                <select name="video_select" id="videoSelect" lay-filter="videoSelect" lay-search>
                                    <option value="">请选择视频</option>
                                </select>
                            </div>
                            <!-- <div class="layui-input-inline">
                                <button type="button" class="layui-btn layui-btn-primary" id="previewVideoBtn" style="display: none;">预览视频</button>
                            </div> -->
                        </div>

                        <div class="layui-form-item video-content" style="display: none;">
                            <label class="layui-form-label">视频名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="video_name" id="selectedVideoName" readonly class="layui-input" style="width: 400px; background-color: #f8f8f8;">
                                <input type="hidden" name="video_url" id="selectedVideoUrl">
                                <input type="hidden" name="video_vid" id="selectedVideoVid">
                            </div>
                        </div>

                        <div class="layui-form-item video-content" style="display: none;">
                            <label class="layui-form-label">视频时长(秒)</label>
                            <div class="layui-input-block">
                                <input type="number" name="video_duration" value="0" placeholder="请输入视频时长（秒）" class="layui-input" style="width: 200px;">
                            </div>
                        </div>

                        <!-- 图文内容 -->
                        <div class="layui-form-item image-content">
                            <label class="layui-form-label">多图展示</label>
                            <div class="layui-input-block">
                                {:BatchImage("images",200,200,"")}
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <input type="radio" name="status" value="1" title="上架" checked>
                                <input type="radio" name="status" value="0" title="下架">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="submit">立即提交</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                <button type="button" class="layui-btn layui-btn-normal" id="back">返回</button>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- 视频预览弹窗模板 -->
<script type="text/html" id="videoPlayerTpl">
    <div style="padding: 20px; text-align: center;">
        <video id="videoPlayer" width="100%" height="400" controls>
            <source src="{{ d.playUrl }}" type="video/mp4">
            您的浏览器不支持HTML5视频
        </video>
    </div>
</script>

<script src="/static/admin/lay-module/xm-select.js"></script>
<script>
    layui.use(['form', 'upload', 'layer', 'laytpl'], function () {
        var $ = layui.$,
            form = layui.form,
            upload = layui.upload,
            layer = layui.layer,
            laytpl = layui.laytpl;

        // 初始化标签选择器
        var tagsStr = '{$tags}';
        var tags = JSON.parse(tagsStr.replace(/&quot;/g, '"'));
        var demo1 = xmSelect.render({
            el: '#demo1', 
            name: 'tags',
            data: tags
        });

        // 监听内容类型切换
        form.on('radio(content_type)', function (data) {
            if (data.value == 1) {
                $('.image-content').show();
                $('.video-content').hide();
            } else {
                $('.image-content').hide();
                $('.video-content').show();
                
                // 加载视频列表
                loadVideoList();
            }
        });

        // 加载视频列表
        function loadVideoList() {
            // 显示加载中提示
            var loadIndex = layer.load(1, {shade: [0.1, '#fff']});
            
            // 请求视频列表数据
            $.ajax({
                url: '{:url("video/getVideoList")}',
                type: 'post',
                dataType: 'json',
                success: function(res) {
                    layer.close(loadIndex);
                    if (res.code === 0 && res.data && res.data.length > 0) {
                        // 清空选项
                        $('#videoSelect').empty();
                        $('#videoSelect').append('<option value="">请选择视频</option>');
                        
                        // 添加视频选项
                        $.each(res.data, function(index, item) {
                            $('#videoSelect').append('<option value="' + item.videoVid + '" data-url="' + item.playUrl + '" data-name="' + item.videoName + '">' + item.videoName + '</option>');
                        });
                        
                        // 重新渲染表单
                        form.render('select');
                    } else {
                        layer.msg('未获取到视频数据', {icon: 2});
                    }
                },
                error: function() {
                    layer.close(loadIndex);
                    layer.msg('获取视频列表失败', {icon: 2});
                }
            });
        }

        // 监听视频选择
        form.on('select(videoSelect)', function(data) {
            if (data.value) {
                var $option = $(data.elem).find('option:selected');
                var videoName = $option.attr('data-name');
                var videoUrl = $option.attr('data-url');
                var videoVid = data.value;
                
                $('#selectedVideoName').val(videoName);
                $('#selectedVideoUrl').val(videoUrl);
                $('#selectedVideoVid').val(videoVid);
                $('#previewVideoBtn').show();
            } else {
                $('#selectedVideoName').val('');
                $('#selectedVideoUrl').val('');
                $('#selectedVideoVid').val('');
                $('#previewVideoBtn').hide();
            }
        });

        // 视频预览按钮点击事件
        $('#previewVideoBtn').on('click', function() {
            var videoUrl = $('#selectedVideoUrl').val();
            if (!videoUrl) {
                layer.msg('请先选择视频', {icon: 2});
                return;
            }
            
            // 打开预览弹窗
            laytpl($('#videoPlayerTpl').html()).render({
                playUrl: videoUrl
            }, function(html) {
                layer.open({
                    type: 1,
                    title: '视频预览',
                    area: ['800px', '500px'],
                    content: html,
                    success: function() {
                        document.getElementById('videoPlayer').play();
                    },
                    end: function() {
                        document.getElementById('videoPlayer').pause();
                    }
                });
            });
        });

        // 表单提交
        form.on('submit(submit)', function (data) {
            var field = data.field;
            
            // 处理标签数据
            if (!field['tags[]']) {
                field.tags = [];
            } else if (typeof field['tags[]'] === 'string') {
                field.tags = [field['tags[]']];
            } else {
                field.tags = field['tags[]'];
            }
            delete field['tags[]'];

            // 视频内容验证
            if (field.content_type == 2 && !field.video_url) {
                layer.msg('请选择视频', {icon: 2});
                return false;
            }

            // 删除不需要提交的字段
            delete field.video_select;
            
            // 提交数据
            var load = layer.load(2, {shade: [0.1, '#fff']});
            $.post("{:url('ajax_add_recipe')}", field, function (res) {
                layer.close(load);
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 1});
                    setTimeout(function () {
                        location.href = '{:url("recipe_list")}';
                    }, 1000);
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, "json");
            
            return false;
        });

        // 返回按钮
        $('#back').click(function () {
            location.href = '{:url("recipe_list")}';
        });
    });
</script>

</body>

</html> 