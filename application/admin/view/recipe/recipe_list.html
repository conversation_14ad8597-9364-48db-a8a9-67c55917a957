<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>审核商品</title>
    {include file="public/iframeheader"/}
    <!-- 编辑器源码文件 -->
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">食谱管理</div>
        <div class="layui-card-body">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">食谱标题</label>
                        <div class="layui-input-block">
                            <input type="text" name="title" placeholder="请输入食谱标题" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">分类</label>
                        <div class="layui-input-block">
                            <select name="category_id">
                                <option value="">全部</option>
                                {volist name="categories" id="category"}
                                <option value="{$category.id}">{$category.name}</option>
                                {/volist}
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">内容类型</label>
                        <div class="layui-input-block">
                            <select name="content_type">
                                <option value="">全部</option>
                                <option value="1">图文</option>
                                <option value="2">视频</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <select name="status">
                                <option value="">全部</option>
                                <option value="1">上架</option>
                                <option value="0">下架</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="LAY-app-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <div style="padding-bottom: 10px;">
                    <button class="layui-btn layuiadmin-btn-list" data-type="add">添加食谱</button>
                </div>
                <table id="LAY-app-list" lay-filter="LAY-app-list"></table>
                <script type="text/html" id="table-cover-image">
                    {{#  if(d.cover_image){ }}
                    <img src="{{d.cover_image}}" style="max-width: 50px; max-height: 50px;">
                    {{#  } else { }}
                    <span>无图片</span>
                    {{#  } }}
                </script>
                <script type="text/html" id="table-content-type">
                    {{#  if(d.content_type == 1){ }}
                    <span class="layui-badge layui-bg-blue">图文</span>
                    {{#  } else if(d.content_type == 2) { }}
                    <span class="layui-badge layui-bg-orange">视频</span>
                    {{#  } }}
                </script>
                <script type="text/html" id="table-difficulty">
                    {{#  if(d.difficulty == 1){ }}
                    <span class="layui-badge layui-bg-green">简单</span>
                    {{#  } else if(d.difficulty == 2) { }}
                    <span class="layui-badge layui-bg-blue">中等</span>
                    {{#  } else if(d.difficulty == 3) { }}
                    <span class="layui-badge layui-bg-red">困难</span>
                    {{#  } }}
                </script>
                <script type="text/html" id="table-status">
                    {{#  if(d.status == 1){ }}
                    <button class="layui-btn layui-btn-xs">上架</button>
                    {{#  } else { }}
                    <button class="layui-btn layui-btn-xs layui-btn-danger">下架</button>
                    {{#  } }}
                </script>
                <script type="text/html" id="table-operation">
                    <!-- <a class="layui-btn layui-btn-xs" lay-event="detail"><i class="layui-icon layui-icon-read"></i>详情</a> -->
                    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
                </script>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['table', 'form'], function () {
        var $ = layui.$,
            form = layui.form,
            table = layui.table;

        // 表格渲染
        table.render({
            elem: '#LAY-app-list',
            url: '{:url("ajax_recipe_list")}',
            method: 'post',
            page: true,
            limit: 10,
            limits: [10, 20, 50, 100],
            cols: [[
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'cover_image', title: '封面图', width: 100, templet: '#table-cover-image'},
                {field: 'title', title: '食谱标题', minWidth: 160},
                {field: 'category_name', title: '分类', width: 120},
                {field: 'content_type', title: '内容类型', width: 100, templet: '#table-content-type'},
                {field: 'views', title: '浏览次数', width: 100},
                // {field: 'likes', title: '点赞次数', width: 100},
                // {field: 'favorites', title: '收藏次数', width: 100},
                {field: 'status', title: '状态', width: 80, templet: '#table-status'},
                {field: 'create_time', title: '创建时间', width: 180},
                {title: '操作', width: 200, align: 'center', fixed: 'right', toolbar: '#table-operation'}
            ]]
        });

        // 监听搜索
        form.on('submit(LAY-app-search)', function (data) {
            var field = data.field;
            // 执行重载
            table.reload('LAY-app-list', {
                where: field,
                page: {
                    curr: 1 // 重新从第 1 页开始
                }
            });
            return false;
        });

        // 事件
        var active = {
            add: function () {
                location.href = '{:url("add_recipe")}';
            }
        };

        $('.layui-btn.layuiadmin-btn-list').on('click', function () {
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

        // 监听工具条
        table.on('tool(LAY-app-list)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                layer.confirm('确定删除此食谱？', function (index) {
                    $.ajax({
                        url: '{:url("del_recipe")}',
                        type: 'post',
                        data: {id: data.id},
                        success: function (res) {
                            if (res.code === 0) {
                                layer.msg(res.msg, {icon: 1});
                                obj.del();
                                layer.close(index);
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        },
                        dataType: 'json',
                        error: function () {
                            layer.msg('网络错误，请稍后重试', {icon: 2});
                        }
                    });
                });
            } else if (obj.event === 'edit') {
                location.href = '{:url("edit_recipe")}?id=' + data.id;
            } else if (obj.event === 'detail') {
                location.href = '{:url("recipe_detail")}?id=' + data.id;
            }
        });
    });
</script>
</body>
</html>
