<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>食谱详情</title>
    {include file="public/iframeheader"/}
</head>

<body>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">食谱详情</div>
                <div class="layui-card-body" pad15>

                    <div class="layui-form" wid100 lay-filter="">
                        <div class="layui-row layui-col-space20">
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">食谱ID</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-mid">{$recipe.id}</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">食谱标题</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-mid">{$recipe.title}</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">所属分类</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-mid">{$recipe.category_name}</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">内容类型</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-mid">
                                            {if $recipe.content_type eq 1}
                                            <span class="layui-badge layui-bg-blue">图文</span>
                                            {else}
                                            <span class="layui-badge layui-bg-orange">视频</span>
                                            {/if}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="layui-form-item">
                                    <label class="layui-form-label">浏览量</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-mid">{$recipe.views}</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">点赞数</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-mid">{$recipe.likes}</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">收藏数</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-mid">{$recipe.favorites}</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">状态</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-mid">
                                            {if $recipe.status eq 1}
                                            <span class="layui-badge layui-bg-green">上架</span>
                                            {else}
                                            <span class="layui-badge layui-bg-gray">下架</span>
                                            {/if}
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">创建时间</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-mid">{$recipe.create_time}</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">更新时间</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-mid">{$recipe.update_time}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">封面图</label>
                                    <div class="layui-input-block">
                                        <img src="{$recipe.cover_image}" style="max-width: 300px; max-height: 300px;">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">食谱描述</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$recipe.description}</div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">标签</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">
                                    {volist name="recipe_tags" id="tag"}
                                    <span class="layui-badge layui-bg-cyan" style="margin-right: 5px;">{$tag.name}</span>
                                    {/volist}
                                </div>
                            </div>
                        </div>

                        {if $recipe.content_type eq 2}
                        <!-- 视频内容 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">视频链接</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">
                                    <a href="{$recipe.video_url}" target="_blank">{$recipe.video_url}</a>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">视频时长</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$recipe.video_duration} 秒</div>
                            </div>
                        </div>
                        {else}
                        <!-- 图文内容 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">图片展示</label>
                            <div class="layui-input-block">
                                <div class="layui-row layui-col-space10">
                                    {volist name="recipe_images" id="image"}
                                    <div class="layui-col-md4">
                                        <div class="layui-card">
                                            <div class="layui-card-header">排序：{$image.sort}</div>
                                            <div class="layui-card-body">
                                                <img src="{$image.image_url}" style="max-width: 100%;">
                                                <p style="margin-top: 10px;">{$image.description}</p>
                                            </div>
                                        </div>
                                    </div>
                                    {/volist}
                                </div>
                            </div>
                        </div>
                        {/if}

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="button" class="layui-btn layui-btn-normal" id="edit">编辑</button>
                                <button type="button" class="layui-btn layui-btn-primary" id="back">返回</button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['layer'], function () {
        var $ = layui.$,
            layer = layui.layer;

        // 编辑按钮
        $('#edit').click(function () {
            location.href = '{:url("edit_recipe", ["id" => $recipe.id])}';
        });

        // 返回按钮
        $('#back').click(function () {
            location.href = '{:url("recipe_list")}';
        });
    });
</script>

</body>

</html> 