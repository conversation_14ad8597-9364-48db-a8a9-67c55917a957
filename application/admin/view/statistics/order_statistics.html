{include file="public/iframeheader"/}
<!--
 * 订单统计视图
 * 
 * 功能说明：
 * 1. 展示订单的统计数据，包括数量、状态、支付方式等
 * 2. 展示最近7天订单增长趋势图
 * 3. 展示店铺订单数量TOP10排行榜
 * 4. 展示订单金额和配送方式统计
 * 
 * 数据来源：ajax_order_statistics方法
 * 
 * <AUTHOR>
 * @date 2023-03-06
-->

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">订单统计</div>
                <div class="layui-card-body">
                    
                    <!-- 筛选条件选择器 -->
                    <div class="layui-form layui-form-pane" style="margin-bottom: 15px;">
                        <div class="layui-form-item">
                            <label class="layui-form-label">时间范围</label>
                            <div class="layui-inline">
                                <div class="layui-input-inline" style="width: 140px;">
                                    <input type="text" class="layui-input" id="start_date" placeholder="开始日期">
                                </div>
                                <div class="layui-form-mid">-</div>
                                <div class="layui-input-inline" style="width: 140px;">
                                    <input type="text" class="layui-input" id="end_date" placeholder="结束日期">
                                </div>
                            </div>
                            <div class="layui-inline" style="margin-left: 20px;">
                                <span class="layui-badge layui-bg-blue" id="quick_select_today">今天</span>
                                <span class="layui-badge layui-bg-green" id="quick_select_7">最近7天</span>
                                <span class="layui-badge layui-bg-orange" id="quick_select_30">最近30天</span>
                                <span class="layui-badge layui-bg-red" id="quick_select_180">最近180天</span>
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">门店筛选</label>
                            <div class="layui-inline">
                                <div class="layui-input-inline" style="width: 200px;">
                                    <select id="shop_filter" lay-filter="shop_filter">
                                        <option value="0">全部门店</option>
                                        <!-- 动态加载门店选项 -->
                                    </select>
                                </div>
                            </div>
                            
                            <label class="layui-form-label" style="margin-left: 20px;">配送方式</label>
                            <div class="layui-inline">
                                <div class="layui-input-inline" style="width: 150px;">
                                    <select id="delivery_type_filter" lay-filter="delivery_type_filter">
                                        <option value="0">全部方式</option>
                                        <option value="1">自提</option>
                                        <option value="2">配送</option>
                                        <option value="3">结算台</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="layui-inline" style="margin-left: 20px;">
                                <button type="button" class="layui-btn" id="query_btn">查询</button>
                                <button type="button" class="layui-btn layui-btn-primary" id="reset_btn">重置</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据概览 -->
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-header">订单总数</div>
                                <div class="layui-card-body layuiadmin-card-list">
                                    <p class="layuiadmin-big-font" id="order_count">0</p>
                                    <p>总金额：<span class="layuiadmin-span-color" id="total_order_amount">0</span>元</p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-header">待支付订单</div>
                                <div class="layui-card-body layuiadmin-card-list">
                                    <p class="layuiadmin-big-font" id="pending_payment_count">0</p>
                                    <p>待审核订单：<span class="layuiadmin-span-color" id="pending_review_count">0</span></p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-header">已接单</div>
                                <div class="layui-card-body layuiadmin-card-list">
                                    <p class="layuiadmin-big-font" id="accepted_count">0</p>
                                    <p>配送中：<span class="layuiadmin-span-color" id="delivering_count">0</span></p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-header">已完成订单</div>
                                <div class="layui-card-body layuiadmin-card-list">
                                    <p class="layuiadmin-big-font" id="completed_count">0</p>
                                    <p>已取消：<span class="layuiadmin-span-color" id="cancelled_count">0</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 支付方式和配送方式 -->
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md6">
                            <div class="layui-card">
                                <div class="layui-card-header">支付方式统计</div>
                                <div class="layui-card-body">
                                    <div id="payment_type_chart" style="width: 100%; height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-card">
                                <div class="layui-card-header">配送方式统计</div>
                                <div class="layui-card-body">
                                    <div id="delivery_type_chart" style="width: 100%; height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 趋势图 -->
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header">最近7天订单趋势</div>
                                <div class="layui-card-body">
                                    <div id="order_trend_chart" style="width: 100%; height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 金额统计 -->
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header">订单金额统计</div>
                                <div class="layui-card-body">
                                    <div class="layui-row">
                                        <div class="layui-col-md2">
                                            <div class="layui-card">
                                                <div class="layui-card-header">订单总金额</div>
                                                <div class="layui-card-body">
                                                    <h2 id="total_order_amount_big">0</h2>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md2">
                                            <div class="layui-card">
                                                <div class="layui-card-header">微信支付金额</div>
                                                <div class="layui-card-body">
                                                    <h2 id="total_wechat_amount">0</h2>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md2">
                                            <div class="layui-card">
                                                <div class="layui-card-header">卡支付金额</div>
                                                <div class="layui-card-body">
                                                    <h2 id="total_card_amount">0</h2>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md2">
                                            <div class="layui-card">
                                                <div class="layui-card-header">线下支付金额</div>
                                                <div class="layui-card-body">
                                                    <h2 id="total_cash_amount">0</h2>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md2">
                                            <div class="layui-card">
                                                <div class="layui-card-header">运费总金额</div>
                                                <div class="layui-card-body">
                                                    <h2 id="total_shipping_amount">0</h2>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 排行榜 -->
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md6">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    店铺订单数量排行
                                    <span class="layui-badge layui-bg-blue" id="rank_display_info">TOP10</span>
                                    <div class="layui-inline" style="float: right;">
                                        <button type="button" class="layui-btn layui-btn-sm" id="view_more_btn">查看更多</button>
                                    </div>
                                </div>
                                <div class="layui-card-body">
                                    <table class="layui-table" lay-skin="line">
                                        <colgroup>
                                            <col width="75">
                                            <col>
                                            <col width="100">
                                            <col width="150">
                                        </colgroup>
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>店铺名称</th>
                                                <th>订单数量</th>
                                                <th>订单金额</th>
                                            </tr>
                                        </thead>
                                        <tbody id="shop_order_rank_body">
                                            <!-- 动态填充 -->
                                        </tbody>
                                    </table>
                                    
                                    <!-- 分页 -->
                                    <div id="shop_rank_page" class="layui-hide"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-col-md6">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    商品订单数量排行
                                    <span class="layui-badge layui-bg-orange" id="product_rank_display_info">TOP10</span>
                                    <div class="layui-inline" style="float: right;">
                                        <button type="button" class="layui-btn layui-btn-sm" id="view_more_product_btn">查看更多</button>
                                    </div>
                                </div>
                                <div class="layui-card-body">
                                    <table class="layui-table" lay-skin="line">
                                        <colgroup>
                                            <col width="75">
                                            <col width="60">
                                            <col>
                                            <col width="100">
                                            <col width="100">
                                        </colgroup>
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>图片</th>
                                                <th>商品名称</th>
                                                <th>订单数量</th>
                                                <th>销售数量</th>
                                            </tr>
                                        </thead>
                                        <tbody id="product_order_rank_body">
                                            <!-- 动态填充 -->
                                        </tbody>
                                    </table>
                                    
                                    <!-- 分页 -->
                                    <div id="product_rank_page" class="layui-hide"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__STATIC__/admin/layui/layui.js"></script>
<script src="__STATIC__/admin/lib/extend/echarts.js"></script>
<script>
    layui.use(['jquery', 'layer', 'form', 'laypage', 'laydate'], function(){
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var laypage = layui.laypage;
        var laydate = layui.laydate;
        
        // 当前选择的开始和结束日期
        var currentStartDate = '';
        var currentEndDate = '';
        
        // 当前筛选条件
        var currentShopId = 0;
        var currentDeliveryType = 0;
        
        // 当前店铺排行显示数量
        var currentShopLimit = 10;
        
        // 当前商品排行显示数量
        var currentProductLimit = 10;
        
        // 是否显示全部店铺
        var isShowAllShops = false;
        
        // 是否显示全部商品
        var isShowAllProducts = false;
        
        // 初始化日期选择器
        laydate.render({
            elem: '#start_date',
            type: 'date',
            format: 'yyyy-MM-dd',
            max: 0, // 不能选择今天之后的日期
            done: function(value, date) {
                currentStartDate = value;
                // 更新结束日期的最小值
                laydate.render({
                    elem: '#end_date',
                    type: 'date',
                    format: 'yyyy-MM-dd',
                    min: value,
                    max: 0
                });
            }
        });
        
        laydate.render({
            elem: '#end_date',
            type: 'date',
            format: 'yyyy-MM-dd',
            max: 0, // 不能选择今天之后的日期
            done: function(value, date) {
                currentEndDate = value;
                // 更新开始日期的最大值
                laydate.render({
                    elem: '#start_date',
                    type: 'date',
                    format: 'yyyy-MM-dd',
                    max: value
                });
            }
        });
        
        // 加载门店列表
        function loadShopList() {
            $.ajax({
                url: "{:url('statistics/ajax_get_shops')}",
                type: 'post',
                dataType: 'json',
                success: function(res) {
                    var options = '<option value="0">全部门店</option>';
                    if (res.shops && res.shops.length > 0) {
                        $.each(res.shops, function(index, shop) {
                            options += '<option value="' + shop.id + '">' + shop.title + '</option>';
                        });
                    }
                    $('#shop_filter').html(options);
                    form.render('select'); // 重新渲染select
                },
                error: function() {
                    console.error('加载门店列表失败');
                }
            });
        }

        // 查询按钮点击事件
        $('#query_btn').on('click', function(){
            if (!currentStartDate || !currentEndDate) {
                layer.msg('请选择开始日期和结束日期', {icon: 2});
                return;
            }
            
            // 获取当前筛选条件
            currentShopId = parseInt($('#shop_filter').val()) || 0;
            currentDeliveryType = parseInt($('#delivery_type_filter').val()) || 0;
            
            // 重置店铺显示数量
            currentShopLimit = 10;
            isShowAllShops = false;
            updateRankDisplayInfo();
            
            // 重置商品显示数量
            currentProductLimit = 10;
            isShowAllProducts = false;
            updateProductRankDisplayInfo();
            
            // 加载数据
            loadStatisticsData(currentStartDate, currentEndDate, currentShopLimit, currentProductLimit);
        });
        
        // 重置按钮点击事件
        $('#reset_btn').on('click', function(){
            $('#start_date').val('');
            $('#end_date').val('');
            currentStartDate = '';
            currentEndDate = '';
            
            // 重置筛选条件
            $('#shop_filter').val('0');
            $('#delivery_type_filter').val('0');
            currentShopId = 0;
            currentDeliveryType = 0;
            form.render('select'); // 重新渲染select
            
            // 设置默认为最近7天
            setQuickSelectRange(7);
        });
        
        // 快速选择时间范围
        function setQuickSelectRange(days) {
            var endDate = new Date();
            var startDate = new Date();
            
            if (days === 'today') {
                // 今天
                startDate = endDate;
            } else {
                // 往前推指定天数
                startDate.setDate(endDate.getDate() - days + 1);
            }
            
            var startDateStr = formatDate(startDate);
            var endDateStr = formatDate(endDate);
            
            $('#start_date').val(startDateStr);
            $('#end_date').val(endDateStr);
            currentStartDate = startDateStr;
            currentEndDate = endDateStr;
            
            // 获取当前筛选条件
            currentShopId = parseInt($('#shop_filter').val()) || 0;
            currentDeliveryType = parseInt($('#delivery_type_filter').val()) || 0;
            
            // 重置显示数量
            currentShopLimit = 10;
            isShowAllShops = false;
            updateRankDisplayInfo();
            
            currentProductLimit = 10;
            isShowAllProducts = false;
            updateProductRankDisplayInfo();
            
            // 加载数据
            loadStatisticsData(currentStartDate, currentEndDate, currentShopLimit, currentProductLimit);
        }
        
        // 格式化日期
        function formatDate(date) {
            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0');
            var day = String(date.getDate()).padStart(2, '0');
            return year + '-' + month + '-' + day;
        }
        
        // 快速选择按钮事件
        $('#quick_select_today').on('click', function(){
            setQuickSelectRange('today');
        });
        
        $('#quick_select_7').on('click', function(){
            setQuickSelectRange(7);
        });
        
        $('#quick_select_30').on('click', function(){
            setQuickSelectRange(30);
        });
        
        $('#quick_select_180').on('click', function(){
            setQuickSelectRange(180);
        });
        
        // 监听查看更多按钮点击
        $('#view_more_btn').on('click', function(){
            if (!currentStartDate || !currentEndDate) {
                layer.msg('请先选择时间范围', {icon: 2});
                return;
            }
            
            if (!isShowAllShops) {
                // 切换到查看全部
                isShowAllShops = true;
                currentShopLimit = 1000; // 设置一个较大的值，相当于查看全部
                $(this).text('返回TOP10');
                updateRankDisplayInfo();
                loadStatisticsData(currentStartDate, currentEndDate, currentShopLimit);
            } else {
                // 切换回TOP10
                isShowAllShops = false;
                currentShopLimit = 10;
                $(this).text('查看更多');
                updateRankDisplayInfo();
                loadStatisticsData(currentStartDate, currentEndDate, currentShopLimit);
            }
        });
        
        // 监听查看更多商品按钮点击
        $('#view_more_product_btn').on('click', function(){
            if (!currentStartDate || !currentEndDate) {
                layer.msg('请先选择时间范围', {icon: 2});
                return;
            }
            
            if (!isShowAllProducts) {
                // 切换到查看全部
                isShowAllProducts = true;
                currentProductLimit = 1000; // 设置一个较大的值，相当于查看全部
                $(this).text('返回TOP10');
                updateProductRankDisplayInfo();
                loadProductStatisticsData(currentStartDate, currentEndDate, currentProductLimit);
            } else {
                // 切换回TOP10
                isShowAllProducts = false;
                currentProductLimit = 10;
                $(this).text('查看更多');
                updateProductRankDisplayInfo();
                loadProductStatisticsData(currentStartDate, currentEndDate, currentProductLimit);
            }
        });
        
        // 更新排行显示信息
        function updateRankDisplayInfo() {
            if (isShowAllShops) {
                $('#rank_display_info').text('全部店铺');
            } else {
                $('#rank_display_info').text('TOP10');
            }
        }
        
        // 更新商品排行显示信息
        function updateProductRankDisplayInfo() {
            if (isShowAllProducts) {
                $('#product_rank_display_info').text('全部商品');
            } else {
                $('#product_rank_display_info').text('TOP10');
            }
        }
        
        // 加载统计数据
        function loadStatisticsData(startDate, endDate, shopLimit, productLimit) {
            layer.load(2);
            $.ajax({
                url: "{:url('statistics/ajax_order_statistics')}",
                type: 'post',
                dataType: 'json',
                data: {
                    start_date: startDate,
                    end_date: endDate,
                    shop_id: currentShopId,
                    delivery_type: currentDeliveryType,
                    shop_limit: shopLimit || 10,
                    product_limit: productLimit || 10
                },
                success: function(res) {
                    layer.closeAll('loading');
                    
                    // 更新数据概览
                    $('#order_count').text(res.order_count || 0);
                    $('#pending_payment_count').text(res.pending_payment_count || 0);
                    $('#pending_review_count').text(res.pending_review_count || 0);
                    $('#accepted_count').text(res.accepted_count || 0);
                    $('#delivering_count').text(res.delivering_count || 0);
                    $('#completed_count').text(res.completed_count || 0);
                    $('#cancelled_count').text(res.cancelled_count || 0);
                    
                    // 更新金额统计
                    var totalOrderAmount = parseFloat(res.total_order_amount || 0).toFixed(2);
                    var totalWechatAmount = parseFloat(res.total_wechat_amount || 0).toFixed(2);
                    var totalCardAmount = parseFloat(res.total_card_amount || 0).toFixed(2);
                    var totalCashAmount = parseFloat(res.total_cash_amount || 0).toFixed(2);
                    var totalShippingAmount = parseFloat(res.total_shipping_amount || 0).toFixed(2);
                    
                    $('#total_order_amount').text(totalOrderAmount);
                    $('#total_order_amount_big').text(totalOrderAmount + '元');
                    $('#total_wechat_amount').text(totalWechatAmount + '元');
                    $('#total_card_amount').text(totalCardAmount + '元');
                    $('#total_cash_amount').text(totalCashAmount + '元');
                    $('#total_shipping_amount').text(totalShippingAmount + '元');
                    
                    // 渲染趋势图
                    renderTrendChart(res.recent_days, res.recent_order_trend, startDate, endDate);
                    
                    // 渲染支付方式图表
                    renderPaymentTypeChart(res.wechat_payment_count, res.card_payment_count, res.combined_payment_count, res.cash_payment_count);
                    
                    // 渲染配送方式图表
                    renderDeliveryTypeChart(res.self_pickup_count, res.delivery_count, res.checkout_count);
                    
                    // 渲染店铺排行
                    renderShopOrderRank(res.shop_order_rank);
                    
                    // 如果显示全部店铺且店铺数量超过一定值，显示分页
                    if (isShowAllShops && res.shop_order_rank && res.shop_order_rank.length > 20) {
                        renderShopRankPagination(res.shop_order_rank);
                    } else {
                        $('#shop_rank_page').addClass('layui-hide');
                    }
                    
                    // 渲染商品排行
                    renderProductOrderRank(res.product_order_rank);
                    
                    // 如果显示全部商品且商品数量超过一定值，显示分页
                    if (isShowAllProducts && res.product_order_rank && res.product_order_rank.length > 20) {
                        renderProductRankPagination(res.product_order_rank);
                    } else {
                        $('#product_rank_page').addClass('layui-hide');
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('加载数据失败，请刷新重试', {icon: 2});
                }
            });
        }
        
        // 渲染趋势图
        function renderTrendChart(days, data, startDate, endDate) {
            var chartDom = document.getElementById('order_trend_chart');
            var myChart = echarts.init(chartDom);
            
            // 根据日期范围设置图表标题
            var title = '';
            if (startDate && endDate) {
                if (startDate === endDate) {
                    title = startDate + ' 订单趋势';
                } else {
                    title = startDate + ' 至 ' + endDate + ' 订单趋势';
                }
            } else {
                title = '订单趋势';
            }
            
            var option = {
                title: {
                    text: title,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['订单数量'],
                    top: '30px'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: days || []
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '订单数量',
                        type: 'line',
                        stack: '总量',
                        data: data || [],
                        areaStyle: {},
                        itemStyle: {
                            color: '#1E9FFF'
                        }
                    }
                ]
            };
            myChart.setOption(option);
            
            // 窗口大小变化时，重新调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }
        
        // 渲染支付方式图表
        function renderPaymentTypeChart(wechatCount, cardCount, combinedCount, cashCount) {
            var chartDom = document.getElementById('payment_type_chart');
            var myChart = echarts.init(chartDom);
            var option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 2,
                    data: ['微信支付', '卡支付', '组合支付', '线下支付']
                },
                label: {
                    show: false,
                    position: 'center'
                },
                series: [
                    {
                        name: '支付方式',
                        type: 'pie',
                        radius: ['50%', '70%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'left'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            {value: wechatCount || 0, name: '微信支付'},
                            {value: cardCount || 0, name: '卡支付'},
                            {value: combinedCount || 0, name: '组合支付'},
                            {value: cashCount || 0, name: '线下支付'}
                        ]
                    }
                ]
            };
            myChart.setOption(option);
            
            // 窗口大小变化时，重新调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }
        
        // 渲染配送方式图表
        function renderDeliveryTypeChart(selfPickupCount, deliveryCount, checkoutCount) {
            var chartDom = document.getElementById('delivery_type_chart');
            var myChart = echarts.init(chartDom);
            var option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 10,
                    data: ['自提', '配送', '结算台']
                },

                label: {
                    show: false,
                    position: 'center'
                },
                series: [
                    {
                        name: '配送方式',
                        type: 'pie',
                        radius: ['50%', '70%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            {value: selfPickupCount || 0, name: '自提'},
                            {value: deliveryCount || 0, name: '配送'},
                            {value: checkoutCount || 0, name: '结算台'}
                        ]
                    }
                ]
            };
            myChart.setOption(option);
            
            // 窗口大小变化时，重新调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }
        
        // 渲染店铺排行
        function renderShopOrderRank(data) {
            var html = '';
            if (data && data.length > 0) {
                // 如果数据量大，只显示前20条，其余通过分页显示
                var displayData = data;
                if (isShowAllShops && data.length > 20) {
                    displayData = data.slice(0, 20);
                }
                
                $.each(displayData, function(index, item) {
                    html += '<tr>';
                    html += '<td>' + (index + 1) + '</td>';
                    html += '<td>' + item.title + '</td>';
                    html += '<td>' + item.count + '</td>';
                    html += '<td>' + parseFloat(item.amount || 0).toFixed(2) + '元</td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="4" style="text-align:center;">暂无数据</td></tr>';
            }
            $('#shop_order_rank_body').html(html);
        }
        
        // 渲染店铺排行分页
        function renderShopRankPagination(data) {
            $('#shop_rank_page').removeClass('layui-hide');
            
            // 计算总页数
            var totalPages = Math.ceil(data.length / 20);
            
            // 渲染分页
            laypage.render({
                elem: 'shop_rank_page',
                count: data.length,
                limit: 20,
                layout: ['prev', 'page', 'next', 'count', 'limit', 'skip'],
                jump: function(obj, first) {
                    if (!first) {
                        // 非首次渲染，根据页码显示对应数据
                        var startIndex = obj.curr * obj.limit - obj.limit;
                        var endIndex = Math.min(startIndex + obj.limit, data.length);
                        var pageData = data.slice(startIndex, endIndex);
                        
                        var html = '';
                        $.each(pageData, function(index, item) {
                            html += '<tr>';
                            html += '<td>' + (startIndex + index + 1) + '</td>';
                            html += '<td>' + item.title + '</td>';
                            html += '<td>' + item.count + '</td>';
                            html += '<td>' + parseFloat(item.amount || 0).toFixed(2) + '元</td>';
                            html += '</tr>';
                        });
                        $('#shop_order_rank_body').html(html);
                    }
                }
            });
        }
        
        // 渲染商品排行
        function renderProductOrderRank(data) {
            var html = '';
            if (data && data.length > 0) {
                // 如果数据量大，只显示前20条，其余通过分页显示
                var displayData = data;
                if (isShowAllProducts && data.length > 20) {
                    displayData = data.slice(0, 20);
                }
                
                $.each(displayData, function(index, item) {
                    html += '<tr>';
                    html += '<td>' + (index + 1) + '</td>';
                    html += '<td><img src="' + item.cover + '" style="width: 50px; height: 50px;" onerror="this.src=\'__STATIC__/admin/images/nopic.jpg\'"></td>';
                    html += '<td>' + item.title + '</td>';
                    html += '<td>' + item.count + '</td>';
                    html += '<td>' + item.total_number + '</td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="5" style="text-align:center;">暂无数据</td></tr>';
            }
            $('#product_order_rank_body').html(html);
        }
        
        // 渲染商品排行分页
        function renderProductRankPagination(data) {
            $('#product_rank_page').removeClass('layui-hide');
            
            // 计算总页数
            var totalPages = Math.ceil(data.length / 20);
            
            // 渲染分页
            laypage.render({
                elem: 'product_rank_page',
                count: data.length,
                limit: 20,
                layout: ['prev', 'page', 'next', 'count', 'limit', 'skip'],
                jump: function(obj, first) {
                    if (!first) {
                        // 非首次渲染，根据页码显示对应数据
                        var startIndex = obj.curr * obj.limit - obj.limit;
                        var endIndex = Math.min(startIndex + obj.limit, data.length);
                        var pageData = data.slice(startIndex, endIndex);
                        
                        var html = '';
                        $.each(pageData, function(index, item) {
                            html += '<tr>';
                            html += '<td>' + (startIndex + index + 1) + '</td>';
                            html += '<td><img src="' + item.cover + '" style="width: 50px; height: 50px;" onerror="this.src=\'__STATIC__/admin/images/nopic.jpg\'"></td>';
                            html += '<td>' + item.title + '</td>';
                            html += '<td>' + item.count + '</td>';
                            html += '<td>' + item.total_number + '</td>';
                            html += '</tr>';
                        });
                        $('#product_order_rank_body').html(html);
                    }
                }
            });
        }
        
        // 加载商品统计数据
        function loadProductStatisticsData(startDate, endDate, productLimit) {
            layer.load(2);
            $.ajax({
                url: "{:url('statistics/ajax_order_statistics')}",
                type: 'post',
                dataType: 'json',
                data: {
                    start_date: startDate,
                    end_date: endDate,
                    shop_id: currentShopId,
                    delivery_type: currentDeliveryType,
                    shop_limit: currentShopLimit,
                    product_limit: productLimit || 10
                },
                success: function(res) {
                    layer.closeAll('loading');
                    
                    // 渲染商品排行
                    renderProductOrderRank(res.product_order_rank);
                    
                    // 如果显示全部商品且商品数量超过一定值，显示分页
                    if (isShowAllProducts && res.product_order_rank && res.product_order_rank.length > 20) {
                        renderProductRankPagination(res.product_order_rank);
                    } else {
                        $('#product_rank_page').addClass('layui-hide');
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('加载数据失败，请刷新重试', {icon: 2});
                }
            });
        }
        
        // 页面加载完成后，加载统计数据
        $(function() {
            // 初始化表单
            form.render();
            
            // 加载门店列表
            loadShopList();
            
            // 设置默认为最近7天并加载数据
            setQuickSelectRange(7);
        });
    });
</script> 