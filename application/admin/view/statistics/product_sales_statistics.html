{include file="public/iframeheader"/}
<!--
 * 商品销量统计视图
 * 
 * 功能说明：
 * 1. 展示商品销量统计数据，支持按时间、门店、商品类型筛选
 * 2. 展示销量排行榜
 * 3. 支持普通商品和计量商品的销量统计
 * 
 * <AUTHOR>
 * @date 2023-03-06
-->

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">商品销量统计</div>
                <div class="layui-card-body">
                    
                    <!-- 筛选条件选择器 -->
                    <div class="layui-form layui-form-pane" style="margin-bottom: 15px;">
                        <!-- 时间范围选择 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">时间范围</label>
                            <div class="layui-inline">
                                <div class="layui-input-inline" style="width: 140px;">
                                    <input type="text" class="layui-input" id="start_date" placeholder="开始日期">
                                </div>
                                <div class="layui-form-mid">-</div>
                                <div class="layui-input-inline" style="width: 140px;">
                                    <input type="text" class="layui-input" id="end_date" placeholder="结束日期">
                                </div>
                            </div>
                        </div>
                        
                        <!-- 筛选条件 -->
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">门店筛选</label>
                                <div class="layui-input-inline" style="width: 200px;">
                                    <select id="shop_filter" lay-filter="shop_filter">
                                        <option value="0">全部门店</option>
                                        {foreach $shop_list as $shop}
                                        <option value="{$shop.id}">{$shop.title}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="layui-inline">
                                <label class="layui-form-label">商品类型</label>
                                <div class="layui-input-inline" style="width: 150px;">
                                    <select id="product_type_filter" lay-filter="product_type_filter">
                                        <option value="0">全部商品</option>
                                        <option value="1">普通商品</option>
                                        <option value="2">计量商品</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="layui-inline">
                                <label class="layui-form-label">关键词</label>
                                <div class="layui-input-inline" style="width: 200px;">
                                    <input type="text" id="keyword" placeholder="商品名称关键词" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-inline">
                                <button type="button" class="layui-btn" id="query_btn">查询</button>
                                <button type="button" class="layui-btn layui-btn-primary" id="reset_btn">重置</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 销量统计卡片 -->
                    <div class="layui-row layui-col-space15" style="margin-bottom: 15px;">
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-header">总销量</div>
                                <div class="layui-card-body" style="height: 80px;">
                                    <h2 id="total_sales" style="font-size: 24px; font-weight: bold; text-align: center; margin-top: 15px;">0</h2>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-header">普通商品销量</div>
                                <div class="layui-card-body" style="height: 80px;">
                                    <h2 id="normal_sales" style="font-size: 24px; font-weight: bold; text-align: center; margin-top: 15px; color: #1E9FFF;">0</h2>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-header">计量商品销量</div>
                                <div class="layui-card-body" style="height: 80px;">
                                    <h2 id="measure_sales" style="font-size: 24px; font-weight: bold; text-align: center; margin-top: 15px; color: #009688;">0</h2>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-header">赠品销量</div>
                                <div class="layui-card-body" style="height: 80px;">
                                    <h2 id="gift_sales" style="font-size: 24px; font-weight: bold; text-align: center; margin-top: 15px; color: #FFB800;">0</h2>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 商品销量排行表格 -->
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header">商品销量排行</div>
                                <div class="layui-card-body">
                                    <table id="product_sales_table" lay-filter="product_sales_table"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__STATIC__/admin/layui/layui.js"></script>
<script type="text/html" id="salesRankTpl">
    <span class="layui-badge layui-bg-orange">第{{d.LAY_TABLE_INDEX+1}}名</span>
</script>

<script type="text/html" id="salesDisplayTpl">
    {{# if(d.product_type == 2){ }}
        <span>{{d.sales_display}}</span>
    {{# }else{ }}
        <span>{{d.sales_display}}</span>
    {{# } }}
</script>

<script type="text/html" id="productTypeTpl">
    {{# if(d.product_type == 2){ }}
        <span class="layui-badge layui-bg-green">计量商品</span>
    {{# }else if(d.product_type == 1){ }}
        <span class="layui-badge layui-bg-blue">普通商品</span>
    {{# } else if(d.product_type == 3){ }}
        <span class="layui-badge layui-bg-gray">赠品</span>
    {{# } }}
</script>

<script>
    layui.use(['table', 'form', 'laydate', 'element'], function(){
        var table = layui.table;
        var form = layui.form;
        var laydate = layui.laydate;
        var element = layui.element;
        
        // 初始化日期选择器
        var startDate = laydate.render({
            elem: '#start_date',
            type: 'date',
            value: getDateStr(-30)
        });
        
        var endDate = laydate.render({
            elem: '#end_date',
            type: 'date',
            value: getDateStr(0)
        });
        
        // 快速选择日期
        $('#quick_select_today').on('click', function(){
            startDate.setValue(getDateStr(0));
            endDate.setValue(getDateStr(0));
        });
        
        $('#quick_select_7').on('click', function(){
            startDate.setValue(getDateStr(-7));
            endDate.setValue(getDateStr(0));
        });
        
        $('#quick_select_30').on('click', function(){
            startDate.setValue(getDateStr(-30));
            endDate.setValue(getDateStr(0));
        });
        
        $('#quick_select_180').on('click', function(){
            startDate.setValue(getDateStr(-180));
            endDate.setValue(getDateStr(0));
        });
        
        // 初始化表格
        var productSalesTable = table.render({
            elem: '#product_sales_table',
            url: '{:url("ajax_product_sales_statistics")}',
            page: true,
            limit: 10,
            limits: [10, 20, 50, 100],
            cols: [[
                {field: 'rank', title: '排名', width: 80, templet: '#salesRankTpl', align: 'center'},
                {field: 'product_title', title: '商品名称'},
                {field: 'inventory_title', title: '规格名称'},
                {field: 'product_type_text', title: '商品类型', templet: '#productTypeTpl', align: 'center'},
                {field: 'sales_display', title: '销量', templet: '#salesDisplayTpl', align: 'center'},
                {field: 'total_amount', title: '销售金额(元)', align: 'center'},
                {field: 'order_count', title: '订单数', align: 'center'}
            ]]
        });
        
        // 查询按钮事件
        $('#query_btn').on('click', function(){
            reloadTable();
            loadSalesStatistics();
        });
        
        // 重置按钮事件
        $('#reset_btn').on('click', function(){
            $('#start_date').val(getDateStr(-30));
            $('#end_date').val(getDateStr(0));
            $('#shop_filter').val(0);
            $('#product_type_filter').val(0);
            $('#keyword').val('');
            form.render('select');
            reloadTable();
            loadSalesStatistics();
        });
        
        // 重新加载表格
        function reloadTable(){
            productSalesTable.reload({
                page: {
                    curr: 1
                },
                where: {
                    start_date: $('#start_date').val(),
                    end_date: $('#end_date').val(),
                    shop_id: $('#shop_filter').val(),
                    product_type: $('#product_type_filter').val(),
                    keyword: $('#keyword').val()
                }
            });
        }
        
        // 加载销量统计数据
        function loadSalesStatistics() {
            var loading = layer.load(1, {shade: [0.1,'#fff']});
            $.ajax({
                url: '{:url("ajax_sales_summary")}',
                type: 'post',
                dataType: 'json',
                data: {
                    start_date: $('#start_date').val(),
                    end_date: $('#end_date').val(),
                    shop_id: $('#shop_filter').val(),
                    keyword: $('#keyword').val()
                },
                success: function(res) {
                    layer.close(loading);
                    if(res.code == 0) {
                        $('#total_sales').text(res.data.total_sales || 0);
                        $('#normal_sales').text(res.data.normal_sales || 0);
                        $('#measure_sales').text(res.data.measure_sales || 0);
                        $('#gift_sales').text(res.data.gift_sales || 0);
                    } else {
                        layer.msg(res.msg || '加载统计数据失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.close(loading);
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                }
            });
        }
        
        // 获取日期字符串
        function getDateStr(days){
            var date = new Date();
            date.setDate(date.getDate() + days);
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            
            month = month < 10 ? '0' + month : month;
            day = day < 10 ? '0' + day : day;
            
            return year + '-' + month + '-' + day;
        }
        
        // 页面加载完成后，加载销量统计数据
        loadSalesStatistics();
    });
</script> 