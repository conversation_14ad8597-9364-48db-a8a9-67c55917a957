
{include file="public/iframeheader"/}
<!--
 * 店铺与商品统计视图
 * 
 * 功能说明：
 * 1. 展示店铺和商品的统计数据，包括数量、状态、库存等
 * 2. 展示最近7天商品增长趋势图
 * 3. 展示商品分类TOP10排行榜
 * 4. 展示店铺商品数量TOP10排行榜
 * 
 * 数据来源：ajax_shop_statistics方法
 * 
 * <AUTHOR>
 * @date 2023-03-06
-->

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">店铺与商品统计</div>
                <div class="layui-card-body">
                    
                    <!-- 数据概览 -->
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-header">店铺总数</div>
                                <div class="layui-card-body layuiadmin-card-list">
                                    <p class="layuiadmin-big-font" id="shop_count">0</p>
                                    <p>最近7天新增：<span class="layuiadmin-span-color" id="recent_shop_count">0</span></p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-header">商品总数</div>
                                <div class="layui-card-body layuiadmin-card-list">
                                    <p class="layuiadmin-big-font" id="product_count">0</p>
                                    <p>最近7天新增：<span class="layuiadmin-span-color" id="recent_product_count">0</span></p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-header">上架商品数</div>
                                <div class="layui-card-body layuiadmin-card-list">
                                    <p class="layuiadmin-big-font" id="product_online_count">0</p>
                                    <p>下架商品数：<span class="layuiadmin-span-color" id="product_offline_count">0</span></p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-header">有库存商品数</div>
                                <div class="layui-card-body layuiadmin-card-list">
                                    <p class="layuiadmin-big-font" id="product_stock_count">0</p>
                                    <p>缺货商品数：<span class="layuiadmin-span-color" id="product_out_of_stock_count">0</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 趋势图 -->
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header">最近7天商品增长趋势</div>
                                <div class="layui-card-body">
                                    <div id="product_trend_chart" style="width: 100%; height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 排行榜 -->
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md6">
                            <div class="layui-card">
                                <div class="layui-card-header">商品分类TOP10</div>
                                <div class="layui-card-body">
                                    <table class="layui-table" lay-skin="line">
                                        <colgroup>
                                            <col width="50">
                                            <col>
                                            <col width="100">
                                        </colgroup>
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>分类名称</th>
                                                <th>商品数量</th>
                                            </tr>
                                        </thead>
                                        <tbody id="category_stats_body">
                                            <!-- 动态填充 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-card">
                                <div class="layui-card-header">店铺商品数量TOP10</div>
                                <div class="layui-card-body">
                                    <table class="layui-table" lay-skin="line">
                                        <colgroup>
                                            <col width="50">
                                            <col>
                                            <col width="100">
                                        </colgroup>
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>店铺名称</th>
                                                <th>商品数量</th>
                                            </tr>
                                        </thead>
                                        <tbody id="shop_product_rank_body">
                                            <!-- 动态填充 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__STATIC__/admin/layui/layui.js"></script>
<script src="__STATIC__/admin/lib/extend/echarts.js"></script>
<script>
    layui.use(['jquery', 'layer'], function(){
        var $ = layui.jquery;
        var layer = layui.layer;
        
        // 加载统计数据
        function loadStatisticsData() {
            layer.load(2);
            $.ajax({
                url: "{:url('statistics/ajax_shop_statistics')}",
                type: 'post',
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');
                    
                    // 更新数据概览
                    $('#shop_count').text(res.shop_count || 0);
                    $('#product_count').text(res.product_count || 0);
                    $('#product_online_count').text(res.product_online_count || 0);
                    $('#product_offline_count').text(res.product_offline_count || 0);
                    $('#product_stock_count').text(res.product_stock_count || 0);
                    $('#product_out_of_stock_count').text(res.product_out_of_stock_count || 0);
                    $('#recent_shop_count').text(res.recent_shop_count || 0);
                    $('#recent_product_count').text(res.recent_product_count || 0);
                    
                    // 渲染趋势图
                    renderTrendChart(res.recent_days, res.recent_product_trend);
                    
                    // 渲染分类排行
                    renderCategoryStats(res.category_stats);
                    
                    // 渲染店铺排行
                    renderShopProductRank(res.shop_product_rank);
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('加载数据失败，请刷新重试', {icon: 2});
                }
            });
        }
        
        // 渲染趋势图
        function renderTrendChart(days, data) {
            var chartDom = document.getElementById('product_trend_chart');
            var myChart = echarts.init(chartDom);
            var option = {
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['新增商品数']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: days || []
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '新增商品数',
                        type: 'line',
                        stack: '总量',
                        data: data || [],
                        areaStyle: {}
                    }
                ]
            };
            myChart.setOption(option);
            
            // 窗口大小变化时，重新调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }
        
        // 渲染分类排行
        function renderCategoryStats(data) {
            var html = '';
            if (data && data.length > 0) {
                $.each(data, function(index, item) {
                    html += '<tr>';
                    html += '<td>' + (index + 1) + '</td>';
                    html += '<td>' + item.title + '</td>';
                    html += '<td>' + item.count + '</td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="3" style="text-align:center;">暂无数据</td></tr>';
            }
            $('#category_stats_body').html(html);
        }
        
        // 渲染店铺排行
        function renderShopProductRank(data) {
            var html = '';
            if (data && data.length > 0) {
                $.each(data, function(index, item) {
                    html += '<tr>';
                    html += '<td>' + (index + 1) + '</td>';
                    html += '<td>' + item.title + '</td>';
                    html += '<td>' + item.count + '</td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="3" style="text-align:center;">暂无数据</td></tr>';
            }
            $('#shop_product_rank_body').html(html);
        }
        
        // 页面加载完成后，加载统计数据
        $(function() {
            loadStatisticsData();
        });
    });
</script>
