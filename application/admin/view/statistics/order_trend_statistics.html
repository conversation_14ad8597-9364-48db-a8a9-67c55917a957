{include file="public/iframeheader" /}

<style>
    .layui-card-header {
        font-weight: bold;
    }
    .chart-container {
        width: 100%;
        height: 350px;
    }
    .layui-form-item .layui-inline {
        margin-right: 0;
    }
    .summary-card {
        text-align: center;
        padding: 10px;
    }
    .summary-card .number {
        font-size: 24px;
        font-weight: bold;
        color: #1E9FFF;
    }
    .summary-card .text {
        color: #666;
        font-size: 14px;
    }
    .filter-container {
        padding: 15px;
        background-color: #f2f2f2;
        margin-bottom: 15px;
        border-radius: 4px;
    }
</style>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">订单趋势分析</div>
                <div class="layui-card-body">
                    <div class="filter-container">
                        <form class="layui-form" action="" lay-filter="statistics-form">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">时间范围</label>
                                    <div class="layui-input-inline" style="width: 300px;">
                                        <input type="text" name="date_range" id="date_range" placeholder="请选择日期范围" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">门店</label>
                                    <div class="layui-input-inline">
                                        <select name="shop_id" lay-filter="shop_id">
                                            <option value="0">全部门店</option>
                                            {volist name="shop_list" id="shop"}
                                            <option value="{$shop.id}">{$shop.title}</option>
                                            {/volist}
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">时间单位</label>
                                    <div class="layui-input-inline">
                                        <select name="time_unit" lay-filter="time_unit">
                                            <option value="day">按天</option>
                                            <option value="week">按周</option>
                                            <option value="month">按月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <button type="button" class="layui-btn" lay-submit lay-filter="search">查询</button>
                                    <button type="button" class="layui-btn layui-btn-primary" id="reset">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据汇总 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">订单概况</div>
                <div class="layui-card-body">
                    <div class="layui-row">
                        <div class="layui-col-md3">
                            <div class="summary-card">
                                <div class="number" id="total_count">0</div>
                                <div class="text">总订单数</div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="summary-card">
                                <div class="number" id="total_amount">0.00</div>
                                <div class="text">总金额(元)</div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="summary-card">
                                <div class="number" id="avg_amount">0.00</div>
                                <div class="text">平均订单金额(元)</div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="summary-card">
                                <div class="number" id="max_amount">0.00</div>
                                <div class="text">最高订单金额(元)</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 趋势图 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">订单趋势</div>
                <div class="layui-card-body">
                    <div class="chart-container" id="order_combined_trend"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 饼图展示 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">配送方式分布</div>
                <div class="layui-card-body">
                    <div class="chart-container" id="delivery_stats_pie"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">支付方式分布</div>
                <div class="layui-card-body">
                    <div class="chart-container" id="payment_stats_pie"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-row layui-col-space15">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">订单状态分布</div>
                <div class="layui-card-body">
                    <div class="chart-container" id="status_stats_pie"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">周一至周日订单分布</div>
                <div class="layui-card-body">
                    <div class="chart-container" id="weekday_stats_chart"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 柱状图展示 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">热门商品TOP10</div>
                <div class="layui-card-body">
                    <div class="layui-tab layui-tab-brief">
                        <ul class="layui-tab-title">
                            <li class="layui-this">按订单数</li>
                            <li>按金额</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <div class="chart-container" id="hot_products_count_chart"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div class="chart-container" id="hot_products_amount_chart"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card" id="hot_shops_card">
                <div class="layui-card-header">热门门店TOP10</div>
                <div class="layui-card-body">
                    <div class="layui-tab layui-tab-brief">
                        <ul class="layui-tab-title">
                            <li class="layui-this">按订单数</li>
                            <li>按金额</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <div class="chart-container" id="hot_shops_count_chart"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div class="chart-container" id="hot_shops_amount_chart"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">各时段订单分布</div>
                <div class="layui-card-body">
                    <div class="chart-container" id="hour_stats_combined_chart"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
<script>
    layui.use(['form', 'layer', 'laydate', 'element'], function () {
        var form = layui.form,
            layer = layui.layer,
            laydate = layui.laydate,
            element = layui.element,
            $ = layui.jquery;

        // 初始化日期范围选择器
        laydate.render({
            elem: '#date_range',
            range: true,
            value: getDefaultDateRange(),
            btns: ['clear', 'now', 'confirm'],
            done: function (value, start, end) {
                // 选择日期后的回调
            }
        });

        // 获取默认日期范围（最近30天）
        function getDefaultDateRange() {
            var endDate = new Date();
            var startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);
            
            return formatDate(startDate) + ' - ' + formatDate(endDate);
        }

        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            
            month = month < 10 ? '0' + month : month;
            day = day < 10 ? '0' + day : day;
            
            return year + '-' + month + '-' + day;
        }

        // 重置按钮
        $('#reset').on('click', function () {
            $('#date_range').val(getDefaultDateRange());
            $('select[name="shop_id"]').val(0);
            $('select[name="time_unit"]').val('day');
            form.render('select');
            
            loadStatisticsData();
        });

        // 提交查询
        form.on('submit(search)', function (data) {
            loadStatisticsData();
            return false;
        });

        // 初始化所有图表
        var charts = {};
        
        function initCharts() {
            // 先确保所有容器可见，以便初始化图表
            $('.layui-tab-item').css('display', 'block');
            
            charts.order_combined_trend = echarts.init(document.getElementById('order_combined_trend'));
            charts.delivery_stats_pie = echarts.init(document.getElementById('delivery_stats_pie'));
            charts.payment_stats_pie = echarts.init(document.getElementById('payment_stats_pie'));
            charts.status_stats_pie = echarts.init(document.getElementById('status_stats_pie'));
            charts.weekday_stats_chart = echarts.init(document.getElementById('weekday_stats_chart'));
            charts.hot_products_count_chart = echarts.init(document.getElementById('hot_products_count_chart'));
            charts.hot_products_amount_chart = echarts.init(document.getElementById('hot_products_amount_chart'));
            charts.hot_shops_count_chart = echarts.init(document.getElementById('hot_shops_count_chart'));
            charts.hot_shops_amount_chart = echarts.init(document.getElementById('hot_shops_amount_chart'));
            charts.hour_stats_combined_chart = echarts.init(document.getElementById('hour_stats_combined_chart'));
            
            // 恢复原来的显示状态，只显示活动选项卡
            $('.layui-tab-item').hide();
            $('.layui-tab-item.layui-show').show();
        }
        
        // 初始化图表
        initCharts();
        
        // 窗口调整大小时，重置图表
        window.onresize = function () {
            for (var key in charts) {
                if (charts.hasOwnProperty(key)) {
                    charts[key].resize();
                }
            }
        };

        // 渲染组合趋势图表（订单量和金额）
        function renderCombinedTrend(chart, categories, countData, amountData) {
            var option = {
                title: {
                    text: '订单量与金额趋势',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function(params) {
                        var tip = params[0].name + '<br/>';
                        
                        params.forEach(function(param) {
                            var color = param.color;
                            var seriesName = param.seriesName;
                            var value = param.value;
                            var unit = seriesName.indexOf('金额') > -1 ? ' 元' : '';
                            
                            if (seriesName.indexOf('金额') > -1) {
                                value = parseFloat(value).toFixed(2);
                            }
                            
                            tip += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' + color + '"></span>';
                            tip += seriesName + ': ' + value + unit + '<br/>';
                        });
                        
                        return tip;
                    }
                },
                legend: {
                    data: ['订单数量', '订单金额'],
                    bottom: 10
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: categories,
                    axisLabel: {
                        interval: 0,
                        rotate: 30
                    },
                    boundaryGap: false
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '订单数量',
                        position: 'left',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                type: 'dashed'
                            }
                        }
                    },
                    {
                        type: 'value',
                        name: '订单金额(元)',
                        position: 'right',
                        axisLabel: {
                            formatter: '{value} 元'
                        },
                        splitLine: {
                            show: false
                        }
                    }
                ],
                series: [
                    {
                        name: '订单数量',
                        type: 'line',
                        data: countData,
                        smooth: true,
                        symbol: 'emptyCircle',
                        symbolSize: 6,
                        showAllSymbol: true,
                        lineStyle: {
                            width: 2
                        },
                        areaStyle: {
                            opacity: 0.2
                        },
                        markPoint: {
                            data: [
                                {type: 'max', name: '最大值'},
                                {type: 'min', name: '最小值'}
                            ]
                        }
                    },
                    {
                        name: '订单金额',
                        type: 'line',
                        yAxisIndex: 1,
                        data: amountData,
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 6,
                        showAllSymbol: true,
                        lineStyle: {
                            width: 2
                        },
                        areaStyle: {
                            opacity: 0.2
                        },
                        markPoint: {
                            data: [
                                {type: 'max', name: '最大值'},
                                {type: 'min', name: '最小值'}
                            ]
                        }
                    }
                ]
            };
            
            chart.setOption(option);
        }
        
        // 渲染饼图
        function renderPieChart(chart, data, title) {
            var seriesData = [];
            
            data.forEach(function(item) {
                seriesData.push({
                    name: item.name,
                    value: item.value
                });
            });
            
            var option = {
                title: {
                    text: title,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        var item = data.find(function(d) { return d.name === params.name; });
                        var tip = params.name + '<br/>';
                        tip += '数量: ' + params.value + ' (' + params.percent + '%)<br/>';
                        if (item && item.amount) {
                            tip += '金额: ' + parseFloat(item.amount).toFixed(2) + '元';
                        }
                        return tip;
                    }
                },
                legend: {
                    orient: 'horizontal',
                    bottom: '5%'
                },
                series: [{
                    name: title,
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '16',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: seriesData
                }]
            };
            
            chart.setOption(option);
        }
        
        // 渲染柱状图
        function renderBarChart(chart, categories, series, title, unit) {
            // 如果是金额数据，确保格式正确
            if (unit === '元') {
                series = series.map(function(value) {
                    return parseFloat(value) || 0;
                });
            }
            
            var option = {
                title: {
                    text: title,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        var tip = params[0].name + '<br/>';
                        tip += params[0].seriesName + ': ';
                        if (unit === '元') {
                            tip += parseFloat(params[0].value).toFixed(2) + unit;
                        } else {
                            tip += params[0].value + (unit ? unit : '');
                        }
                        return tip;
                    }
                },
                grid: {
                    left: unit === '元' ? '12%' : '3%',  // 金额显示时大幅增加左侧空间
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: categories,
                    axisLabel: {
                        interval: 0,
                        rotate: 30
                    }
                },
                yAxis: {
                    type: 'value',
                    name: unit || '',
                    scale: unit === '元' ? true : false,
                    axisLabel: {
                        formatter: function(value) {
                            return unit === '元' ? value.toFixed(2) : value;
                        }
                    }
                },
                series: [{
                    name: title,
                    type: 'bar',
                    data: series,
                    barWidth: '40%',
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: '#83bff6'},
                            {offset: 0.5, color: '#188df0'},
                            {offset: 1, color: '#188df0'}
                        ])
                    },
                    emphasis: {
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#2378f7'},
                                {offset: 0.7, color: '#2378f7'},
                                {offset: 1, color: '#83bff6'}
                            ])
                        }
                    }
                }]
            };
            
            chart.setOption(option);
        }
        
        // 渲染时段分布组合图表
        function renderHourCombinedChart(chart, hours, counts, amounts) {
            var option = {
                title: {
                    text: '各时段订单分布',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function(params) {
                        var tip = params[0].name + '时段<br/>';
                        
                        params.forEach(function(param) {
                            var color = param.color;
                            var seriesName = param.seriesName;
                            var value = param.value;
                            var unit = seriesName.indexOf('金额') > -1 ? ' 元' : '';
                            
                            if (seriesName.indexOf('金额') > -1) {
                                value = parseFloat(value).toFixed(2);
                            }
                            
                            tip += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' + color + '"></span>';
                            tip += seriesName + ': ' + value + unit + '<br/>';
                        });
                        
                        return tip;
                    }
                },
                legend: {
                    data: ['订单数量', '订单金额'],
                    bottom: 10
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: hours,
                    axisLabel: {
                        formatter: '{value}点'
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '订单数量',
                        position: 'left',
                        axisLine: {
                            show: true
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                type: 'dashed'
                            }
                        }
                    },
                    {
                        type: 'value',
                        name: '订单金额(元)',
                        position: 'right',
                        axisLine: {
                            show: true
                        },
                        axisLabel: {
                            formatter: '{value} 元'
                        },
                        splitLine: {
                            show: false
                        }
                    }
                ],
                series: [
                    {
                        name: '订单数量',
                        type: 'bar',
                        data: counts,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#83bff6'},
                                {offset: 0.5, color: '#188df0'},
                                {offset: 1, color: '#188df0'}
                            ])
                        },
                        emphasis: {
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {offset: 0, color: '#2378f7'},
                                    {offset: 0.7, color: '#2378f7'},
                                    {offset: 1, color: '#83bff6'}
                                ])
                            }
                        }
                    },
                    {
                        name: '订单金额',
                        type: 'line',
                        yAxisIndex: 1,
                        data: amounts,
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: {
                            color: '#FF6B3B'
                        },
                        lineStyle: {
                            width: 2,
                            color: '#FF6B3B'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: 'rgba(255, 107, 59, 0.3)'},
                                {offset: 1, color: 'rgba(255, 107, 59, 0.1)'}
                            ])
                        }
                    }
                ]
            };
            
            chart.setOption(option);
        }
        
        // 加载统计数据
        function loadStatisticsData() {
            var dateRange = $('#date_range').val().split(' - ');
            var shopId = $('select[name="shop_id"]').val();
            var timeUnit = $('select[name="time_unit"]').val();
            
            var params = {
                start_date: dateRange[0],
                end_date: dateRange[1],
                shop_id: shopId,
                time_unit: timeUnit
            };
            
            var loading = layer.load(2);
            
            $.ajax({
                url: "{:url('statistics/ajax_order_trend_statistics')}",
                type: 'POST',
                data: params,
                dataType: 'json',
                success: function (res) {
                    layer.close(loading);
                    
                    if (res.code === 0) {
                        // 处理金额数据，确保数据格式正确
                        if (res.data && res.data.trend && res.data.trend.order_amounts) {
                            res.data.trend.order_amounts = res.data.trend.order_amounts.map(function(amount) {
                                return parseFloat(amount) || 0;
                            });
                            
                            // 确保有足够的数据点以正确显示趋势
                            if (res.data.trend.dates && res.data.trend.order_amounts) {
                                if (res.data.trend.dates.length !== res.data.trend.order_amounts.length) {
                                    console.error("日期和金额数据长度不匹配");
                                }
                                
                                // 确保没有undefined或null值
                                for (var i = 0; i < res.data.trend.order_amounts.length; i++) {
                                    if (res.data.trend.order_amounts[i] === undefined || res.data.trend.order_amounts[i] === null) {
                                        res.data.trend.order_amounts[i] = 0;
                                    }
                                }
                            }
                        }
                        
                        // 处理统计汇总的金额数据
                        if (res.data && res.data.order_total) {
                            res.data.order_total.total_amount = parseFloat(res.data.order_total.total_amount).toFixed(2);
                            res.data.order_total.avg_amount = parseFloat(res.data.order_total.avg_amount).toFixed(2);
                            res.data.order_total.max_amount = parseFloat(res.data.order_total.max_amount).toFixed(2);
                        }
                        
                        renderStatisticsData(res.data);
                    } else {
                        layer.msg(res.msg || '获取数据失败', {icon: 2});
                    }
                },
                error: function () {
                    layer.close(loading);
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                }
            });
        }
        
        // 渲染统计数据
        function renderStatisticsData(data) {
            // 更新概览数据
            $('#total_count').text(data.order_total.total_count);
            $('#total_amount').text(data.order_total.total_amount);
            $('#avg_amount').text(data.order_total.avg_amount);
            $('#max_amount').text(data.order_total.max_amount);
            
            // 保存订单金额数据以便tab切换时使用
            window.lastOrderAmountData = {
                dates: data.trend.dates,
                amounts: data.trend.order_amounts
            };
            
            // 渲染组合趋势图表
            renderCombinedTrend(charts.order_combined_trend, data.trend.dates, data.trend.order_counts, data.trend.order_amounts);
            
            // 渲染饼图
            renderPieChart(charts.delivery_stats_pie, data.delivery_stats, '配送方式分布');
            renderPieChart(charts.payment_stats_pie, data.payment_stats, '支付方式分布');
            renderPieChart(charts.status_stats_pie, data.status_stats, '订单状态分布');
            
            // 渲染周一至周日分布图
            renderBarChart(charts.weekday_stats_chart, data.weekday_stats.weekdays, data.weekday_stats.counts, '周一至周日订单分布');
            
            // 渲染热门商品排行
            renderBarChart(charts.hot_products_count_chart, data.hot_products.names, data.hot_products.counts, '热门商品订单数排行', '订单数');
            renderBarChart(charts.hot_products_amount_chart, data.hot_products.names, data.hot_products.amounts, '热门商品金额排行', '元');
            
            // 如果存在热门门店数据，渲染热门门店排行
            if (data.hot_shops) {
                $('#hot_shops_card').show();
                renderBarChart(charts.hot_shops_count_chart, data.hot_shops.names, data.hot_shops.counts, '热门门店订单数排行', '订单数');
                renderBarChart(charts.hot_shops_amount_chart, data.hot_shops.names, data.hot_shops.amounts, '热门门店金额排行', '元');
            } else {
                $('#hot_shops_card').hide();
            }
            
            // 渲染各时段订单分布
            renderHourCombinedChart(charts.hour_stats_combined_chart, data.hour_stats.hours, data.hour_stats.counts, data.hour_stats.amounts);
            
            // 所有图表渲染完成后，强制重新计算大小
            setTimeout(function() {
                for (var key in charts) {
                    if (charts.hasOwnProperty(key)) {
                        charts[key].resize();
                    }
                }
            }, 100);
        }
        
        // 页面加载完成后，加载初始数据
        loadStatisticsData();
        
        // 为选项卡切换添加事件监听
        element.on('tab', function(data){
            // 隐藏所有选项卡内容
            $(data.elem).find('.layui-tab-item').hide();
            // 只显示当前选项卡
            $(data.elem).find('.layui-tab-item:eq(' + data.index + ')').show();
            
            // 获取当前激活的选项卡内容中的图表
            var chartContainer = $(data.elem).find('.layui-tab-item:eq(' + data.index + ')').find('.chart-container');
            if (chartContainer.length > 0) {
                var chartId = chartContainer.attr('id');
                if (charts[chartId]) {
                    // 需要一点延时，确保DOM完全更新
                    setTimeout(function() {
                        charts[chartId].resize();
                    }, 50);
                }
            }
        });
    });
</script> 