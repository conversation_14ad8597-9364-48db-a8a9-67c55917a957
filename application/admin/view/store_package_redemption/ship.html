{include file="public/iframeheader"/}
{__NOLAYOUT__}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-form" id="shipForm" lay-filter="shipForm">
                <input type="hidden" name="id" value="{$info.id}">
                
                <div class="layui-form-item">
                    <label class="layui-form-label">卡号</label>
                    <div class="layui-input-block">
                        <input type="text" value="{$info.card_no}" class="layui-input" readonly>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">收货人</label>
                    <div class="layui-input-block">
                        <input type="text" value="{$info.name}" class="layui-input" readonly>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">联系电话</label>
                    <div class="layui-input-block">
                        <input type="text" value="{$info.phone}" class="layui-input" readonly>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">收货地址</label>
                    <div class="layui-input-block">
                        <input type="text" value="{$info.province}{$info.city}{$info.area}{$info.address}" class="layui-input" readonly>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">快递公司</label>
                    <div class="layui-input-block">
                        <input type="text" name="express_company" placeholder="请输入快递公司" class="layui-input" lay-verify="required" lay-reqText="快递公司不能为空">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">快递单号</label>
                    <div class="layui-input-block">
                        <input type="text" name="express_no" placeholder="请输入快递单号" class="layui-input" lay-verify="required" lay-reqText="快递单号不能为空">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea name="express_remark" placeholder="请输入备注信息" class="layui-textarea"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item text-right">
                    <button class="layui-btn" lay-filter="shipSubmit" lay-submit>确认发货</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['form'], function () {
        var $ = layui.jquery;
        var form = layui.form;
        
        // 提交表单
        form.on('submit(shipSubmit)', function (data) {
            var loading = layer.load(2);
            $.ajax({
                url: '{:url("updateExpress")}',
                data: data.field,
                type: 'post',
                dataType: 'json',
                success: function (res) {
                    layer.close(loading);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        setTimeout(function(){
                        // 关闭当前iframe弹窗
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        parent.window.location.reload();
                        }, 2000);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function () {
                    layer.close(loading);
                    layer.msg('发货失败，请稍后再试', {icon: 2});
                }
            });
            return false;
        });
    });
</script> 