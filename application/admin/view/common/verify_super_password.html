<div class="verify-super-password-container" style="display: none;">
    <div class="verify-super-password-mask" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 19999;"></div>
    <div class="verify-super-password-dialog" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: #fff; width: 360px; border-radius: 2px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12); z-index: 20000;">
        <div class="verify-super-password-header" style="padding: 15px 20px; border-bottom: 1px solid #eee;">
            <div class="verify-super-password-title" style="font-weight: bold;">高级密码验证</div>
        </div>
        <div class="verify-super-password-body" style="padding: 20px;">
            <div class="layui-form">
                <div class="layui-form-item">
                    <input type="password" id="verify_super_password_input" placeholder="请输入高级密码" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>
        <div class="verify-super-password-footer" style="padding: 10px 20px; text-align: right; border-top: 1px solid #eee;">
            <button class="layui-btn layui-btn-primary verify-super-password-cancel">取消</button>
            <button class="layui-btn verify-super-password-confirm">确认</button>
        </div>
    </div>
</div>

<script>
/**
 * 高级密码验证组件
 * 使用方法：
 * 1. 在需要验证高级密码的页面引入此组件：{include file="common/verify_super_password" /}
 * 2. 在需要验证的操作前调用：verifySuperPassword(function(success) { if(success) { 执行需要验证的操作 } })
 */
function verifySuperPassword(callback) {
    layui.use(['layer', 'jquery'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;

        // 显示验证框
        $('.verify-super-password-container').show();
        $('#verify_super_password_input').val('').focus();

        // 取消按钮
        $('.verify-super-password-cancel').off('click').on('click', function() {
            $('.verify-super-password-container').hide();
            if (callback) callback(false);
        });

        // 确认按钮
        $('.verify-super-password-confirm').off('click').on('click', function() {
            var password = $('#verify_super_password_input').val();
            if (!password) {
                layer.msg('请输入高级密码', {icon: 2});
                return;
            }

            // 发送验证请求
            $.ajax({
                type: 'post',
                url: '{:url("Plat/ajax_check_super_password")}',
                data: {
                    password: password
                },
                dataType: 'json',
                success: function(res) {
                    if (res.sta == 1) {
                        $('.verify-super-password-container').hide();
                        if (callback) callback(true);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请稍后再试', {icon: 2});
                }
            });
        });

        // 按下回车键确认
        $('#verify_super_password_input').off('keydown').on('keydown', function(e) {
            if (e.keyCode == 13) {
                $('.verify-super-password-confirm').click();
            }
        });
    });
}
</script> 