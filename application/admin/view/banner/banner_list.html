<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
<br/>
<div style="margin-left: 20px;">
    <div class="page-content">
<!--        <div style="margin-top: 10px;margin-left: 25px;">-->
<!--            <button class="layui-btn layuiadmin-btn-useradmin   layui-btn-sm" id="add-banner">新增</button>-->
<!--        </div>-->
        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-sm" lay-event='edit'>修改</a>
            <a class="layui-btn layui-btn-sm" lay-event='del'>删除</a>
        </script>
    </div>
</div>

</body>

</html>
<script type="text/javascript">
    get_table();

    function get_table(){
        layui.use('table', function () {

            var table = layui.table;
            //方法级渲染
            table.render({
                elem: '#demo'
                , url: "{:url('ajax_banner_list')}"
                , cols: [[
                    {type: 'numbers', title: '序号'}
                    , {field: 'title', title: '轮播图标题'}
                    , {
                        field: 'image', title: '轮播图', templet: function (item) {
                            return '<img onclick="showimg(this);"   src="' + item.image + '">';
                        }
                    }
                    , {field: 'status', title: '是否展示', templet:function (row) {
                            if (row.status == 1) {
                                return '展示';
                            } else if(row.status == 2) {
                                return '不展示';
                            }
                        }}
                    , {field: 'sort', title: '排序'}
                    , {field: 'add_time', title: '添加时间'}
                    , { fixed: 'right', title: "操作", align: 'center', toolbar: '#barDemo' }
                ]]
                , id: 'testReload'
                , page: true
            });

            table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                var data = obj.data //获得当前行数据
                    , layEvent = obj.event; //获得 lay-event 对应的值
                var id = data.id;
                if (layEvent === 'del') {
                    layer.confirm('您确定要删除【<font color=red>' + data.title + '</font>】这条数据吗？', function (index) {
                        $.post("{:url('del_banner')}", { id: id }, function (data) {
                            layer.msg(data.msg);
                            if (data.code == 0) {
                                obj.del();
                            }
                        }, "json");
                    });
                } else if (layEvent === 'edit') {
                    // window.location.href = "{:url('edit_banner')}?id=" + id;
                    layer.open({
                        type: 2,
                        title: "修改轮播图",
                        content: "{:url('edit_banner')}?id=" + id,
                        maxmin: true,
                        area: ["98%", "98%"],
                        end: function () {
                            get_table();
                        }
                    })
                }
            });

            var $ = layui.$, active = {
                reload: function () {
                    //执行重载
                    table.reload('testReload', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        }
                    });
                },
            };
        });
    }



    //点击新增弹窗
    $("[id=add-banner]").click(function () {
        layer.open({
            type: 2,
            title: "新增轮播图",
            content: "{:url('add_banner')}",
            maxmin: true,
            area: ["98%", "98%"],
            end: function () {
                get_table();
            }
        })
    });

    //点击放大图片
    function showimg(t) {
        var src = $(t).attr("src");
        if (src == '') {
            layer.msg("图为为空！");
            return false;
        }
        layer.open({
            type: 1,
            title: false,
            area: '516px',
            content: '<img style="display: inline-block; width: 100%; height: 100%;" src="' + src + '">'
        });
    }


</script>

<script type="text/html" id="img">
    <img src="{{d.image}}" style="" alt="">
</script>
