<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>新增轮播图</title>
    {include file="public/iframeheader"/}
</head>

<script type="text/javascript" src="__STATIC__/admin/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__STATIC__/admin/ueditor/ueditor.all.js"></script>

<body>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body" pad15>
                    <div class="layui-form" wid100 lay-filter="">
                        <input type="hidden" name="id" value="{$info['id']}">
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题</label>
                            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                            <div class="layui-input-inline" style="width: 500px;">
                                <input type="text" name="title" value="{$info['title']}" required style="width: 500px;" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">轮播图</label>
                            <div class="layui-input-block layui-btn-container ">
                                {:UpImage("image",200,200,$info['image'])}
                            </div>
                            <!--                            <div class="layui-form-mid layui-word-aux">推荐尺寸：375*667</div>-->
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">排序</label>
                            <div class="layui-input-inline" style="width: 500px;">
                                <input type="number" name="sort" value="{$info['sort']}" style="width: 500px;" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">是否展示</label>
                            <div class="layui-input-inline" style="width: 500px;">
                                {if $info['status'] == 1}
                                <input type="radio" name="status" value="1" title="是" checked class="layui-input">
                                <input type="radio" name="status" value="0" title="否" class="layui-input">
                                {else}
                                <input type="radio" name="status" value="1" title="是" class="layui-input">
                                <input type="radio" name="status" value="0" title="否" checked class="layui-input">
                                {/if}
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">详情</label>
                            <div class="layui-input-block">
                                <script id="container" type="text/plain" >{$info.content|raw}</script>
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="sub">确认保存</button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<script>
    //百度编辑器服务器统一请求接口路径 ，路由定义在\vendor\dh2y\think-ueditor\src\helper.php
    window.UEDITOR_CONFIG.serverUrl="{:url('ueditor/index')}";
    //渲染某个元素为百度富文本编辑器
    cn_ueditor = UE.getEditor('container', {
        toolbars: [
            ['fullscreen', 'source', 'undo', 'redo', 'simpleupload', 'insertimage'],
            ['justifyleft', //居左对齐
                'justifyright', //居右对齐
                'justifycenter', //居中对齐
                'justifyjustify', //两端对齐
                'forecolor', //字体颜色
                'backcolor', //背景色
                'fontfamily', //字体
                'fontsize', //字号
            ]
        ],
        // autoHeightEnabled: true,
        // autoFloatEnabled: true
    });
</script>
<script>

    function callback(msg) {
        layer.msg(msg, {time: 1500}, function (data) {
            layer.closeAll();
            window.parent.location.reload();
        })
    }

    //一般直接写在一个js文件中
    layui.use(['element', 'form'], function () {
        var form = layui.form;
        //监听提交1
        form.on('submit(sub)', function (data) {
            var editorcontent=cn_ueditor.getContent();
            data.field.content = editorcontent;
            load = layer.load(2, {shade: [0.1, '#fff']});
            $.post("{:url('ajax_edit_banner')}", data.field, function (data) {
                layer.close(load);
                if (data.code == 0) {
                    layer.msg(data.msg);
                    setInterval(function () {
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    }, 1500);
                } else {
                    layer.msg(data.msg);
                }
            }, "json");

            return false;
        });
    });
</script>

</body>

</html>
