<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>食谱标签列表</title>
    {include file="public/iframeheader"/}
</head>

<body>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">食谱标签列表</div>
                <div class="layui-card-body">
                    <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">标签名称</label>
                                <div class="layui-input-block">
                                    <input type="text" name="name" placeholder="请输入标签名称" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">状态</label>
                                <div class="layui-input-block">
                                    <select name="status">
                                        <option value="">全部</option>
                                        <option value="1">正常</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="LAY-app-search">
                                    <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div style="padding-bottom: 10px;">
                        <button class="layui-btn layuiadmin-btn-list" data-type="add">添加标签</button>
                        <!-- <button class="layui-btn layuiadmin-btn-list layui-btn-normal" data-type="batch_add">批量添加</button> -->
                    </div>

                    <table id="LAY-app-list" lay-filter="LAY-app-list"></table>

                    <script type="text/html" id="statusTpl">
                        {{#  if(d.status == 1){ }}
                        <span class="layui-badge layui-bg-green">正常</span>
                        {{#  } else { }}
                        <span class="layui-badge layui-bg-gray">禁用</span>
                        {{#  } }}
                    </script>

                    <script type="text/html" id="table-operation">
                        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['table', 'form', 'layer'], function () {
        var $ = layui.$,
            table = layui.table,
            form = layui.form,
            layer = layui.layer;

        // 表格渲染
        table.render({
            elem: '#LAY-app-list',
            url: '{:url("ajax_tag_list")}',
            cols: [[
                {type: 'numbers', title: '序号', width: 80},
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'name', title: '标签名称', minWidth: 150},
                {field: 'recipe_count', title: '关联食谱数', width: 120, sort: true},
                {field: 'status', title: '状态', width: 100, templet: '#statusTpl'},
                {field: 'create_time', title: '创建时间', width: 180, sort: true},
                {title: '操作', width: 180, align: 'center', fixed: 'right', toolbar: '#table-operation'}
            ]],
            page: true,
            limit: 10,
            limits: [10, 20, 30, 50, 100],
            text: {
                none: '暂无相关数据'
            }
        });

        // 监听搜索
        form.on('submit(LAY-app-search)', function (data) {
            var field = data.field;

            // 执行重载
            table.reload('LAY-app-list', {
                where: field,
                page: {
                    curr: 1 // 重新从第 1 页开始
                }
            });

            return false;
        });

        // 事件
        var active = {
            add: function () {
                location.href = '{:url("add_tag")}';
            },
            batch_add: function () {
                location.href = '{:url("add_tag")}?batch=1';
            }
        };

        $('.layui-btn.layuiadmin-btn-list').on('click', function () {
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

        // 监听工具条
        table.on('tool(LAY-app-list)', function (obj) {
            var data = obj.data;
            if (obj.event === 'edit') {
                location.href = '{:url("edit_tag")}?id=' + data.id;
            } else if (obj.event === 'del') {
                layer.confirm('确定删除此标签吗？', function (index) {
                    $.ajax({
                        url: '{:url("del_tag")}',
                        type: 'post',
                        data: {id: data.id},
                        success: function (res) {
                            console.log(res);
                            console.log(res.code);
                            if (res.code == 0) {
                                layer.msg(res.msg, {icon: 1});
                                obj.del();
                                layer.close(index);
                            } else {
                                console.log(false);
                                layer.msg(res.msg, {icon: 2});
                            }
                        },
                        error: function () {
                            layer.msg('网络错误，请稍后重试', {icon: 2});
                        },
                        dataType: 'json',
                    });
                });
            }
        });
    });
</script>

</body>

</html> 