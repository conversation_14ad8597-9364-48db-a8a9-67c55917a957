<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>编辑标签</title>
    {include file="public/iframeheader"/}
</head>

<body>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">编辑标签</div>
                <div class="layui-card-body">
                    <form class="layui-form" lay-filter="form-tag" id="form-tag">
                        <input type="hidden" name="id" value="{$tag.id|default=''}">
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">标签名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="name" value="{$tag.name|default=''}" lay-verify="required" placeholder="请输入标签名称" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <input type="radio" name="status" value="1" title="正常" {if condition="$tag.status eq 1"}checked{/if}>
                                <input type="radio" name="status" value="0" title="禁用" {if condition="$tag.status eq 0"}checked{/if}>
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="LAY-app-submit" id="LAY-app-submit">提交</button>
                                <button type="button" class="layui-btn layui-btn-primary" id="btn-cancel">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['form', 'layer'], function () {
        var $ = layui.$,
            form = layui.form,
            layer = layui.layer;

        // 表单提交
        form.on('submit(LAY-app-submit)', function (data) {
            var field = data.field;
            
            // 显示loading
            var loadIndex = layer.load(1, {shade: [0.1, '#fff']});
            
            // 提交数据
            $.ajax({
                url: '{:url("ajax_edit_tag")}',
                type: 'post',
                data: field,
                success: function (res) {
                    layer.close(loadIndex);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1, time: 1000}, function () {
                            // 跳转到列表页
                            location.href = '{:url("tag_list")}';
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function () {
                    layer.close(loadIndex);
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                }
            });
            
            return false;
        });

        // 取消按钮
        $('#btn-cancel').on('click', function () {
            location.href = '{:url("tag_list")}';
        });
    });
</script>

</body>

</html> 