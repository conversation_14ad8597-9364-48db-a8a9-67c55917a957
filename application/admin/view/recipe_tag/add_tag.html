<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>{if condition="isset($batch) && $batch"}批量添加标签{else}添加标签{/if}</title>
    {include file="public/iframeheader"/}
</head>

<body>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">{if condition="isset($batch) && $batch"}批量添加标签{else}添加标签{/if}</div>
                <div class="layui-card-body">
                    <form class="layui-form" lay-filter="form-tag" id="form-tag">
                        {if condition="isset($batch) && $batch"}
                        <div class="layui-form-item">
                            <label class="layui-form-label">标签列表</label>
                            <div class="layui-input-block">
                                <textarea name="tags" placeholder="请输入标签名称，多个标签用逗号或空格分隔" class="layui-textarea" style="height: 150px;" lay-verify="required"></textarea>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <div class="layui-input-block">
                                <div class="layui-form-mid layui-word-aux">
                                    <p>批量添加说明：</p>
                                    <p>1. 多个标签请用逗号或空格分隔</p>
                                    <p>2. 例如：标签1,标签2,标签3 或 标签1 标签2 标签3</p>
                                    <p>3. 批量添加的标签默认状态为"正常"</p>
                                </div>
                            </div>
                        </div>
                        {else}
                        <div class="layui-form-item">
                            <label class="layui-form-label">标签名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="name" lay-verify="required" placeholder="请输入标签名称" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <input type="radio" name="status" value="1" title="正常" checked>
                                <input type="radio" name="status" value="0" title="禁用">
                            </div>
                        </div>
                        {/if}
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="LAY-app-submit" id="LAY-app-submit">提交</button>
                                <button type="button" class="layui-btn layui-btn-primary" id="btn-cancel">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['form', 'layer'], function () {
        var $ = layui.$,
            form = layui.form,
            layer = layui.layer;

        // 表单提交
        form.on('submit(LAY-app-submit)', function (data) {
            var field = data.field;
            var url = '{if condition="isset($batch) && $batch"}{:url("ajax_batch_add_tags")}{else}{:url("ajax_add_tag")}{/if}';
            
            // 显示loading
            var loadIndex = layer.load(1, {shade: [0.1, '#fff']});
            
            // 提交数据
            $.ajax({
                url: url,
                type: 'post',
                data: field,
                success: function (res) {
                    console.log(res);
                    layer.close(loadIndex);
                    if (res.code == 0) {
                        layer.msg(res.msg, {icon: 1, time: 1000}, function () {
                            // 跳转到列表页
                            location.href = '{:url("tag_list")}';
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function () {
                    layer.close(loadIndex);
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                },
                dataType: 'json',
            });
            
            return false;
        });

        // 取消按钮
        $('#btn-cancel').on('click', function () {
            location.href = '{:url("tag_list")}';
        });
    });
</script>

</body>

</html> 