<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>微信账号绑定 - 后台管理系统</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="__STATIC__/admin/layui/css/layui.css" media="all">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .bind-container {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .bind-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            text-align: center;
            padding: 30px 20px;
        }
        .wechat-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 15px;
            background: #fff;
            padding: 3px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .wechat-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }
        .wechat-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .bind-tip {
            font-size: 14px;
            opacity: 0.9;
        }
        .bind-form {
            padding: 30px;
        }
        .form-item {
            margin-bottom: 20px;
        }
        .form-item label {
            display: block;
            margin-bottom: 8px;
            color: #666;
            font-weight: 500;
        }
        .form-item input {
            width: 100%;
            height: 45px;
            border: 1px solid #e6e6e6;
            border-radius: 6px;
            padding: 0 15px;
            font-size: 14px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        .form-item input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 2px rgba(102,126,234,0.2);
        }
        .bind-btn {
            width: 100%;
            height: 45px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .bind-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102,126,234,0.4);
        }
        .bind-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .bind-footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #666;
            font-size: 12px;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .error-msg {
            color: #ff4757;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }
        .success-msg {
            color: #2ed573;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }
        .layui-layer-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff !important;
        }
    </style>
</head>
<body>
    <div class="bind-container">
        <div class="bind-header">
            <div class="wechat-avatar">
                {if $avatar}
                <img src="{$avatar}" alt="微信头像" onerror="this.src='__STATIC__/admin/images/default-avatar.png'">
                {else/}
                <img src="__STATIC__/admin/images/default-avatar.png" alt="默认头像">
                {/if}
            </div>
            <div class="wechat-name">{$nickname|default='微信用户'}</div>
            <div class="bind-tip">请绑定您的后台管理账号</div>
        </div>
        
        <div class="bind-form">
            <form id="bindForm">
                <input type="hidden" name="state" value="{$state}">
                
                <div class="form-item">
                    <label for="accountname">
                        <i class="layui-icon layui-icon-username"></i> 账号名称
                    </label>
                    <input type="text" id="accountname" name="accountname" placeholder="请输入您的账号名称" required>
                    <div class="error-msg" id="accountname-error"></div>
                </div>
                
                <div class="form-item">
                    <label for="password">
                        <i class="layui-icon layui-icon-password"></i> 登录密码
                    </label>
                    <input type="password" id="password" name="password" placeholder="请输入您的登录密码" required>
                    <div class="error-msg" id="password-error"></div>
                </div>
                
                <div class="form-item">
                    <button type="submit" class="bind-btn" id="bindBtn">
                        <span class="btn-text">绑定账号</span>
                        <span class="loading" id="btnLoading">
                            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
                            绑定中...
                        </span>
                    </button>
                </div>
                
                <div class="success-msg" id="success-msg"></div>
                <div class="error-msg" id="form-error"></div>
            </form>
        </div>
        
        <div class="bind-footer">
            <p>温馨提示：绑定后可使用微信快速登录后台管理系统</p>
            <p>如有问题，请联系系统管理员</p>
        </div>
    </div>

    <script src="__STATIC__/admin/layui/layui.js"></script>
    <script>
        layui.use(['layer', 'form'], function(){
            var layer = layui.layer;
            var form = layui.form;
            
            // 表单提交
            document.getElementById('bindForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                var accountname = document.getElementById('accountname').value.trim();
                var password = document.getElementById('password').value;
                var state = document.querySelector('input[name="state"]').value;
                
                // 清除之前的错误信息
                clearErrors();
                
                // 验证输入
                if (!accountname) {
                    showFieldError('accountname', '请输入账号名称');
                    return;
                }
                
                if (!password) {
                    showFieldError('password', '请输入登录密码');
                    return;
                }
                
                // 显示加载状态
                showLoading(true);
                
                // 提交数据
                var xhr = new XMLHttpRequest();
                xhr.open('POST', '{:url("admin/wechat_auth/doBind")}', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        showLoading(false);
                        
                        if (xhr.status === 200) {
                            try {
                                var response = JSON.parse(xhr.responseText);
                                
                                if (response.code === 200) {
                                    // 绑定成功
                                    showSuccess('绑定成功，正在跳转...');
                                    
                                    setTimeout(function() {
                                        var redirectUrl = response.data.redirect_url || '{:url("admin/index/index")}';
                                        window.location.href = redirectUrl;
                                    }, 1500);
                                    
                                } else {
                                    // 绑定失败
                                    showFormError(response.msg || '绑定失败，请重试');
                                }
                            } catch (e) {
                                showFormError('响应数据解析失败');
                            }
                        } else {
                            showFormError('网络请求失败，请检查网络连接');
                        }
                    }
                };
                
                var data = 'state=' + encodeURIComponent(state) + 
                          '&accountname=' + encodeURIComponent(accountname) + 
                          '&password=' + encodeURIComponent(password);
                
                xhr.send(data);
            });
            
            // 输入框失焦验证
            document.getElementById('accountname').addEventListener('blur', function() {
                if (!this.value.trim()) {
                    showFieldError('accountname', '账号名称不能为空');
                } else {
                    clearFieldError('accountname');
                }
            });
            
            document.getElementById('password').addEventListener('blur', function() {
                if (!this.value) {
                    showFieldError('password', '登录密码不能为空');
                } else {
                    clearFieldError('password');
                }
            });
            
            // 工具函数
            function showLoading(show) {
                var btn = document.getElementById('bindBtn');
                var btnText = btn.querySelector('.btn-text');
                var btnLoading = btn.querySelector('.loading');
                
                if (show) {
                    btn.disabled = true;
                    btnText.style.display = 'none';
                    btnLoading.style.display = 'inline';
                } else {
                    btn.disabled = false;
                    btnText.style.display = 'inline';
                    btnLoading.style.display = 'none';
                }
            }
            
            function showFieldError(field, message) {
                var errorDiv = document.getElementById(field + '-error');
                if (errorDiv) {
                    errorDiv.textContent = message;
                    errorDiv.style.display = 'block';
                }
                
                var input = document.getElementById(field);
                if (input) {
                    input.style.borderColor = '#ff4757';
                }
            }
            
            function clearFieldError(field) {
                var errorDiv = document.getElementById(field + '-error');
                if (errorDiv) {
                    errorDiv.style.display = 'none';
                }
                
                var input = document.getElementById(field);
                if (input) {
                    input.style.borderColor = '#e6e6e6';
                }
            }
            
            function clearErrors() {
                clearFieldError('accountname');
                clearFieldError('password');
                
                document.getElementById('form-error').style.display = 'none';
                document.getElementById('success-msg').style.display = 'none';
            }
            
            function showFormError(message) {
                var errorDiv = document.getElementById('form-error');
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            }
            
            function showSuccess(message) {
                var successDiv = document.getElementById('success-msg');
                successDiv.textContent = message;
                successDiv.style.display = 'block';
            }
        });
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            // 自动聚焦到账号输入框
            document.getElementById('accountname').focus();
            
            // 按回车键提交表单
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('bindForm').dispatchEvent(new Event('submit'));
                }
            });
        });
    </script>
</body>
</html> 