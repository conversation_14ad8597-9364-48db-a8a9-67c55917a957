{include file="public/iframeheader"/}

{block name="style"}
<style type="text/css">
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        white-space: normal;
        word-wrap: break-word;
        vertical-align: middle;
    }
    .layui-table td {
        height: auto;
        vertical-align: middle;
    }
    /* 操作列按钮样式调整 */
    .layui-table td .layui-btn-xs {
        margin: 2px;
        vertical-align: middle;
    }
    /* 多选框样式调整 */
    .layui-table td .layui-form-checkbox {
        margin: 0;
        vertical-align: middle;
    }
    /* 固定列（多选列和操作列）垂直居中 */
    .layui-table-fixed .layui-table-cell {
        vertical-align: middle;
        height: auto;
    }
    /* 操作列容器调整 */
    .layui-table .layui-table-cell {
        padding: 9px 15px;
        height: auto;
        word-break: normal;
        white-space: pre-wrap;
        word-wrap: break-word;
    }
    /* 固定列内容垂直居中 */
    .layui-table-fixed .layui-table-body .layui-table-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 40px;
    }
    /* 操作列特殊样式 */
    .layui-table tbody tr .layui-table-cell:last-child {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
    }
    /* 操作按钮容器 */
    .layui-table tbody tr .layui-table-cell:last-child > div {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        width: 100%;
    }
</style>
{/block}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-breadcrumb">
            <a href="{:url('index')}">兑换管理</a>
            <a><cite>已发货列表</cite></a>
        </span>
    </div>
    <div class="layui-card-body">
        <div class="layui-form layui-form-pane">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">门店</label>
                    <div class="layui-input-inline">
                        <select name="shop_id">
                            <option value="">全部门店</option>
                            {foreach $shops as $shop}
                            <option value="{$shop.id}">{$shop.title}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">卡号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="card_no" placeholder="请输入卡号" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">用户名</label>
                    <div class="layui-input-inline">
                        <input type="text" name="user_name" placeholder="请输入用户名" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">快递公司</label>
                    <div class="layui-input-inline">
                        <input type="text" name="express_company" placeholder="请输入快递公司" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">快递单号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="express_no" placeholder="请输入快递单号" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">发货时间</label>
                    <div class="layui-input-inline">
                        <input type="text" name="start_time" id="start_time" placeholder="开始时间" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" name="end_time" id="end_time" placeholder="结束时间" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="search">
                        <i class="layui-icon layui-icon-search"></i>搜索
                    </button>
                    <button class="layui-btn layui-btn-primary" type="reset">
                        <i class="layui-icon layui-icon-refresh"></i>重置
                    </button>
                </div>
            </div>
        </div>
        
        <table class="layui-table" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>
{/block}

{block name="js"}
<!-- <script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <a class="layui-btn layui-btn-sm layui-btn-warm" href="{:url('shopPendingStatistics')}">
            <i class="layui-icon layui-icon-chart"></i>门店待发货统计
        </a>
        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="export">
            <i class="layui-icon layui-icon-export"></i>导出数据
        </button>
    </div>
</script> -->

<script type="text/html" id="opTool">
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="detail">详情</a>
</script>

<script>
    layui.use(['table', 'form', 'jquery', 'laydate'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table,
            laydate = layui.laydate;
            
        // 时间选择器
        laydate.render({
            elem: '#start_time',
            type: 'datetime'
        });
        
        laydate.render({
            elem: '#end_time',
            type: 'datetime'
        });
            
        // 渲染表格
        table.render({
            elem: '#dataTable',
            url: '{:url("shippedList")}',
            toolbar: '#toolbar',
            defaultToolbar: ['filter', 'exports', 'print'],
            cols: [[
                {field: 'id', title: 'ID', width: 100, sort: true},
                {field: 'card_no', title: '卡号', width: 150},
                {field: 'user_name', title: '用户名', width: 100},
                {field: 'package_names', title: '套餐', width: 180},
                {field: 'product_names', title: '商品', width: 200, templet: function(d){
                    return d.product_names || '';
                }},
                {field: 'shop_name', title: '发货门店', width: 130},
                {field: 'name', title: '收货人', width: 100},
                {field: 'phone', title: '联系电话', width: 130},
                {field: 'express_company', title: '快递公司', width: 120},
                {field: 'express_no', title: '快递单号', width: 150},
                {field: 'express_time', title: '发货时间', width: 160, sort: true},
                {field: 'address', title: '收货地址', width: 200},
                {fixed: 'right', title: '操作', toolbar: '#opTool', width: 80}
            ]],
            limit: 15,
            page: true,
            text: {
                none: '暂无相关数据'
            },
            done: function (res, curr, count) {
                // 该方法用于解决,使用fixed固定列后,行高和其他列不一致的问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });
        
        // 监听搜索
        form.on('submit(search)', function (data) {
            table.reload('dataTable', {
                where: data.field,
                page: {
                    curr: 1
                }
            });
            return false;
        });
        
        // 监听表头工具栏
        table.on('toolbar(dataTable)', function (obj) {
            if (obj.event === 'export') {
                // 获取当前查询条件
                var queryParams = $('.layui-form').serialize();
                window.location.href = '{:url("export")}?' + queryParams;
            }
        });
        
        // 监听行工具事件
        table.on('tool(dataTable)', function (obj) {
            var data = obj.data;
            
            if (obj.event === 'detail') {
                location.href = '{:url("detail")}?id=' + data.id;
            }
        });
    });
</script>
{/block} 