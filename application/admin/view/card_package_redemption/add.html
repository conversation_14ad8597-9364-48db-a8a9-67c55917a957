{include file="public/iframeheader"/}
{block name="title"}添加兑换{/block}

{block name="style"}
<style type="text/css">
    .hidden {
        display: none;
    }
    .package-info {
        margin-top: 20px;
    }
    .package-title {
        font-weight: bold;
        margin-bottom: 10px;
        font-size: 16px;
        border-bottom: 1px solid #eee;
        padding-bottom: 5px;
    }
    .package-item {
        background-color: #f9f9f9;
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 3px;
    }
    .package-name {
        font-weight: bold;
        margin-bottom: 5px;
    }
    .package-desc {
        color: #666;
        margin-bottom: 10px;
    }
    .product-list {
        margin-left: 20px;
    }
    .product-item {
        margin-bottom: 5px;
    }
</style>
{/block}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">添加兑换</div>
        <div class="layui-card-body">
            <form class="layui-form" action="" lay-filter="redemption-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">卡号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="card_no" required lay-verify="required" placeholder="请输入卡号" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-input-inline">
                        <button type="button" class="layui-btn" id="check-card">校验卡</button>
                    </div>
                </div>
                
                <div id="card-info" class="hidden">
                    <div class="layui-form-item">
                        <label class="layui-form-label">卡类型</label>
                        <div class="layui-input-block">
                            <input type="text" name="type_name" readonly class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">兑换类型</label>
                        <div class="layui-input-block">
                            <input type="radio" name="type" value="1" title="到店兑换" checked>
                            <input type="radio" name="type" value="2" title="快递配送">
                        </div>
                    </div>
                    
                    <div id="shop-info">
                        <div class="layui-form-item">
                            <label class="layui-form-label">选择门店</label>
                            <div class="layui-input-block">
                                <select name="shop_id" lay-verify="required">
                                    <option value="">请选择门店</option>
                                    {volist name="shops" id="shop"}
                                    <option value="{$shop.id}">{$shop.name}</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div id="address-info" class="hidden">
                        <div class="layui-form-item">
                            <label class="layui-form-label">收货人</label>
                            <div class="layui-input-block">
                                <input type="text" name="name" placeholder="请输入收货人姓名" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">联系电话</label>
                            <div class="layui-input-block">
                                <input type="text" name="phone" placeholder="请输入联系电话" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">省份</label>
                            <div class="layui-input-inline">
                                <input type="text" name="province" placeholder="请输入省份" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label">城市</label>
                            <div class="layui-input-inline">
                                <input type="text" name="city" placeholder="请输入城市" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">区县</label>
                            <div class="layui-input-inline">
                                <input type="text" name="area" placeholder="请输入区县" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label">邮政编码</label>
                            <div class="layui-input-inline">
                                <input type="text" name="postal_code" placeholder="请输入邮政编码" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">详细地址</label>
                            <div class="layui-input-block">
                                <input type="text" name="address" placeholder="请输入详细地址" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">备注</label>
                        <div class="layui-input-block">
                            <textarea name="remark" placeholder="请输入备注" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    
                    <div id="package-info" class="package-info">
                        <div class="package-title">套餐信息</div>
                        <div id="package-list"></div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="redemption-submit">立即提交</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{/block}

{block name="scripts"}
<script>
    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;
        
        // 监听兑换类型切换
        form.on('radio(type)', function(data){
            if(data.value == 1){
                $('#shop-info').removeClass('hidden');
                $('#address-info').addClass('hidden');
            } else {
                $('#shop-info').addClass('hidden');
                $('#address-info').removeClass('hidden');
            }
        });
        
        // 监听校验卡按钮点击
        $('#check-card').on('click', function(){
            var cardNo = $('input[name="card_no"]').val();
            if(!cardNo){
                layer.msg('请输入卡号', {icon: 2});
                return false;
            }
            
            // 校验卡信息
            $.ajax({
                url: '{:url("getCardInfo")}',
                type: 'post',
                data: {
                    card_no: cardNo
                },
                success: function(res){
                    if(res.code === 0){
                        var data = res.data;
                        var card = data.card;
                        var packages = data.packages;
                        
                        // 显示卡信息
                        $('input[name="type_name"]').val(card.type_name);
                        $('#card-info').removeClass('hidden');
                        
                        // 显示套餐信息
                        var packageHtml = '';
                        $.each(packages, function(index, item){
                            packageHtml += '<div class="package-item">' +
                                '<div class="package-name">' + item.name + '</div>' +
                                '<div class="package-desc">价格：¥' + item.price + ' &nbsp; 有效期：' + item.validity_days + '天</div>';
                            
                            if(item.products && item.products.length > 0){
                                packageHtml += '<div class="product-list">';
                                $.each(item.products, function(i, product){
                                    packageHtml += '<div class="product-item">' + product.product_name + 
                                        (product.spec_values ? '（' + product.spec_values + '）' : '') + 
                                        ' × ' + product.quantity + '</div>';
                                });
                                packageHtml += '</div>';
                            }
                            
                            packageHtml += '</div>';
                        });
                        
                        $('#package-list').html(packageHtml);
                        
                        form.render();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }
            });
            
            return false;
        });
        
        // 监听表单提交
        form.on('submit(redemption-submit)', function(data){
            var field = data.field;
            
            // 验证必填项
            if(field.type == 1 && !field.shop_id){
                layer.msg('请选择门店', {icon: 2});
                return false;
            }
            
            if(field.type == 2){
                if(!field.name){
                    layer.msg('请输入收货人姓名', {icon: 2});
                    return false;
                }
                if(!field.phone){
                    layer.msg('请输入联系电话', {icon: 2});
                    return false;
                }
                if(!field.address){
                    layer.msg('请输入详细地址', {icon: 2});
                    return false;
                }
            }
            
            // 提交表单
            layer.confirm('确定要提交兑换吗？', function(index){
                $.ajax({
                    url: '{:url("add")}',
                    type: 'post',
                    data: field,
                    success: function(res){
                        if(res.code === 0){
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function(){
                                parent.layui.table.reload('redemption-table');
                                parent.layer.close(parent.layer.getFrameIndex(window.name));
                            }, 1000);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                
                layer.close(index);
            });
            
            return false;
        });
        
        // 监听兑换类型选择
        form.on('radio', function(data){
            if(data.elem.name === 'type'){
                if(data.value === '1'){
                    $('#shop-info').removeClass('hidden');
                    $('#address-info').addClass('hidden');
                } else {
                    $('#shop-info').addClass('hidden');
                    $('#address-info').removeClass('hidden');
                }
            }
        });
    });
</script>
{/block} 