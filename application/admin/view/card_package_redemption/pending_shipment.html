{include file="public/iframeheader"/}

{block name="style"}
<style type="text/css">
    /*这里需要加上 .layui-table 如若不加可能会不生效*/
    .layui-table .layui-table-cell {
        height: auto;
        word-break: normal;
        display: block;
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow: hidden;
        padding: 0 10px !important;
    }
    /* 修复固定列行高不一致问题 */
    .layui-table-fixed .layui-table-cell {
        height: auto;
        vertical-align: middle;
    }
    /* 操作列按钮样式调整 */
    .layui-table td .layui-btn-xs {
        margin: 2px;
        vertical-align: middle;
    }
    /* 多选框样式调整 */
    .layui-table td .layui-form-checkbox {
        margin: 0;
        vertical-align: middle;
    }
    /* 固定列内容垂直居中 */
    .layui-table-fixed .layui-table-body .layui-table-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 40px;
    }
</style>
{/block}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-breadcrumb">
            <a href="{:url('index')}">兑换管理</a>
            <a><cite>待发货列表</cite></a>
        </span>
    </div>
    <div class="layui-card-body">
        <div class="layui-form layui-form-pane">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">门店</label>
                    <div class="layui-input-inline">
                        <select name="shop_id">
                            <option value="">全部门店</option>
                            {foreach $shops as $shop}
                            <option value="{$shop.id}">{$shop.title}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">卡号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="card_no" placeholder="请输入卡号" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">用户名</label>
                    <div class="layui-input-inline">
                        <input type="text" name="user_name" placeholder="请输入用户名" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">兑换时间</label>
                    <div class="layui-input-inline">
                        <input type="text" name="start_time" id="start_time" placeholder="开始时间" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" name="end_time" id="end_time" placeholder="结束时间" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="search">
                        <i class="layui-icon layui-icon-search"></i>搜索
                    </button>
                    <button class="layui-btn layui-btn-primary" type="reset">
                        <i class="layui-icon layui-icon-refresh"></i>重置
                    </button>
                </div>
            </div>
        </div>
        
        <table class="layui-table" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>
{/block}

{block name="js"}
<!-- <script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <a class="layui-btn layui-btn-sm layui-btn-warm" href="{:url('shopPendingStatistics')}">
            <i class="layui-icon layui-icon-chart"></i>门店待发货统计
        </a>
    </div>
</script> -->

<script type="text/html" id="opTool">
    <!-- <a class="layui-btn layui-btn-xs" lay-event="express">发货</a> -->
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="detail">详情</a>
</script>

<script>
    layui.use(['table', 'form', 'jquery', 'laydate'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table,
            laydate = layui.laydate;
            
        // 时间选择器
        laydate.render({
            elem: '#start_time',
            type: 'datetime'
        });
        
        laydate.render({
            elem: '#end_time',
            type: 'datetime'
        });
            
        // 渲染表格
        table.render({
            elem: '#dataTable',
            url: '{:url("pendingShipment")}',
            toolbar: '#toolbar',
            defaultToolbar: ['filter', 'exports', 'print'],
            cols: [[
                {field: 'id', title: 'ID', width: 100, sort: true},
                {field: 'card_no', title: '卡号', width: 150},
                {field: 'user_name', title: '用户名', width: 100},
                {field: 'package_names', title: '套餐', width: 180},
                {field: 'product_names', title: '商品', width: 200, templet: function(d){
                    return d.product_names || '';
                }},
                {field: 'shop_name', title: '分配门店', width: 130},
                {field: 'name', title: '收货人', width: 100},
                {field: 'phone', title: '联系电话', width: 130},
                {field: 'province', title: '省份', width: 80},
                {field: 'city', title: '城市', width: 80},
                {field: 'area', title: '区县', width: 80},
                {field: 'address', title: '详细地址', width: 200},
                {field: 'redemption_time', title: '兑换时间', width: 160, sort: true},
                {fixed: 'right', title: '操作', toolbar: '#opTool', width: 120}
            ]],
            limit: 15,
            page: true,
            text: {
                none: '暂无相关数据'
            },
            done: function (res, curr, count) {
                // 该方法用于解决,使用fixed固定列后,行高和其他列不一致的问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });
        
        // 监听搜索
        form.on('submit(search)', function (data) {
            table.reload('dataTable', {
                where: data.field,
                page: {
                    curr: 1
                }
            });
            return false;
        });
        
        // 监听行工具事件
        table.on('tool(dataTable)', function (obj) {
            var data = obj.data;
            
            if (obj.event === 'express') {
                // 弹出发货表单
                layer.open({
                    type: 2,
                    title: '填写快递信息',
                    content: '{:url("updateExpress")}?id=' + data.id,
                    area: ['500px', '400px'],
                    btn: ['确定', '取消'],
                    yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index],
                            submitBtn = layero.find('iframe').contents().find('#expressSubmit');
                        
                        submitBtn.click();
                    }
                });
            } else if (obj.event === 'detail') {
                location.href = '{:url("detail")}?id=' + data.id;
            }
        });
    });
</script>
{/block} 