{include file="public/iframeheader"/}

{block name="css"}
<style>
    .statistics-card {
        background-color: #fff;
        box-shadow: 0 2px 10px rgba(0,0,0,.05);
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .statistics-header {
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
        margin-bottom: 15px;
    }
    .statistics-header h3 {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }
    .shop-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #f2f2f2;
    }
    .shop-item:last-child {
        border-bottom: none;
    }
    .shop-name {
        font-size: 15px;
        color: #333;
    }
    .pending-count {
        font-size: 15px;
        font-weight: bold;
        color: #ff6b00;
    }
    .total-card {
        background-color: #1E9FFF;
        color: #fff;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;
    }
    .total-card .title {
        font-size: 14px;
        margin-bottom: 5px;
    }
    .total-card .count {
        font-size: 28px;
        font-weight: bold;
    }
    .unassigned-card {
        background-color: #FF5722;
        color: #fff;
    }
    .button-group {
        margin-bottom: 20px;
    }
</style>
{/block}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-breadcrumb">
            <a href="{:url('index')}">兑换管理</a>
            <a><cite>门店待发货统计</cite></a>
        </span>
    </div>
    <div class="layui-card-body">
        <!-- <div class="layui-row layui-col-space15 button-group">
            <div class="layui-col-md12">
                <a href="{:url('pendingShipment')}" class="layui-btn">
                    <i class="layui-icon layui-icon-list"></i> 查看全部待发货订单
                </a>
                <a href="{:url('unassignedList')}" class="layui-btn layui-btn-danger">
                    <i class="layui-icon layui-icon-release"></i> 查看未分配门店订单
                </a>
                <a href="{:url('shippedList')}" class="layui-btn layui-btn-normal">
                    <i class="layui-icon layui-icon-ok-circle"></i> 查看已发货订单
                </a>
                <button type="button" class="layui-btn layui-btn-primary" id="refreshBtn">
                    <i class="layui-icon layui-icon-refresh"></i> 刷新数据
                </button>
            </div>
        </div> -->
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md3">
                <div class="total-card">
                    <div class="title">待发货总数</div>
                    <div class="count" id="totalPending">0</div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="total-card unassigned-card">
                    <div class="title">未分配门店订单</div>
                    <div class="count" id="unassignedCount">0</div>
                </div>
            </div>
        </div>
        
        <div class="statistics-card">
            <div class="statistics-header">
                <h3>各门店待发货订单数量</h3>
            </div>
            <div id="shopStatistics">
                <!-- 动态加载门店统计数据 -->
                <div class="layui-text" style="padding: 30px 0; text-align: center;" id="loadingTip">
                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 20px;"></i> 正在加载数据...
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="js"}
<script>
    layui.use(['jquery', 'layer'], function () {
        var $ = layui.jquery,
            layer = layui.layer;
            
        // 加载门店统计数据
        function loadStatistics() {
            $('#loadingTip').show();
            
            $.ajax({
                url: '{:url("shopPendingStatistics")}',
                type: 'post',
                dataType: 'json',
                success: function (res) {
                    $('#loadingTip').hide();
                    
                    if (res.code === 0) {
                        var statistics = res.data.statistics;
                        var unassignedCount = res.data.unassigned_count;
                        var totalPending = 0;
                        var html = '';
                        
                        if (statistics.length === 0) {
                            html = '<div class="layui-text" style="padding: 30px 0; text-align: center;">暂无门店待发货数据</div>';
                        } else {
                            // 按待发货数量降序排序
                            statistics.sort(function (a, b) {
                                return b.pending_count - a.pending_count;
                            });
                            
                            // 生成门店统计列表
                            statistics.forEach(function (item) {
                                totalPending += parseInt(item.pending_count);
                                html += '<div class="shop-item">' +
                                    '<div class="shop-name">' + item.shop_name + '</div>' +
                                    '<div class="pending-count">' + item.pending_count + '</div>' +
                                    '</div>';
                            });
                        }
                        
                        // 更新到页面
                        $('#shopStatistics').html(html);
                        $('#unassignedCount').text(unassignedCount);
                        $('#totalPending').text(totalPending + parseInt(unassignedCount));
                    } else {
                        layer.msg('获取数据失败', {icon: 2});
                    }
                },
                error: function (xhr) {
                    $('#loadingTip').hide();
                    layer.msg('请求失败', {icon: 2});
                }
            });
        }
        
        // 初始加载数据
        loadStatistics();
        
        // 刷新按钮事件
        $('#refreshBtn').on('click', function () {
            loadStatistics();
        });
    });
</script>
{/block} 