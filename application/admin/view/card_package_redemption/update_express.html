<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>填写快递信息</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="__STATIC__/admin/layui/css/layui.css" media="all">
    <style>
        body {
            padding: 15px;
        }
        .layui-form-item .required:after {
            content: '*';
            color: red;
            position: absolute;
            margin-left: 4px;
            font-weight: bold;
            line-height: 1.8em;
            top: 6px;
            right: 5px;
        }
    </style>
</head>
<body>
    <form class="layui-form" action="" lay-filter="expressForm">
        <input type="hidden" name="id" value="{$Think.get.id}">
        
        <div class="layui-form-item">
            <label class="layui-form-label required">快递公司</label>
            <div class="layui-input-block">
                <input type="text" name="express_company" required lay-verify="required" placeholder="请输入快递公司" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label required">快递单号</label>
            <div class="layui-input-block">
                <input type="text" name="express_no" required lay-verify="required" placeholder="请输入快递单号" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="express_remark" placeholder="请输入备注" class="layui-textarea"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item" style="display: none;">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="expressSubmit" id="expressSubmit">确认发货</button>
            </div>
        </div>
    </form>

    <script src="__STATIC__/admin/layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function () {
            var $ = layui.jquery,
                form = layui.form,
                layer = layui.layer;

            // 监听提交
            form.on('submit(expressSubmit)', function (data) {
                var index = parent.layer.msg('正在提交数据...', {icon: 16, time: false, shade: 0.3});
                
                $.ajax({
                    url: '{:url("updateExpress")}',
                    type: 'post',
                    dataType: 'json',
                    data: data.field,
                    success: function (res) {
                        parent.layer.close(index);
                        if (res.code === 0) {
                            parent.layer.msg(res.msg, {icon: 1, time: 2000}, function () {
                                parent.layer.closeAll();
                                parent.layui.table.reload('dataTable');
                            });
                        } else {
                            parent.layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    },
                    error: function (xhr) {
                        parent.layer.close(index);
                        parent.layer.msg('请求失败', {icon: 2, time: 2000});
                    }
                });
                
                return false;
            });
        });
    </script>
</body>
</html> 