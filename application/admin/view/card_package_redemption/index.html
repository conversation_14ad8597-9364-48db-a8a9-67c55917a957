{include file="public/iframeheader"/}
<!-- {block name="title"}卡套餐兑换管理{/block} -->

{block name="style"}
<style type="text/css">
    .layui-form-item .layui-input-inline{
        width: 180px;
    }
    /*这里需要加上 .layui-table 如若不加可能会不生效*/
    .layui-table .layui-table-cell {
        height: auto;
        word-break: normal;
        display: block;
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow: hidden;
        padding: 0 10px !important;
    }
    /* 修复固定列行高不一致问题 */
    .layui-table-fixed .layui-table-cell {
        height: auto;
        vertical-align: middle;
    }
    /* 操作列按钮样式调整 */
    .layui-table td .layui-btn-xs {
        margin: 2px;
        vertical-align: middle;
    }
    /* 多选框样式调整 */
    .layui-table td .layui-form-checkbox {
        margin: 0;
        vertical-align: middle;
    }
    /* 固定列内容垂直居中 */
    .layui-table-fixed .layui-table-body .layui-table-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 40px;
    }
</style>
{/block}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 搜索表单 -->
            <form class="layui-form layui-form-pane" action="">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">卡号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="card_no" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <!-- <div class="layui-inline">
                        <label class="layui-form-label">用户</label>
                        <div class="layui-input-inline">
                            <input type="text" name="user_name" autocomplete="off" class="layui-input">
                        </div>
                    </div> -->
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">全部</option>
                                <option value="0">已取消</option>
                                <option value="1">已兑换</option>
                                <option value="2">已完成</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">类型</label>
                        <div class="layui-input-inline">
                            <select name="type">
                                <option value="">全部</option>
                                <option value="1">到店兑换</option>
                                <option value="2">快递配送</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">兑换时间</label>
                        <div class="layui-input-inline" style="width: 400px;">
                            <input type="text" name="start_time" id="start_time" placeholder="开始时间" autocomplete="off" class="layui-input" style="width: 190px;display: inline-block;">
                            -
                            <input type="text" name="end_time" id="end_time" placeholder="结束时间" autocomplete="off" class="layui-input" style="width: 190px;display: inline-block;">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" lay-submit lay-filter="search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i> 搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
            
            <!-- <div class="layui-btn-group">
                <a href="{:url('shopPendingStatistics')}" class="layui-btn layui-btn-sm layui-btn-warm">
                    <i class="layui-icon layui-icon-chart"></i> 门店待发货统计
                </a>
                <a href="{:url('unassignedList')}" class="layui-btn layui-btn-sm layui-btn-danger">
                    <i class="layui-icon layui-icon-release"></i> 未分配门店列表
                </a>
                <a href="{:url('pendingShipment')}" class="layui-btn layui-btn-sm">
                    <i class="layui-icon layui-icon-list"></i> 待发货列表
                </a>
                <a href="{:url('shippedList')}" class="layui-btn layui-btn-sm layui-btn-normal">
                    <i class="layui-icon layui-icon-ok-circle"></i> 已发货列表
                </a>
                <button type="button" class="layui-btn layui-btn-sm" id="add-btn">
                    <i class="layui-icon layui-icon-add-1"></i> 添加兑换
                </button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="export-btn">
                    <i class="layui-icon layui-icon-export"></i> 导出数据
                </button>
            </div>
             -->
            <!-- 数据表格 -->
            <table class="layui-hide" id="redemption-table" lay-filter="redemption-table"></table>
            
            <!-- 表格操作列 -->
            <script type="text/html" id="operation-bar">
                <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
                
                {{# if(d.status == 1 && d.type == 2 && d.shop_id == null){ }}
                <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="assign">分配门店</a>
                {{# } }}
                
                {{# if(d.status == 1 && d.type == 2 && d.shop_id != null){ }}
                <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="express">发货</a>
                {{# } }}
                
                {{# if(d.status == 1){ }}
                <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="cancel">取消</a>
                {{# } }}
                
                {{# if(d.status == 1 && d.type == 1){ }}
                <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="complete">标记完成</a>
                {{# } }}
            </script>
        </div>
    </div>
</div>
{/block}

{block name="scripts"}
<script>
    layui.use(['table', 'form', 'laydate'], function(){
        var table = layui.table;
        var form = layui.form;
        var laydate = layui.laydate;
        var $ = layui.$;
        
        // 日期时间选择器初始化
        laydate.render({
            elem: '#start_time',
            type: 'datetime'
        });
        
        laydate.render({
            elem: '#end_time',
            type: 'datetime'
        });
        
        // 表格初始化
        table.render({
            elem: '#redemption-table',
            url: '{:url("index")}',
            method: 'post',
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            cols: [[
                {field: 'id', title: 'ID', width: 100, sort: true},
                {field: 'card_no', title: '卡号', width: 150},
                {field: 'redemption_time', title: '兑换时间', width: 160},
                {field: 'card_type_name', title: '卡型名称', width: 130},
                {field: 'package_names', title: '套餐', width: 180},
                {field: 'product_names', title: '商品', width: 200, templet: function(d){
                    return d.product_names || '';
                }},
                {field: 'user_name', title: '用户', width: 100},
                {field: 'status_text', title: '状态', width: 80},
                {field: 'type_text', title: '兑换类型', width: 100},
                {field: 'shop_name', title: '门店', width: 130},
                {field: 'name', title: '收货人', width: 100},
                {field: 'phone', title: '联系电话', width: 130},
                {field: 'express_company', title: '快递公司', width: 110},
                {field: 'express_no', title: '快递单号', width: 150},
                {field: 'express_remark', title: '备注', width: 120},
                {title: '操作', toolbar: '#operation-bar', fixed: 'right', width: 200}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res){
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.count,
                    "data": res.data
                };
            },
            done: function (res, curr, count) {
                // 该方法用于解决,使用fixed固定列后,行高和其他列不一致的问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });
        
        // 搜索表单提交
        form.on('submit(search)', function(data){
            table.reload('redemption-table', {
                where: data.field,
                page: {
                    curr: 1
                }
            });
            return false;
        });
        
        // 表格工具条事件
        table.on('tool(redemption-table)', function(obj){
            var data = obj.data;
            var event = obj.event;
            
            if(event === 'detail'){
                // 查看详情
                layer.open({
                    type: 2,
                    title: '兑换详情',
                    content: '{:url("detail")}?id=' + data.id,
                    area: ['80%', '80%'],
                    anim: 2,
                    shadeClose: true,
                });
            } else if(event === 'express'){
                // 填写快递信息
                layer.open({
                    type: 1,
                    title: '填写快递信息',
                    content: '<div class="layui-form layui-form-pane" style="margin: 20px;">' +
                        '<div class="layui-form-item">' +
                        '<label class="layui-form-label">快递公司</label>' +
                        '<div class="layui-input-block">' +
                        '<input type="text" name="express_company" required lay-verify="required" placeholder="请输入快递公司" class="layui-input">' +
                        '</div>' +
                        '</div>' +
                        '<div class="layui-form-item">' +
                        '<label class="layui-form-label">快递单号</label>' +
                        '<div class="layui-input-block">' +
                        '<input type="text" name="express_no" required lay-verify="required" placeholder="请输入快递单号" class="layui-input">' +
                        '</div>' +
                        '</div>' +
                        '<div class="layui-form-item layui-form-text">' +
                        '<label class="layui-form-label">备注</label>' +
                        '<div class="layui-input-block">' +
                        '<textarea name="express_remark" placeholder="请输入备注" class="layui-textarea"></textarea>' +
                        '</div>' +
                        '</div>' +
                        '<div class="layui-form-item">' +
                        '<div class="layui-input-block">' +
                        '<button class="layui-btn" lay-submit lay-filter="expressForm">立即提交</button>' +
                        '<button type="reset" class="layui-btn layui-btn-primary">重置</button>' +
                        '</div>' +
                        '</div>' +
                        '</div>',
                    area: ['500px', '400px'],
                    success: function(layero, index){
                        form.render();
                        
                        // 表单提交
                        form.on('submit(expressForm)', function(data){
                            var field = data.field;
                            
                            // 提交快递信息
                            $.ajax({
                                url: '{:url("updateExpress")}',
                                type: 'post',
                                data: {
                                    id: obj.data.id,
                                    express_company: field.express_company,
                                    express_no: field.express_no,
                                    express_remark: field.express_remark
                                },
                                success: function(res){
                                    if(res.code === 0){
                                        layer.msg(res.msg, {icon: 1});
                                        layer.close(index);
                                        table.reload('redemption-table');
                                    } else {
                                        layer.msg(res.msg, {icon: 2});
                                    }
                                }
                            });
                            
                            return false;
                        });
                    }
                });
            } else if(event === 'complete'){
                // 标记完成
                layer.confirm('确定要将该兑换标记为已完成吗？', function(index){
                    $.ajax({
                        url: '{:url("updateStatus")}',
                        type: 'post',
                        data: {
                            id: data.id,
                            status: 2
                        },
                        success: function(res){
                            if(res.code === 0){
                                layer.msg(res.msg, {icon: 1});
                                obj.update({
                                    status: 2,
                                    status_text: '已完成'
                                });
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }
                    });
                    
                    layer.close(index);
                });
            } else if(event === 'cancel'){
                // 取消兑换
                layer.prompt({
                    formType: 2,
                    title: '请输入取消原因',
                    area: ['300px', '150px']
                }, function(value, index, elem){
                    $.ajax({
                        url: '{:url("updateStatus")}',
                        type: 'post',
                        data: {
                            id: data.id,
                            status: 0,
                            remark: value
                        },
                        success: function(res){
                            if(res.code === 0){
                                layer.msg(res.msg, {icon: 1});
                                obj.update({
                                    status: 0,
                                    status_text: '已取消'
                                });
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }
                    });
                    
                    layer.close(index);
                });
            } else if(event === 'assign') {
                // 分配门店
                location.href = '{:url("assignShop")}?id=' + data.id;
            }
        });
        
        // 添加兑换按钮点击事件
        $('#add-btn').on('click', function(){
            layer.open({
                type: 2,
                title: '添加兑换',
                content: '{:url("add")}',
                area: ['800px', '600px'],
                maxmin: true
            });
        });
        
        // 导出按钮点击事件
        $('#export-btn').on('click', function(){
            var params = $('form').serialize();
            window.location.href = '{:url("export")}?' + params;
        });
    });
</script>
{/block} 