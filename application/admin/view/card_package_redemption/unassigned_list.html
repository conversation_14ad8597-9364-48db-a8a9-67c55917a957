{include file="public/iframeheader"/}

{block name="style"}
<style type="text/css">
    /*这里需要加上 .layui-table 如若不加可能会不生效*/
    .layui-table .layui-table-cell {
        height: auto;
        word-break: normal;
        display: block;
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow: hidden;
        padding: 0 10px !important;
    }
    /* 修复固定列行高不一致问题 */
    .layui-table-fixed .layui-table-cell {
        height: auto;
        vertical-align: middle;
    }
    /* 操作列按钮样式调整 */
    .layui-table td .layui-btn-xs {
        margin: 2px;
        vertical-align: middle;
    }
    /* 多选框样式调整 */
    .layui-table td .layui-form-checkbox {
        margin: 0;
        vertical-align: middle;
    }
    /* 固定列内容垂直居中 */
    .layui-table-fixed .layui-table-body .layui-table-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 40px;
    }
    /* 多选框列垂直居中 */
    .layui-table-fixed-l .layui-table-body .layui-table-cell {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    /* 操作列垂直居中 */
    .layui-table-fixed-r .layui-table-body .layui-table-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
    }
</style>
{/block}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-breadcrumb">
            <a href="{:url('index')}">兑换管理</a>
            <a><cite>未分配门店列表</cite></a>
        </span>
    </div>
    <div class="layui-card-body">
        <div class="layui-form layui-form-pane">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">卡号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="card_no" placeholder="请输入卡号" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">用户名</label>
                    <div class="layui-input-inline">
                        <input type="text" name="user_name" placeholder="请输入用户名" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">兑换时间</label>
                    <div class="layui-input-inline">
                        <input type="text" name="start_time" id="start_time" placeholder="开始时间" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" name="end_time" id="end_time" placeholder="结束时间" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="search">
                        <i class="layui-icon layui-icon-search"></i>搜索
                    </button>
                    <button class="layui-btn layui-btn-primary" type="reset">
                        <i class="layui-icon layui-icon-refresh"></i>重置
                    </button>
                </div>
            </div>
        </div>
        
        <table class="layui-table" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>
{/block}

{block name="js"}
<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="batchAssign">
            <i class="layui-icon layui-icon-share"></i>批量分配门店
        </button>
    </div>
</script>

<script type="text/html" id="opTool">
    <a class="layui-btn layui-btn-xs" lay-event="assign">分配门店</a>
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="detail">详情</a>
</script>

<script>
    layui.use(['table', 'form', 'jquery', 'laydate'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table,
            laydate = layui.laydate;
            
        // 时间选择器
        laydate.render({
            elem: '#start_time',
            type: 'datetime'
        });
        
        laydate.render({
            elem: '#end_time',
            type: 'datetime'
        });
            
        // 渲染表格
        table.render({
            elem: '#dataTable',
            url: '{:url("unassignedList")}',
            toolbar: '#toolbar',
            defaultToolbar: ['filter', 'exports', 'print'],
            cols: [[
                {type: 'checkbox', fixed: 'left', width: 50},
                {field: 'id', title: 'ID', width: 100, sort: true},
                {field: 'card_no', title: '卡号', width: 150},
                {field: 'user_name', title: '用户名', width: 100},
                {field: 'package_names', title: '套餐', width: 180},
                {field: 'product_names', title: '商品', width: 200, templet: function(d){
                    return d.product_names || '';
                }},
                {field: 'name', title: '收货人', width: 100},
                {field: 'phone', title: '联系电话', width: 130},
                {field: 'province', title: '省份', width: 80},
                {field: 'city', title: '城市', width: 80},
                {field: 'area', title: '区县', width: 80},
                {field: 'address', title: '详细地址', width: 200},
                {field: 'redemption_time', title: '兑换时间', width: 160, sort: true},
                {fixed: 'right', title: '操作', toolbar: '#opTool', width: 150}
            ]],
            limit: 15,
            page: true,
            text: {
                none: '暂无相关数据'
            },
            done: function (res, curr, count) {
                // 该方法用于解决,使用fixed固定列后,行高和其他列不一致的问题
                setTimeout(function() {
                    $(".layui-table-main tr").each(function (index, val) {
                        var $mainRow = $(val);
                        var mainHeight = $mainRow.height();
                        
                        // 同步左侧固定列（多选框列）
                        var $leftFixedRow = $(".layui-table-fixed-l .layui-table-body tbody tr").eq(index);
                        if ($leftFixedRow.length) {
                            $leftFixedRow.height(mainHeight);
                        }
                        
                        // 同步右侧固定列（操作列）
                        var $rightFixedRow = $(".layui-table-fixed-r .layui-table-body tbody tr").eq(index);
                        if ($rightFixedRow.length) {
                            $rightFixedRow.height(mainHeight);
                        }
                        
                        // 兼容旧版本的固定列处理
                        var $fixedRow = $(".layui-table-fixed .layui-table-body tbody tr").eq(index);
                        if ($fixedRow.length) {
                            $fixedRow.height(mainHeight);
                        }
                    });
                }, 100);
            }
        });
        
        // 监听搜索
        form.on('submit(search)', function (data) {
            table.reload('dataTable', {
                where: data.field,
                page: {
                    curr: 1
                },
                done: function (res, curr, count) {
                    // 搜索后重新同步固定列行高
                    setTimeout(function() {
                        $(".layui-table-main tr").each(function (index, val) {
                            var $mainRow = $(val);
                            var mainHeight = $mainRow.height();
                            
                            // 同步左侧固定列（多选框列）
                            var $leftFixedRow = $(".layui-table-fixed-l .layui-table-body tbody tr").eq(index);
                            if ($leftFixedRow.length) {
                                $leftFixedRow.height(mainHeight);
                            }
                            
                            // 同步右侧固定列（操作列）
                            var $rightFixedRow = $(".layui-table-fixed-r .layui-table-body tbody tr").eq(index);
                            if ($rightFixedRow.length) {
                                $rightFixedRow.height(mainHeight);
                            }
                            
                            // 兼容旧版本的固定列处理
                            var $fixedRow = $(".layui-table-fixed .layui-table-body tbody tr").eq(index);
                            if ($fixedRow.length) {
                                $fixedRow.height(mainHeight);
                            }
                        });
                    }, 100);
                }
            });
            return false;
        });
        
        // 监听表头工具栏
        table.on('toolbar(dataTable)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id);
            var data = checkStatus.data;
            
            switch (obj.event) {
                case 'batchAssign':
                    if (data.length === 0) {
                        layer.msg('请选择需要分配的订单', {icon: 2});
                        return;
                    }
                    
                    var ids = [];
                    $.each(data, function (index, item) {
                        ids.push(item.id);
                    });
                    
                    location.href = '{:url("batchAssignShop")}?ids=' + ids.join(',');
                    break;
            }
        });
        
        // 监听行工具事件
        table.on('tool(dataTable)', function (obj) {
            var data = obj.data;
            
            if (obj.event === 'assign') {
                location.href = '{:url("assignShop")}?id=' + data.id;
            } else if (obj.event === 'detail') {
                location.href = '{:url("detail")}?id=' + data.id;
            }
        });
    });
</script>
{/block} 