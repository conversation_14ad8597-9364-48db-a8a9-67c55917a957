{include file="public/iframeheader"/}

{block name="css"}
<style>
    .layui-form-item .required:after {
        content: '*';
        color: red;
        position: absolute;
        margin-left: 4px;
        font-weight: bold;
        line-height: 1.8em;
        top: 6px;
        right: 5px;
    }
</style>
{/block}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-breadcrumb">
            <a href="{:url('unassignedList')}">未分配订单</a>
            <a><cite>批量分配门店</cite></a>
        </span>
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="">
            <input type="hidden" name="ids" value="{$ids}">
            
            <div class="layui-form-item" style="display: none;">
                <label class="layui-form-label">订单ID</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" disabled>{$ids}</textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label required">分配门店</label>
                <div class="layui-input-block">
                    <select name="shop_id" lay-verify="required" lay-reqtext="请选择门店">
                        <option value="">请选择门店</option>
                        {foreach $shops as $shop}
                        <option value="{$shop.id}">{$shop.title}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" placeholder="请输入备注" class="layui-textarea"></textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formSubmit">确认分配</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="history.back(-1);">返回</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name="js"}
<script>
    layui.use(['form', 'layer'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer;

        // 监听提交
        form.on('submit(formSubmit)', function (data) {
            var index = layer.msg('正在提交数据...', {icon: 16, time: false, shade: 0.3});
            
            $.ajax({
                url: '{:url("batchAssignShop")}',
                type: 'post',
                dataType: 'json',
                data: data.field,
                success: function (res) {
                    layer.close(index);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1, time: 2000}, function () {
                            window.location.href = '{:url("unassignedList")}';
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 2000});
                    }
                },
                error: function (xhr) {
                    layer.close(index);
                    layer.msg('请求失败', {icon: 2, time: 2000});
                }
            });
            
            return false;
        });
    });
</script>
{/block} 