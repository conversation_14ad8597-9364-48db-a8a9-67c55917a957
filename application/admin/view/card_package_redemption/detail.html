{include file="public/iframeheader"/}
{block name="title"}卡兑换详情{/block}

{block name="style"}
<style type="text/css">
    .detail-box {
        padding: 20px;
    }
    .detail-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    .detail-item {
        margin-bottom: 10px;
    }
    .detail-label {
        display: inline-block;
        width: 100px;
        font-weight: bold;
    }
    .status-tag {
        display: inline-block;
        padding: 2px 5px;
        border-radius: 2px;
        color: #fff;
        font-size: 12px;
    }
    .status-canceled {
        background-color: #FF5722;
    }
    .status-redeemed {
        background-color: #1E9FFF;
    }
    .status-completed {
        background-color: #5FB878;
    }
    .info-section {
        margin-bottom: 20px;
    }
</style>
{/block}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="detail-box">
                <!-- 兑换基本信息 -->
                <div class="info-section">
                    <div class="detail-title">基本信息</div>
                    <div class="detail-item">
                        <span class="detail-label">兑换ID：</span>
                        <span>{$info.id}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">卡号：</span>
                        <span>{$info.card_no}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">用户：</span>
                        <span>{$info.user_name}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">兑换时间：</span>
                        <span>{$info.redemption_time}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">状态：</span>
                        <span class="status-tag {eq name='$info.status' value='0'}status-canceled{/eq} {eq name='$info.status' value='1'}status-redeemed{/eq} {eq name='$info.status' value='2'}status-completed{/eq}">{$info.status_text}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">兑换类型：</span>
                        <span>{$info.type_text}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">门店：</span>
                        <span>{$info.shop_name|default='--'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">备注：</span>
                        <span>{$info.remark|default='--'}</span>
                    </div>
                </div>
                
                <!-- 收货地址信息 -->
                {eq name="$info.type" value="2"}
                <div class="info-section">
                    <div class="detail-title">收货信息</div>
                    <div class="detail-item">
                        <span class="detail-label">收货人：</span>
                        <span>{$info.name|default='--'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">联系电话：</span>
                        <span>{$info.phone|default='--'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">收货地址：</span>
                        <span>
                            {$info.province|default=''}{$info.city|default=''}{$info.area|default=''}{$info.address|default=''}
                            {empty name="$info.province"}{empty name="$info.city"}{empty name="$info.area"}{empty name="$info.address"}--{/empty}{/empty}{/empty}{/empty}
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">邮政编码：</span>
                        <span>{$info.postal_code|default='--'}</span>
                    </div>
                </div>
                
                <!-- 快递信息 -->
                <div class="info-section">
                    <div class="detail-title">快递信息</div>
                    <div class="detail-item">
                        <span class="detail-label">快递公司：</span>
                        <span>{$info.express_company|default='--'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">快递单号：</span>
                        <span>{$info.express_no|default='--'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">发货时间：</span>
                        <span>{$info.express_time|default='--'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">快递备注：</span>
                        <span>{$info.express_remark|default='--'}</span>
                    </div>
                </div>
                {/eq}
                
                <!-- 套餐明细 -->
                <div class="info-section">
                    <div class="detail-title">套餐明细</div>
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>套餐名称</th>
                                <th>套餐价格</th>
                            </tr>
                        </thead>
                        <tbody>
                            {volist name="details" id="detail"}
                            <tr>
                                <td>{$detail.package_name}</td>
                                <td>{$detail.package_price}</td>
                            </tr>
                            {/volist}
                        </tbody>
                    </table>
                </div>
                
                <!-- 商品明细 -->
                <div class="info-section">
                    <div class="detail-title">商品明细</div>
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>规格</th>
                                <th>单价</th>
                                <th>数量</th>
                                <th>小计</th>
                            </tr>
                        </thead>
                        <tbody>
                            {volist name="products" id="product"}
                            <tr>
                                <td>{$product.product_name}</td>
                                <td>{$product.spec_values}</td>
                                <td>{$product.product_price}</td>
                                <td>{$product.quantity}</td>
                                <td>{$product.product_price * $product.quantity}</td>
                            </tr>
                            {/volist}
                        </tbody>
                    </table>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-primary" onclick="parent.layer.close(parent.layer.getFrameIndex(window.name));">关闭</button>
                        {eq name="$info.status" value="1"}
                        <button class="layui-btn" id="btn-complete">标记完成</button>
                        <button class="layui-btn layui-btn-danger" id="btn-cancel">取消兑换</button>
                        {eq name="$info.type" value="2"}
                        {empty name="$info.express_no"}
                        <button class="layui-btn layui-btn-normal" id="btn-express">填写快递信息</button>
                        {/empty}
                        {/eq}
                        {/eq}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="scripts"}
<script>
    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;
        
        // 标记完成
        $('#btn-complete').on('click', function(){
            layer.confirm('确定要将该兑换标记为已完成吗？', function(index){
                $.ajax({
                    url: '{:url("updateStatus")}',
                    type: 'post',
                    data: {
                        id: '{$info.id}',
                        status: 2
                    },
                    success: function(res){
                        if(res.code === 0){
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function(){
                                parent.layui.table.reload('redemption-table');
                                parent.layer.close(parent.layer.getFrameIndex(window.name));
                            }, 1000);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                
                layer.close(index);
            });
        });
        
        // 取消兑换
        $('#btn-cancel').on('click', function(){
            layer.prompt({
                formType: 2,
                title: '请输入取消原因',
                area: ['300px', '150px']
            }, function(value, index, elem){
                $.ajax({
                    url: '{:url("updateStatus")}',
                    type: 'post',
                    data: {
                        id: '{$info.id}',
                        status: 0,
                        remark: value
                    },
                    success: function(res){
                        if(res.code === 0){
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function(){
                                parent.layui.table.reload('redemption-table');
                                parent.layer.close(parent.layer.getFrameIndex(window.name));
                            }, 1000);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                
                layer.close(index);
            });
        });
        
        // 填写快递信息
        $('#btn-express').on('click', function(){
            layer.open({
                type: 1,
                title: '填写快递信息',
                content: '<div class="layui-form layui-form-pane" style="margin: 20px;">' +
                    '<div class="layui-form-item">' +
                    '<label class="layui-form-label">快递公司</label>' +
                    '<div class="layui-input-block">' +
                    '<input type="text" name="express_company" required lay-verify="required" placeholder="请输入快递公司" class="layui-input">' +
                    '</div>' +
                    '</div>' +
                    '<div class="layui-form-item">' +
                    '<label class="layui-form-label">快递单号</label>' +
                    '<div class="layui-input-block">' +
                    '<input type="text" name="express_no" required lay-verify="required" placeholder="请输入快递单号" class="layui-input">' +
                    '</div>' +
                    '</div>' +
                    '<div class="layui-form-item layui-form-text">' +
                    '<label class="layui-form-label">备注</label>' +
                    '<div class="layui-input-block">' +
                    '<textarea name="express_remark" placeholder="请输入备注" class="layui-textarea"></textarea>' +
                    '</div>' +
                    '</div>' +
                    '<div class="layui-form-item">' +
                    '<div class="layui-input-block">' +
                    '<button class="layui-btn" lay-submit lay-filter="expressForm">立即提交</button>' +
                    '<button type="reset" class="layui-btn layui-btn-primary">重置</button>' +
                    '</div>' +
                    '</div>' +
                    '</div>',
                area: ['500px', '400px'],
                success: function(layero, index){
                    form.render();
                    
                    // 表单提交
                    form.on('submit(expressForm)', function(data){
                        var field = data.field;
                        
                        // 提交快递信息
                        $.ajax({
                            url: '{:url("updateExpress")}',
                            type: 'post',
                            data: {
                                id: '{$info.id}',
                                express_company: field.express_company,
                                express_no: field.express_no,
                                express_remark: field.express_remark
                            },
                            success: function(res){
                                if(res.code === 0){
                                    layer.msg(res.msg, {icon: 1});
                                    setTimeout(function(){
                                        parent.layui.table.reload('redemption-table');
                                        parent.layer.close(parent.layer.getFrameIndex(window.name));
                                    }, 1000);
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                        
                        return false;
                    });
                }
            });
        });
    });
</script>
{/block} 