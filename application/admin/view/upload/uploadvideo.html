<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>视频上传</title>
    <include file="Public/simpleheader"/>
    <style type="text/css">
    html,body{margin:0;padding:0;width:{$Width}px;height:{$Height}px;}
	.uploadpic{position:relative;}
	#ImgPr{border:0;}
	.puloadpic{position:absolute;width:{$Width}px;height:{$Height}px;}
	.inputfile{position:absolute;top:0px;left:0px;z-index:100;-moz-opacity:0.3;opacity:0.3;filter: alpha(opacity=30);background:#4f99c6;cursor:pointer;}
	#uploadicon{font-size:200px;overflow:hidden;display:block;width:{$Width}px;height:{$Height}px;-moz-opacity:0.0;opacity:0.0;filter: alpha(opacity=0);cursor:pointer;}
	.uploadtext{position:absolute;top:0px;left:0px;z-index:50;color:red;font-weight:bold;text-align:center;width:{$Width}px;height:{$Height}px;line-height:{$Height}px;cursor:pointer;overflow:hidden;}
        #uploadicon{
            opacity:1;
            height: 20px;
            width: 65px;
            font-size: 12px;
        }
    </style>
    <!--[if !IE]> -->
		<script type="text/javascript">
			window.jQuery || document.write("<script src='__STATIC__/admin/js/jquery.js'>"+"<"+"/script>");
		</script>
		<!-- <![endif]-->
		<!--[if IE]>
		<script type="text/javascript">
		 window.jQuery || document.write("<script src='__STATIC__/admin/js/jquery1x.js'>"+"<"+"/script>");
		</script>
		<![endif]-->
		<script type="text/javascript">
			if('ontouchstart' in document.documentElement) document.write("<script src='__STATIC__/admin/js/jquery.mobile.custom.js'>"+"<"+"/script>");
		</script>
</head>

<body>
    <form enctype="multipart/form-data" id="PostMe"   action="?" method="post" name="upform">
        <input type="hidden" value="{$Width}"    name="Width">
        <input type="hidden" value="{$Height}"   name="Height">
        <input type="hidden" value="{$BackCall}" name="BackCall">
        <video style="z-index:9999"  width="{$Width}" height="{$Height}"  controls >
		      <source src="{$Video|default='__STATIC__/qwadmin/images/nopic.gif'}"    type="video/mp4">
		</video>
        <a onclick="$('#uploadicon').click()" class='layui-btn'>上传视频</a>
        <input type="file" name="video" id="uploadicon" style="display:none" value="upload"/>


    </form>
    <script type="text/javascript">
        $('#uploadicon').on("change",function(){
           $('#PostMe').submit();
        })
       $('#{$BackCall}',parent.document).val("{$Video}");
    </script>
</body>

</html>
