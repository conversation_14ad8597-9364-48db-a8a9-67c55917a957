<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}
<link rel="stylesheet" type="text/css" href="__STATIC__/admin/jquery-easyui-1.5.2/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="__STATIC__/admin/jquery-easyui-1.5.2/themes/icon.css">
<script type="text/javascript" src="__STATIC__/admin/jquery-easyui-1.5.2/jquery.easyui.min.js"></script>


<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body" pad15>
                    <div class="layui-form" wid100 lay-filter="">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">价格</label>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <input type="text" name="price" placeholder="￥" value="{$info['price']}" autocomplete="off" lay-verify="number" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <label class="layui-form-label">已绑定商品</label>
                        <div style="width: 80%;margin-left: 14%;padding-bottom: 3%">
                            <table class="layui-hide" id="goods-list"></table>
                        </div>

                        <input type="hidden" name="id" value="{$info['id']}">

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="sub">确认保存</button>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>



</body>
<script>
    layui.use('table', function(){
        var table = layui.table;
        var form = layui.form;


        // 已知数据渲染
        var inst = table.render({
            elem: '#goods-list',
            cols: [[ //标题栏
                { type: 'numbers', title: '序号' }
                , { field: 'goodsname', title: '商品名称' }
                , { field: 'card_type_classify_name', title: '所属商品模板' }
                , { field: 'card_template_name', title: '所属模板分类' }
                , {
                    field: 'goodsimg', title: '商品图片', width: 150, templet: function (item) {
                        return '<img onclick="showimg(this);"   style=" width: 50%;height:100%" src="' + item.goodsimg + '">';
                    }
                }
                , { field: 'price', title: '商品价格', templet: function (item) {
                        return '<div class="layui-input-inline">\n' +
                            '<input type="text" id="son_price_' + item.id +'" name="son_price_' + item.id +' " placeholder="￥" value="'+ item.price +'" autocomplete="off" lay-verify="number" class="layui-input">\n' +
                            '</div>';
                    } }
            ]],
            data: {$list},
        });


        form.on('submit(sub)', function (data) {
            var table = layui.table.cache['goods-list'];
            var select_list = [];
            for (var a in table) {
                var price = document.getElementById("son_price_" + table[a].id).value;
                select_list[a] = {
                    id: table[a].id,
                    price: price
                }
            }
            data.field.select_list = select_list;
            load = layer.load(2, {shade: [0.1, '#fff']});
            $.post("{:url('ajax_edit_goods_price')}", data.field, function (data) {
                layer.close(load);
                if (data.code == 0) {
                    layer.msg(data.msg);
                    setInterval(function () {
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    }, 1500);
                } else {
                    layer.msg(data.msg);
                }
            }, "json");

            return false;
        });
    });

    //点击放大图片
    function showimg(t) {
        var src = $(t).attr("src");
        if (src == '') {
            layer.msg("图为为空！");
            return false;
        }
        layer.open({
            type: 1,
            title: false,
            area: '516px',
            content: '<img style="display: inline-block; width: 100%; height: 100%;" src="' + src + '">'
        });
    }

</script>
</html>
