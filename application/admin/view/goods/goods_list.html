<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}
<link rel="stylesheet" href="__STATIC__/admin//layui/css/layui.css">
<link rel="stylesheet" href="__STATIC__/admin/dtree__STATIC__/layui_ext/dtree/dtree.css">
<link rel="stylesheet" href="__STATIC__/admin/dtree__STATIC__/layui_ext/dtree/font/dtreefont.css">
<script type="text/javascript" src="__STATIC__/admin/layui/layui.js"></script>
<style>
    .layui-table-cell {
        height: auto;
        line-height: 28px;
    }
</style>

<body>
    <br />
    <div id="main">
        <div style="float:left;width:15%;">
            <div class="easyui-panel" style="padding:5px;border:0px solid #fff;">
                <ul id="demoTree" class="dtree" data-id="0"></ul>
            </div>
        </div>

        <div style="float:right;width:85%;">
            <input type='hidden' name='keynum'>
            <input type='hidden' name='id'>
            <div class="demoTable layui-form">


                <div class="layui-inline">
                    <label class="layui-form-label ">查询条件：</label>
                    <div class="layui-input-block">
                        <select name="field">
                            <option value='title'>商品名称</option>
                        </select>
                    </div>

                </div>


                <div class='layui-inline'>
                    <input class="layui-input" name="keyword" id="demoReload" autocomplete="off">
                </div>


                <button class="layui-btn  layui-btn-sm" onclick="get_table()">搜索</button>
            </div>

            <br />

            <script type="text/html" id="test-table-toolbar-toolbarDemo">
                <div class="layui-btn-container">
                {if condition="!empty($join_id)"}
                    <button class="layui-btn layui-btn-sm" lay-event="add_check">添加所选</button>
                {else /}
                    {if condition="$status eq '1'"}
                    <!-- <button class="layui-btn layui-btn-sm" lay-event="add_goods">添加商品</button> -->
                    <button class="layui-btn layui-btn-sm" lay-event="xiajia">标记下架</button>
                    {elseif condition="$status eq '0'"/}
                    <button class="layui-btn layui-btn-sm" lay-event="shangjia">标记上架</button>
                    {/if}
                {/if}

                </div>
            </script>

            <table id="demo" lay-filter="demo">
            </table>
            <script type='text/html' id="barDemo">
                {if condition="!empty($join_id)"}
                <a class='layui-btn layui-btn-sm' lay-event="zanwu">暂无</a>
                {else /}
<!--                <a class='layui-btn layui-btn-sm' lay-event="edit-price">修改价格</a>-->
                <a class='layui-btn layui-btn-sm' lay-event="edit">修改</a>
                <a class='layui-btn layui-btn-sm' lay-event="del">删除</a>
                {/if}
            </script>
        </div>
        <script>
            get_table();
            function get_table() {
                var id = $("[name=id]").val();
                var field = $("[name=field]").val();
                var keyword = $("[name=keyword]").val();
                var status = "{$status}";
                var join_id = "{$join_id}";
                var action = "{$action}";
                layui.use('table', function () {
                    var table = layui.table;
                    table.render({
                        elem: '#demo'
                        , toolbar: '#test-table-toolbar-toolbarDemo'
                        , defaultToolbar: []
                        , url: "{:url('ajax_get_goods_list')}?id=" + id + "&field=" + field + "&keyword=" + keyword + "&status=" + status
                            + "&join_id=" + join_id + "&action=" + action
                        , page: true //开启分页
                        , cols: [[ //表头
                            { type: 'checkbox' }
                            , { type: 'numbers', title: '序号' }
                            , { field: 'goodsname', title: '商品名称' }
                            , { field: 'supplier_name', title: '供应商' }
                            , { field: 'goodsclassify', title: '所属分类' }
                            , { field: 'market_price', title: '划线价' }
                            , { field: 'price', title: '商品价格' }
                            , {
                                field: 'goodsimg', title: '商品图片', width: 150, templet: function (item) {
                                    if (item.goodsimg != '') {
                                        return '<img onclick="showimg(this);" style=" width: 50%;height:100%" src="' + item.goodsimg + '">';
                                    } else {
                                        return '';
                                    }
                                }
                            }
                            , { field: 'o', title: '排序号' }
                            , { field: 'status', title: '状态' }
                            , { fixed: 'right', title: "操作", toolbar: '#barDemo', width: 260}
                        ]],
                        done: function (res, curr, count) {
                            setColor('.layui-table');
                        }
                    })

                })
            }

            layui.extend({
                dtree: '__STATIC__/admin/dtree__STATIC__/layui_ext/dtree/dtree'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
            }).use(["table", 'dtree', 'jquery'], function () {
                var table = layui.table;

                var dtree = layui.dtree, layer = layui.layer, $ = layui.jquery, table = layui.table;

                // 初始化树
                var DemoTree = dtree.render({
                    elem: "#demoTree",
                    url: "{:url('get_goodsclassify_treedata')}" // 使用url加载（可与data加载同时存在）
                });
                // 绑定节点点击
                dtree.on("node('demoTree')", function (obj) {
                    $('#select-classify').val(obj.param.nodeId);
                    var id = obj.param.nodeId;
                    table.reload('demo', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        },
                        where: {
                            id: id
                        }
                    });
                });


                table.on('checkbox(demo)', function (obj) {
                    var checkStatus = table.checkStatus('demo');

                })
                //头工具栏事件
                table.on('toolbar(demo)', function (obj) {
                    var checkStatus = table.checkStatus(obj.config.id);
                    switch (obj.event) {
                        case 'add_goods':
                            layer.open({
                                type: 2,
                                title: "添加商品",
                                content: "{:url('add_goods')}",
                                maxmin: true,
                                area: ["98%", "98%"],
                                end: function () {
                                    get_table();
                                }
                            })
                            break;
                        case 'xiajia':
                            var alldata1 = checkStatus.data;
                            var alldata = JSON.stringify(alldata1);
                            var index = layer.load();
                            $.post("{:url('ajax_xiajia_goods')}", { alldata: alldata }, function (data) {
                                layer.closeAll('loading');
                                if (data.sta != 1) {
                                    layer.msg(data.msg);
                                } else {
                                    //关闭当前窗口
                                    layer.msg(data.msg);
                                    setTimeout(function () {
                                        get_table();
                                    }, 1500);
                                }
                            }, "json")
                            break;
                        case 'shangjia':
                            var alldata1 = checkStatus.data;
                            var alldata = JSON.stringify(alldata1);
                            var index = layer.load();
                            $.post("{:url('ajax_shangjia_goods')}", { alldata: alldata }, function (data) {
                                layer.closeAll('loading');
                                if (data.sta != 1) {
                                    layer.msg(data.msg);
                                } else {
                                    //关闭当前窗口
                                    layer.msg(data.msg);
                                    setTimeout(function () {
                                        get_table();
                                    }, 1500);
                                }
                            }, "json")
                            break;
                        case 'add_check':
                            var alldata1 = checkStatus.data;
                            var alldata = JSON.stringify(alldata1);
                            var index = layer.load();
                            var action = "{$action}";
                            var join_id = "{$join_id}";
                            $.post("{:url('ajax_add_check')}", { alldata: alldata, action: action, join_id: join_id }, function (data) {
                                layer.closeAll('loading');
                                if (data.sta != 1) {
                                    layer.msg(data.msg);
                                } else {
                                    window.parent.callback(data.msg);
                                    // layer.msg(data.msg);
                                    // window.parent.callback();
                                }
                            }, "json")
                            break;
                    };
                });
            })

            layui.use("table", function () {
                var table = layui.table;
                var form = layui.form;
                table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                    var data = obj.data //获得当前行数据
                        , layEvent = obj.event; //获得 lay-event 对应的值
                    var id = data.id;
                    if (layEvent === 'edit') {
                        layer.open({
                            type: 2,
                            title: "修改商品",
                            content: "{:url('add_goods')}?id=" + id,
                            maxmin: true,
                            area: ["98%", "98%"],
                            end: function () {
                                // get_table();
                            }
                        })
                    } else if (layEvent === 'del') {
                        layer.confirm('确定要删除该商品吗？', function (index) {
                            var index = layer.load();
                            $.post("{:url('ajax_del_goods')}", { ids: data.id }, function (data) {
                                layer.msg(data.msg);
                                layer.closeAll('loading');
                                if (data.sta == 1) {
                                    get_table();
                                }
                            }, "json")
                        });
                    }
                });
            })
            //点击放大图片
            function showimg(t) {
                var src = $(t).attr("src");
                if (src == '') {
                    layer.msg("图为为空！");
                    return false;
                }
                layer.open({
                    type: 1,
                    title: false,
                    area: '516px',
                    content: '<img style="display: inline-block; width: 100%; height: 100%;" src="' + src + '">'
                });
            }
            //时间转换
            function createTime(v) {
                var v = v * 1000; //js的时间戳要*1000
                var date = new Date(v);
                var y = date.getFullYear();
                var m = date.getMonth() + 1;
                m = m < 10 ? '0' + m : m;
                var d = date.getDate();
                d = d < 10 ? ("0" + d) : d;
                var h = date.getHours();
                h = h < 10 ? ("0" + h) : h;
                var M = date.getMinutes();
                M = M < 10 ? ("0" + M) : M;
                var S = date.getSeconds();
                S = S < 10 ? ("0" + S) : S;
                var str = y + "-" + m + "-" + d + " " + h + ":" + M + ":" + S;
                return str;
            }
            function setColor(tableClassName) {
                var $table = $(tableClassName).eq(1);
                //console.log($table)
                if ($table.length > 0) {
                    //遍历所有行
                    $table.find('tr').each(function () {
                        var open = $(this).find('td[data-field="is_error"]').find('div').text()
                        if (open == "是") {   //给状态为2的数据行设置背景色
                            $(this).attr('style', "background:#de806d;color:#000");
                        }
                    })
                }
            }
            function callback(msg) {
                layer.msg(msg, { time: 1500 }, function (data) {
                    layer.closeAll();
                    // get_table();
                })
            }
        </script>


</body>

</html>
