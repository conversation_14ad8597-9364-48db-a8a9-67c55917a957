<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>审核商品</title>
  {include file="public/iframeheader"/}
  <!-- 编辑器源码文件 -->
</head>

<body>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body" pad15>



            <div class="layui-form" wid100 lay-filter="">
              <input type="hidden" name="id" value="{$goods_info['id']|default=''}">



              <div class="layui-form-item">
                <label class="layui-form-label">商品名称</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                <div class="layui-input-inline">
                  <input type="text" name="goodsname" style="width: 400px;"
                    value="{$goods_info['goodsname']|default=''}" lay-verify="required" class="layui-input ">
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">供应商</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                <div class="layui-input-inline" style="width: 400px;">
                  <select name="supplier_id" lay-verify="required" lay-search>
                    {foreach name='supplier_list' key='key' item='value'}
                    {if condition="$goods_info['supplier_id'] eq $value['id']" }
                    <option value="{$value['id']}" selected> {$value["name"]}</option>
                    {else /}
                    <option value="{$value['id']}"> {$value["name"]}</option>
                    {/if}
                    {/foreach}
                  </select>
                </div>
              </div>

              {if $plat_config['is_first_category'] == 1}

              <div class="layui-form-item">
                <label class="layui-form-label">商品分类</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                <div class="layui-input-inline" style="width: 400px;">
                  <select name="goodsclassify" lay-verify="required" lay-search>
                    {foreach name='goodsclassify' key='key' item='value'}
                    {if condition="$goods_info['goodsclassify'] eq $value['id']" }
                    <option value="{$value['id']}" selected> {$value["classifyname"]}</option>
                    {else /}
                    <option value="{$value['id']}"> {$value["classifyname"]}</option>
                    {/if}
                    {/foreach}
                  </select>
                </div>
              </div>

              {else}

              <div class="layui-form-item">
                <label class="layui-form-label">商品分类</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                <div class="layui-input-inline" style="width: 400px;">
                  <select name="goodsclassify" lay-verify="required" lay-search>
                    <option value="">请选择</option>
                    {foreach goodsclassify as $v}
                    <optgroup label="{$v['classifyname']}">
                      {foreach $v['children'] as $val}
                      {if $goods_info['goodsclassify'] == $val['id']}
                      <option value="{$val['id']}" selected>{$val['classifyname']}</option>
                      {else/}
                      <option value="{$val['id']}">{$val['classifyname']}</option>
                      {/if}
                    </optgroup>
                    {/foreach}
                    {/foreach}
                  </select>
                </div>
              </div>
              {/if}




<!--              <div class="layui-form-item">-->
<!--                <label class="layui-form-label">商品分类</label>-->
<!--                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>-->
<!--                <div class="layui-input-inline" style="width: 400px;">-->
<!--                  <select name="goodsclassify" lay-verify="required" lay-search>-->
<!--                    {foreach name='goodsclassify' key='key' item='value'}-->
<!--                    {if condition="$goods_info['goodsclassify'] eq $value['id']" }-->
<!--                    <option value="{$value['id']}" selected> {$value["classifyname"]}</option>-->
<!--                    {else /}-->
<!--                    <option value="{$value['id']}"> {$value["classifyname"]}</option>-->
<!--                    {/if}-->
<!--                    {/foreach}-->
<!--                  </select>-->
<!--                </div>-->
<!--              </div>-->

              <div class="layui-form-item">
                <label class="layui-form-label">划线价</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                <div class="layui-input-inline">
                  <input type="number" name="market_price" lay-verify="required" value="{$goods_info['market_price']|default='0'}" class="layui-input">
                </div>
                <div class="layui-input-inline layui-input-company"></div>
                <div class="layui-form-mid layui-word-aux"></div>
              </div>

              <div class="layui-form-item">
                <label class="layui-form-label">商品价格</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                <div class="layui-input-inline">
                  <input type="number" name="price" lay-verify="required" value="{$goods_info['price']|default='0'}" class="layui-input">
                </div>
                <div class="layui-input-inline layui-input-company"></div>
                <div class="layui-form-mid layui-word-aux"></div>
              </div>

              <div class="layui-form-item">
                <label class="layui-form-label">商品图片</label>
                <div class="layui-input-block layui-btn-container ">
                  {:UpImage("goodsimg",200,200,$goods_info.goodsimg)}
                </div>
              </div>

              <div class="layui-form-item">
                <label class="layui-form-label">商品详情</label>
                <div class="layui-input-block">
                  {:BatchImage("detail",200,200,$goods_info['detail'])}
                </div>
                <div class="layui-form-mid layui-word-aux"></div>
              </div>

              <div class="layui-form-item">
                <label class="layui-form-label">是否上架</label>
                <div class="layui-input-block">
                  <input type="checkbox" name="status" {if
                    condition="$goods_info['status'] eq '1'   || isset($goods_info['status']) eq  false " }
                    checked='checked' {/if} lay-skin="switch">
                </div>
              </div>


              <div class="layui-form-item">
                <label class="layui-form-label">排序号</label>
                <div class="layui-input-inline">
                  <input type="number" name="o" value="{$goods_info['o']|default='0'}" class="layui-input">
                </div>
                <div class="layui-input-inline layui-input-company"></div>
                <div class="layui-form-mid layui-word-aux">排序号 小的在前</div>
              </div>


              <div class="layui-form-item">
                <div class="layui-input-block">
                  <button class="layui-btn" lay-submit lay-filter="sub2">确认</button>
                </div>
              </div>

            </div>

          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    //一般直接写在一个js文件中
    layui.use(['element', 'form', 'layedit'], function () {
      var form = layui.form;
      form.on('submit(sub2)', function (data) {
        var id = $("[name=id]").val();
        load = layer.load(2, { shade: [0.1, '#fff'] });
        $.post("{:url('ajax_add_goods')}", data.field, function (data) {
          layer.close(load);
          if (data.sta == 1) {
            if (id == '') { //新增
              // window.parent.callback(data.msg);
              window.location.reload();
            } else { //修改不刷新页面
              layer.msg(data.msg);
              setInterval(function () {
                window.parent.layer.closeAll();//关闭弹窗
              }, 1500);
            }
          } else {
            layer.msg(data.msg);
          }
        }, "json");

        return false;
      });
    });
  </script>

</body>

</html>
