<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}
<link rel="stylesheet" href="__STATIC__/admin//layui/css/layui.css">
<link rel="stylesheet" href="__STATIC__/admin/dtree__STATIC__/layui_ext/dtree/dtree.css">
<link rel="stylesheet" href="__STATIC__/admin/dtree__STATIC__/layui_ext/dtree/font/dtreefont.css">
<script type="text/javascript" src="__STATIC__/admin/layui/layui.js"></script>
<div style="margin-top:25px;">
    <input type="hidden" name="select-classify" id="select-classify">

    <div style="width: 15%;float: left">
        <ul id="demoTree" class="dtree" data-id="0"></ul>
    </div>

    <div style="width: 65%;margin:auto">

        <div style="margin-top: 10px;margin-left: 25px;">
            <button class="layui-btn layuiadmin-btn-useradmin   layui-btn-sm" link="add_classify">添加分类</button>
        </div>

        <table id="test" lay-filter="test"></table>
        <div id="page"></div>
        <script type='text/html' id="barDemo">
            <a class='layui-btn layui-btn-sm' lay-event="edit">修改</a>
            <a class='layui-btn layui-btn-sm' lay-event="del">删除</a>
        </script>
    </div>
</div>

<script type="text/javascript">
    layui.extend({
        dtree: '__STATIC__/admin/dtree__STATIC__/layui_ext/dtree/dtree'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
    }).use(['table', 'dtree', 'jquery'], function () {
        var dtree = layui.dtree, layer = layui.layer, $ = layui.jquery, table = layui.table;
        var is_first_category = "{$is_first_category}";
        // 初始化树 如果用户配置不是一级分类的话

        if (is_first_category != 1) {
            console.log(111);
            var DemoTree = dtree.render({
                elem: "#demoTree",
                url: "{:url('get_goodsclassify_treedata')}", // 使用url加载（可与data加载同时存在）
                toolbar:true,
                // toolbarShow:[], // 默认按钮制空
                toolbarWay:"fixed",
                // toolbarExt:[{
                //     toolbarId: "testAdd", icon: "dtree-icon-wefill", title: "自定义新增", handler: function (node, $div) {
                //         layer.msg(JSON.stringify(node));
                //         // 你可以在此添加一个layer.open，里面天上你需要添加的表单元素，就跟你写新增页面是一样的
                //     }
                // }]
            });
            // 绑定节点点击
            dtree.on("node('demoTree')", function (obj) {
                if (obj.param.level == '2'){
                    return false;
                }
                $('#select-classify').val(obj.param.nodeId);
                var id = obj.param.nodeId;
                table.reload('test', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    },
                    where: {
                        pid: id
                    }
                });
            });
        }


        table.render({
            elem: '#test'
            , url: "{:url('ajax_get_goodsclassify')}"
            , page: true //开启分页
            , cols: [[ //表头
                {field: 'id', title: 'id'},
                {field: 'classifyname', title: '名称'},
                {field: 'goods_count', title: '商品总数'},
                {field: 'o', title: '排序'},
                { fixed: 'right', title: "操作", toolbar: '#barDemo', width: 260}
            ]],
        });

        table.on('tool(test)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            var id = data.id;
            if (layEvent === 'del') {
                layer.confirm('确定要删除该分类吗？', function (index) {
                    var index = layer.load();
                    $.post("{:url('ajax_del_goodsclassify')}", { ids: data.id }, function (data) {
                        layer.msg(data.msg);
                        layer.closeAll('loading');
                        if (data.sta == 1) {

                        }
                    }, "json")
                });
            } else if (layEvent === 'edit') {
                layer.open({
                    type: 2,
                    title: "修改分类",
                    content: "{:url('add_goodsclassify')}?id=" + id,
                    maxmin: true,
                    area: ["98%", "98%"],
                    end: function () {

                    }
                })
            }
        });


    });

    //添加分类
    $("[link=add_classify]").click(function () {
        var pid = $('#select-classify').val();
        layer.open({
            type: 2,
            title: "添加商品分类",
            content: "{:url('add_goodsclassify')}?pid=" + pid,
            maxmin: true,
            area: ["98%", "98%"],
            end: function () {
                var id = obj.param.nodeId;
                table.reload('test', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    },
                    where: {
                        pid: id
                    }
                });
            }
        })
    });
</script>


</html>
