<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>售后申请管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/admin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/static/admin/style/admin.css" media="all">
</head>
<body>

<div class="layuiadmin-tabsbody-item layui-show">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <span class="layui-inline">
                        售后申请管理
                        <span class="layui-breadcrumb layui-inline" lay-separator="/">
                            <a href="javascript:;" onclick="location.reload();">刷新</a>
                        </span>
                    </span>
                </div>
                <div class="layui-card-body">
                    
                    <!-- 搜索条件 -->
                    <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">状态筛选</label>
                                <div class="layui-input-inline">
                                    <select name="status" id="status" lay-search>
                                        <option value="">全部状态</option>
                                        <option value="0">待审核</option>
                                        <option value="1">审核通过</option>
                                        <option value="2">审核拒绝</option>
                                        <!-- <option value="8">退款中</option>
                                        <option value="9">退款成功</option> -->
                                        <option value="10">售后完成</option>
                                        <option value="-1">已取消</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="layui-inline">
                                <label class="layui-form-label">搜索字段</label>
                                <div class="layui-input-inline">
                                    <select name="field" id="field">
                                        <option value="after_sales_no">售后单号</option>
                                        <option value="order_no">订单号</option>
                                        <option value="user_name">用户昵称</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="layui-inline">
                                <label class="layui-form-label">关键词</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="keyword" id="keyword" placeholder="输入关键词" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-inline">
                                <button class="layui-btn layuiadmin-btn-useradmin" data-type="reload" id="LAY-user-back-search">
                                    <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">申请时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input" id="starttime" placeholder="开始时间">
                                </div>
                                <div class="layui-form-mid">-</div>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input" id="endtime" placeholder="结束时间">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <table class="layui-hide" id="aftersales-table" lay-filter="aftersales-table"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮模板 -->
<script type="text/html" id="table-aftersales-tools">
    <!-- <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="detail">
        <i class="layui-icon layui-icon-search"></i>查看详情
    </a> -->
    {{# if(d.status == 0) { }}
    <a class="layui-btn layui-btn-xs" lay-event="audit">
        <i class="layui-icon layui-icon-edit"></i>审核
    </a>
    {{# } }}

</script>

<!-- 状态模板 -->
<script type="text/html" id="status-tpl">
    {{# if(d.status == 0) { }}
    <span class="layui-badge layui-bg-orange">{{ d.status_text }}</span>
    {{# } else if(d.status == 1) { }}
    <span class="layui-badge layui-bg-green">{{ d.status_text }}</span>
    {{# } else if(d.status == 2) { }}
    <span class="layui-badge layui-bg-red">{{ d.status_text }}</span>
    {{# } else if(d.status == 8) { }}
    <span class="layui-badge layui-bg-blue">{{ d.status_text }}</span>
    {{# } else if(d.status == 9) { }}
    <span class="layui-badge layui-bg-cyan">{{ d.status_text }}</span>
    {{# } else if(d.status == 10) { }}
    <span class="layui-badge layui-bg-gray">{{ d.status_text }}</span>
    {{# } else { }}
    <span class="layui-badge">{{ d.status_text }}</span>
    {{# } }}
</script>

<!-- 类型模板 -->
<script type="text/html" id="type-tpl">
    {{# if(d.after_sales_type == 1) { }}
    <span class="layui-badge layui-bg-blue">{{ d.type_text }}</span>
    {{# } else { }}
    <span class="layui-badge layui-bg-green">{{ d.type_text }}</span>
    {{# } }}
</script>

<!-- 图片模板 -->
<script type="text/html" id="images-tpl">
    <div class="image-gallery" data-images="{{ d.images }}" data-count="{{ d.image_count }}">
        {{ d.image_html }}
    </div>
</script>

<script src="/static/admin/layui/layui.js"></script>
<script>
layui.config({
    base: '/static/admin/'
}).extend({
    index: 'lib/index'
}).use(['index', 'table', 'laydate', 'form'], function(){
    var $ = layui.$
        ,admin = layui.admin
        ,view = layui.view
        ,table = layui.table
        ,laydate = layui.laydate
        ,form = layui.form;

    // 初始化日期选择器
    laydate.render({
        elem: '#starttime'
        ,type: 'datetime'
    });
    laydate.render({
        elem: '#endtime'
        ,type: 'datetime'
    });

    // 数据表格
    var aftersalesTable = table.render({
        elem: '#aftersales-table'
        ,url: '{:url("ajax_get_after_sales_list")}'
        ,cols: [[
            {field: 'id', width: 80, title: 'ID', sort: true}
            ,{field: 'after_sales_no', width: 200, title: '售后单号'}
            ,{field: 'order_no', width: 200, title: '订单号'}
            ,{field: 'user_name', width: 120, title: '用户'}
            ,{field: 'user_phone', width: 120, title: '手机号'}
            ,{field: 'type_text', width: 120, title: '类型', templet: '#type-tpl'}
            ,{field: 'goodsinfo', title: '商品信息', minWidth: 200}
            ,{field: 'images', width: 200, title: '售后图片', templet: '#images-tpl'}
            ,{field: 'total_amount', width: 100, title: '申请金额'}
            ,{field: 'approved_amount', width: 100, title: '批准金额'}
            ,{field: 'status_text', width: 100, title: '状态', templet: '#status-tpl'}
            ,{field: 'apply_time', width: 200, title: '申请时间'}
            ,{title: '操作', width: 280, align: 'center', fixed: 'right', toolbar: '#table-aftersales-tools'}
        ]]
        ,page: true
        ,limit: 20
        ,limits: [20, 50, 100]
        ,text: {
            none: '暂无相关数据'
        }
    });

    // 监听工具条
    table.on('tool(aftersales-table)', function(obj){
        var data = obj.data;
        if(obj.event === 'detail'){
            // 查看详情
            layer.open({
                type: 2,
                title: '售后详情',
                area: ['90%', '90%'],
                content: '{:url("detail")}?id=' + data.id
            });
            admin.popup({
                title: '售后详情'
                ,area: ['90%', '90%']
                ,id: 'LAY-popup-aftersales-detail'
                ,url: '{:url("detail")}?id=' + data.id
            });
        } else if(obj.event === 'audit'){
            // 审核
            showAuditDialog(data);
        } else if(obj.event === 'refund'){
            // 退款
            showRefundDialog(data);
        } else if(obj.event === 'complete'){
            // 完成
            layer.confirm('确认完成该售后申请？', function(index){
                admin.req({
                    url: '{:url("complete")}'
                    ,data: {
                        after_sales_id: data.id
                    }
                    ,done: function(res){
                        layer.msg('操作成功', {icon: 1});
                        table.reload('aftersales-table');
                    }
                });
                layer.close(index);
            });
        }
    });

    // 搜索
    $('#LAY-user-back-search').on('click', function(){
        var status = $('#status').val();
        var field = $('#field').val();  
        var keyword = $('#keyword').val();
        var starttime = $('#starttime').val();
        var endtime = $('#endtime').val();
        
        table.reload('aftersales-table', {
            where: {
                status: status,
                field: field,
                keyword: keyword,
                starttime: starttime,
                endtime: endtime
            }
            ,page: {
                curr: 1
            }
        });
    });

    // 审核弹窗
    function showAuditDialog(data) {
        layer.open({
            type: 1,
            title: '审核售后申请',
            area: ['500px', '400px'],
            content: `
                <form class="layui-form" style="padding: 20px;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">审核结果</label>
                        <div class="layui-input-block">
                            <input type="radio" name="audit_result" value="1" title="审核通过" checked>
                            <input type="radio" name="audit_result" value="2" title="审核拒绝">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">批准金额</label>
                        <div class="layui-input-block">
                            <input type="number" name="approved_amount" value="${data.total_amount}" class="layui-input" step="0.01" min="0">
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">备注</label>
                        <div class="layui-input-block">
                            <textarea name="admin_remark" placeholder="请输入审核备注" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="audit-form">确认审核</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                        </div>
                    </div>
                </form>
            `,
            success: function(layero, index) {
                form.render();
                form.on('submit(audit-form)', function(formData){
                    admin.req({
                        url: '{:url("audit")}',
                        data: Object.assign({after_sales_id: data.id}, formData.field),
                        done: function(res){
                            layer.msg('审核成功', {icon: 1});
                            table.reload('aftersales-table');
                            layer.close(index);
                        }
                    });
                    return false;
                });
            }
        });
    }

    // 退款弹窗
    function showRefundDialog(data) {
        layer.open({
            type: 1,
            title: '处理退款',
            area: ['400px', '300px'],
            content: `
                <form class="layui-form" style="padding: 20px;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">退款金额</label>
                        <div class="layui-input-block">
                            <input type="number" name="refund_amount" value="${data.approved_amount}" class="layui-input" step="0.01" min="0" max="${data.approved_amount}">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="refund-form">确认退款</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                        </div>
                    </div>
                </form>
            `,
            success: function(layero, index) {
                form.render();
                form.on('submit(refund-form)', function(formData){
                    admin.req({
                        url: '{:url("process_refund")}',
                        data: Object.assign({after_sales_id: data.id}, formData.field),
                        done: function(res){
                            layer.msg('退款处理成功', {icon: 1});
                            table.reload('aftersales-table');
                            layer.close(index);
                        }
                    });
                    return false;
                });
            }
        });
    }

    // 图片预览功能
    window.previewImages = function(imagesJson, startIndex) {
        try {
            var images = typeof imagesJson === 'string' ? JSON.parse(imagesJson) : imagesJson;
            if (!images || !Array.isArray(images) || images.length === 0) {
                layer.msg('暂无图片', {icon: 2});
                return;
            }

            startIndex = startIndex || 0;

            // 构建图片预览HTML
            var imagesHtml = images.map(function(url, index) {
                return '<div style="text-align: center; margin-bottom: 10px;">' +
                       '<img src="' + url + '" style="max-width: 100%; max-height: 400px; cursor: pointer;" onclick="layer.photos({photos: {data: [{src: \'' + url + '\'}]}, anim: 0})">' +
                       '<div style="color: #666; font-size: 12px; margin-top: 5px;">第' + (index + 1) + '张 / 共' + images.length + '张</div>' +
                       '</div>';
            }).join('');

            layer.open({
                type: 1,
                title: '售后图片预览 (共' + images.length + '张)',
                area: ['80%', '80%'],
                maxmin: true,
                content: '<div style="padding: 20px; max-height: 500px; overflow-y: auto;">' + imagesHtml + '</div>',
                success: function(layero, index) {
                    // 如果指定了起始索引，滚动到对应位置
                    if (startIndex > 0) {
                        setTimeout(function() {
                            var targetImg = layero.find('img').eq(startIndex);
                            if (targetImg.length) {
                                layero.find('.layui-layer-content').scrollTop(targetImg.parent().position().top);
                            }
                        }, 100);
                    }
                    
                    // 支持键盘ESC关闭
                    $(document).on('keyup.preview', function(e) {
                        if (e.keyCode === 27) {
                            layer.close(index);
                        }
                    });
                },
                end: function() {
                    $(document).off('keyup.preview');
                }
            });
        } catch (e) {
            console.error('图片预览错误:', e);
            layer.msg('图片数据格式错误', {icon: 2});
        }
    };
});
</script>

</body>
</html> 