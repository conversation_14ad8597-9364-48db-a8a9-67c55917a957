<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>售后详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/admin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/static/admin/style/admin.css" media="all">
    <style>
        .detail-card {
            margin-bottom: 15px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
            color: #666;
        }
        .info-value {
            color: #333;
        }
        .status-badge {
            padding: 3px 8px;
            border-radius: 3px;
            color: white;
            font-size: 12px;
        }
        .status-0 { background-color: #ff9800; }
        .status-1 { background-color: #4caf50; }
        .status-2 { background-color: #f44336; }
        .status-8 { background-color: #2196f3; }
        .status-9 { background-color: #00bcd4; }
        .status-10 { background-color: #9e9e9e; }
        .type-1 { background-color: #2196f3; }
        .type-2 { background-color: #4caf50; }
    </style>
</head>
<body>

<div class="layui-row layui-col-space15">
    <!-- 基本信息 -->
    <div class="layui-col-md6">
        <div class="layui-card detail-card">
            <div class="layui-card-header">基本信息</div>
            <div class="layui-card-body">
                <div class="info-item">
                    <span class="info-label">售后单号:</span>
                    <span class="info-value">{$after_sales.after_sales_no}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">订单号:</span>
                    <span class="info-value">{$after_sales.order_no}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">售后类型:</span>
                    <span class="status-badge type-{$after_sales.after_sales_type}">
                        {if condition="$after_sales.after_sales_type == 1"}仅退款{else/}退货退款{/if}
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">申请状态:</span>
                    <span class="status-badge status-{$after_sales.status}">
                        {switch name="after_sales.status"}
                            {case value="0"}待审核{/case}
                            {case value="1"}审核通过{/case}
                            {case value="2"}审核拒绝{/case}
                            {case value="8"}退款中{/case}
                            {case value="9"}退款成功{/case}
                            {case value="10"}售后完成{/case}
                            {case value="-1"}已取消{/case}
                            {default/}未知状态
                        {/switch}
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">申请时间:</span>
                    <span class="info-value">{$after_sales.apply_time}</span>
                </div>
                {if condition="$after_sales.audit_time"}
                <div class="info-item">
                    <span class="info-label">审核时间:</span>
                    <span class="info-value">{$after_sales.audit_time}</span>
                </div>
                {/if}
                {if condition="$after_sales.audit_user"}
                <div class="info-item">
                    <span class="info-label">审核人:</span>
                    <span class="info-value">{$after_sales.audit_user}</span>
                </div>
                {/if}
            </div>
        </div>
    </div>

    <!-- 用户信息 -->
    <div class="layui-col-md6">
        <div class="layui-card detail-card">
            <div class="layui-card-header">用户信息</div>
            <div class="layui-card-body">
                <div class="info-item">
                    <span class="info-label">用户昵称:</span>
                    <span class="info-value">{$user_info.nickname|default='未知'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">手机号:</span>
                    <span class="info-value">{$user_info.phone|default='未知'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">用户ID:</span>
                    <span class="info-value">{$after_sales.user_id}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 金额信息 -->
    <div class="layui-col-md6">
        <div class="layui-card detail-card">
            <div class="layui-card-header">金额信息</div>
            <div class="layui-card-body">
                <div class="info-item">
                    <span class="info-label">申请金额:</span>
                    <span class="info-value">￥{$after_sales.total_amount}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">批准金额:</span>
                    <span class="info-value">￥{$after_sales.approved_amount}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">实际退款:</span>
                    <span class="info-value">￥{$after_sales.actual_refund_amount}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 原订单信息 -->
    <div class="layui-col-md6">
        <div class="layui-card detail-card">
            <div class="layui-card-header">原订单信息</div>
            <div class="layui-card-body">
                <div class="info-item">
                    <span class="info-label">订单总额:</span>
                    <span class="info-value">￥{$order_info.total_price}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">卡支付:</span>
                    <span class="info-value">￥{$order_info.card_price}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">微信支付:</span>
                    <span class="info-value">￥{$order_info.total_price - $order_info.card_price}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">订单状态:</span>
                    <span class="info-value">
                        {switch name="order_info.status"}
                            {case value="1"}待审核{/case}
                            {case value="2"}已接单{/case}
                            {case value="3"}配送中{/case}
                            {case value="4"}已完成{/case}
                            {case value="100"}已完成{/case}
                            {case value="-1"}已取消{/case}
                            {default/}未知状态
                        {/switch}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- 申请原因 -->
    <div class="layui-col-md12">
        <div class="layui-card detail-card">
            <div class="layui-card-header">申请原因</div>
            <div class="layui-card-body">
                <div class="info-item">
                    <span class="info-label">退款原因:</span>
                    <span class="info-value">{$after_sales.refund_reason|default='未填写'}</span>
                </div>
                {if condition="$after_sales.user_description"}
                <div class="info-item">
                    <span class="info-label">用户说明:</span>
                    <div class="info-value" style="margin-top: 5px; line-height: 1.5;">
                        {$after_sales.user_description}
                    </div>
                </div>
                {/if}
                {if condition="$after_sales.admin_remark"}
                <div class="info-item">
                    <span class="info-label">管理员备注:</span>
                    <div class="info-value" style="margin-top: 5px; line-height: 1.5;">
                        {$after_sales.admin_remark}
                    </div>
                </div>
                {/if}
            </div>
        </div>
    </div>

    <!-- 商品明细 -->
    <div class="layui-col-md12">
        <div class="layui-card detail-card">
            <div class="layui-card-header">商品明细</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <thead>
                        <tr>
                            <th>商品名称</th>
                            <th>规格</th>
                            <th>单价</th>
                            <th>申请数量</th>
                            <th>批准数量</th>
                            <th>申请金额</th>
                            <th>批准金额</th>
                        </tr>
                    </thead>
                    <tbody>
                        {volist name="items" id="item"}
                        <tr>
                            <td>{$item.product_title}</td>
                            <td>{$item.inventory_title}</td>
                            <td>￥{$item.unit_price}</td>
                            <td>{$item.apply_quantity}</td>
                            <td>{$item.approved_quantity}</td>
                            <td>￥{$item.apply_amount}</td>
                            <td>￥{$item.approved_amount}</td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 操作日志 -->
    <div class="layui-col-md12">
        <div class="layui-card detail-card">
            <div class="layui-card-header">操作日志</div>
            <div class="layui-card-body">
                <table class="layui-hide" id="logs-table" lay-filter="logs-table"></table>
            </div>
        </div>
    </div>
</div>

<script src="/static/admin/layui/layui.js"></script>
<script>
layui.config({
    base: '/static/admin/'
}).extend({
    index: 'lib/index'
}).use(['index', 'table'], function(){
    var $ = layui.$
        ,admin = layui.admin
        ,table = layui.table;

    // 操作日志表格
    table.render({
        elem: '#logs-table'
        ,url: '/admin/saftersales/ajax_after_sales_logs'
        ,where: {
            id: '{$id}'
        }
        ,cols: [[
            {field: 'created_at', width: 160, title: '操作时间'}
            ,{field: 'action_type', width: 120, title: '操作类型', templet: function(d){
                var actionMap = {
                    'apply': '申请售后',
                    'audit': '审核处理', 
                    'refund': '退款处理',
                    'complete': '完成售后',
                    'cancel': '取消售后'
                };
                return actionMap[d.action_type] || d.action_type;
            }}
            ,{field: 'operator_name', width: 120, title: '操作人'}
            ,{field: 'operation_content', title: '操作内容', minWidth: 200}
            ,{field: 'remark', title: '备注', minWidth: 150}
        ]]
        ,page: true
        ,limit: 10
        ,limits: [10, 20, 50]
        ,text: {
            none: '暂无操作日志'
        }
    });
});
</script>

</body>
</html> 