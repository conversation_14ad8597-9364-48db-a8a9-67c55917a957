<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>门店配送能力配置</title>
    {include file="public/iframeheader"/}
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="layui-row">
                            <div class="layui-col-md6">
                                <h3>门店配送能力配置</h3>
                            </div>
                            <div class="layui-col-md6" style="text-align: right;">
                                <button type="button" class="layui-btn layui-btn-normal" id="add-capacity">添加配送时间段</button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-form">
                            <blockquote class="layui-elem-quote">
                                当前门店：<span style="font-weight: bold; color: #009688;">{$shop.title}</span>
                                <br>
                                配送能力设置可以帮助您合理安排每个时间段的订单接收量，避免在高峰期间订单过多导致无法及时配送。
                            </blockquote>
                        </div>

                        <table class="layui-table" id="capacity-table" lay-filter="capacity-table"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 表格操作按钮模板 -->
    <script type="text/html" id="table-operation">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </script>

    <!-- 状态开关模板 -->
    <script type="text/html" id="status-switch">
        <input type="checkbox" name="status" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="status-switch" {{ d.status == 1 ? 'checked' : '' }}>
    </script>

    <!-- 添加/编辑表单模板 -->
    <script type="text/html" id="form-tpl">
        <form class="layui-form" lay-filter="capacity-form" style="padding: 20px;">
            <input type="hidden" name="id" value="{{d.id || ''}}">
            
            <div class="layui-form-item">
                <label class="layui-form-label">配送时间段</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                <div class="layui-input-block">
                    <input type="text" name="delivery_time_range" value="{{d.delivery_time_range || ''}}" lay-verify="required" placeholder="格式如：9:00-10:00" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">最大配送量</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                <div class="layui-input-block">
                    <input type="number" name="max_capacity" value="{{d.max_capacity || '0'}}" lay-verify="required|number" min="1" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" {{d.status === undefined || d.status == 1 ? 'checked' : ''}}>
                    <input type="radio" name="status" value="0" title="禁用" {{d.status !== undefined && d.status == 0 ? 'checked' : ''}}>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="capacity-submit">确认保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </script>

    <script>
        layui.use(['table', 'form', 'layer', 'laytpl'], function() {
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.jquery;
            var laytpl = layui.laytpl;
            
            // 初始化表格
            var capacityTable = table.render({
                elem: '#capacity-table',
                url: '{:url("getCapacityList")}',
                page: true,
                cols: [[
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'delivery_time_range', title: '配送时间段', width: 180},
                    {field: 'max_capacity', title: '最大配送量', width: 120},
                    {field: 'status', title: '状态', width: 100, templet: '#status-switch'},
                    {field: 'add_time', title: '创建时间', width: 180},
                    {field: 'update_time', title: '更新时间', width: 180},
                    {fixed: 'right', title: '操作', toolbar: '#table-operation', width: 150}
                ]],
                parseData: function(res) {
                    return {
                        "code": res.sta === 1 ? 0 : 1,
                        "msg": res.msg,
                        "count": res.data ? res.data.list.length : 0,
                        "data": res.data ? res.data.list : []
                    };
                },
                response: {
                    statusCode: 0
                }
            });
            
            // 监听表格工具条
            table.on('tool(capacity-table)', function(obj) {
                var data = obj.data;
                if (obj.event === 'del') {
                    layer.confirm('确定删除该配送时间段？', function(index) {
                        $.ajax({
                            url: '{:url("deleteCapacity")}',
                            type: 'POST',
                            data: {id: data.id},
                            success: function(res) {
                                if (res.sta === 1) {
                                    layer.msg(res.msg, {icon: 1});
                                    obj.del();
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                        layer.close(index);
                    });
                } else if (obj.event === 'edit') {
                    // 编辑
                    openEditForm(data);
                }
            });
            
            // 监听开关操作
            form.on('switch(status-switch)', function(obj) {
                var id = this.value;
                var status = obj.elem.checked ? 1 : 0;
                
                $.ajax({
                    url: '{:url("updateStatus")}',
                    type: 'POST',
                    data: {id: id, status: status},
                    success: function(res) {
                        if (res.sta !== 1) {
                            layer.msg(res.msg, {icon: 2});
                            // 还原开关状态
                            $(obj.elem).prop('checked', !obj.elem.checked);
                            form.render('checkbox');
                        } else {
                            layer.msg(res.msg, {icon: 1});
                        }
                    }
                });
            });
            
            // 添加配送时间段
            $('#add-capacity').click(function() {
                openEditForm({});
            });
            
            // 打开添加/编辑表单
            function openEditForm(data) {
                console.log("编辑数据：", data); // 调试用
                var title = data.id ? '编辑配送时间段' : '添加配送时间段';
                
                // 获取表单模板
                var getTpl = document.getElementById('form-tpl').innerHTML;
                
                // 渲染模板
                laytpl(getTpl).render(data, function(html) {
                    layer.open({
                        type: 1,
                        title: title,
                        content: html,
                        area: ['500px', '400px'],
                        success: function(layero, index) {
                            form.render(null, 'capacity-form');
                            
                            // 监听表单提交
                            form.on('submit(capacity-submit)', function(formData) {
                                var loading = layer.load(2);
                                
                                $.ajax({
                                    url: '{:url("saveCapacity")}',
                                    type: 'POST',
                                    data: formData.field,
                                    success: function(res) {
                                        layer.close(loading);
                                        
                                        if (res.sta === 1) {
                                            layer.msg(res.msg, {icon: 1});
                                            layer.close(index);
                                            
                                            // 重新加载表格
                                            capacityTable.reload();
                                        } else {
                                            layer.msg(res.msg, {icon: 2});
                                        }
                                    },
                                    error: function() {
                                        layer.close(loading);
                                        layer.msg('网络错误，请稍后重试', {icon: 2});
                                    }
                                });
                                
                                return false;
                            });
                        }
                    });
                });
            }
            
            // 工作时间提示
            $('.time-tips').click(function() {
                layer.tips('例如：9:00-10:00，表示上午9点到10点的配送时间段', this, {
                    tips: [1, '#3595CC'],
                    time: 4000
                });
            });
        });
    </script>
</body>
</html> 