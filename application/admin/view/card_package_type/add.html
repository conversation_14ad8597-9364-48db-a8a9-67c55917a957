{include file="public/iframeheader"/}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <form id="modelForm" class="layui-form model-form" lay-filter="modelForm">
                        <input type="hidden" name="id" value="{$info.id|default=''}">
                        <div class="layui-form-item">
                            <label class="layui-form-label">卡型名称<span style="color: red;font-size: 16px;">*</span></label>
                            <div class="layui-input-block">
                                <input name="name" placeholder="请输入卡型名称" class="layui-input" 
                                       value="{$info.name|default=''}" lay-verType="tips" lay-verify="required" required/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">价格<span style="color: red;font-size: 16px;">*</span></label>
                            <div class="layui-input-block">
                                <input name="price" placeholder="请输入价格" class="layui-input" type="number" step="0.01"
                                       value="{$info.price|default='0'}" lay-verType="tips" lay-verify="required|number" required/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">描述</label>
                            <div class="layui-input-block">
                                <textarea name="description" placeholder="请输入描述" class="layui-textarea">{$info.description|default=''}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">关联套餐<span style="color: red;font-size: 16px;">*</span></label>
                            <div class="layui-input-block">
                                <div class="layui-btn-group">
                                    <button type="button" class="layui-btn layui-btn-sm" id="btnAddPackage">
                                        <i class="layui-icon">&#xe654;</i>添加一行
                                    </button>
                                </div>
                                <table class="layui-table" id="packageTable">
                                    <thead>
                                        <tr>
                                            <th>套餐名称</th>
                                            <th>套餐价格</th>
                                            <th>排序</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {if isset($selectedPackages) && !empty($selectedPackages)}
                                        {volist name="packages" id="package"}
                                        {if in_array($package.id, $selectedPackages)}
                                        <tr>
                                            <td>
                                                <select name="package_ids[]" lay-verify="required" class="layui-select package-select" lay-search="">
                                                    <option value="">请选择套餐</option>
                                                    {volist name="packages" id="p"}
                                                    <option value="{$p.id}" data-price="{$p.price}" {if $p.id == $package.id}selected{/if}>{$p.name}</option>
                                                    {/volist}
                                                </select>
                                            </td>
                                            <td class="package-price">{$package.price}</td>
                                            <td>
                                                <input type="number" name="package_sorts[]" value="{$index-1}" class="layui-input" min="0" required>
                                            </td>
                                            <td>
                                                <button type="button" class="layui-btn layui-btn-danger layui-btn-xs btn-del-package">删除</button>
                                            </td>
                                        </tr>
                                        {/if}
                                        {/volist}
                                        {/if}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <input type="radio" name="status" value="1" title="启用" {if isset($info) && $info.status==1}checked{elseif !isset($info)}checked{/if}>
                                <input type="radio" name="status" value="0" title="禁用" {if isset($info) && $info.status==0}checked{/if}>
                            </div>
                        </div>
                        <div class="layui-form-item text-right">
                            <button class="layui-btn" id="modelSubmitBtn" lay-filter="modelSubmitBtn" lay-submit>保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="/static/admin/lay-module/xm-select.js"></script>
<script>
layui.use(['form', 'layer'], function() {
    var $ = layui.jquery;
    var form = layui.form;
    var layer = layui.layer;
    
    // 添加套餐按钮点击事件
    $('#btnAddPackage').click(function() {
        var index = $('#packageTable tbody tr').length;
        var html = '<tr>';
        html += '<td>';
        html += '<select name="package_ids[]" lay-verify="required" class="layui-select package-select" lay-search="">';
        html += '<option value="">请选择套餐</option>';
        {volist name="packages" id="package"}
        html += '<option value="{$package.id}" data-price="{$package.price}">{$package.name}</option>';
        {/volist}
        html += '</select>';
        html += '</td>';
        html += '<td class="package-price"></td>';
        html += '<td>';
        html += '<input type="number" name="package_sorts[]" value="' + index + '" class="layui-input" min="0" required>';
        html += '</td>';
        html += '<td>';
        html += '<button type="button" class="layui-btn layui-btn-danger layui-btn-xs btn-del-package">删除</button>';
        html += '</td>';
        html += '</tr>';
        
        $('#packageTable tbody').append(html);
        form.render('select'); // 重新渲染select
    });
    
    // 监听套餐选择变化
    form.on('select', function(data) {
        var $select = $(data.elem);
        if ($select.hasClass('package-select')) {
            var $tr = $select.closest('tr');
            var price = $select.find('option:selected').data('price');
            $tr.find('.package-price').text(price || '');
        }
    });
    
    // 删除套餐
    $(document).on('click', '.btn-del-package', function() {
        $(this).parents('tr').remove();
        
        // 重新排序索引
        $('#packageTable tbody tr').each(function(i) {
            $(this).find('input[name="package_sorts[]"]').val(i);
        });
    });
    
    // 表单提交事件
    form.on('submit(modelSubmitBtn)', function(data) {
        // 检查是否添加了套餐
        if ($('#packageTable tbody tr').length == 0) {
            layer.msg('请至少添加一个套餐', {icon: 2});
            return false;
        }
        
        layer.load(2);
        var url = data.field.id ? '{:url("edit")}' : '{:url("add")}';
        $.post(url, data.field, function(res) {
            layer.closeAll('loading');
            if (res.code == 0) {
                layer.msg(res.msg, {icon: 1});
                // 关闭当前iframe层
                var index = parent.layer.getFrameIndex(window.name);
                if (index) parent.layer.close(index);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        return false;
    });
});
</script> 