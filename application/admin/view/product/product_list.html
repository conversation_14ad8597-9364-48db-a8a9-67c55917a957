<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
    <style>
        /*.layui-table-view .layui-table td{*/
        /*    width: 85px;*/
        /*    height: 85px;*/
        /*}*/
        .laytable-cell-2-0-5 {
            height: 45px;
            line-height: 45px;
            text-align: center;
        }

        .layui-table-cell {
            height: 45px;
            line-height: 45px;
        }
    </style>
    <br />
    <div style="margin: 20px">
        <div class="demoTable layui-form">
            <div class="layui-inline">
                <label class="layui-form-label">查询条件：</label>
                <div class="layui-input-block">
                    <select name="field">
                        <option value="title">商品名称</option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <input class="layui-input" name="keyword" id="demoReload" autocomplete="off" />
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">商品类型：</label>
                <div class="layui-input-block">
                    <select name="product_type">
                        <option value="-1">全部</option>
                        <option value="1">普通商品</option>
                        <option value="2">计量商品</option>
                        <option value="3">赠品</option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">是否上架：</label>
                <div class="layui-input-block">
                    <select name="status">
                        <option value="-1">全部</option>
                        <option value="1">上架</option>
                        <option value="0">下架</option>
                    </select>
                </div>
            </div>

            <button class="layui-btn layui-btn-sm" id="search" data-type="reload" link="sub">
                搜索
            </button>
        </div>

        <div class="page-content">
            <table class="layui-hide" id="demo" lay-filter="demo"></table>
        </div>
        <script type="text/html" id="barDemo">
        <a class="layui-btn layui-btn-sm" lay-event="edit">修改</a>
        <a class="layui-btn layui-btn-sm layui-btn-danger" lay-event="del"
          >删除</a
        >
      </script>
    </div>
</body>

</html>

<script type="text/html" id="img">
  <img src="{{d.cover}}" style="width: 48px;" onclick="showimg(this)" />
</script>

<script type="text/html" id="barcode">
    <img src="{{d.barcode}}" style="width: 48px;" onclick="showimg(this)" />
</script>

<script type="text/html" id="productTypeTemplate">
    {{# if(d.product_type == 2) { }}
        <span class="layui-badge layui-bg-orange">计量商品</span>
    {{# } else if(d.product_type == 1) { }}
        <span class="layui-badge layui-bg-blue">普通商品</span>
    {{# } else if(d.product_type == 3) { }}
        <span class="layui-badge layui-bg-green">赠品</span>
    {{# } }}
</script>

<script type="text/javascript">
    layui
        .config({
            base: "__STATIC__/admin/modules/",
        })
        .extend({
            soulTable: "soulTable",
        })
        .use(["table", "soulTable"], function () {
            var table = layui.table;
            var soulTable = layui.soulTable;
            //方法级渲染

            var myTable = table.render({
                elem: "#demo",
                url: "{:url('ajax_product_list')}",
                cols: [
                    [
                        { type: "checkbox", fixed: "left" },
                        {
                            title: "#",
                            width: 50,
                            children: [
                                {
                                    title: '规格明细',
                                    url: "{:url('ajax_product_inventory_list')}",
                                    height: 300,
                                    page: false,
                                    where: function(d) {
                                        return {id: d.id};
                                    },
                                    cols: [[
                                        {field: 'id', title: 'ID', filter: true},
                                        {field: 'sn', title: 'SKU', filter: true},
                                        {field: 'barcode', title: '条形码', filter: true, templet: '#barcode'},
                                        {field: 'title', title: '规格名', filter: true},
                                        {field: 'price', title: '价格', filter: true, sort: true}
                                    ]],
                                    done: function () {
                                        soulTable.render(this);
                                    },
                                    filter: {
                                        bottom: false 
                                    },
                                }
                            ]
                        },
                        { type: "numbers", title: "序号" },
                        { field: "cover", title: "封面", toolbar: "#img" },
                        {
                            field: "category",
                            title: "所属分类",
                            templet: function (d) {
                                return d.category.title;
                            },
                            filter: true,
                        },
                        { field: "title", title: "商品名称", filter: true },
                        { field: "product_type", title: "商品类型", templet: "#productTypeTemplate", filter: true },
                        { field: "price", title: "价格", filter: true, sort: true },
                        { field: "status_text", title: "是否上架", filter: true },
                        { fixed: "right", title: "操作", toolbar: "#barDemo" },
                    ],
                ],
                page: true,
                id: "productTable",
                height: 'full-120',
                cellMinWidth: 80,
                even: true,
                size: 'lg',
                filter: {
                    bottom: false 
                },
                done: function () {
                    soulTable.render(this);
                },
            });

            layui.table.on("tool(demo)", function (obj) {
                //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                var id = data.id;
                if (layEvent === "edit") {
                    layer.open({
                        type: 2,
                        title: "修改商品",
                        content: "{:url('edit_product')}?id=" + id,
                        maxmin: true,
                        area: ["98%", "98%"],
                        end: function () { },
                    });
                } else if (layEvent === "del") {
                    layer.confirm(
                        "确认删除【" + data.title + "】吗？",
                        {
                            btn: ["确定", "关闭"], //按钮
                        },
                        function () {
                            $.post(
                                "{:url('del_product')}",
                                { id: id },
                                function (res) {
                                    if (res.code == 0) {
                                        layer.msg(res.msg, { icon: 1 });
                                        table.reload("productTable");
                                    } else {
                                        layer.msg(res.msg, { icon: 1 });
                                    }
                                },
                                "json"
                            );
                        },
                        function () { }
                    );
                }
            });

            $("#search").click(function (res) {
                var keyword = $("[name=keyword]").val();
                var field = $("[name=field]").val();
                var status = $("[name=status]").val();
                var product_type = $("[name=product_type]").val();
                //执行重载
                table.reload("productTable", {
                    page: {
                        curr: 1, //重新从第 1 页开始
                    },
                    where: {
                        field: field,
                        keyword: keyword,
                        status: status,
                        product_type: product_type,
                    },
                });
            });

            $("#export").on("click", function() {
                soulTable.export(myTable);
            });
        });

    //点击放大图片
    function showimg(t) {
        var src = $(t).attr("src");
        if (src == "") {
            layer.msg("图为为空！");
            return false;
        }
        layer.open({
            type: 1,
            title: false,
            area: "516px",
            content:
                '<img style="display: inline-block; width: 100%; height: 100%;" src="' +
                src +
                '">',
        });
    }
</script>