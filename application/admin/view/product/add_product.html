<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>审核商品</title>
    {include file="public/iframeheader"/}
    <!-- 编辑器源码文件 -->
</head>
<!--<script type="text/javascript" src="__STATIC__/admin/ueditor/ueditor.config.js"></script>-->
<!--<script type="text/javascript" src="__STATIC__/admin/ueditor/ueditor.all.js"></script>-->

<body>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body" pad15>


                    <div class="layui-form" wid100 lay-filter="">

                        <div class="layui-form-item">
                            <label class="layui-form-label">选择分类</label>
                            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                            <div class="layui-input-inline" style="width: 400px;">
                                <select name="category_id" lay-verify="required" lay-search>
                                    <option value="">请选择</option>
                                    {foreach name='category_list' key='key' item='value'}
                                    <option value="{$value['id']}"> {$value["title"]}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">商品名称</label>
                            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                            <div class="layui-input-inline">
                                <input type="text" name="title" style="width: 400px;" lay-verify="required"
                                       class="layui-input ">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">商品类型</label>
                            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                            <div class="layui-input-block">
                                <input type="radio" name="product_type" value="1" title="普通商品" checked lay-filter="product_type">
                                <input type="radio" name="product_type" value="2" title="计量商品" lay-filter="product_type">
                                <input type="radio" name="product_type" value="3" title="赠品" lay-filter="product_type">
                            </div>
                            <div class="layui-form-mid layui-word-aux">
                                <span style="color: #999; font-size: 12px;">
                                    普通商品：按件数销售的商品；计量商品：按重量销售的商品（如生鲜、水果等）
                                </span>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">商品图片</label>
                            <div class="layui-input-block layui-btn-container ">
                                {:UpImage("cover",200,200,$goods_info.cover)}
                            </div>
                        </div>

                        <!-- 规格类型 -->
                        <div id="fairy-is-attribute"></div>
                        <!--商品规格表-->

                        <div class="layui-form-item">
                            <label class="layui-form-label">规格信息</label>
                            <div class="layui-input-block layui-btn-container ">
                            <div id="fairy-spec-table"></div>
                            </div>
                        </div>
<!--                        <div id="fairy-spec-table"></div>-->
                        <!--商品库存表-->
                        <div id="fairy-sku-table"></div>

<!--                        <div class="layui-form-item">-->
<!--                            <label class="layui-form-label">商品详情</label>-->
<!--                            <div class="layui-input-block">-->
<!--                                {:BatchImage("detail",200,200,$goods_info['detail'])}-->
<!--                            </div>-->
<!--                            <div class="layui-form-mid layui-word-aux"></div>-->
<!--                        </div>-->

                        <div class="layui-form-item">
                            <label class="layui-form-label">商品描述</label>
                            <div class="layui-input-block">

                                {:BatchImage("content",200,200,$product['content'])}
<!--                                <script id="container" type="text/plain" >{$info.content|default=''}</script>-->

<!--                                <textarea name="content" placeholder="请输入描述" class="layui-textarea"></textarea>-->
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">是否上架</label>
                            <div class="layui-input-block">
                                <input type="checkbox" name="state" lay-skin="switch">
                            </div>
                        </div>

                        <input type="hidden" name="is_attribute" value="1">

<!--                        <div class="layui-form-item">-->
<!--                            <label class="layui-form-label">排序号</label>-->
<!--                            <div class="layui-input-inline">-->
<!--                                <input type="number" name="o" class="layui-input">-->
<!--                            </div>-->
<!--                            <div class="layui-input-inline layui-input-company"></div>-->
<!--                            <div class="layui-form-mid layui-word-aux">排序号 小的在前</div>-->
<!--                        </div>-->


                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="sub2">确认</button>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<!--<script>-->
<!--    //百度编辑器服务器统一请求接口路径 ，路由定义在\vendor\dh2y\think-ueditor\src\helper.php-->
<!--    window.UEDITOR_CONFIG.serverUrl="{:url('ueditor/index')}";-->
<!--    //渲染某个元素为百度富文本编辑器-->
<!--    cn_ueditor1 = UE.getEditor('container', {-->
<!--        toolbars: [-->
<!--            ['fullscreen', 'source', 'undo', 'redo', 'simpleupload', 'insertimage'],-->
<!--            ['justifyleft', //居左对齐-->
<!--                'justifyright', //居右对齐-->
<!--                'justifycenter', //居中对齐-->
<!--                'justifyjustify', //两端对齐-->
<!--                'forecolor', //字体颜色-->
<!--                'backcolor', //背景色-->
<!--                'fontfamily', //字体-->
<!--                'fontsize', //字号-->
<!--            ]-->
<!--        ],-->
<!--        autoHeightEnabled: true,-->
<!--        autoFloatEnabled: true-->
<!--    });-->
<!--</script>-->
<script>
    //一般直接写在一个js文件中
    layui.config({
        base: '__STATIC__/admin/lay-module/', // 设定扩展的 layui 模块的所在目录，一般用于外部模块扩展
    }).use(['form', 'skuTable', 'element', 'layedit'], function () {
        var form = layui.form;
        var skuTable = layui.skuTable;

        // 商品类型配置管理器
        var ProductTypeConfig = {
            // 普通商品配置
            normal: {
                // 统一规格配置
                singleSkuTableConfig: {
                    thead: [
                        {title: 'SKU编码', icon: 'layui-icon-cols'},
                        {title: '销售价(元)', icon: 'layui-icon-cols'},
                        {title: '状态', icon: ''},
                    ],
                    tbody: [
                        {type: 'input', field: 'sku', value: '', verify: 'required', reqtext: 'SKU编码不能为空'},
                        {type: 'input', field: 'price', value: '', verify: 'required|number', reqtext: '销售价不能为空'},
                        {type: 'select', field: 'status', option: [{key: '启用', value: '1'}, {key: '禁用', value: '0'}], verify: 'required', reqtext: '状态不能为空'},
                    ]
                },
                // 多规格配置
                multipleSkuTableConfig: {
                    thead: [
                        {title: '图片', icon: ''},
                        {title: 'SKU编码', icon: 'layui-icon-cols'},
                        {title: '销售价(元)', icon: 'layui-icon-cols'},
                        {title: '状态', icon: ''},
                    ],
                    tbody: [
                        {type: 'image', field: 'image', value: '', verify: '', reqtext: ''},
                        {type: 'input', field: 'sku', value: '', verify: 'required', reqtext: 'SKU编码不能为空'},
                        {type: 'input', field: 'price', value: '', verify: 'required|number', reqtext: '销售价不能为空'},
                        {type: 'select', field: 'status', option: [{key: '启用', value: '1'}, {key: '禁用', value: '0'}], verify: 'required', reqtext: '状态不能为空'},
                    ]
                },
                defaultIsAttribute: 1,  // 普通商品默认多规格
                allowMultiSpec: true,   // 允许多规格
                showSpecTable: true     // 显示规格表
            },
            
            // 计量商品配置
            weight: {
                // 统一规格配置
                singleSkuTableConfig: {
                    thead: [
                        {title: 'SKU编码', icon: 'layui-icon-cols'},
                        {title: '销售价(元/重量单位)', icon: 'layui-icon-cols'},
                        {title: '重量单位', icon: 'layui-icon-down'},
                        {title: '状态', icon: ''},
                    ],
                    tbody: [
                        {type: 'input', field: 'sku', value: '', verify: 'required', reqtext: 'SKU编码不能为空'},
                        {type: 'input', field: 'price', value: '', verify: 'required|number', reqtext: '销售价不能为空'},
                        {type: 'select', field: 'weight_unit', option: [
                            // {key: '斤', value: 'jin'}, 
                            {key: '公斤', value: 'kg'},
                            // {key: '克', value: 'g'},
                            // {key: '千克', value: 'kg'},
                            // {key: '两', value: 'liang'}
                        ], verify: 'required', reqtext: '重量单位不能为空'},
                        {type: 'select', field: 'status', option: [{key: '启用', value: '1'}, {key: '禁用', value: '0'}], verify: 'required', reqtext: '状态不能为空'},
                    ]
                },
                // 多规格配置 - 计量商品也可以有规格（如：苹果-红富士-斤，苹果-青苹果-斤）
                multipleSkuTableConfig: {
                    thead: [
                        {title: '图片', icon: ''},
                        {title: 'SKU编码', icon: 'layui-icon-cols'},
                        {title: '销售价(元/重量单位)', icon: 'layui-icon-cols'},
                        {title: '重量单位', icon: 'layui-icon-down'},
                        {title: '状态', icon: ''},
                    ],
                    tbody: [
                        {type: 'image', field: 'image', value: '', verify: '', reqtext: ''},
                        {type: 'input', field: 'sku', value: '', verify: 'required', reqtext: 'SKU编码不能为空'},
                        {type: 'input', field: 'price', value: '', verify: 'required|number', reqtext: '销售价不能为空'},
                        {type: 'select', field: 'weight_unit', option: [
                            // {key: '斤', value: 'jin'}, 
                            {key: '公斤', value: 'kg'},
                            // {key: '克', value: 'g'},
                            // {key: '千克', value: 'kg'},
                            // {key: '两', value: 'liang'}
                        ], verify: 'required', reqtext: '重量单位不能为空'},
                        {type: 'select', field: 'status', option: [{key: '启用', value: '1'}, {key: '禁用', value: '0'}], verify: 'required', reqtext: '状态不能为空'},
                    ]
                },
                defaultIsAttribute: 0,  // 计量商品默认统一规格
                allowMultiSpec: true,   // 计量商品也允许多规格
                showSpecTable: true     // 显示规格表
            }
        };

        // 初始化SKU表
        var obj = skuTable.render({
            //规格类型 0统一规格 1多规格
            isAttributeValue: ProductTypeConfig.normal.defaultIsAttribute,
            //规格类型容器id
            isAttributeElemId: 'fairy-is-attribute',
            //规格表容器id
            specTableElemId: 'fairy-spec-table',
            //sku表容器id
            skuTableElemId: 'fairy-sku-table',
            //规格拖拽排序
            sortable: true,
            //sku表相同属性值是否合并行
            rowspan: true,
            //请求成功返回状态码值
            requestSuccessCode: 200,
            //上传接口地址
            uploadUrl: "{:url('Upload/upload')}",
            //添加规格接口地址
            specCreateUrl: "{:url('Product/ajax_add_product_spec')}",
            //添加规格时的额外参数
            specCreateParams: {},
            //删除规格接口地址，如果为空则表示仅前端删除
            specDeleteUrl: '',
            //添加规格值接口地址
            specValueCreateUrl: "{:url('Product/ajax_add_product_spec_attr')}",
            //删除规格值接口地址，如果为空则表示仅前端删除
            specValueDeleteUrl: '',
            //统一规格配置项 - 初始使用普通商品配置
            singleSkuTableConfig: ProductTypeConfig.normal.singleSkuTableConfig,
            //多规格配置项 - 初始使用普通商品配置
            multipleSkuTableConfig: ProductTypeConfig.normal.multipleSkuTableConfig,
            //商品id
            productId: '',
            //规格数据
            specData: [],
        });

        // 切换商品类型配置
        function switchProductTypeConfig(productType) {
            var config = ProductTypeConfig[productType] || ProductTypeConfig.normal;
            
            // 更新SKU表配置
            obj.options.singleSkuTableConfig = config.singleSkuTableConfig;
            obj.options.multipleSkuTableConfig = config.multipleSkuTableConfig;
            
            // 设置默认规格类型
            var defaultIsAttribute = config.defaultIsAttribute;
            $('input[name="is_attribute"]').val(defaultIsAttribute);
            
            // 更新组件内部的is_attribute值
            obj.options.isAttributeValue = defaultIsAttribute;
            
            // 控制UI显示/隐藏
            if (config.showSpecTable) {
                $('#fairy-is-attribute').show();
                $('#fairy-spec-table').show();
            } else {
                $('#fairy-is-attribute').hide();
                $('#fairy-spec-table').hide();
            }
            
            // 重新渲染表格
            obj.render();
            
            return config;
        }

        // 监听商品类型变化
        form.on('radio(product_type)', function(data){
            var productType = data.value;
            var typeName = productType == '2' ? '计量商品' : productType == '3' ? '赠品' : '普通商品';
            var configKey = productType == '2' ? 'weight' : 'normal';
            
            // layer.msg('已切换为' + typeName + '模式', {icon: 1, time: 1500});
            
            // 切换配置
            var config = switchProductTypeConfig(configKey);
            
            // 计量商品强制使用统一规格
            if (configKey === 'weight') {
                // 强制设置为统一规格
                $('input[name="is_attribute"]').val('0');
                obj.options.isAttributeValue = '0';
                
                // 强制重新渲染为统一规格
                setTimeout(function() {
                    obj.render();
                    // 确保隐藏域也更新
                    $('input[name="is_attribute"]').val('0');
                    // 重新渲染表单元素
                    form.render('radio');
                }, 100);
            }
        });

        // 页面加载完成后初始化
        $(document).ready(function() {
            // 延迟执行以确保表单渲染完成
            setTimeout(function() {
                var currentProductType = $('input[name="product_type"]:checked').val();
                var configKey = currentProductType == '2' ? 'weight' : 'normal';
                switchProductTypeConfig(configKey);
            }, 100);
        });

        // 表单提交验证
        form.on('submit(sub2)', function (data) {
            var productType = $('input[name="product_type"]:checked').val();
            var isWeight = productType == '2';
            
            if (data.field.is_attribute == 1) {
                // 多规格模式
                console.log(obj.getSpecData());
                data.field.spec = obj.getSpecData();

                var hasSkuData = Object.keys(data.field).some(function (item, index, array) {
                    return item.startsWith('skus');
                });
                
                if(!hasSkuData) {
                    layer.msg('SKU表数据不能为空', {icon: 5, anim: 6});
                    return false;
                }
                
                // 计量商品多规格验证重量单位
                if (isWeight) {
                    var hasWeightUnit = Object.keys(data.field).some(function(key) {
                        return key.includes('[weight_unit]') && data.field[key];
                    });
                    if (!hasWeightUnit) {
                        layer.msg('计量商品请为所有SKU选择重量单位', {icon: 2});
                        return false;
                    }
                }
                
            } else {
                // 统一规格模式验证
                if(!data.field.sku) {
                    layer.msg('请填写SKU编码', {icon: 2});
                    return false;
                }
                if(!data.field.price) {
                    layer.msg('请填写销售价格', {icon: 2});
                    return false;
                }
                
                // 计量商品需要验证重量单位
                if(isWeight && !data.field.weight_unit) {
                    layer.msg('请选择重量单位', {icon: 2});
                    return false;
                }
            }

            load = layer.load(2, {shade: [0.1, '#fff']});
            $.post("{:url('ajax_add_product')}", data.field, function (data) {
                layer.close(load);
                if (data.code == 0) {
                    layer.msg(data.msg, {icon: 1, time: 2000}, function(){
                        window.location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 2});
                }
            }, "json");

            return false;
        });
    });
</script>

</body>

</html>
