<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>短信设置</title>
    {include file="public/iframeheader"/}
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">短信设置</div>
                    <div class="layui-card-body" pad15>
                        <div class="layui-form" wid100 lay-filter="">
                            <input type="hidden" name="keynum" value="{$keynum|default=''}" />

                            <div class="layui-form-item">
                                <label class="layui-form-label">接口地址</label>
                                <span style="
                                    color: red;
                                    font-size: 150%;
                                    float: left;
                                    margin-left: -10px;
                                    margin-top: 8px;
                                    ">*</span>
                                <div class="layui-input-inline" style="width: 40%">
                                    <input type="text" name="api_url" style="border: none" disabled
                                        value="{:get_plat_system_set('lizengbang_api_url')}" class="layui-input" />
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">appid</label>
                                <span style="
                                    color: red;
                                    font-size: 150%;
                                    float: left;
                                    margin-left: -10px;
                                    margin-top: 8px;
                                    ">*</span>
                                <div class="layui-input-inline" style="width: 380px">
                                    <input type="text" name="appid" value="{$info['appid']|default=''}"
                                        class="layui-input" />
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">appkey</label>
                                <span style="
                                    color: red;
                                    font-size: 150%;
                                    float: left;
                                    margin-left: -10px;
                                    margin-top: 8px;
                                    ">*</span>
                                <div class="layui-input-inline" style="width: 380px">
                                    <input type="text" name="appkey" value="{$info['appkey']|default=''}"
                                        class="layui-input" />
                                </div>
                                <div class="layui-input-inline layui-input-company"></div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>

                            <div class="layui-form-item invisible">
                                <label class="layui-form-label">短信签名</label>
                                <span style="
                                    color: red;
                                    font-size: 150%;
                                    float: left;
                                    margin-left: -10px;
                                    margin-top: 8px;
                                    ">*</span>
                                <div class="layui-input-inline" style="width: 200px">
                                    <input type="text" name="sign" value="{$info['sign']|default=''}"
                                        class="layui-input" />
                                </div>
                                <div class="layui-form-mid layui-word-aux">
                                    注意不要使用中性签名，比如"礼品兑换中心"，中性签名会降低短信发送成功率。
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">短信总开关</label>
                                <div class="layui-input-block">
                                    <input type="hidden" name="sms_status_flag" value="1" />
                                    <input type="checkbox" name="sms_status" {if condition="$info['sms_status'] eq '1'"
                                        } checked {/if} lay-skin="switch">
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">账户信息</label>
                                <div class="layui-input-inline" style="width: 80px"></div>
                                <!--
                <div class="layui-input-inline layui-input-company">公司名称：<i class="layui-icon" id="name">&#xe63d;</i>   </div>
                <div class="layui-input-inline layui-input-company">签名：<i class="layui-icon" id="sign">&#xe63d;</i>   </div>
                -->
                                <div class="layui-form-mid layui-word-aux">
                                    剩余条数：<i class="layui-icon" id="riches">&#xe63d;</i>
                                </div>
                                <!--
                <div class="layui-form-mid layui-word-aux">发送条数：<i class="layui-icon " id="totalsend">&#xe63d;</i>   </div>
                <div class="layui-form-mid layui-word-aux">总充值：<i class="layui-icon " id="totalin">&#xe63d;</i>   </div>
                -->
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">短信验证码开关</label>
                                <div class="layui-input-block">
                                    <input type="hidden" name="code_status_flag" value="1" />
                                    <input type="checkbox" name="code_status" {if
                                        condition="$info['code_status'] eq '1'" } checked {/if} lay-skin="switch">
                                </div>
                            </div>

                            <div class="layui-form-item layui-form-text invisible">
                                <label class="layui-form-label">验证码模板</label>
                                <div class="layui-input-block">
                                    <textarea name="code_content" class="layui-textarea">
{$info['code_content']|default=''}</textarea>
                                    <div class="layui-form-mid layui-word-aux">
                                        {code}为验证码,示例：你本次的验证码为:{code}，请妥善保管，不要告诉其他人。
                                    </div>
                                </div>
                            </div>

                            <!--
             <div class="layui-form-item">
                <label class="layui-form-label">下单通知开关</label>
                  <div class="layui-input-block">
                  <input type="checkbox" name="add_order_status"   {if condition="$info['add_order_status'] eq '1'"} checked {/if}  lay-skin="switch">
                  </div>
             </div>

              <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">下单通知模板</label>
                <div class="layui-input-block">
                  <textarea name="add_order_content" class="layui-textarea">{$info['add_order_content']|default=''}</textarea>
                  <div class="layui-form-mid layui-word-aux">{ordernumber}为订单号</div>
                </div>
              </div>  

             <div class="layui-form-item">
                <label class="layui-form-label">发货通知开关</label>
                  <div class="layui-input-block">
                  <input type="checkbox" name="delivery_status"    {if condition="$info['delivery_status'] eq '1'"} checked {/if}   lay-skin="switch">
                  </div>
             </div>

              <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">发货通知模板</label>
                <div class="layui-input-block">
                  <textarea name="delivery_content" class="layui-textarea">{$info['delivery_content']|default=''}</textarea>
                  <div class="layui-form-mid layui-word-aux">{ordernumber}为订单号,{deliveryname}为快递方式,{deliverynum}为快递单号</div>
                </div>
              </div>
			-->

                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="set_sms">
                                        确认保存
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        //一般直接写在一个js文件中
        layui.use(["element", "form"], function () {
            var form = layui.form;

            //监听提交1
            form.on("submit(set_sms)", function (data) {
                load = layer.load(2, { shade: [0.1, "#fff"] });
                $.post(
                    "{:url('ajax_sms_lizengbang_set')}",
                    data.field,
                    function (data) {
                        layer.close(load);
                        if (data.sta == 1) {
                            layer.msg(data.msg);
                        } else {
                            layer.msg(data.msg);
                        }
                    },
                    "json"
                );

                return false;
            });
        });
    </script>
    <script>
        //异步获取短信信息
        var keynum = $("[name=keynum]").val();
        $.post(
            "{:url('sms_set_lizengbang')}?ajax=ajax",
            { keynum: keynum },
            function (data) {
                if (data.sta == 1) {
                    $("#sign").text(data.sign);
                    $("#totalsend").text(data.totalsend);
                    $("#totalin").text(data.totalin);
                    $("#riches").text(data.riches);
                    $("#name").text(data.name);
                } else {
                    $("#sign").text("异常");
                    $("#totalsend").text("异常");
                    $("#totalin").text("异常");
                    $("#riches").text("异常");
                    $("#name").text("异常");
                }
            },
            "json"
        );
    </script>
</body>

</html>