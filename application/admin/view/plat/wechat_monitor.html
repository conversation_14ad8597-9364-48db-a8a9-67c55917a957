<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>微信监控</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="__STATIC__/admin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="__STATIC__/admin/style/admin.css" media="all">
    <style>
            /* 表格样式优化 */
    .layui-table-cell {
        height: auto;
        white-space: normal;
        line-height: 28px;
        padding: 6px 15px;
    }

    /* 小程序码列单元格样式 */
    .layui-table-cell[data-field="qrcode_url"] {
        height: 100px !important;
        line-height: 100px;
        padding-top: 10px;
        padding-bottom: 10px;
    }

        /* 图片样式 */
.qrcode-img {
    max-width: 80px;
    height: 80px;
    display: block;
    margin: 0 auto;
    object-fit: cover;
}

        /* 状态样式 */
        .status-normal {
            color: #0fb10f;
            font-weight: bold;
        }
        .status-warning {
            color: #FFB800;
            font-weight: bold;
        }
        .status-error {
            color: #FF5722;
            font-weight: bold;
        }
        .expire-warn {
            color: #FF5722;
        }

        /* 认证状态颜色样式 */
        .auth-status-safe {
            color: #0fb10f;
            font-weight: bold;
        }
        .auth-status-warning {
            color: #FFB800;
            font-weight: bold;
        }
        .auth-status-expired {
            color: #FF5722;
            font-weight: bold;
        }

        /* 小程序码可点击样式 */
        .qrcode-img {
            cursor: pointer;
            transition: transform 0.2s;
        }
        .qrcode-img:hover {
            transform: scale(1.1);
        }

        /* 操作按钮区域样式 */
        .layui-btn + .layui-btn {
            margin-left: 5px;
        }

        /* 表格工具栏样式 */
        .table-header-tools {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-tab" lay-filter="monitor-tab">
                <ul class="layui-tab-title">
                    <li class="layui-this" lay-id="0">客户</li>
                    <li lay-id="1">门店</li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <div class="table-header-tools">
                            <button class="layui-btn layui-btn-sm" id="plat-refresh-all">刷新全部</button>
                            <button class="layui-btn layui-btn-sm layui-btn-normal" id="plat-refresh-selected">刷新选中</button>
                        </div>
                        <table class="layui-hide" id="plat-table" lay-filter="plat-table"></table>
                    </div>
                    <div class="layui-tab-item">
                        <div class="table-header-tools">
                            <button class="layui-btn layui-btn-sm" id="shop-refresh-all">刷新全部</button>
                            <button class="layui-btn layui-btn-sm layui-btn-normal" id="shop-refresh-selected">刷新选中</button>
                        </div>
                        <table class="layui-hide" id="shop-table" lay-filter="shop-table"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 单行操作模板 -->
<script type="text/html" id="rowActionTpl">
  <a class="layui-btn layui-btn-xs" lay-event="refresh">刷新</a>
</script>

<!-- 状态模板 -->
<script type="text/html" id="statusTpl">
  {{#  if(d.status === '正常'){ }}
    <span class="status-normal">{{ d.status }}</span>
  {{#  } else if(d.status === '异常' || d.status === '配置错误' || d.status === '连接失败'){ }}
    <span class="status-error">{{ d.status }}</span>
  {{#  } else { }}
    <span class="status-warning">{{ d.status }}</span>
  {{#  } }}
</script>

<!-- 到期日期模板 -->
<script type="text/html" id="expireTpl">
  {{#  if(d.is_expire_warn){ }}
    <span class="expire-warn">{{ d.expire_date }}</span>
  {{#  } else { }}
    <span>{{ d.expire_date }}</span>
  {{#  } }}
</script>

<!-- 小程序码模板 -->
<script type="text/html" id="qrcodeTpl">
  {{#  if(d.qrcode_url){ }}
    <img src="{{ d.qrcode_url }}" class="qrcode-img" alt="小程序码" lay-event="previewQrcode">
  {{#  } else { }}
    <span>未生成</span>
  {{#  } }}
</script>

<!-- 认证状态模板 -->
<script type="text/html" id="authStatusTpl">
  {{#  var daysLeft = d.days_left; }}
  {{#  if(daysLeft === undefined || daysLeft === null || daysLeft < 0){ }}
    <span class="auth-status-expired">{{ d.auth_status }}</span>
  {{#  } else if(daysLeft <= 30){ }}
    <span class="auth-status-warning">{{ d.auth_status }}</span>
  {{#  } else { }}
    <span class="auth-status-safe">{{ d.auth_status }}</span>
  {{#  } }}
</script>

<!-- AppID/主体名称合并模板 -->
<script type="text/html" id="appidPrincipalTpl">
  <div style="line-height: 1.3;">
    <div style="font-weight: bold; color: #333; margin-bottom: 3px;">{{ d.appid || '未设置' }}</div>
    <div style="color: #666; font-size: 12px;">{{ d.principal || '未获取' }}</div>
  </div>
</script>

<script src="__STATIC__/admin/layui/layui.js"></script>
<script>
layui.use(['table', 'element', 'jquery', 'layer'], function(){
    var table = layui.table;
    var element = layui.element;
    var $ = layui.jquery;
    var layer = layui.layer;

    // 表格列配置
    var tableColumns = [[
        {type: 'checkbox', width: 50},
        {field: 'client_name', title: '客户名称', },
        {field: 'appid_principal', title: 'AppID/主体名称', templet: '#appidPrincipalTpl'},
        {field: 'status', title: '状态', templet: '#statusTpl'},
        {field: 'auth_status', title: '认证状态', templet: '#authStatusTpl'},
        {field: 'expire_date', title: '认证到期', templet: '#expireTpl'},
        {field: 'qrcode_url', title: '小程序码', templet: '#qrcodeTpl'},
        {field: 'check_time', title: '最后检查', },
        {fixed: 'right', title: '操作', align: 'center', templet: '#rowActionTpl'}
    ]];

    // 平台小程序表格
    var platTable = table.render({
        elem: '#plat-table',
        url: '{:url("ajax_get_wechat_monitor_list")}?type=plat',
        cols: tableColumns,
        page: false,
        id: 'platTableId',
        even: true,
        skin: 'line'
    });

    // 商户小程序表格
    var shopTable = table.render({
        elem: '#shop-table',
        url: '{:url("ajax_get_wechat_monitor_list")}?type=shop',
        cols: tableColumns,
        page: false,
        id: 'shopTableId',
        even: true,
        skin: 'line'
    });

    // 刷新单个监控
    function refreshItem(data, tableType) {
        var loadingIndex = layer.msg('正在刷新...', {icon: 16, time: 0});

        $.ajax({
            url: '{:url("ajax_refresh_wechat_monitor")}',
            type: 'POST',
            data: {
                id: data.id,
                type: data.type,
                client_keynum: data.client_keynum
            },
            success: function(res) {
                layer.close(loadingIndex);

                if (res.code === 0) {
                    layer.msg('刷新成功', {icon: 1});

                    // 刷新对应表格
                    if (tableType === 'plat') {
                        platTable.reload();
                    } else {
                        shopTable.reload();
                    }
                } else {
                    layer.msg(res.msg || '刷新失败', {icon: 2});
                }
            },
            error: function() {
                layer.close(loadingIndex);
                layer.msg('网络错误', {icon: 2});
            }
        });
    }

    // 批量刷新监控
    function batchRefreshItems(dataList, tableType) {
        if (dataList.length === 0) return;

        var total = dataList.length;
        var success = 0;
        var failed = 0;
        var index = 0;

        var loadingIndex = layer.msg('正在刷新选中项，请稍候...', {icon: 16, time: 0});

        function processNext() {
            if (index >= total) {
                layer.close(loadingIndex);

                layer.msg('刷新完成：成功' + success + '条，失败' + failed + '条', {
                    icon: failed > 0 ? 0 : 1
                });

                // 刷新对应表格
                if (tableType === 'plat') {
                    platTable.reload();
                } else {
                    shopTable.reload();
                }

                return;
            }

            var item = dataList[index++];
            $.ajax({
                url: '{:url("ajax_refresh_wechat_monitor")}',
                type: 'POST',
                data: {
                    id: item.id,
                    type: item.type,
                    client_keynum: item.client_keynum
                },
                success: function(res) {
                    if (res.code === 0) {
                        success++;
                    } else {
                        failed++;
                    }
                    processNext();
                },
                error: function() {
                    failed++;
                    processNext();
                }
            });
        }

        processNext();
    }

    // 预览小程序码
    function previewQrcode(qrcodeUrl) {
        if (!qrcodeUrl) {
            layer.msg('小程序码不存在', {icon: 2});
            return;
        }

        // 使用layer.photos预览图片
        var photos = {
            "title": "小程序码预览",
            "id": 123,
            "data": [{
                "src": qrcodeUrl,
                "alt": "小程序码",
                "thumb": qrcodeUrl
            }]
        };

        layer.photos({
            photos: photos,
            anim: 5,
            shade: 0.8
        });
    }

    // 监听平台表格行工具事件
    table.on('tool(plat-table)', function(obj) {
        var data = obj.data;
        if (obj.event === 'refresh') {
            refreshItem(data, 'plat');
        } else if (obj.event === 'previewQrcode') {
            previewQrcode(data.qrcode_url);
        }
    });

    // 监听商户表格行工具事件
    table.on('tool(shop-table)', function(obj) {
        var data = obj.data;
        if (obj.event === 'refresh') {
            refreshItem(data, 'shop');
        } else if (obj.event === 'previewQrcode') {
            previewQrcode(data.qrcode_url);
        }
    });

    // 平台表格刷新全部按钮
    $('#plat-refresh-all').on('click', function() {
        platTable.reload();
        layer.msg('列表已刷新', {icon: 1});
    });

    // 平台表格刷新选中按钮
    $('#plat-refresh-selected').on('click', function() {
        var checkStatus = table.checkStatus('platTableId');
        var data = checkStatus.data;

        if (data.length === 0) {
            layer.msg('请选择需要刷新的项', {icon: 2});
            return;
        }

        batchRefreshItems(data, 'plat');
    });

    // 商户表格刷新全部按钮
    $('#shop-refresh-all').on('click', function() {
        shopTable.reload();
        layer.msg('列表已刷新', {icon: 1});
    });

    // 商户表格刷新选中按钮
    $('#shop-refresh-selected').on('click', function() {
        var checkStatus = table.checkStatus('shopTableId');
        var data = checkStatus.data;

        if (data.length === 0) {
            layer.msg('请选择需要刷新的项', {icon: 2});
            return;
        }

        batchRefreshItems(data, 'shop');
    });

    // 监听Tab切换
    element.on('tab(monitor-tab)', function(data) {
        // 使用事件对象中的index属性而不是lay-id属性
        var index = data.index;

        // 延迟执行以确保DOM已经渲染完毕
        setTimeout(function() {
            if (index === 0) {
                platTable.resize();
            } else {
                // 对于第二个表格，先销毁再重新渲染
                table.reload('shopTableId', {
                    elem: '#shop-table',
                    url: '{:url("ajax_get_wechat_monitor_list")}?type=shop',
                    cols: tableColumns,
                    page: false,
                    id: 'shopTableId',
                    even: true,
                    skin: 'line'
                });
            }
        }, 100);
    });
});
</script>
</body>
</html>
