<!DOCTYPE html >
<html>
{include file="public/iframeheader"/}
<body>
<br/>
<div style="margin-left: 20px;">
    <div class="page-content">
        <div class="demoTable">
             系统名称：
             <div class='layui-inline'>
                 <input class="layui-input" name="keyword" id="demoReload" autocomplete="off">
             </div>
             <button class="layui-btn  layui-btn-sm" data-type="reload">搜索</button>
        </div>
        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-sm" lay-event="pur">权限</a>
        </script>
    </div>
</div>
</body>
</html>
<script type="text/javascript">
layui.use('table', function(){
  var table = layui.table;
  //方法级渲染
  table.render({
    elem: '#demo'
    ,url: "{:url('customer_systemlist')}?ajax=ajax"
    ,cols: [[
      {type: 'numbers', title: '序号'}
      ,{field:'site_id', title: '系统id'}
      ,{field:'sitename',title:'系统名称'}
      ,{field:'o',title:'排序号',sort:true}
      ,{field: 'tablename', title: 'tablename'}
      ,{field: 'remark', title: '系统备注'}
      ,{fixed: 'right',title:"操作", align:'center', toolbar: '#barDemo'}
    ]]
    ,id: 'testReload'
    ,page: true
  });
  table.on('tool(demo)', function(obj){ //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                var data = obj.data //获得当前行数据
                ,layEvent = obj.event; //获得 lay-event 对应的值
                var keynum=data.keynum;
                if(layEvent === 'del'){
                    layer.confirm('真的删除行么', function(index){
                        $.post("{:url('site_del')}",{keynums:keynum},function(data){
                            layer.msg(data.msg);
                            if(data.sta==1){
                                obj.del();
                            }
                        },"json");
                    });
                } else if(layEvent === 'edit'){
                    window.location.href="{:url('site_form')}?keynum="+keynum;
                } else if(layEvent === 'pur'){
                    window.location.href="{:url('site_pur_form')}?keynum="+keynum;
                }
    });
  var $ = layui.$, active = {
    reload: function(){
      var keyword=$("[name=keyword]").val();
      //执行重载
      table.reload('testReload', {
        page: {
          curr: 1 //重新从第 1 页开始
        }
        ,where: {
            keyword:keyword,
        }
      });
    },
    add:function(){
        window.location.href="{:url('role_form')}";
    }
  };
  
  $('.demoTable .layui-btn').on('click', function(){
    var type = $(this).data('type');
    active[type] ? active[type].call(this) : '';
  });
});
</script>

