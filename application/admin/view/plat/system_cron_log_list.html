<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>短信日志列表</title>
  {include file="public/iframeheader"/}
</head>

<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-form layui-card-header layuiadmin-card-header-auto">
        <div class="layui-form-item">
          <div class="layui-inline">
            <label class="layui-form-label">ID</label>
            <div class="layui-input-block">
              <input type="text" name="id" placeholder="请输入" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-inline">
            <label class="layui-form-label">操作方法</label>
            <div class="layui-input-block">
              <input type="text" name="action" placeholder="请输入" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-inline">
            <label class="layui-form-label">内容</label>
            <div class="layui-input-block">
              <input type="text" name="words" placeholder="请输入" autocomplete="off" class="layui-input">
            </div>
          </div>

          <div class="layui-inline">
            <button class="layui-btn layuiadmin-btn-forum-list" lay-submit lay-filter="LAY-app-forumlist-search">
              <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="layui-card-body">

        <table id="LAY-app-forum-list" lay-filter="LAY-app-forum-list"></table>


        <script type="text/html" id="imgTpl">
          <img style="display: inline-block; width: 50%; height: 100%;" src= {{ d.avatar }}>
        </script>
        <script type="text/html" id="buttonTpl">
          {{#  if(d.top == true){ }}
            <button class="layui-btn layui-btn-xs">已置顶</button>
          {{#  } else { }}
            <button class="layui-btn layui-btn-primary layui-btn-xs">正常显示</button>
          {{#  } }}
        </script>
        <script type="text/html" id="table-forum-list">
          <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
          <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
        </script>
      </div>
    </div>
  </div>

  <script>
    layui.config({
      base: '__STATIC__/admin/' //静态资源所在路径
    }).extend({
      index: 'lib/index' //主入口模块
    }).use(['index', 'forum', 'table'], function () {
      var $ = layui.$
        , form = layui.form
        , table = layui.table;

      //帖子管理
      table.render({
        elem: '#LAY-app-forum-list'
        , url: "{:url('system_cron_log_list')}?ajax=ajax&basekeynum={$basekeynum}"
        , cols: [[
          //{type: 'checkbox', fixed: 'left'}
          { field: 'id', width: 100, title: 'ID', sort: true }
          , { field: 'action', title: '操作方法', width: 120 }
          , { field: 'count', title: '影响行数' }
          , { field: 'words', title: '内容' }
          , { field: 'return_content', title: '接口返回内容' }
          , { field: 'time', title: '生成时间', sort: true, templet: function (row) { return createTime(row.time); } }
          //,{field: 'top', title: '置顶', templet: '#buttonTpl', minWidth: 80, align: 'center'}
          //,{title: '操作', width: 150, align: 'center', fixed: 'right', toolbar: '#table-forum-list'}
        ]]
        , page: true
        , limit: 10
        , limits: [10, 15, 20, 25, 30]
      });


      //监听搜索
      form.on('submit(LAY-app-forumlist-search)', function (data) {
        var field = data.field;
        //执行重载
        table.reload('LAY-app-forum-list', {
          where: field
        });
      });

      //事件
      var active = {
        batchdel: function () {
          var checkStatus = table.checkStatus('LAY-app-forum-list')
            , checkData = checkStatus.data; //得到选中的数据

          if (checkData.length === 0) {
            return layer.msg('请选择数据');
          }

          layer.confirm('确定删除吗？', function (index) {
            //删除操作

            table.reload('LAY-app-forum-list');
            layer.msg('已删除');
          });
        }
      }

      $('.layui-btn.layuiadmin-btn-forum-list').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
      });
    });
  </script>


  <script>
    //时间转换
    function createTime(v) {
      var v = v * 1000; //js的时间戳要*1000
      var date = new Date(v);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? '0' + m : m;
      var d = date.getDate();
      d = d < 10 ? ("0" + d) : d;
      var h = date.getHours();
      h = h < 10 ? ("0" + h) : h;
      var M = date.getMinutes();
      M = M < 10 ? ("0" + M) : M;
      var S = date.getSeconds();
      S = S < 10 ? ("0" + S) : S;
      var str = y + "-" + m + "-" + d + " " + h + ":" + M + ":" + S;
      return str;
    }

    function is_show_name(v) {
      var str = "";
      if (v == '1') {
        str = "显示";
      } else if (v == '0') {
        str = "不显示";
      } else {
        str = "暂无";
      }
      return str;
    }


  </script>
</body>

</html>
