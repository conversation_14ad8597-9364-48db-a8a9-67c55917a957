{include file="public/iframeheader"/}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <form id="editForm" class="layui-form model-form" lay-filter="editForm">
                        <input type="hidden" name="id" value="{$info.id}">
                        <div class="layui-form-item">
                            <label class="layui-form-label">套餐名称<span style="color: red;font-size: 16px;">*</span></label>
                            <div class="layui-input-block">
                                <input name="name" placeholder="请输入套餐名称" class="layui-input" 
                                       value="{$info.name}" lay-verType="tips" lay-verify="required" required/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">价格<span style="color: red;font-size: 16px;">*</span></label>
                            <div class="layui-input-block">
                                <input name="price" placeholder="请输入价格" class="layui-input" type="number" step="0.01"
                                       value="{$info.price}" lay-verType="tips" lay-verify="required|number" required/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">描述</label>
                            <div class="layui-input-block">
                                <textarea name="description" placeholder="请输入描述" class="layui-textarea">{$info.description}</textarea>
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">关联商品<span style="color: red;font-size: 16px;">*</span></label>
                            <div class="layui-input-block">
                                <div class="layui-btn-group">
                                    <button type="button" class="layui-btn layui-btn-sm" id="btnAddProduct">
                                        <i class="layui-icon">&#xe654;</i>添加一行
                                    </button>
                                </div>
                                <table class="layui-table" id="productTable">
                                    <thead>
                                        <tr>
                                            <th>商品名称</th>
                                            <th>规格</th>
                                            <th>数量</th>
                                            <th>排序</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {volist name="packageProducts" id="product" key="index"}
                                        <tr>
                                            <td>
                                                <select name="products[{$index-1}][product_inventory_id]" lay-verify="required" class="layui-select product-select" lay-filter="productSelect_{$index-1}">
                                                    <option value="">请选择商品</option>
                                                    {volist name="products" id="p"}
                                                    <option value="{$p.id}" data-spec="{$p.spec_values}" {if $p.id == $product.product_inventory_id}selected{/if}>{$p.name} - {$p.spec_values}</option>
                                                    {/volist}
                                                </select>
                                            </td>
                                            <td class="spec-values">{$product.spec_values}</td>
                                            <td>
                                                <input type="number" name="products[{$index-1}][quantity]" value="{$product.quantity}" class="layui-input" min="1" required>
                                            </td>
                                            <td>
                                                <input type="number" name="products[{$index-1}][sort]" value="{$index-1}" class="layui-input" min="0" required>
                                            </td>
                                            <td>
                                                <button type="button" class="layui-btn layui-btn-danger layui-btn-xs btn-del-product">删除</button>
                                            </td>
                                        </tr>
                                        {/volist}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <input type="radio" name="status" value="1" title="启用" {if $info.status==1}checked{/if}>
                                <input type="radio" name="status" value="0" title="禁用" {if $info.status==0}checked{/if}>
                            </div>
                        </div>
                        <div class="layui-form-item text-right">
                            <button class="layui-btn" id="modelSubmitBtn" lay-filter="modelSubmitBtn" lay-submit>保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 选择商品弹窗 -->
<script type="text/html" id="selectProductTpl">
    <div class="layui-form" style="padding: 20px;">
        <div class="layui-form-item">
            <input type="text" id="searchProduct" placeholder="输入商品名称搜索" class="layui-input">
            <button type="button" class="layui-btn layui-btn-sm" id="btnSearchProduct" style="margin-top: 10px;">
                <i class="layui-icon">&#xe615;</i>搜索
            </button>
        </div>
        <div style="height: 300px; overflow-y: auto;">
            <table class="layui-table" id="productSelectTable">
                <thead>
                    <tr>
                        <th>商品名称</th>
                        <th>规格</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {volist name="products" id="product"}
                    <tr>
                        <td>{$product.name}</td>
                        <td>{$product.spec_values}</td>
                        <td>
                            <button type="button" class="layui-btn layui-btn-xs btn-select-product" 
                                    data-id="{$product.id}" data-name="{$product.name}" data-spec="{$product.spec_values}">
                                选择
                            </button>
                        </td>
                    </tr>
                    {/volist}
                </tbody>
            </table>
        </div>
    </div>
</script>

<script>
layui.use(['form', 'layer'], function() {
    var $ = layui.jquery;
    var form = layui.form;
    var layer = layui.layer;
    
    // 打印调试信息
    console.log('编辑套餐ID: {$info.id}');
    console.log('套餐名称: {$info.name}');
    console.log('价格: {$info.price}');
    console.log('商品数量: {$packageProducts|count}');
    
    // 添加商品按钮点击事件
    $('#btnAddProduct').click(function() {
        var index = $('#productTable tbody tr').length;
        var html = '<tr>';
        html += '<td>';
        html += '<select name="products[' + index + '][product_inventory_id]" lay-verify="required" class="layui-select product-select" lay-filter="productSelect_' + index + '">';
        html += '<option value="">请选择商品</option>';
        {volist name="products" id="product"}
        html += '<option value="{$product.id}" data-spec="{$product.spec_values}">{$product.name} - {$product.spec_values}</option>';
        {/volist}
        html += '</select>';
        html += '</td>';
        html += '<td class="spec-values"></td>';
        html += '<td>';
        html += '<input type="number" name="products[' + index + '][quantity]" value="1" class="layui-input" min="1" required>';
        html += '</td>';
        html += '<td>';
        html += '<input type="number" name="products[' + index + '][sort]" value="' + index + '" class="layui-input" min="0" required>';
        html += '</td>';
        html += '<td>';
        html += '<button type="button" class="layui-btn layui-btn-danger layui-btn-xs btn-del-product">删除</button>';
        html += '</td>';
        html += '</tr>';
        
        $('#productTable tbody').append(html);
        form.render('select'); // 重新渲染select
    });
    
    // 监听商品选择变化
    form.on('select', function(data) {
        var $select = $(data.elem);
        var $tr = $select.closest('tr');
        var spec = $select.find('option:selected').data('spec');
        $tr.find('.spec-values').text(spec || '');
    });
    
    // 删除商品
    $(document).on('click', '.btn-del-product', function() {
        $(this).parents('tr').remove();
        
        // 重新排序索引
        $('#productTable tbody tr').each(function(i) {
            $(this).find('select[name*="[product_inventory_id]"]').attr('name', 'products[' + i + '][product_inventory_id]');
            $(this).find('input[name*="[quantity]"]').attr('name', 'products[' + i + '][quantity]');
            $(this).find('input[name*="[sort]"]').attr('name', 'products[' + i + '][sort]');
        });
    });
    
    // 表单提交事件
    form.on('submit(modelSubmitBtn)', function(data) {
        // 检查是否添加了商品
        if ($('#productTable tbody tr').length == 0) {
            layer.msg('请至少添加一个商品', {icon: 2});
            return false;
        }
        
        layer.load(2);
        $.post('{:url("edit")}', data.field, function(res) {
            layer.closeAll('loading');
            if (res.code == 0) {
                layer.msg(res.msg, {icon: 1});
                // 关闭当前iframe层
                var index = parent.layer.getFrameIndex(window.name);
                if (index) parent.layer.close(index);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        return false;
    });
});
</script> 