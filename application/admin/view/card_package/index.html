{include file="public/iframeheader"/}

{block name="style"}
<style type="text/css">
    /*这里需要加上 .layui-table 如若不加可能会不生效*/
    .layui-table .layui-table-cell {
        height: auto;
        word-break: normal;
        display: block;
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow: hidden;
        padding: 0 10px !important;
    }
    /* 修复固定列行高不一致问题 */
    .layui-table-fixed .layui-table-cell {
        height: auto;
        vertical-align: middle;
    }
    /* 操作列按钮样式调整 */
    .layui-table td .layui-btn-xs {
        margin: 2px;
        vertical-align: middle;
    }
    /* 多选框样式调整 */
    .layui-table td .layui-form-checkbox {
        margin: 0;
        vertical-align: middle;
    }
    /* 固定列内容垂直居中 */
    .layui-table-fixed .layui-table-body .layui-table-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 40px;
    }
</style>
{/block}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">套餐管理</div>
                <div class="layui-card-body">
                    <div class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">套餐名称：</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="name" class="layui-input" placeholder="请输入套餐名称">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">状态：</label>
                                <div class="layui-input-inline">
                                    <select name="status">
                                        <option value="">全部</option>
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn icon-btn" lay-filter="formSearch" lay-submit>
                                    <i class="layui-icon">&#xe615;</i>搜索
                                </button>
                                <button class="layui-btn icon-btn" id="btnAdd">
                                    <i class="layui-icon">&#xe654;</i>添加
                                </button>
                            </div>
                        </div>
                    </div>

                    <table class="layui-table" id="dataTable" lay-filter="dataTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 状态列 -->
<script type="text/html" id="statusTpl">
    <input type="checkbox" lay-filter="ckStatus" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" {{d.status==1?'checked':''}} />
</script>

<script>
layui.use(['table', 'form', 'util', 'layer'], function() {
    var $ = layui.jquery;
    var table = layui.table;
    var form = layui.form;
    var util = layui.util;
    var layer = layui.layer;
    
    // 渲染表格
    table.render({
        elem: '#dataTable',
        url: '{:url("index")}',
        page: true,
        cols: [[
            {type: 'numbers'},
            {field: 'id', title: 'ID', width: 80},
            {field: 'name', title: '套餐名称'},
            {field: 'price', title: '价格'},
            {field: 'product_specs', title: '商品规格', width: 200, templet: function(d){
                return d.product_specs || '';
            }},
            {field: 'description', title: '描述'},
            {field: 'status', title: '状态',  templet: '#statusTpl'},
            {field: 'add_time', title: '添加时间',  templet: function(d) {
                return util.toDateString(d.add_time);
            }},
            {field: 'update_time', title: '更新时间',  templet: function(d) {
                return util.toDateString(d.update_time);
            }},
            {title: '操作', toolbar: "#tableBar",  fixed: 'right'}
        ]],
        done: function (res, curr, count) {
            // 该方法用于解决,使用fixed固定列后,行高和其他列不一致的问题
            $(".layui-table-main tr").each(function (index, val) {
                $($(".layui-table-fixed .layui-table-body tbody tr")[index]).height($(val).height());
            });
        }
    });
    
    // 搜索
    form.on('submit(formSearch)', function(data) {
        table.reload('dataTable', {
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });
    
    // 工具条点击事件
    table.on('tool(dataTable)', function(obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'edit') { // 修改
            showEditModel(data.id);
        } else if (layEvent === 'del') { // 删除
            doDel(data.id);
        }
    });
    
    // 添加按钮点击事件
    $('#btnAdd').click(function() {
        showAddModel();
    });
    
    // 显示添加弹窗
    function showAddModel() {
        layer.open({
            type: 2,
            title: '添加套餐',
            area: ['1200px', '90%'],
            shade: 0.1,
            anim: 5,
            content: '{:url("add")}',
            yes: function(index, layero) {
                var iframeWindow = window['layui-layer-iframe' + index];
                var $iframeDoc = $(layero).find('iframe').contents();
                $iframeDoc.find('#modelSubmitBtn').click();
            },
            end: function() {
                table.reload('dataTable');
            }
        });
    }
    
    // 显示编辑弹窗
    function showEditModel(id) {
        layer.open({
            type: 2,
            title: '修改套餐',
            area: ['1200px', '90%'],
            shade: 0.1,
            anim: 5,
            content: '{:url("edit")}' + '?id=' + id,
            yes: function(index, layero) {
                var iframeWindow = window['layui-layer-iframe' + index];
                var $iframeDoc = $(layero).find('iframe').contents();
                $iframeDoc.find('#modelSubmitBtn').click();
            },
            end: function() {
                table.reload('dataTable');
            }
        });
    }
    
    // 删除
    function doDel(id) {
        layer.confirm('确定要删除此套餐吗？', {
            skin: 'layui-layer-admin',
            shade: .1
        }, function(i) {
            layer.close(i);
            layer.load(2);
            $.post('{:url("delete")}', {
                id: id
            }, function(res) {
                layer.closeAll('loading');
                if (res.code == 0) {
                    layer.msg(res.msg, {icon: 1});
                    table.reload('dataTable');
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, 'json');
        });
    }
    
    // 修改状态
    form.on('switch(ckStatus)', function(obj) {
        layer.load(2);
        $.post('{:url("updateStatus")}', {
            id: obj.value,
            status: obj.elem.checked ? 1 : 0
        }, function(res) {
            layer.closeAll('loading');
            if (res.code == 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
                $(obj.elem).prop('checked', !obj.elem.checked);
                form.render('checkbox');
            }
        }, 'json');
    });
});
</script>
{/block} 