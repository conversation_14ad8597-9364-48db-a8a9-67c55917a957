<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
<br/>
<div style="margin: 20px;">

    <div class="page-content">
        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
    </div>



</div>

</body>

</html>
<script type="text/javascript">

    layui.use(['table', 'form'], function () {
        var table = layui.table;
        var form = layui.form;
        //方法级渲染
        table.render({
            elem: '#demo'
            , url: "{:url('ajax_order_logs')}?id={$id}"
            , cols: [[
                { type: 'checkbox' }
                , { type: 'numbers', title: '序号', width: 50 }
                , { field: 'order_no', title: '订单号', align: 'center' }
                // , { field: 'background_image', title: '背景图', width: 250 }
                , { field: 'content', title: '操作内容' }
                , { field: 'operator', title: '操作人' }
                , { field: 'add_time', title: '操作时间' }
            ]]
            , page: true
        });

    });



</script>
