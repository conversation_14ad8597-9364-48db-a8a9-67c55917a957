

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>登入 - {$titlecn}</title>
  {include file="public/iframeheader" }
  <link rel="stylesheet" href="__STATIC__/admin/style/login.css" media="all">
</head>
<body>

<div class="api_check_str" style="display:none;">{:api_check_str()}</div>
  <div class="layadmin-user-login layadmin-user-display-show" id="LAY-user-login" style="display: none;">

    <div class="layadmin-user-login-main">
      <div class="layadmin-user-login-box layadmin-user-login-header">
        <h2>{$titlecn}</h2>
        <p>官方出品的单页面后台管理模板系统</p>
      </div>
      <div class="layadmin-user-login-box layadmin-user-login-body layui-form">
        <div class="layui-form-item">
          <label class="layadmin-user-login-icon layui-icon layui-icon-username" for="LAY-user-login-username"></label>
          <input type="text" name="accountname" id="LAY-user-login-username" lay-verify="required" placeholder="用户名" class="layui-input">
        </div>
        <div class="layui-form-item">
          <label class="layadmin-user-login-icon layui-icon layui-icon-password" for="LAY-user-login-password"></label>
          <input type="password" name="accountpassword" id="LAY-user-login-password" lay-verify="required" placeholder="密码" class="layui-input">
        </div>
        <div class="layui-form-item">
          <div class="layui-row">
            <div class="layui-col-xs7">
              <label class="layadmin-user-login-icon layui-icon layui-icon-vercode" for="LAY-user-login-vercode"></label>
              <input type="text" name="verify" id="LAY-user-login-vercode" lay-verify="required" placeholder="图形验证码" class="layui-input">
            </div>
            <div class="layui-col-xs5">
              <div style="margin-left: 10px;">
                  <img link="imgcode" style="cursor:pointer;"
                                                                 src="{:captcha_src()}" width="120px" height="40px" title="看不清楚？点击刷新"
                                                                 onclick="this.src = '{:captcha_src()}?'+new Date().getTime()">
              </div>
            </div>
          </div>
        </div>
        <div class="layui-form-item" style="margin-bottom: 20px;">
          <!--
          <input type="checkbox" name="remember" lay-skin="primary" title="记住密码">
          <a href="forget.html" class="layadmin-user-jump-change layadmin-link" style="margin-top: 7px;">忘记密码？</a>
        -->
        </div>
        <div class="layui-form-item">
          <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="LAY-user-login-submit"  link="sub">登 入</button>
        </div>
        <!--
        <div class="layui-trans layui-form-item layadmin-user-login-other">
          <label>社交账号登入</label>
          <a href="javascript:;"><i class="layui-icon layui-icon-login-qq"></i></a>
          <a href="javascript:;"><i class="layui-icon layui-icon-login-wechat"></i></a>
          <a href="javascript:;"><i class="layui-icon layui-icon-login-weibo"></i></a>

          <a href="reg.html" class="layadmin-user-jump-change layadmin-link">注册帐号</a>
        </div>
        -->
      </div>
    </div>
    <!--
    <div class="layui-trans layadmin-user-login-footer">

      <p>© 2018 <a href="#" target="_blank">0</a></p>
      <p>
        <span><a href="#" target="_blank">1</a></span>
        <span><a href="#" target="_blank">2</a></span>
        <span><a href="#" target="_blank">3</a></span>
      </p>
    </div>
    -->
    <!--<div class="ladmin-user-login-theme">
      <script type="text/html" template>
        <ul>
          <li data-theme=""><img src="{{ layui.setter.base }}style/res/bg-none.jpg"></li>
          <li data-theme="#03152A" style="background-color: #03152A;"></li>
          <li data-theme="#2E241B" style="background-color: #2E241B;"></li>
          <li data-theme="#50314F" style="background-color: #50314F;"></li>
          <li data-theme="#344058" style="background-color: #344058;"></li>
          <li data-theme="#20222A" style="background-color: #20222A;"></li>
        </ul>
      </script>
    </div>-->

  </div>

  <script>
  layui.config({
    base: '__STATIC__/admin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'user'], function(){
    var $ = layui.$
    ,setter = layui.setter
    ,admin = layui.admin
    ,form = layui.form
    ,router = layui.router()
    ,search = router.search;

    form.render();

    //提交
    form.on('submit(LAY-user-login-submit)', function(obj){

      //请求登入接口
      admin.req({
        url:'{:url('Login/login')}' //实际使用请改成服务端真实接口
        ,data: obj.field
        ,done: function(res){

          //登入成功的提示与跳转
          layer.msg('登入成功', {
            offset: '15px'
            ,icon: 1
            ,time: 1000
          }, function(){
               window.location.href = "{:url('Index/index')}";
          });



        }
      });

    });


  });
  </script>
</body>
</html>


<script>
    //添加回车事件
    $(function () {
        $(document).keydown(function (event) {
            if (event.keyCode == 13) {
                $("[link=sub]").click();
            }
        });
    });
</script>
