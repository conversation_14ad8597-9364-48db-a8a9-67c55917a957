<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{$titlecn}</title>
    <link rel="shortcut icon" href="__ROOT__/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="__STATIC__/admin/login/login3/css/common.css">
    <link rel="stylesheet" href="__STATIC__/admin/login/login3/css/login.css">
    <link rel="stylesheet" href="__STATIC__/admin/login/login3/css/loading.css">
    <script src="__STATIC__/admin/login/login3/js/jquery-1.9.1.min.js" type="text/javascript"></script>
    <script src="__STATIC__/admin/login/login3/layer/layer.js" type="text/javascript"></script>
    <!-- 引入微信官方登录JS -->
    <script src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js"></script>
    <style>
        .loadingtop {
            width: 100%;
            height: 100%;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 100;
            background-color: #fff;
        }

        .loadingtop .pic {
            /*页面开始加载的时候上方显示的一条线，默认宽度为0*/
            width: 0%;
            height: 5px;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background: #409f69;
        }

        /* 二维码登录区域样式 */
        .qrcode-login {
            position: absolute;
            right: calc(10rem + 23rem + 1rem); /* 账号密码登录位置 + 宽度 + 间距 */
            top: 50%;
            transform: translateY(-50%);
            width: 20rem; /* 与账号密码登录保持一致的宽度单位 */
            height: 28rem; /* 与账号密码登录保持一致的高度 */
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .qrcode-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .qrcode-subtitle {
            font-size: 12px;
            color: #666;
            margin-bottom: 15px;
        }

        /* 微信登录容器样式 */
        #wechat_login_container {
            width: 200px;
            height: 200px;
            margin: 0 auto 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 微信官方二维码样式重写 */
        #wechat_login_container .impowerBox {
            border: none !important;
            background: transparent !important;
            width: 190px !important;
            height: 190px !important;
        }

        #wechat_login_container .qrcode {
            width: 190px !important;
            height: 190px !important;
        }

        #wechat_login_container .title {
            display: none !important;
        }

        #wechat_login_container .info {
            width: 190px !important;
            text-align: center !important;
        }

        #wechat_login_container .status {
            text-align: center !important;
            font-size: 10px !important;
            color: #666 !important;
        }

        .qrcode-status {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
            line-height: 1.4;
        }

        .qrcode-expired {
            color: #f56c6c;
        }

        .qrcode-success {
            color: #67c23a;
        }

        /* 移除分隔线 */
        .login-divider {
            display: none;
        }


        /* 响应式设计 */
        @media (max-width: 768px) {
            .qrcode-login {
                position: relative;
                left: auto;
                right: auto;
                top: auto;
                transform: none;
                width: 90%;
                height: auto;
                margin: 20px auto 10px auto;
                display: flex;
            }
            
            .login1 {
                position: relative !important;
                left: auto !important;
                top: auto !important;
                transform: none !important;
                width: 90%;
                height: auto;
                margin: 10px auto 20px auto;
            }
            
            .container {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 20px;
                min-height: 100vh;
                justify-content: center;
            }
        }

        
    </style>
</head>

<body>

<div class="api_check_str" style="display:none;">{:api_check_str()}</div>
    <div class="loadingtop">
        <div class="pic"></div>
    </div>

    <div class="loading">
        <div class="content">
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
        </div>
    </div>

    <script>
        $(".loadingtop .pic").animate({ width: "10%" }, 100);
    </script>

    <div class="container">
        <!-- 左侧微信二维码登录区域 -->
        {if condition="$wechat_login_enabled eq 1"}
        <div class="qrcode-login " id="qrcodeLogin">
            <!-- <div class="qrcode-title">微信扫码登录</div>
            <div class="qrcode-subtitle">使用微信扫描下方二维码</div> -->
            
            <!-- 微信二维码容器 -->
            <div id="wechat_login_container" style="margin: 20px auto;"></div>
            
            <!-- <div class="qrcode-status" id="qrcodeStatus">请使用微信扫描二维码</div> -->
        </div>
        {/if}
        <!-- 分隔线 -->
        <div class="login-divider"></div>

        <!-- 右侧账号密码登录区域 -->
        <div class="login1">
            <div class="word1">
                {$titlecn}
            </div>
            <script>
                $(".loadingtop .pic").animate({ width: "30%" }, 100);
            </script>
            <div class="user1">
                <input type="text" placeholder="用户名" name="accountname" value="{:cookie('cn_accountname')}">
            </div>
            <div class="pass1">
                <input type="password" id="test" placeholder="密码" name="accountpassword">
            </div>
            <div class="yzm1" link="verify" style="display:{if condition='$try_num gt 3'}block{else}none{/if};">
                <input type="text" class="topAlign" id="verify" name="verify" required placeholder="验证码">
                <canvas width="100" height="40" id="verifyCanvas"></canvas>
                <img link="imgcode" id="code_img" src="{:captcha_src()}"
                    onclick="this.src = '{:captcha_src()}?' + new Date().getTime()">
            </div>
            <div class="choose">
                <input type="checkbox" name="remember" value="1" {if condition="cookie('cn_accountname') neq ''"} checked="checked"
                    {/if}>
                记住我
            </div>
            <a class="login_button" link="sub">
                登录
            </a>
        </div>
    </div>

    <script>
        $(".loadingtop .pic").animate({ width: "60%" }, 100);
    </script>
</body>

    <script>
        // 微信登录配置
        let wechatConfig = null;
        let wxLoginObj = null;

        // 页面加载完成后初始化微信登录
        $(document).ready(function() {
            // 检查是否有微信授权错误信息
            checkWechatError();
            initWechatLogin();
            checkWechatLoginReturn();
        });
        
        // 检查微信授权错误信息
        function checkWechatError() {
            {if condition="$Think.session.wechat_error_msg"}
                layer.msg('{$Think.session.wechat_error_msg}', {
                    icon: 2,
                    time: 4000
                });
                // 清除session中的错误信息
                {php}
                    session('wechat_error_msg', null);
                {/php}
            {/if}
        }
        
        // 显示错误信息函数
        function showError(message) {
            layer.msg(message, {
                icon: 2,
                time: 3000
            });
        }

        // 初始化微信内嵌登录
        function initWechatLogin() {
            $('#qrcodeStatus').text('正在初始化微信登录...');
            
            // 获取微信登录配置
            $.ajax({
                url: "{:url('admin/wechat_auth/getWechatLoginConfig')}",
                type: 'POST',
                success: function(response) {
                    if (response.code === 200) {
                        wechatConfig = response.data;
                        createWxLogin();
                    } else {
                        showError('微信登录初始化失败: ' + response.msg);
                    }
                },
                error: function() {
                    showError('网络错误，请刷新重试');
                }
            });
        }

        // 创建微信官方内嵌登录二维码
        function createWxLogin() {
            if (!wechatConfig || !window.WxLogin) {
                showError('微信登录SDK未加载或配置错误');
                return;
            }

            try {
                // 使用后端生成的state参数
                var state = wechatConfig.state;
                
                // 创建微信登录对象
                wxLoginObj = new WxLogin({
                    self_redirect: false, // 不在当前窗口跳转
                    id: "wechat_login_container", // 二维码容器ID
                    appid: wechatConfig.appid, // 微信开放平台AppID
                    scope: wechatConfig.scope, // 授权范围
                    redirect_uri: wechatConfig.redirect_uri, // 回调地址
                    state: state, // 使用后端生成的状态参数
                    style: "black", // 二维码样式
                    href: "" // 自定义样式文件
                });

                $('#qrcodeStatus').text('请使用微信扫描二维码');
                
                // 监听微信登录回调（通过postMessage）
                window.addEventListener('message', function(event) {
                    // 检查消息来源
                    if (event.origin !== 'https://open.weixin.qq.com') {
                        return;
                    }
                    
                    if (event.data.type === 'WX_LOGIN_SUCCESS') {
                        $('#qrcodeStatus').html('<span class="qrcode-success">✓ 登录成功，正在跳转...</span>');
                        // 微信登录成功，等待页面跳转
                    }
                }, false);

            } catch (error) {
                console.error('微信登录初始化失败:', error);
                showError('微信登录初始化失败，请刷新页面重试');
            }
        }

        // 显示错误信息
        function showError(message) {
            $('#wechat_login_container').html(`
                <div style="color: #f56c6c; font-size: 14px; text-align: center; padding: 60px 20px;">
                    <div style="margin-bottom: 10px; font-size: 24px;">⚠️</div>
                    <div>${message}</div>
                    <div style="margin-top: 15px;">
                        <button onclick="initWechatLogin()" style="background: #07c160; color: white; border: none; padding: 8px 20px; border-radius: 6px; cursor: pointer;">
                            重新加载
                        </button>
                    </div>
                </div>
            `);
            $('#qrcodeStatus').html(`<span class="qrcode-expired">${message}</span>`);
        }

        // 检查是否从微信登录返回
        function checkWechatLoginReturn() {
            var urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('wechat_login') === 'success') {
                layer.msg('微信登录成功！', {
                    icon: 1,
                    time: 2000,
                    end: function() {
                        window.history.replaceState({}, document.title, window.location.pathname);
                    }
                });
            } else if (urlParams.get('wechat_login') === 'error') {
                var errorMsg = urlParams.get('msg') || '微信登录失败';
                layer.msg(decodeURIComponent(errorMsg), {icon: 2});
                window.history.replaceState({}, document.title, window.location.pathname);
            }
        }

    // 原有的账号密码登录逻辑
    $(function () {
        $("[link=sub]").click(function () {
            var accountname = $("[name=accountname]").val();
            var accountpassword = $("[name=accountpassword]").val();
            var verify = $("[name=verify]").val();
            var remember = $("[name=remember]:checked").val();
            layer.msg("正在验证，请稍后");

            $.post("{:url('Login/login')}", {
                accountname: accountname,
                accountpassword: accountpassword,
                verify: verify,
                remember: remember
            }, function (data) {
                if (data.sta == '1') {
                    layer.msg("验证成功，正在跳转", {
                        time: 1000,
                        end: function () {
                            window.location.href = "{:url('Index/index')}";
                        }
                    });
                } else if (data.sta == '0') {
                    layer.msg(data.msg);
                    $("[link=imgcode]").trigger("click");
                    if (data.try_count > 3 || data.try_num > 3) {
                        $("[link=verify]").show();
                    }
                }
            }, "json");
            return false;
        });

        // 添加回车事件
        $(document).keydown(function (event) {
            if (event.keyCode == 13) {
                $("[link=sub]").click();
            }
        });
    });
</script>

<script>
    $(".loadingtop .pic").animate({ width: "80%" }, 100);
    ;
</script>
<script>
    document.onreadystatechange = function () {
        if (document.readyState == "complete")
            console.log(document.readyState)
        $(".loading").fadeOut();
    }
</script>
<script>
    $(".loadingtop .pic").animate({ width: "100%" }, 100, function () {
        $(".loadingtop").fadeOut();
    });
</script>

</html>
