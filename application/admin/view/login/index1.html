<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta charset="utf-8"/>
    <title>{$titlecn}</title>
    <link rel="shortcut icon" href="__ROOT__/favicon.ico" type="image/x-icon"/>
    <meta name="keywords" content="{$Think.CONFIG.keywords}"/>
    <meta name="description" content="{$Think.CONFIG.description}"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>

    <script src="__STATIC__/admin/login/login1/js/jquery-1.9.1.min.js" type="text/javascript"></script>
    <script src="__STATIC__/admin/login/login1/layer/layer.js" type="text/javascript"></script>

    <!-- HTML5shiv and Respond.js for IE8 to support HTML5 elements and media queries -->

    <!-- basic scripts -->

    <!--[if !IE]> -->
    <script type="text/javascript">
        window.jQuery || document.write("<script src='__STATIC__/admin/login/login1/js/jquery.js'>" + "<" + "/script>");
    </script>

    <!-- <![endif]-->

    <!--[if IE]>
    <script type="text/javascript">
        window.jQuery || document.write("<script src='__STATIC__/admin/login/login1/js/jquery1x.js'>" + "<" + "/script>");
    </script>
    <![endif]-->
    <script type="text/javascript">
        if ('ontouchstart' in document.documentElement) document.write("<script src='__STATIC__/admin/login/login1/js/jquery.mobile.custom.js'>" + "<" + "/script>");
    </script>


    <!-- bootstrap & fontawesome -->
    <link rel="stylesheet" href="__STATIC__/admin/login/login1/css/bootstrap.css"/>
    <link rel="stylesheet" href="__STATIC__/admin/login/login1/css/font-awesome.css"/>

    <!-- page specific plugin styles -->

    <!-- ace styles -->
    <link rel="stylesheet" href="__STATIC__/admin/login/login1/css/ace.css" class="ace-main-stylesheet"
          id="main-ace-style"/>

    <!--[if lte IE 9]>
    <link rel="stylesheet" href="__STATIC__/admin/login/login1/css/ace-part2.css" class="ace-main-stylesheet"/>
    <![endif]-->

    <!--[if lte IE 9]>
    <link rel="stylesheet" href="__STATIC__/admin/login/login1/css/ace-ie.css"/>

    <![endif]-->


</head>
<body class="login-layout" style="background-image:url('__STATIC__/admin/login/login1/img/bj.png')">

<div class="main-container login-main-container">

    <div class="api_check_str" style="display:none;">{:api_check_str()}</div>
    <div class="main-content">
        <div class="row">
            <div class="col-sm-10 col-sm-offset-1">
                <div class="login-container">
                    <div class="space-6"></div>

                    <br/>
                    <div class="position-relative">
                        <div id="login-box" class="login-box visible widget-box no-border">
                            <div class="widget-body">
                                <div class="widget-main">
                                    <h4 class="header blue lighter bigger" style="text-align: center;">
                                        <i class="ace-icon fa fa-coffee green"></i>
                                        <b>{$titlecn}</b>
                                    </h4>

                                    <div class="space-6"></div>

                                    <form action="#" method="post">
                                        <fieldset>
                                            <label class="block clearfix">
														<span class="block input-icon input-icon-right">
															<input type="text" class="form-control" name="accountname"
                                                                   placeholder="用户名" value=""/>
															<i class="ace-icon fa fa-user"></i>
														</span>
                                            </label>

                                            <label class="block clearfix">
														<span class="block input-icon input-icon-right">
															<input type="password" class="form-control"
                                                                   name="accountpassword"
                                                                   placeholder="密码"
                                                                   value=""/>
															<i class="ace-icon fa fa-lock"></i>
														</span>
                                            </label>


                                            <div link="verify" style='display:{if condition="$try_num  gt '3'"}block{else}none{/if};'>
                                                <div class="space"></div>
                                                <label class="block clearfix">
														<span class="block input-icon ">
															<span class="inline"><input type="text" class="form-control"
                                                                                        name="verify" placeholder="验证码"
                                                                                        id="code" required/></span>

															<img link="imgcode" style="cursor:pointer;"
                                                                 src="{:captcha_src()}" width="100px" title="看不清楚？点击刷新"
                                                                 onclick="this.src = '{:captcha_src()}?'+new Date().getTime()">
														</span>
                                                </label>
                                            </div>


                                            <div class="space"></div>

                                            <div class="clearfix">

                                                <label class="inline">
                                                    <input checked="checked" type="checkbox" class="ace" value="1"
                                                           name="remember"/>
                                                    <span class="lbl"> 记住我</span>
                                                </label>

                                                <button type="button" link="sub"
                                                        class="width-35 pull-right btn btn-sm btn-primary">
                                                    <i class="ace-icon fa fa-key"></i>
                                                    <span class="bigger-110">登录</span>
                                                </button>
                                            </div>

                                            <div class="space-4"></div>
                                        </fieldset>
                                    </form>
                                </div><!-- /.widget-main -->
                            </div><!-- /.widget-body -->
                        </div><!-- /.login-box -->
                    </div><!-- /.position-relative -->

                </div>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.main-content -->
</div><!-- /.main-container -->


<!-- inline scripts related to this page -->
<script type="text/javascript">
    jQuery(function ($) {
        $(document).on('click', '.toolbar a[data-target]', function (e) {
            e.preventDefault();
            var target = $(this).data('target');
            $('.widget-box.visible').removeClass('visible');//hide others
            $(target).addClass('visible');//show target
        });
    });


    //you don't need this, just used for changing background
    jQuery(function ($) {
        $('#btn-login-dark').on('click', function (e) {
            $('body').attr('class', 'login-layout');
            $('#id-text2').attr('class', 'white');
            $('#id-company-text').attr('class', 'blue');

            e.preventDefault();
        });
        $('#btn-login-light').on('click', function (e) {
            $('body').attr('class', 'login-layout light-login');
            $('#id-text2').attr('class', 'grey');
            $('#id-company-text').attr('class', 'blue');

            e.preventDefault();
        });
        $('#btn-login-blur').on('click', function (e) {
            $('body').attr('class', 'login-layout blur-login');
            $('#id-text2').attr('class', 'white');
            $('#id-company-text').attr('class', 'light-blue');

            e.preventDefault();
        });

    });
</script>
</body>
</html>

<script>
    $(function () {
        $("[link=sub]").click(function () {
            var accountname = $("[name=accountname]").val();
            var accountpassword = $("[name=accountpassword]").val();
            var verify = $("[name=verify]").val();
            var remember = $("[name=remember]:checked").val();
            layer.msg("正在验证，请稍后");

            $.post("{:url('Login/login')}", {
                accountname: accountname,
                accountpassword: accountpassword,
                verify: verify,
                remember: remember
            }, function (data) {
                if (data.sta == '1') {
                    layer.msg("验证成功，正在跳转", {
                        time: 1000,
                        end: function () {
                            window.location.href = "{:url('Index/index')}";
                        }
                    });
                }
                else if (data.sta == '0') {
                    layer.msg(data.msg);
                    $("[link=imgcode]").trigger("click");
                    if (data.try_count > 3 || data.try_num > 3) {
                        $("[link=verify]").show();
                    }
                }
            }, "json");
            return false;
        });
    });

</script>
<script>
    //添加回车事件
    $(function () {
        $(document).keydown(function (event) {
            if (event.keyCode == 13) {
                $("[link=sub]").click();
            }
        });
    });
</script>
