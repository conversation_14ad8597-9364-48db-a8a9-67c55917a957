<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>控制台主页</title>
  {include file="public/iframeheader"/}
</head>
<style>
  .layui-col-xs6 {
    width: 25%
  }

  .layadmin-backlog .layadmin-backlog-body {
    background: crimson;
    color: white;
  }

  .layadmin-backlog-body p cite {
    color: white;
  }
</style>

<body>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md8">
        <div class="layui-row layui-col-space15">
          <div class="layui-col-md6" style="display: none;">


            <div class="layui-card">

              <div class="layui-card-header">常用功能</div>
              <div class="layui-card-body">


                <div class="layui-carousel layadmin-carousel layadmin-shortcut">
                  <div carousel-item>


                    <ul class="layui-row layui-col-space10">

                      {volist name="menulist_arr" id="menulist_arr0"}
                      {volist name="menulist_arr0['children']" id="menulist_arr1" length="80"}
                      {if condition="$menulist_arr1['is_changyong'] eq '1' "}
                      <li class="layui-col-xs3">
                        <a lay-href="{:url($menulist_arr1['url'])}">
                          <i class="layui-icon {$menulist_arr1['icon']|default=$menulist_arr0['icon']}"></i>
                          <cite>{$menulist_arr1['menu_name']}</cite>
                        </a>
                      </li>
                      {/if}
                      {/volist}
                      {/volist}
                    </ul>


                  </div>
                </div>

              </div>
            </div>

          </div>



          <div class="layui-col-md6" style="width: 100%;display: none;">
            <div class="layui-card">
              <div class="layui-card-header">数据概览</div>
              <div class="layui-card-body">

                <div class="layui-carousel layadmin-carousel layadmin-backlog">
                  <div carousel-item>
                    <ul class="layui-row layui-col-space10">
                      <li class="layui-col-xs6">
                        <a href="javascript:;" onclick="layer.tips('总订单量', this, {tips: 1});"
                          class="layadmin-backlog-body">
                          <h3>总订单量</h3>
                          <p><cite>{$all_order_count|default="0"}</cite></p>
                        </a>
                      </li>
                      <li class="layui-col-xs6">
                        <a href="javascript:;" onclick="layer.tips('平台订单量', this, {tips: 2});"
                          class="layadmin-backlog-body">
                          <h3>平台订单量</h3>
                          <p><cite>{$dg_order_count|default="0"}</cite></p>
                        </a>
                      </li>
                      <li class="layui-col-xs6">
                        <a href="javascript:;" onclick="layer.tips('自营订单量', this, {tips: 3});"
                          class="layadmin-backlog-body">
                          <h3>自营订单量</h3>
                          <p><cite>{$ddcf_order_count|default="0"}</cite></p>
                        </a>
                      </li>
                      <li class="layui-col-xs6">
                        <a href="javascript:;" onclick="layer.tips('会员数', this, {tips: 4});"
                          class="layadmin-backlog-body">
                          <h3>会员数</h3>
                          <p><cite>{$client_member_count|default="0"}</cite></p>
                        </a>
                      </li>
                      <li class="layui-col-xs6">
                        <a href="javascript:;" onclick="layer.tips('充值卡数', this, {tips: 5});"
                          class="layadmin-backlog-body">
                          <h3>充值卡数</h3>
                          <p><cite>{$client_card_count|default="0"}</cite></p>
                        </a>
                      </li>
                      <li class="layui-col-xs6">
                        <a href="javascript:;" onclick="layer.tips('代金卡数', this, {tips: 6});"
                          class="layadmin-backlog-body">
                          <h3>代金卡数</h3>
                          <p><cite>{$client_ycard_count|default="0"}</cite></p>
                        </a>
                      </li>

                    </ul>


                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="layui-col-md12">
            <div class="layui-card" style="display: none;">
              <div class="layui-card-header">图形统计走势</div>
              <div class="layui-card-body">

                <div class="layui-carousel layadmin-carousel layadmin-dataview" data-anim="fade"
                  lay-filter="LAY-index-dataview">
                  <div carousel-item id="LAY-index-dataview">
                    <div><i class="layui-icon layui-icon-loading1 layadmin-loading"></i></div>
                    <div></div>
                    <div></div>

                  </div>
                </div>

              </div>
            </div>
            <div class="layui-card">
              <div class="layui-tab layui-tab-brief layadmin-latestData">
                <ul class="layui-tab-title">
                  <li class="layui-this">登录日志</li>

                  <li>操作日志</li>

                </ul>
                <div class="layui-tab-content">
                  <div class="layui-tab-item layui-show">
                    <table id="LAY-index-login_log"></table>
                  </div>
                  <div class="layui-tab-item">
                    <table id="LAY-index-operate_log"></table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="layui-col-md4">

        <div class="layui-card">
          <div class="layui-card-header">版本信息</div>
          <div class="layui-card-body layui-text">
            <table class="layui-table">
              <colgroup>
                <col width="120">
                <col>
              </colgroup>
              <tbody>
                <tr>
                  <td>服务器系统：</td>
                  <td>
                    {:php_uname()}
                  </td>
                </tr>
                <tr>
                  <td>web服务器：</td>
                  <td> {$http_version}</td>
                </tr>

                <tr>
                  <td>TP版本：</td>
                  <td>
                    {$Think.version}
                  </td>
                </tr>
                <tr>
                  <td>php版本：</td>
                  <td>
                    {$Think.const.PHP_VERSION}
                  </td>
                </tr>
                <tr>
                  <td>mysql版本：</td>
                  <td> {$mysql_version}</td>
                </tr>
                <tr>
                  <td>redis版本：</td>
                  <td> {$redis_version}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>





        <div class="layui-card">

          {if condition="$sys_os eq 'win' "}
          <div class="layui-card-header">{$path|default='未知'}磁盘占用</div>
          <div class="layui-card-body layadmin-takerates">


            <div class="layui-progress" lay-showPercent="yes">
              <h3>使用率（总计：{$total_space|default='未知'}G&nbsp;&nbsp;&nbsp;空闲：{$free_space|default='未知'}G ）</h3>
              <div class="layui-progress-bar" lay-percent="{$disk_usage|default='0'}%"></div>
            </div>
          </div>
          {/if}

          {if condition="$sys_os eq 'lin' "}
          <div class="layui-card-header">磁盘占用</div>
          <div class="layui-card-body layadmin-takerates">
            {if condition="empty($disklist) neq true "}
            {volist name="disklist" id='v'}
            <div class="layui-progress" lay-showPercent="yes">
              <h3>（总计:{$v['total_space']|default='未知'} 空闲:{$v['free_space']|default='未知'} 分区:{$v['zone']|default='未知'}
                挂载点:{$v['mount_point']|default='未知'}）</h3>
              <div class="layui-progress-bar" lay-percent="{$v['percent']|default='0'}"></div>
            </div>
            {/volist}
            {else /}
            <div class="layui-progress" lay-showPercent="yes">
              <h3>（{$err_msg|default='未知错误'}）</h3>
              <div class="layui-progress-bar" lay-percent="{$v['percent']|default='0'}"></div>
            </div>
            {/if}
          </div>
          {/if}



        </div>

        {if condition="$sys_os eq 'win' "}
        <div class="layui-card">
          <div class="layui-card-header">实时监控</div>
          <div class="layui-card-body layadmin-takerates">
            <div class="layui-progress" lay-showPercent="yes">
              <h3>CPU使用率</h3>
              <div class="layui-progress-bar" id="cpu" lay-percent="{$cpu|default='0'}%"></div>
            </div>
            <div class="layui-progress" lay-showPercent="yes">
              <h3>内存占用率</h3>
              <div class="layui-progress-bar layui-bg-red" id="memory" lay-percent="{$memory['usage']|default='0'}%">
              </div>
            </div>
          </div>
        </div>
        {/if}


        <div class="layui-card" style="display: none;">
          <div class="layui-card-header">产品动态</div>
          <div class="layui-card-body">
            <div class="layui-carousel layadmin-carousel layadmin-news" data-autoplay="true" data-anim="fade"
              lay-filter="news">
              <div carousel-item>
                <div><a class="layui-bg-red">储值卡商城系统</a></div>
                <div><a class="layui-bg-green">一件代发系统</a></div>
                <div><a class="layui-bg-blue">卡册提供系统</a></div>
              </div>
            </div>
          </div>
        </div>


        <div class="layui-card" style="display: none;">
          <div class="layui-card-header">
            作者心语
            <i class="layui-icon layui-icon-tips" lay-tips="要支持的噢" lay-offset="5"></i>
          </div>
          <div class="layui-card-body layui-text layadmin-text">
            {:session("cn_system_set_info.client_author_words")}
          </div>
        </div>
      </div>

    </div>
  </div>

  <script>
    layui.config({
      base: '__STATIC__/admin/' //静态资源所在路径
    }).extend({
      index: 'lib/index' //主入口模块
    }).use(['index', 'console']);
  </script>


  <script>
    //最新订单
    layui.use('table', function () {
      var $ = layui.$
        , table = layui.table;
      //登录日志
      table.render({
        elem: '#LAY-index-login_log'
        , url: "{:url('Plat/loginrecordlist')}?ajax=ajax" //模拟接口
        , page: true
        , cols: [[
          { type: 'numbers', fixed: 'left', title: '序号' }
          , { field: 'accountname', title: '登录账号' }
          , { field: 'logindatetime', title: '登录时间', sort: true }
          , { field: 'loginip', title: '登录ip' }
          , { field: 'remark', title: '登录备注' }
        ]]
        , skin: 'line'
      });
      //操作日志
      table.render({
        elem: '#LAY-index-operate_log'
        , url: "{:url('Plat/operaterecordlist')}?ajax=ajax" //模拟接口
        , page: true
        , cols: [[
          { type: 'numbers', fixed: 'left', title: '序号' }
          , { field: 'accountname', title: '操作账号' }
          , { field: 'operatedatetime', title: '操作时间', sort: true }
          , { field: 'ip', title: '操作者ip' }
          , { field: 'log', title: '操作内容' }
        ]]
        , skin: 'line'
      });
    })
  </script>



  {if condition="$sys_os eq 'win' "}
  <script>
    setInterval("get_sys_info()", "**********");
    //异步获取短信信息
    function get_sys_info() {
      //注意进度条依赖 element 模块，否则无法进行正常渲染和功能性操作
      layui.use('element', function () {
        var element = layui.element;
        $.post("{:url('console')}?ajax=ajax", {}, function (data) {
          if (data.sta == 1) {
            $("#cpu").attr("lay-percent", data.cpu);
            $("#memory").attr("lay-percent", data.memory);
            element.init();
          }
        }, "json");
      });
    }
  </script>
  {/if}


  <script>
    var options = [
      //今日流量趋势
      {
        title: {
          text: '最近流量趋势',
          x: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['', '']
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          data: [{ $recent_visit_hour_str }]
        }],
        yAxis: [{
          type: 'value'
        }],
        series: [{
          name: 'PV',
          type: 'line',
          smooth: true,
          itemStyle: { normal: { areaStyle: { type: 'default' } } },
          data: [{ $recent_visit_pv_nums_str }]
        }, {
          name: 'UV',
          type: 'line',
          smooth: true,
          itemStyle: { normal: { areaStyle: { type: 'default' } } },
          data: [{ $recent_visit_uv_nums_str }]
        }]
      },

      //访客浏览器分布
      {
        title: {
          text: '前台访客终端分布',
          x: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: "{a} <br/>{b} : {c} ({d}%)"
        },
        legend: {
          orient: 'vertical',
          x: 'left',
          data: [{ $terminal_name_str }]
        },
        series: [{
          name: '访问来源',
          type: 'pie',
          radius: '55%',
          center: ['50%', '50%'],
          data: [
            { $terminal_visit_str }
          ]
        }]
      },

      //新增的用户量
      {
        title: {
          text: '最近一周新增的用户量',
          x: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: { //提示框
          trigger: 'axis',
          formatter: "{b}<br>新增用户：{c}"
        },
        xAxis: [{ //X轴
          type: 'category',
          data: [{ $recent_weeks_str }]
        }],
        yAxis: [{  //Y轴
          type: 'value'
        }],
        series: [{ //内容
          type: 'line',
          data: [{ $recent_weeks_member_str }],
        }]
      }
    ]

  </script>



  <script>
//view.error('请求视图文件异常，状态：'+ e.status);
  </script>


</body>

</html>
