<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>{$cn_webconfig['titlecn']|default="后台管理系统"}</title>
  {include file="public/iframeheader"/}

</head>

<body class="layui-layout-body">

  {include file="public/top" }

  {include file="public/leftnav" }

  {include file="public/biaoqian" }




  <!-- 主体内容 -->
  <div class="layui-body" id="LAY_app_body">
    <div class="layadmin-tabsbody-item layui-show">
      {if condition="$basekeynum eq '平台'"}

      {elseif condition="session('cn_login_flag') eq 'self'" /}
      <iframe src="{:url('Index/console')}" frameborder="0" class="layadmin-iframe"></iframe>
      {elseif condition="session('cn_login_flag') eq 'lzt' and !empty(session('cn_accountinfo')['certkeynum'])" /}
      <iframe src="http://vip.lizengtong.com/member/service.aspx?CertKeyNum={:session('cn_accountinfo')['certkeynum']}"
        frameborder="0" class="layadmin-iframe"></iframe>
      {else /}
      <iframe src="{:url('Index/console')}" frameborder="0" class="layadmin-iframe"></iframe>
      {/if}

    </div>
  </div>



  <!-- 辅助元素，一般用于移动设备下遮罩 -->
  <div class="layadmin-body-shade" layadmin-event="shade"></div>
  </div>
  </div>
  <script>
    layui.config({
      base: '__STATIC__/admin/' //静态资源所在路径
    }).extend({
      index: 'lib/index' //主入口模块
    }).use('index');
  </script>
  <script>
    //退出系统
    $('#logout').on('click', function () {
      layer.confirm('是否确定退出系统？', {
        btn: ['是', '否'], //按钮
        icon: 2,
      },
        function () {
          //发出ajax请求清除session
          $.post("{:url('Cn/logout')}", {}, function (data) {
            if (data.sta == 1) {
              location.href = "{:url('Login/index')}";
            } else {
              layer.msg(data.msg);
            }
          }, "json");


        });
    });

    //清除redis数据缓存
    $("[link=redis_cache_clear]").on('click', function () {
      layer.confirm('确定要清理redis缓存吗？', {
        btn: ['是', '否'], //按钮
        icon: 2,
      },
        function () {
          //发出ajax请求清除redis数据缓存
          $.post("{:url('Plat/redis_cache_clear')}", {}, function (data) {
            if (data.sta == 1) {
              layer.msg(data.msg);
              setInterval(() => {
                //location.reload();
              }, 1500);

            } else {
              layer.msg(data.msg);
            }
          }, "json");

        });
    });
  </script>
</body>

</html>
