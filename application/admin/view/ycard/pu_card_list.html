<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
    <br />
    <div style="margin-left: 20px;">
        <div class="page-content">
            <div class="demoTable   layui-form">
                <label class="layui-form-label">查询条件：</label>
                <form class="layui-inline">
                    <select name='field' class='form-control' lay-filter="test">
                        <option value="">全部</option>
                        <option value="customer_name">客户</option>
                        <option value="cardnum">卡号</option>
                        <option value="sale_name">销售员</option>
                    </select>
                </form>
                <div class='layui-inline'>
                    <input class="layui-input" name="keyword" id="reload" autocomplete="off">
                </div>
                <label class="layui-inline">销售单时间：</label>
                <div class='layui-inline' id="time">
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="开始时间" id="start_time_2" name="start_time_2"
                            autocomplete="off">
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="截止时间" id="end_time_2" name="end_time_2"
                            autocomplete="off">
                    </div>
                </div>
                <button class="layui-btn  layui-btn-sm" onclick="get_table()">搜索</button>
                <button class="layui-btn  layui-btn-sm" onclick="export_card()">导出主表</button>
                <button class="layui-btn  layui-btn-sm" onclick="export_card_son()">导出子表</button>
                <button class="layui-btn  layui-btn-sm" onclick="export_card_num()">导出卡号</button>
            </div>


            <table class="layui-hide" id="demo" lay-filter="demo">
            </table>
            <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-sm" lay-event="select">查看销售单</a>
            <a class="layui-btn layui-btn-sm" lay-event="select_card">查看卡号</a>
            <!-- <a class="layui-btn layui-btn-sm" lay-event="select_finance">查看收支记录</a> -->
            <!-- <a class="layui-btn layui-btn-sm" lay-event='downlod'>下载</a> -->
        </script>
        </div>
    </div>

</body>

</html>
<script type="text/javascript">
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#end_time_2' //指定元素
        });
        laydate.render({
            elem: '#start_time_2' //指定元素
        });

    });
    layui.use(['layer', 'jquery', 'form'], function () {
        var form = layui.form;
        // form.on('select(test)', function (data) {
        //     var type = data.value;
        //     //console.log(type);
        //     if (type == 'operator_time') {
        //         $("[name=keyword]").css('display', 'none');
        //         $('#time').css('display', 'inline-block')
        //     }
        //     else {
        //         $("#time").css('display', 'none');
        //         $("[name=keyword]").show();
        //     }
        // });
    })
    get_table();

    function get_table() {
        var field = $("[name=field]").val();
        var keyword = $("[name=keyword]").val();
        var start_time_2 = $("[name=start_time_2]").val();
        var end_time_2 = $("[name=end_time_2]").val();
        layui.use('table', function () {
            var table = layui.table;
            //方法级渲染
            table.render({
                elem: '#demo'
                , url: "{:url('ajax_pucard_list')}?field=" + field + "&keyword=" + keyword + "&start_time_2=" + start_time_2 + "&end_time_2="
                    + end_time_2
                , cols: [[
                    { type: 'numbers', title: '序号' }
                    , { field: 'pici_number', title: '销售单号' }
                    , { field: 'opeartor', title: '操作人' }
                    , { field: 'operator_time', title: '操作时间' }
                    , { field: 'customer_name', title: '客户' }
                    , { field: 'sale_name', title: '销售' }
                    , { field: 'all_cardnum_count', title: '销售卡号总数' }
                    , { field: 'all_price', title: '销售单总额' }
                    , { field: 'begin_time', title: '可兑开始时间' }
                    , { field: 'end_time', title: '可兑结束时间' }
                    , { field: 'uncleared_money', title: '未结清' }
                    , { field: 'remark', title: '备注' }
                    // , { field: 'content2', title: '开卡备注' }
                    , { fixed: 'right', title: "操作", align: 'center', toolbar: '#barDemo', width: 300 }
                ]]
                , id: 'testReload'
                , page: true
            });
            table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                var data = obj.data //获得当前行数据
                    , layEvent = obj.event; //获得 lay-event 对应的值
                var id = data.id;
                if (layEvent === 'select') {
                    layer.open({
                        type: 2,
                        title: "查看销售单",
                        content: "{:url('pu_card_select')}?id=" + id,
                        area: ["99%", "99%"]
                    })
                } else if (layEvent === 'select_card') {
                    layer.open({
                        type: 2,
                        title: "查看卡号",
                        content: "{:url('cardstatus')}?pu_id=" + id,
                        area: ["99%", "99%"]
                    })
                } else if (layEvent === "downlod") {
                    window.location.href = "{:url('download_cardbatch2')}?id=" + id + "&action=pu";
                } else if (layEvent === 'select_finance') {
                    layer.open({
                        type: 2,
                        title: "查看收支记录",
                        content: "{:url('Finance/get_list_by_puno')}?no=" + data.pici_number,
                        area: ["99%", "99%"]
                    });
                }
            });

            var $ = layui.$, active = {
                reload: function () {
                    var field = $("[name=field]").val();
                    var keyword = $("[name=keyword]").val();
                    var start_time_2 = $("[name=start_time_2]").val();
                    var end_time_2 = $("[name=end_time_2]").val();
                    //执行重载
                    table.reload('testReload', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        }
                        , where: {
                            field: field,
                            keyword: keyword,
                            start_time_2: start_time_2,
                            end_time_2: end_time_2

                        }
                    });
                },
            };

            $('.demoTable .layui-btn').on('click', function () {
                var type = $(this).data('type');
                active[type] ? active[type].call(this) : '';
            });
        });
    }

    function createTime(v) {
        var v = v * 1000; //js的时间戳要*1000
        var date = new Date(v);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? '0' + m : m;
        var d = date.getDate();
        d = d < 10 ? ("0" + d) : d;
        var h = date.getHours();
        h = h < 10 ? ("0" + h) : h;
        var M = date.getMinutes();
        M = M < 10 ? ("0" + M) : M;
        var S = date.getSeconds();
        S = S < 10 ? ("0" + S) : S;
        var str = y + "-" + m + "-" + d + " " + h + ":" + M + ":" + S;
        return str;
    }

    function export_card() {
        window.location.href = "{:url('export_pu_card_list')}";
    }
    function export_card_son() {
        window.location.href = "{:url('export_pu_card_list_son')}";
    }

    function export_card_num() {
        window.location.href = "{:url('export_pu_card_list_cardnum')}";
    }
</script>