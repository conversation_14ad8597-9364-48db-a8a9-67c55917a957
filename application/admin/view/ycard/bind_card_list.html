<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
<br/>
<div style="margin-left: 20px;">
    <div class="page-content">

        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
    </div>

</div>

</body>

</html>
<script type="text/javascript">

    layui.use('table', function () {

        var table = layui.table;
        //方法级渲染
        table.render({
            elem: '#demo'
            , url: "{:url('ajax_bind_card_list')}?member_id=" + {$member_id}
            , cols: [[
                {type: 'numbers', title: '序号'}
                , {field: 'cardnum', title: '卡号'}
                , {field: 'bind_time', title: '绑定时间'}
                , {field: 'status', title: '状态'}
                , {field: 'kai_money', title: '面值'}
                , {field: 'yu_money', title: '余额'}
            ]]
            , id: 'testReload'
            , page: true
        });

        var $ = layui.$, active = {
            reload: function () {
                //执行重载
                table.reload('testReload', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    , where: {
                    }
                });
            },
        };

    })


</script>
