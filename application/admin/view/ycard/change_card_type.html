<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>修改储值卡等级</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__LAYUI__/css/layui.css" media="all">
    <style>
        .layui-form-label {
            width: 120px;
        }
        .layui-input-block {
            margin-left: 150px;
        }
        .preview-table {
            margin-top: 20px;
        }
        .card-info {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .card-info .info-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 5px;
        }
        .card-info .info-item label {
            font-weight: bold;
            color: #333;
        }
    </style>
</head>
<body>
<div class="layui-container" style="padding: 20px;">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>修改储值卡等级</h3>
        </div>
        <div class="layui-card-body">
            <form class="layui-form" action="">
                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">选择客户</label>
                            <div class="layui-input-block">
                                <select name="customer_id" lay-verify="required" lay-search="">
                                    <option value="">请选择客户</option>
                                    {volist name="customer_list" id="customer"}
                                    <option value="{$customer.id}">{$customer.name}</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">新卡型等级</label>
                            <div class="layui-input-block">
                                <select name="new_cardtype_id" lay-verify="required" lay-search="">
                                    <option value="">请选择新的卡型</option>
                                    {volist name="cardtype_list" id="cardtype"}
                                    <option value="{$cardtype.id}">{$cardtype.name}</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">销售单号</label>
                            <div class="layui-input-block">
                                <input type="text" name="sale_number" placeholder="请输入销售单号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">开始卡号</label>
                            <div class="layui-input-block">
                                <input type="text" name="begin_cardnumber" placeholder="或者输入开始卡号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">结束卡号</label>
                            <div class="layui-input-block">
                                <input type="text" name="end_cardnumber" placeholder="输入结束卡号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">修改原因</label>
                    <div class="layui-input-block">
                        <textarea name="remarks" placeholder="请输入修改原因" class="layui-textarea"></textarea>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" type="button" id="preview-btn">生成预览</button>
                        <button class="layui-btn layui-btn-primary" type="reset">重置</button>
                    </div>
                </div>
            </form>

            <!-- 预览区域 -->
            <div id="preview-area" style="display: none;">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h4>预览信息</h4>
                    </div>
                    <div class="layui-card-body">
                        <div class="card-info">
                            <div class="info-item">
                                <label>总卡数：</label>
                                <span id="total-cards">0</span>
                            </div>
                            <div class="info-item">
                                <label>客户：</label>
                                <span id="customer-name">-</span>
                            </div>
                            <div class="info-item">
                                <label>新卡型：</label>
                                <span id="new-cardtype-name">-</span>
                            </div>
                        </div>
                        
                        <table class="layui-table" lay-skin="line">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>卡号</th>
                                    <th>当前卡型</th>
                                    <th>当前状态</th>
                                    <th>余额</th>
                                </tr>
                            </thead>
                            <tbody id="preview-tbody">
                            </tbody>
                        </table>
                        
                        <div class="layui-form-item" style="margin-top: 20px;">
                            <div class="layui-input-block">
                                <button class="layui-btn layui-btn-normal" type="button" id="submit-btn">确认修改</button>
                                <button class="layui-btn layui-btn-primary" type="button" id="cancel-btn">取消</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__LAYUI__/layui.js"></script>
<script>
layui.use(['form', 'layer', 'table'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;
    
    var previewData = []; // 存储预览数据
    
    // 生成预览
    $('#preview-btn').on('click', function(){
        var formData = {
            customer_id: $('select[name="customer_id"]').val(),
            sale_number: $('input[name="sale_number"]').val(),
            begin_cardnumber: $('input[name="begin_cardnumber"]').val(),
            end_cardnumber: $('input[name="end_cardnumber"]').val()
        };
        
        if (!formData.customer_id) {
            layer.msg('请选择客户');
            return;
        }
        
        if (!formData.sale_number && (!formData.begin_cardnumber || !formData.end_cardnumber)) {
            layer.msg('请输入销售单号或卡号范围');
            return;
        }
        
        var loadIndex = layer.load();
        
        $.post('/admin/ycard/get_card_type_change_preview', formData, function(res){
            layer.close(loadIndex);
            if (res.sta == 0) {
                previewData = res.data;
                showPreview(res.data);
            } else {
                layer.msg(res.msg);
            }
        }, 'json');
    });
    
    // 显示预览
    function showPreview(data) {
        $('#total-cards').text(data.length);
        $('#customer-name').text($('select[name="customer_id"] option:selected').text());
        $('#new-cardtype-name').text($('select[name="new_cardtype_id"] option:selected').text());
        
        var tbody = '';
        $.each(data, function(index, item){
            tbody += '<tr>';
            tbody += '<td>' + (index + 1) + '</td>';
            tbody += '<td>' + item.cardnum + '</td>';
            tbody += '<td>' + (item.cardtype_name || '未设置') + '</td>';
            tbody += '<td>' + item.status + '</td>';
            tbody += '<td>￥' + (item.yu_money || '0.00') + '</td>';
            tbody += '</tr>';
        });
        
        $('#preview-tbody').html(tbody);
        $('#preview-area').show();
    }
    
    // 确认修改
    $('#submit-btn').on('click', function(){
        if (previewData.length == 0) {
            layer.msg('请先生成预览');
            return;
        }
        
        var new_cardtype_id = $('select[name="new_cardtype_id"]').val();
        if (!new_cardtype_id) {
            layer.msg('请选择新的卡型');
            return;
        }
        
        var cardNums = [];
        $.each(previewData, function(index, item){
            cardNums.push(item.cardnum);
        });
        
        var submitData = {
            customer_id: $('select[name="customer_id"]').val(),
            new_cardtype_id: new_cardtype_id,
            card_nums: cardNums.join(','),
            remarks: $('textarea[name="remarks"]').val()
        };
        
        layer.confirm('确认要修改这 ' + previewData.length + ' 张卡的等级吗？', function(confirmIndex){
            var loadIndex = layer.load();
            
            $.post('/admin/ycard/ajax_change_card_type', submitData, function(res){
                layer.close(loadIndex);
                layer.close(confirmIndex);
                
                if (res.sta == 0) {
                    layer.msg('修改成功', {icon: 1}, function(){
                        location.reload();
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, 'json');
        });
    });
    
    // 取消
    $('#cancel-btn').on('click', function(){
        $('#preview-area').hide();
        previewData = [];
    });
    
    // 表单渲染
    form.render();
});
</script>
</body>
</html> 