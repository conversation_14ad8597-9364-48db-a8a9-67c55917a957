<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
<br/>
<div style="width: 800px;float: left">
    <form class="layui-form" action="">

        <div class="layui-form-item">
            <label class="layui-form-label">选择客户</label>
            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
            <div class="layui-input-inline" style="width: 400px;">
                <select name="customer_id" lay-verify="required" lay-search>
                    <option value="">请选择</option>
                    {foreach name='customer_list' key='key' item='value'}
                    <option value="{$value['id']}"> {$value["name"]}</option>
                    {/foreach}
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">批次名称</label>
            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>
            <div class="layui-input-inline" style="width: 400px;">
                <input type="text" name="piciname" required lay-verify="required" autocomplete="off"
                       class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">销售单号</label>
            <div class="layui-input-inline" style="width: 400px;">
                <input type="text" name="sale_number" value="" placeholder="" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">卡号范围</label>
                <div class="layui-input-inline" style="width: 200px;">
                    <input type="text" name="begin_cardnumber" placeholder="起始卡号" autocomplete="off"
                           class="layui-input">
                </div>
                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline" style="width: 200px;">
                    <input type="text" name="end_cardnumber" placeholder="结束卡号" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
            <input type='button' class="layui-btn" value="生成预览" id='yulan'/>
        </div>

        <div id="template-table-div" style="padding-bottom: 10px;">

            <div class="layui-inline">
                <label class="layui-form-label">预览数据(选填)</label>
                <div class="layui-input-block">
                <textarea id="preview_textarea" name="add_cardnum" cols="50" rows="15" placeholder=""
                          class="layui-textarea"></textarea>
                </div>
            </div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label"></label>
            <div class="layui-input-block" style="width:40%;">
                当前总数：<span id="count" style="color: red">0</span>
            </div>
        </div>

        <!--        <div>-->
        <!--            <table class='layui-table' style='width: 150px;margin-left: 110px;'>-->
        <!--                <tbody id='cardlist'>-->
        <!--                </tbody>-->
        <!--            </table>-->
        <!--        </div>-->

        <!--        <div class="layui-form-item">-->
        <!--            <label class="layui-form-label">退货明细</label>-->
        <!--            <div class="layui-input-inline" style="width: auto;">-->
        <!--                <table class="layui-table">-->
        <!--                    <colgroup>-->
        <!--                        <col width="">-->
        <!--                        <col width="">-->
        <!--                        <col width="">-->
        <!--                        <col>-->
        <!--                    </colgroup>-->
        <!--                    <thead>-->
        <!--                        <tr>-->
        <!--                            <th>卡型名称</th>-->
        <!--                            <th>市场价</th>-->
        <!--                            <th>数量</th>-->
        <!--                            <th>卡号</th>-->
        <!--                            <th>合计</th>-->
        <!--                        </tr>-->
        <!--                    </thead>-->
        <!--                    <tbody id="tbody">-->

        <!--                    </tbody>-->
        <!--                </table>-->

        <!--            </div>-->
        <!--            <div class="layui-input-inline layui-input-company"></div>-->
        <!--            <div class="layui-form-mid layui-word-aux"></div>-->
        <!--        </div>-->

        <div class="layui-form-item">
            <label class="layui-form-label">实退金额</label>
            <div class="layui-input-inline" style="width: 400px;">
                <input type="number" name="real_tui_money" value="" placeholder="" autocomplete="off"
                       class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注说明</label>
            <div class="layui-input-inline" style="width: 400px;">
                <textarea name="content" placeholder="请输入内容" class="layui-textarea"></textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn  layui-btn-sm" lay-submit lay-filter="formDemo">确认退卡</button>
            </div>
        </div>
    </form>
</div>

<div class="layui-card" style="width: 450px;margin:15px;float:left;">
    <table id="templateTable" lay-filter="templateTable"></table>
</div>

<script>
    //Demo
    layui.use('form', function () {
        var form = layui.form;
        var upload = layui.upload;
        var ischeck = false;
        //监听提交
        form.on('submit(formDemo)', function (data) {
            //console.log(data);return false;
            var table_data = layui.table.cache['templateTable'];
            var ids = '';
            for (const key in table_data) {
                ids += table_data[key].cardnum + ','
            }
            ids = ids.slice(0, ids.length - 1);
            $('#card_nums').val(ids);
            data.field.cardnumberlist = ids;
            // if (ischeck == false) {
            //     layer.msg("请生成预览并确认无误");
            //     return false;
            // }
            // var cardnumberlist = "";
            // $(".cardnumber").each(function (i, n) {
            //     var cardnum = $(n).html();
            //     cardnumberlist += cardnum + ",";
            // });
            // if (cardnumberlist == '') {
            //     layer.msg("请输入并生成预览卡号");
            //     return false;
            // }
            var index = layer.load(0, {
                shade: [0.4, 'black'] //0.1透明度的白色背景
            });
            //console.log(cardnumberlist);return false;
            // data.field.cardnumberlist = cardnumberlist;
            // console.log(data.field);return false;
            $.post("{:url('ajax_tui_card')}", data.field, function (data) {
                //console.log(data);return false;
                if (data.sta == 1) {
                    layer.msg(data.msg, {time: 1500}, function () {
                        window.location.href = "{:url('tui_card_list')}";
                    });
                } else {
                    layer.msg(data.msg);
                }
            }, "json");
            return false;
        });


        $("#yulan").click(function () {

            var customer_id = $("[name=customer_id]").val();
            if (customer_id == '') {
                layer.msg("请选择客户");
                return false;
            }
            var begin_cardnumber = $("[name=begin_cardnumber]").val();
            var end_cardnumber = $("[name=end_cardnumber]").val();
            var sale_number = $("[name=sale_number]").val();
            if (sale_number == '') {
                if (begin_cardnumber == '') {
                    layer.msg("请输入开始卡号");
                    return false;
                }
                if (end_cardnumber == '') {
                    layer.msg("请输入结束卡号");
                    return false;
                }
            }
            $.post("{:url('get_tui_card')}", {
                customer_id: customer_id,
                begin_cardnumber: begin_cardnumber, end_cardnumber: end_cardnumber,
                sale_number: sale_number
            }, function (data) {
                ischeck = true;
                if (data.sta == '1') {
                    var cardnum = '';
                    for (key in data.list) {
                        cardnum += data.list[key] + "\n";
                    }
                    layer.msg('生成卡号', {icon: 6});
                    $("#cardlist").html("");
                    var list = data.list;

                    popup(list);

                    $("[name=real_tui_money]").val(data.all_money);


                    // var html = "";
                    // $.each(list, function (i, n) {
                    //     i = i + 1;
                    //     if (i % 10 == 1) {
                    //         html += "<tr>";
                    //     }
                    //     html += "<td class='cardnumber'>" + n + "</td>";
                    //     if (i % 10 == 0) {
                    //         html += "</tr>";
                    //     }
                    // })
                    // $("#cardlist").html(html);
                    // var html2 = "";
                    // $.each(data.cardtype, function (i, n) {
                    //     html2 += "<tr>";
                    //     html2 += "<td>" + n.info.name + "</td>";
                    //     html2 += "<td>" + n.info.market_price + "</td>";
                    //     html2 += "<td>" + n.num + "</td>";
                    //     html2 += "<td>" + n.cardnum + "</td>";
                    //     html2 += "<td>" + n.all_money + "</td>";
                    //     html2 += "</tr>";
                    // })
                    // $("#tbody").html(html2)
                    form.render();
                } else {
                    var errormsg = data.msg;
                    layer.alert(errormsg, {icon: 2});
                    $("#cardlist").html("");
                    var list = data.list;
                    var html = "";
                    $.each(list, function (i, n) {
                        i = i + 1;
                        if (i % 10 == 1) {
                            html += "<tr>";
                        }
                        html += "<td class='cardnumber'>" + n + "</td>";
                        if (i % 10 == 0) {
                            html += "</tr>";
                        }
                    })
                    $("#cardlist").html(html);
                    $("[name=real_tui_money]").val(data.all_money);
                    var html2 = "";
                    $.each(data.cardtype, function (i, n) {
                        html2 += "<tr>";
                        html2 += "<td>" + n.info.name + "</td>";
                        html2 += "<td>" + n.info.market_price + "</td>";
                        html2 += "<td>" + n.num + "</td>";
                        html2 += "<td>" + n.cardnum + "</td>";
                        html2 += "<td>" + n.all_money + "</td>";
                        html2 += "</tr>";
                    })
                    $("#tbody").html(html2)
                    form.render();
                }
            }, "json");
        })
    });

    layui.use('table', function () {

        var table = layui.table;

        table.render({
            elem: '#templateTable'
            , height: 400
            , data: []
            , cols: [[ //设置数据表格表头
                {field: 'cardnum', title: '卡号', width: '40%', align: "center"}
                , {field: 'status', title: '状态', width: '30%', align: "center"}
                , {
                    field: 'id', title: '是否可以更改', align: "center", width: '30%', templet: function (item) {
                        if (item.status == '已销售' || item.status == '已开卡' ){
                            return '<button type="button" class="layui-btn layui-btn-normal layui-btn-sm">可以</button>';
                        } else {
                            return '<button type="button" class="layui-btn layui-btn-danger layui-btn-sm">不可以</button>';
                        }
                    }
                }
            ]],
            maxHeight: 600,
            limit: Number.MAX_VALUE
        });

    });

    function popup(result) {
        var cardnum = $('textarea[name="add_cardnum"]').val();

        var alter_cardnunm = '';

        for (key in result) {
            alter_cardnunm += result[key] + "\n";
        }

        cardnum = alter_cardnunm + cardnum;
        cardnum = jsexplode(cardnum, "\n");
        cardnum = unique(cardnum);
        var alter_cardnum1 = '';
        for (key in cardnum) {
            alter_cardnum1 += cardnum[key] + "\n";
        }
        console.log(alter_cardnum1)

        $('textarea[name="add_cardnum"]').val(alter_cardnum1);
        $('textarea[name="add_cardnum"]').change();

    }

    function jsexplode(str, delimiter) {
        var arr = [];
        var start = 0;
        var end = str.indexOf(delimiter);
        while (end !== -1) {
            arr.push(str.substring(start, end));
            start = end + delimiter.length;
            end = str.indexOf(delimiter, start);
        }
        arr.push(str.substring(start));
        return arr;
    }

    function unique(arr) {
        return Array.from(new Set(arr));
    }

    $("#preview_textarea").change(function () {
        // 提交卡号，增加预览数据
        var cardnum = $('textarea[name="add_cardnum"]').val();
        var customer_id = $("[name='customer_id']").val();
        var type = $("input[name='type']:checked").val();
        var count = $('#count').html();
        count = jsexplode(cardnum.trim(), "\n").length;
        $('#count').html(count);
        if (customer_id == '') {
            layer.msg("请选择客户");
            return false;
        }
        if (type == '') {
            leyer.msg('请先选择更改状态');
            return false;
        }
        var request_data = {
            cardnum: cardnum,
            type: 'bind_blessing_archives',
            customer_id: customer_id,
            status: type,
        };

        $.post("{:url('ajax_add_preview_info')}"
            , request_data
            , function (data) {
                if (data.code == 0) {
                    layui.use('table', function () {
                        var table_data = layui.table.cache['templateTable'];
                        table_data = data.data;
                        layui.table.reload("templateTable", {
                            // 将新数据 dataBak  重新载入ID为 titleId 的表格 实现新增一行效果
                            data: table_data
                        });
                    });
                    layer.msg('请求成功');
                } else {
                    layer.msg(data.msg);
                }
            }, "json");
    });

    layui.use('laydate', function () {
        var laydate = layui.laydate;

        //执行一个laydate实例
        laydate.render({
            elem: '#begin_start' //指定元素
        });
        laydate.render({
            elem: '#end_time' //指定元素
        });

    });

</script>

</body>

</html>