<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
    <br />
    <div style="margin-left: 20px;">
        <div class="page-content">
            <div class="sk">总面值：<span id="kai_money"></span></div>
            <div class="sk">总消费：<span id="consume"></span></div>
            <div class="sk">总余额：<span id="yu_money"></span></div>
            <div class="demoTable   layui-form">
                <label class="layui-form-label">查询条件：</label>
                <form class="layui-inline">
                    <select name='field' class='form-control' lay-filter="test">
                        <option value="">全部</option>
                        <!-- <option value="member_phone">会员手机号</option> -->
                        <option value="cardnum">卡号</option>
                        <option value="gq_card">过期</option>
                        <!-- <option value="status">卡号状态</option> -->
                        <option value="end_dui">到期时间</option>
                        <option value="market">销售员</option>
                        <option value="customer">客户</option>
                        <option value="card_type">卡型</option>
                    </select>
                </form>
                <div class='layui-inline card_search' style="display:none">
                    <input style="height:100%;padding:10px;border:1px solid #e6e6e6" name="start_card" value=""
                        placeholder="请输入开始卡号">
                    <span>-</span>
                    <input style="height:100%;padding:10px;border:1px solid #e6e6e6" name="end_card" value=""
                        placeholder="请输入结束卡号">
                </div>
                <div class='layui-inline'>
                    <input class="layui-input" name="keyword" id="reload" autocomplete="off">
                </div>
                <div class='layui-inline' id="is_expire" style="display: none">
                    <select name="is_expire">
                        <option value="0">未过期</option>
                        <option value="1">已过期</option>
                    </select>
                </div>
                <!-- <div class='layui-inline' id="status_type" style="display: none">
                    <select name="status_type">
                        <option value="5">未铺卡</option>
                        <option value="0">未开卡</option>
                        <option value="1">已开卡</option>
                        <option value="2">已关卡</option>
                        <option value="3">已使用</option>
                        <option value="6">已退卡</option>
                    </select>
                </div> -->
                <div class='layui-inline' style="display: none" id="time">
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="开始时间" id="start_time_2" name="start_time_2">
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="截止时间" id="end_time_2" name="end_time_2">
                    </div>
                </div>

                <form class="layui-inline">
                    <option value="status">卡号状态：</option>
                </form>
                <div class='layui-inline' id="status_type">
                    <select name="status_type">
                        <option value="">全部</option>
                        <option value="5">未销售</option>
                        <option value="1">已销售</option>
                        <option value="3">已开卡</option>
                        <option value="2">已退卡</option>
                        <option value="4">已关卡</option>
                        <option value="-1">已废卡</option>
                    </select>
                </div>

                <button class="layui-btn  layui-btn-sm" onclick="get_table()">搜索</button>
                <button class="layui-btn  layui-btn-sm" onclick="export_card()">导出</button>
            </div>


            <table class="layui-hide" id="demo" lay-filter="demo">
            </table>
            <script type="text/html" id="barDemo">
                
                <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="usage_record">卡使用记录</a>
                <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit_level">修改等级</a>
                <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="extend_time">延期</a>
                <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="clear_balance">余额清零</a>
<!--            <a class="layui-btn layui-btn-sm" lay-event="show_dingdan">关联订单</a>-->
            </script>
        </div>
    </div>

</body>

</html>
<script type="text/javascript">
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#end_time_2' //指定元素
        });
        laydate.render({
            elem: '#start_time_2' //指定元素
        });

    });
    layui.use(['layer', 'jquery', 'form'], function () {
        var form = layui.form;
        form.on('select(test)', function (data) {
            var type = data.value;
            //console.log(type);
            if (type == 'status') {
                $("[name=keyword]").css('display', 'none');
                $("#time").css('display', 'none');
                $(".card_search").css('display', 'none');
                $("#is_expire").css('display', 'none');
                $("#status_type").show();
            } else if (type == 'end_dui') {
                $("#status_type").css('display', 'none');
                $("[name=keyword]").css('display', 'none');
                $(".card_search").css('display', 'none');
                $("#is_expire").css('display', 'none');
                $('#time').css('display', 'inline-block')
            } else if (type == 'cardnum') {
                $("[name=keyword]").css('display', 'none');
                $("#time").css('display', 'none');
                $("#status_type").css('display', 'none');
                $("#is_expire").css('display', 'none');
                $(".card_search").show();
            } else if (type == 'gq_card') {
                $("[name=keyword]").css('display', 'none');
                $("#time").css('display', 'none');
                $(".card_search").css('display', 'none');
                $("#status_type").css('display', 'none');
                $("#is_expire").show();
            }
            else {
                $("#status_type").css('display', 'none');
                $(".card_search").css('display', 'none');
                $("#time").css('display', 'none');
                $("#is_expire").css('display', 'none');
                $("[name=keyword]").show();
            }
            // console.log(type);

        });
    })
    get_table();

    function get_table() {
        var field = $("[name=field]").val();
        var keyword = $("[name=keyword]").val();
        var start_card = $("[name=start_card]").val();
        var end_card = $("[name=end_card]").val();
        var start_time_2 = $("[name=start_time_2]").val();
        //alert(end_card);
        if (field == 'status') {
            var keyword = $("[name=status_type]").val();
        } else if (field == 'end_dui') {
            var keyword = $("[name=end_time_2]").val();
        } else if (field == 'gq_card') {
            var keyword = $("[name=is_expire]").val();
        }
        else {
            var keyword = $("[name=keyword]").val();
        }
        var pu_id = "{$pu_id}";
        var opend_id = "{$opend_id}";
        var close_id = "{$close_id}";
        var tui_id = "{$tui_id}";
        var status_type = $("[name=status_type]").val();
        // var field = $("[name=field]").val();
        // var keyword = $("[name=keyword]").val();
        layui.use('table', function () {
            var table = layui.table;
            //方法级渲染
            table.render({
                elem: '#demo'
                , url: "{:url('ajax_selectcard')}?field=" + field + "&keyword=" + keyword + "&end_card=" + end_card
                    + "&start_card=" + start_card + "&start_time_2=" + start_time_2 + "&pu_id=" + pu_id
                    + "&opend_id=" + opend_id + "&close_id=" + close_id + "&tui_id=" + tui_id + "&status_type=" + status_type
                , filter: 'demo' // 添加过滤器，使得排序事件能被正确捕获
                , cols: [[
                    { type: 'numbers', title: '序号', width: 60 }
                    // , { field: 'market', title: '销售员' }
                    , { field: 'cardnum', title: '卡号', width: 120 }
                    , { field: 'card_level', title: '卡等级', width: 100 }
                    , { field: 'status', title: '卡号状态', width: 100 }
                    , { field: 'sale_name', title: '销售员', width: 100 }
                    , { field: 'customer_name', title: '客户', width: 120 }
                    , { field: 'cardtype_name', title: '卡型', width: 100 }

                    , { field: 'begin_dui', title: '起兑时间', width: 160, sort: true }
                    , { field: 'end_dui', title: '止兑时间', width: 160, sort: true }
                    , { field: 'piciname', title: '批次名称', width: 120 }
                    , { field: 'uid', title: '最后使用会员', width: 120 }
                    , { field: 'kai_money', title: '面值', width: 100, sort: true }
                    , { field: 'consume', title: '已消费', width: 100, sort: true }
                    , { field: 'yu_money', title: '余额', width: 100, sort: true }
                    , { field: 'addtime', title: '生成时间', width: 200 }
                    , { field: 'operator', title: '生成时操作人', width: 120 }
                    , { field: 'content', title: '生成时备注', width: 150 }
                    // , {
                    //     field: 'qrcode', title: '二维码', width: 150, templet: function (item) {
                    //         return '<img onclick="showimg(this);"   style=" width: 50%;height:100%" src="' + item.qrcode + '">';
                    //     }
                    // }
                    // , { field: 'content2', title: '开卡备注' }
                    , { fixed: 'right', title: "操作", align: 'center', toolbar: '#barDemo', width: 240}
                ]]
                , id: 'testReload'
                , page: true
                , done: function (res, curr, count) {
                    $("#kai_money").text(res.kai_money);
                    $("#yu_money").text(res.yu_money);
                    $("#consume").text(res.total_consume);
                }
                , request: {
                    pageName: 'page' //页码的参数名称，默认：page
                    , limitName: 'limit' //每页数据量的参数名，默认：limit
                }
                , response: {
                    statusName: 'code' //规定数据状态的字段名称，默认：code
                    , statusCode: 0 //规定成功的状态码，默认：0
                    , msgName: 'msg' //规定状态信息的字段名称，默认：msg
                    , countName: 'count' //规定数据总数的字段名称，默认：count
                    , dataName: 'data' //规定数据列表的字段名称，默认：data
                }
            });

            layui.use("table", function () {
                var table = layui.table;
                var form = layui.form;
                
                // 添加排序事件监听
                table.on('sort(demo)', function(obj) {
                    console.log(obj.field); // 当前排序的字段名
                    console.log(obj.type); // 当前排序类型：desc（降序）、asc（升序）、null（空对象，默认排序）
                    
                    // 重载表格，执行后端排序
                    table.reload('testReload', {
                        initSort: obj, // 记录初始排序，如果不设定，则重载时丢失排序状态
                        where: { // 请求参数
                            fields: obj.field, // 排序字段
                            order: obj.type // 排序方式
                        }
                    });
                });
                
                table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                    var data = obj.data //获得当前行数据
                        , layEvent = obj.event; //获得 lay-event 对应的值
                    var id = data.id;
                    if (layEvent === 'show_dingdan') {
                        layer.open({
                            type: 2,
                            title: "卡号：" + data.cardnum,
                            content: "{:url('Order/order_list')}?cardnum=" + data.cardnum,
                            maxmin: true,
                            area: ["100%", "100%"]
                        })
                    } else if (layEvent === 'usage_record') {
                        // 查看卡使用记录
                        showCardUsageRecord(data);
                    } else if (layEvent === 'edit_level') {
                        // 修改卡等级
                        editCardLevel(data);
                    } else if (layEvent === 'extend_time') {
                        // 延期操作
                        extendCardTime(data);
                    } else if (layEvent === 'clear_balance') {
                        // 余额清零操作
                        clearCardBalance(data);
                    }
                });

            })

            var $ = layui.$, active = {
                reload: function () {
                    var piciname = $("[name=piciname]").val();
                    var cardnum = $("[name=cardnum]").val();
                    var status = $("[name=status]").val();
                    var name = $("[name=operator]").val();
                    //执行重载
                    table.reload('testReload', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        }
                        , where: {
                            cardnum: cardnum,
                            status: status,
                            piciname: piciname,
                            operator: operator

                        }
                    });
                },
            };

            $('.demoTable .layui-btn').on('click', function () {
                var type = $(this).data('type');
                active[type] ? active[type].call(this) : '';
            });
        });
    }

    function createTime(v) {
        var v = v * 1000; //js的时间戳要*1000
        var date = new Date(v);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? '0' + m : m;
        var d = date.getDate();
        d = d < 10 ? ("0" + d) : d;
        var h = date.getHours();
        h = h < 10 ? ("0" + h) : h;
        var M = date.getMinutes();
        M = M < 10 ? ("0" + M) : M;
        var S = date.getSeconds();
        S = S < 10 ? ("0" + S) : S;
        var str = y + "-" + m + "-" + d + " " + h + ":" + M + ":" + S;
        return str;
    }

    function export_card() {
        window.location.href = "{:url('export_selectcard')}";
    }

    //点击放大图片
    function showimg(t) {
        var src = $(t).attr("src");
        if (src == '') {
            layer.msg("图为为空！");
            return false;
        }
        layer.open({
            type: 1,
            title: false,
            area: '516px',
            content: '<img style="display: inline-block; width: 100%; height: 100%;" src="' + src + '">'
        });
    }

    // 修改卡等级
    function editCardLevel(data) {
        // 首先获取可用的卡等级列表
        $.get("{:url('get_card_levels')}", function(res) {
            console.log(res.code)
            if (res.code == 0) {
                var levelOptions = '';
                for (var i = 0; i < res.data.length; i++) {
                    var selected = res.data[i].title === data.card_level ? 'selected' : '';
                    levelOptions += '<option value="' + res.data[i].id + '" ' + selected + '>' + res.data[i].title + '</option>';
                }
                
                var content = `
                    <div style="padding: 20px;" class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label">卡号:</label>
                            <div class="layui-input-block">
                                <input type="text" value="${data.cardnum}" readonly class="layui-input" style="background-color: #f5f5f5;">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">当前等级:</label>
                            <div class="layui-input-block">
                                <input type="text" value="${data.card_level || '无'}" readonly class="layui-input" style="background-color: #f5f5f5;">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">新等级:</label>
                            <div class="layui-input-block">
                                <select name="new_level" lay-filter="new_level" lay-search>
                                    <option value="">请选择新等级</option>
                                    ${levelOptions}
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">修改原因:</label>
                            <div class="layui-input-block">
                                <textarea name="reason" placeholder="请输入修改原因" class="layui-textarea"></textarea>
                            </div>
                        </div>
                    </div>
                `;
                
                layer.open({
                    type: 1,
                    title: '修改卡等级',
                    area: ['450px', '400px'],
                    content: content,
                    btn: ['确定修改', '取消'],
                    success: function(layero, index) {
                        // 重新渲染表单以应用layui样式
                        layui.form.render();
                    },
                    yes: function(index, layero) {
                        var newLevel = layero.find('select[name="new_level"]').val();
                        var reason = layero.find('textarea[name="reason"]').val();
                        
                        if (!newLevel) {
                            layer.msg('请选择新等级');
                            return;
                        }
                        // if (!reason.trim()) {
                        //     layer.msg('请输入修改原因');
                        //     return;
                        // }
                        
                        // 提交修改
                        $.post("{:url('update_card_level')}", {
                            card_id: data.id,
                            cardnum: data.cardnum,
                            new_level_id: newLevel,
                            reason: reason
                        }, function(res) {
                            if (res.code === 0) {
                                layer.msg('修改成功', {icon: 1});
                                layer.close(index);
                                get_table(); // 刷新表格
                            } else {
                                layer.msg(res.msg || '修改失败', {icon: 2});
                            }
                        }, 'json');
                    }
                });
            } else {
                layer.msg('获取卡等级列表失败');
            }
        }, 'json');
    }

    // 延期操作
    function extendCardTime(data) {
        var content = `
            <div style="padding: 20px;" class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">卡号:</label>
                    <div class="layui-input-block">
                        <input type="text" value="${data.cardnum}" readonly class="layui-input" style="background-color: #f5f5f5;">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">当前到期时间:</label>
                    <div class="layui-input-block">
                        <input type="text" value="${data.end_dui || '无'}" readonly class="layui-input" style="background-color: #f5f5f5;">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">新到期时间:</label>
                    <div class="layui-input-block">
                        <input type="text" name="new_end_time" id="new_end_time" placeholder="请选择新的到期时间" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">延期原因:</label>
                    <div class="layui-input-block">
                        <textarea name="reason" placeholder="请输入延期原因" class="layui-textarea"></textarea>
                    </div>
                </div>
            </div>
        `;
        
        layer.open({
            type: 1,
            title: '卡号延期',
            area: ['450px', '400px'],
            content: content,
            btn: ['确定延期', '取消'],
            success: function(layero, index) {
                // 初始化日期选择器
                layui.laydate.render({
                    elem: layero.find('#new_end_time')[0],
                    type: 'date'
                });
            },
            yes: function(index, layero) {
                var newEndTime = layero.find('input[name="new_end_time"]').val();
                var reason = layero.find('textarea[name="reason"]').val();
                
                if (!newEndTime) {
                    layer.msg('请选择新的到期时间');
                    return;
                }
                // if (!reason.trim()) {
                //     layer.msg('请输入延期原因');
                //     return;
                // }
                
                // 提交延期
                $.post("{:url('extend_card_time')}", {
                    card_id: data.id,
                    cardnum: data.cardnum,
                    new_end_time: newEndTime,
                    reason: reason
                }, function(res) {
                    if (res.code === 0) {
                        layer.msg('延期成功', {icon: 1});
                        layer.close(index);
                        get_table(); // 刷新表格
                    } else {
                        layer.msg(res.msg || '延期失败', {icon: 2});
                    }
                }, 'json');
            }
        });
    }

    // 余额清零操作
    function clearCardBalance(data) {
        var content = `
            <div style="padding: 20px;" class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">卡号:</label>
                    <div class="layui-input-block">
                        <input type="text" value="${data.cardnum}" readonly class="layui-input" style="background-color: #f5f5f5;">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">当前余额:</label>
                    <div class="layui-input-block">
                        <input type="text" value="${data.yu_money || '0'}" readonly class="layui-input" style="background-color: #f5f5f5;">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">清零原因:</label>
                    <div class="layui-input-block">
                        <textarea name="reason" placeholder="请输入余额清零原因" class="layui-textarea"></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <p style="color: #ff5722; font-size: 14px;">
                            <i class="layui-icon layui-icon-tips"></i>
                            注意：余额清零操作不可逆，请谨慎操作！
                        </p>
                    </div>
                </div>
            </div>
        `;
        
        layer.open({
            type: 1,
            title: '余额清零',
            area: ['450px', '350px'],
            content: content,
            btn: ['确定清零', '取消'],
            success: function(layero, index) {
                // 重新渲染表单以应用layui样式
                layui.form.render();
            },
            yes: function(index, layero) {
                var reason = layero.find('textarea[name="reason"]').val();
                
                if (!reason.trim()) {
                    layer.msg('请输入清零原因');
                    return;
                }
                
                // 二次确认
                layer.confirm('确定要将该卡余额清零吗？此操作不可撤销！', {
                    icon: 3,
                    title: '确认清零'
                }, function(confirmIndex) {
                    // 提交清零请求
                    $.post("{:url('clear_card_balance')}", {
                        card_id: data.id,
                        cardnum: data.cardnum,
                        reason: reason
                    }, function(res) {
                        if (res.code === 0) {
                            layer.msg('余额清零成功', {icon: 1});
                            layer.close(index);
                            layer.close(confirmIndex);
                            get_table(); // 刷新表格
                        } else {
                            layer.msg(res.msg || '余额清零失败', {icon: 2});
                        }
                    }, 'json');
                });
            }
        });
    }

    // 查看卡使用记录
    function showCardUsageRecord(data) {
        layer.open({
            type: 2,
            title: '卡使用记录 - ' + data.cardnum,
            content: "{:url('get_card_usage_record')}?card_id=" + data.id + "&cardnum=" + data.cardnum,
            area: ['90%', '80%'],
            maxmin: true
        });
    }
</script>
