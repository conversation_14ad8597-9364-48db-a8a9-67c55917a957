<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>储值卡等级修改详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__LAYUI__/css/layui.css" media="all">
</head>
<body>
<div class="layui-container" style="padding: 20px;">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>储值卡等级修改详情</h3>
        </div>
        <div class="layui-card-body">
            <!-- 数据表格 -->
            <table class="layui-hide" id="data-table" lay-filter="data-table"></table>
        </div>
    </div>
</div>

<script src="__LAYUI__/layui.js"></script>
<script>
layui.use(['table'], function(){
    var table = layui.table;
    var $ = layui.$;
    
    // 获取批次号
    var urlParams = new URLSearchParams(window.location.search);
    var no = urlParams.get('no') || '{$no}';
    
    // 数据表格
    table.render({
        elem: '#data-table',
        url: '/admin/ycard/ajax_card_type_change_detail',
        where: {no: no},
        page: true,
        cols: [[
            {type: 'numbers', title: '序号'},
            {field: 'cardnum', title: '卡号', width: 150},
            {field: 'old_cardtype_name', title: '原卡型', width: 150},
            {field: 'new_cardtype_name', title: '新卡型', width: 150},
            {field: 'yu_money', title: '余额', width: 100, templet: function(d){
                return '￥' + (d.yu_money || '0.00');
            }},
            {field: 'operator', title: '操作员', width: 120},
            {field: 'add_time', title: '操作时间', width: 180},
            {field: 'remarks', title: '备注', minWidth: 200}
        ]],
        request: {
            pageName: 'page',
            limitName: 'limit'
        },
        response: {
            statusName: 'sta',
            statusCode: 0,
            msgName: 'msg',
            countName: 'count',
            dataName: 'data'
        }
    });
});
</script>
</body>
</html> 