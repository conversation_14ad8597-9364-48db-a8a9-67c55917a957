<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
    <br />
    <div style="margin-left: 20px;">
        <div class="page-content">
            <div class="demoTable layui-form">
                <label class="layui-form-label">查询条件：</label>
                <form class="layui-inline">
                    <select name='field' class='form-control' lay-filter="test">
                        <option value="">全部</option>
                        <option value="custom">客户</option>
                        <option value="cardnum">卡号</option>
                        <option value="market">销售员</option>
                    </select>
                </form>
                <div class='layui-inline'>
                    <input class="layui-input" name="keyword" id="reload" autocomplete="off">
                </div>
                <label class="layui-inline">退卡时间：</label>
                <div class='layui-inline' id="time">
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="开始时间" id="start_time_2" name="start_time_2">
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="截止时间" id="end_time_2" name="end_time_2">
                    </div>
                </div>
                <button class="layui-btn  layui-btn-sm" data-type="reload">搜索</button>
                <button class="layui-btn  layui-btn-sm" onclick="export_card()">导出主表</button>
                <button class="layui-btn  layui-btn-sm" onclick="export_card_num()">导出卡号</button>
            </div>
            <table class="layui-hide" id="demo" lay-filter="demo">
            </table>
            <script type="text/html" id="barDemo">
                <a class="layui-btn layui-btn-sm" lay-event="select_card">查看卡号</a>
                <a class="layui-btn layui-btn-sm" lay-event="select_finance">查看收支记录</a>
                <!-- <a class="layui-btn layui-btn-sm" lay-event='downlod'>下载</a> -->
            </script>
        </div>
    </div>

</body>

</html>
<script type="text/javascript">
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#end_time_2' //指定元素
        });
        laydate.render({
            elem: '#start_time_2' //指定元素
        });

    });
    layui.use(['layer', 'jquery', 'form'], function () {
        var form = layui.form;
        // form.on('select(test)', function (data) {
        //     var type = data.value;
        //     //console.log(type);
        //     if (type == 'operator_time') {
        //         $("[name=keyword]").css('display', 'none');
        //         $('#time').css('display', 'inline-block')
        //     }
        //     else {
        //         $("#time").css('display', 'none');
        //         $("[name=keyword]").show();
        //     }
        // });
    })

    layui.use('table', function () {
        var table = layui.table;
        //方法级渲染
        table.render({
            elem: '#demo'
            , url: "{:url('tui_card_list')}?ajax=ajax"
            , cols: [[
                { type: 'numbers', title: '序号' }
                , { field: 'piciname', title: '批次名称' }
                , { field: 'pici_number', title: '批次号' }
                , { field: 'customer_name', title: '客户' }
                , { field: 'sale_name', title: '销售' }
                , { field: 'open_num', title: '退卡数量' }
                , { field: 'real_tui_money', title: '实退金额' }
                , { field: 'uncleared_money', title: '未结清' }
                , { field: 'opeartor', title: '操作人员' }
                , { field: 'operator_time', title: '操作时间' }
                , { field: 'remark', title: '备注' }
                , { fixed: 'right', title: "操作", align: 'center', toolbar: '#barDemo', width:210 }
            ]]
            , id: 'testReload'
            , page: true
        });
        table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            //console.log(obj);
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            // console.log(data); return false;
            var id = data.id;
            var piciname = data.piciname;
            if (layEvent === "downlod") {
                window.location.href = "{:url('download_cardbatch2')}?id=" + id + "&action=tui";;
            } else if (layEvent === 'select_card') {
                layer.open({
                    type: 2,
                    title: "查看卡号",
                    content: "{:url('cardstatus')}?tui_id=" + id,
                    area: ["99%", "99%"]
                })
            } else if (layEvent === 'select_finance') {
                layer.open({
                    type: 2,
                    title: "查看收支记录",
                    content: "{:url('Finance/get_list_by_puno')}?no=" + data.pici_number,
                    area: ["99%", "99%"]
                });
            }
        });
        var $ = layui.$, active = {
            reload: function () {
                // var keyword = $("[name=keyword]").val();
                //console.log(keyword);return;
                var field = $("[name=field]").val();
                var keyword = $("[name=keyword]").val();
                var start_time_2 = $("[name=start_time_2]").val();
                var end_time_2 = $("[name=end_time_2]").val();
                //执行重载
                table.reload('testReload', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    , where: {
                        field: field,
                        keyword: keyword,
                        start_time_2: start_time_2,
                        end_time_2: end_time_2
                    }
                });
            },
        };

        $('.demoTable .layui-btn').on('click', function () {
            //console.log($(this).data('type'));
            var type = $(this).data('type');
            //console.log(1);return;
            active[type] ? active[type].call(this) : '';
        });
    });


    function createTime(v) {
        var v = v * 1000; //js的时间戳要*1000
        var date = new Date(v);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? '0' + m : m;
        var d = date.getDate();
        d = d < 10 ? ("0" + d) : d;
        var h = date.getHours();
        h = h < 10 ? ("0" + h) : h;
        var M = date.getMinutes();
        M = M < 10 ? ("0" + M) : M;
        var S = date.getSeconds();
        S = S < 10 ? ("0" + S) : S;
        var str = y + "-" + m + "-" + d + " " + h + ":" + M + ":" + S;
        return str;
    }

    function export_card() {
        window.location.href = "{:url('export_tui_card_list')}";
    }
    function export_card_num() {
        window.location.href = "{:url('export_tui_card_list_cardnum')}";
    }
</script>