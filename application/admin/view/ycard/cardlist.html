<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}
<style>
/* 确保layui loading和msg不会换行 */
.layui-layer-loading .layui-layer-content {
    white-space: nowrap !important;
    text-align: center !important;
    width: auto !important;
    min-width: 150px !important;
}

.layui-layer-msg .layui-layer-content {
    white-space: nowrap !important;
    text-align: center !important;
    width: auto !important;
    min-width: 120px !important;
}

.layui-layer-loading1 .layui-layer-content,
.layui-layer-loading2 .layui-layer-content {
    white-space: nowrap !important;
    text-align: center !important;
    width: auto !important;
    min-width: 150px !important;
}
</style>

<body>
    <br />
    <div style="margin-left: 20px;">
        <div class="page-content">
            <div class="demoTable">
                <a class='layui-btn  layui-btn-sm' data-type="generate">生成卡号</a>
                <a class='layui-btn  layui-btn-sm' data-type="add">导入卡号</a>
                批次名称：
                <div class='layui-inline'>
                    <input class="layui-input" name="keyword" id="demoReload" autocomplete="off">
                </div>
                <button class="layui-btn  layui-btn-sm" data-type="reload">搜索</button>
            </div>
            <table class="layui-hide" id="demo" lay-filter="demo">
            </table>
            <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-sm" lay-event='downlod'>下载卡号密码</a>
            </script>
        </div>
    </div>

    <!--弹出新增的模态框-->
    <div id="add_cardbatch" style="display: none; margin-top:20px">

        <form class="layui-form" action="" enctype="multipart/form-data" method="">
            <!--        <input type="hidden" name="keynum" value="{:input('param.keynum')}">-->

            <div class="layui-form-item">
                <label class="layui-form-label">批次名称</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>
                <div class="layui-input-inline">
                    <input type="text" name="piciname" value="" required lay-verify="required" placeholder=""
                        autocomplete="off" class="layui-input">
                </div>
            </div>


            <div class="layui-form-item">
                <label class="layui-form-label">上传excel</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>
                <div class="layui-input-inline" style="display: flex;">
                    <button type="button" class="layui-btn" id="uploadExcel" style="margin-right:10px">
                        <i class="layui-icon">&#xe67c;</i>上传卡号密码
                    </button>
                    <a style="color: white;" href="__STATIC__/admin/excel/卡号信息导入.xls" download="卡号信息导入.xls">
                        <button type="button" class="layui-btn">
                            <i class="layui-icon">&#xe67c;</i>下载Excel模板
                        </button>
                    </a>
                </div>
            </div>

            <div class="table_card" style="display:none">
                <label class="layui-form-label" style="width: 18%;">随机卡号验证</label>
                <input type="hidden" value="" name="cardlist">
                <table class='layui-table' style='width: 150px;margin-left: 110px;'>
                    <tbody id='cardlist'>
                    </tbody>
                </table>
            </div>
            <div class="layui-form-item layui-form-text c_num" style="display:none">
                <label class="layui-form-label">上传卡数</label>
                <div class="layui-input-inline" style="margin-top: 10px;">
                    <span class="card_nums"></span>
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注说明</label>
                <div class="layui-input-inline">
                    <textarea name="content" placeholder="" class="layui-textarea"></textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn  layui-btn-sm" lay-submit lay-filter="sub" id="sub">提交</button>
                    <button type="button" onClick="javascript:history.back();"
                        class="layui-btn layui-btn-sm">返回</button>
                    <!--                导出模板-->
                    <!--                <button onclick="" type="button" class="layui-btn"><a style="color: white;" href="__STATIC__/admin/excel/卡号信息导入.xlsx" download="user_import_template.xlsx">下载Excel模板 </a></button>-->
                </div>
            </div>
        </form>

        <!--监听新增弹窗-->
        <script>
            layui.use(['form', 'upload'], function () {
                //console.log(111);
                var form = layui.form;
                var upload = layui.upload;
                var ischeck = false;
                var issub = false;
                //监听提交
                form.on('submit(sub)', function (data) {
                    if (issub == true) {
                        layer.msg("请勿重复提交");
                        return false
                    }
                    issub = true;
                    if (ischeck == false) {
                        layer.msg("请生成预览并确认无误");
                        return false;
                    }
                    var cardnumberlist = "";
                    $(".cardnum").each(function (i, n) {
                        var cardnum = $(n).html();
                        cardnumberlist += cardnum + ","
                    });
                    if (cardnumberlist == '') {
                        layer.msg("请输入并生成导入卡号");
                        return false;
                    }
                    
                    // 加载loading
                    var loading = layer.load(2, {
                        content: '正在提交数据，请稍等...',
                        style: 'width: auto; min-width: 180px; text-align: center; white-space: nowrap;'
                    });
                    //console.log(cardnumberlist);return false;
                    data.field.cardnumberlist = cardnumberlist;
                    $.post("{:url('ajax_add_card')}", data.field, function (data) {
                        //console.log(data);return false;
                        if (data.sta == 1) {
                            layer.close(loading);
                            layer.msg(data.msg, { time: 1500 }, function () {
                                window.location.href = "{:url('cardlist')}";
                            });
                        } else {
                            layer.close(loading);
                            issub = false;
                            layer.msg(data.msg);
                        }
                    }, "json");
                    return false;
                });

                var uploadLoading; // 声明上传loading变量
                
                upload.render({ //允许上传的文件后缀
                    elem: '#uploadExcel'
                    , url: '{:url("ajax_add_cardbatch")}' //此处为所上传的请求路径
                    , accept: 'file' //普通文件
                    , exts: 'xls|excel|xlsx' //只允许上传压缩文件
                    , method: 'POST'
                    , before: function(obj) {
                        // 上传前显示loading
                        uploadLoading = layer.load(2, {
                            content: '正在上传并解析文件，请稍等...',
                            style: 'width: auto; min-width: 200px; text-align: center; white-space: nowrap;'
                        });
                    }
                    , done: function (data) {
                        // 关闭上传loading
                        if (uploadLoading) {
                            layer.close(uploadLoading);
                        }
                        //console.log(1);
                        ischeck = true;
                        //console.log(1);
                        if (data.sta == 1) {
                            $(".c_num").show();
                            $(".table_card").show();
                            $(".card_nums").text(data.count);
                            $("[name=cardlist]").val(data.all_cardlist);
                            layer.msg('生成卡号', { icon: 6 });
                            $("#cardlist").html("");
                            var list = data.list;
                            var html = "";
                            $.each(list, function (i, n) {
                                i = i + 1;
                                if (i % 10 == 1) {
                                    html += "<tr>";
                                }
                                html += "<td class='cardnum'>" + n + "</td>";
                                if (i % 10 == 0) {
                                    html += "</tr>";
                                }
                            });
                            $("#cardlist").html(html);
                        } else {
                            var errormsg = data.msg;
                            layer.alert(errormsg, { icon: 2 });
                            $("#cardlist").html("");
                            var list = data.list;
                            var html = "";
                            $.each(list, function (i, n) {
                                i = i + 1;
                                if (i % 10 == 1) {
                                    html += "<tr>";
                                }
                                html += "<td class='cardnum'>" + n + "</td>";
                                if (i % 10 == 0) {
                                    html += "</tr>";
                                }
                            });
                            $("#cardlist").html(html);
                        }
                    }
                    , error: function() {
                        // 上传错误时关闭loading
                        if (uploadLoading) {
                            layer.close(uploadLoading);
                        }
                        layer.msg('上传失败，请检查网络或文件格式');
                    }
                })
            })
        </script>

    </div>

    <!--弹出新增的模态框在线生成-->
    <div id="generate" style="display: none; margin-top:20px">

        <form class="layui-form" action="" enctype="multipart/form-data" method="">
            <!--        <input type="hidden" name="keynum" value="{:input('param.keynum')}">-->

            <div class="layui-form-item">
                <label class="layui-form-label">批次名称</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>
                <div class="layui-input-inline">
                    <input type="text" name="piciname" value="" required lay-verify="required" placeholder=""
                           autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">开始卡号</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>
                <div class="layui-input-inline">
                    <input type="text" name="start_cardnum" value="" required lay-verify="required" placeholder=""
                           autocomplete="off" class="layui-input">
                </div>
            </div>



            <div class="layui-form-item">
                <label class="layui-form-label">卡号数量</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>
                <div class="layui-input-inline">
                    <input type="number" name="number"   value="" required lay-verify="required" placeholder=""
                           autocomplete="off" class="layui-input">
                </div>
            </div>




            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注说明</label>
                <div class="layui-input-inline">
                    <textarea name="content" placeholder="" class="layui-textarea"></textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn  layui-btn-sm" lay-submit lay-filter="sub1" id="sub">提交</button>
                    <button type="button" onClick="javascript:layer.closeAll();"
                            class="layui-btn layui-btn-sm">返回</button>
                </div>
            </div>
        </form>

        <!--监听新增弹窗-->
        <script>
            layui.use(['form', 'upload'], function () {
                var form = layui.form;
                var upload = layui.upload;
                var issub = false;
                //监听提交
                form.on('submit(sub1)', function (data) {
                    if (issub == true) {
                        layer.msg("请勿重复提交");
                        return false
                    }
                    issub = true;
                    
                    // 显示loading
                    var generateLoading = layer.load(2, {
                        content: '正在生成卡号，请稍等...',
                        style: 'width: auto; min-width: 180px; text-align: center; white-space: nowrap;'
                    });
                    
                    $.post("{:url('ajax_add_card1')}", data.field, function (data) {
                        // 关闭loading
                        layer.close(generateLoading);
                        
                        if (data.sta == 1) {
                            layer.msg(data.msg, { time: 1500 }, function () {
                                window.location.href = "{:url('cardlist')}";
                            });
                        } else {
                            issub = false;
                            layer.alert(data.msg);
                        }
                    }, "json").fail(function() {
                        // 请求失败时也要关闭loading
                        layer.close(generateLoading);
                        issub = false;
                        layer.msg('网络请求失败，请重试');
                    });
                    return false;
                });


            })
        </script>

    </div>
</body>

</html>
<script type="text/javascript">
    layui.use('table', function () {
        var table = layui.table;
        
        // 初始加载时显示loading
        var tableLoading = layer.load(2, {
            content: '正在加载数据，请稍等...',
            style: 'width: auto; min-width: 180px; text-align: center; white-space: nowrap;'
        });
        
        //方法级渲染
        table.render({
            elem: '#demo'
            , url: "{:url('cardlist')}?ajax=ajax"
            , cols: [[
                { field: 'id', title: '批次ID' }
                , { field: 'piciname', title: '批次名称' }
                , { field: 'card_num', title: '开卡数量' }
                , { field: 'begin_cardnumber', title: '开始卡号', sort: true }
                , { field: 'end_cardnumber', title: '结束卡号', sort: true }
                // ,{field:'keynum',title:'绑定子站',width:100}
                // ,{field:'begin_dui', title: '起兑时间',templet: function (row){ return createTime(row.begin_dui);},width:100}
                // ,{field:'end_dui', title: '止兑时间',templet: function (row){ return createTime(row.end_dui);},width:100}
                , { field: 'time', title: '生成时间', templet: function (row) { return createTime(row.time); } }
                , { field: 'name', title: '操作人员' }
                , { field: 'content', title: '备注' }
                , { fixed: 'right', title: "操作", align: 'center', toolbar: '#barDemo', width: 300 }
            ]]
            , id: 'testReload'
            , page: true
            , done: function(res, curr, count) {
                // 表格加载完成后关闭loading
                layer.close(tableLoading);
            }
        });
        table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            //console.log(obj);
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            //console.log(data); return false;
            var id = data.id;
            var piciname = data.piciname;
            //console.log(piciname);return false;
            if (layEvent === 'del') {
                layer.confirm('真的删除行么', function (index) {
                    // 显示删除loading
                    var deleteLoading = layer.load(2, {
                        content: '正在删除，请稍等...',
                        style: 'width: auto; min-width: 160px; text-align: center; white-space: nowrap;'
                    });
                    
                    $.post("{:url('delcardbatch')}", { id: id }, function (data) {
                        // 关闭loading
                        layer.close(deleteLoading);
                        layer.msg(data.msg);
                        if (data.sta == 1) {
                            obj.del();
                        }
                    }, "json").fail(function() {
                        // 请求失败时也要关闭loading
                        layer.close(deleteLoading);
                        layer.msg('删除失败，请重试');
                    });
                });
            } else if (layEvent === 'edit') {
                layer.open({
                    type: 2,
                    title: "修改信息",
                    content: "{:url('edit_cardbatch')}?id=" + id,
                    area: ["600px", "500px"]
                })
            } else if (layEvent === 'pur') {
                window.location.href = "{:url('pur_form')}?id=" + id;
            } else if (layEvent === "downlod") {
                // 弹出密码输入框进行验证
                layer.prompt({
                    formType: 1,
                    title: '请输入高级密码进行验证',
                    area: ['300px', '150px']
                }, function(value, index, elem){
                    // 验证密码不为空
                    if(value.trim() === '') {
                        layer.msg('请输入高级密码', {icon: 2});
                        return;
                    }
                    
                    // 显示验证中状态
                    var loadIndex = layer.load(2, {content: '正在验证密码...'});
                    
                    // 先异步验证密码
                    $.ajax({
                        url: '{:url("verify_download_password")}',
                        type: 'POST',
                        data: {
                            id: id,
                            admin_password: value
                        },
                        dataType: 'json',
                        success: function(res) {
                            layer.close(loadIndex);
                            if(res.code === 0) {
                                // 验证成功，开始下载
                                layer.msg('验证成功，正在准备下载...', {
                                    icon: 1,
                                    time: 2000,
                                    style: 'width: auto; min-width: 180px; text-align: center; white-space: nowrap;'
                                });
                                // 关闭密码输入框
                                layer.close(index);
                                // 开始下载
                                window.location.href = "{:url('download_cardbatch')}?id=" + id + "&admin_password=" + encodeURIComponent(value);
                            } else {
                                // 验证失败，显示错误信息
                                layer.msg(res.msg || '密码验证失败', {icon: 2});
                            }
                        },
                        error: function() {
                            layer.close(loadIndex);
                            layer.msg('网络错误，请重试', {icon: 2});
                        }
                    });
                });
            } else if (layEvent === "downlod_qrcode") {
                // 弹出密码输入框进行验证
                layer.prompt({
                    formType: 1,
                    title: '请输入高级密码进行验证',
                    area: ['300px', '150px']
                }, function(value, index, elem){
                    // 验证密码不为空
                    if(value.trim() === '') {
                        layer.msg('请输入高级密码', {icon: 2});
                        return;
                    }
                    
                    // 显示验证中状态
                    var loadIndex = layer.load(2, {content: '正在验证密码...'});
                    
                    // 先异步验证密码
                    $.ajax({
                        url: '{:url("verify_download_password")}',
                        type: 'POST',
                        data: {
                            id: id,
                            admin_password: value
                        },
                        dataType: 'json',
                        success: function(res) {
                            layer.close(loadIndex);
                            if(res.code === 0) {
                                // 验证成功，开始下载
                                layer.msg('验证成功，正在准备下载二维码文件...', {
                                    icon: 1,
                                    time: 2000,
                                    style: 'width: auto; min-width: 200px; text-align: center; white-space: nowrap;'
                                });
                                // 关闭密码输入框
                                layer.close(index);
                                // 开始下载
                                window.location.href = "{:url('download_cardbatch_qrcode')}?id=" + id + "&admin_password=" + encodeURIComponent(value);
                            } else {
                                // 验证失败，显示错误信息
                                layer.msg(res.msg || '密码验证失败', {icon: 2});
                            }
                        },
                        error: function() {
                            layer.close(loadIndex);
                            layer.msg('网络错误，请重试', {icon: 2});
                        }
                    });
                });
            }
        });
        var $ = layui.$, active = {
            reload: function () {
                var keyword = $("[name=keyword]").val();
                
                // 显示搜索loading
                var searchLoading = layer.load(2, {
                    content: '正在搜索，请稍等...',
                    style: 'width: auto; min-width: 160px; text-align: center; white-space: nowrap;'
                });
                
                //执行重载
                table.reload('testReload', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    , where: {
                        keyword: keyword,
                    }
                    , done: function(res, curr, count) {
                        // 搜索完成后关闭loading
                        layer.close(searchLoading);
                    }
                });
            },
            add: function () {
                layui.use(['layer'], function () {
                    var layer = layui.layer, $ = layui.$;
                    layer.open({
                        type: 1,//类型
                        area: ['600px', '500px'],//定义宽和高
                        title: '导入批次卡',//题目
                        shadeClose: false,//点击遮罩层关闭
                        content: $('#add_cardbatch')//打开的内容
                    });
                })
                // window.location.href="{:url('add_cardbatch')}";
            },
            generate: function(){
                layui.use(['layer'], function () {
                    var layer = layui.layer, $ = layui.$;
                    layer.open({
                        type: 1,//类型
                        area: ['600px', '500px'],//定义宽和高
                        title: '导入批次卡',//题目
                        shadeClose: false,//点击遮罩层关闭
                        content: $('#generate')//打开的内容
                    });
                })
            }
        };

        $('.demoTable .layui-btn').on('click', function () {
            //console.log($(this).data('type'));
            var type = $(this).data('type');
            //console.log(1);return;
            active[type] ? active[type].call(this) : '';
        });
    });

    function createTime(v) {
        var v = v * 1000; //js的时间戳要*1000
        var date = new Date(v);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? '0' + m : m;
        var d = date.getDate();
        d = d < 10 ? ("0" + d) : d;
        var h = date.getHours();
        h = h < 10 ? ("0" + h) : h;
        var M = date.getMinutes();
        M = M < 10 ? ("0" + M) : M;
        var S = date.getSeconds();
        S = S < 10 ? ("0" + S) : S;
        var str = y + "-" + m + "-" + d + " ";
        return str;
    }
</script>
