<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>储值卡等级修改记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__LAYUI__/css/layui.css" media="all">
</head>
<body>
<div class="layui-container" style="padding: 20px;">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>储值卡等级修改记录</h3>
        </div>
        <div class="layui-card-body">
            <!-- 搜索栏 -->
            <form class="layui-form" id="search-form">
                <div class="layui-row">
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">操作员</label>
                            <div class="layui-input-block">
                                <input type="text" name="operator" placeholder="请输入操作员" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">客户</label>
                            <div class="layui-input-block">
                                <input type="text" name="customer" placeholder="请输入客户名称" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">开始时间</label>
                            <div class="layui-input-block">
                                <input type="text" name="start_time" id="start_time" placeholder="开始时间" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">结束时间</label>
                            <div class="layui-input-block">
                                <input type="text" name="end_time" id="end_time" placeholder="结束时间" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" type="button" id="search-btn">查询</button>
                        <button class="layui-btn layui-btn-primary" type="reset">重置</button>
                        <button class="layui-btn layui-btn-normal" type="button" id="export-btn">导出</button>
                    </div>
                </div>
            </form>

            <!-- 数据表格 -->
            <table class="layui-hide" id="data-table" lay-filter="data-table"></table>
        </div>
    </div>
</div>

<!-- 操作按钮模板 -->
<script type="text/html" id="toolbar-tpl">
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看详情</a>
</script>

<script src="__LAYUI__/layui.js"></script>
<script>
layui.use(['table', 'form', 'laydate', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var laydate = layui.laydate;
    var layer = layui.layer;
    var $ = layui.$;
    
    // 日期选择器
    laydate.render({
        elem: '#start_time',
        type: 'datetime'
    });
    
    laydate.render({
        elem: '#end_time',
        type: 'datetime'
    });
    
    // 数据表格
    var dataTable = table.render({
        elem: '#data-table',
        url: '/admin/ycard/ajax_card_type_change_list',
        page: true,
        cols: [[
            {field: 'no', title: '批次单号', width: 200},
            {field: 'customer_name', title: '客户', width: 150},
            {field: 'old_cardtype_name', title: '原卡型', width: 120},
            {field: 'new_cardtype_name', title: '新卡型', width: 120},
            {field: 'operator', title: '操作员', width: 120},
            {field: 'add_time', title: '操作时间', width: 180},
            {field: 'remarks', title: '备注', minWidth: 200},
            {fixed: 'right', title: '操作', width: 120, toolbar: '#toolbar-tpl'}
        ]],
        request: {
            pageName: 'page',
            limitName: 'limit'
        },
        response: {
            statusName: 'sta',
            statusCode: 0,
            msgName: 'msg',
            countName: 'count',
            dataName: 'data'
        }
    });
    
    // 搜索
    $('#search-btn').on('click', function(){
        var formData = form.val('search-form');
        table.reload('data-table', {
            where: formData,
            page: {curr: 1}
        });
    });
    
    // 导出
    $('#export-btn').on('click', function(){
        var formData = form.val('search-form');
        var params = new URLSearchParams(formData).toString();
        window.open('/admin/ycard/export_card_type_change?' + params);
    });
    
    // 监听工具条
    table.on('tool(data-table)', function(obj){
        var data = obj.data;
        if(obj.event === 'detail'){
            // 查看详情
            layer.open({
                type: 2,
                title: '修改记录详情',
                content: '/admin/ycard/card_type_change_detail?no=' + data.no,
                area: ['80%', '70%'],
                maxmin: true
            });
        }
    });
});
</script>
</body>
</html> 