<!DOCTYPE html>
{include file="public/iframeheader"/}
<link rel="stylesheet" type="text/css" href="__STATIC__/admin/jquery-easyui-1.5.2/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="__STATIC__/admin/jquery-easyui-1.5.2/themes/icon.css">
<script type="text/javascript" src="__STATIC__/admin/jquery-easyui-1.5.2/jquery.easyui.min.js"></script>



<body>
    <br />
    <div style="margin-left: 20px;">

        <form class="layui-form" action="">
            <input type="hidden" name="id" value="{:input('param.id')}">

            <div class="layui-form-item">
                <label class="layui-form-label">业务员名称</label>
                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>
                <div class="layui-input-inline">
                    <input type="text" name="name" value="{$info['name']|default=''}" required lay-verify="required"
                        placeholder="" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux"></div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">业务员电话</label>
                <div class="layui-input-inline">
                    <input type="text" name="phone" value="{$info['phone']|default=''}"
                        placeholder="" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux"></div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注说明</label>
                <div class="layui-input-block" style="width:40%;">
                    <textarea name="remark" placeholder=""
                        class="layui-textarea">{$info['remark']|default=""}</textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn  layui-btn-sm" lay-submit lay-filter="sub">立即提交</button>
                    <button type="button" onClick="javascript:history.back();"
                        class="layui-btn layui-btn-sm">返回</button>
                </div>
            </div>
        </form>



        <script>
            layui.use('form', function () {
                var form = layui.form;
                //监听提交
                form.on('submit(sub)', function (data) {
                    load = layer.load(2, { shade: [0.1, '#fff'] });
                    $.post("{:url('ajax_add_sale')}", data.field, function (data) {
                        layer.close(load);
                        if (data.sta == 1) {
                            layer.msg(data.msg, { time: 1500 }, function () {
                                window.location.href = "{:url('sale_list')}";
                            });
                        } else {
                            layer.msg(data.msg);
                        }
                    }, "json");

                    return false;
                });
            });
        </script>

    </div>
</body>

</html>
