<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
<br/>
<div style="margin-left: 20px;">
    <div class="page-content">
        <button class="layui-btn  layui-btn-sm" onclick="export_card()">导出</button>
        <input type="hidden" name="no" id="no" value="{$no}">
        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
    </div>
</div>

</body>

</html>
<script type="text/javascript">
    var no = $("[name=no]").val();
    get_table();

    function get_table(){
        layui.use(['table', 'jquery'], function () {
            var no = document.getElementById("no").value;
            var table = layui.table;
            //方法级渲染
            table.render({
                elem: '#demo'
                , url: "{:url('ajax_card_status_change_detail')}?no=" + no
                , cols: [[
                    {type: 'numbers', title: '序号'}
                    , {field: 'card_name', title: '卡号'}
                    , {field: 'status', title: '状态'}
                ]]
                , id: 'id'
                , page: true
            });

            var $ = layui.$, active = {
                reload: function () {
                    //执行重载
                    table.reload('testReload', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        }
                    });
                },
            };
        });
    }

    function export_card(){
        var no = $("[name=no]").val();
        window.location.href = "{:url('export_card_status_change_detail')}?no=" + no;
    }


</script>