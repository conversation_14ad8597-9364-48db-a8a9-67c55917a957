<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
    <br />
    <div style="margin-left: 20px;">
        <div class="page-content">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>卡使用记录 - {$cardnum}</h3>
                </div>
                <div class="layui-card-body">
                  
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <table class="layui-hide" id="usageTable" lay-filter="usageTable"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>

<script type="text/javascript">
    layui.use(['table', 'layer'], function() {
        var table = layui.table;
        var layer = layui.layer;
        
        // 渲染表格
        table.render({
            elem: '#usageTable',
            url: "{:url('ajax_get_card_usage_record')}?card_id={$card_id}&cardnum={$cardnum}",
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            cols: [[
                {type: 'numbers', title: '序号', width: 60},
                {field: 'time', title: '使用时间', width: 200, sort: true},
                {field: 'order_no', title: '订单号', width: 150, templet: function(d) {
                    return d.order_no ? d.order_no : '--';
                }},
                {field: 'amount', title: '操作金额', width: 120, templet: function(d) {
                    if (d.amount && d.amount !== '0' && d.amount !== '--') {
                        return '<span style="color: red">￥' + d.amount + '</span>';
                    }
                    return '--';
                }},
                {field: 'after_money', title: '操作前余额', width: 120, templet: function(d) {
                    return '<span style="color: #666">￥' + d.after_money + '</span>';
                }},
                {field: 'yu_money', title: '操作后余额', width: 120, templet: function(d) {
                    return '<span style="color: #009688">￥' + d.yu_money + '</span>';
                }},
                {field: 'status', title: '状态', width: 100, templet: function(d) {
                    var color = d.status === '已支付' ? 'green' : 'orange';
                    return '<span style="color: ' + color + '">' + d.status + '</span>';
                }},
                {field: 'remark', title: '备注', minWidth: 150}
            ]],
            id: 'usageTableReload',
            request: {
                pageName: 'page',
                limitName: 'limit'
            },
            response: {
                statusName: 'code',
                statusCode: 0,
                msgName: 'msg',
                countName: 'count',
                dataName: 'data'
            },
            text: {
                none: '暂无使用记录'
            }
        });
        
        // 表格排序事件
        table.on('sort(usageTable)', function(obj) {
            table.reload('usageTableReload', {
                initSort: obj,
                where: {
                    field: obj.field,
                    order: obj.type
                }
            });
        });
    });
</script>