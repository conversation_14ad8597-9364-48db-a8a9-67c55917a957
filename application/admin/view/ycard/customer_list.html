<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
    <br />
    <div style="margin-left: 20px;">
        <div class="page-content">
            <div class="demoTable">
                <a class='layui-btn  layui-btn-sm' data-type="add">添加</a>
                名称：
                <div class='layui-inline'>
                    <input class="layui-input" name="keyword" id="demoReload" autocomplete="off">
                </div>
                <button class="layui-btn  layui-btn-sm" data-type="reload" link="sub">搜索</button>
            </div>
            <table class="layui-hide" id="demo" lay-filter="demo">
            </table>
            <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-sm" lay-event='edit'>修改</a>
            <a class="layui-btn layui-btn-sm" lay-event='del'>删除</a>
        </script>
        </div>
    </div>
</body>

</html>
<script type="text/javascript">
    layui.use('table', function () {
        var table = layui.table;
        //方法级渲染
        table.render({
            elem: '#demo'
            , url: "{:url('customer_list')}?ajax=ajax"
            , cols: [[
                { type: 'numbers', title: '序号' }
                , { field: 'name', title: '客户名称' }
                , { field: 'linkman', title: '联系人' }
                , { field: 'linktel', title: '联系电话' }
                , { field: 'sale_name', title: '所属业务员' }
                , { field: 'remark', title: '备注' }
                , { fixed: 'right', title: "操作", align: 'center', toolbar: '#barDemo' }
            ]]
            , id: 'testReload'
            , page: true
        });
        table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            var id = data.id;
            if (layEvent === 'del') {
                layer.confirm('您确定要删除【<font color=red>' + data.name + '</font>】这条数据吗？', function (index) {
                    $.post("{:url('customer_del')}", { id: id }, function (data) {
                        layer.msg(data.msg);
                        if (data.sta == 1) {
                            obj.del();
                        }
                    }, "json");
                });
            } else if (layEvent === 'edit') {
                window.location.href = "{:url('add_customer')}?id=" + id;
            }
        });
        var $ = layui.$, active = {
            reload: function () {
                var keyword = $("[name=keyword]").val();
                //执行重载
                table.reload('testReload', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    , where: {
                        keyword: keyword,
                    }
                });
            },
            add: function () {
                window.location.href = "{:url('add_customer')}";
            }
        };

        $('.demoTable .layui-btn').on('click', function () {
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>
<script>
    //添加回车事件
    $(function () {
        $(document).keydown(function (event) {
            if (event.keyCode == 13) {
                $("[link=sub]").click();
            }
        });
    });
</script>
