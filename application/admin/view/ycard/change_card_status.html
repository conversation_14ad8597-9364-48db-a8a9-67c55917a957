<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>更改卡状态</title>
    {include file="public/iframeheader"/}
</head>

<body>

<div style="width: 800px;float: left">
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body" pad15>
                        <div class="layui-form" wid100 lay-filter="">

                            <div class="layui-form-item">
                                <label class="layui-form-label">选择客户</label>
                                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                                <div class="layui-input-inline" style="width: 400px;">
                                    <select name="customer_id" lay-verify="required" lay-search>
                                        <option value="">请选择</option>
                                        {foreach name='customer_list' key='key' item='value'}
                                        <option value="{$value['id']}"> {$value["name"]}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">状态</label>
                                <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
                                <div class="layui-input-block">
                                    <input type="radio" name="type" value="1" title="开卡">
                                    <input type="radio" name="type" value="2" title="关卡">
                                    <input type="radio" name="type" value="3" title="废卡">
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">销售单号(选填)</label>
                                <div class="layui-input-inline" style="width: 400px;">
                                    <input type="text" name="sale_number" value="" placeholder="" autocomplete="off"
                                           class="layui-input">
                                </div>
                                <br>
                                <!--                                <div class="layui-form-mid layui-word-aux">输入销售单号后，卡号范围为销售单内所有卡号，卡号范围和销售单号填其一，同时填写时销售单号优先</div>-->
                            </div>

                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">卡号范围(选填)</label>
                                    <div class="layui-input-inline" style="width: 200px;">
                                        <input type="text" name="begin_cardnumber" placeholder="起始卡号"
                                               autocomplete="off"
                                               class="layui-input">
                                    </div>
                                    <div class="layui-form-mid">-</div>
                                    <div class="layui-input-inline" style="width: 200px;">
                                        <input type="text" name="end_cardnumber" placeholder="结束卡号"
                                               autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <input type='button' class="layui-btn" value="生成预览" id='preview'/>
                            </div>

                            <div id="template-table-div" style="padding-bottom: 10px;">

                                <div class="layui-inline">
                                    <!--                                    <a class='layui-btn  layui-btn-sm' id="add-preview" data-type="add" style="margin-bottom: 10px;">添加</a>-->
                                    <label class="layui-form-label">预览数据(选填)</label>
                                    <div class="layui-input-block">
                                        <textarea id="preview_textarea" name="add_cardnum" cols="50" rows="15" placeholder=""
                                                  class="layui-textarea"></textarea>
                                        <!--                                        <table id="templateTable" lay-filter="templateTable"></table>-->
                                        <!--                                        <script type="text/html" id="barDemo">-->
                                        <!--                                            <a class="layui-btn layui-btn-sm" lay-event='del'>删除</a>-->
                                        <!--                                        </script>-->
                                    </div>
                                </div>
                            </div>

                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label"></label>
                                <div class="layui-input-block" style="width:40%;">
                                    当前总数：<span id="count" style="color: red">0</span>
                                </div>
                            </div>

                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">备注</label>
                                <div class="layui-input-block" style="width:40%;">
                                    <textarea name="remarks" placeholder="" class="layui-textarea">{$info['remark']|default=""}</textarea>
                                </div>
                            </div>

                            <input type="hidden" name="card_nums" id="card_nums">


                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="sub">确认保存</button>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="layui-card" style="width: 450px;margin:15px;float:left;">
    <table id="templateTable" lay-filter="templateTable"></table>
</div>

<!--增加预览数据表单-->
<!--<div id="add-preview-form" style="display: none; margin-top:20px">-->
<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label">卡号</label>-->
<!--        <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 8px;">*</span>-->
<!--        <div class="layui-input-inline">-->
<!--            <textarea name="add_cardnum" cols="50" rows="10"></textarea>-->
<!--            <input type="text" name="add_cardnum" value="" required lay-verify="required" placeholder=""-->
<!--                   autocomplete="off" class="layui-input">-->
<!--        </div>-->
<!--    </div>-->


<!--    <div class="layui-form-item">-->
<!--        <div class="layui-input-block">-->
<!--            <button class="layui-btn  layui-btn-sm" lay-submit lay-filter="add-preview-button"-->
<!--                    id="add-preview-button">添加-->
<!--            </button>-->
<!--        </div>-->
<!--    </div>-->
<!--</div>-->
<script src="/public__STATIC__/admin/layui-2.8/src/layui.js"></script>
<script>

    function callback(msg) {
        layer.msg(msg, {time: 1500}, function (data) {
            layer.closeAll();
            window.parent.location.reload();
        })
    }

    //一般直接写在一个js文件中
    layui.use(['element', 'form'], function () {
        var form = layui.form;
        //监听提交1
        form.on('submit(sub)', function (data) {
            var table_data = layui.table.cache['templateTable'];
            var ids = '';
            for (const key in table_data) {
                ids += table_data[key].cardnum + ','
            }
            ids = ids.slice(0, ids.length - 1);
            $('#card_nums').val(ids);
            data.field['card_nums'] = ids;
            load = layer.load(2, {shade: [0.1, '#fff']});
            $.post("{:url('ajax_change_card_status')}", data.field, function (data) {
                if (data.code == 0) {
                    layer.msg(data.msg);
                    setInterval(function () {
                        window.location.href = "{:url('change_card_status')}";
                    }, 1500);
                } else {
                    layer.msg(data.msg);
                }
            }, "json");
            layer.close(load);
            return false;
        });

        $("#preview").click(function () {

            var customer_id = $("[name=customer_id]").val();
            if (customer_id == '') {
                layer.msg("请选择客户");
                return false;
            }
            var begin_cardnumber = $("[name=begin_cardnumber]").val();
            var end_cardnumber = $("[name=end_cardnumber]").val();
            var sale_number = $("[name=sale_number]").val();
            if (sale_number == '') {
                if (begin_cardnumber == '') {
                    layer.msg("请输入开始卡号");
                    return false;
                }
                if (end_cardnumber == '') {
                    layer.msg("请输入结束卡号");
                    return false;
                }
            }
            $.post("{:url('get_card_change_preview')}", {
                customer_id: customer_id,
                begin_cardnumber: begin_cardnumber,
                end_cardnumber: end_cardnumber,
                sale_number: sale_number
            }, function (data) {
                if (data.code == 0) {
                    // 请求成功 显示表格
                    $('#template-table-div').show();
                    popup(data.data);
                } else {
                    layer.msg(data.msg);
                }

            }, "json");
        })
    });

    layui.use('table', function () {

        var table = layui.table;

        table.render({
            elem: '#templateTable'
            , data: []
            , cols: [[ //设置数据表格表头
                {field: 'cardnum', title: '卡号', align: "center", width: '40%'}
                , {field: 'status', title: '状态', align: "center", width: '30%'}
                , {
                    field: 'id', title: '是否可以更改', align: "center", width: '30%', templet: function (item) {
                        if (item.status == '已销售' || item.status == '已开卡' || item.status == '已关卡') {
                            return '<button type="button" class="layui-btn layui-btn-normal layui-btn-sm">可以</button>';
                        } else {
                            return '<button type="button" class="layui-btn layui-btn-danger layui-btn-sm">不可以</button>';
                        }
                    }
                }
            ]],
            maxHeight: 600,
            limit: Number.MAX_VALUE
        });
    });

    function popup(result) {

        var cardnum = $('textarea[name="add_cardnum"]').val();

        var alter_cardnunm = '';

        for (key in result) {
            alter_cardnunm += result[key].cardnum + "\n";
        }

        cardnum = alter_cardnunm + cardnum;
        cardnum = jsexplode(cardnum,"\n");
        cardnum = unique(cardnum);
        var alter_cardnum1 = '';
        for (key in cardnum) {
            alter_cardnum1 += cardnum[key] + "\n";
        }
        console.log(alter_cardnum1)

        $('textarea[name="add_cardnum"]').val(alter_cardnum1);
        $('textarea[name="add_cardnum"]').change();

    }

    function jsexplode(str, delimiter) {
        var arr = [];
        var start = 0;
        var end = str.indexOf(delimiter);
        while (end !== -1) {
            arr.push(str.substring(start, end));
            start = end + delimiter.length;
            end = str.indexOf(delimiter, start);
        }
        arr.push(str.substring(start));
        return arr;
    }

    function unique (arr) {
        return Array.from(new Set(arr));
    }

    $("#preview_textarea").change(function () {
        // 提交卡号，增加预览数据
        var cardnum = $('textarea[name="add_cardnum"]').val();
        var customer_id = $("[name='customer_id']").val();
        var type = $("input[name='type']:checked").val();
        var count = $('#count').html();
        count = jsexplode(cardnum.trim(),"\n").length;
        $('#count').html(count);
        if (customer_id == '') {
            layer.msg("请选择客户");
            return false;
        }
        if (type == '') {
            leyer.msg('请先选择更改状态');
            return false;
        }
        var request_data = {
            cardnum: cardnum,
            type: 'change_card_status',
            customer_id: customer_id,
            status: type,
        };

        $.post("{:url('ajax_add_preview_info')}"
            , request_data
            , function (data) {
                if (data.code == 0) {
                    layui.use('table', function () {
                        var table_data = layui.table.cache['templateTable'];
                        table_data = data.data;
                        layui.table.reload("templateTable", {
                            // 将新数据 dataBak  重新载入ID为 titleId 的表格 实现新增一行效果
                            data: table_data
                        });
                    });
                    layer.msg('请求成功');
                } else {
                    layer.msg(data.msg);
                }
            }, "json");
    });

</script>

</body>

</html>
