<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
    <br />
    <form class="layui-form" action="">


        <div class="layui-form-item">
            <label class="layui-form-label">选择客户</label>
            <div class="layui-input-inline" style="width: 400px;" id="customer_div">
                <input type="text" name="customer_id" id="customer_id" required lay-verify="required" autocomplete="off"
                    class="layui-input" placeholder="请选择客户" disabled style="background-color: #f0f0f0;"
                    value="{$pucard_info.customer_info.name}">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">联系人</label>
            <div class="layui-input-inline" style="width: 400px;">
                <input type="text" name="linkman" required lay-verify="required" autocomplete="off" class="layui-input"
                    disabled style="background-color: #f0f0f0;" value="{$pucard_info.customer_info.linkman}">
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">联系电话</label>
            <div class="layui-input-inline" style="width: 400px;">
                <input type="text" name="linktel" required lay-verify="required" autocomplete="off" class="layui-input"
                    disabled style="background-color: #f0f0f0;" value="{$pucard_info.customer_info.linktel}">
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">销售人员</label>
            <div class="layui-input-inline" style="width: 400px;">
                <input type="text" name="sale_name" required lay-verify="required" autocomplete="off"
                    class="layui-input" disabled style="background-color: #f0f0f0;" value="{$pucard_info.sale_name}">
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>


        <div class="layui-form-item" id="begin_start_show">
            <label class="layui-form-label">卡号有效期(开始时间)</label>
            <div class="layui-input-inline" style="width: 400px;">
                <input class="layui-input" placeholder="开始时间" id="begin_start" name="begin_time" disabled
                    style="background-color: #f0f0f0;" value="{$pucard_info.begin_time}">
            </div>
        </div>

        <div class="layui-form-item" id="end_time_show">
            <label class="layui-form-label">卡号有效期(结束时间)</label>
            <div class="layui-input-inline" style="width: 400px;">
                <input class="layui-input" placeholder="截止时间" id="end_time" name="end_time" disabled
                    style="background-color: #f0f0f0;" value="{$pucard_info.end_time}">
            </div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注说明</label>
            <div class="layui-input-inline" style="width: 400px;">
                <textarea name="content" placeholder="请输入内容" class="layui-textarea" disabled
                    style="background-color: #f0f0f0;">{$pucard_info.remark}</textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">销售明细</label>
            <div class="layui-input-inline" style="width: auto;">
                <table class="layui-table">
                    <colgroup>
                        <col width="">
                        <col width="">
                        <col width="">
                        <col>
                    </colgroup>
                    <thead>
                        <tr>
                            <th>选择卡型</th>
                            <th>卡折扣</th>
                            <th>开始卡号</th>
                            <th>结束卡号</th>
                            <th>卡型市场价</th>
                            <th>折扣</th>
                            <th>最终价格(单价)</th>
                            <th>合计</th>
                            <!-- <th>操作</th> -->
                        </tr>
                    </thead>
                    <tbody id="tbody">
                        <!-- 添加下一行 -->
                        <!-- <tr><a class="layui-btn layui-btn-sm add_tr">添加</a></tr> -->
                        <!-- 铺卡默认有一行 -->
                        {foreach name="pucard_son_list" key="k" item="v"}
                        <tr id="base_tr" class="base_tr">
                            <td class="cardtype_id">
                                <select name="cardtype_id[]" lay-verify="required" lay-search lay-filter="cardtype_id">
                                    <option value="">请选择</option>
                                    {foreach name='cardtype_list' key='key' item='value'}
                                    {if condition="$v['cardtype_id'] eq $value['id']" }
                                    <option value="{$value['id']}" selected> {$value["name"]}</option>
                                    {else /}
                                    <option value="{$value['id']}"> {$value["name"]}</option>
                                    {/if}
                                    {/foreach}
                                </select>
                            </td>
                            <td class="card_leve_title"><input name="card_leve_title[]" class="layui_input" disabled
                                style="background-color: #f0f0f0;" value="{$v.card_leve_title}" /></td>
                            <td class="begin_card"><input name="begin_card[]" class="layui_input" disabled
                                    style="background-color: #f0f0f0;" value="{$v.begin_card}" /></td>
                            <td class="end_card"><input name="end_card[]" class="layui_input" disabled
                                    style="background-color: #f0f0f0;" value="{$v.end_card}" /></td>
                            <td class="market_price"><input name="market_price[]" class="layui_input" disabled
                                    style="background-color: #f0f0f0;" value="{$v.market_price}" /></td>
                            <td class="zhekou"><input name="zhekou[]" class="layui_input" disabled
                                    style="background-color: #f0f0f0;" value="{$v.zhekou}" /></td>
                            <td class="end_price"><input name="end_price[]" class="layui_input" disabled
                                    style="background-color: #f0f0f0;" value="{$v.end_price}" /></td>
                            <td class="all_price"><input name="all_price[]" class="layui_input" disabled
                                    style="background-color: #f0f0f0;" disabled style="background-color: #f0f0f0;"
                                    value="{$v.all_price}" /></td>
                            <!-- <td><a class="layui-btn layui-btn-sm del_tr">删除</a></td> -->
                        </tr>
                        {/foreach}

                    </tbody>
                </table>

            </div>
            <div class="layui-input-inline layui-input-company"></div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <!-- <div class="layui-form-item" style="display: none;" id="sub">
            <div class="layui-input-block">
                <button class="layui-btn  layui-btn-sm" lay-submit lay-filter="formDemo">确认铺卡</button>
            </div>
        </div> -->
    </form>
    <script>
        layui.use(['layer', 'jquery', 'form'], function () {
            var form = layui.form;

        })
    </script>

</body>

</html>
