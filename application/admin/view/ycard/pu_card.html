<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}
<script src="__STATIC__/admin/js/jquery.autocompleter.js" type="text/javascript"></script>
<style>
    .autocompleter {
        width: 180px;
        background: #dddddd;
        position: absolute;
        top: 100;
        left: 100;
        z-index: 100;
    }

    .autocompleter,
    .autocompleter-hint {
        position: absolute;
    }

    .autocompleter-list {
        box-shadow: inset 0px 0px 6px rgba(0, 0, 0, 0.1);
        list-style: none;
        margin: 0;
        padding: 0;
        text-align: left;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
    }

    .autocompleter-item-selected {
        background: #ffffff;
    }

    .autocompleter-item {
        padding: 6px 12px;
        color: #444444;
        font-size: 20px;
        cursor: pointer;
    }

    .autocompleter-item:hover {
        background: #dbed8a;
    }

    .autocompleter-item strong {
        background: #f9de8f;
        text-shadow: 0 1px 0 #ffffff;
    }

    .autocompleter-item span {
        color: #bbbbbb;
    }

    .autocompleter-hint {
        color: #ccc;
        text-align: left;
        top: -56px;
        font-weight: 400;
        left: 0;
        width: 100%;
        padding: 12px 12px 12px 13px;
        font-size: 24px;
        display: none;
    }

    .autocompleter-hint span {
        color: transparent;
    }

    .autocompleter-hint-show {
        display: block;
    }

    .autocompleter-closed {
        display: none;
    }


</style>
<style>
    .monthpicker {
        width: 300px;
    }

    .Amount {
        text-align: center
    }
</style>

<body>
    <br />
    <form class="layui-form" action="">


        <div class="layui-form-item">
            <label class="layui-form-label">选择客户</label>
            <span style="color: red;font-size: 150%;float: left;margin-left: -10px;margin-top: 10px;">*</span>
            <div class="layui-input-inline" style="width: 400px;" id="customer_div">
                <input type="text" name="customer_id" id="customer_id" required lay-verify="required" autocomplete="off"
                    class="layui-input" placeholder="请选择客户">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">联系人</label>
            <div class="layui-input-inline" style="width: 400px;">
                <input type="text" name="linkman" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">联系电话</label>
            <div class="layui-input-inline" style="width: 400px;">
                <input type="text" name="linktel" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">销售人员</label>
            <div class="layui-input-inline" style="width: 400px;">
                <input type="text" name="sale_name" autocomplete="off"
                    class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">是否自动开卡</label>
            <div class="layui-input-inline " style="width: 400px;">
                <select name="is_auto_open" lay-verify="required" lay-filter="is_auto_open">
                    <option value="0">否</option>
                    <option value="1" selected>是</option>
                </select>
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <div class="layui-form-item " id="begin_start_show" >
            <label class="layui-form-label">卡号有效期(开始时间)</label>
            <div class="layui-input-inline " style="width: 400px;">
                <input class="layui-input" placeholder="开始时间" id="begin_start" name="begin_time" autocomplete="off">
            </div>
        </div>

        <div class="layui-form-item" id="end_time_show">
            <label class="layui-form-label">卡号有效期(结束时间)</label>
            <div class="layui-input-inline" style="width: 400px;">
                <input class="layui-input" placeholder="截止时间" id="end_time" name="end_time" autocomplete="off">
            </div>
        </div>


        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注说明</label>
            <div class="layui-input-inline" style="width: 400px;">
                <textarea name="content" placeholder="请输入内容" class="layui-textarea"></textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">销售明细</label>
            <div class="layui-input-inline" style="width: auto;">
                <table class="layui-table">
                    <colgroup>
                        <col width="">
                        <col width="">
                        <col width="">
                        <col>
                    </colgroup>
                    <thead>
                        <tr>
                            <th>选择卡型</th>
                            <th>选择卡折扣</th>
                            <th>数量</th>
                            <th>开始卡号</th>
                            <th>结束卡号</th>
                            <th>卡型销售价</th>
                            <th>折扣(百分比)</th>
                            <th>最终价格(单价)</th>
                            <th>合计</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tbody">
                        <!-- 添加下一行 -->
                        <tr><a class="layui-btn layui-btn-sm add_tr">添加</a></tr>
                        <!-- 铺卡默认有一行 -->
                        <tr id="base_tr" class="base_tr">
                            <td class="cardtype_id">
                                <select name="cardtype_id[]" lay-verify="required" lay-search lay-filter="cardtype_id">
                                    <option value="">请选择</option>
                                    {foreach name='cardtype_list' key='key' item='value'}
                                    <option value="{$value['id']}"> {$value["name"]}</option>
                                    {/foreach}
                                </select>
                            </td>
                            <td class="card_level_id">
                                <select name="card_level_id[]" lay-verify="required" lay-search lay-filter="card_level_id">
                                    <option value="">请选择</option>
                                    {foreach name='card_level' key='key' item='value'}
                                    <option value="{$value['id']}"> {$value["title"]}</option>
                                    {/foreach}
                                </select>
                            </td>
                            <td class="card_num"><input name="card_num[]" class="layui_input" placeholder="数量"/></td>
                            <td class="begin_card"><input name="begin_card[]" class="layui_input" /></td>
                            <td class="end_card"><input name="end_card[]" class="layui_input" /></td>
                            <td class="market_price"><input name="market_price[]" class="layui_input" disabled
                                    style="background-color: #f0f0f0;" /></td>
                            <td class="zhekou"><input name="zhekou[]" class="layui_input" value="100" /></td>
                            <td class="end_price"><input name="end_price[]" class="layui_input" /></td>
                            <td class="all_price"><input name="all_price[]" class="layui_input" disabled
                                    style="background-color: #f0f0f0;" /></td>
                            <td><a class="layui-btn layui-btn-sm del_tr">删除</a></td>
                        </tr>
                    </tbody>
                </table>

            </div>
            <div class="layui-input-inline layui-input-company"></div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>

        <div class="layui-form-item" style="display: none;" id="sub">
            <div class="layui-input-block">
                <button class="layui-btn  layui-btn-sm" lay-submit lay-filter="formDemo">确认销售</button>
            </div>
        </div>
    </form>
    <script>
        var data2 = "";
        $.ajax({
            type: 'post',
            url: "{:url('ajax_card_customer')}",
            dataType: 'json',
            success: function (data) {
                $('#customer_id').autocompleter({ source: data });
            }
        });
    </script>
    <script>

        //Demo
        layui.use('form', function () {
            var form = layui.form;
            var upload = layui.upload;
            var ischeck = false;


            form.on('submit(formDemo)', function (data) {
                var index = layer.load(0, {
                    shade: [0.4, 'black'] //0.1透明度的白色背景
                });
                $.post("{:url('ajax_pu_card')}", data.field, function (data) {
                    if (data.sta == 1) {
                        layer.msg(data.msg, { time: 1500 }, function () {
                            layer.close(index);
                            window.location.href = "{:url('pu_card_list')}";
                        });
                    } else {
                        layer.msg(data.msg);
                        layer.close(index);
                    }
                }, "json");
                return false;
            });

            // 通用的计算结束卡号函数
            function calculateEndCard(begin_card, card_num) {
                if (begin_card == '' || isNaN(card_num) || card_num <= 0) {
                    return null;
                }

                // 处理字母前缀+数字格式的卡号
                var matches = begin_card.match(/^([A-Za-z]*)(\d+)$/);
                if (matches && matches.length >= 3) {
                    var prefix = matches[1]; // 字母前缀
                    var begin_num = parseInt(matches[2]); // 数字部分
                    var numLength = matches[2].length; // 数字部分长度，用于补零

                    console.log("计算结束卡号 - 匹配结果：", "前缀="+prefix, "数字="+begin_num, "长度="+numLength);

                    if (!isNaN(begin_num)) {
                        var end_num = begin_num + card_num - 1;
                        // 设置结束卡号，保持数字部分相同长度（前导零）
                        var end_card = prefix + String(end_num).padStart(numLength, '0');
                        console.log("计算得到结束卡号：", end_card);
                        return end_card;
                    }
                } else {
                    console.log("卡号格式不匹配：", begin_card);
                }
                return null;
            }

            // 在输入数量后，自动计算结束卡号
            $(document).on('blur', "input[name^='card_num']", function () {
                var card_num = parseInt($(this).val());
                var tr = $(this).closest('tr');
                var begin_card = tr.find('.begin_card input').val();

                console.log("数量输入：", card_num, "开始卡号：", begin_card);

                if (begin_card != '' && !isNaN(card_num) && card_num > 0) {
                    var end_card = calculateEndCard(begin_card, card_num);
                    if (end_card) {
                        tr.find('.end_card input').val(end_card);
                        console.log("设置结束卡号：", end_card);

                        // 获取卡型和折扣信息
                        var cardtype_id = tr.find('.cardtype_id select').val();
                        var card_level_id = tr.find('.card_level_id select').val();
                        var zhekou = tr.find('.zhekou input').val();

                        // 如果已选择卡型，重新计算价格
                        if (cardtype_id) {
                            var rs = get_cardnum_and_price(cardtype_id, begin_card, end_card, zhekou, 0, 1, card_level_id);
                            if (rs && rs.sta == 1) {
                                tr.find('.zhekou input').val(rs.zhekou);
                                tr.find('.end_price input').val(rs.end_price);
                                tr.find('.all_price input').val(rs.all_price);
                                $("#sub").show();
                            }
                        }
                    }
                } else {
                    console.log("输入验证失败：", "card_num="+card_num, "begin_card="+begin_card);
                }
            });

            // 输入卡号范围，获取卡号段
            $(document).on('blur', "input[name^='begin_card']", function () {
                var begin_card = $(this).val();
                var tr = $(this).closest('tr');
                var end_card = tr.find('.end_card input').val();
                var cardtype_id = tr.find('.cardtype_id select').val();
                var card_level_id = tr.find('.card_level_id select').val();
                var zhekou = tr.find('.zhekou input').val();
                var end_price = tr.find('.end_price input').val();
                var card_num = parseInt(tr.find('.card_num input').val());

                console.log("开始卡号输入：", begin_card, "数量：", card_num);

                // 如果有数量但没有结束卡号，自动计算结束卡号
                if (begin_card != '' && !isNaN(card_num) && card_num > 0 && end_card == '') {
                    var calculated_end_card = calculateEndCard(begin_card, card_num);
                    if (calculated_end_card) {
                        tr.find('.end_card input').val(calculated_end_card);
                        end_card = calculated_end_card;
                        console.log("自动设置结束卡号：", calculated_end_card);
                    }
                }

                // 如果卡型、开始卡号和结束卡号都有值，计算价格
                if (cardtype_id && begin_card && end_card) {
                    var rs = get_cardnum_and_price(cardtype_id, begin_card, end_card, zhekou, end_price, 1, card_level_id);
                    console.log(rs);
                    if (rs == false) {
                        $("#sub").hide();
                        return false
                    }
                    if (rs.sta != 1) {
                        layer.msg(rs.msg);
                        $("#sub").hide();
                        return false;
                    }
                    $(this).parent().next().next().next().children().val(rs.zhekou);
                    $(this).parent().next().next().next().next().children().val(rs.end_price);
                    $(this).parent().next().next().next().next().next().children().val(rs.all_price);
                    $("#sub").show();
                }
            })
            $(document).on('blur', "input[name^='end_card']", function () {
                var tr = $(this).closest('tr');
                var end_card = $(this).val();
                var begin_card = tr.find('.begin_card input').val();
                var cardtype_id = tr.find('.cardtype_id select').val();
                var card_level_id = tr.find('.card_level_id select').val();
                var zhekou = tr.find('.zhekou input').val();
                var end_price = tr.find('.end_price input').val();

                if (cardtype_id && begin_card && end_card) {
                    var rs = get_cardnum_and_price(cardtype_id, begin_card, end_card, zhekou, end_price, 1, card_level_id);
                    if (rs == false) {
                        $("#sub").hide();
                        return false
                    }
                    if (rs.sta != 1) {
                        layer.msg(rs.msg);
                        $("#sub").hide();
                        return false;
                    }
                    tr.find('.zhekou input').val(rs.zhekou);
                    tr.find('.end_price input').val(rs.end_price);
                    tr.find('.all_price input').val(rs.all_price);
                    $("#sub").show();
                }
            })
            $(document).on('blur', "input[name^='zhekou']", function () {
                var tr = $(this).closest('tr');
                var begin_card = tr.find('.begin_card input').val();
                var end_card = tr.find('.end_card input').val();
                var cardtype_id = tr.find('.cardtype_id select').val();
                var card_level_id = tr.find('.card_level_id select').val();
                var zhekou = $(this).val();
                var end_price = tr.find('.end_price input').val();

                if (cardtype_id && begin_card && end_card) {
                    var rs = get_cardnum_and_price(cardtype_id, begin_card, end_card, zhekou, end_price, 1, card_level_id);
                    if (rs == false) {
                        $("#sub").hide();
                        return false
                    }
                    if (rs.sta != 1) {
                        layer.msg(rs.msg);
                        $("#sub").hide();
                        return false;
                    }
                    $(this).val(rs.zhekou);
                    tr.find('.end_price input').val(rs.end_price);
                    tr.find('.all_price input').val(rs.all_price);
                    $("#sub").show();
                }
            })
            $(document).on('blur', "input[name^='end_price']", function () {
                var tr = $(this).closest('tr');
                var begin_card = tr.find('.begin_card input').val();
                var end_card = tr.find('.end_card input').val();
                var cardtype_id = tr.find('.cardtype_id select').val();
                var card_level_id = tr.find('.card_level_id select').val();
                var zhekou = tr.find('.zhekou input').val();
                var end_price = $(this).val();

                if (cardtype_id && begin_card && end_card) {
                    var rs = get_cardnum_and_price(cardtype_id, begin_card, end_card, zhekou, end_price, 2, card_level_id);
                    if (rs == false) {
                        $("#sub").hide();
                        return false
                    }
                    if (rs.sta != 1) {
                        layer.msg(rs.msg);
                        $("#sub").hide();
                        return false;
                    }
                    tr.find('.zhekou input').val(rs.zhekou);
                    $(this).val(rs.end_price);
                    tr.find('.all_price input').val(rs.all_price);
                    $("#sub").show();
                }
            })

            // 计算合计
            function get_cardnum_and_price(cardtype_id, begin_card, end_card, zhekou, end_price, type = 1, card_level_id = '') {
                if (cardtype_id == '' || begin_card == '' || end_card == '') {
                    return false;
                }
                if (zhekou == '' && end_price == '') {
                    return false;
                }
                $.ajaxSettings.async = false;
                var rs = "";
                // console.log(cardtype_id, begin_card, end_card, zhekou, end_price);
                $.post("{:url('get_pu_card_mingxi')}", {
                    cardtype_id: cardtype_id,
                    card_level_id: card_level_id,
                    begin_card: begin_card,
                    end_card: end_card,
                    zhekou: zhekou,
                    end_price: end_price,
                    type: type
                }, function (data) {
                    rs = data;
                }, "json");
                $.ajaxSettings.async = true;
                return rs;
            }

            $(".add_tr").click(function () {
                var html = "";
                html += "<tr>";
                html += $("#base_tr").html();
                html += "</tr>";
                $("#tbody").append(html);
                form.render();
            })
            $(document).on('click', '.del_tr', function () {
                if ($(this).parent().parent().hasClass("base_tr")) {
                    layer.msg("不可删除"); return false;
                }
                $(this).parent().parent().remove();
            });

            // 切换客户
            $("#customer_id").blur(function () {
                var value = $(this).val();
                if (value == "") {
                    $("[name=linkman]").val("");
                    $("[name=linktel]").val("");
                    $("[name=sale_name]").val("");
                    layer.msg("请选择客户"); return false;
                }
                $.post("{:url('get_customer_info')}", { customer_name: value }, function (data) {
                    if (data.sta == 1) {
                        $("[name=linkman]").val(data.linkman);
                        $("[name=linktel]").val(data.linktel);
                        $("[name=sale_name]").val(data.sale_name);
                    } else {
                        layer.msg(data.msg);
                    }
                }, "json");
                form.render();
            })

            // 切换是否自动开卡
            // form.on('select(is_auto_open)', function (data) {
            //     var value = data.value;
            //     if (value == 0) {
            //         $("#begin_start_show").hide();
            //         $("#end_time_show").hide();
            //     } else if (value == 1) {
            //         $("#begin_start_show").show();
            //         $("#end_time_show").show();
            //     }
            // });

            // 切换卡型获取金额
            form.on('select(cardtype_id)', function (data) {
                var elem = data.elem;
                var value = data.value;
                if (!value) {
                    return false; // 如果没有选择卡型，不执行后续代码
                }

                $.post("{:url('get_card_type_info')}", { cardtype_id: value }, function (data) {
                    if (data.sta == 1) {
                        var tr = $(elem).closest('tr');
                        tr.find('.market_price input').val(data.market_price);

                        // 获取一下是否有卡号
                        var card_level_id = tr.find('.card_level_id select').val();
                        var begin_card = tr.find('.begin_card input').val();
                        var end_card = tr.find('.end_card input').val();
                        var zhekou = tr.find('.zhekou input').val();
                        var end_price = tr.find('.end_price input').val();

                        // 如果卡号已填写，则计算价格
                        if (begin_card && end_card) {
                            var rs = get_cardnum_and_price(value, begin_card, end_card, zhekou, end_price, 1, card_level_id);
                            if (rs == false) {
                                $("#sub").hide();
                                return false
                            }
                            if (rs.sta != 1) {
                                layer.msg(rs.msg);
                                $("#sub").hide();
                                return false;
                            }
                            tr.find('.zhekou input').val(rs.zhekou);
                            tr.find('.end_price input').val(rs.end_price);
                            tr.find('.all_price input').val(rs.all_price);
                            $("#sub").show();
                        } else {
                            // 如果没有填写卡号，设置默认折扣和价格
                            var market_price = parseFloat(data.market_price);
                            var zhekou_val = 100; // 默认折扣100%
                            var end_price = market_price; // 没有折扣时最终价格等于市场价

                            tr.find('.zhekou input').val(zhekou_val);
                            tr.find('.end_price input').val(end_price.toFixed(2));
                            // 不设置总价，因为没有卡数量
                        }
                    } else {
                        layer.msg(data.msg);
                    }
                }, "json");
            });

            // 切换卡等级
            form.on('select(card_level_id)', function (data) {
                var elem = data.elem;
                var value = data.value;

                // 如果已有卡号，则重新计算价格
                var tr = $(elem).closest('tr');
                var cardtype_id = tr.find('.cardtype_id select').val();
                var begin_card = tr.find('.begin_card input').val();
                var end_card = tr.find('.end_card input').val();

                if(cardtype_id && begin_card && end_card) {
                    var zhekou = tr.find('.zhekou input').val();
                    var end_price = tr.find('.end_price input').val();
                    var rs = get_cardnum_and_price(cardtype_id, begin_card, end_card, zhekou, end_price, 1, value);
                    if (rs == false) {
                        $("#sub").hide();
                        return false;
                    }
                    if (rs.sta != 1) {
                        layer.msg(rs.msg);
                        $("#sub").hide();
                        return false;
                    }
                    tr.find('.zhekou input').val(rs.zhekou);
                    tr.find('.end_price input').val(rs.end_price);
                    tr.find('.all_price input').val(rs.all_price);
                    $("#sub").show();
                }
            });

        });


        layui.use('laydate', function () {
            var laydate = layui.laydate;

            //执行一个laydate实例
            laydate.render({
                elem: '#begin_start' //指定元素
            });
            laydate.render({
                elem: '#end_time' //指定元素
            });

        });

    </script>


</body>

</html>
