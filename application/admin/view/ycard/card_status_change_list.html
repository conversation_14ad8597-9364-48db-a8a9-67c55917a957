<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}

<body>
<br/>
<div style="margin-left: 20px;">
    <div class="page-content">
        <div class="demoTable layui-form">
            <label class="layui-form-label">查询条件：</label>
            <form class="layui-inline">
                <select name='field' class='form-control' lay-filter="test">
                    <option value="">全部</option>
                    <option value="custom">客户</option>
                    <option value="market">销售员</option>
                </select>
            </form>
            <div class='layui-inline'>
                <input class="layui-input" name="keyword" id="reload" autocomplete="off">
            </div>
            <label class="layui-inline">操作时间：</label>
            <div class='layui-inline' id="time">
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="开始时间" id="start_time" name="start_time">
                </div>
                <span>-</span>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="截止时间" id="end_time" name="end_time">
                </div>
            </div>
            <button class="layui-btn  layui-btn-sm" data-type="reload">搜索</button>
<!--            <button class="layui-btn  layui-btn-sm" onclick="export_card()">导出</button>-->
        </div>

        <table class="layui-hide" id="demo" lay-filter="demo">
        </table>
        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-sm" lay-event="select_card">查看卡号</a>
        </script>
    </div>

</div>

</body>

</html>
<script type="text/javascript">

    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#end_time' //指定元素
        });
        laydate.render({
            elem: '#start_time' //指定元素
        });

    });

    layui.use('table', function () {

        var table = layui.table;
        //方法级渲染
        table.render({
            elem: '#demo'
            , url: "{:url('ajax_card_status_change_list')}"
            , cols: [[
                {type: 'numbers', title: '序号'}
                , {field: 'add_time', title: '操作时间'}
                , {field: 'operator', title: '操作人'}
                , {field: 'customer_name', title: '客户'}
                , {field: 'sale_name', title: '销售员'}
                , {field: 'status', title: '状态'}
                , {field: 'card_count', title: '更改数量'}
                , {field: 'remarks', title: '备注'}
                , { fixed: 'right', title: "操作", align: 'center', toolbar: '#barDemo' }
            ]]
            , id: 'testReload'
            , page: true
        });

        table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
            var data = obj.data //获得当前行数据
                , layEvent = obj.event; //获得 lay-event 对应的值
            var no = data.no;
            if (layEvent === 'select_card') {
                layer.open({
                    type: 2,
                    title: "查看卡号",
                    content: "{:url('card_status_change_detail')}?no=" + no,
                    area: ["99%", "99%"]
                })
            }
        });

        var $ = layui.$, active = {
            reload: function () {
                var field = $("[name=field]").val();
                var keyword = $("[name=keyword]").val();
                var start_time = $("[name=start_time]").val();
                var end_time = $("[name=end_time]").val();
                //执行重载
                table.reload('testReload', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    , where: {
                        field: field,
                        keyword: keyword,
                        start_time: start_time,
                        end_time: end_time
                    }
                });
            },
        };

        $('.demoTable .layui-btn').on('click', function () {
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    })

    function export_card(){
        var field = $("[name=field]").val();
        var keyword = $("[name=keyword]").val();
        var start_time = $("[name=start_time]").val();
        var end_time = $("[name=end_time]").val();
        window.location.href = "{:url('export_card_status_change')}?field=" + field + "&keyword=" + keyword +
            "&start_time=" + start_time + "&end_time=" + end_time;
    }


</script>
