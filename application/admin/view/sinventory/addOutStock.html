{include file="public/iframeheader"/}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">添加出库单</div>
        <div class="layui-card-body">
            <form class="layui-form" action="" lay-filter="form-data">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">业务类型</label>
                        <div class="layui-input-inline">
                            <select name="business_type" lay-verify="required" lay-filter="business_type">
                                <option value="">请选择业务类型</option>
                                <option value="1">销售出库</option>
                                <option value="2">调拨出库</option>
                                <option value="3">报损出库</option>
                                <option value="4">其他出库</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关联单号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="related_order_no" placeholder="请输入关联单号" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-card">
                        <div class="layui-card-header">商品信息</div>
                        <div class="layui-card-body">
                            <div class="layui-btn-group" style="margin-bottom: 10px;">
                                <button type="button" class="layui-btn layui-btn-sm" id="add-product-btn">
                                    <i class="layui-icon">&#xe654;</i> 添加商品
                                </button>
                            </div>
                            
                            <table class="layui-table" id="product-table" lay-filter="product-table">
                                <thead>
                                    <tr>
                                        <th>商品名称</th>
                                        <th>规格</th>
                                        <th>出库数量</th>
                                        <th>单价(元)</th>
                                        <th>金额(元)</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 商品数据行将动态添加到这里 -->
                                </tbody>
                            </table>
                            
                            <div class="layui-form-item">
                                <div class="layui-inline" style="float: right;">
                                    <label class="layui-form-label">总金额</label>
                                    <div class="layui-input-inline">
                                        <input type="text" name="total_amount" id="total_amount" value="0.00" readonly class="layui-input">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="form-submit">保存</button>
                        <button type="button" class="layui-btn layui-btn-primary" id="cancel-btn">取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 商品选择模板 -->
<script type="text/html" id="product-select-tpl">
    <div class="layui-form" lay-filter="product-select-form" style="padding: 20px;">
        <div class="layui-form-item">
            <label class="layui-form-label">商品名称</label>
            <div class="layui-input-block">
                <select name="product_id" lay-verify="required" lay-filter="product_id">
                    <option value="">请选择商品</option>
                    {{# layui.each(d.products, function(index, item){ }}
                    <option value="{{ item.id }}">{{ item.product_name }}</option>
                    {{# }); }}
                </select>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">规格</label>
            <div class="layui-input-block">
                <select name="inventory_id" lay-verify="required" lay-filter="inventory_id">
                    <option value="">请先选择商品</option>
                </select>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">库存</label>
            <div class="layui-input-block">
                <input type="text" name="stock" value="0" readonly class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">出库数量</label>
            <div class="layui-input-block">
                <input type="number" name="quantity" value="1" min="1" lay-verify="required|number|stock" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">单价(元)</label>
            <div class="layui-input-block">
                <input type="number" name="price" value="0.00" step="0.01" min="0" lay-verify="required|number" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="product-select-submit">确定</button>
                <button type="button" class="layui-btn layui-btn-primary" id="product-select-cancel">取消</button>
            </div>
        </div>
    </div>
</script>

<script>
    layui.use(['form', 'layer', 'laytpl'], function() {
        var form = layui.form,
            layer = layui.layer,
            laytpl = layui.laytpl,
            $ = layui.jquery;
        
        // 商品数据数组
        var productData = [];
        // 总金额
        var totalAmount = 0;
        
        // 取消按钮点击事件
        $('#cancel-btn').on('click', function() {
            history.back();
        });
        
        // 添加商品按钮点击事件
        $('#add-product-btn').on('click', function() {
            // 获取可用商品数据
            $.ajax({
                url: '{:url("sinventory/getProductsForOutStock")}',
                type: 'GET',
                dataType: 'json',
                success: function(res) {
                    if (res.code === 0) {
                        // 渲染商品选择表单
                        var getTpl = document.getElementById('product-select-tpl').innerHTML;
                        laytpl(getTpl).render({products: res.data}, function(html) {
                            // 打开商品选择层
                            layer.open({
                                type: 1,
                                title: '选择商品',
                                area: ['500px', 'auto'],
                                content: html,
                                success: function() {
                                    form.render(null, 'product-select-form');
                                    
                                    // 商品选择变更事件
                                    form.on('select(product_id)', function(data) {
                                        if (!data.value) return;
                                        
                                        // 获取商品规格数据
                                        $.ajax({
                                            url: '{:url("sinventory/getInventoriesByProductId")}',
                                            type: 'GET',
                                            data: {product_id: data.value},
                                            dataType: 'json',
                                            success: function(res) {
                                                if (res.code === 0) {
                                                    // 重置规格下拉框
                                                    var options = '<option value="">请选择规格</option>';
                                                    layui.each(res.data, function(index, item) {
                                                        options += '<option value="' + item.id + '" data-stock="' + item.stock + '" data-price="' + item.price + '">' + item.title + '</option>';
                                                    });
                                                    $('select[name=inventory_id]').html(options);
                                                    form.render('select');
                                                } else {
                                                    layer.msg(res.msg, {icon: 2});
                                                }
                                            }
                                        });
                                    });
                                    
                                    // 规格选择变更事件
                                    form.on('select(inventory_id)', function(data) {
                                        if (!data.value) return;
                                        
                                        // 获取选中的库存和价格
                                        var stock = $(data.elem).find("option:selected").data('stock');
                                        var price = $(data.elem).find("option:selected").data('price');
                                        
                                        // 更新表单中的库存和价格值
                                        $('input[name=stock]').val(stock);
                                        $('input[name=price]').val(price.toFixed(2));
                                        
                                        // 重置数量为1并限制最大值
                                        $('input[name=quantity]').val(1).attr('max', stock);
                                    });
                                    
                                    // 添加库存验证规则
                                    form.verify({
                                        stock: function(value) {
                                            var stock = parseInt($('input[name=stock]').val());
                                            if (parseInt(value) > stock) {
                                                return '出库数量不能超过库存数量';
                                            }
                                        }
                                    });
                                    
                                    // 商品选择提交事件
                                    form.on('submit(product-select-submit)', function(data) {
                                        var formData = data.field;
                                        
                                        // 检查是否已经添加过相同商品规格
                                        var exists = false;
                                        layui.each(productData, function(index, item) {
                                            if (item.inventory_id == formData.inventory_id) {
                                                exists = true;
                                                return false;
                                            }
                                        });
                                        
                                        if (exists) {
                                            layer.msg('该商品规格已添加', {icon: 2});
                                            return false;
                                        }
                                        
                                        // 获取商品名称和规格名称
                                        var productText = $('select[name=product_id] option:selected').text();
                                        var inventoryText = $('select[name=inventory_id] option:selected').text();
                                        
                                        // 计算金额
                                        var quantity = parseFloat(formData.quantity);
                                        var price = parseFloat(formData.price);
                                        var amount = quantity * price;
                                        
                                        // 添加商品数据
                                        var productItem = {
                                            product_id: formData.product_id,
                                            product_name: productText,
                                            inventory_id: formData.inventory_id,
                                            inventory_name: inventoryText,
                                            quantity: quantity,
                                            price: price,
                                            amount: amount
                                        };
                                        
                                        productData.push(productItem);
                                        
                                        // 更新商品表格
                                        updateProductTable();
                                        
                                        // 关闭弹窗
                                        layer.closeAll();
                                        
                                        return false;
                                    });
                                    
                                    // 取消按钮事件
                                    $('#product-select-cancel').on('click', function() {
                                        layer.closeAll();
                                    });
                                }
                            });
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }
            });
        });
        
        // 更新商品表格
        function updateProductTable() {
            var html = '';
            totalAmount = 0;
            
            layui.each(productData, function(index, item) {
                html += '<tr data-index="' + index + '">' +
                        '<td>' + item.product_name + '</td>' +
                        '<td>' + item.inventory_name + '</td>' +
                        '<td>' + item.quantity + '</td>' +
                        '<td>' + item.price.toFixed(2) + '</td>' +
                        '<td>' + item.amount.toFixed(2) + '</td>' +
                        '<td>' +
                        '<a class="layui-btn layui-btn-xs layui-btn-danger delete-btn" data-index="' + index + '">删除</a>' +
                        '</td>' +
                        '</tr>';
                
                totalAmount += item.amount;
            });
            
            // 更新表格和总金额
            $('#product-table tbody').html(html);
            $('#total_amount').val(totalAmount.toFixed(2));
            
            // 绑定删除按钮事件
            $('.delete-btn').off('click').on('click', function() {
                var index = $(this).data('index');
                // 删除数据并更新表格
                productData.splice(index, 1);
                updateProductTable();
            });
        }
        
        // 表单提交事件
        form.on('submit(form-submit)', function(data) {
            var formData = data.field;
            
            // 校验商品数据
            if (productData.length === 0) {
                layer.msg('请至少添加一个商品', {icon: 2});
                return false;
            }
            
            // 准备提交的数据
            var submitData = {
                business_type: formData.business_type,
                related_order_no: formData.related_order_no,
                remark: formData.remark,
                total_amount: totalAmount,
                details: productData
            };
            
            // 发送请求
            $.ajax({
                url: '{:url("sinventory/addOutStock")}',
                type: 'POST',
                data: submitData,
                dataType: 'json',
                success: function(res) {
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1, time: 1000}, function() {
                            // 跳转到出库单列表页
                            window.location.href = '{:url("sinventory/outstock")}';
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }
            });
            
            return false;
        });
    });
</script> 