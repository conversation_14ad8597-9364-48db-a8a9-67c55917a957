{include file="public/iframeheader"/}
<style>
    .layui-table-cell{
        height: inherit;
    }
    
    /* 统计卡片样式 */
    .stats-card .layui-card-header {
        font-weight: bold;
        font-size: 16px;
        padding: 12px 15px;
        text-align: center;
    }
    .stats-card .layui-card-body {
        padding: 20px 15px;
        text-align: center;
    }
    .stats-card h2 {
        font-size: 24px;
        margin: 0;
        padding: 0;
    }
    .stats-card .stats-icon {
        font-size: 20px;
        margin-right: 5px;
    }
    .stats-card .in-stock-icon {
        color: #009688;
    }
    .stats-card .out-stock-icon {
        color: #FF5722;
    }
    .stats-card .diff-icon {
        color: #1E9FFF;
    }
    .stats-detail {
        margin-top: 10px;
        font-size: 14px;
        color: #666;
    }
    .stats-card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
    }
</style>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">门店库存变动日志</div>
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">商品</label>
                        <div class="layui-input-inline">
                            <input type="text" name="product_search" id="product_search" autocomplete="off" class="layui-input" placeholder="搜索商品">
                            <input type="hidden" name="product_id" id="product_id">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">规格</label>
                        <div class="layui-input-inline">
                            <select name="inventory_id" id="inventory_id">
                                <option value="">全部规格</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">变动类型</label>
                        <div class="layui-input-inline">
                            <select name="change_type" id="change_type">
                                <option value="">全部</option>
                                <option value="1">入库</option>
                                <option value="2">出库</option>
                                <option value="3">调拨入</option>
                                <option value="4">调拨出</option>
                                <option value="5">销售</option>
                                <option value="6">退货</option>
                                <option value="7">盘点</option>
                                <option value="8">其他</option>
                                <option value="9">其他入库</option>
                                <option value="10">其他出库</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">日期范围</label>
                        <div class="layui-input-inline">
                            <input type="text" name="date_range" id="date_range" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="search-btn">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button class="layui-btn layui-btn-primary" id="reset-btn">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计卡片区域 -->
            <div class="layui-row layui-col-space15" style="margin-top: 15px; margin-bottom: 15px;">
                <div class="layui-col-md4">
                    <div class="layui-card stats-card">
                        <div class="layui-card-header">总入库数量</div>
                        <div class="layui-card-body">
                            <h2 id="total-in-stock">0</h2>
                            <div class="stats-detail">
                                <span>普通商品：<span id="normal-in-stock">0</span>件</span>
                                <span style="margin-left: 15px;">计量商品：<span id="weight-in-stock">0</span>kg</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="layui-card stats-card">
                        <div class="layui-card-header">总出库数量</div>
                        <div class="layui-card-body">
                            <h2 id="total-out-stock">0</h2>
                            <div class="stats-detail">
                                <span>普通商品：<span id="normal-out-stock">0</span>件</span>
                                <span style="margin-left: 15px;">计量商品：<span id="weight-out-stock">0</span>kg</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="layui-card stats-card">
                        <div class="layui-card-header">出入库差值</div>
                        <div class="layui-card-body">
                            <h2 id="stock-difference">0</h2>
                            <div class="stats-detail">
                                <span>普通商品：<span id="normal-difference">0</span>件</span>
                                <span style="margin-left: 15px;">计量商品：<span id="weight-difference">0</span>kg</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table id="data-table" lay-filter="data-table"></table>
        </div>
    </div>
</div>

<script>
    // 配置 layui 模块路径
    layui.config({
        base: '__STATIC__/admin/js/'
    }).use(['table', 'form', 'laydate', 'autocomplete'], function() {
        var table = layui.table,
            form = layui.form,
            laydate = layui.laydate,
            autocomplete = layui.autocomplete,
            $ = layui.jquery;

        // 日期范围选择器
        laydate.render({
            elem: '#date_range',
            range: true
        });

        // 商品搜索自动完成
        autocomplete.render({
            elem: '#product_search',
            url: '{:url("sinventory/searchProduct")}',
            cache: false,
            template_val: '{{d.name}}',
            onselect: function (resp) {
                $('#product_id').val(resp.id);
                // 加载该商品的规格
                loadInventoryOptions(resp.id);
            }
        });

        // 加载商品规格选项
        function loadInventoryOptions(productId) {
            if (!productId) {
                $('#inventory_id').html('<option value="">全部规格</option>');
                form.render('select');
                return;
            }
            
            $.ajax({
                url: '{:url("sinventory/getInventoryByProduct")}',
                type: 'get',
                data: {product_id: productId},
                dataType: 'json',
                success: function(res) {
                    if (res.code === 0) {
                        var options = '<option value="">全部规格</option>';
                        $.each(res.data, function(index, item) {
                            options += '<option value="' + item.id + '">' + item.name + '</option>';
                        });
                        $('#inventory_id').html(options);
                        form.render('select');
                    } else {
                        layer.msg(res.msg || '获取规格失败');
                    }
                },
                error: function() {
                    layer.msg('网络错误，获取规格失败');
                }
            });
        }

        // 表格实例
        var tableIns = table.render({
            elem: '#data-table',
            url: '{:url("sinventory/log")}',
            defaultToolbar: ['filter', 'exports', 'print'],
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            cols: [[
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'created_at', title: '操作时间', sort: true},
                {field: 'created_by', title: '操作人'},
                {field: 'product_name', title: '商品名称'},
                {field: 'inventory_name', title: '商品规格'},
                {field: 'related_no', title: '关联单号'},
                {field: 'change_type_text', title: '变动类型', templet: function(d){
                    var className = '';
                    switch(d.change_type) {
                        case 1: className = 'layui-bg-green'; break; // 入库
                        case 2: className = 'layui-bg-orange'; break; // 出库
                        case 3: className = 'layui-bg-green'; break; // 调拨入
                        case 4: className = 'layui-bg-orange'; break; // 调拨出
                        case 5: className = 'layui-bg-orange'; break; // 销售
                        case 6: className = 'layui-bg-green'; break; // 退货
                        case 7: className = 'layui-bg-blue'; break; // 盘点
                        case 8: className = 'layui-bg-gray'; break; // 其他
                        case 9: className = 'layui-bg-green'; break; // 其他入库
                        case 10: className = 'layui-bg-orange'; break; // 其他出库
                    }
                    return '<span class="layui-badge ' + className + '">' + d.change_type_text + '</span>';
                }},
                {field: 'quantity_before', title: '变动前数量'},
                {field: 'quantity_change', title: '变动数量', templet: function(d){
                    var change = parseFloat(d.quantity_change);
                    if(change > 0) {
                        return '<span style="color: green;">+' + change + '</span>';
                    } else {
                        return '<span style="color: red;">' + change + '</span>';
                    }
                }},
                {field: 'quantity_after', title: '变动后数量'},
                {field: 'remark', title: '备注'}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res) {
                // 处理统计数据
                if(res.stats !== undefined) {
                    // 格式化数字，添加千位分隔符
                    function formatNumber(num) {
                        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                    }
                    
                    // 设置入库总数
                    $('#total-in-stock').text(formatNumber(res.stats.total_in_stock || 0));
                    $('#normal-in-stock').text(formatNumber(res.stats.normal_in_stock || 0));
                    $('#weight-in-stock').text(formatNumber(res.stats.weight_in_stock || 0));
                    
                    // 设置出库总数
                    $('#total-out-stock').text(formatNumber(res.stats.total_out_stock || 0));
                    $('#normal-out-stock').text(formatNumber(res.stats.normal_out_stock || 0));
                    $('#weight-out-stock').text(formatNumber(res.stats.weight_out_stock || 0));
                    
                    // 设置差值，并根据正负值设置颜色
                    var difference = res.stats.difference || 0;
                    var differenceText = formatNumber(difference);
                    if (difference > 0) {
                        $('#stock-difference').text(differenceText).css('color', '#009688'); // 正值显示绿色
                    } else if (difference < 0) {
                        $('#stock-difference').text(differenceText).css('color', '#FF5722'); // 负值显示红色
                    } else {
                        $('#stock-difference').text(differenceText).css('color', ''); // 零值使用默认颜色
                    }
                    
                    // 设置普通商品差值
                    var normalDiff = res.stats.normal_difference || 0;
                    $('#normal-difference').text(formatNumber(normalDiff));
                    if (normalDiff > 0) {
                        $('#normal-difference').css('color', '#009688');
                    } else if (normalDiff < 0) {
                        $('#normal-difference').css('color', '#FF5722');
                    } else {
                        $('#normal-difference').css('color', '');
                    }
                    
                    // 设置计量商品差值
                    var weightDiff = res.stats.weight_difference || 0;
                    $('#weight-difference').text(formatNumber(weightDiff));
                    if (weightDiff > 0) {
                        $('#weight-difference').css('color', '#009688');
                    } else if (weightDiff < 0) {
                        $('#weight-difference').css('color', '#FF5722');
                    } else {
                        $('#weight-difference').css('color', '');
                    }
                }
                
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.count,
                    "data": res.data
                };
            }
        });

        // 搜索按钮点击事件
        $('#search-btn').on('click', function() {
            var param = {
                product_id: $('#product_id').val(),
                inventory_id: $('#inventory_id').val(),
                change_type: $('#change_type').val(),
                date_range: $('#date_range').val()
            };
            
            table.reload('data-table', {
                where: param,
                page: {
                    curr: 1
                }
            });
        });

        // 重置按钮点击事件
        $('#reset-btn').on('click', function() {
            $('#product_search').val('');
            $('#product_id').val('');
            $('#inventory_id').html('<option value="">全部规格</option>');
            $('#change_type').val('');
            $('#date_range').val('');
            form.render('select');
            
            $('#search-btn').click();
        });
    });
</script> 