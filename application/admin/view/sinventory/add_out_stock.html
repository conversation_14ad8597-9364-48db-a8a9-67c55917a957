{include file="public/iframeheader"/}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">添加出库单</div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="form-add" action="">
                <input type="hidden" name="order_type" value="2">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">业务类型<span style="color: red;font-size: 16px;">*</span></label>
                        <div class="layui-input-inline">
                            <select name="business_type" lay-verify="required" lay-filter="business_type">
                                <option value="">请选择业务类型</option>
                                <option value="1">销售出库</option>
                                <option value="2">调拨出库</option>
                                <option value="3">报损出库</option>
                                <option value="4">其他出库</option>
                            </select>
                        </div>
                    </div>
                    <!-- <div class="layui-inline">
                        <label class="layui-form-label">关联单号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="related_order_no" placeholder="请输入关联单号" autocomplete="off" class="layui-input">
                        </div>
                    </div> -->
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-inline customer-field">
                        <label class="layui-form-label">相关单位<span style="color: red;font-size: 16px;">*</span></label>
                        <div class="layui-input-inline"  style="width: 400px;">
                            <input type="text" name="supplier_name" placeholder="请输入相关单位名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            商品明细<span style="color: red;font-size: 16px;">*</span>
                            <div class="layui-btn-group">
                                <button type="button" class="layui-btn layui-btn-sm" id="add-detail-btn">
                                    <i class="layui-icon">&#xe654;</i>添加一行
                                </button>
                            </div>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table" id="detail-table">
                                <thead>
                                    <tr>
                                        <th width="25%">商品</th>
                                        <th width="12%">规格</th>
                                        <th width="8%">类型</th>
                                        <th width="8%">库存</th>
                                        <th width="10%">数量/重量</th>
                                        <th width="10%">单价</th>
                                        <th width="10%">金额</th>
                                        <th width="9%">备注</th>
                                        <th width="8%">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 明细行将动态添加到这里 -->
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="6" style="text-align: right;">合计：</td>
                                        <td width="10%"><span id="total-amount">0.00</span></td>
                                        <td width="9%"></td>
                                        <td width="8%"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn" lay-submit lay-filter="save-btn">保存为草稿</button>
                        <button type="button" class="layui-btn layui-btn-normal" lay-submit lay-filter="submit-btn">保存并提交</button>
                        <button type="button" class="layui-btn layui-btn-primary" id="cancel-btn">取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="/static/admin/lay-module/xm-select.js"></script>

<script>
    layui.use(['form', 'table', 'layer', 'laytpl'], function() {
        var form = layui.form,
            table = layui.table,
            layer = layui.layer,
            laytpl = layui.laytpl,
            $ = layui.jquery;
            
        // 业务类型切换事件
        form.on('select(business_type)', function(data) {
            if (data.value == '1') { // 销售出库
                $('.customer-field').show();
            } else {
                $('.customer-field').hide();
                $('input[name="supplier_name"]').val('');
            }
        });
            
        // 表单提交处理
        form.on('submit(save-btn)', function(data) {
            saveOrder(data.field, 0);
            return false;
        });
        
        form.on('submit(submit-btn)', function(data) {
            saveOrder(data.field, 1);
            return false;
        });
        
        // 保存订单
        function saveOrder(formData, submitFlag) {
            // 获取明细
            var details = [];
            var hasError = false;
            
            $('#detail-table tbody tr').each(function() {
                var $tr = $(this);
                var productId = $tr.data('product-id');
                if (!productId) {
                    layer.msg('请选择商品', {icon: 2});
                    hasError = true;
                    return false;
                }
                
                var quantity = parseFloat($tr.find('.detail-quantity').val());
                if (isNaN(quantity) || quantity <= 0) {
                    layer.msg('商品数量必须大于0', {icon: 2});
                    hasError = true;
                    return false;
                }
                
                var maxStock = parseFloat($tr.data('max-stock')) || 0;
                var isWeightProduct = $tr.data('is-weight-product') || false;
                var weightUnit = $tr.data('weight-unit') || 'kg';
                
                if (quantity > maxStock) {
                    var productName = $tr.find('.product-name').text() || '';
                    var unitText = isWeightProduct ? weightUnit : 'pcs';
                    layer.msg(productName + ' 库存不足，当前库存: ' + maxStock + unitText + 
                             '，' + (isWeightProduct ? '出库重量: ' : '出库数量: ') + quantity + unitText, {icon: 2});
                    hasError = true;
                    return false;
                }
                
                details.push({
                    product_id: productId,
                    inventory_id: $tr.data('inventory-id') || 0,
                    quantity: quantity,
                    price: parseFloat($tr.find('.detail-price').val()),
                    amount: parseFloat($tr.find('.detail-amount').text()),
                    remark: $tr.find('.detail-remark').val()
                });
            });
            
            if (hasError) {
                return;
            }
            
            if (details.length === 0) {
                layer.msg('请至少添加一个商品明细', {icon: 2});
                return;
            }
            
            // 设置总金额
            formData.total_amount = $('#total-amount').text();
            formData.details = details;
            
            // 发送请求
            $.ajax({
                url: '{:url("sinventory/addOutStock")}',
                type: 'post',
                data: formData,
                dataType: 'json',
                success: function(res) {
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        if (submitFlag === 1) {
                            // 提交订单
                            $.ajax({
                                url: '{:url("sinventory/submit")}',
                                type: 'post',
                                data: {id: res.id},
                                dataType: 'json',
                                success: function(submitRes) {
                                    if (submitRes.code === 0) {
                                        layer.msg(submitRes.msg, {icon: 1});
                                        setTimeout(function() {
                                            window.location.href = '{:url("sinventory/outstock")}';
                                        }, 1500);
                                    } else {
                                        layer.msg(submitRes.msg, {icon: 2});
                                    }
                                }
                            });
                        } else {
                            setTimeout(function() {
                                window.location.href = '{:url("sinventory/outstock")}';
                            }, 1500);
                        }
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }
            });
        }
        
        // 添加明细按钮点击事件
        $('#add-detail-btn').on('click', function() {
            var index = $('#detail-table tbody tr').length;
            var html = '<tr>' +
                '<td width="25%">' +
                '<div id="product-select-' + index + '" class="xm-select-demo"></div>' +
                '<span class="product-name" style="display:none;"></span>' +
                '</td>' +
                '<td width="12%" class="product-spec"></td>' +
                '<td width="8%"><span class="detail-type">-</span></td>' +
                '<td width="8%">' +
                '<div style="display: flex; align-items: center;">' +
                '<span class="stock-quantity">0</span>' +
                '<span class="stock-unit" style="margin-left: 3px; font-size: 11px; color: #999;">pcs</span>' +
                '</div>' +
                '</td>' +
                '<td width="10%">' +
                '<div style="display: flex; align-items: center;">' +
                '<input type="number" class="layui-input detail-quantity" value="1" min="0.01" step="0.01" style="flex: 1;" onchange="checkStockAndCalculate(this)">' +
                '<span class="quantity-unit" style="margin-left: 5px; font-size: 12px; color: #999;">pcs</span>' +
                '</div>' +
                '</td>' +
                '<td width="10%"><input type="number" class="layui-input detail-price" value="0" min="0" step="0.01" style="width: 100%;" onchange="calculateRowAmount(this)"></td>' +
                '<td width="10%" class="detail-amount">0.00</td>' +
                '<td width="9%"><input type="text" class="layui-input detail-remark" style="width: 100%;"></td>' +
                '<td width="8%"><button type="button" class="layui-btn layui-btn-danger layui-btn-xs delete-row-btn"><i class="layui-icon layui-icon-delete"></i></button></td>' +
                '</tr>';
            
            $('#detail-table tbody').append(html);
            
            // 初始化xm-select
            var xmSelect = window.xmSelect.render({
                el: '#product-select-' + index,
                radio: true,
                filterable: true,
                filterMethod: function(val, item, index, prop){
                    if(val == item.value){ 
                        return true;
                    }
                    if(item.name.indexOf(val) != -1){ 
                        return true;
                    }
                    return false;
                },
                data: [],
                remoteSearch: true,
                remoteMethod: function(val, cb, show){
                    // 远程搜索方法
                    $.ajax({
                        url: '{:url("sinventory/getList")}',
                        type: 'get',
                        data: {
                            keyword: val,
                            limit: 20,
                            has_stock: 1  // 只搜索有库存的商品
                        },
                        dataType: 'json',
                        success: function(res) {
                            if (res.code === 0) {
                                var options = [];
                                $.each(res.data, function(i, item) {
                                    options.push({
                                        name: item.name,
                                        value: item.id + '_' + item.inventory_id,
                                        selected: false,
                                        spec: item.spec_info || '',
                                        price: item.price || 0,
                                        stock_quantity: item.stock_quantity || 0,
                                        stock_unit: item.stock_unit || 'pcs',
                                        inventory_id: item.inventory_id || 0,
                                        product_id: item.id || 0,
                                        product_type: item.product_type || 1,
                                        product_type_text: item.product_type_text || '普通商品',
                                        is_weight_product: item.is_weight_product || false,
                                        weight_unit: item.weight_unit || 'kg'
                                    });
                                });
                                cb(options);
                            } else {
                                cb([]);
                            }
                        },
                        error: function(){
                            cb([]);
                        }
                    });
                },
                template: function(data){
                    var stockInfo = '';
                    var typeTag = data.item.is_weight_product ? '[计量]' : '[普通]';
                    var unit = data.item.is_weight_product ? data.item.weight_unit : 'pcs';
                    
                    if (data.item.stock_quantity > 0) {
                        stockInfo = ' [库存: ' + data.item.stock_quantity + unit + ']';
                    } else {
                        stockInfo = ' [无库存]';
                    }
                    return typeTag + ' ' + data.item.name + (data.item.spec ? ' (' + data.item.spec + ')' : '') + stockInfo;
                },
                on: function(data){
                    if(data.isAdd && data.change.length){
                        // 选择了商品
                        var $tr = $('#product-select-' + index).closest('tr');
                        var selectedItem = data.change[0];
                        
                        // 设置基本信息
                        $tr.data('product-id', selectedItem.product_id);
                        $tr.data('inventory-id', selectedItem.inventory_id);
                        $tr.data('is-weight-product', selectedItem.is_weight_product);
                        $tr.data('weight-unit', selectedItem.weight_unit);
                        $tr.data('max-stock', selectedItem.stock_quantity);
                        
                        // 更新显示信息
                        $tr.find('.product-name').text(selectedItem.name);
                        $tr.find('.product-spec').text(selectedItem.spec || '');
                        $tr.find('.detail-type').text(selectedItem.product_type_text);
                        $tr.find('.detail-price').val(selectedItem.price || 0);
                        
                        // 根据商品类型设置库存和数量显示
                        var $stockSpan = $tr.find('.stock-quantity');
                        var $stockUnitSpan = $tr.find('.stock-unit');
                        var $quantityInput = $tr.find('.detail-quantity');
                        var $quantityUnitSpan = $tr.find('.quantity-unit');
                        
                        if (selectedItem.is_weight_product) {
                            // 计量商品
                            $stockSpan.text(selectedItem.stock_quantity || 0);
                            $stockUnitSpan.text(selectedItem.weight_unit || 'kg').css('color', '#FF5722');
                            
                            $quantityInput.attr('min', '0.01').attr('step', '0.01').attr('placeholder', '重量');
                            $quantityUnitSpan.text(selectedItem.weight_unit || 'kg').css('color', '#FF5722');
                        } else {
                            // 普通商品
                            $stockSpan.text(selectedItem.stock_quantity || 0);
                            $stockUnitSpan.text('pcs').css('color', '#999');
                            
                            $quantityInput.attr('min', '1').attr('step', '1').attr('placeholder', '数量');
                            $quantityUnitSpan.text('pcs').css('color', '#999');
                        }
                        
                        // 限制出库数量不能超过库存
                        var maxStock = selectedItem.stock_quantity || 0;
                        $quantityInput.attr('max', maxStock);
                        if (parseFloat($quantityInput.val()) > maxStock) {
                            $quantityInput.val(maxStock > 0 ? (selectedItem.is_weight_product ? maxStock : Math.floor(maxStock)) : 0);
                        }
                        
                        calculateRowAmount($tr.find('.detail-quantity')[0]);
                    }
                }
            });
            
            // 绑定删除按钮点击事件
            $('.delete-row-btn').off('click').on('click', function() {
                $(this).closest('tr').remove();
                calculateTotalAmount();
            });
        });
        
        // 检查库存并计算金额
        window.checkStockAndCalculate = function(input) {
            var $tr = $(input).closest('tr');
            var quantity = parseFloat($tr.find('.detail-quantity').val()) || 0;
            var maxStock = parseFloat($tr.data('max-stock')) || 0;
            var isWeightProduct = $tr.data('is-weight-product') || false;
            var weightUnit = $tr.data('weight-unit') || 'kg';
            
            if (quantity > maxStock) {
                var unitText = isWeightProduct ? weightUnit : 'pcs';
                var productName = $tr.find('.product-name').text() || '';
                layer.msg(productName + ' 库存不足，当前库存: ' + maxStock + unitText + '，' + 
                         (isWeightProduct ? '出库重量: ' : '出库数量: ') + quantity + unitText, {icon: 2});
                $(input).val(maxStock > 0 ? maxStock : 0);
                quantity = maxStock > 0 ? maxStock : 0;
            }
            
            calculateRowAmount(input);
        };
        
        // 计算行金额
        window.calculateRowAmount = function(input) {
            var $tr = $(input).closest('tr');
            var quantity = parseFloat($tr.find('.detail-quantity').val()) || 0;
            var price = parseFloat($tr.find('.detail-price').val()) || 0;
            var amount = (quantity * price).toFixed(2);
            $tr.find('.detail-amount').text(amount);
            calculateTotalAmount();
        };
        
        // 计算总金额
        function calculateTotalAmount() {
            var total = 0;
            $('#detail-table tbody tr').each(function() {
                var amount = parseFloat($(this).find('.detail-amount').text()) || 0;
                total += amount;
            });
            $('#total-amount').text(total.toFixed(2));
        }
        
        // 取消按钮点击事件
        $('#cancel-btn').on('click', function() {
            window.location.href = '{:url("sinventory/outstock")}';
        });
    });
</script> 