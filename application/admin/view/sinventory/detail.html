{include file="public/iframeheader"/}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">库存单详情</div>
        <div class="layui-card-body">
            <div class="layui-form"  lay-filter="detail-form">
                <!-- 基本信息 -->
                <fieldset class="layui-elem-field">
                    <legend>基本信息</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">单据编号</label>
                                <div class="layui-input-inline">
                                    <input type="text" value="{$data.order.order_no}" disabled class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">单据类型</label>
                                <div class="layui-input-inline">
                                    <input type="text" value="{$data.order.order_type_text}" disabled class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">业务类型</label>
                                <div class="layui-input-inline">
                                    <input type="text" value="{$data.order.business_type_text}" disabled class="layui-input">
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">总金额</label>
                                <div class="layui-input-inline">
                                    <input type="text" value="￥{$data.order.total_amount}" disabled class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">门店</label>
                                <div class="layui-input-inline">
                                    <input type="text" value="{$data.order.title}" disabled class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">状态</label>
                                <div class="layui-input-inline">
                                    <input type="text" value="{$data.order.status_text}" disabled class="layui-input">
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            {if $data.order.order_type == 1}
                            <div class="layui-inline">
                                <label class="layui-form-label">供应商</label>
                                <div class="layui-input-inline">
                                    <input type="text" value="{$data.order.supplier_name}" disabled class="layui-input">
                                </div>
                            </div>
                            {/if}
                            {if $data.order.order_type == 3}
                            <div class="layui-inline">
                                <label class="layui-form-label">会员</label>
                                <div class="layui-input-inline">
                                    <input type="text" value="{$data.order.member_name}" disabled class="layui-input">
                                </div>
                            </div>
                            {/if}
                            <div class="layui-inline">
                                <label class="layui-form-label">创建人</label>
                                <div class="layui-input-inline">
                                    <input type="text" value="{$data.order.created_by}" disabled class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">创建时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" value="{$data.order.created_at}" disabled class="layui-input">
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">备注</label>
                            <div class="layui-input-block">
                                <textarea disabled class="layui-textarea">{$data.order.remark}</textarea>
                            </div>
                        </div>
                    </div>
                </fieldset>

                <!-- 商品明细 -->
                <fieldset class="layui-elem-field">
                    <legend>商品明细</legend>
                    <div class="layui-field-box">
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>商品名称</th>
                                    <th>规格</th>
                                    <th>数量</th>
                                    <th>单价</th>
                                    <th>小计</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="data.details" id="item"}
                                <tr>
                                    <td>{$item.product_name}</td>
                                    <td>{$item.spec_info}</td>
                                    <td>{$item.quantity}</td>
                                    <td>￥{$item.price}</td>
                                    <td>￥{$item.quantity * $item.price}</td>
                                </tr>
                                {/volist}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" style="text-align: right;">总计：</td>
                                    <td>￥{$data.order.total_amount}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </fieldset>

                <!-- 操作按钮 -->
                <div class="layui-form-item" style="margin-top: 20px;">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn layui-btn-primary" onclick="goBack()">返回列表</button>
                        {if $data.order.status == 0}
                        <a href="{:url('sinventory/' . ($data.order.order_type == 1 ? 'editInStock' : 'editOutStock'))}?id={$data.order.id}" class="layui-btn">编辑</a>
                        <button type="button" class="layui-btn layui-btn-danger" onclick="deleteOrder({$data.order.id})">删除</button>
                        <button type="button" class="layui-btn layui-btn-warm" onclick="submitOrder({$data.order.id})">提交</button>
                        {/if}
                        {if $data.order.status == 1}
                        <button type="button" class="layui-btn layui-btn-danger" onclick="cancelOrder({$data.order.id})">取消</button>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['layer'], function() {
        var $ = layui.jquery,
            layer = layui.layer;
        
        // 将函数暴露到全局
        window.goBack = function() {
            var url = '{$data.order.order_type}' == 1 ? 
                '{:url("sinventory/index")}' : 
                '{:url("sinventory/outstock")}';
            window.location.href = url;
        };
        
        window.deleteOrder = function(id) {
            layer.confirm('确定要删除该库存单吗？', function(index) {
                $.ajax({
                    url: '{:url("sinventory/delete")}',
                    type: 'post',
                    data: {id: id},
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function() {
                                goBack();
                            }, 1000);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        };
        
        window.submitOrder = function(id) {
            layer.confirm('确定要提交该库存单吗？提交后不能修改', function(index) {
                $.ajax({
                    url: '{:url("sinventory/submit")}',
                    type: 'post',
                    data: {id: id},
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        };
        
        window.cancelOrder = function(id) {
            layer.confirm('确定要取消该库存单吗？', function(index) {
                $.ajax({
                    url: '{:url("sinventory/cancel")}',
                    type: 'post',
                    data: {id: id},
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        };
    });
</script> 