<!DOCTYPE html>
<html>
{include file="public/iframeheader"/}
<link rel="stylesheet" type="text/css" href="__STATIC__/admin/jquery-easyui-1.5.2/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="__STATIC__/admin/jquery-easyui-1.5.2/themes/icon.css">
<script type="text/javascript" src="__STATIC__/admin/jquery-easyui-1.5.2/jquery.easyui.min.js"></script>


<body>
    <br />
    <div id="main">


        <div style="margin:0  auto;width:95%;">
            <input type='hidden' name='keynum'>
            <div class="demoTable layui-form">


                <div class="layui-inline">
                    <label class="layui-form-label ">查询条件：</label>
                    <div class="layui-input-block">
                        <select name="field">
                            <option value='express'>快递名称</option>
                            <option value='expressnum'>快递单号</option>
                        </select>
                    </div>
                </div>


                <div class='layui-inline'>
                    <input class="layui-input" name="keyword" id="demoReload" autocomplete="off">
                </div>
                <button class="layui-btn  layui-btn-sm" onclick="get_table()" id="reload">搜索</button>
            </div>
            </form>


            <div style="margin-top: 10px;margin-left: 25px;">
            </div>




            <table id="demo" lay-filter="demo">
            </table>
            <script type="text/html" id="test-table-toolbar-toolbarDemo">
                <div class="layui-btn-container">
                {if condition="$status eq '0'"}
                <button class="layui-btn layui-btn-sm" lay-event="dingyue">重新订阅</button>
                {/if}
                </div>
            </script>
        </div>

        <script>
            get_table();
            function get_table() {
                var keynum = $("[name=keynum]").val();
                var field = $("[name=field]").val();
                var keyword = $("[name=keyword]").val();
                var status = "{$status}";
                layui.use('table', function () {
                    var table = layui.table
                        , form = layui.form;
                    table.render({
                        elem: '#demo'
                        , url: "{:url('ajax_get_orderlist')}?field=" + field + "&keyword=" + keyword + "&status=" + status//数据接口
                        , page: true //开启分页
                        , toolbar: '#test-table-toolbar-toolbarDemo'
                        , cols: [[ //表头
                            { type: 'checkbox' }
                            , { type: 'numbers', title: '序号', width: 50 }
                            , { field: 'order_sn', title: '订单号', width: 150 }
                            , { field: 'name', title: '收货人', width: 150 }
                            , { field: 'phone', title: '收货电话', width: 150 }
                            , { field: 'address', title: '收货地址', width: 150 }
                            , { field: 'goodsname', title: '商品信息', width: 250 }
                            , { field: 'express', title: '快递公司', width: 200 }
                            , { field: 'expressnum', title: '快递单号', width: 200 }
                            , { field: 'wuliu_content', title: '物流轨迹', width: 200 }
                            // , { fixed: 'right', title: "操作", toolbar: '#barDemo', width: 140 }
                        ]]
                    })

                })
            }
            layui.use(["upload"], function () {
                var upload = layui.upload;
                upload.render({ //允许上传的文件后缀
                    elem: '#uploadExcel'
                    , url: '{:url("ajax_send_order")}' //此处为所上传的请求路径
                    , accept: 'file' //普通文件
                    , exts: 'xls|excel|xlsx' //只允许上传压缩文件
                    , method: 'POST'
                    , done: function (data) {
                        if (data.sta == 1) {
                            layer.msg(data.msg);
                            get_table();
                        } else {
                            data.msg = "<div style='margin:10px;padding:10px'>" + data.msg + "</div>";
                            layer.open({
                                type: 1,
                                // content: "<div style='margin:10px;padding:10px'>" + data.msg + "</div>",
                                content: data.msg
                            })
                        }
                    }
                })
            })
            layui.use("table", function () {
                var table = layui.table;
                var form = layui.form;
                table.on('tool(demo)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
                    var data = obj.data //获得当前行数据
                        , layEvent = obj.event; //获得 lay-event 对应的值
                    var id = data.id;
                    console.log(data);
                    if (layEvent === 'save_group') {
                        layer.open({
                            type: 2,
                            title: "订单号：" + data.my_order_sn,
                            content: "{:url('save_order_group')}?id=" + id,
                            maxmin: true,
                            area: ["15%", "40%"]
                        })
                    }

                });
                //头工具栏事件
                table.on('toolbar(demo)', function (obj) {
                    var checkStatus = table.checkStatus(obj.config.id);
                    switch (obj.event) {
                        case 'dingyue':
                            var alldata1 = checkStatus.data;
                            var alldata = JSON.stringify(alldata1);
                            var index = layer.load();
                            $.post("{:url('ajax_dingyue')}", { alldata: alldata }, function (data) {
                                layer.closeAll('loading');
                                if (data.sta != 1) {
                                    layer.msg(data.msg);
                                } else {
                                    //关闭当前窗口
                                    layer.msg(data.msg);
                                    setTimeout(function () {
                                        get_table();
                                    }, 1500);
                                }
                            }, "json")
                            break;
                    };
                });


            })

            function callback(msg) {
                layer.msg(msg, { time: 1500 }, function (data) {
                    layer.closeAll();
                    get_table();
                })
            }



            //点击放大图片
            function showimg(t) {
                var src = $(t).attr("src");
                if (src == '') {
                    layer.msg("图为为空！");
                    return false;
                }
                layer.open({
                    type: 1,
                    title: false,
                    area: '516px',
                    content: '<img style="display: inline-block; width: 100%; height: 100%;" src="' + src + '">'
                });
            }


            //时间转换
            function createTime(v) {
                if (v == null || v == 0) {
                    return "暂无";
                }
                var v = v * 1000; //js的时间戳要*1000
                var date = new Date(v);
                var y = date.getFullYear();
                var m = date.getMonth() + 1;
                m = m < 10 ? '0' + m : m;
                var d = date.getDate();
                d = d < 10 ? ("0" + d) : d;
                var h = date.getHours();
                h = h < 10 ? ("0" + h) : h;
                var M = date.getMinutes();
                M = M < 10 ? ("0" + M) : M;
                var S = date.getSeconds();
                S = S < 10 ? ("0" + S) : S;
                var str = y + "-" + m + "-" + d + " " + h + ":" + M + ":" + S;
                return str;
            }
        </script>
</body>

</html>
