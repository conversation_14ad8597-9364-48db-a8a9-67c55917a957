<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/9/11 下午4:46
 */

namespace app\admin\model;

use think\Model;

class ProductCategory extends Model
{
    protected $table = 'product_category';

    protected $pk = 'id';

    /**
     * 验证当前分类标题是否存在
     * @param $title
     * @param $id
     * @return ProductCategory
     */
    public static function checkTitleExist($title, $id = null)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $where = [['title','=', $title], ['clientkeynum', '=', $basekeynum]];

        if ($id) {
            $where[] = ['id','<>', $id];
        }

        return self::where($where)->find();
    }

    public function products()
    {
        return $this->hasMany('Product', 'category_id', 'id');
    }
}
