<?php
namespace app\admin\model;

use \think\facade\Log;
use think\Model;
use think\Db;

/**
 * 库存单主表模型
 * Class InventoryOrder
 * @package app\admin\model
 */
class InventoryOrder extends Model
{
    protected $name = 'inventory_order';

    /**
     * 获取库存单列表
     * @param array $where 查询条件
     * @param int $page 当前页码
     * @param int $limit 每页记录数
     * @return array
     */
    public function getList($where = [], $page = 1, $limit = 15)
    {
        $count = $this->where($where)->count();
        $list = $this->where($where)
            ->order('id', 'desc')
            ->page($page, $limit)
            ->select();

        return ['count' => $count, 'list' => $list];
    }

    /**
     * 添加库存单
     * @param array $data 表单数据
     * @return int|string
     */
    public function add($data)
    {
        // 生成订单编号
        $data['order_no'] = 'IO' . date('YmdHis') . mt_rand(1000, 9999);
        // 开启事务
        Db::startTrans();
        try {
            // 添加主表记录
            $id = $this->insertGetId($data);

            Db::commit();
            return $id;
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            Db::rollback();
            return 0;
        }
    }

    /**
     * 更新库存单状态
     * @param int $id 库存单ID
     * @param int $status 状态值
     * @param string $operator 操作人
     * @return bool
     */
    public function updateStatus($id, $status, $operator = '')
    {
        $data = ['status' => $status];

        // 根据状态更新不同字段
        switch ($status) {
            case 2: // 已审核
                $data['reviewed_by'] = $operator;
                $data['reviewed_time'] = date('Y-m-d H:i:s');
                break;
            case 3: // 已完成
                $data['reviewed_by'] = $operator;
                $data['reviewed_time'] = date('Y-m-d H:i:s');
                $data['completed_time'] = date('Y-m-d H:i:s');
                break;
        }

        return $this->where('id', $id)->update($data) !== false;
    }

    /**
     * 删除库存单
     * @param int $id 库存单ID
     * @return bool
     */
    public function remove($id)
    {
        // 只允许删除草稿状态的库存单
        $order = $this->where('id', $id)->where('status', 0)->find();
        if (!$order) {
            return false;
        }

        // 开启事务
        Db::startTrans();
        try {
            // 删除主表记录
            $this->where('id', $id)->delete();

            // 删除明细表记录
            // 删除库存单详情 - 添加clientkeynum过滤确保数据隔离
        $order = $this->find($id);
        $detailWhere = [['order_id', '=', $id]];
        if (isset($order['clientkeynum'])) {
            $detailWhere[] = ['clientkeynum', '=', $order['clientkeynum']];
        }
        Db::name('inventory_order_detail')->where($detailWhere)->delete();

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return false;
        }
    }

    /**
     * 获取库存单详情
     * @param int $id 库存单ID
     * @return array|null
     */
    public function getDetail($id)
    {
        $order = $this->where('id', $id)->find();
        if (!$order) {
            return null;
        }

        // 获取明细 - 添加clientkeynum过滤确保数据隔离
        $detailWhere = [['order_id', '=', $id]];
        if (isset($order['clientkeynum'])) {
            $detailWhere[] = ['clientkeynum', '=', $order['clientkeynum']];
        }
        $details = Db::name('inventory_order_detail')->where($detailWhere)->select();

        foreach ($details as $key => $detail) {
            $details[$key]['product_name'] = Db::name('products')->where('id', $detail['product_id'])->value('title');
            $details[$key]['spec_info'] = Db::name('product_inventory')->where('id', $detail['inventory_id'])->value('title');
        }

        return ['order' => $order, 'details' => $details];
    }

    /**
     * 审核并完成库存单
     * @param int $id 库存单ID
     * @param string $operator 操作人
     * @return bool
     */
    public function complete($id, $operator)
    {
        // 查询库存单
        $order = $this->where('id', $id)->where('status', 0)->find();
        if (!$order) {
            return false;
        }

        // 获取库存单明细
        // 获取明细 - 添加clientkeynum过滤确保数据隔离
        $detailWhere = [['order_id', '=', $id]];
        if (isset($order['clientkeynum'])) {
            $detailWhere[] = ['clientkeynum', '=', $order['clientkeynum']];
        }
        $details = Db::name('inventory_order_detail')->where($detailWhere)->select();
        if (empty($details)) {
            return false;
        }

        // 开启事务
        Db::startTrans();
        try {
            // 更新库存单状态为已审核
            $this->updateStatus($id, 2, $operator);

            // 处理库存变动
            foreach ($details as $detail) {
                // 获取商品信息，判断是否为计量商品
                $product = Db::name('products')->where('id', $detail['product_id'])->find();
                if (!$product) {
                    throw new \Exception("商品ID {$detail['product_id']} 不存在");
                }
                
                $is_weight_product = ($product['product_type'] ?? 1) == 2; // 2表示计量商品

                // 获取当前库存
                $inventory = Db::name('shop_product_inventory')->where([
                    'product_id' => $detail['product_id'],
                    'inventory_id' => $detail['inventory_id'],
                    'shop_id' => $order['shop_id']
                ])->find();

                if ($is_weight_product) {
                    // 计量商品：处理重量库存
                    $before_quantity = $inventory ? ($inventory['weight_stock'] ?? 0) : 0;
                    $change_quantity = $detail['quantity'];

                    // 根据订单类型处理库存
                    if ($order['order_type'] == 1) {
                        // 入库：增加重量库存
                        $after_quantity = $before_quantity + $change_quantity;
                        $change_type = 1; // 入库
                        if ($order['business_type'] == 2) {
                            $change_type = 3; // 调拨入库
                        } else if ($order['business_type'] == 3) {
                            $change_type = 6; // 退货入库
                        }
                    } else {
                        // 出库：减少重量库存
                        $change_quantity = -$change_quantity; // 出库为负数
                        $after_quantity = $before_quantity + $change_quantity;
                        
                        if ($order['order_type'] == 2) {
                            // 普通出库
                            $change_type = 2; // 出库
                            if ($order['business_type'] == 2) {
                                $change_type = 4; // 调拨出
                            } else if ($order['business_type'] == 3) {
                                $change_type = 7; // 盘点出库(报损)
                            }
                        } else {
                            // 销售出库
                            $change_type = 5; // 销售
                        }
                    }

                    // 更新重量库存
                    if ($inventory) {
                        Db::name('shop_product_inventory')->where([
                            'product_id' => $detail['product_id'],
                            'inventory_id' => $detail['inventory_id'],
                            'shop_id' => $order['shop_id']
                        ])->update(['weight_stock' => $after_quantity, 'update_time' => date('Y-m-d H:i:s')]);
                    } else {
                        // 如果库存记录不存在，创建新记录
                        Db::name('shop_product_inventory')->insert([
                            'product_id' => $detail['product_id'],
                            'inventory_id' => $detail['inventory_id'],
                            'weight_stock' => $after_quantity,
                            'stock' => 0, // 计量商品的数量库存设为0
                            'stock_unit' => 'kg', // 计量商品使用重量单位
                            'clientkeynum' => $order['clientkeynum'],
                            'shop_id' => $order['shop_id'],
                            'add_time' => date('Y-m-d H:i:s'),
                            'update_time' => date('Y-m-d H:i:s')
                        ]);
                    }

                    // 记录重量库存变动日志
                    Db::name('inventory_log')->insert([
                        'clientkeynum' => $order['clientkeynum'],
                        'shop_id' => $order['shop_id'],
                        'product_id' => $detail['product_id'],
                        'inventory_id' => $detail['inventory_id'],
                        'before_quantity' => $before_quantity,
                        'change_quantity' => $change_quantity,
                        'after_quantity' => $after_quantity,
                        'change_type' => $change_type,
                        'related_id' => $order['id'],
                        'related_no' => $order['order_no'],
                        'remark' => $order['remark'] . '（计量商品重量库存变动）',
                        'operator' => $operator,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                } else {
                    // 普通商品：处理数量库存（原有逻辑）
                    $before_quantity = $inventory ? ($inventory['stock'] ?? 0) : 0;
                    $change_quantity = $detail['quantity'];

                    // 根据订单类型处理库存
                    if ($order['order_type'] == 1) {
                        // 入库
                        $after_quantity = $before_quantity + $change_quantity;
                        $change_type = 1; // 入库
                        if ($order['business_type'] == 2) {
                            $change_type = 3; // 调拨入库
                        } else if ($order['business_type'] == 3) {
                            $change_type = 6; // 退货入库
                        }
                    } else {
                        // 出库或销售出库
                        $change_quantity = -$change_quantity; // 出库为负数
                        $after_quantity = $before_quantity + $change_quantity;
                        
                        if ($order['order_type'] == 2) {
                            // 普通出库
                            $change_type = 2; // 出库
                            if ($order['business_type'] == 2) {
                                $change_type = 4; // 调拨出
                            } else if ($order['business_type'] == 3) {
                                $change_type = 7; // 盘点出库(报损)
                            }
                        } else {
                            // 销售出库
                            $change_type = 5; // 销售
                        }
                    }

                    // 更新数量库存
                    if ($inventory) {
                        Db::name('shop_product_inventory')->where([
                            'product_id' => $detail['product_id'],
                            'inventory_id' => $detail['inventory_id'],
                            'shop_id' => $order['shop_id']
                        ])->update(['stock' => $after_quantity, 'update_time' => date('Y-m-d H:i:s')]);
                    } else {
                        Db::name('shop_product_inventory')->insert([
                            'product_id' => $detail['product_id'],
                            'inventory_id' => $detail['inventory_id'],
                            'stock' => $after_quantity,
                            'weight_stock' => null, // 普通商品不使用重量库存
                            'stock_unit' => 'pcs', // 普通商品使用件数单位
                            'clientkeynum' => $order['clientkeynum'],
                            'shop_id' => $order['shop_id'],
                            'add_time' => date('Y-m-d H:i:s'),
                            'update_time' => date('Y-m-d H:i:s')
                        ]);
                    }

                    // 记录数量库存变动日志
                    Db::name('inventory_log')->insert([
                        'clientkeynum' => $order['clientkeynum'],
                        'shop_id' => $order['shop_id'],
                        'product_id' => $detail['product_id'],
                        'inventory_id' => $detail['inventory_id'],
                        'before_quantity' => $before_quantity,
                        'change_quantity' => $change_quantity,
                        'after_quantity' => $after_quantity,
                        'change_type' => $change_type,
                        'related_id' => $order['id'],
                        'related_no' => $order['order_no'],
                        'remark' => $order['remark'] . '（普通商品数量库存变动）',
                        'operator' => $operator,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }
            }

            // 更新状态为已完成
            $this->updateStatus($id, 3, $operator);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            Db::rollback();
            return false;
        }
    }
}
