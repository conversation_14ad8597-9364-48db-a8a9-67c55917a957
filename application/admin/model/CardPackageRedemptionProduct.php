<?php
/**
 * 卡套餐兑换商品表模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use think\Model;

class CardPackageRedemptionProduct extends Model
{
    protected $table = 'card_package_redemption_product';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    /**
     * 关联兑换主表
     * @return \think\model\relation\BelongsTo
     */
    public function redemption()
    {
        return $this->belongsTo('CardPackageRedemption', 'redemption_id', 'id');
    }
    
    /**
     * 关联商品库存
     * @return \think\model\relation\BelongsTo
     */
    public function productInventory()
    {
        return $this->belongsTo('ProductInventory', 'product_inventory_id', 'id');
    }
    
    /**
     * 获取兑换商品
     * @param int $redemptionId 兑换主表ID
     * @param string $clientkeynum 客户唯一标识
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getRedemptionProducts($redemptionId, $clientkeynum)
    {
        return $this->with(['productInventory'])
            ->where('redemption_id', $redemptionId)
            ->where('clientkeynum', $clientkeynum)
            ->select();
    }
    
    /**
     * 批量添加兑换商品
     * @param array $products 商品数据数组
     * @return bool
     */
    public function addBatchProducts($products)
    {
        if (empty($products)) {
            return false;
        }
        
        return $this->saveAll($products);
    }
} 