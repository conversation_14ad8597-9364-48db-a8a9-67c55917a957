<?php

namespace app\admin\model;

use think\Model;

class CardRefundBatchDetail extends Model
{
    protected $table = 'card_refund_batch_detail';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = false;
    protected $createTime = 'add_time';
    protected $updateTime = false;
    
    /**
     * 关联批次主表
     * @return \think\model\relation\BelongsTo
     */
    public function batch()
    {
        return $this->belongsTo('CardRefundBatch', 'batch_id');
    }
    
    /**
     * 关联卡型
     * @return \think\model\relation\BelongsTo
     */
    public function cardType()
    {
        return $this->belongsTo('CardPackageType', 'type_id');
    }
    
    /**
     * 关联卡表
     * @return \think\model\relation\BelongsTo
     */
    public function card()
    {
        return $this->belongsTo('Card', 'card_id');
    }
    
    /**
     * 获取批次明细列表
     * @param int $batchId 批次ID
     * @return array|\think\Collection
     */
    public function getDetailsList($batchId)
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        return $this->alias('d')
            ->join('card_package_type t', 't.id = d.type_id')
            ->field('d.*, t.name as type_name')
            ->where('d.batch_id', $batchId)
            ->where('d.clientkeynum', $clientkeynum)
            ->select();
    }
}