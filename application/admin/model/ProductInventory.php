<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/9/11 下午5:38
 */

namespace app\admin\model;

use think\Model;
use Picqer\Barcode\BarcodeGeneratorPNG;
use think\facade\Env;

class ProductInventory extends Model
{
    protected $table = 'product_inventory';

    protected $pk = 'id';

    /**
     * 生成商品条形码并保存
     * @param string $sn 商品SKU编号
     * @return string 条形码图片路径
     */
    public function generateBarcode($sn)
    {
        try {
            // 创建条形码生成器
            $generator = new BarcodeGeneratorPNG();
            
            // 确保目录存在
            $dir = Env::get('root_path') . 'public/uploads/barcode/';
            if (!is_dir($dir)) {
                mkdir($dir, 0777, true);
            }
            
            // 生成条形码图片文件名
            $filename = 'barcode_' . $sn . '.png';
            $filepath = $dir . $filename;
            
            // 生成条形码
            $barcode = $generator->getBarcode($sn, $generator::TYPE_CODE_128, 2, 60);
            
            // 保存条形码图片
            file_put_contents($filepath, $barcode);
            
            // 上传到阿里云OSS(如果有配置)
            $uri = 'public/uploads/barcode/' . $filename;
            $ossResult = uploadFileToAliOss(Env::get('ROOT_PATH') . $uri, $uri);
            
            // 返回条形码图片URL
            return isset($ossResult['url']) ? $ossResult['url'] : '/public/uploads/barcode/' . $filename;
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('生成条形码失败：' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            return '';
        }
    }
}
