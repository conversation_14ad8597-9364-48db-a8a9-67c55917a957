<?php
namespace app\admin\model;

use think\Model;

/**
 * 卡生成明细模型
 */
class CardGenerationDetail extends Model
{
    protected $pk = 'id';
    
    // 设置数据表名
    protected $table = 'card_generation_detail';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = false;
    
    /**
     * 关联生成记录
     */
    public function generationRecord()
    {
        return $this->belongsTo('CardGenerationRecord', 'generation_id');
    }
    
    /**
     * 关联卡
     */
    public function card()
    {
        return $this->belongsTo('Card', 'card_id');
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = isset($data['status']) ? $data['status'] : $this->status;
        $statusMap = [
            0 => '生成失败',
            1 => '生成成功'
        ];
        return isset($statusMap[$status]) ? $statusMap[$status] : '未知状态';
    }
} 