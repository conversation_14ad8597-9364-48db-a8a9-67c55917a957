<?php
/**
 * 客户端用户模型
 * @date 2023-5-9
 * <AUTHOR>
 */
namespace app\admin\model;

use think\Model;

class ClientMember extends Model
{
    protected $pk = 'id';

    protected $table = 'client_member';

    /**
     * 获取用户信息
     * @param $id
     * @param $where
     * @return array|bool|string|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getInfoById($id, $where = [])
    {
        $where['id'] = $id;
        return self::where($where)->find();
    }

    /**
     * 绑定手机号
     * @param $where
     * @param $phone
     * @return ClientMember
     */
    public static function bindPhone($where, $phone)
    {
        return self::where($where)->update(['phone' => $phone]);
    }
}
