<?php
/**
 * 销售单模型
 * @date 2023-5-16
 * <AUTHOR>
 */

namespace app\admin\model;

use think\Model;

class ClientYcardPucardModel extends Model
{
    protected $pk = 'id';

    protected $table = 'client_ycard_pucard';

    /**
     * 通过销售单号获取info信息
     * @param $picinumber
     * @return ClientYcardPucardModel|null
     * @throws \think\exception\DbException
     */
    public static function getInfoByPiciNumber($picinumber,$where = [])
    {
        if (!empty($where)) {
            $where += ['pici_number' => $picinumber];
        }
        $info = self::get($where);
        return $info;
    }

    /**
     * 获取客户的所有销售单列表
     * @param $customer_id
     * @param $where
     * @return bool|string|\think\Collection
     */
    public static function getAllByCustomer($customer_id, $where)
    {
        $where['customer_id'] = $customer_id;
        $list = self::where($where)->select();
        return $list;
    }
}
