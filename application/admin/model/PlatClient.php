<?php
/**
 * 平台客户模型
 * @date 2023-5-9
 * <AUTHOR>
 */
namespace app\admin\model;

use think\Model;

class PlatClient extends Model
{
    protected $pk = 'id';

    protected $table = 'plat_client';

    /**
     * 通过basekeynum获取基本信息
     * @param $baseKeyNum
     * @return array|bool|string|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getInfoByBaseKeyNum($baseKeyNum)
    {
        return self::where(['keynum' => $baseKeyNum])->find();
    }
}
