<?php
/**
 * 卡号状态变更日志模型
 * @date 2023-4-28
 * <AUTHOR>
 */


namespace app\admin\model;

use think\Model;

class CardStatusChangeLogModel extends Model
{
    protected $pk = 'id';

    protected $table = 'card_status_change_log';

    public function getCardNameAttr($value, $data)
    {
        return ClientYcardModel::where(['id' => $data['card_id']])->value('cardnum');
    }

    public function getCustomerNameAttr($value, $data)
    {
        return CardCustomer::where(['id' => $data['customer_id']])->value('name');
    }

    public function getSaleNameAttr($value, $data)
    {
        return CardSale::where(['id' => $data['sale_id']])->value('name');
    }

    public function getStatusAttr($value, $data)
    {
        $status = ClientYcardModel::where(['id' => $data['card_id']])->value('status');
        $type_arr = [
            0       => '未销售',
            1       => '已销售',
            2       => '退卡',
            3       => '开卡',
            4       => '关卡',
            -1      => '废卡',
        ];
        return $type_arr[$status];
    }

    public function getTypeAttr($value)
    {
        $type_arr = [
            0       => '未销售',
            1       => '已销售',
            2       => '退卡',
            3       => '开卡',
            4       => '关卡',
            -1      => '废卡',
        ];
        return $type_arr[$value];
    }

    /**
     * 获取导出数据
     * @param $where array 查询条件
     */
    public static function getExportAll($where)
    {
        return self::where($where)
            ->field('*, customer_id as customer_name, sale_id as sale_name, card_id as card_name')
            ->order('add_time', 'desc')
            ->select();
    }

    /**
     * 批量添加
     * @return
     */
    public static function batchAdd($insert_data)
    {
        $model = new self();
        $result = $model->saveAll($insert_data);
        return $result;
    }

    /**
     * 查
     * @param $where array 查询条件
     * @param $page int 页数
     * @param $pagesize int 每页条数
     * @return bool|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getList($where, $page, $pagesize)
    {
        return self::where($where)
            ->field('*, card_id as card_name, customer_id as customer_name, sale_id as sale_name, type as status')
            ->order('add_time', 'desc')
            ->page($page, $pagesize)
            ->select();
    }

    /**
     * 获取总条数
     * @param $where array 查询条件
     * @return int|string
     * @throws \think\Exception
     */
    public static function getCount($where)
    {
        return self::where($where)
            ->count();
    }
}
