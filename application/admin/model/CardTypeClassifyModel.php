<?php
/**
 * 卡分类模型
 * @date 2023-4-26
 * <AUTHOR>
 */

namespace app\admin\model;

use think\Model;

class CardTypeClassifyModel extends Model
{
    protected $pk = 'id';

    protected $table = 'card_type_classify';

    /**
     * 查
     * @param $where array 查询条件
     * @param $page int 页数
     * @param $pagesize int 每页条数
     * @return bool|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getList($where, $page, $pagesize)
    {
        return self::where($where)
            ->where($where)
            ->order("o asc")
            ->limit($page, $pagesize)
            ->select();
    }

    /**
     * 获取总条数
     * @param $where array 查询条件
     * @return int|string
     * @throws \think\Exception
     */
    public static function getCount($where)
    {
        return self::where($where)
            ->count();
    }

    public static function getAllByTemplate_id($template_id,$clientkeynum)
    {
        $list = self::where("card_template_id='$template_id' and clientkeynum='$clientkeynum'")
        ->field("id,name")->order('o asc')->select();
        return $list;
    }
}
