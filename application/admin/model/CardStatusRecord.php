<?php
/**
 * 卡状态记录表模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use think\Model;

class CardStatusRecord extends Model
{
    protected $table = 'card_status_record';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = false;
    
    /**
     * 关联卡
     * @return \think\model\relation\BelongsTo
     */
    public function card()
    {
        return $this->belongsTo('Card', 'card_id', 'id');
    }
    
    /**
     * 获取卡状态变更记录
     * @param int $cardId 卡ID
     * @param string $clientkeynum 客户唯一标识
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getCardStatusRecords($cardId, $clientkeynum)
    {
        return $this->where('card_id', $cardId)
            ->where('clientkeynum', $clientkeynum)
            ->order('operation_time', 'desc')
            ->select();
    }
} 