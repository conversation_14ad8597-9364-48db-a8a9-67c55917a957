<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/10/17 14:16
 */

namespace app\admin\model;

use think\Model;

class OrderDetail extends Model
{
    protected $pk = 'id';
    protected $table = 'order_detail';

    public function getProductJsonAttr($value)
    {
        if (!empty($value)) {
            return json_decode($value, true);
        }
    }

    public function getInventoryJsonAttr($value)
    {
        if (!empty($value)) {
            return json_decode($value, true);
        }
    }
}
