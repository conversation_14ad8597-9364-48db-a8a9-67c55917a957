<?php
/**
 * 收货地址模型
 * @date 2023-6-1
 * <AUTHOR>
 */

namespace app\admin\model;


use think\Model;

class Address extends Model
{
    protected $pk = 'id';

    protected $table = 'address';

    public static function del($where)
    {
        return self::where($where)->delete();
    }

    public static function add($params)
    {
        $model = new self();
        $model->member_id = $params['member_id'];
        $model->basekeynum = $params['basekeynum'];
        $model->member_name = $params['member_name'];
        $model->phone = $params['phone'];
        $model->detail = $params['detail'];
        $model->province = $params['province'];
        $model->city = $params['city'];
        $model->area = $params['area'];
        $model->is_default = $params['is_default'];
        $model->add_time = $params['add_time'];
        return $model->save();
    }

    public static function getInfo($where)
    {
        return self::where($where)->find();
    }

    public static function getList($where)
    {
        return self::where($where)->select();
    }
}
