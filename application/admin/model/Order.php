<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/10/11 13:35
 */

namespace app\admin\model;

use think\Model;

class Order extends Model
{
    protected $pk = 'id';

    protected $table = 'order';

    protected $append = ['status_text', 'type_text'];

    public function orderDetail(){
        return $this->hasMany('OrderDetail','order_no','order_no');
    }

    public function getAddressJsonAttr($value)
    {
        if (!empty($value)) {
            return json_decode($value, true);
        }
    }

    public function getTypeTextAttr($value, $data)
    {
        $arr = [
            1 => '自提订单',
            2 => '配送订单',
            3 => '结算台订单'
        ];
        return $arr[$data['type']];
    }

    public function getStatusTextAttr($value, $data)
    {
        if ($data['type'] == 2) {
            $arr = [
                0=>'待支付',
                1=>'待审核',
                2=>'已接单',
                3=>'正在配送',
                4=>'已完成',
                -1=> '已取消'
            ];
            return $arr[$data['status']];
        }
        if ($data['type'] == 1) {
            $arr = [
                0 =>'待支付',
                1 => '待核销',
                100 => '已完成',
                -1 => '已取消',
                2 => '已核销'
            ];
            return $arr[$data['status']];
        }
    }
}
