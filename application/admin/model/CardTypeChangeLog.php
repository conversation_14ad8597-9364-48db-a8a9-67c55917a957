<?php
namespace app\admin\model;

use think\Model;
use think\Db;

/**
 * 储值卡等级修改日志模型
 */
class CardTypeChangeLog extends Model
{
    protected $table = 'card_type_change_log';
    
    /**
     * 生成批次单号
     * @return string
     */
    public static function createNo()
    {
        return 'CTCH' . date('YmdHis') . rand(1000, 9999);
    }
    
    /**
     * 添加记录
     * @param array $data
     * @return int|false
     */
    public static function add($data)
    {
        return self::insertGetId($data);
    }
    
    /**
     * 获取列表
     * @param array $where
     * @param int $page
     * @param int $pagesize
     * @return array
     */
    public static function getList($where = [], $page = 1, $pagesize = 20)
    {
        $offset = ($page - 1) * $pagesize;
        
        return self::alias('t')
            ->join('card_customer c', 't.customer_id = c.id', 'LEFT')
            ->field('t.*, c.name as customer_name')
            ->where($where)
            ->order('t.add_time DESC')
            ->limit($offset, $pagesize)
            ->select()
            ->toArray();
    }
    
    /**
     * 获取总数
     * @param array $where
     * @return int
     */
    public static function getCount($where = [])
    {
        return self::where($where)->count();
    }
    
    /**
     * 根据批次号获取信息
     * @param string $no
     * @return array|null
     */
    public static function getInfoByNo($no)
    {
        return self::alias('t')
            ->join('card_customer c', 't.customer_id = c.id', 'LEFT')
            ->join('card_type old_ct', 't.old_cardtype_id = old_ct.id', 'LEFT')
            ->join('card_type new_ct', 't.new_cardtype_id = new_ct.id', 'LEFT')
            ->field('t.*, c.name as customer_name, old_ct.name as old_cardtype_name, new_ct.name as new_cardtype_name')
            ->where('t.no', $no)
            ->find();
    }
} 