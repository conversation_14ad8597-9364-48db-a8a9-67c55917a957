<?php
namespace app\admin\model;

use think\Model;

/**
 * 储值卡等级修改详细日志模型
 */
class CardTypeChangeDetailLog extends Model
{
    protected $table = 'card_type_change_detail_log';
    
    /**
     * 批量添加记录
     * @param array $data
     * @return bool
     */
    public static function batchAdd($data)
    {
        return self::insertAll($data);
    }
    
    /**
     * 根据批次号获取详细信息
     * @param string $changeNo
     * @return array
     */
    public static function getDetailByChangeNo($changeNo)
    {
        return self::alias('t')
            ->join('client_ycard cy', 't.card_id = cy.id', 'LEFT')
            ->join('card_type old_ct', 't.old_cardtype_id = old_ct.id', 'LEFT')
            ->join('card_type new_ct', 't.new_cardtype_id = new_ct.id', 'LEFT')
            ->field('t.*, cy.cardnum, cy.yu_money, old_ct.name as old_cardtype_name, new_ct.name as new_cardtype_name')
            ->where('t.change_no', $changeNo)
            ->order('t.add_time DESC')
            ->select()
            ->toArray();
    }
} 