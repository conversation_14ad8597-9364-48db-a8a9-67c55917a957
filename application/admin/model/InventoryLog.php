<?php
namespace app\admin\model;

use think\Model;

/**
 * 库存变动日志模型
 * Class InventoryLog
 * @package app\admin\model
 */
class InventoryLog extends Model
{
    protected $name = 'inventory_log';
    
    /**
     * 添加库存变动日志
     * @param array $data 数据
     * @return int|string
     */
    public function add($data)
    {
        return $this->insertGetId($data);
    }
    
    /**
     * 获取库存变动日志列表
     * @param array $where 查询条件
     * @param int $page 当前页码
     * @param int $limit 每页记录数
     * @return array
     */
    public function getList($where = [], $page = 1, $limit = 15)
    {
        $count = $this->where($where)->count();
        $list = $this->alias('l')
            ->join('products p', 'p.id = l.product_id', 'LEFT')
            ->join('product_inventory pi', 'pi.id = l.inventory_id', 'LEFT')
            ->where($where)
            ->field('l.*, p.title as product_name, pi.title as inventory_name')
            ->order('l.id', 'desc')
            ->page($page, $limit)
            ->select();
        
        // 获取门店信息
        $shops = \think\Db::name('shop')->column('title', 'id');
        
        // 处理数据
        foreach ($list as &$item) {
            // 处理门店名称
            $item['shop_name'] = $item['shop_id'] > 0 ? ($shops[$item['shop_id']] ?? '未知门店') : '平台';
            
            // 处理变动类型文本
            $item['change_type_text'] = $this->getChangeTypeText($item['change_type']);
            
            // 处理变动数量
            $item['quantity_before'] = $item['before_quantity'];
            $item['quantity_change'] = $item['change_quantity'];
            $item['quantity_after'] = $item['after_quantity'];
            
            // 处理操作人
            $item['created_by'] = $item['operator'];
        }
        
        return ['count' => $count, 'list' => $list];
    }
    
    /**
     * 获取变动类型文本
     * @param int $change_type 变动类型
     * @return string
     */
    public function getChangeTypeText($change_type)
    {
        $types = [
            1 => '入库',
            2 => '出库',
            3 => '调拨入',
            4 => '调拨出',
            5 => '销售',
            6 => '退货',
            7 => '盘点',
            8 => '其他'
        ];
        
        return isset($types[$change_type]) ? $types[$change_type] : '未知类型';
    }
    
    /**
     * 获取商品库存变动记录
     * @param int $product_id 商品ID
     * @param int $inventory_id 库存ID
     * @param int $page 当前页码
     * @param int $limit 每页记录数
     * @return array
     */
    public function getProductLogs($product_id, $inventory_id, $page = 1, $limit = 15)
    {
        $where = [
            'product_id' => $product_id,
            'inventory_id' => $inventory_id
        ];
        
        return $this->getList($where, $page, $limit);
    }
} 