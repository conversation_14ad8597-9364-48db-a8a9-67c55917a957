<?php
/**
 * 卡表模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use think\Model;

class Card extends Model
{
    protected $table = 'card';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = false;
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';

    
    
    // 卡状态：0-待销售，1-已销售，2-已兑换，3-已开卡，4-已关卡，5-已废卡，-1-已过期，-2-已退卡
    const STATUS_PENDING_SALE = 0;
    const STATUS_SOLD = 1;
    const STATUS_REDEEMED = 2;
    const STATUS_OPENED = 3;
    const STATUS_CLOSED = 4;
    const STATUS_WASTED = 5;
    const STATUS_EXPIRED = -1;
    const STATUS_RETURNED = -2;

    public function getStatusTextAttr($value, $data)
    {
        $status = $data['status'];
        $statusText = '';
        
        switch ($status) {
            case self::STATUS_PENDING_SALE:
                $statusText = '待销售';
                break;
            case self::STATUS_SOLD:
                $statusText = '已销售';
                break;
            case self::STATUS_REDEEMED:
                $statusText = '已兑换';
                break;
            case self::STATUS_OPENED:
                $statusText = '已开卡';
                break;
            case self::STATUS_CLOSED:
                $statusText = '已关卡';
                break;
            case self::STATUS_WASTED:
                $statusText = '已废卡';
                break;
            case self::STATUS_EXPIRED:
                $statusText = '已过期';
                break;
            case self::STATUS_RETURNED:
                $statusText = '已退卡';
                break;
            default:
                $statusText = '未知状态';
                break;
        }
        
        return $statusText;
    }
    
    /**
     * 关联卡型
     * @return \think\model\relation\BelongsTo
     */
    public function cardType()
    {
        return $this->belongsTo('CardPackageType', 'type_id');
    }
    
    /**
     * 关联客户
     * @return \think\model\relation\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo('CardCustomer', 'customer_id');
    }
    
    /**
     * 关联销售员
     * @return \think\model\relation\BelongsTo
     */
    public function salesperson()
    {
        return $this->belongsTo('CardSale', 'salesperson_id', 'id');
    }
    
    /**
     * 关联销售批次
     * @return \think\model\relation\BelongsTo
     */
    public function saleBatch()
    {
        return $this->belongsTo('CardSaleBatch', 'batch_id', 'id');
    }
    
    /**
     * 关联用户
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo('User', 'user_id');
    }
    
    /**
     * 关联生成批次
     * @return \think\model\relation\BelongsTo
     */
    public function generationRecord()
    {
        return $this->belongsTo('CardGenerationRecord', 'generation_batch_no', 'batch_no');
    }
    
    /**
     * 关联生成详情
     * @return \think\model\relation\HasOne
     */
    public function generationDetail()
    {
        return $this->hasOne('CardGenerationDetail', 'card_id');
    }
    
    /**
     * 关联销售记录
     * @return \think\model\relation\HasOne
     */
    public function saleRecord()
    {
        return $this->hasOne('CardSaleRecord', 'card_id');
    }
    
    /**
     * 关联状态记录
     * @return \think\model\relation\HasMany
     */
    public function statusRecords()
    {
        return $this->hasMany('CardStatusRecord', 'card_id');
    }
    
    /**
     * 关联兑换记录
     * @return \think\model\relation\HasMany
     */
    public function redemptionRecords()
    {
        return $this->hasMany('CardPackageRedemption', 'card_id');
    }
    
    /**
     * 根据卡号获取卡信息
     * @param string $cardNo 卡号
     * @param string $clientkeynum 客户唯一标识
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getCardByCardNo($cardNo, $clientkeynum)
    {
        return $this->where('card_no', $cardNo)
            ->where('clientkeynum', $clientkeynum)
            ->find();
    }
    
    /**
     * 更新卡状态
     * @param int $id 卡ID
     * @param int $status 新状态
     * @param int $operatorId 操作人ID
     * @param string $operatorName 操作人姓名
     * @param string $reason 操作原因
     * @param string $remark 备注
     * @param string $clientkeynum 客户唯一标识
     * @param int $batchId 批次ID
     * @return bool
     * @throws \think\Exception
     * @throws \think\exception\PDOException
     */
    public function updateCardStatus($id, $status, $operatorId, $operatorName, $reason = '', $remark = '', $clientkeynum = '', $batchId = 0)
    {
        // 开启事务
        $this->startTrans();
        try {
            // 获取卡信息
            $card = $this->where('id', $id)->find();
            if (!$card) {
                $this->rollback();
                return false;
            }
            
            $oldStatus = $card->status;
            
            // 更新卡状态
            $card->status = $status;
            $card->update_time = date('Y-m-d H:i:s');
            $card->save();
            
            // 记录状态变更
            $statusRecord = new CardStatusRecord;
            $statusRecord->clientkeynum = $clientkeynum ?: $card->clientkeynum;
            $statusRecord->card_id = $id;
            $statusRecord->old_status = $oldStatus;
            $statusRecord->new_status = $status;
            $statusRecord->operator_id = $operatorId;
            $statusRecord->operator_name = $operatorName;
            $statusRecord->operation_time = date('Y-m-d H:i:s');
            $statusRecord->reason = $reason;
            $statusRecord->remark = $remark;
            $statusRecord->add_time = date('Y-m-d H:i:s');
            $statusRecord->batch_id = $batchId;
            $statusRecord->save();
            
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * 根据卡号列表获取退卡预览数据
     * @param array $cardNos 卡号列表
     * @param int $customer_id 客户ID
     * @return array
     * @throws \Exception
     */
    public function getRefundPreviewData($cardNos, $customer_id)
    {
        // 查询卡片信息
        $cards = $this->alias('c')
            ->join('card_package_type t', 't.id = c.type_id')
            ->join('card_sale_record s', 's.card_id = c.id')
            ->where('c.card_no', 'in', $cardNos)
            ->where('c.customer_id', $customer_id)
            ->field('c.id, c.card_no, t.name as type_name, s.sale_price, c.status')
            ->select();

        if (empty($cards)) {
            throw new \Exception('未找到符合条件的卡片');
        }

        // 检查卡片状态
        $refundableCount = 0;
        $nonRefundableCount = 0;
        $refundableAmount = 0;
        
        foreach ($cards as &$card) {
            $originalStatus = $card['status']; // 保存原始状态值用于判断
            $card['status'] = $this->getStatusText($card['status']);
            
            // 判断是否可退（已销售或已开卡的卡）
            if (in_array($originalStatus, [self::STATUS_SOLD, self::STATUS_OPENED])) { 
                $refundableCount++;
                $refundableAmount += floatval($card['sale_price']);
            } else {
                $nonRefundableCount++;
            }
        }

        // 添加统计信息
        $stats = [
            'refundable_count' => $refundableCount,
            'non_refundable_count' => $nonRefundableCount,
            'refundable_amount' => number_format($refundableAmount, 2, '.', '')
        ];

        return [
            'data' => $cards,
            'stats' => $stats
        ];
    }

    /**
     * 根据参数获取退卡预览数据
     * @param array $params 参数
     * @return array
     * @throws \Exception
     */
    public function getRefundPreviewByParams($params)
    {
        $where = [];
        $where[] = ['c.customer_id', '=', $params['customer_id']];

        // 根据销售单号查询
        if (!empty($params['sale_number'])) {
            $where[] = ['s.batch_no', '=', $params['sale_number']];
        }

        // 根据卡号范围查询
        if (!empty($params['start_card_no']) && !empty($params['end_card_no'])) {
            // 提取前缀字母部分和数字部分
            preg_match('/([A-Za-z]*)(\d+)/', $params['start_card_no'], $startMatches);
            preg_match('/([A-Za-z]*)(\d+)/', $params['end_card_no'], $endMatches);
            
            if (count($startMatches) >= 3 && count($endMatches) >= 3) {
                $prefix = $startMatches[1]; // 字母前缀
                $startNum = intval($startMatches[2]); // 起始数字
                $endNum = intval($endMatches[2]); // 结束数字
                
                // 确保前缀相同
                if ($prefix === $endMatches[1] && $startNum <= $endNum) {
                    // 生成卡号列表
                    $cardNos = [];
                    for ($i = $startNum; $i <= $endNum; $i++) {
                        $cardNos[] = $prefix . str_pad($i, strlen($startMatches[2]), '0', STR_PAD_LEFT);
                    }
                    
                    // 使用IN查询
                    if (!empty($cardNos)) {
                        $where[] = ['c.card_no', 'in', $cardNos];
                    }
                } else {
                    throw new \Exception('卡号范围前缀必须相同，且起始卡号必须小于等于结束卡号');
                }
            } else {
                throw new \Exception('卡号格式不正确，应为字母前缀+数字');
            }
        }

        // 查询卡片信息
        $cards = $this->alias('c')
            ->join('card_package_type t', 't.id = c.type_id')
            ->join('card_sale_record s', 's.card_id = c.id')
            ->where($where)
            ->field('c.id, c.card_no, t.name as type_name, s.sale_price, c.status')
            ->select();
        
        if (empty($cards)) {
            throw new \Exception('未找到符合条件的卡片');
        }

        // 检查卡片状态并提取卡号
        $plainCardNos = [];
        $refundableCount = 0;
        $nonRefundableCount = 0;
        $refundableAmount = 0;
        
        foreach ($cards as &$card) {
            $originalStatus = $card['status']; // 保存原始状态值用于判断
            $card['status'] = $this->getStatusText($card['status']);
            $plainCardNos[] = $card['card_no'];
            
            // 判断是否可退（已销售或已开卡的卡）
            if (in_array($originalStatus, [self::STATUS_SOLD, self::STATUS_OPENED])) {
                $refundableCount++;
                $refundableAmount += floatval($card['sale_price']);
            } else {
                $nonRefundableCount++;
            }
        }

        // 添加统计信息
        $stats = [
            'refundable_count' => $refundableCount,
            'non_refundable_count' => $nonRefundableCount,
            'refundable_amount' => number_format($refundableAmount, 2, '.', '')
        ];

        return [
            'data' => $cards,
            'plain_card_nos' => $plainCardNos,
            'stats' => $stats
        ];
    }

    /**
     * 执行退卡操作
     * @param array $cardNos 卡号列表
     * @param array $params 参数
     * @return bool|string
     * @throws \Exception
     */
    public function refundCards($cardNos, $params)
    {
        $clientkeynum = session("cn_accountinfo.basekeynum");

        $operator = session('cn_accountinfo.accountname');
        $operator_id = session('cn_accountinfo.account_id');
        // 开启事务
        $this->startTrans();
        try {
            // 查询卡片信息
            $cards = $this->alias('c')
                ->join('card_sale_record s', 's.card_id = c.id')
                ->where('c.card_no', 'in', $cardNos)
                ->where('c.customer_id', $params['customer_id'])
                ->field('c.id, c.card_no, c.type_id, c.status, s.sale_price, s.sale_time, s.batch_no')
                ->select();

            if (empty($cards)) {
                throw new \Exception('未找到符合条件的卡片');
            }

            // 生成退卡批次号
            $batchNo = 'RF' . date('YmdHis') . rand(1000, 9999);
            
            // 创建退卡批次记录
            $batchData = [
                'clientkeynum' => $clientkeynum,
                'batch_no' => $batchNo,
                'batch_name' => $params['batch_name'],
                'sale_batch_no' => $cards[0]['batch_no'], // 使用第一张卡的销售批次号
                'customer_id' => $params['customer_id'],
                'total_amount' => 0, // 初始化总金额
                'card_count' => count($cards),
                'refund_time' => date('Y-m-d H:i:s'),
                'reason' => $params['reason'],
                'remark' => $params['remark'] ?? '',
                'operator_id' => $operator_id,
                'operator_name' => $operator,
                'status' => 0,
                'add_time' => date('Y-m-d H:i:s')
            ];

            $batchId = model('CardRefundBatch')->insertGetId($batchData);

            // 处理每张卡
            $totalAmount = 0;
            $detailData = [];
            $statusData = [];
            $cardIds = [];

            foreach ($cards as $card) {
                // 验证卡片状态
                if (!in_array($card['status'], [self::STATUS_SOLD, self::STATUS_REDEEMED, self::STATUS_OPENED])) { // 只允许已销售和已兑换和已开卡的卡退卡
                    throw new \Exception("卡号{$card['card_no']}状态不允许退卡");
                }

                // 准备退卡明细数据
                $detailData[] = [
                    'clientkeynum' => $clientkeynum,
                    'batch_id' => $batchId,
                    'batch_no' => $batchNo,
                    'card_id' => $card['id'],
                    'card_no' => $card['card_no'],
                    'type_id' => $card['type_id'],
                    'sale_price' => $card['sale_price'],
                    'refund_amount' => $card['sale_price'],
                    'sale_time' => $card['sale_time'],
                    'status' => 0,
                    'reason' => $params['reason'],
                    'remark' => $params['remark'] ?? '',
                    'add_time' => date('Y-m-d H:i:s')
                ];

                // 准备状态变更记录数据
                $statusData[] = [
                    'clientkeynum' => $clientkeynum,
                    'card_id' => $card['id'],
                    'old_status' => $card['status'],
                    'new_status' => self::STATUS_RETURNED, // 设置为退卡状态
                    'operator_id' => $operator_id,
                    'operator_name' => $operator,
                    'operation_time' => date('Y-m-d H:i:s'),
                    'reason' => $params['reason'],
                    'remark' => $params['remark'] ?? '',
                    'add_time' => date('Y-m-d H:i:s'),
                    'batch_id' => $batchId
                ];

                $totalAmount += $card['sale_price'];
                $cardIds[] = $card['id'];
            }

            // 批量插入退卡明细
            model('CardRefundBatchDetail')->insertAll($detailData);

            // 批量插入状态变更记录
            model('CardStatusRecord')->insertAll($statusData);

            // 更新卡片状态
            $this->where('id', 'in', $cardIds)->update([
                'status' => self::STATUS_RETURNED,
                'update_time' => date('Y-m-d H:i:s')
            ]);

            // 更新退卡批次总金额
            model('CardRefundBatch')->where('id', $batchId)->update([
                'total_amount' => $totalAmount,
                'actual_refund_amount' => $params['actual_refund_amount'],
                'status' => 1,
                'update_time' => date('Y-m-d H:i:s')
            ]);

            // 提交事务
            $this->commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            $this->rollback();
            return $e->getMessage();
        }
    }

    /**
     * 获取状态文本
     * @param int $status 状态值
     * @return string
     */
    private function getStatusText($status)
    {
        $statusMap = [
            0 => '未激活',
            1 => '已激活',
            2 => '已使用',
            3 => '已过期',
            4 => '已作废',
            5 => '已废卡',
            -1 => '已过期',
            -2 => '已退卡'
        ];
        return isset($statusMap[$status]) ? $statusMap[$status] : '未知状态';
    }
} 






