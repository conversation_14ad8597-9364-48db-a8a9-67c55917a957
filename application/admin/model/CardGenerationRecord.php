<?php
namespace app\admin\model;

use think\Model;

/**
 * 卡生成记录模型
 */
class CardGenerationRecord extends Model
{
    protected $pk = 'id';
    
    // 设置数据表名
    protected $table = 'card_generation_record';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = false;
    


    /**
     * 关联生成详情
     */
    public function details()
    {
        return $this->hasMany('CardGenerationDetail', 'generation_id');
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = isset($data['status']) ? $data['status'] : $this->status;
        $statusMap = [
            0 => '生成中',
            1 => '生成完成',
            2 => '生成失败'
        ];
        return isset($statusMap[$status]) ? $statusMap[$status] : '未知状态';
    }
    
    /**
     * 获取密码类型文本
     */
    public function getPasswordTypeTextAttr($value, $data)
    {
        $type = isset($data['password_type']) ? $data['password_type'] : $this->password_type;
        $typeMap = [
            0 => '无密码',
            1 => '随机密码',
            2 => '固定密码'
        ];
        return isset($typeMap[$type]) ? $typeMap[$type] : '未知类型';
    }
    
    /**
     * 获取生成方式文本
     */
    public function getGenerationMethodTextAttr($value, $data)
    {
        $method = isset($data['generation_method']) ? $data['generation_method'] : $this->generation_method;
        $methodMap = [
            0 => '连续编号',
            1 => '随机编号',
            2 => '自定义范围'
        ];
        return isset($methodMap[$method]) ? $methodMap[$method] : '未知方式';
    }
} 