<?php

namespace app\admin\model;

use think\Model;

class CardRefundBatch extends Model
{
    protected $table = 'card_refund_batch';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = false;
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    /**
     * 关联客户
     * @return \think\model\relation\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo('CardCustomer', 'customer_id');
    }
    
    /**
     * 关联退卡明细
     * @return \think\model\relation\HasMany
     */
    public function details()
    {
        return $this->hasMany('CardRefundBatchDetail', 'batch_id');
    }
    
    /**
     * 获取退卡批次列表
     * @param array $where 查询条件
     * @param string $sort 排序
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getList($where = [], $sort = 'id desc', $page = 1, $limit = 15)
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        $where[] = ['clientkeynum', '=', $clientkeynum];
        
        $total = $this->where($where)->count();
        $list = $this->with(['customer' => function($query) {
            $query->field('id,name');
        }])->where($where)
            ->field('id, batch_no, customer_id, total_amount, actual_refund_amount, card_count, refund_time, refund_type, reason, operator_name, status, add_time')
            ->order($sort)
            ->page($page, $limit)
            ->select();
        
        return [
            'total' => $total,
            'list' => $list
        ];
    }
    
    /**
     * 获取退卡批次详情
     * @param int $id 批次ID
     * @return array|null
     */
    public function getDetail($id)
    {
        $clientkeynum = session('cn_accountinfo.basekeynum');
        
        $batch = $this->with(['customer' => function($query) {
            $query->field('id,name');
        }])->where('id', $id)
            ->where('clientkeynum', $clientkeynum)
            ->find();
        
        if (!$batch) {
            return null;
        }
        
        $details = model('CardRefundBatchDetail')->with(['cardType' => function($query) {
            $query->field('id,name');
        }])->where('batch_id', $id)
            ->where('clientkeynum', $clientkeynum)
            ->select();
        
        return [
            'batch' => $batch,
            'details' => $details
        ];
    }
    
    /**
     * 获取退款方式文本
     * @param int $refundType 退款方式
     * @return string
     */
    public static function getRefundTypeText($refundType)
    {
        $types = [
            1 => '原路退回',
            2 => '现金退款',
            3 => '其他'
        ];
        
        return isset($types[$refundType]) ? $types[$refundType] : '未知';
    }
    
    /**
     * 获取状态文本
     * @param int $status 状态
     * @return string
     */
    public static function getStatusText($status)
    {
        $statusMap = [
            0 => '处理中',
            1 => '已完成',
            2 => '已取消'
        ];
        
        return isset($statusMap[$status]) ? $statusMap[$status] : '未知';
    }
}