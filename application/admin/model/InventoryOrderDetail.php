<?php
namespace app\admin\model;

use think\Model;
use think\Db;
use think\facade\Log;
/**
 * 库存单明细表模型
 * Class InventoryOrderDetail
 * @package app\admin\model
 */
class InventoryOrderDetail extends Model
{
    protected $name = 'inventory_order_detail';
    
    // 自动完成字段
    protected $auto = ['amount'];
    
    /**
     * 金额自动计算
     * @param mixed $value
     * @param array $data
     * @return float
     */
    protected function setAmountAttr($value, $data)
    {
        // 如果是销售出库，需要考虑折扣
        if (isset($data['order_type']) && $data['order_type'] == 3 && isset($data['discount'])) {
            return round($data['quantity'] * $data['price'] - $data['discount'], 2);
        } else {
            return round($data['quantity'] * $data['price'], 2);
        }
    }
    
    /**
     * 批量添加订单明细
     * @param int $order_id 订单ID
     * @param string $order_no 订单编号
     * @param int $order_type 订单类型
     * @param array $details 明细数组
     * @param int $shop_id 店铺ID
     * @param string $clientkeynum 客户端标识
     * @return bool
     */
    public function addBatch($order_id, $order_no, $order_type, $details, $shop_id, $clientkeynum = '')
    {
        if (empty($details)) {
            return false;
        }
        
        $data = [];
        $total_amount = 0;
        
        foreach ($details as $item) {
            // 计算金额
            $amount = $order_type == 3 && isset($item['discount']) ? 
                      round($item['quantity'] * $item['price'] - $item['discount'], 2) : 
                      round($item['quantity'] * $item['price'], 2);
            
            $total_amount += $amount;
            
            $data[] = [
                'order_id' => $order_id,
                'order_no' => $order_no,
                'order_type' => $order_type,
                'shop_id' => $shop_id,
                'product_id' => $item['product_id'],
                'inventory_id' => $item['inventory_id'],
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'discount' => isset($item['discount']) ? $item['discount'] : 0,
                'amount' => $amount,
                'remark' => isset($item['remark']) ? $item['remark'] : '',
                'clientkeynum' => $clientkeynum,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 插入明细
            $this->insertAll($data);
            
            // 更新主表金额
            Db::name('inventory_order')->where('id', $order_id)->update([
                'total_amount' => $total_amount,
                'actual_amount' => $total_amount
            ]);
            
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Log::info('添加库存单明细失败: ' . $e->getMessage());
            Log::info($e->getTraceAsString());
            Db::rollback();
            return false;
        }
    }
    
    /**
     * 删除订单明细
     * @param int $order_id 订单ID
     * @return bool
     */
    public function removeByOrderId($order_id)
    {
        // 开启事务
        Db::startTrans();
        try {
            // 删除明细
            $this->where('order_id', $order_id)->delete();
            
            // 更新主表金额
            Db::name('inventory_order')->where('id', $order_id)->update([
                'total_amount' => 0,
                'actual_amount' => 0
            ]);
            
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return false;
        }
    }
    
    /**
     * 获取订单明细列表
     * @param int $order_id 订单ID
     * @return array
     */
    public function getListByOrderId($order_id)
    {
        return $this->alias('d')
            ->join('product p', 'p.id = d.product_id')
            ->join('inventory i', 'i.id = d.inventory_id')
            ->where('d.order_id', $order_id)
            ->field('d.*, p.name as product_name, i.spec_info, d.clientkeynum')
            ->select();
    }
} 