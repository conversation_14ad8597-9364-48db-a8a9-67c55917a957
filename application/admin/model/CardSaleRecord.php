<?php
/**
 * 卡销售记录表模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use think\Model;

class CardSaleRecord extends Model
{
    protected $table = 'card_sale_record';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    /**
     * 关联卡
     * @return \think\model\relation\BelongsTo
     */
    public function card()
    {
        return $this->belongsTo('Card', 'card_id', 'id');
    }
    
    /**
     * 关联销售批次
     * @return \think\model\relation\BelongsTo
     */
    public function saleBatch()
    {
        return $this->belongsTo('CardSaleBatch', 'batch_id', 'id');
    }
    
    /**
     * 关联卡型
     * @return \think\model\relation\BelongsTo
     */
    public function cardType()
    {
        return $this->belongsTo('CardPackageType', 'type_id', 'id');
    }
    
    /**
     * 关联客户
     * @return \think\model\relation\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo('ClientMember', 'customer_id', 'id');
    }
    
    /**
     * 关联销售员
     * @return \think\model\relation\BelongsTo
     */
    public function salesperson()
    {
        return $this->belongsTo('ClientMember', 'salesperson_id', 'id');
    }
    
    /**
     * 添加卡销售记录
     * @param array $data 销售记录数据
     * @return int|string 新增ID
     */
    public function addSaleRecord($data)
    {
        $this->clientkeynum = $data['clientkeynum'];
        $this->card_id = $data['card_id'];
        $this->card_no = isset($data['card_no']) ? $data['card_no'] : '';
        $this->type_id = isset($data['type_id']) ? $data['type_id'] : 0;
        $this->customer_id = $data['customer_id'];
        $this->salesperson_id = $data['salesperson_id'];
        $this->sale_price = $data['sale_price'];
        $this->zhekou = isset($data['zhekou']) ? $data['zhekou'] : 100;
        $this->sale_time = isset($data['sale_time']) ? $data['sale_time'] : date('Y-m-d H:i:s');
        $this->payment_method = isset($data['payment_method']) ? $data['payment_method'] : '';
        $this->transaction_no = isset($data['transaction_no']) ? $data['transaction_no'] : '';
        $this->batch_no = isset($data['batch_no']) ? $data['batch_no'] : '';
        $this->batch_id = isset($data['batch_id']) ? $data['batch_id'] : 0;
        $this->remark = isset($data['remark']) ? $data['remark'] : '';
        $this->operator_id = isset($data['operator_id']) ? $data['operator_id'] : 0;
        $this->operator_name = isset($data['operator_name']) ? $data['operator_name'] : '';
        $this->add_time = date('Y-m-d H:i:s');
        $this->update_time = date('Y-m-d H:i:s');
        $this->start_time = isset($data['start_time']) ? $data['start_time'] : null;
        $this->end_time = isset($data['end_time']) ? $data['end_time'] : null;
        $this->save();
        
        return $this->id;
    }
    
    /**
     * 获取卡销售记录
     * @param int $cardId 卡ID
     * @param string $clientkeynum 客户唯一标识
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getCardSaleRecord($cardId, $clientkeynum)
    {
        return $this->with(['customer', 'salesperson'])
            ->where('card_id', $cardId)
            ->where('clientkeynum', $clientkeynum)
            ->find();
    }
} 