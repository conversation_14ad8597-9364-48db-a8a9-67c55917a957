<?php

/**
 * 食谱模型
 * @date 2024-10-31
 */

namespace app\admin\model;

use think\Db;
use think\Model;
use think\facade\Log;

class Recipe extends Model
{
    protected $pk = 'id';

    protected $table = 'recipe';

    /**
     * 删除食谱
     * @param $id
     * @param array $where
     * @return bool
     */
    public static function del($id, $where = [])
    {
        $where['id'] = $id;
        // 开启事务
        Db::startTrans();
        try {
            // 删除食谱
            self::where($where)->delete();
            // 删除食谱标签关联
            Db::table('recipe_tag_relation')->where('recipe_id', $id)->delete();
            // 提交事务
            Db::commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return false;
        }
    }

    /**
     * 修改食谱
     * @param $where
     * @param $data
     * @param $tags array 标签ID数组
     * @return bool
     */
    public static function edit($where, $data, $tags = [])
    {
        // 开启事务
        Db::startTrans();
        try {
            // 更新食谱信息
            self::where($where)->update($data);

            // 如果有标签数据，则更新标签关联
            if (!empty($tags)) {
                $recipe_id = $where['id'];
                // 删除原有标签关联
                Db::table('recipe_tag_relation')->where('recipe_id', $recipe_id)->delete();

                // 添加新的标签关联
                $tag_data = [];
                foreach ($tags as $str) {
                    $tag_id = explode(',', $str);
                    foreach ($tag_id as $id) {
                        $tag_data[] = [
                            'recipe_id' => $recipe_id,
                            'tag_id' => $id,
                            'create_time' => date('Y-m-d H:i:s')
                        ];
                    }
                }
                Db::table('recipe_tag_relation')->insertAll($tag_data);
            }

            // 提交事务
            Db::commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }
    }

    /**
     * 根据id查询数据
     * @param $id
     * @param $where
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getInfoById($id, $where = [])
    {
        $where['id'] = ['=', $id];
        $info = self::where($where)->find();

        if ($info) {
            $info = $info->toArray();

            // 获取分类名称
            $category = RecipeCategory::getInfoById($info['category_id']);
            $info['category_name'] = $category ? $category['name'] : '';

            // 获取标签信息
            $tags = Db::table('recipe_tag_relation')
                ->alias('r')
                ->join('recipe_tag t', 'r.tag_id = t.id')
                ->where('r.recipe_id', $id)
                ->field('t.id, t.name')
                ->select();
            $info['tags'] = $tags;

            // 处理图片JSON数据
            if (!empty($info['images'])) {
                $info['images_array'] = json_decode($info['images'], true);
            } else {
                $info['images_array'] = [];
            }
        }

        return $info;
    }

    /**
     * 添加食谱
     * @param $data
     * @param $tags array 标签ID数组
     * @return false|int
     */
    public static function add($data, $tags = [])
    {
        // 开启事务
        Db::startTrans();
        try {
            // 添加食谱
            $model = new self();
            $model->title = $data['title'];
            $model->user_id = $data['user_id'];
            $model->category_id = $data['category_id'];
            $model->cover_image = isset($data['cover_image']) ? $data['cover_image'] : '';
            $model->description = isset($data['description']) ? $data['description'] : '';
            $model->cooking_time = isset($data['cooking_time']) ? $data['cooking_time'] : 0;
            $model->serving_size = isset($data['serving_size']) ? $data['serving_size'] : 0;
            $model->difficulty = isset($data['difficulty']) ? $data['difficulty'] : 1;
            $model->content_type = isset($data['content_type']) ? $data['content_type'] : 1;
            $model->video_url = isset($data['video_url']) ? $data['video_url'] : '';
            $model->video_duration = isset($data['video_duration']) ? $data['video_duration'] : 0;
            $model->images = isset($data['images']) ? $data['images'] : '';
            $model->status = isset($data['status']) ? $data['status'] : 1;
            $model->create_time = date('Y-m-d H:i:s');
            $model->save();

            $recipe_id = $model->id;

            // 如果有标签数据，则添加标签关联
            if (!empty($tags)) {
                $tag_data = [];
                foreach ($tags as $str) {
                    $tag_id = explode(',', $str);
                    foreach ($tag_id as $id) {
                        $tag_data[] = [
                            'recipe_id' => $recipe_id,
                            'tag_id' => $id,
                            'create_time' => date('Y-m-d H:i:s')
                        ];
                    }
                }
                Db::table('recipe_tag_relation')->insertAll($tag_data);
            }

            // 提交事务
            Db::commit();
            return $recipe_id;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return false;
        }
    }

    /**
     * 获取食谱列表
     * @param $where array 查询条件
     * @param $page int 页数
     * @param $pagesize int 每页条数
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getList($where, $page, $pagesize)
    {
        $list = self::where($where)
            ->order('id', 'desc')
            ->page($page, $pagesize)
            ->select();

        $result = [];
        foreach ($list as $item) {
            $data = $item->toArray();

            // 获取分类名称
            $category = RecipeCategory::getInfoById($data['category_id']);
            $data['category_name'] = $category ? $category['name'] : '';

            // 获取标签信息
            $tags = Db::table('recipe_tag_relation')
                ->alias('r')
                ->join('recipe_tag t', 'r.tag_id = t.id')
                ->where('r.recipe_id', $data['id'])
                ->field('t.id, t.name')
                ->select();
            $data['tags'] = $tags;

            $result[] = $data;
        }

        return $result;
    }

    /**
     * 获取总条数
     * @param $where array 查询条件
     * @return int|string
     * @throws \think\Exception
     */
    public static function getCount($where)
    {
        return self::where($where)
            ->count();
    }

    /**
     * 更新浏览次数
     * @param $id
     * @return bool
     */
    public static function updateViews($id)
    {
        return self::where('id', $id)->setInc('views');
    }

    /**
     * 更新点赞次数
     * @param $id
     * @return bool
     */
    public static function updateLikes($id)
    {
        return self::where('id', $id)->setInc('likes');
    }

    /**
     * 更新收藏次数
     * @param $id
     * @return bool
     */
    public static function updateFavorites($id)
    {
        return self::where('id', $id)->setInc('favorites');
    }
}
