<?php
// +----------------------------------------------------------------------
// | 售后退款记录模型
// +----------------------------------------------------------------------
namespace app\admin\model;

use think\Model;

/**
 * 售后退款记录模型
 * 用于记录售后申请的退款信息
 */
class AfterSalesRefund extends Model
{
    // 设置表名
    protected $table = 'after_sales_refund';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 时间字段取值
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 退款类型定义
     */
    const TYPE_WECHAT = 1;    // 微信退款
    const TYPE_CARD = 2;      // 卡余额退款
    const TYPE_COMBO = 3;     // 组合退款
    
    /**
     * 退款状态定义
     */
    const STATUS_PROCESSING = 0;  // 处理中
    const STATUS_SUCCESS = 1;     // 成功
    const STATUS_FAILED = 2;      // 失败
    
    /**
     * 退款渠道定义
     */
    const CHANNEL_WECHAT = 'wechat';  // 微信
    const CHANNEL_CARD = 'card';      // 卡支付
    const CHANNEL_CASH = 'cash';      // 现金
    const CHANNEL_COMBO = 'combo';    // 组合
    
    /**
     * 关联售后订单
     * @return \think\model\relation\BelongsTo
     */
    public function afterSalesOrder()
    {
        return $this->belongsTo('AfterSalesOrder', 'after_sales_id', 'id');
    }
    
    /**
     * 获取退款类型文本
     * @param int $type
     * @return string
     */
    public static function getRefundTypeText($type)
    {
        $typeMap = [
            self::TYPE_WECHAT => '微信退款',
            self::TYPE_CARD => '卡余额退款',
            self::TYPE_COMBO => '组合退款',
        ];
        
        return $typeMap[$type] ?? '未知类型';
    }
    
    /**
     * 获取退款状态文本
     * @param int $status
     * @return string
     */
    public static function getRefundStatusText($status)
    {
        $statusMap = [
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_SUCCESS => '成功',
            self::STATUS_FAILED => '失败',
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }
    
    /**
     * 获取退款渠道文本
     * @param string $channel
     * @return string
     */
    public static function getRefundChannelText($channel)
    {
        $channelMap = [
            self::CHANNEL_WECHAT => '微信支付',
            self::CHANNEL_CARD => '卡支付',
            self::CHANNEL_CASH => '现金支付',
            self::CHANNEL_COMBO => '组合支付',
        ];
        
        return $channelMap[$channel] ?? '未知渠道';
    }
    
    /**
     * 获取退款详细数据（从JSON字段解析）
     * @return array
     */
    public function getRefundDataAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }
    
    /**
     * 设置退款详细数据（转为JSON格式）
     * @param array $value
     * @return string
     */
    public function setRefundDataAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }
} 