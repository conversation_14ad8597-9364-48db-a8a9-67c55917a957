<?php
/**
 * 订单模型
 * @date 2023-6-5
 * <AUTHOR>
 */

namespace app\admin\model;


use app\admin\controller\MemberController;
use think\Model;

class ClientOrderInfo extends Model
{
    protected $pk = 'id';

    protected $table = 'client_order_info';

    public function getAddTimeAttr($value)
    {
        if (!empty($value))
        return date('Y-m-d H:i:s',$value);
    }

    public function getPayTypeAttr($value)
    {
        $arr = [
            1 => '微信支付'
        ];
        return $arr[$value];
    }

    public function getPayStatusAttr($value)
    {
        $arr = [
            1 => '待支付',
            2 => '已支付',
            -1 => '已过期'
        ];
        return $arr[$value];
    }

    /**
     * 查单个信息
     * @param $where
     * @return array|bool|\PDOStatement|string|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getInfo($where)
    {
        return self::where($where)->find();
    }

    /**
     * 查列表
     * @param $where
     * @param int $page
     * @param int $page_size
     * @return bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getList($where, $page = 1, $page_size = 10)
    {
        return self::where($where)->order('add_time', 'desc')->page($page, $page_size)->select();
    }

    public static function getCount($where)
    {
        return self::where($where)->count();
    }
}
