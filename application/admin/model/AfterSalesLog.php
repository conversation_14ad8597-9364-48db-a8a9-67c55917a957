<?php
// +----------------------------------------------------------------------
// | 售后日志模型
// +----------------------------------------------------------------------
namespace app\admin\model;

use think\Model;

/**
 * 售后日志模型
 * 用于记录售后申请的操作日志
 */
class AfterSalesLog extends Model
{
    // 设置表名
    protected $table = 'after_sales_log';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = 'datetime';
    
    // 时间字段取值
    protected $createTime = 'created_at';
    protected $updateTime = false;
    
    /**
     * 操作类型定义
     */
    const ACTION_APPLY = 'apply';          // 申请
    const ACTION_AUDIT = 'audit';          // 审核
    const ACTION_REFUND = 'refund';        // 退款
    const ACTION_COMPLETE = 'complete';    // 完成
    const ACTION_CANCEL = 'cancel';        // 取消
    
    /**
     * 操作者类型定义
     */
    const OPERATOR_USER = 1;      // 用户
    const OPERATOR_ADMIN = 2;     // 管理员
    const OPERATOR_SYSTEM = 3;    // 系统
    
    /**
     * 关联售后订单
     * @return \think\model\relation\BelongsTo
     */
    public function afterSalesOrder()
    {
        return $this->belongsTo('AfterSalesOrder', 'after_sales_id', 'id');
    }
    
    /**
     * 获取操作类型文本
     * @param string $action_type
     * @return string
     */
    public static function getActionTypeText($action_type)
    {
        $actionMap = [
            self::ACTION_APPLY => '申请售后',
            self::ACTION_AUDIT => '审核处理',
            self::ACTION_REFUND => '退款处理',
            self::ACTION_COMPLETE => '完成售后',
            self::ACTION_CANCEL => '取消售后',
        ];
        
        return $actionMap[$action_type] ?? '未知操作';
    }
    
    /**
     * 获取操作者类型文本
     * @param int $operator_type
     * @return string
     */
    public static function getOperatorTypeText($operator_type)
    {
        $operatorMap = [
            self::OPERATOR_USER => '用户',
            self::OPERATOR_ADMIN => '管理员',
            self::OPERATOR_SYSTEM => '系统',
        ];
        
        return $operatorMap[$operator_type] ?? '未知';
    }
    
    /**
     * 获取额外数据（从JSON字段解析）
     * @return array
     */
    public function getExtraDataAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }
    
    /**
     * 设置额外数据（转为JSON格式）
     * @param array $value
     * @return string
     */
    public function setExtraDataAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }
} 