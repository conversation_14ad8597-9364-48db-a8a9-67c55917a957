<?php
/**
 * 订单详情模型
 * @date 2023-6-15
 * <AUTHOR>
 */

namespace app\admin\model;


use think\Model;

class ClientOrderDetail extends Model
{
    protected $table = 'client_order_orderdetail';

    protected $pk = 'id';

    public function getShippingTimeAttr($value)
    {
        if (!empty($value)) {
            return date('Y-m-d H:i:s', $value);
        } else return '暂未发货';
    }

    /**
     * 列表
     * @param $where
     * @param int $page
     * @param int $page_size
     * @return bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getList($where, $page = 1, $page_size = 10)
    {
        $list = self::where($where);
        if ($page > 0) {
            $list = $list->page($page, $page_size);
        }
        return $list->select();
    }

    public static function getCount($where)
    {
        return self::where($where)->count();
    }
}
