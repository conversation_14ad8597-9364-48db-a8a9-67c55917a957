<?php

namespace app\admin\model;

use think\Model;
use think\Db;

/**
 * 门店打印机配置模型
 * Class YlyShopPrinterConfig
 * @package app\admin\model
 */
class YlyShopPrinterConfig extends Model
{
    // 表名
    protected $table = 'yly_shop_printer_config';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 关联门店信息
     */
    public function shop()
    {
        return $this->belongsTo('Shop', 'shop_id', 'id');
    }
    
    /**
     * 根据门店ID获取打印机配置
     * @param int $shopId
     * @return array|null
     */
    public static function getByShopId($shopId)
    {
        return self::where('shop_id', $shopId)->find();
    }
    
    /**
     * 根据门店ID和客户端标识获取配置
     * @param int $shopId
     * @param string $clientkeynum
     * @return array|null
     */
    public static function getByShopAndClient($shopId, $clientkeynum)
    {
        return self::where([
            'shop_id' => $shopId,
            'clientkeynum' => $clientkeynum,
        ])->find();
    }
    
    /**
     * 保存或更新门店打印机配置
     * @param array $data
     * @return bool
     */
    public static function saveConfig($data)
    {
        $shopId = $data['shop_id'];
        $config = self::where('shop_id', $shopId)->find();
        
        if ($config) {
            // 更新配置
            return $config->save($data);
        } else {
            // 新增配置
            $model = new self();
            return $model->save($data);
        }
    }
    
    /**
     * 获取分页列表
     * @param array $where
     * @param int $pageSize
     * @return \think\Paginator
     */
    public static function getList($where = [], $pageSize = 20)
    {
        $query = self::with('shop');
        
        // 按条件筛选
        if (!empty($where['clientkeynum'])) {
            $query->where('clientkeynum', $where['clientkeynum']);
        }
        
        if (!empty($where['shop_id'])) {
            $query->where('shop_id', $where['shop_id']);
        }
        
        
        if (!empty($where['printer_name'])) {
            $query->where('printer_name', 'like', '%' . $where['printer_name'] . '%');
        }
        
        return $query->order('id desc')->paginate($pageSize);
    }
    
    /**
     * 检查打印机序列号是否已存在
     * @param string $printerSn
     * @param int $excludeId
     * @return bool
     */
    public static function checkPrinterSnExists($printerSn, $excludeId = 0)
    {
        $query = self::where('printer_sn', $printerSn);
        if ($excludeId > 0) {
            $query->where('id', '<>', $excludeId);
        }
        return $query->count() > 0;
    }
    
    /**
     * 批量更新状态
     * @param array $ids
     * @param int $status
     * @return bool
     */
    public static function batchUpdateStatus($ids, $status)
    {
        if (empty($ids)) {
            return false;
        }
        
        return self::whereIn('id', $ids)->update(['status' => $status]);
    }
    
    /**
     * 获取启用状态的打印机配置
     * @param int $shopId
     * @return array|null
     */
    public static function getActivePrinterByShopId($shopId)
    {
        return self::where([
            'shop_id' => $shopId,
        ])->find();
    }
    
    /**
     * 更新打印统计
     * @param int $id
     * @return bool
     */
    public static function updatePrintCount($id)
    {
        return self::where('id', $id)->update([
            'print_count' => Db::raw('print_count + 1'),
            'last_print_time' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 获取所有门店打印机配置列表
     * @param string $clientkeynum
     * @param array $condition
     * @return array
     */
    public static function getConfigList($clientkeynum, $condition = [])
    {
        $where = ['clientkeynum' => $clientkeynum];
        if (!empty($condition)) {
            $where = array_merge($where, $condition);
        }
        
        return self::where($where)
            ->order('created_at desc')
            ->paginate(15)
            ->each(function($item) {
                // 关联门店信息（需要根据实际shop表调整）
                $item['shop_name'] = Db::name('shop')->where('id', $item['shop_id'])->value('shop_name') ?: '未知门店';
                return $item;
            });
    }
    
    /**
     * 获取打印机配置统计
     * @param string $clientkeynum
     * @return array
     */
    public static function getStatistics($clientkeynum)
    {
        $total = self::where('clientkeynum', $clientkeynum)->count();
        $todayPrint = self::where('clientkeynum', $clientkeynum)
            ->where('last_print_time', '>=', date('Y-m-d 00:00:00'))
            ->count();
        
        return [
            'total_printers' => $total,
            'today_printed' => $todayPrint
        ];
    }
    
    /**
     * 验证打印机配置
     * @param array $data
     * @return array
     */
    public static function validateConfig($data)
    {
        $errors = [];
        
        if (empty($data['shop_id'])) {
            $errors[] = '门店ID不能为空';
        }
        
        if (empty($data['printer_sn'])) {
            $errors[] = '打印机序列号不能为空';
        }
        
        if (empty($data['printer_key'])) {
            $errors[] = '打印机密钥不能为空';
        }
        
        if (!empty($data['print_copies']) && ($data['print_copies'] < 1 || $data['print_copies'] > 5)) {
            $errors[] = '打印份数必须在1-5之间';
        }
        
        // 检查是否已存在相同配置
        if (!empty($data['shop_id']) && !empty($data['printer_sn'])) {
            $exists = self::where([
                'shop_id' => $data['shop_id'],
                'printer_sn' => $data['printer_sn']
            ]);
            
            if (!empty($data['id'])) {
                $exists->where('id', '<>', $data['id']);
            }
            
            if ($exists->count() > 0) {
                $errors[] = '该门店已配置此打印机';
            }
        }
        
        return $errors;
    }
} 