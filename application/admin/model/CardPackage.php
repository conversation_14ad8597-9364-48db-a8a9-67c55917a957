<?php
/**
 * 套餐表模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use think\Model;

class CardPackage extends Model
{
    protected $table = 'card_package';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    /**
     * 获取启用的套餐列表
     * @param string $clientkeynum 客户唯一标识
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getEnabledPackages($clientkeynum)
    {
        return $this->where('clientkeynum', $clientkeynum)
            ->where('status', 1)
            ->select();
    }
    
    /**
     * 关联卡型
     * @return \think\model\relation\BelongsToMany
     */
    public function cardTypes()
    {
        return $this->belongsToMany('CardPackageType', 'card_type_package_relation', 'type_id', 'package_id');
    }
    
    /**
     * 关联套餐商品
     * @return \think\model\relation\HasMany
     */
    public function products()
    {
        return $this->hasMany('CardPackageProduct', 'package_id', 'id');
    }
} 