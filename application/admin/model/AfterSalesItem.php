<?php
// +----------------------------------------------------------------------
// | 售后商品明细模型
// +----------------------------------------------------------------------
namespace app\admin\model;

use think\Model;

/**
 * 售后商品明细模型
 * 用于处理售后申请中的商品明细数据
 */
class AfterSalesItem extends Model
{
    // 设置表名
    protected $table = 'after_sales_item';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = 'datetime';
    
    // 时间字段取值
    protected $createTime = 'created_at';
    protected $updateTime = false;
    
    /**
     * 关联售后订单
     * @return \think\model\relation\BelongsTo
     */
    public function afterSalesOrder()
    {
        return $this->belongsTo('AfterSalesOrder', 'after_sales_id', 'id');
    }
    
    /**
     * 关联订单详情
     * @return \think\model\relation\BelongsTo
     */
    public function orderDetail()
    {
        return $this->belongsTo('OrderDetail', 'order_detail_id', 'id');
    }
    
    /**
     * 关联商品
     * @return \think\model\relation\BelongsTo
     */
    public function product()
    {
        return $this->belongsTo('Product', 'product_id', 'id');
    }
    
    /**
     * 获取商品信息（从JSON字段解析）
     * @return array
     */
    public function getProductInfoAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }
    
    /**
     * 设置商品信息（转为JSON格式）
     * @param array $value
     * @return string
     */
    public function setProductInfoAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }
    
    /**
     * 获取规格信息（从JSON字段解析）
     * @return array
     */
    public function getInventoryInfoAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }
    
    /**
     * 设置规格信息（转为JSON格式）
     * @param array $value
     * @return string
     */
    public function setInventoryInfoAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }
} 