<?php
/**
 * 卡号延期模型
 * @date 2023-5-4
 * <AUTHOR>
 */

namespace app\admin\model;

use think\Model;

class CardDelayLog extends Model
{
    protected $pk = 'id';

    protected $table = 'client_card_delay_log';

    public function getCardCountAttr($value, $data)
    {
        return count(explode(',', $data['cardnum']));
    }

    public function getCustomerNameAttr($value, $data)
    {
        return CardCustomer::where(['id' => $data['customer_id']])->value('name');
    }

    public static function getInfoByNo($no)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $info = self::where(['no' => $no, 'basekeynum' => $basekeynum])->find();
        return $info;
    }

    // 增
    public static function add($params)
    {
        $model = new self();
        $model->basekeynum = $params['basekeynum'];
        $model->no = $params['no'];
        $model->cardnum = $params['cardnum'];
        $model->operator = $params['operator'];
        $model->delay_time = $params['delay_time'];
        $model->customer_id = $params['customer_id'];
        $model->remarks = $params['remarks'];
        $model->add_time = $params['add_time'];
        $model->save();
        return $model->id;
    }

    // 创建批次单号
    public static function createNo()
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $no = "DELAY" . substr(time(), 4, 6) . mt_rand(000000, 999999);
        return $no;
    }

    /**
     * 查
     * @param $where array 查询条件
     * @param $page int 页数
     * @param $pagesize int 每页条数
     * @return bool|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getList($where, $page, $pagesize)
    {
        return self::where($where)
            ->field(['*, cardnum as card_count,customer_id as customer_name'])
            ->order('add_time', 'desc')
            ->page($page, $pagesize)
            ->select();
    }

    /**
     * 获取总条数
     * @param $where array 查询条件
     * @return int|string
     * @throws \think\Exception
     */
    public static function getCount($where)
    {
        return self::where($where)
            ->count();
    }

    public static function getExportAll($where)
    {
        return self::where($where)
            ->field('*, customer_id as customer_name')
            ->order('add_time', 'desc')
            ->select();
    }
}
