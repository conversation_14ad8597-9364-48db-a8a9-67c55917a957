<?php
/**
 * 订单发货记录模型
 * @date 2023-5-26
 * <AUTHOR>
 */
namespace app\admin\model;

use think\Model;

class CardSendOrderRecord extends Model
{
    protected $pk = 'id';

    protected $table = 'card_send_order_record';

    public static function add($params)
    {
        $model = new self();
        $model->basekeynum = $params['basekeynum'];
        $model->operator = $params['operator'];
        $model->path = $params['path'];
        $model->add_time = $params['add_time'];
        $model->save();
        return $model->id;
    }

    /**
     * 查
     * @param $where array 查询条件
     * @param $page int 页数
     * @param $pagesize int 每页条数
     * @return bool|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getList($where, $page, $pagesize)
    {
        $list = new self();
        $list->where($where);
        $list = $list->order('add_time', 'desc')
            ->page($page, $pagesize)
            ->select();
        return $list;
    }

    /**
     * 获取总条数
     * @param $where array 查询条件
     * @return int|string
     * @throws \think\Exception
     */
    public static function getCount($where)
    {
        $count = new self();
        $count = $count->where($where);
        return $count->count();
    }
}
