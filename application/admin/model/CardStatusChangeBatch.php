<?php
/**
 * 卡状态变更批次表模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use think\Model;

class CardStatusChangeBatch extends Model
{
    protected $table = 'card_status_change_batch';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    /**
     * 生成批次号
     * @return string
     */
    public static function generateBatchNo()
    {
        return 'CS' . date('YmdHis') . mt_rand(1000, 9999);
    }
    
    /**
     * 关联客户
     * @return \think\model\relation\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo('CardCustomer', 'customer_id');
    }
    
    /**
     * 关联操作员
     * @return \think\model\relation\BelongsTo
     */
    public function operator()
    {
        return $this->belongsTo('Admin', 'operator_id');
    }
    
    /**
     * 关联状态记录
     * @return \think\model\relation\HasMany
     */
    public function statusRecords()
    {
        return $this->hasMany('CardStatusRecord', 'batch_id');
    }
    
    /**
     * 获取目标状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getTargetStatusTextAttr($value, $data)
    {
        $status = $data['target_status'];
        $statusMap = [
            0 => '待销售',
            1 => '已销售',
            2 => '已兑换',
            3 => '已开卡',
            4 => '已关卡',
            5 => '已废卡',
            -1 => '已过期'
        ];
        return isset($statusMap[$status]) ? $statusMap[$status] : '未知状态';
    }
    
    /**
     * 创建状态变更批次
     * @param int $customerId 客户ID
     * @param int $targetStatus 目标状态
     * @param int $cardCount 卡数量
     * @param string $reason 变更原因
     * @param string $remark 备注
     * @param int $operatorId 操作员ID
     * @param string $operatorName 操作员姓名
     * @param string $clientkeynum 客户唯一标识
     * @return mixed
     */
    public static function createBatch($customerId, $targetStatus, $cardCount, $reason, $remark, $operatorId, $operatorName, $clientkeynum)
    {
        $batch = new self;
        $batch->clientkeynum = $clientkeynum;
        $batch->batch_no = self::generateBatchNo();
        $batch->target_status = $targetStatus;
        $batch->card_count = $cardCount;
        $batch->success_count = 0;
        $batch->fail_count = 0;
        $batch->customer_id = $customerId;
        $batch->reason = $reason;
        $batch->remark = $remark;
        $batch->operator_id = $operatorId;
        $batch->operator_name = $operatorName;
        $batch->operation_time = date('Y-m-d H:i:s');
        $batch->add_time = date('Y-m-d H:i:s');
        $batch->save();
        
        return $batch;
    }
    
    /**
     * 更新批次统计数据
     * @param int $batchId 批次ID
     * @param int $successCount 成功数量
     * @param int $failCount 失败数量
     * @return bool
     */
    public static function updateBatchStats($batchId, $successCount, $failCount)
    {
        $batch = self::get($batchId);
        if (!$batch) {
            return false;
        }
        
        $batch->success_count = $successCount;
        $batch->fail_count = $failCount;
        $batch->update_time = date('Y-m-d H:i:s');
        return $batch->save();
    }
} 