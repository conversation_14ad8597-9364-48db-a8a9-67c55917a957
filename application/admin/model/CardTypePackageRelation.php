<?php
/**
 * 卡型套餐关联表模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use think\Model;

class CardTypePackageRelation extends Model
{
    protected $table = 'card_type_package_relation';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = false;
    
    /**
     * 关联卡型
     * @return \think\model\relation\BelongsTo
     */
    public function cardType()
    {
        return $this->belongsTo('CardPackageType', 'type_id', 'id');
    }
    
    /**
     * 关联套餐
     * @return \think\model\relation\BelongsTo
     */
    public function package()
    {
        return $this->belongsTo('CardPackage', 'package_id', 'id');
    }
    
    /**
     * 获取卡型关联的套餐ID列表
     * @param int $typeId 卡型ID
     * @param string $clientkeynum 客户唯一标识
     * @return array
     */
    public function getPackageIdsByTypeId($typeId, $clientkeynum)
    {
        return $this->where('type_id', $typeId)
            ->where('clientkeynum', $clientkeynum)
            ->column('package_id');
    }
} 