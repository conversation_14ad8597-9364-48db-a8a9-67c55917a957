<?php
/**
 * 食谱分类模型
 * @date 2024-10-31
 */

namespace app\admin\model;

use think\Model;

class RecipeCategory extends Model
{
    protected $pk = 'id';

    protected $table = 'recipe_category';

    /**
     * 删除分类
     * @param $id
     * @param array $where
     * @return bool
     */
    public static function del($id, $where = [])
    {
        $where['id'] = $id;
        return self::where($where)->delete();
    }

    /**
     * 修改分类
     * @param $where
     * @param $data
     * @return bool
     */
    public static function edit($where, $data)
    {
        return self::where($where)->update($data);
    }

    /**
     * 根据id查询数据
     * @param $id
     * @param $where
     * @return array|bool|\PDOStatement|string|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getInfoById($id, $where = [])
    {
        $where['id'] = ['=', $id];
        $info = self::where($where)->find();
        return $info;
    }

    /**
     * 添加分类
     * @param $data
     * @return false|int
     */
    public static function add($data)
    {
        $model = new self();
        $model->setAttr('name', $data['name']);
        $model->parent_id = isset($data['parent_id']) ? $data['parent_id'] : 0;
        $model->sort = isset($data['sort']) ? $data['sort'] : 0;
        $model->status = isset($data['status']) ? $data['status'] : 1;
        $result = $model->save();
        return $result;
    }

    /**
     * 获取分类列表
     * @param $where array 查询条件
     * @param $page int 页数
     * @param $pagesize int 每页条数
     * @return bool|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getList($where, $page, $pagesize)
    {
        return self::where($where)
            ->order('sort', 'desc')
            ->page($page, $pagesize)
            ->select();
    }

    /**
     * 获取所有分类（用于下拉选择）
     * @param array $where
     * @return array|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getAllCategories($where = [])
    {
        $where['status'] = 1;
        return self::where($where)
            ->order('sort', 'desc')
            ->select();
    }

    /**
     * 获取总条数
     * @param $where array 查询条件
     * @return int|string
     * @throws \think\Exception
     */
    public static function getCount($where)
    {
        return self::where($where)
            ->count();
    }

    /**
     * 获取分类树形结构
     * @param int $parent_id
     * @param array $where
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getCategoryTree($parent_id = 0, $where = [])
    {
        $where['parent_id'] = $parent_id;
        $where['status'] = 1;
        $categories = self::where($where)
            ->order('sort', 'desc')
            ->select()
            ->toArray();

        foreach ($categories as &$category) {
            $category['children'] = self::getCategoryTree($category['id'], $where);
        }

        return $categories;
    }
} 