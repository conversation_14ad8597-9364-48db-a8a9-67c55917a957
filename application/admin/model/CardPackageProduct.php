<?php
/**
 * 套餐商品关联表模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use think\Model;

class CardPackageProduct extends Model
{
    protected $table = 'card_package_product';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    /**
     * 关联套餐
     * @return \think\model\relation\BelongsTo
     */
    public function package()
    {
        return $this->belongsTo('CardPackage', 'package_id', 'id');
    }
    
    /**
     * 关联商品库存
     * @return \think\model\relation\BelongsTo
     */
    public function productInventory()
    {
        return $this->belongsTo('ProductInventory', 'product_inventory_id', 'id');
    }
    
    /**
     * 获取套餐包含的商品列表
     * @param int $packageId 套餐ID
     * @param string $clientkeynum 客户唯一标识
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getProductsByPackageId($packageId, $clientkeynum)
    {
        return $this->with(['productInventory'])
            ->where('package_id', $packageId)
            ->where('clientkeynum', $clientkeynum)
            ->select();
    }
} 