<?php
/**
 * 客户模型
 * @date 2023-4-26
 * <AUTHOR>
 */

namespace app\admin\model;

use think\Model;

class CardCustomer extends Model
{
    protected $pk = 'id';

    protected $table = 'card_customer';

    public static function getInfoById($id)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $info = self::where(['clientkeynum' => $basekeynum, 'id' => $id])->find();
        return $info;
    }

    public static function getInfoByName($name)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $info = self::where(['clientkeynum' => $basekeynum, 'name' => $name])->find();
        return $info;
    }

    /**
     * 获取平台的客户枚举
     * @return array|false|string
     */
    public static function getEnumByBaseKeyNum()
    {
        $basekeynum = session("cn_accountinfo.basekeynum");
        return self::where(['clientkeynum' => $basekeynum])
            ->field('id, name')
            ->select();
    }
}
