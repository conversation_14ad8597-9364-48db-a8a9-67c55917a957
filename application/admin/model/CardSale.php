<?php
/**
 * 销售员模型
 * @date 2023-4-28
 * <AUTHOR>
 */

namespace app\admin\model;

use think\Model;

class CardSale extends Model
{
    protected $pk = 'id';

    protected $table = 'card_sale';

    public static function getInfoByName($name)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $info = self::where(['clientkeynum' => $basekeynum, 'name' => $name])->find();
        return $info;
    }
}
