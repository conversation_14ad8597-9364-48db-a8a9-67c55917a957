<?php

namespace app\admin\model;

use think\Model;
use think\Db;

/**
 * 打印日志模型
 * Class YlyPrintLog
 * @package app\admin\model
 */
class YlyPrintLog extends Model
{
    // 表名
    protected $table = 'yly_print_log';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 打印状态常量
    const STATUS_PENDING = 0;    // 待打印
    const STATUS_PRINTING = 1;   // 打印中
    const STATUS_SUCCESS = 2;    // 打印成功
    const STATUS_FAILED = -1;    // 打印失败
    
    // 打印类型常量
    const TYPE_ORDER = 'order';  // 订单打印
    const TYPE_TEST = 'test';    // 测试打印
    
    /**
     * 关联门店信息
     */
    public function shop()
    {
        return $this->belongsTo('Shop', 'shop_id', 'id');
    }
    
    /**
     * 关联订单信息
     */
    public function order()
    {
        return $this->belongsTo('Order', 'order_no', 'order_no');
    }
    
    /**
     * 创建打印日志
     * @param array $data
     * @return bool|object
     */
    public static function createLog($data)
    {
        $logData = [
            'clientkeynum' => $data['clientkeynum'],
            'shop_id' => $data['shop_id'],
            'order_no' => $data['order_no'],
            'printer_sn' => $data['printer_sn'],
            'print_content' => $data['print_content'],
            'print_status' => self::STATUS_PENDING,
            'print_type' => $data['print_type'] ?? self::TYPE_ORDER,
            'retry_count' => 0
        ];
        
        return self::create($logData);
    }
    
    /**
     * 更新打印状态
     * @param int $id
     * @param int $status
     * @param string $response
     * @param string $error
     * @return bool
     */
    public static function updateStatus($id, $status, $response = '', $error = '')
    {
        $updateData = [
            'print_status' => $status,
            'print_time' => date('Y-m-d H:i:s')
        ];
        
        if (!empty($response)) {
            $updateData['api_response'] = $response;
        }
        
        if (!empty($error)) {
            $updateData['error_message'] = $error;
        }
        
        return self::where('id', $id)->update($updateData);
    }
    
    /**
     * 增加重试次数
     * @param int $id
     * @return bool
     */
    public static function incrementRetry($id)
    {
        return self::where('id', $id)->update([
            'retry_count' => Db::raw('retry_count + 1')
        ]);
    }
    
    /**
     * 获取打印日志列表
     * @param string $clientkeynum
     * @param array $condition
     * @return array
     */
    public static function getLogList($clientkeynum, $condition = [])
    {
        $where = ['clientkeynum' => $clientkeynum];
        if (!empty($condition)) {
            $where = array_merge($where, $condition);
        }
        
        return self::where($where)
            ->order('created_at desc')
            ->paginate(20)
            ->each(function($item) {
                // 关联门店信息
                $item['shop_name'] = Db::name('shop')->where('id', $item['shop_id'])->value('shop_name') ?: '未知门店';
                
                // 状态文字
                $item['status_text'] = self::getStatusText($item['print_status']);
                
                // 打印类型文字
                $item['type_text'] = self::getTypeText($item['print_type']);
                
                return $item;
            });
    }
    
    /**
     * 获取状态文字
     * @param int $status
     * @return string
     */
    public static function getStatusText($status)
    {
        $statusMap = [
            self::STATUS_PENDING => '待打印',
            self::STATUS_PRINTING => '打印中',
            self::STATUS_SUCCESS => '打印成功',
            self::STATUS_FAILED => '打印失败'
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }
    
    /**
     * 获取类型文字
     * @param string $type
     * @return string
     */
    public static function getTypeText($type)
    {
        $typeMap = [
            self::TYPE_ORDER => '订单打印',
            self::TYPE_TEST => '测试打印'
        ];
        
        return $typeMap[$type] ?? '未知类型';
    }
    
    /**
     * 获取打印统计
     * @param string $clientkeynum
     * @param string $date
     * @return array
     */
    public static function getStatistics($clientkeynum, $date = '')
    {
        if (empty($date)) {
            $date = date('Y-m-d');
        }
        
        $where = [
            'clientkeynum' => $clientkeynum,
            'created_at' => ['>=', $date . ' 00:00:00'],
            'created_at' => ['<=', $date . ' 23:59:59']
        ];
        
        $total = self::where($where)->count();
        $success = self::where($where)->where('print_status', self::STATUS_SUCCESS)->count();
        $failed = self::where($where)->where('print_status', self::STATUS_FAILED)->count();
        $pending = self::where($where)->where('print_status', self::STATUS_PENDING)->count();
        
        return [
            'total' => $total,
            'success' => $success,
            'failed' => $failed,
            'pending' => $pending,
            'success_rate' => $total > 0 ? round($success / $total * 100, 2) : 0
        ];
    }
    
    /**
     * 获取失败的打印记录（用于重试）
     * @param string $clientkeynum
     * @param int $maxRetry
     * @return array
     */
    public static function getFailedLogs($clientkeynum, $maxRetry = 3)
    {
        return self::where([
            'clientkeynum' => $clientkeynum,
            'print_status' => self::STATUS_FAILED,
            'retry_count' => ['<', $maxRetry]
        ])
        ->order('created_at desc')
        ->limit(10)
        ->select()
        ->toArray();
    }
    
    /**
     * 清理历史日志
     * @param string $clientkeynum
     * @param int $days
     * @return int
     */
    public static function cleanOldLogs($clientkeynum, $days = 30)
    {
        $expireDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        return self::where([
            'clientkeynum' => $clientkeynum,
            'created_at' => ['<', $expireDate]
        ])->delete();
    }
} 