<?php
/**
 * 折扣卡充值套餐模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use think\Model;

class CardRechargePackage extends Model
{
    protected $table = 'client_ycard_recharge_package';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    // 状态常量
    const STATUS_DISABLED = 0; // 禁用
    const STATUS_ENABLED = 1;  // 启用
    
    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED => '启用'
        ];
        return $statusMap[$data['status']] ?? '未知';
    }
    
    /**
     * 获取有效的充值套餐列表
     * @param string $clientkeynum 客户唯一标识
     * @return array|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getActivePackages($clientkeynum)
    {
        $now = date('Y-m-d H:i:s');
        
        return $this->where('clientkeynum', $clientkeynum)
            ->where('status', self::STATUS_ENABLED)
            ->where(function ($query) use ($now) {
                $query->whereNull('start_time')
                    ->whereOr('start_time', '<=', $now);
            })
            ->where(function ($query) use ($now) {
                $query->whereNull('end_time')
                    ->whereOr('end_time', '>=', $now);
            })
            ->order('sort_order', 'asc')
            ->order('id', 'asc')
            ->select();
    }
    
    /**
     * 获取套餐详情
     * @param int $id 套餐ID
     * @param string $clientkeynum 客户唯一标识
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getPackageById($id, $clientkeynum)
    {
        return $this->where('id', $id)
            ->where('clientkeynum', $clientkeynum)
            ->where('status', self::STATUS_ENABLED)
            ->find();
    }
    
    /**
     * 检查套餐是否有效
     * @param int $id 套餐ID
     * @param string $clientkeynum 客户唯一标识
     * @return bool
     */
    public function checkPackageValid($id, $clientkeynum)
    {
        $now = date('Y-m-d H:i:s');
        
        $package = $this->where('id', $id)
            ->where('clientkeynum', $clientkeynum)
            ->where('status', self::STATUS_ENABLED)
            ->where(function ($query) use ($now) {
                $query->whereNull('start_time')
                    ->whereOr('start_time', '<=', $now);
            })
            ->where(function ($query) use ($now) {
                $query->whereNull('end_time')
                    ->whereOr('end_time', '>=', $now);
            })
            ->find();
            
        return !empty($package);
    }
    
    /**
     * 创建充值套餐
     * @param array $data 套餐数据
     * @return bool
     */
    public function createPackage($data)
    {
        try {
            // 计算总金额
            $data['total_amount'] = $data['recharge_amount'] + $data['bonus_amount'];
            
            // 设置创建时间
            $data['add_time'] = date('Y-m-d H:i:s');
            
            $this->save($data);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 更新充值套餐
     * @param int $id 套餐ID
     * @param array $data 更新数据
     * @return bool
     */
    public function updatePackage($id, $data)
    {
        try {
            // 计算总金额
            if (isset($data['recharge_amount']) || isset($data['bonus_amount'])) {
                $package = $this->find($id);
                if ($package) {
                    $rechargeAmount = $data['recharge_amount'] ?? $package['recharge_amount'];
                    $bonusAmount = $data['bonus_amount'] ?? $package['bonus_amount'];
                    $data['total_amount'] = $rechargeAmount + $bonusAmount;
                }
            }
            
            // 设置更新时间
            $data['update_time'] = date('Y-m-d H:i:s');
            
            $this->where('id', $id)->update($data);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 删除充值套餐
     * @param int $id 套餐ID
     * @param string $clientkeynum 客户唯一标识
     * @return bool
     */
    public function deletePackage($id, $clientkeynum)
    {
        try {
            // 检查是否有充值记录
            $hasRecords = model('CardRechargeRecord')
                ->where('package_id', $id)
                ->where('clientkeynum', $clientkeynum)
                ->count();
                
            if ($hasRecords > 0) {
                return false; // 有充值记录，不能删除
            }
            
            $this->where('id', $id)
                ->where('clientkeynum', $clientkeynum)
                ->delete();
                
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
} 