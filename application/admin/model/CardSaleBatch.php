<?php
/**
 * 卡销售批次主表模型
 * Date: 2024/04/25
 */

namespace app\admin\model;

use think\Model;

class CardSaleBatch extends Model
{
    protected $table = 'card_sale_batch';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    /**
     * 关联销售明细记录
     * @return \think\model\relation\HasMany
     */
    public function saleRecords()
    {
        return $this->hasMany('CardSaleRecord', 'batch_id', 'id');
    }
    
    /**
     * 关联销售批次详情
     * @return \think\model\relation\HasMany
     */
    public function batchDetails()
    {
        return $this->hasMany('CardSaleBatchDetail', 'batch_id', 'id');
    }
    
    /**
     * 关联客户
     * @return \think\model\relation\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo('CardCustomer', 'customer_id', 'id');
    }
    
    /**
     * 关联销售员
     * @return \think\model\relation\BelongsTo
     */
    public function salesperson()
    {
        return $this->belongsTo('CardSale', 'salesperson_id', 'id');
    }
    
    /**
     * 添加卡销售批次
     * @param array $data 销售批次数据
     * @return int|string 新增ID
     */
    public function addSaleBatch($data)
    {
        $this->data([
            'clientkeynum' => $data['clientkeynum'],
            'batch_no' => $data['batch_no'],
            'customer_id' => $data['customer_id'],
            'salesperson_id' => $data['salesperson_id'],
            'total_price' => isset($data['total_price']) ? $data['total_price'] : 0,
            'card_count' => isset($data['card_count']) ? $data['card_count'] : 0,
            'sale_time' => isset($data['sale_time']) ? $data['sale_time'] : date('Y-m-d H:i:s'),
            'payment_method' => isset($data['payment_method']) ? $data['payment_method'] : '1',
            'transaction_no' => isset($data['transaction_no']) ? $data['transaction_no'] : '',
            'remark' => isset($data['remark']) ? $data['remark'] : '',
            'operator_id' => isset($data['operator_id']) ? $data['operator_id'] : 0,
            'operator_name' => isset($data['operator_name']) ? $data['operator_name'] : '',
            'begin_time' => isset($data['begin_time']) ? $data['begin_time'] : null,
            'end_time' => isset($data['end_time']) ? $data['end_time'] : null,
            'linkman' => isset($data['linkman']) ? $data['linkman'] : '',
            'linktel' => isset($data['linktel']) ? $data['linktel'] : '',
            'is_auto_open' => isset($data['is_auto_open']) ? $data['is_auto_open'] : 1,
            'add_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->save();
        
        return $this->id;
    }
    
    /**
     * 根据批次号获取批次信息
     * @param string $batchNo 批次号
     * @param string $clientkeynum 客户唯一标识
     * @return array|false|\PDOStatement|string|\think\Model
     */
    public function getBatchByBatchNo($batchNo, $clientkeynum)
    {
        return $this->with(['customer', 'salesperson'])
            ->where('batch_no', $batchNo)
            ->where('clientkeynum', $clientkeynum)
            ->find();
    }
    
    /**
     * 更新批次信息
     * @param int $id 批次ID
     * @param array $data 更新数据
     * @return bool
     */
    public function updateBatch($id, $data)
    {
        return $this->where('id', $id)->update($data);
    }
} 