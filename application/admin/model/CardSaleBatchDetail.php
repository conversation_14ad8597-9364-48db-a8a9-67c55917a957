<?php
/**
 * 销售批次详情表模型
 * Date: 2024/04/27
 */

namespace app\admin\model;

use think\Model;

class CardSaleBatchDetail extends Model
{
    protected $table = 'card_sale_batch_detail';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    /**
     * 关联销售批次
     * @return \think\model\relation\BelongsTo
     */
    public function saleBatch()
    {
        return $this->belongsTo('CardSaleBatch', 'batch_id', 'id');
    }
    
    /**
     * 关联卡型
     * @return \think\model\relation\BelongsTo
     */
    public function cardType()
    {
        return $this->belongsTo('CardPackageType', 'type_id', 'id');
    }
    
    /**
     * 添加销售批次详情
     * @param array $data 销售批次详情数据
     * @return int|string 新增ID
     */
    public function addBatchDetail($data)
    {
        $this->clientkeynum = $data['clientkeynum'];
        $this->batch_id = $data['batch_id'];
        $this->batch_no = $data['batch_no'];
        $this->type_id = $data['type_id'];
        $this->begin_card = $data['begin_card'];
        $this->end_card = $data['end_card'];
        $this->card_count = isset($data['card_count']) ? $data['card_count'] : 0;
        $this->market_price = isset($data['market_price']) ? $data['market_price'] : 0;
        $this->zhekou = isset($data['zhekou']) ? $data['zhekou'] : 100;
        $this->end_price = isset($data['end_price']) ? $data['end_price'] : 0;
        $this->all_price = isset($data['all_price']) ? $data['all_price'] : 0;
        $this->add_time = date('Y-m-d H:i:s');
        $this->update_time = date('Y-m-d H:i:s');
        $this->save();
        
        return $this->id;
    }
    
    /**
     * 获取销售批次所有详情
     * @param int $batchId 批次ID
     * @return \think\model\Collection
     */
    public function getBatchDetails($batchId)
    {
        return $this->with(['cardType'])
            ->where('batch_id', $batchId)
            ->select();
    }
    
    /**
     * 获取销售批次详情总计
     * @param int $batchId 批次ID
     * @return array 包含总卡数和总金额
     */
    public function getBatchDetailSummary($batchId)
    {
        $result = $this->where('batch_id', $batchId)
            ->field([
                'sum(card_count) as total_cards',
                'sum(all_price) as total_amount'
            ])
            ->find();
            
        return [
            'total_cards' => $result['total_cards'] ?: 0,
            'total_amount' => $result['total_amount'] ?: 0,
        ];
    }
} 