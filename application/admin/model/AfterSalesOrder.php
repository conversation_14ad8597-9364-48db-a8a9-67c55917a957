<?php
// +----------------------------------------------------------------------
// | 售后订单模型
// +----------------------------------------------------------------------
namespace app\admin\model;

use think\Model;

/**
 * 售后订单模型
 * 用于处理售后申请相关的数据操作
 */
class AfterSalesOrder extends Model
{
    // 设置表名
    protected $table = 'after_sales_order';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 时间字段取值
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 售后状态定义
     */
    const STATUS_PENDING = 0;      // 待审核
    const STATUS_APPROVED = 1;     // 审核通过
    const STATUS_REJECTED = 2;     // 审核拒绝
    const STATUS_REFUNDING = 8;    // 退款中
    const STATUS_REFUNDED = 9;     // 退款成功
    const STATUS_COMPLETED = 10;   // 售后完成
    const STATUS_CANCELLED = -1;   // 已取消
    
    /**
     * 售后类型定义
     */
    const TYPE_REFUND_ONLY = 1;    // 仅退款
    const TYPE_RETURN_REFUND = 2;  // 退货退款
    
    /**
     * 获取状态文本
     * @param int $status
     * @return string
     */
    public static function getStatusText($status)
    {
        $statusMap = [
            self::STATUS_PENDING => '待审核',
            self::STATUS_APPROVED => '审核通过',
            self::STATUS_REJECTED => '审核拒绝',
            self::STATUS_REFUNDING => '退款中',
            self::STATUS_REFUNDED => '退款成功',
            self::STATUS_COMPLETED => '售后完成',
            self::STATUS_CANCELLED => '已取消',
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }
    
    /**
     * 获取类型文本
     * @param int $type
     * @return string
     */
    public static function getTypeText($type)
    {
        $typeMap = [
            self::TYPE_REFUND_ONLY => '仅退款',
            self::TYPE_RETURN_REFUND => '退货退款',
        ];
        
        return $typeMap[$type] ?? '未知类型';
    }
    
    /**
     * 关联售后商品明细
     * @return \think\model\relation\HasMany
     */
    public function items()
    {
        return $this->hasMany('AfterSalesItem', 'after_sales_id', 'id');
    }
    
    /**
     * 关联售后日志
     * @return \think\model\relation\HasMany
     */
    public function logs()
    {
        return $this->hasMany('AfterSalesLog', 'after_sales_id', 'id');
    }
    
    /**
     * 关联退款记录
     * @return \think\model\relation\HasMany
     */
    public function refunds()
    {
        return $this->hasMany('AfterSalesRefund', 'after_sales_id', 'id');
    }
    
    /**
     * 关联用户
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id');
    }
    
    /**
     * 关联店铺
     * @return \think\model\relation\BelongsTo
     */
    public function shop()
    {
        return $this->belongsTo('Shop', 'shop_id', 'id');
    }
    
    /**
     * 关联原订单
     * @return \think\model\relation\BelongsTo
     */
    public function order()
    {
        return $this->belongsTo('Order', 'order_no', 'order_no');
    }
} 