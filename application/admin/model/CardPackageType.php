<?php
/**
 * 卡型表模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use think\Model;

class CardPackageType extends Model
{
    protected $table = 'card_package_type';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    /**
     * 获取启用的卡型列表
     * @param string $clientkeynum 客户唯一标识
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getEnabledTypes($clientkeynum)
    {
        return $this->where('clientkeynum', $clientkeynum)
            ->where('status', 1)
            ->select();
    }
    
    /**
     * 关联套餐
     * @return \think\model\relation\BelongsToMany
     */
    public function packages()
    {
        return $this->belongsToMany('CardPackage', 'card_type_package_relation', 'package_id', 'type_id');
    }
} 