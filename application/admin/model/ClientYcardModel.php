<?php

namespace app\admin\model;

use think\Db;
use think\facade\Log;
use think\Model;

class ClientYcardModel extends Model
{
    protected $pk = 'id';

    protected $table = 'client_ycard';

    public function getStatusAttr($value)
    {
        $type_arr = [
            0       => '未销售',
            1       => '已销售',
            2       => '已退卡',
            3       => '已开卡',
            4       => '已关卡',
            5       => '未销售',
            -1      => '废卡',
        ];
        return $type_arr[$value] ?? '状态异常';
    }

    public function getEndDuiAttr($value)
    {
        if (!empty($value)) return date('Y-m-d H:i:s',$value);
    }

    public function getAddTimeAttr($value)
    {
        if (!empty($value)) return date('Y-m-d H:i:s',$value);
    }

    public function getBindTimeAttr($value)
    {
        if (!empty($value)) return date('Y-m-d H:i:s',$value);
    }

    public function getCustomerNameAttr($value, $data)
    {
        return CardCustomer::where(['id' => $data['customer_id']])->value('name');
    }

    public function getSaleNameAttr($value, $data)
    {
        return CardSale::where(['id' => $data['sale_id']])->value('name');
    }


    /**
     * 获取器 获取卡类型名称
     * @param $value
     * @param $data
     */
    public function getCardTypeNameAttr($value, $data)
    {
        if (!empty($data['cardtype_id'])){
            return Db::table('card_type')->where(['id' => $data['cardtype_id']])->value('name');
        } else return '暂未分配';
    }

    /**
     * 更改卡状态时 提交的状态与数据表不符，需要在此方法获取数据库表里面的真实状态
     * @param $type int
     * @return
     */
    public static function getRealStatus($type)
    {
        $status_arr = [
            1 => 3,
            2 => 4,
            3 => -1
        ];
        return $status_arr[$type];
    }

    /**
     * 获取用户绑定的卡号总数
     * @param $member_id
     * @param $where
     * @return int|string
     * @throws \think\Exception
     */
    public static function getMemberBindCardCount($member_id, $where)
    {
        $where['member_id'] = $member_id;
        $where['is_bind_member'] = 1;
        $count = self::where($where)->count();
        return $count;
    }

    /**
     * 获取用户绑定的卡号列表
     * @param $member_id
     * @param $where
     * @param $page
     * @param $page_size
     * @return bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getMemberBindCardList($member_id, $where = [], $page = 1, $page_size = 10)
    {
        $where['member_id'] = $member_id;
        $where['is_bind_member'] = 1;
        $list = self::where($where);
        $list = $list->page($page, $page_size)->order('bind_time', 'desc')->select();
        return $list;
    }

    /**
     * 通过 cardnum 获取卡号信息
     * @param $cardnum
     * @param $where
     * @return array|bool|\PDOStatement|string|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getInfoByNum($cardnum, $where)
    {
        $where['cardnum'] = ['=', $cardnum];
        $info = self::where($where)->find();
        return $info;
    }

    /**
     * 卡号绑定用户
     * @param $member_id
     * @param $cardnum
     * @param $where
     * @return ClientYcardModel
     */
    public static function bindMember($member_id, $cardnum, $where)
    {
        $where['cardnum'] = $cardnum;
        $update_data = [
            'member_id' => $member_id,
            'is_bind_member' => 1,
            'bind_time' => time()
        ];
        return self::where($where)->update($update_data);
    }

    /**
     * 根据销售单号获取卡列表
     * @param $pu_id int 销售单号
     * @param $where
     * @return bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getListByPuId($pu_id,$where = [])
    {
        $list = self::where(['pu_id' => $pu_id]);
        if (!empty($where)) $list = $list->where($where);
        $list = $list->field('*, cardtype_id as card_type_name')->select();
        return $list;
    }

    /**
     * 通过card_nums获取列表数据
     * @param $card_numbers array 由 card_nums 组成的数组
     * @param $where
     * @return bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getListByCardNumbers($card_numbers, $where = [])
    {
        $list = self::where('cardnum', 'in', implode(',', $card_numbers))
            ->field(['*, sale_id as sale_name, customer_id as customer_name']);
        if (!empty($where)) {
            foreach ($card_numbers as $k => $v) {
                $card_numbers[$k] = "'$v'";
            }
            $list = $list->where($where)
            ->order(Db::raw('field(cardnum, ' . implode(",", $card_numbers) . ')'));
        }
        $list = $list->select();

        return $list;
    }

    /**
     * 批量更新卡状态
     * @param $list array 需要更新的数据 [id => 1, status => 2] 格式
     * @return array|false|\think\Collection|\think\model\Collection
     * @throws \Exception
     */
    public static function batchChangeCardStatus($list)
    {
        $model = new self();
        $result = $model->saveAll($list);
        return $result;
    }


    /**
     * 批量更改卡过期时间
     * @param $card_nums string 卡号字段 , 分割
     * @param $delay_time string 过期时间
     * @return ClientYcardModel
     */
    public static function batchDelayCard($card_nums, $delay_time)
    {
        $where[] = ['cardnum', 'in', $card_nums];
        $result = self::where($where)->update(['end_dui' => strtotime($delay_time)]);
        Log::error('lastsql:' . self::getLastSql());
        return $result;
    }
}
