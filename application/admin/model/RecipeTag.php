<?php
/**
 * 食谱标签模型
 * @date 2024-10-31
 */

namespace app\admin\model;

use think\Model;

class RecipeTag extends Model
{
    protected $pk = 'id';

    protected $table = 'recipe_tag';

    /**
     * 删除标签
     * @param $id
     * @param array $where
     * @return bool
     */
    public static function del($id, $where = [])
    {
        $where['id'] = $id;
        return self::where($where)->delete();
    }

    /**
     * 修改标签
     * @param $where
     * @param $data
     * @return bool
     */
    public static function edit($where, $data)
    {
        return self::where($where)->update($data);
    }

    /**
     * 根据id查询数据
     * @param $id
     * @param $where
     * @return array|bool|\PDOStatement|string|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getInfoById($id, $where = [])
    {
        $where['id'] = ['=', $id];
        $info = self::where($where)->find();
        return $info;
    }

    /**
     * 添加标签
     * @param $data
     * @return false|int
     */
    public static function add($data)
    {
        $model = new self();
        $model->setAttr('name', $data['name']);
        $model->status = isset($data['status']) ? $data['status'] : 1;
        $result = $model->save();
        return $result;
    }

    /**
     * 获取标签列表
     * @param $where array 查询条件
     * @param $page int 页数
     * @param $pagesize int 每页条数
     * @return bool|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getList($where, $page, $pagesize)
    {
        return self::where($where)
            ->order('id', 'desc')
            ->page($page, $pagesize)
            ->select();
    }

    /**
     * 获取所有标签（用于下拉选择）
     * @param array $where
     * @return array|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getAllTags($where = [])
    {
        $where['status'] = 1;
        return self::where($where)
            ->order('id', 'desc')
            ->select();
    }

    /**
     * 获取总条数
     * @param $where array 查询条件
     * @return int|string
     * @throws \think\Exception
     */
    public static function getCount($where)
    {
        return self::where($where)
            ->count();
    }
} 