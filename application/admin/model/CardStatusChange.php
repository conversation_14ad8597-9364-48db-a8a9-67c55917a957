<?php
/**
 * 卡号状态变更表
 * @date 2023-4-28
 * <AUTHOR>
 */
namespace app\admin\model;

use think\Model;

class CardStatusChange extends Model
{
    protected $pk = 'id';

    protected $table = 'card_status_change';


    // 创建批次单号
    public static function createNo()
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $no = substr($basekeynum, 0, 5) . substr(time(), 4, 6) . mt_rand(000000, 999999);
        return $no;
    }

    public function getCustomerNameAttr($value, $data)
    {
        return CardCustomer::where(['id' => $data['customer_id']])->value('name');
    }

    public function getSaleNameAttr($value, $data)
    {
        return CardSale::where(['id' => $data['sale_id']])->value('name');
    }

    public function getCardCountAttr($value, $data)
    {
        return count(explode(',', $data['cardnum']));
    }

    public function getStatusAttr($value)
    {
        $type_arr = [
            0       => '未销售',
            1       => '销售',
            2       => '退卡',
            3       => '开卡',
            4       => '关卡',
            -1      => '废卡',
        ];
        return $type_arr[$value];
    }

    /**
     * 判断当前状态是否可以更改
     * @param $originStatus
     * @param $status
     * @return bool
     */
    public static function checkStatus($originStatus, $status)
    {
        if (in_array($originStatus, [2, -1])) return false;
        if (in_array($status, [3, 4]) && $originStatus == 0) return false;
        return true;
    }

    /**
     * 获取导出数据
     * @param $where array 查询条件
     */
    public static function getExportAll($where)
    {
        return self::where($where)
            ->field('*, customer_id as customer_name, sale_id as sale_name')
            ->order('add_time', 'desc')
            ->select();
    }

    /**
     * 增
     * @param $params array
     * @return array|bool|float|int|mixed|object|\stdClass|null
     */
    public static function add($params)
    {
        $model = new self();
        $model->customer_id     = $params['customer_id'];
        $model->operator        = $params['operator'];
        $model->basekeynum      = $params['basekeynum'];
        $model->no              = $params['no'];
        $model->sale_id         = $params['sale_id'];
        $model->status          = $params['status'];
        $model->cardnum         = $params['cardnum'];
        $model->remarks         = $params['remarks'];
        $model->add_time        = $params['add_time'];
        $model->save();
        return $model->id;
    }

    /**
     * 查
     * @param $where array 查询条件
     * @param $page int 页数
     * @param $pagesize int 每页条数
     * @return bool|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getList($where, $page, $pagesize)
    {
        return self::where($where)
            ->field('*, customer_id as customer_name, sale_id as sale_name, cardnum as card_count')
            ->order('add_time', 'desc')
            ->page($page, $pagesize)
            ->select();
    }

    /**
     * 获取总条数
     * @param $where array 查询条件
     * @return int|string
     * @throws \think\Exception
     */
    public static function getCount($where)
    {
        return self::where($where)
            ->count();
    }


}
