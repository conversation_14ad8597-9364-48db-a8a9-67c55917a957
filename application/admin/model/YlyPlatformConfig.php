<?php

namespace app\admin\model;

use think\Model;
use think\Db;

/**
 * 易联云平台配置模型
 * Class YlyPlatformConfig
 * @package app\admin\model
 */
class YlyPlatformConfig extends Model
{
    // 表名
    protected $table = 'yly_platform_config';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 根据客户端标识获取配置
     * @param string $clientkeynum
     * @return array|null
     */
    public static function getByClientkey($clientkeynum)
    {
        return self::where('clientkeynum', $clientkeynum)->find();
    }
    
    /**
     * 保存或更新配置
     * @param array $data
     * @return bool
     */
    public static function saveConfig($data)
    {
        $clientkeynum = $data['clientkeynum'];
        $config = self::where('clientkeynum', $clientkeynum)->find();
        
        if ($config) {
            // 更新配置
            return $config->save($data);
        } else {
            // 新增配置
            $model = new self();
            return $model->save($data);
        }
    }
    
    /**
     * 更新Token信息
     * @param int $id
     * @param string $accessToken
     * @param string $refreshToken
     * @return bool
     */
    public static function updateToken($id, $accessToken, $refreshToken = '')
    {
        $today = date('Y-m-d');
        $config = self::find($id);
        
        // 判断是否是同一天，计算Token获取次数
        $lastTokenDate = $config['last_token_time'] ? date('Y-m-d', strtotime($config['last_token_time'])) : '';
        $tokenGetCount = ($lastTokenDate == $today) ? $config['token_get_count'] + 1 : 1;
        
        return self::where('id', $id)->update([
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken,
            'last_token_time' => date('Y-m-d H:i:s'),
            'token_get_count' => $tokenGetCount
        ]);
    }
    
    /**
     * 检查Token是否有效
     * @param array $config
     * @return bool
     */
    public static function isTokenValid($config)
    {
        if (empty($config['access_token']) || empty($config['last_token_time'])) {
            return false;
        }
        
        $lastTokenTime = strtotime($config['last_token_time']);
        // Token有效期24小时
        return (time() - $lastTokenTime) < 86400;
    }
    
    /**
     * 检查今日Token获取次数是否超限
     * @param array $config
     * @return bool
     */
    public static function isTokenLimitExceeded($config)
    {
        $today = date('Y-m-d');
        $lastTokenDate = $config['last_token_time'] ? date('Y-m-d', strtotime($config['last_token_time'])) : '';
        
        return ($lastTokenDate == $today && $config['token_get_count'] >= 20);
    }
    
    /**
     * 获取统计信息
     * @param string $clientkeynum
     * @return array
     */
    public static function getStatistics($clientkeynum)
    {
        $config = self::getByClientkey($clientkeynum);
        if (!$config) {
            return [];
        }
        
        $today = date('Y-m-d');
        $lastTokenDate = $config['last_token_time'] ? date('Y-m-d', strtotime($config['last_token_time'])) : '';
        
        return [
            'today_token_count' => ($lastTokenDate == $today) ? $config['token_get_count'] : 0,
            'token_limit' => 20,
            'token_valid' => self::isTokenValid($config),
            'last_token_time' => $config['last_token_time'],
            'status' => $config['status']
        ];
    }
} 