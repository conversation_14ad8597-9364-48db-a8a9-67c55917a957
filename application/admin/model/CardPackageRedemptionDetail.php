<?php
/**
 * 卡套餐兑换明细表模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use think\Model;

class CardPackageRedemptionDetail extends Model
{
    protected $table = 'card_package_redemption_detail';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    /**
     * 关联兑换主表
     * @return \think\model\relation\BelongsTo
     */
    public function redemption()
    {
        return $this->belongsTo('CardPackageRedemption', 'redemption_id', 'id');
    }
    
    /**
     * 关联套餐
     * @return \think\model\relation\BelongsTo
     */
    public function package()
    {
        return $this->belongsTo('CardPackage', 'package_id', 'id');
    }
    
    /**
     * 获取兑换明细
     * @param int $redemptionId 兑换主表ID
     * @param string $clientkeynum 客户唯一标识
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getRedemptionDetails($redemptionId, $clientkeynum)
    {
        return $this->with(['package'])
            ->where('redemption_id', $redemptionId)
            ->where('clientkeynum', $clientkeynum)
            ->select();
    }
    
    /**
     * 批量添加兑换明细
     * @param array $details 明细数据数组
     * @return bool
     */
    public function addBatchDetails($details)
    {
        if (empty($details)) {
            return false;
        }
        
        return $this->saveAll($details);
    }
} 