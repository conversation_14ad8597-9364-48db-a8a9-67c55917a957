<?php
/**
 * 卡号延期模型
 * @date 2023-5-4
 * <AUTHOR>
 */

namespace app\admin\model;

use think\Model;

/**
 * 卡延期记录模型
 */
class CardDelayRecord extends Model
{
    // 设置数据表名
    protected $table = 'card_delay_record';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = false;

    public function getCardCountAttr($value, $data)
    {
        return count(explode(',', $data['cardnum']));
    }

    public function getCustomerNameAttr($value, $data)
    {
        return CardCustomer::where(['id' => $data['customer_id']])->value('name');
    }

    /**
     * 生成延期单号
     * @return string
     */
    public static function createNo()
    {
        return 'D' . date('YmdHis') . mt_rand(1000, 9999);
    }
    
    /**
     * 根据条件获取延期记录列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页记录数
     * @return array
     */
    public static function getList($where, $page = 1, $limit = 15)
    {
        return self::where($where)
            ->order('id', 'desc')
            ->page($page, $limit)
            ->select();
    }
    
    /**
     * 获取记录总数
     * @param array $where 查询条件
     * @return int
     */
    public static function getCount($where)
    {
        return self::where($where)->count();
    }
    
    /**
     * 根据单号获取详情
     * @param string $no 单号
     * @return array|null
     */
    public static function getInfoByNo($no)
    {
        return self::where('no', $no)->find();
    }

    // 增
    public static function add($params)
    {
        $model = new self();
        $model->basekeynum = $params['basekeynum'];
        $model->no = $params['no'];
        $model->cardnum = $params['cardnum'];
        $model->operator = $params['operator'];
        $model->delay_time = $params['delay_time'];
        $model->customer_id = $params['customer_id'];
        $model->remarks = $params['remarks'];
        $model->add_time = $params['add_time'];
        $model->save();
        return $model->id;
    }

    public static function getExportAll($where)
    {
        return self::where($where)
            ->field('*, customer_id as customer_name')
            ->order('add_time', 'desc')
            ->select();
    }
}
