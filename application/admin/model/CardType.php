<?php
/**
 * 卡型模型
 * @date 2023-4-27
 * <AUTHOR>
 */

namespace app\admin\model;

use think\Model;
use think\facade\Session;

class CardType extends Model
{
    protected $pk = 'id';

    protected $table = 'client_card_type';

    public function getTypeAttr($value)
    {
        $value = ($value == 1) ? '储值卡' : '暂无';
        return $value;
    }

    // 查
    public static function getList($where, $page, $pagesize)
    {
        $list = self::where($where)
            ->field('*')
            ->order('id', 'desc')
            ->page($page, $pagesize)
            ->select();
        return $list;
    }

    // 查总条数
    public static function getCount($where)
    {
        return self::where($where)
            ->count();
    }

    /**
     * 通过主键id获取数据
     * @param $id int 主键id
     * @return array|bool|\PDOStatement|string|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getInfoById($id)
    {
        return self::find($id);
    }

    /**
     * 根据客户标识获取卡型枚举
     * @return array
     */
    public static function getEnumByBaseKeyNum()
    {
        $basekeynum = Session::get("cn_accountinfo.basekeynum");
        return self::where('clientkeynum', $basekeynum)
            ->field('id, name')
            ->order('id', 'asc')
            ->select()
            ->toArray();
    }
}
