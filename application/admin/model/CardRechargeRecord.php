<?php
/**
 * 折扣卡充值记录模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use app\api\model\Ycard;
use think\Model;
use think\Db;
use think\facade\Log;

class CardRechargeRecord extends Model
{
    protected $table = 'client_ycard_recharge_record';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    // 支付方式常量
    const PAYMENT_WECHAT = 1; // 微信支付
    const PAYMENT_ALIPAY = 2; // 支付宝
    const PAYMENT_CASH = 3;   // 现金
    const PAYMENT_BANK = 4;   // 银行卡
    
    // 支付状态常量
    const STATUS_PENDING = 0;  // 待支付
    const STATUS_PAID = 1;     // 已支付
    const STATUS_FAILED = 2;   // 支付失败
    const STATUS_REFUNDED = 3; // 已退款
    
    // 操作类型常量
    const OPERATOR_USER = 1;  // 用户自助
    const OPERATOR_ADMIN = 2; // 管理员代充
    
    /**
     * 获取支付方式文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getPaymentMethodTextAttr($value, $data)
    {
        $methodMap = [
            self::PAYMENT_WECHAT => '微信支付',
            self::PAYMENT_ALIPAY => '支付宝',
            self::PAYMENT_CASH => '现金',
            self::PAYMENT_BANK => '银行卡'
        ];
        return $methodMap[$data['payment_method']] ?? '未知';
    }
    
    /**
     * 获取支付状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getPaymentStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_PENDING => '待支付',
            self::STATUS_PAID => '已支付',
            self::STATUS_FAILED => '支付失败',
            self::STATUS_REFUNDED => '已退款'
        ];
        return $statusMap[$data['payment_status']] ?? '未知';
    }
    
    /**
     * 获取操作类型文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getOperatorTypeTextAttr($value, $data)
    {
        $typeMap = [
            self::OPERATOR_USER => '用户自助',
            self::OPERATOR_ADMIN => '管理员代充'
        ];
        return $typeMap[$data['operator_type']] ?? '未知';
    }
    
    /**
     * 关联用户
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo('Member', 'user_id', 'id');
    }
    
    /**
     * 关联卡信息
     * @return \think\model\relation\BelongsTo
     */
    public function card()
    {
        return $this->belongsTo('app\admin\model\Ycard', 'card_id', 'id');
    }
    
    /**
     * 关联充值套餐
     * @return \think\model\relation\BelongsTo
     */
    public function package()
    {
        return $this->belongsTo('CardRechargePackage', 'package_id', 'id');
    }
    
    /**
     * 生成充值单号
     * @return string
     */
    public function generateRechargeNo()
    {
        return 'RC' . date('YmdHis') . rand(1000, 9999);
    }
    
    /**
     * 创建充值记录
     * @param array $data 充值数据
     * @return bool|string
     */
    public function createRechargeRecord($data)
    {
        $this->startTrans();
        try {
            // 验证卡信息
            $card = Ycard::where('id', $data['card_id'])
                ->where('member_id', $data['user_id'])
                ->where('clientkeynum', $data['clientkeynum'])
                ->find();
                
            if (empty($card)) {
                throw new \Exception('卡信息不存在或不属于当前用户');
            }
            
            // 检查卡状态是否正常
            if ($card['status'] != 3) { // 假设3表示正常使用状态
                throw new \Exception('卡状态异常，无法充值');
            }
            
            // 验证充值套餐（如果使用套餐）
            if (!empty($data['package_id'])) {
                $model = new CardRechargePackage();
                $package = $model->checkPackageValid($data['package_id'], $data['clientkeynum']);
                if (!$package) {
                    throw new \Exception('充值套餐无效或已过期');
                }
            }
            
            // 生成充值单号
            $rechargeNo = $this->generateRechargeNo();
            
            // 记录充值前余额
            $beforeBalance = $card['yu_money'];
            $afterBalance = bcadd($beforeBalance, $data['total_amount'], 2);
            
            // 准备充值记录数据
            $recordData = [
                'clientkeynum' => $data['clientkeynum'],
                'recharge_no' => $rechargeNo,
                'user_id' => $data['user_id'],
                'card_id' => $data['card_id'],
                'cardnum' => $card['cardnum'],
                'package_id' => $data['package_id'] ?? null,
                'package_name' => $data['package_name'] ?? null,
                'recharge_amount' => $data['recharge_amount'],
                'bonus_amount' => $data['bonus_amount'] ?? 0,
                'total_amount' => $data['total_amount'],
                'before_balance' => $beforeBalance,
                'after_balance' => $afterBalance,
                'payment_method' => $data['payment_method'],
                'payment_status' => $data['payment_status'] ?? self::STATUS_PENDING,
                'operator_type' => $data['operator_type'] ?? self::OPERATOR_USER,
                'operator_id' => $data['operator_id'] ?? null,
                'operator_name' => $data['operator_name'] ?? null,
                'remark' => $data['remark'] ?? '',
                'add_time' => date('Y-m-d H:i:s')
            ];
            
            // 如果是已支付状态，直接更新卡余额
            if ($recordData['payment_status'] == self::STATUS_PAID) {
                // 更新卡余额
                Ycard::where('id', $data['card_id'])
                    ->where('id', $data['card_id'])
                    ->update([
                        'yu_money' => $afterBalance,
                        'update_time' => date('Y-m-d H:i:s')
                    ]);
                    
                $recordData['pay_time'] = date('Y-m-d H:i:s');
            }
            
            // 插入充值记录
            $this->insert($recordData);
            
            $this->commit();
            return $rechargeNo;
        } catch (\Exception $e) {
            $this->rollback();
            return $e->getMessage();
        }
    }
    
    /**
     * 更新支付状态
     * @param string $rechargeNo 充值单号
     * @param int $status 支付状态
     * @param string $transactionId 第三方交易单号
     * @return bool
     */
    public function updatePaymentStatus($rechargeNo, $status, $transactionId = '')
    {
        $this->startTrans();
        try {
            // 获取充值记录
            $record = $this->where('recharge_no', $rechargeNo)->find();
            if (empty($record)) {
                throw new \Exception('充值记录不存在');
            }
            
            // 如果状态变为已支付，需要更新卡余额
            if ($status == self::STATUS_PAID && $record['payment_status'] != self::STATUS_PAID) {
                // 更新卡余额
                Ycard::where('id', $record['card_id'])
                    ->update([
                        'yu_money' => $record['after_balance'],
                        // 'update_time' => date('Y-m-d H:i:s')
                    ]);
            }
            
            // 更新充值记录
            $updateData = [
                'payment_status' => $status,
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            if ($status == self::STATUS_PAID) {
                $updateData['pay_time'] = date('Y-m-d H:i:s');
            }
            
            if (!empty($transactionId)) {
                $updateData['transaction_id'] = $transactionId;
            }
            
            $this->where('recharge_no', $rechargeNo)->update($updateData);
            
            $this->commit();
            return true;
        } catch (\Exception $e) {
            Log::error('更新充值记录支付状态失败: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            $this->rollback();
            return false;
        }
    }
    
    /**
     * 获取用户充值记录
     * @param int $userId 用户ID
     * @param string $clientkeynum 客户唯一标识
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getUserRechargeRecords($userId, $clientkeynum, $page = 1, $limit = 20)
    {
        $offset = ($page - 1) * $limit;
        
        $list = $this->where('user_id', $userId)
            ->where('clientkeynum', $clientkeynum)
            ->order('add_time', 'desc')
            ->limit($offset, $limit)
            ->select();
            
        $total = $this->where('user_id', $userId)
            ->where('clientkeynum', $clientkeynum)
            ->count();
            
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ];
    }
} 