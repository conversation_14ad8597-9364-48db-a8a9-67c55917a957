<?php
/**
 * 在线支付记录模型
 * @date 2023-6-20
 * <AUTHOR>
 */

namespace app\admin\model;


use think\Model;

class PaymentRecord extends Model
{
    protected $table = 'payment_record';

    protected $pk = 'id';

    public function getPayStatusAttr($value)
    {
        $arr = [
            0 => '未支付',
            1 => '已支付'
        ];
        return $arr[$value] ?? '状态异常';
    }

    public function getPayTimeAttr($value)
    {
        if (!empty($value)) {
            return date('Y-m-d H:i:s', $value);
        }
    }

    public static function add($data)
    {
        $model = new self();
        $model->member_id = $data['member_id'];
        $model->basekeynum = $data['basekeynum'];
        $model->order_sn = $data['order_sn'];
        $model->money = $data['money'];
        $model->pay_no = $data['pay_no'];
        $model->pay_status = $data['pay_status'];
        $model->add_time = $data['add_time'];
        return $model->save();
    }

    public static function getInfo($where)
    {
        return self::where($where)->find();
    }

    public static function getList($where, $page = 1, $page_size = 10)
    {
        return self::where($where)->page($page, $page_size)->order('id', 'desc')->select();
    }

    public static function getCount($where)
    {
        return self::where($where)->count();
    }
}
