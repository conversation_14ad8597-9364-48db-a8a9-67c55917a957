<?php
/**
 * 卡套餐兑换主表模型
 * Date: 2024/03/17
 */

namespace app\admin\model;

use think\Model;
use think\Db;

class CardPackageRedemption extends Model
{
    protected $table = 'card_package_redemption';
    protected $pk = 'id';
    
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    
    // 兑换状态：0-已取消，1-已兑换，2-已发货，100-已完成
    const STATUS_CANCELED = 0;
    const STATUS_REDEEMED = 1;
    const STATUS_SHIPPED = 2;
    const STATUS_COMPLETED = 100;
    
    // 兑换类型：1-到店兑换，2-快递配送
    const TYPE_STORE = 1;
    const TYPE_EXPRESS = 2;
    
    /**
     * 关联卡
     * @return \think\model\relation\BelongsTo
     */
    public function card()
    {
        return $this->belongsTo('Card', 'card_id', 'id');
    }
    
    /**
     * 关联用户
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo('ClientMember', 'user_id', 'id');
    }
    
    /**
     * 关联门店
     * @return \think\model\relation\BelongsTo
     */
    public function shop()
    {
        return $this->belongsTo('Shop', 'shop_id', 'id');
    }
    
    /**
     * 关联操作员
     * @return \think\model\relation\BelongsTo
     */
    public function operator()
    {
        return $this->belongsTo('ClientMember', 'operator_id', 'id');
    }
    
    /**
     * 关联兑换明细
     * @return \think\model\relation\HasMany
     */
    public function details()
    {
        return $this->hasMany('CardPackageRedemptionDetail', 'redemption_id', 'id');
    }
    
    /**
     * 关联兑换商品
     * @return \think\model\relation\HasMany
     */
    public function products()
    {
        return $this->hasMany('CardPackageRedemptionProduct', 'redemption_id', 'id');
    }
    
    /**
     * 获取用户的兑换记录
     * @param int $userId 用户ID
     * @param string $clientkeynum 客户唯一标识
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getUserRedemptions($userId, $clientkeynum)
    {
        return $this->with(['details', 'products'])
            ->where('user_id', $userId)
            ->where('clientkeynum', $clientkeynum)
            ->order('redemption_time', 'desc')
            ->select();
    }
    
    /**
     * 获取卡的兑换记录
     * @param int $cardId 卡ID
     * @param string $clientkeynum 客户唯一标识
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getCardRedemptions($cardId, $clientkeynum)
    {
        return $this->with(['details', 'products'])
            ->where('card_id', $cardId)
            ->where('clientkeynum', $clientkeynum)
            ->order('redemption_time', 'desc')
            ->select();
    }
    
    /**
     * 更新兑换状态
     * @param int $id 兑换ID
     * @param int $status 新状态
     * @param string $remark 备注
     * @return bool
     * @throws \think\Exception
     * @throws \think\exception\PDOException
     */
    public function updateRedemptionStatus($id, $status, $remark = '')
    {
        // 开启事务
        $this->startTrans();
        try {
            // 更新主表状态
            $redemption = $this->where('id', $id)->find();
            if (!$redemption) {
                $this->rollback();
                return false;
            }
            
            $redemption->status = $status;
            if ($remark) {
                $redemption->remark = $remark;
            }
            $redemption->update_time = date('Y-m-d H:i:s');
            $redemption->save();
            
            // 更新明细表状态
            $detailModel = new CardPackageRedemptionDetail;
            $detailModel->where('redemption_id', $id)->update([
                'status' => $status,
                'update_time' => date('Y-m-d H:i:s')
            ]);
            
            // 记录状态变更日志
            $operator_id = session('cn_accountinfo.account_id');
            $operator = session('cn_accountinfo.accountname');
            
            Db::name('card_package_redemption_log')->insert([
                'clientkeynum' => $redemption->clientkeynum,
                'redemption_id' => $id,
                'operator_id' => $operator_id,
                'operator' => $operator,
                'operation' => '状态变更',
                'content' => '状态从 ' . $this->getStatusText($redemption['status']) . ' 变更为 ' . $this->getStatusText($status) . ($remark ? ', 备注: ' . $remark : ''),
                'add_time' => date('Y-m-d H:i:s')
            ]);
            
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }
    
    /**
     * 获取状态文本
     * @param int $status 状态值
     * @return string 状态文本
     */
    public function getStatusText($status)
    {
        $statusArr = [
            self::STATUS_CANCELED => '已取消',
            self::STATUS_REDEEMED => '已兑换',
            self::STATUS_SHIPPED => '已发货',
            self::STATUS_COMPLETED => '已完成'
        ];
        
        return isset($statusArr[$status]) ? $statusArr[$status] : '未知状态';
    }
    
    /**
     * 获取类型文本
     * @param int $type 类型值
     * @return string 类型文本
     */
    public function getTypeText($type)
    {
        $typeArr = [
            self::TYPE_STORE => '到店兑换',
            self::TYPE_EXPRESS => '快递配送'
        ];
        
        return isset($typeArr[$type]) ? $typeArr[$type] : '未知类型';
    }
    
    /**
     * 获取各门店待发货订单统计
     * @param string $clientkeynum 客户唯一标识
     * @return array 统计数据
     */
    public function getShopPendingStatistics($clientkeynum)
    {
        // 查询各门店待发货数量
        $statistics = Db::name('shop')->alias('s')
            ->join('card_package_redemption r', 's.id = r.shop_id', 'left')
            ->field('s.id, s.title as shop_name, COUNT(IF(r.status = 1 AND r.type = 2, 1, NULL)) as pending_count')
            ->where('s.clientkeynum', $clientkeynum)
            ->where('s.status', 1)
            ->group('s.id')
            ->order('pending_count', 'desc')
            ->select();
            
        // 获取未分配门店的订单数量
        $unassignedCount = Db::name('card_package_redemption')
            ->where('clientkeynum', $clientkeynum)
            ->where('type', 2) // 快递配送
            ->where('status', 1) // 已兑换
            ->where(function ($query) {
                $query->where('shop_id', 'null')
                    ->whereOr('shop_id', 0);
            })
            ->count();
            
        return [
            'statistics' => $statistics,
            'unassigned_count' => $unassignedCount
        ];
    }
    
    /**
     * 分配门店处理
     * @param int $id 兑换ID
     * @param int $shop_id 门店ID
     * @param string $remark 备注
     * @param string $clientkeynum 客户唯一标识
     * @return bool 是否成功
     */
    public function assignShop($id, $shop_id, $remark = '', $clientkeynum = '')
    {
        // 获取操作员信息
        $operator_id = session('cn_accountinfo.account_id');
        $operator = session('cn_accountinfo.accountname');
        if (empty($clientkeynum)) {
            $clientkeynum = session('cn_accountinfo.basekeynum');
        }
        
        // 获取门店信息
        $shop = Db::name('shop')->where('id', $shop_id)->where('clientkeynum', $clientkeynum)->find();
        if (!$shop) {
            return false;
        }
        
        // 获取兑换记录
        $redemption = $this->where('id', $id)->where('clientkeynum', $clientkeynum)->find();
        if (!$redemption) {
            return false;
        }
        
        // 验证状态
        if ($redemption['status'] != 1) {
            return false;
        }
        
        // 更新门店信息
        $result = $this->where('id', $id)
            ->where('clientkeynum', $clientkeynum)
            ->update([
                'shop_id' => $shop_id,
                'assign_time' => date('Y-m-d H:i:s'),
                'assign_operator_id' => $operator_id,
                'assign_operator' => $operator,
                'assign_remark' => $remark,
                'update_time' => date('Y-m-d H:i:s')
            ]);
            
        if ($result) {
            // 记录操作日志
            Db::name('card_package_redemption_log')->insert([
                'clientkeynum' => $clientkeynum,
                'redemption_id' => $id,
                'operator_id' => $operator_id,
                'operator' => $operator,
                'operation' => '分配门店',
                'content' => '分配给门店: ' . $shop['title'] . ($remark ? ', 备注: ' . $remark : ''),
                'add_time' => date('Y-m-d H:i:s')
            ]);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 批量分配门店
     * @param array $ids 兑换ID数组
     * @param int $shop_id 门店ID
     * @param string $remark 备注
     * @param string $clientkeynum 客户唯一标识
     * @return int 成功处理数量
     */
    public function batchAssignShop($ids, $shop_id, $remark = '', $clientkeynum = '')
    {
        if (empty($clientkeynum)) {
            $clientkeynum = session('cn_accountinfo.basekeynum');
        }
        
        $success_count = 0;
        
        foreach ($ids as $id) {
            $result = $this->assignShop($id, $shop_id, $remark, $clientkeynum);
            if ($result) {
                $success_count++;
            }
        }
        
        return $success_count;
    }
} 