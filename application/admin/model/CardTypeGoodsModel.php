<?php
/**
 * 卡号绑定商品模型
 * @date 2023-5-8
 * <AUTHOR>
 */
namespace app\admin\model;

use think\Model;

class CardTypeGoodsModel extends Model
{
    protected $pk = 'id';

    protected $table = 'card_type_goods';

    public function getCardTypeClassifyNameAttr($value, $data)
    {
        return CardTypeClassifyModel::where(['id' => $data['card_type_classify_id']])->value('name');
    }

    public function getCardTemplateNameAttr($value, $data)
    {
        return CardTemplateModel::where(['id' => $data['card_template_id']])->value('title');
    }

    public function goods()
    {
        return $this->hasOne('GoodsModel','id','goodsid');
    }

    /**
     * 通过goodsid获取列表
     * @param $goods_id
     * @param $where
     * @return bool|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getListByGoodsId($goods_id, $where)
    {
        $where['goodsid'] = $goods_id;
        $list = self::where($where)
            ->field('*, card_type_classify_id as card_type_classify_name')
            ->with('goods')
            ->select();
        return $list;
    }


    /**
     * 批量修改价格
     * @param $data
     * @param $where
     * @return array|false|\think\Collection|\think\model\Collection
     * @throws \Exception
     */
    public static function batchUpdatePrice($data)
    {
        $model = new self();
        return $model->saveAll($data);
    }
}
