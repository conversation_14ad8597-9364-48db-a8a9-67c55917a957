<?php
/**
 * 轮播图模型
 * @date 2023-5-31
 * <AUTHOR>
 */

namespace app\admin\model;


use think\Model;

class Banner extends Model
{
    protected $pk = 'id';

    protected $table = 'banner';

    public static function del($id, $where = [])
    {
        $where['id'] = $id;
        return self::where($where)->delete();
    }

    // 改
    public static function edit($where, $data)
    {
        return self::where($where)->update($data);
    }

    /**
     * 根据id查询数据
     * @param $id
     * @param $where
     * @return array|bool|\PDOStatement|string|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getInfoById($id, $where)
    {
        $where['id'] = ['=', $id];
        $info = self::where($where)->find();
        return $info;
    }

    /**
     * 增
     * @param $data
     * @return false|int
     */
    public static function add($data)
    {
        $model = new self();
        $model->basekeynum = $data['basekeynum'];
        $model->title = $data['title'];
        $model->status = $data['status'];
        $model->sort = $data['sort'];
        $model->image = $data['image'];
        $model->add_time = $data['add_time'];
        $model->content = $data['content'];
        $result = $model->save();
        return $result;
    }

    /**
     * 查
     * @param $where array 查询条件
     * @param $page int 页数
     * @param $pagesize int 每页条数
     * @return bool|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getList($where, $page, $pagesize)
    {
        return self::where($where)
            ->order('sort', 'desc')
            ->field('id, title, status, sort, image, add_time')
            ->page($page, $pagesize)
            ->select();
    }

    /**
     * 获取总条数
     * @param $where array 查询条件
     * @return int|string
     * @throws \think\Exception
     */
    public static function getCount($where)
    {
        return self::where($where)
            ->count();
    }

}
