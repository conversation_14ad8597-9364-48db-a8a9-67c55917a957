<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/9/11 下午5:37
 */

namespace app\admin\model;

use app\api\lib\Toolkit;
use app\api\model\ProductInventory;
use app\api\model\ProductSpecAttr;
use app\api\model\ShopProductInventory;
use app\api\model\SkuSpec;
use think\Db;
use think\facade\Log;
use think\Model;

class Product extends Model
{
    protected $table = 'products';

    protected $pk = 'id';

    protected $append = [
        'status_text' ,
    ];

    public function inventories()
    {
        return $this->hasMany('Inventory', 'product_id', 'id');
    }

    const STATUS_FORBIDDEN = 0;
    const STATUS_NORMAL = 1;

    const UNIFIED_SPECIFICATION = 0;
    const MULTIPLE_SPECIFICATIONS = 1;

    // 商品类型常量
    const PRODUCT_TYPE_NORMAL = 1;    // 普通商品
    const PRODUCT_TYPE_WEIGHT = 2;    // 计量商品
    const PRODUCT_TYPE_GIFT = 3;    // 赠品
    public function category()
    {
        return $this->hasOne('ProductCategory', 'id', 'category_id');
    }

    public static function statusOptions()
    {
        return [
            self::STATUS_FORBIDDEN => '下架',
            self::STATUS_NORMAL => '上架',
        ];
    }

    public function getStatusTextAttr($value, $data)
    {
        $options = self::statusOptions();
        return $options[$data['state']] ?? '';
    }

    public static function isAttributeOptions()
    {
        return [
            self::UNIFIED_SPECIFICATION => '统一规格',
            self::MULTIPLE_SPECIFICATIONS => '多规格',
        ];
    }

    /**
     * 获取商品类型文本
     */
    public function getProductTypeTextAttr($value, $data)
    {
        $typeMap = [
            self::PRODUCT_TYPE_NORMAL => '普通商品',
            self::PRODUCT_TYPE_WEIGHT => '计量商品',
            self::PRODUCT_TYPE_GIFT => '赠品'
        ];
        return $typeMap[$data['product_type']] ?? '未知类型';
    }
    
    /**
     * 是否为计量商品
     */
    public function isWeightProduct()
    {
        return $this->product_type == self::PRODUCT_TYPE_WEIGHT;
    }
    
    /**
     * 获取普通商品列表（排除计量商品）
     */
    public static function getNormalProducts($where = [])
    {
        return self::where('product_type', self::PRODUCT_TYPE_NORMAL)
                   ->where($where)
                   ->select();
    }
    
    /**
     * 获取计量商品列表
     */
    public static function getWeightProducts($where = [])
    {
        return self::where('product_type', self::PRODUCT_TYPE_WEIGHT)
                   ->where($where)
                   ->select();
    }

    /**
     * 增加/更新商品
     * @param $post 表单提交的数据
     * @param null|Product $productModel 商品模型 有值表示更新
     * @return string|null
     */
    public static function addOrEdit($post, $productModel = null)
    {
        $basekeynum = session('cn_accountinfo.basekeynum');
        $post['clientkeynum'] = $basekeynum;
        
        // 数据校验
        $validationResult = self::validateProductData($post, $productModel);
        if ($validationResult !== null) {
            return $validationResult;
        }
        
        if ($post['state'] == 'on') {
            $post['state'] = self::STATUS_NORMAL;
        } else {
            $post['state'] = self::STATUS_FORBIDDEN;
        }
        $errMsg = null;
        try {
            Db::startTrans();
            if ($productModel) {
                // Update basic information
                $id = $post['id'];
                unset($post['id']);
                $productModel->save($post);
                $post['id'] = $id;
            } else {
                // Create product basic information
                $post['price'] = $post['price'] ?: 999;
                $productModel = Product::create($post);
            }

            // Handle multiple specifications
            if ($post['is_attribute'] == Product::MULTIPLE_SPECIFICATIONS) {
                if (empty($post['skus'])) {
                    throw new \Exception('SKU数据不能为空');
                }
                
                // 验证规格数据
                $specValidationResult = self::validateSpecData($post['spec'] ?? []);
                if ($specValidationResult !== null) {
                    throw new \Exception($specValidationResult);
                }

                // SKU编号重复检测
                $skuValidationResult = self::validateSkuData($post['skus'], $productModel->id, $basekeynum);
                if ($skuValidationResult !== null) {
                    throw new \Exception($skuValidationResult);
                }

                // Collect existing spec IDs
                $existingSpecIds = SkuSpec::where('product_id', $productModel->id)->column('id');

                $specValueGroups = [];
                $specValueIdMap = [];
                $submittedSpecIds = [];
                $submittedSpecValueIds = [];
                foreach ($post['spec'] as $spec) {
                    // Find or create product specification
                    $specModel = SkuSpec::where('product_id', $productModel->id)
                        ->where('spec_name', $spec['title'])
                        ->find();
                    if (!$specModel) {
                        $specModel = SkuSpec::create([
                            'product_id' => $productModel->id,
                            'spec_name' => $spec['title']
                        ]);
                    }
                    $submittedSpecIds[] = $specModel->id;

                    $specValueIdGroup = [];
                    foreach ($spec['child'] as $specValue) {
                        // Find or create product specification value
                        $specValueModel = ProductSpecAttr::where('product_id', $productModel->id)
                            ->where('spec_id', $specModel->id)
                            ->where('attr_value', $specValue['title'])
                            ->find();
                        if (!$specValueModel) {
                            $specValueModel = ProductSpecAttr::create([
                                'product_id' => $productModel->id,
                                'spec_id' => $specModel->id,
                                'attr_value' => $specValue['title'],
                                'checked' => $specValue['checked'] === 'true' ? 1 : 0
                            ]);
                        } else {
                            $specValueModel->checked = $specValue['checked'] === 'true' ? 1 : 0;
                            $specValueModel->save();
                        }

                        if ($specValue['checked'] === 'true') {
                            $specValueIdMap[$specValue['id']] = [
                                'id' => $specValue['id'],
                                'attr_value' => $specValue['title'],
                                'spec_id' => $specValueModel->id
                            ];
                            $specValueIdGroup[] = $specValue['id'];
                            $submittedSpecValueIds[] = $specValueModel->id;
                        }
                    }

                    if ($specValueIdGroup) {
                        $specValueGroups[] = $specValueIdGroup;
                    }
                }

                // Delete spec values that are no longer present
                $existingSpecValueIds = ProductSpecAttr::whereIn('spec_id', $existingSpecIds)->column('id');
                $specValuesToDelete = array_diff($existingSpecValueIds, $submittedSpecValueIds);
                if (!empty($specValuesToDelete)) {
                    $specValuesToDelete = array_values($specValuesToDelete);
                    ProductSpecAttr::destroy($specValuesToDelete);
                }

                // Delete specs that are no longer present
                $specsToDelete = array_diff($existingSpecIds, $submittedSpecIds);
                if (!empty($specsToDelete)) {
                    $specsToDelete = array_values($specsToDelete);
                    SkuSpec::destroy($specsToDelete);
                }

                // Collect existing inventory IDs
                $existingInventoryIds = ProductInventory::where('product_id', $productModel->id)->column('id');

                $totalStock = 0;
                $minPrice = 9999999999;
                $submittedInventoryIds = [];
                foreach (Toolkit::diker($specValueGroups) as $diker) {
                    $skuName = [];
                    $skuData = [];
                    $skuIndexData = [];
                    if (is_array($diker)) {
                        // Multiple specifications
                        foreach ($diker as $v) {
                            $skuName[] = $specValueIdMap[$v]['attr_value'];
                            $skuData[] = $specValueIdMap[$v]['spec_id'];
                            $skuIndexData[] = $specValueIdMap[$v]['id'];
                        }
                    } else {
                        // Single specification
                        $skuName[] = $specValueIdMap[$diker]['attr_value'];
                        $skuData[] = $specValueIdMap[$diker]['spec_id'];
                        $skuIndexData[] = $specValueIdMap[$diker]['id'];
                    }

                    $skuIndexStr = join('-', $skuIndexData);
                    $inventory = ProductInventory::where('clientkeynum', $basekeynum)
                        ->where('product_id', $productModel->id)
                        ->where('id', $post['skus'][$skuIndexStr]['id'] ?? $post['skus'][$skuIndexStr]['sku'])
                        ->find();
                    if (!$inventory) {
                        $inventoryData = [
                            'clientkeynum' => $basekeynum,
                            'product_id' => $productModel->id,
                            'sn' => $post['skus'][$skuIndexStr]['sku'] ?? $post['skus'][$skuIndexStr]['id'],
                            'title' => join(' ', $skuName),
                            'image' => $post['skus'][$skuIndexStr]['image'],
                            'price' => $post['skus'][$skuIndexStr]['price'],
                            'data' => join('-', $skuData),
                            'status' => $post['skus'][$skuIndexStr]['checked'] === 'true' ? 1 : 0,
                        ];
                        
                        // 如果是计量商品，保存重量单位
                        if (($post['product_type'] ?? self::PRODUCT_TYPE_NORMAL) == self::PRODUCT_TYPE_WEIGHT) {
                            $inventoryData['weight_unit'] = $post['skus'][$skuIndexStr]['weight_unit'] ?? 'kg';
                        }
                        
                        $inventory = ProductInventory::create($inventoryData);
                        
                        // 生成条形码
                        $barcode = $inventory->generateBarcode($inventory->sn);
                        $inventory->barcode = $barcode;
                        $inventory->save();
                    } else {
                        $inventory['title'] = join(' ', $skuName);
                        $inventory['image'] = $post['skus'][$skuIndexStr]['image'];
                        $inventory['price'] = $post['skus'][$skuIndexStr]['price'];
                        $inventory['data'] = join('-', $skuData);
                        $inventory['status'] = $post['skus'][$skuIndexStr]['checked'] === 'true' ? 1 : 0;
                        
                        // 如果是计量商品，更新重量单位
                        if (($post['product_type'] ?? self::PRODUCT_TYPE_NORMAL) == self::PRODUCT_TYPE_WEIGHT) {
                            $inventory['weight_unit'] = $post['skus'][$skuIndexStr]['weight_unit'] ?? 'kg';
                        }
                        
                        // 如果sn有变更，重新生成条形码
                        if ($inventory->sn != ($post['skus'][$skuIndexStr]['sku'] ?? $post['skus'][$skuIndexStr]['id'])) {
                            $inventory->sn = $post['skus'][$skuIndexStr]['sku'] ?? $post['skus'][$skuIndexStr]['id'];
                            $barcode = $inventory->generateBarcode($inventory->sn);
                            $inventory->barcode = $barcode;
                        }
                        $inventory->save();
                    }

                    $submittedInventoryIds[] = $inventory->id;

                    if ($post['skus'][$skuIndexStr]['price'] < $minPrice) {
                        $minPrice = $post['skus'][$skuIndexStr]['price'];
                    }
                }

                // Delete inventories that are no longer present
                $inventoriesToDelete = array_diff($existingInventoryIds, $submittedInventoryIds);
                if (!empty($inventoriesToDelete)) {
                    $inventoriesToDelete = array_values($inventoriesToDelete);
                    ProductInventory::destroy($inventoriesToDelete);
                    // 也需要删除 shop_product_inventory 表中相关数据
                    ShopProductInventory::where('product_id', $productModel->id)->whereIn('inventory_id', $inventoriesToDelete)->delete();
                }

                // Update product basic information
                $productModel->save([
                    'price' => $minPrice,
                ]);
            } else {
                // Handle unified specifications (统一规格)
                // 统一规格模式的处理逻辑
                
                // 验证统一规格数据
                if (empty($post['sku'])) {
                    throw new \Exception('SKU编码不能为空');
                }
                if (empty($post['price']) || !is_numeric($post['price']) || $post['price'] <= 0) {
                    throw new \Exception('销售价格不能为空且必须大于0');
                }
                
                // 计量商品需要验证重量单位
                if (($post['product_type'] ?? self::PRODUCT_TYPE_NORMAL) == self::PRODUCT_TYPE_WEIGHT) {
                    if (empty($post['weight_unit'])) {
                        throw new \Exception('计量商品必须选择重量单位');
                    }
                }
                
                // SKU编号重复检测
                $existingSku = ProductInventory::where('sn', $post['sku'])
                    ->where('clientkeynum', $basekeynum);
                if ($productModel) {
                    $existingSku->where('product_id', '<>', $productModel->id);
                }
                if ($existingSku->find()) {
                    throw new \Exception("SKU编码[{$post['sku']}]已被其他商品使用，请使用不同的编码");
                }
                
                // 查找或创建统一规格的库存记录
                $inventory = ProductInventory::where('id', $post['id'])
                    ->where('clientkeynum', $basekeynum)
                    ->find();
                
                if (!$inventory) {
                    // 创建新的库存记录
                    $inventoryData = [
                        'clientkeynum' => $basekeynum,
                        'product_id' => $productModel->id,
                        'sn' => $post['sku'],
                        'title' => $productModel->title,
                        'image' => '',
                        'price' => $post['price'],
                        'data' => '',
                        'status' => isset($post['status']) ? $post['status'] : 1,
                    ];
                    
                    // 如果是计量商品，保存重量单位
                    if (($post['product_type'] ?? self::PRODUCT_TYPE_NORMAL) == self::PRODUCT_TYPE_WEIGHT) {
                        $inventoryData['weight_unit'] = $post['weight_unit'];
                    }
                    
                    $inventory = ProductInventory::create($inventoryData);
                    
                    // 生成条形码
                    $barcode = $inventory->generateBarcode($inventory->sn);
                    $inventory->barcode = $barcode;
                    $inventory->save();
                } else {
                    // 更新现有库存记录
                    $inventory->sn = $post['sku'];
                    $inventory->title = $productModel->title;
                    $inventory->price = $post['price'];
                    $inventory->status = isset($post['status']) ? $post['status'] : 1;
                    
                    // 如果是计量商品，更新重量单位
                    if (($post['product_type'] ?? self::PRODUCT_TYPE_NORMAL) == self::PRODUCT_TYPE_WEIGHT) {
                        $inventory->weight_unit = $post['weight_unit'];
                    }
                    
                    // 重新生成条形码（如果SKU有变更）
                    $barcode = $inventory->generateBarcode($inventory->sn);
                    $inventory->barcode = $barcode;
                    $inventory->save();
                }
                
                // 更新商品基础价格
                $productModel->save([
                    'price' => $post['price'],
                ]);
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('error_line' . $e->getLine());
            Log::error('error_trace' . $e->getTraceAsString());
            $errMsg = $e->getMessage();
        }

        return $errMsg;
    }

    /**
     * 验证商品基础数据
     * @param array $data 商品数据
     * @param Product|null $productModel 商品模型
     * @return string|null 验证错误信息，null表示验证通过
     */
    private static function validateProductData($data, $productModel = null)
    {
        // 必填字段验证
        $requiredFields = [
            'title' => '商品标题',
            'category_id' => '商品分类',
            'cover' => '商品封面'
        ];
        
        foreach ($requiredFields as $field => $fieldName) {
            if (empty($data[$field])) {
                return "{$fieldName}不能为空";
            }
        }
        
        // 商品标题长度验证
        if (mb_strlen($data['title']) > 100) {
            return '商品标题不能超过100个字符';
        }
        
        // 商品标题重复检测（同一商户下）
        $basekeynum = session('cn_accountinfo.basekeynum');
        $titleQuery = self::where('clientkeynum', $basekeynum)
            ->where('title', $data['title']);
        
        // 如果是更新操作，排除当前商品
        if ($productModel) {
            $titleQuery->where('id', '<>', $productModel->id);
        }
        
        if ($titleQuery->find()) {
            return '商品标题已存在，请使用其他标题';
        }
        
        // 分类ID有效性验证
        $categoryExists = ProductCategory::where('id', $data['category_id'])
            ->where('clientkeynum', $basekeynum)
            ->find();
        if (!$categoryExists) {
            return '商品分类不存在或无权限访问';
        }
        
        // 价格验证（如果是统一规格）
        if (isset($data['price']) && $data['price'] !== '') {
            if (!is_numeric($data['price']) || $data['price'] < 0) {
                return '商品价格必须为大于等于0的数字';
            }
            if ($data['price'] > 999999.99) {
                return '商品价格不能超过999999.99';
            }
        }
        
        // 计量商品特殊验证
        $productType = $data['product_type'] ?? self::PRODUCT_TYPE_NORMAL;
        if ($productType == self::PRODUCT_TYPE_WEIGHT) {
            $isAttribute = $data['is_attribute'] ?? self::UNIFIED_SPECIFICATION;
            
            // 统一规格模式下，计量商品必须设置重量单位
            if ($isAttribute == self::UNIFIED_SPECIFICATION) {
                if (empty($data['weight_unit'])) {
                    return '计量商品在统一规格模式下必须选择重量单位';
                }
                
                // 计量商品必须设置价格（作为单位价格）
                if (!isset($data['price']) || $data['price'] === '' || $data['price'] <= 0) {
                    return '计量商品必须设置单位价格';
                }
            }
        }
        
        return null;
    }
    
    /**
     * 验证SKU数据
     * @param array $skus SKU数据
     * @param int $productId 商品ID
     * @param string $basekeynum 商户标识
     * @return string|null 验证错误信息，null表示验证通过
     */
    private static function validateSkuData($skus, $productId, $basekeynum)
    {
        $skuCodes = [];
        $skuPrices = [];
        
        foreach ($skus as $skuKey => $sku) {
            // SKU编号验证
            if (empty($sku['sku'])) {
                return "SKU编号不能为空";
            }
            
            // SKU编号格式验证（只允许字母数字和下划线）
            if (!preg_match('/^[a-zA-Z0-9_-]+$/', $sku['sku'])) {
                return "SKU编号[{$sku['sku']}]格式不正确，只允许使用字母、数字、下划线和连字符";
            }
            
            // SKU编号长度验证
            if (strlen($sku['sku']) > 50) {
                return "SKU编号[{$sku['sku']}]长度不能超过50个字符";
            }
            
            // 检查当前提交的SKU中是否有重复
            if (in_array($sku['sku'], $skuCodes)) {
                return "SKU编号[{$sku['sku']}]在当前商品中重复，请使用不同的编号";
            }
            $skuCodes[] = $sku['sku'];
            
            // 检查数据库中是否存在相同的SKU编号（排除当前商品的SKU）
            $existingSku = ProductInventory::where('sn', $sku['sku'])
                ->where('clientkeynum', $basekeynum);
                
            // 如果是更新操作，排除当前商品的库存记录
            if ($productId) {
                $existingSku->where('product_id', '<>', $productId);
            }
            
            $existingSkuRecord = $existingSku->find();
            if ($existingSkuRecord) {
                return "SKU编号[{$sku['sku']}]已被其他商品使用，请使用不同的编号";
            }
            
            // 价格验证
            if (!isset($sku['price']) || $sku['price'] === '') {
                return "SKU[{$sku['sku']}]的价格不能为空";
            }
            
            if (!is_numeric($sku['price']) || $sku['price'] < 0) {
                return "SKU[{$sku['sku']}]的价格必须为大于等于0的数字";
            }
            
            if ($sku['price'] > 999999.99) {
                return "SKU[{$sku['sku']}]的价格不能超过999999.99";
            }
            
            $skuPrices[] = $sku['price'];
        }
        
        // 检查是否至少有一个有效的SKU
        if (empty($skuCodes)) {
            return "至少需要设置一个有效的SKU";
        }
        
        return null;
    }
    
    /**
     * 验证规格数据
     * @param array $specs 规格数据
     * @return string|null 验证错误信息，null表示验证通过
     */
    private static function validateSpecData($specs)
    {
        if (empty($specs)) {
            return '多规格商品必须设置商品规格';
        }
        
        foreach ($specs as $specIndex => $spec) {
            // 验证规格名称
            if (empty($spec['title'])) {
                return "第" . ($specIndex + 1) . "个规格的名称不能为空";
            }
            
            if (mb_strlen($spec['title']) > 20) {
                return "规格名称[{$spec['title']}]不能超过20个字符";
            }
            
            // 验证规格值
            if (empty($spec['child']) || !is_array($spec['child'])) {
                return "规格[{$spec['title']}]必须至少包含一个规格值";
            }
            
            $checkedCount = 0;
            $specValueNames = [];
            
            foreach ($spec['child'] as $valueIndex => $specValue) {
                // 验证规格值名称
                if (empty($specValue['title'])) {
                    return "规格[{$spec['title']}]的第" . ($valueIndex + 1) . "个规格值名称不能为空";
                }
                
                if (mb_strlen($specValue['title']) > 20) {
                    return "规格值[{$specValue['title']}]不能超过20个字符";
                }
                
                // 检查规格值名称重复
                if (in_array($specValue['title'], $specValueNames)) {
                    return "规格[{$spec['title']}]中存在重复的规格值[{$specValue['title']}]";
                }
                $specValueNames[] = $specValue['title'];
                
                // 统计选中的规格值数量
                if (isset($specValue['checked']) && $specValue['checked'] === 'true') {
                    $checkedCount++;
                }
            }
            
            // 验证至少选中一个规格值
            if ($checkedCount == 0) {
                return "规格[{$spec['title']}]必须至少选中一个规格值";
            }
        }
        
        // 检查规格名称重复
        $specNames = array_column($specs, 'title');
        if (count($specNames) != count(array_unique($specNames))) {
            $duplicateNames = array_diff_assoc($specNames, array_unique($specNames));
            return "存在重复的规格名称：" . implode('、', array_unique($duplicateNames));
        }
        
        return null;
    }

    /* 获取商品规格相关数据
    * @param Product $productInfo 商品信息
    * @return array
    * @throws \think\db\exception\DataNotFoundException
    * @throws \think\db\exception\DbException
    * @throws \think\db\exception\ModelNotFoundException
    */
    public static function makeSkuTableData($productInfo)
    {
        if ($productInfo['is_attribute']) {
            $specArr = SkuSpec::where('product_id', $productInfo['id'])->select()->toArray();
            $specValueMap = Toolkit::setArray2Index(
                ProductSpecAttr::whereIn('spec_id', array_column($specArr, 'id'))->select()->toArray(),
                'spec_id'
            );
            $specData = [];
            foreach ($specArr as $item) {
                $specData[] = [
                    'id' => $item['id'],
                    'title' => $item['spec_name'],
                    'child' => array_map(function ($v) {
                        return [
                            'id' => $v['id'],
                            'title' => $v['attr_value'],
                            'checked' => $v['checked'] == 1,
                        ];
                    }, $specValueMap[$item['id']]),
                ];
            }

            $skuArr = ProductInventory::where('product_id', $productInfo['id'])->select()->toArray();
            $skuData = [];
            foreach ($skuArr as $item) {
                $skuData['skus[' . $item['data'] . '][id]'] = $item['id'];
                $skuData['skus[' . $item['data'] . '][image]'] = $item['image'];
                $skuData['skus[' . $item['data'] . '][price]'] = $item['price'];
                $skuData['skus[' . $item['data'] . '][sku]'] = $item['sn'];
//                $skuData['skus[' . $item['data'] . '][market_price]'] = $item['market_price'];
//                $skuData['skus[' . $item['data'] . '][cost_price]'] = $item['cost_price'];
                // if (!empty($shop_id)) {
                //     $skuData['skus[' . $item['data'] . '][stock]'] = ShopProductInventory::where([
                //         'shop_id' => $shop_id,
                //         'product_id' => $productInfo['id'],
                //         'inventory_id' => $item['id'],
                //     ])->value('stock') ?? 0;
                // } else {
                //     $skuData['skus[' . $item['data'] . '][stock]'] = $item['stock'];
                // }
                $skuData['skus[' . $item['data'] . '][checked]'] = $item['status'];
                
                // 如果是计量商品，添加重量单位字段
                if (($productInfo['product_type'] ?? self::PRODUCT_TYPE_NORMAL) == self::PRODUCT_TYPE_WEIGHT) {
                    $skuData['skus[' . $item['data'] . '][weight_unit]'] = $item['weight_unit'] ?? 'kg';
                }
            }
        } else {
            $specData = [];
            
            // 统一规格模式下的数据回显
            $inventory = ProductInventory::where('product_id', $productInfo['id'])->find();
            
            $skuData = [
                'price' => $productInfo['price'],
                // 'stock' => $productInfo['stock'],
                'sku' => $inventory ? $inventory['sn'] : '',
                'status' => $inventory ? $inventory['status'] : 1,
                'id' => $inventory ? $inventory['id'] : 0,
            ];
            
            // 如果是计量商品，回显重量单位
            if (($productInfo['product_type'] ?? self::PRODUCT_TYPE_NORMAL) == self::PRODUCT_TYPE_WEIGHT) {
                $skuData['weight_unit'] = $inventory ? ($inventory['weight_unit'] ?? 'kg') : 'kg';
            }
        }

        return ['specData' => $specData, 'skuData' => $skuData];
    }

}
