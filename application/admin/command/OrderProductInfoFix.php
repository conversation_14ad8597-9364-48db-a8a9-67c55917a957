<?php
namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\facade\Log;
use app\store\model\Order;
use app\store\model\OrderDetail;
use app\store\model\Product;
use app\store\model\ProductInventory;

class OrderProductInfoFix extends Command
{
    protected function configure()
    {
        $this->setName('order:product_info_fix')
            ->setDescription('重新处理所有订单的product_info字段');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始执行订单商品信息修复...');
        
        try {
            // 查询所有订单，可以根据需要添加条件限制
            $orders = Order::order('id', 'asc')->select();
            $total = count($orders);
            $success = 0;
            $failed = 0;
            
            $output->writeln("共找到 {$total} 个订单需要处理");
            
            foreach ($orders as $index => $order) {
                try {
                    // 获取订单详情
                    $orderDetails = OrderDetail::where('order_no', $order['order_no'])->select();
                    
                    if (empty($orderDetails)) {
                        $output->writeln("[失败] 订单 {$order['order_no']} 无详情数据");
                        $failed++;
                        continue;
                    }
                    
                    // 构建新的product_info
                    $product_info = $this->buildProductInfo($orderDetails);
                    
                    // 更新订单
                    Order::where('id', $order['id'])->update([
                        'product_info' => $product_info,
                        'update_time' => date('Y-m-d H:i:s')
                    ]);
                    
                    $success++;
                    
                    // 每处理50个订单输出一次进度
                    if (($index + 1) % 50 === 0 || $index === $total - 1) {
                        $output->writeln("已处理 " . ($index + 1) . " / {$total} 个订单");
                    }
                } catch (\Exception $e) {
                    $failed++;
                    $output->writeln("[错误] 处理订单 {$order['order_no']} 时出错: " . $e->getMessage());
                    Log::error("订单商品信息修复错误 - 订单号: {$order['order_no']} - " . $e->getMessage());
                }
            }
            
            $output->writeln("处理完成，成功：{$success}，失败：{$failed}");
            
        } catch (\Exception $e) {
            $output->writeln('执行过程中发生错误: ' . $e->getMessage());
            Log::error('订单商品信息修复命令执行错误: ' . $e->getMessage());
        }
    }
    
    /**
     * 根据订单详情构建商品信息
     * 
     * @param array $orderDetails 订单详情数组
     * @return string 格式化后的商品信息
     */
    private function buildProductInfo($orderDetails)
    {
        $product_info = '';
        
        foreach ($orderDetails as $detail) {
            // 解析商品和库存JSON数据
            $product_data = is_array($detail['product_json']) 
                ? $detail['product_json'] 
                : json_decode($detail['product_json'], true);
            
            $inventory_data = is_array($detail['inventory_json']) 
                ? $detail['inventory_json'] 
                : json_decode($detail['inventory_json'], true);
            
            if (empty($product_data) || empty($inventory_data)) {
                // 如果JSON数据有问题，尝试从数据库查询
                $product = Product::where('id', $detail['product_id'] ?? 0)->find();
                $inventory = ProductInventory::where('id', $detail['inventory_id'] ?? 0)->find();
                
                if ($product) {
                    $product_data = $product->toArray();
                }
                
                if ($inventory) {
                    $inventory_data = $inventory->toArray();
                }
                
                // 如果依然无法获取数据，则跳过
                if (empty($product_data) || empty($inventory_data)) {
                    continue;
                }
            }
            
            // 判断是否为计量商品（通过actual_weight字段判断）
            if (!empty($detail['actual_weight'])) {
                // 计量商品格式
                $product_info .= "{$product_data['title']}：{$detail['actual_weight']}kg*{$detail['weight_unit_price']}元/kg = " . round($detail['weight_unit_price'] * $detail['actual_weight'], 2) . "元<br>";
            } else {
                // 普通商品格式
                $product_info .= "{$product_data['title']} - {$inventory_data['title']}：{$detail['number']}件 * {$inventory_data['price']}元/件 = " . round($inventory_data['price'] * $detail['number'], 2) . "元<br>";
            }
        }
        
        return $product_info;
    }
} 