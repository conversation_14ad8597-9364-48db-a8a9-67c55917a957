<?php
/**
 * 定时取消订单任务
 * @date 2023-6-12
 * <AUTHOR>
 */

namespace app\admin\command;

use app\admin\model\ClientOrderInfo;
use app\admin\model\ClientYcardModel;
use app\admin\model\Order;
use app\admin\model\OrderDetail;
use app\api\model\CardUseLog;
use app\api\model\ShopProductInventory;
use app\api\model\ShopProductStockLog;
use app\api\model\InventoryLog;
use app\api\model\InventoryOrder;
use app\api\model\InventoryOrderDetail;
use app\api\model\OrderPayLog;
use app\api\model\PlatWechatSet;
use app\api\lib\Wechat;
use app\store\model\Ycard;
use think\console\Input;
use think\console\Output;
use think\console\Command;
use think\Db;
use think\facade\Log;

class OrderCancel extends Command
{
    protected function configure()
    {
        $this->setName('OrderCancel')->setDescription('Execute every five minutes,cancel not pay order and return balance to card.');
    }

    protected function execute(Input $input, Output $output)
    {
        ini_set("max_execution_time", 0); // 设置程序运行时间
        ini_set('memory_limit', '2048M'); // 设置内存使用情况
        // 查询20分钟之前 未支付 卡支付金额 进行退款操作(这里主要是退储值卡余额)
        $last_time = time() - (60 * 15);
        $where = [
            ['status', '=',  0],
            ['add_time', '<', date('Y-m-d H:i:s', $last_time)],
        ];
        $orders = Order::where($where)
            ->select();
        Log::info('order cancel start.');
        Log::info('total number of current orders:' . count($orders));
        if (count($orders) > 0) {
            try {
                Db::startTrans();
                foreach ($orders as $v) {

                    if ($v['pay_type'] == 1) {
                        // 微信支付 - 需要先查询支付状态
                        $paymentResult = $this->checkWechatPaymentStatus($v);
                        if ($paymentResult['is_paid']) {
                            // 支付成功，修改订单状态为100
                            Order::where(['id' => $v['id']])->update([
                                'status' => 100,
                                'pay_time' => date('Y-m-d H:i:s'),
                                'transaction_id' => $paymentResult['transaction_id'] ?? '',
                                'real_price' => $paymentResult['real_price'] ?? $v['need_pay_price']
                            ]);
                            Log::info('订单已支付，状态更新为100。订单号：' . $v['order_no']);
                        } else {
                            // 未支付，正常取消订单
                            Order::where(['id' => $v['id']])->update(['status' => -1]);
                            Log::info('微信支付订单未支付，已取消。订单号：' . $v['order_no']);
                            // 需要还原库存
                            $this->restoreOrderStock($v);
                        }
                    } else if ($v['pay_type'] == 2) {
                        // 储值卡支付
                        $logs = CardUseLog::where(['order_sn' => $v['order_no']])->select();
                        if (!empty($logs)) {
                            foreach ($logs as $log) {
                                $card = Ycard::where(['cardnum' => $log['cardnum']])->find();
                                if (!empty($card)) {
                                    // 退回余额
                                    Ycard::where(['id' => $card['id']])->update(['yu_money' => Db::raw('yu_money + '. $log['use_money'])]);
                                    // 记录修改状态
                                    CardUseLog::where(['id' => $log['id']])->update(['remark' => '超时未支付，系统已退回', 'status' => -1]);
                                    // 记录修改日志
                                    CardUseLog::insert([
                                        'basekeynum' => $card['clientkeynum'],
                                        'member_id' => $card['member_id'],
                                        'order_sn' => $v['order_no'],
                                        'cardnum' => $card['cardnum'],
                                        'yu_money' => $card['yu_money'] + $log['use_money'],
                                        'after_money' => $card['yu_money'],
                                        'use_money' => $log['use_money'],
                                        'status' => 2, //增加
                                        'add_time' => date('Y-m-d H:i:s'),
                                        'remark' => '超时未支付，系统已退回',

                                    ]);
                                }
                            }
                            Order::where(['id' => $v['id']])->update(['status' => -1]);
                        }
                        // 还原库存
                        $this->restoreOrderStock($v);

                    } else if ($v['pay_type'] == 3) {
                        // 组合支付
                        // 储值卡支付
                        $logs = CardUseLog::where(['order_sn' => $v['order_no']])->select();
                        if (!empty($logs)) {
                            foreach ($logs as $log) {
                                $card = Ycard::where(['cardnum' => $log['cardnum']])->find();
                                if (!empty($card)) {
                                    // 退回余额
                                    Ycard::where(['id' => $card['id']])->update(['yu_money' => Db::raw('yu_money + '. $log['use_money'])]);
                                    // 记录修改状态
                                    CardUseLog::where(['id' => $log['id']])->update(['remark' => '超时未支付，系统已退回', 'status' => -1]);
                                    // 记录修改日志
                                    CardUseLog::insert([
                                        'basekeynum' => $card['clientkeynum'],
                                        'member_id' => $card['member_id'],
                                        'order_sn' => $v['order_no'],
                                        'cardnum' => $card['cardnum'],
                                        'yu_money' => $card['yu_money'] + $log['use_money'],
                                        'after_money' => $card['yu_money'],
                                        'use_money' => $log['use_money'],
                                        'status' => 2, //增加
                                        'add_time' => date('Y-m-d H:i:s'),
                                        'remark' => '超时未支付，系统已退回',

                                    ]);
                                }
                            }
                            Order::where(['id' => $v['id']])->update(['status' => -1]);
                        }
                        // 还原库存
                        $this->restoreOrderStock($v);
                    } else {
                        Log::error('order cancel fail, unknow pay_type. order_id:'. $v['id']);
                        continue;  // 跳过此订单，不做任何处理
                    }
                }
                DB::commit();
                Log::info('order cancel success.');
            }catch (\Exception $e) {
                Db::rollback();
                Log::error('order cancel fail.error message:' . $e->getMessage());
                Log::error('file info:' . $e->getFile() . ' ' . $e->getLine() . $e->getCode());
                Log::error('fail trace:' . $e->getTraceAsString());
            }

        }
        $output->writeln("Order Cancel Done.");
    }

    /**
     * 检查微信支付状态
     * @param array $order 订单信息
     * @return array 支付状态信息
     */
    private function checkWechatPaymentStatus($order)
    {
        try {
            // 查询最近的支付日志
            $payLog = OrderPayLog::where(['order_no' => $order['order_no']])
                ->order('id desc')
                ->find();

            if (empty($payLog)) {
                Log::info('订单支付日志不存在，订单号：' . $order['order_no']);
                return ['is_paid' => false, 'message' => '支付日志不存在'];
            }

            // 如果日志已标记支付成功
            if ($payLog['is_pay'] == 1) {
                return [
                    'is_paid' => true,
                    'transaction_id' => $payLog['transaction_id'] ?? '',
                    'real_price' => $payLog['real_price'] ?? $order['need_pay_price'],
                    'message' => '支付日志已标记支付成功'
                ];
            }

            // 获取微信支付配置
            $wechat_config = PlatWechatSet::getInfoByKeyNum($order['clientkeynum']);
            if (empty($wechat_config)) {
                Log::error('微信支付配置不存在，订单号：' . $order['order_no']);
                return ['is_paid' => false, 'message' => '微信支付配置不存在'];
            }

            // 调用微信查询订单API
            $result = Wechat::queryOrderStatus($wechat_config, $payLog['id']);

            if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
                if ($result['trade_state'] === 'SUCCESS') {
                    // 支付成功，更新支付日志
                    OrderPayLog::where('id', $payLog['id'])->update([
                        'is_pay' => 1,
                        'pay_time' => date('Y-m-d H:i:s'),
                        'transaction_id' => $result['transaction_id'] ?? '',
                        'real_price' => ($result['total_fee'] ?? 0) / 100
                    ]);

                    Log::info('微信支付查询确认已支付，订单号：' . $order['order_no']);
                    return [
                        'is_paid' => true,
                        'transaction_id' => $result['transaction_id'] ?? '',
                        'real_price' => ($result['total_fee'] ?? 0) / 100,
                        'message' => '微信查询确认支付成功'
                    ];
                } else {
                    Log::info('微信支付查询未支付，trade_state：' . $result['trade_state'] . '，订单号：' . $order['order_no']);
                    return [
                        'is_paid' => false,
                        'message' => '微信查询未支付，状态：' . $result['trade_state']
                    ];
                }
            } else {
                Log::error('微信支付查询失败，订单号：' . $order['order_no'] . '，错误：' . ($result['return_msg'] ?? '未知错误'));
                return [
                    'is_paid' => false,
                    'message' => '微信查询失败：' . ($result['return_msg'] ?? '未知错误')
                ];
            }
        } catch (\Exception $e) {
            Log::error('检查微信支付状态异常，订单号：' . $order['order_no'] . '，错误：' . $e->getMessage());
            return [
                'is_paid' => false,
                'message' => '检查支付状态异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 还原订单库存
     * @param array $order 订单信息
     */
    private function restoreOrderStock($order)
    {
        // 查询详情 退回库存
        $order_detail = OrderDetail::where(['order_no' => $order['order_no']])->select();
        if (!empty($order_detail) && count($order_detail) > 0) {
            // 准备创建入库单的明细数据
            $inventory_details = [];

            foreach ($order_detail as $detail) {
                // 获取当前库存
                $stock = ShopProductInventory::where([
                    'product_id' => $detail['product_json']['id'],
                    'inventory_id' => $detail['inventory_json']['id'],
                    'shop_id' => $detail['shop_id'],
                ])->find();

                if (!$stock) {
                    Log::error('库存记录不存在，无法还原。订单号：' . $order['order_no'] . '，商品ID：' . $detail['product_json']['id']);
                    continue;
                }

                // 检查是否为计量商品（通过订单详情中的actual_weight字段判断）
                $isWeightProduct = !empty($detail['actual_weight']);

                if ($isWeightProduct) {
                    // 计量商品：还原重量库存
                    $beforeWeightStock = $stock['weight_stock'];
                    $changeWeight = $detail['actual_weight'];
                    $afterWeightStock = $beforeWeightStock + $changeWeight;

                    ShopProductInventory::where(['id' => $stock['id']])->update([
                        'weight_stock' => Db::raw('weight_stock + ' . $changeWeight)
                    ]);

                    // 记录库存变动日志
                    $inventory_log = [
                        'clientkeynum' => $order['clientkeynum'],
                        'shop_id' => $detail['shop_id'],
                        'product_id' => $detail['product_json']['id'],
                        'inventory_id' => $detail['inventory_json']['id'],
                        'before_quantity' => $beforeWeightStock,
                        'change_quantity' => $changeWeight,
                        'after_quantity' => $afterWeightStock,
                        'change_type' => 1, // 入库
                        'change_unit' => 'kg',
                        'related_no' => $order['order_no'],
                        'operator' => '系统',
                        'remark' => "订单取消重量入库：{$detail['product_json']['title']} - {$detail['inventory_json']['title']}，重量：{$changeWeight}kg",
                        'created_at' => date('Y-m-d H:i:s'),
                    ];

                    $inventoryLogModel = new InventoryLog();
                    $inventoryLogModel->add($inventory_log);

                    // 记录库存变动（旧方式，保留兼容）
                    ShopProductStockLog::create([
                        'order_no' => $order['order_no'],
                        'product_id' => $detail['product_json']['id'],
                        'inventory_id' => $detail['inventory_json']['id'],
                        'shop_id' => $detail['shop_id'],
                        'old_stock' => null, // 计量商品不使用数量库存
                        'new_stock' => null,
                        'old_weight_stock' => $beforeWeightStock,
                        'new_weight_stock' => $afterWeightStock,
                        'time' => date('Y-m-d H:i:s'),
                    ]);

                    // 准备入库单明细数据（计量商品）
                    $inventory_details[] = [
                        'product_id' => $detail['product_json']['id'],
                        'inventory_id' => $detail['inventory_json']['id'],
                        'quantity' => $changeWeight, // 计量商品使用重量作为数量
                        'price' => $detail['weight_unit_price'] ?? ($detail['price'] / $changeWeight),
                        'discount' => 0,
                        'amount' => $detail['price'],
                        'product' => $detail['product_json'],
                        'inventory' => $detail['inventory_json']
                    ];

                    Log::info("订单取消恢复重量库存：订单{$order['order_no']}，商品{$detail['product_json']['title']}，重量{$changeWeight}kg");
                } else {
                    // 普通商品：还原数量库存
                    $beforeStock = $stock['stock'];
                    $changeQuantity = $detail['number'];
                    $afterStock = $beforeStock + $changeQuantity;

                    ShopProductInventory::where(['id' => $stock['id']])->update([
                        'stock' => Db::raw('stock + ' . $changeQuantity)
                    ]);

                    // 记录库存变动日志
                    $inventory_log = [
                        'clientkeynum' => $order['clientkeynum'],
                        'shop_id' => $detail['shop_id'],
                        'product_id' => $detail['product_json']['id'],
                        'inventory_id' => $detail['inventory_json']['id'],
                        'before_quantity' => $beforeStock,
                        'change_quantity' => $changeQuantity,
                        'after_quantity' => $afterStock,
                        'change_type' => 1, // 入库
                        'change_unit' => 'pcs',
                        'related_no' => $order['order_no'],
                        'operator' => '系统',
                        'remark' => "订单取消入库：{$detail['product_json']['title']} - {$detail['inventory_json']['title']}，数量：{$changeQuantity}",
                        'created_at' => date('Y-m-d H:i:s'),
                    ];

                    $inventoryLogModel = new InventoryLog();
                    $inventoryLogModel->add($inventory_log);

                    // 记录库存变动（旧方式，保留兼容）
                    ShopProductStockLog::create([
                        'order_no' => $order['order_no'],
                        'product_id' => $detail['product_json']['id'],
                        'inventory_id' => $detail['inventory_json']['id'],
                        'shop_id' => $detail['shop_id'],
                        'old_stock' => $beforeStock,
                        'new_stock' => $afterStock,
                        'old_weight_stock' => null, // 普通商品不使用重量库存
                        'new_weight_stock' => null,
                        'time' => date('Y-m-d H:i:s'),
                    ]);

                    // 准备入库单明细数据（普通商品）
                    $inventory_details[] = [
                        'product_id' => $detail['product_json']['id'],
                        'inventory_id' => $detail['inventory_json']['id'],
                        'quantity' => $changeQuantity,
                        'price' => $detail['price'] / $changeQuantity,
                        'discount' => 0,
                        'amount' => $detail['price'],
                        'product' => $detail['product_json'],
                        'inventory' => $detail['inventory_json']
                    ];

                    Log::info("订单取消恢复库存：订单{$order['order_no']}，商品{$detail['product_json']['title']}，数量{$changeQuantity}");
                }
            }

            // 创建入库单
            if (!empty($inventory_details)) {
                $this->createInventoryInOrder($detail['shop_id'], $order['clientkeynum'], $order['order_no'], $inventory_details);
            }
        }
    }

    /**
     * 创建入库单及明细
     *
     * @param int $shop_id 商店ID
     * @param string $clientkeynum 客户端密钥
     * @param string $order_no 关联订单号
     * @param array $inventory_details 入库明细数组
     * @return bool 创建结果
     */
    private function createInventoryInOrder($shop_id, $clientkeynum, $order_no, $inventory_details)
    {
        if (empty($inventory_details)) {
            return false;
        }

        // 创建入库单主表数据
        $inventoryOrderData = [
            'order_no' => 'II' . date('YmdHis') . mt_rand(1000, 9999), // 自动生成入库单号
            'clientkeynum' => $clientkeynum,
            'shop_id' => $shop_id,
            'order_type' => 1, // 1-入库
            'business_type' => 4, // 4-其他入库
            'related_order_no' => $order_no, // 关联订单号
            'status' => 2, // 已审核状态
            'remark' => "订单取消自动入库，订单号：{$order_no}",
            'created_by' => '系统',
            'reviewed_by' => '系统',
            'reviewed_time' => date('Y-m-d H:i:s'),
            'completed_time' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
        ];

        try {
            // 开启事务
            Db::startTrans();

            // 创建入库单主表记录
            $inventoryOrder = new InventoryOrder();
            $inventoryOrderResult = $inventoryOrder->save($inventoryOrderData);

            if (!$inventoryOrderResult) {
                throw new \Exception('创建入库单失败');
            }

            $total_amount = 0;
            $details = [];

            // 准备所有入库单明细数据
            foreach ($inventory_details as $detail) {
                $product = $detail['product'];
                $inventory = $detail['inventory'];

                $detail_data = [
                    'order_id' => $inventoryOrder->id,
                    'order_no' => $inventoryOrderData['order_no'],
                    'order_type' => 1, // 1-入库
                    'product_id' => $detail['product_id'],
                    'inventory_id' => $detail['inventory_id'],
                    'quantity' => $detail['quantity'],
                    'price' => $detail['price'],
                    'discount' => $detail['discount'],
                    'amount' => $detail['amount'],
                    'shop_id' => $shop_id,
                    'remark' => "商品：{$product['title']} - {$inventory['title']}，数量：{$detail['quantity']}",
                    'created_at' => date('Y-m-d H:i:s'),
                    'clientkeynum' => $clientkeynum
                ];

                $details[] = $detail_data;
                $total_amount += $detail['amount'];
            }

            // 批量创建入库单明细
            $detailModel = new InventoryOrderDetail;
            $detailResult = $detailModel->saveAll($details);

            if (!$detailResult) {
                throw new \Exception('创建入库单明细失败');
            }

            // 更新入库单主表的总金额
            InventoryOrder::where('id', $inventoryOrder->id)->update([
                'total_amount' => $total_amount,
                'actual_amount' => $total_amount,
            ]);

            // 提交事务
            Db::commit();

            Log::info('订单取消自动生成入库单成功，单号：' . $inventoryOrderData['order_no']);
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            Log::error('创建订单取消入库单异常：'. $e->getMessage());
            return false;
        }
    }
}
