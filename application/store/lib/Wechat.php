<?php
/**
 * Create by AI
 * Date:2024/10/25
 */

namespace app\store\lib;

use EasyWeChat\Factory;
use think\facade\App;
use think\facade\Log;

class Wechat
{
    /**
     * 获取微信支付实例
     *
     * @param array $config 微信支付配置信息
     * @return \EasyWeChat\Payment\Application 微信支付应用实例
     */
    public static function getPaymentWechatInstance($config)
    {
         // 处理 cret_path 和 pem_path 为文件
        $cert_path = self::makeCertPath($config);
        $wechatConfig = [
            'app_id' => $config['appid'],
            'secret' => $config['appsecret'],
            'mch_id' => $config['mchid'],
            'key' => $config['key'],
            'cert_path' => $cert_path['cert_path'] ?? '', // 证书路径
            'key_path' => $cert_path['key_path'] ?? '',   // 证书密钥路径
            
            // 下面为可选项
            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',

            'log' => [
                'level' => 'debug',
                'file' => App::getRuntimePath() . '/log/wechat.log',
            ],
            'notify_url' => request()->domain() . '/api.php/store/notify/wechat_notify', // 支付结果通知回调
        ];

        $app = Factory::payment($wechatConfig);
        return $app;
    }

    private static function makeCertPath($config)
    {
        // 查询根目录cert文件夹的商户号文件夹
        $cert_path = App::getRootPath() . 'cert/' . $config['mchid'];
        // 如果存在文件夹
        if (!is_dir($cert_path)) {
            mkdir($cert_path, 0777, true);
        }
        $pem_path = $cert_path . '/apiclient_cert.pem';
        $key_path = $cert_path . '/apiclient_key.pem';
        // 如果文件不存在，则创建文件
        if (!file_exists($pem_path)) {
            file_put_contents($pem_path, $config['apiclient_cert'], FILE_APPEND);
        }
        if (!file_exists($key_path)) {
            file_put_contents($key_path, $config['apiclient_key'], FILE_APPEND);
        }
        return [
            'cert_path' => $pem_path,
            'key_path' => $key_path,
        ];
    }
    
    /**
     * 生成微信支付Native支付二维码链接
     *
     * @param array $wechat_config 微信配置
     * @param string $out_trade_no 商户订单号
     * @param float $total_fee 支付金额（元）
     * @param string $body 商品描述
     * @param string $product_id 商品ID
     * @return array 包含预支付ID和二维码URL的数组
     */
    public static function createNativePayment($wechat_config, $out_trade_no, $total_fee, $body, $product_id = '')
    {
        try {
            $app = self::getPaymentWechatInstance($wechat_config);
            
            // 订单信息
            $order = [
                'body' => $body,
                'out_trade_no' => $out_trade_no,
                'total_fee' => intval($total_fee * 100), // 单位：分
                'trade_type' => 'NATIVE', // Native支付
                'product_id' => $product_id ?: $out_trade_no, // 如果没有产品ID，使用订单号
            ];
            
            $result = $app->order->unify($order);
            
            Log::info('Native支付创建结果: ' . json_encode($result));
            
            if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
                return [
                    'prepay_id' => $result['prepay_id'],
                    'code_url' => $result['code_url'], // 二维码链接
                    'result' => $result
                ];
            } else {
                $error_msg = isset($result['err_code_des']) ? $result['err_code_des'] : (isset($result['return_msg']) ? $result['return_msg'] : '创建支付失败');
                Log::error('创建Native支付失败: ' . $error_msg);
                return ['error' => $error_msg];
            }
        } catch (\Exception $e) {
            Log::error('创建Native支付异常: ' . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * 查询订单状态
     * 
     * @param array $wechat_config 微信配置
     * @param string $out_trade_no 商户订单号
     * @return array 订单查询结果
     */
    public static function queryOrder($wechat_config, $out_trade_no)
    {
        try {
            $app = self::getPaymentWechatInstance($wechat_config);
            $result = $app->order->queryByOutTradeNumber($out_trade_no);
            Log::info('订单查询结果: ' . json_encode($result));
            return $result;
        } catch (\Exception $e) {
            Log::error('查询订单异常: ' . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * 关闭订单
     * 
     * @param array $wechat_config 微信配置
     * @param string $out_trade_no 商户订单号
     * @return array 关闭订单结果
     */
    public static function closeOrder($wechat_config, $out_trade_no)
    {
        try {
            $app = self::getPaymentWechatInstance($wechat_config);
            $result = $app->order->close($out_trade_no);
            Log::info('关闭订单结果: ' . json_encode($result));
            return $result;
        } catch (\Exception $e) {
            Log::error('关闭订单异常: ' . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * 处理微信退款
     * 
     * @param array $wechat_config 微信配置
     * @param string $out_trade_no 商户订单号
     * @param string $out_refund_no 商户退款单号
     * @param int $total_fee 订单总金额（分）
     * @param int $refund_fee 退款金额（分）
     * @return array 退款结果
     */
    public static function handleRefund($wechat_config, $out_trade_no, $out_refund_no, $total_fee, $refund_fee)
    {
        try {
            $app = self::getPaymentWechatInstance($wechat_config);
            
            $refund_data = [
                'out_trade_no' => $out_trade_no,
                'out_refund_no' => $out_refund_no,
                'total_fee' => $total_fee,
                'refund_fee' => $refund_fee,
                'refund_desc' => '订单取消退款', // 退款原因
            ];
            
            $result = $app->refund->byOutTradeNumber(
                $out_trade_no,
                $out_refund_no,
                $total_fee,
                $refund_fee,
                [
                    'refund_desc' => '订单取消退款'
                ]
            );
            
            Log::info('微信退款结果: ' . json_encode($result));
            return $result;
        } catch (\Exception $e) {
            Log::error('微信退款异常: ' . $e->getMessage());
            return [
                'return_code' => 'FAIL',
                'return_msg' => $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 微信付款码支付（刷卡支付）
     * 用户出示付款码，商家扫码收款
     * 
     * @param array $wechat_config 微信配置
     * @param string $out_trade_no 商户订单号
     * @param float $total_fee 支付金额（元）
     * @param string $body 商品描述
     * @param string $auth_code 付款码（18位数字）
     * @return array 支付结果
     */
    public static function createMicropay($wechat_config, $out_trade_no, $total_fee, $body, $auth_code)
    {
        try {
            $app = self::getPaymentWechatInstance($wechat_config);
            
            // 验证付款码格式（18位数字）
            if (!preg_match('/^\d{18}$/', $auth_code)) {
                return ['error' => '付款码格式错误，应为18位数字'];
            }
            
            // 订单信息
            $order_data = [
                'body' => $body,
                'out_trade_no' => $out_trade_no,
                // 这个地方  会吧 129.7 转换为 12969 分
                'total_fee' => intval(round($total_fee * 100)), // 单位：分
                'spbill_create_ip' => request()->ip() ?: '127.0.0.1', // 客户端IP
                'auth_code' => $auth_code, // 付款码
            ];

            Log::info('付款码支付订单信息: ' . json_encode($order_data));
            
            // 使用付款码支付接口 - 根据EasyWeChat文档，直接使用 $app->pay() 方法
            $result = $app->pay($order_data);
            
            Log::info('付款码支付结果: ' . json_encode($result));
            
            // 支付成功
            if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
                return [
                    'status' => 'SUCCESS',
                    'transaction_id' => $result['transaction_id'],
                    'out_trade_no' => $result['out_trade_no'],
                    'total_fee' => $result['total_fee'],
                    'time_end' => $result['time_end'],
                    'result' => $result
                ];
            }
            
            // 需要查询订单状态（USERPAYING：用户支付中，需要输入密码）
            if ($result['return_code'] === 'SUCCESS' && 
                isset($result['err_code']) && $result['err_code'] === 'USERPAYING') {
                return [
                    'status' => 'USERPAYING',
                    'message' => '用户支付中，需要输入密码',
                    'out_trade_no' => $out_trade_no,
                    'result' => $result
                ];
            }
            
            // 系统错误
            if ($result['return_code'] === 'SUCCESS' && 
                isset($result['err_code']) && $result['err_code'] === 'SYSTEMERROR') {
                return [
                    'status' => 'SYSTEMERROR',
                    'message' => '系统错误，建议重新查询',
                    'out_trade_no' => $out_trade_no,
                    'result' => $result
                ];
            }
            
            // 其他错误情况
            $error_msg = isset($result['err_code_des']) ? $result['err_code_des'] : 
                        (isset($result['return_msg']) ? $result['return_msg'] : '付款码支付失败');
            
            Log::error('付款码支付失败: ' . $error_msg);
            return [
                'status' => 'FAIL',
                'error' => $error_msg,
                'result' => $result
            ];
            
        } catch (\Exception $e) {
            Log::error('付款码支付异常: ' . $e->getMessage());
            return [
                'status' => 'FAIL',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 撤销付款码支付订单
     * 当付款码支付失败或超时时调用
     * 
     * @param array $wechat_config 微信配置
     * @param string $out_trade_no 商户订单号
     * @return array 撤销结果
     */
    public static function reverseOrder($wechat_config, $out_trade_no)
    {
        try {
            $app = self::getPaymentWechatInstance($wechat_config);
            // 根据EasyWeChat文档，撤销订单使用 $app->reverse() 方法
            $result = $app->reverse($out_trade_no);
            
            Log::info('撤销订单结果: ' . json_encode($result));
            return $result;
        } catch (\Exception $e) {
            Log::error('撤销订单异常: ' . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }
}
