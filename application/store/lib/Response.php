<?php
/**
 * 通用响应类
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/9/11 上午11:30
 */

namespace app\store\lib;

class Response
{
    CONST SUCCESS = 0;
    CONST ERROR = 500;
    CONST NOT_LOGIN = 401;
    CONST NOT_ACCOUNT_LOGIN = 4001;
    CONST NOT_FOUND = 404;
    CONST SIGN_FAIL = 410;
    CONST WECHAT_CONFIG_NOT_EXISTS = 1001;
    CONST WECHAT_LOGIN_FAIL = 1002;
    CONST MEMBER_REGISTER_FAIL = 1003;
    CONST WECHAT_BIND_PHONE_FAIL = 1004;

    public static function json($code, $message = '', $data = [])
    {
        $result = [
            'code' => $code,
            'message' => empty($message) ? self::getMessage($code) : $message,
            'data' => $data
        ];

        return json($result);
    }


    public static function getMessage($code)
    {
        $arr = [
            self::SUCCESS => '请求成功',
            self::ERROR => '请求失败',
            self::NOT_LOGIN => '当前未登录，请登录',
            self::NOT_FOUND => '未查询到信息',
            self::SIGN_FAIL => '签名错误',
            self::WECHAT_CONFIG_NOT_EXISTS => 'Wechat配置查询失败！',
            self::WECHAT_LOGIN_FAIL => '微信登录失败！！',
            self::MEMBER_REGISTER_FAIL => '用户注册失败，请刷新重试',
            self::WECHAT_BIND_PHONE_FAIL => '获取用户手机号失败'
        ];
        return $arr[$code] ?? '系统异常';
    }

}
