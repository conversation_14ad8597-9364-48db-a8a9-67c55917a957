<?php
namespace app\store\model;

use think\Model;
use think\Db;
use think\facade\Log;

/**
 * 库存单主表模型 - Store版本
 * Class InventoryOrder
 * @package app\store\model
 */
class InventoryOrder extends Model
{
    protected $name = 'inventory_order';

    /**
     * 创建销售出库单
     * @param array $data 出库单数据
     * @return array|false 返回包含出库单ID和编号的数组或false
     */
    public function createSalesOutOrder($data)
    {
        // 生成出库单编号
        $order_no = 'SO' . date('YmdHis') . mt_rand(1000, 9999);
        $data['order_no'] = $order_no;
        $data['order_type'] = 2; // 2-出库
        $data['business_type'] = 1; // 1-销售出库
        $data['status'] = 3; // 已完成状态
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['reviewed_time'] = date('Y-m-d H:i:s');
        $data['completed_time'] = date('Y-m-d H:i:s');

        try {
            $id = $this->insertGetId($data);
            return $id ? ['id' => $id, 'order_no' => $order_no] : false;
        } catch (\Exception $e) {
            Log::error('创建销售出库单失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新出库单总金额
     * @param int $id 出库单ID
     * @param float $total_amount 总金额
     * @return bool
     */
    public function updateTotalAmount($id, $total_amount)
    {
        return $this->where('id', $id)->update([
            'total_amount' => $total_amount,
            'actual_amount' => $total_amount
        ]) !== false;
    }
} 