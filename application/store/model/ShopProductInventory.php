<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/9/13 下午5:09
 */

namespace app\store\model;

use app\api\model\BaseModel;
use think\Db;
use think\Exception;

class ShopProductInventory extends BaseModel
{
    protected $table = 'shop_product_inventory';

    protected $pk = 'id';

    public function inventory()
    {
        return $this->hasOne('ProductInventory', 'id', 'inventory_id');
    }

    /**
     * 扣减重量库存
     */
    public function reduceWeightStock($weight, $remark = '销售出库')
    {
        if ($this->weight_stock < $weight) {
            throw new Exception('重量库存不足');
        }
        
        $oldStock = $this->weight_stock;
        $this->weight_stock -= $weight;
        $this->save();
        
        // 记录库存变动日志
        $this->logWeightStockChange($oldStock, $this->weight_stock, -$weight, $remark);
        
        return true;
    }
    
    /**
     * 增加重量库存
     */
    public function addWeightStock($weight, $remark = '入库')
    {
        $oldStock = $this->weight_stock;
        $this->weight_stock += $weight;
        $this->save();
        
        // 记录库存变动日志
        $this->logWeightStockChange($oldStock, $this->weight_stock, $weight, $remark);
        
        return true;
    }
    
    /**
     * 记录重量库存变动日志
     */
    private function logWeightStockChange($oldStock, $newStock, $change, $remark)
    {
        $logData = [
            'clientkeynum' => $this->clientkeynum,
            'shop_id' => $this->shop_id,
            'inventory_id' => $this->inventory_id,
            'old_weight_stock' => $oldStock,
            'new_weight_stock' => $newStock,
            'weight_change' => $change,
            'change_type' => 2, // 重量变动
            'remark' => $remark,
            'time' => date('Y-m-d H:i:s')
        ];
        
        Db::name('shop_product_stock_log')->insert($logData);
    }
}
