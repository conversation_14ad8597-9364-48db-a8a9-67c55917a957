<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/10/12 15:16
 */

namespace app\store\model;

use think\Model;

class Order extends Model
{
    protected $table = 'order';
    protected $pk = 'id';

    public static function makeOrderNo()
    {
        return 'D' . date('YmdHi') . rand(100000, 999999);
    }

    public function orderDetail(){
        return $this->hasMany('OrderDetail','order_no','order_no');
    }

    public function getAddressJsonAttr($value)
    {
        if (!empty($value)) {
            return json_decode($value, true);
        }
    }

    public function user(){
        return $this->belongsTo('User','user_id','id');
    }

    public function shop()
    {
        return $this->belongsTo('Shop','shop_id','id');
    }

}
