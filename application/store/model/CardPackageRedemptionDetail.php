<?php
/**
 * 套餐卡兑换明细模型
 * Create by Augment Agent
 * Date: 2024-12-19
 */

namespace app\store\model;

use think\Model;

class CardPackageRedemptionDetail extends Model
{
    protected $table = 'card_package_redemption_detail';
    protected $pk = 'id';
    
    /**
     * 关联兑换主表
     */
    public function redemption()
    {
        return $this->belongsTo('CardPackageRedemption', 'redemption_id', 'id');
    }
    
    /**
     * 关联套餐
     */
    public function package()
    {
        return $this->belongsTo('CardPackage', 'package_id', 'id');
    }
    
    /**
     * 获取兑换明细
     */
    public function getRedemptionDetails($redemptionId, $clientkeynum)
    {
        return $this->with(['package'])
            ->where('redemption_id', $redemptionId)
            ->where('clientkeynum', $clientkeynum)
            ->select();
    }
} 