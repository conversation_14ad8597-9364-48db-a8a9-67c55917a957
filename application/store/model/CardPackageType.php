<?php
/**
 * 套餐卡类型模型
 * Create by Augment Agent
 * Date: 2024-12-19
 */

namespace app\store\model;

use think\Model;

class CardPackageType extends Model
{
    protected $table = 'card_package_type';
    protected $pk = 'id';
    
    /**
     * 获取启用的卡型列表
     */
    public function getEnabledTypes($clientkeynum)
    {
        return $this->where('clientkeynum', $clientkeynum)
            ->where('status', 1)
            ->select();
    }
    
    /**
     * 关联套餐
     */
    public function packages()
    {
        return $this->belongsToMany('CardPackage', 'card_type_package_relation', 'package_id', 'type_id');
    }
} 