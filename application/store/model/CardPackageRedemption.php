<?php
/**
 * 套餐卡兑换主表模型
 * Create by Augment Agent
 * Date: 2024-12-19
 */

namespace app\store\model;

use think\Model;

class CardPackageRedemption extends Model
{
    protected $table = 'card_package_redemption';
    protected $pk = 'id';
    
    // 兑换状态：0-已取消，1-已兑换，2-已发货，100-已完成
    const STATUS_CANCELED = 0;
    const STATUS_REDEEMED = 1;
    const STATUS_SHIPPED = 2;
    const STATUS_COMPLETED = 100;
    
    // 兑换类型：1-到店兑换，2-快递配送
    const TYPE_STORE = 1;
    const TYPE_EXPRESS = 2;
    
    /**
     * 关联卡
     */
    public function card()
    {
        return $this->belongsTo('Card', 'card_id', 'id');
    }
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id');
    }
    
    /**
     * 关联门店
     */
    public function shop()
    {
        return $this->belongsTo('Shop', 'shop_id', 'id');
    }
    
    /**
     * 关联兑换明细
     */
    public function details()
    {
        return $this->hasMany('CardPackageRedemptionDetail', 'redemption_id', 'id');
    }
    
    /**
     * 关联兑换商品
     */
    public function products()
    {
        return $this->hasMany('CardPackageRedemptionProduct', 'redemption_id', 'id');
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusText($value)
    {
        $statusArr = [
            self::STATUS_CANCELED => '已取消',
            self::STATUS_REDEEMED => '已兑换',
            self::STATUS_SHIPPED => '已发货',
            self::STATUS_COMPLETED => '已完成'
        ];
        
        return isset($statusArr[$value]) ? $statusArr[$value] : '未知状态';
    }
    
    /**
     * 获取类型文本
     */
    public function getTypeText($value)
    {
        $typeArr = [
            self::TYPE_STORE => '到店兑换',
            self::TYPE_EXPRESS => '快递配送'
        ];
        
        return isset($typeArr[$value]) ? $typeArr[$value] : '未知类型';
    }
} 