<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/9/11 下午5:37
 */

namespace app\store\model;

use app\api\lib\Toolkit;
use app\api\model\BaseModel;
use app\api\model\ProductInventory;
use app\api\model\ProductSpecAttr;
use app\api\model\ShopProductInventory;
use app\api\model\SkuSpec;

class Product extends BaseModel
{
    protected $table = 'products';

    protected $pk = 'id';

    const STATUS_FORBIDDEN = 0;
    const STATUS_NORMAL = 1;

    const UNIFIED_SPECIFICATION = 0;
    const MULTIPLE_SPECIFICATIONS = 1;

    public static function statusOptions()
    {
        return [
            self::STATUS_FORBIDDEN => '下架',
            self::STATUS_NORMAL => '上架',
        ];
    }

    public static function isAttributeOptions()
    {
        return [
            self::UNIFIED_SPECIFICATION => '统一规格',
            self::MULTIPLE_SPECIFICATIONS => '多规格',
        ];
    }

    public function inventories()
    {
        return $this->hasMany('ProductInventory', 'product_id', 'id');
    }

    /**
     * 获取商品规格相关数据
     * @param Product $productInfo 商品信息
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function makeSkuTableData($productInfo, $shop_id = null)
    {
        if ($productInfo['is_attribute']) {
            $specArr = SkuSpec::where('product_id', $productInfo['id'])->select()->toArray();
            $specValueMap = Toolkit::setArray2Index(
                ProductSpecAttr::whereIn('spec_id', array_column($specArr, 'id'))->select()->toArray(),
                'spec_id'
            );
            $specData = [];
            foreach ($specArr as $item) {
                $specData[] = [
                    'id' => $item['id'],
                    'title' => $item['spec_name'],
                    'child' => array_map(function ($v) {
                        return [
                            'id' => $v['id'],
                            'title' => $v['attr_value'],
                            'checked' => $v['is_checked'] == 1,
                        ];
                    }, $specValueMap[$item['id']]),
                ];
            }

            $skuArr = ProductInventory::where('product_id', $productInfo['id'])->select()->toArray();
            $skuData = [];
            foreach ($skuArr as $item) {
                $skuData['skus[' . $item['data'] . '][picture]'] = $item['picture'];
                $skuData['skus[' . $item['data'] . '][price]'] = $item['price'];
//                $skuData['skus[' . $item['data'] . '][market_price]'] = $item['market_price'];
//                $skuData['skus[' . $item['data'] . '][cost_price]'] = $item['cost_price'];
                if (!empty($shop_id)) {
                    $skuData['skus[' . $item['data'] . '][stock]'] = ShopProductInventory::where([
                        'shop_id' => $shop_id,
                        'product_id' => $productInfo['id'],
                        'inventory_id' => $item['id'],
                    ])->value('stock') ?? 0;
                } else {
                    $skuData['skus[' . $item['data'] . '][stock]'] = $item['stock'];
                }
                $skuData['skus[' . $item['data'] . '][status]'] = $item['status'];
            }
        } else {
            $specData = [];
            $skuData = [
                'price' => $productInfo['price'],
//                'market_price' => $productInfo['market_price'],
//                'cost_price' => $productInfo['cost_price'],
                'stock' => $productInfo['stock'],
            ];
        }

        return ['specData' => $specData, 'skuData' => $skuData];
    }
}
