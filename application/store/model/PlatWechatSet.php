<?php
/**
 * Create by AI
 * Date:2024/10/25
 */

namespace app\store\model;

use think\Model;

/**
 * 平台微信设置模型
 */
class PlatWechatSet extends Model
{
    protected $table = 'plat_wechat_set';
    
    /**
     * 根据客户密钥获取微信配置信息
     *
     * @param string $clientkeynum 客户密钥
     * @return array|null 微信配置信息
     */
    public static function getInfoByKeyNum($clientkeynum)
    {
        $config = self::where('basekeynum', $clientkeynum)->find();
        return $config ? $config->toArray() : null;
    }
} 