<?php
/**
 * 计量商品销售记录模型
 * Create by AI Assistant
 * Date: 2024/12/19
 */

namespace app\store\model;

use app\api\model\BaseModel;

class WeightProductSales extends BaseModel
{
    protected $table = 'weight_product_sales';
    
    protected $pk = 'id';
    
    /**
     * 记录计量商品销售
     */
    public static function recordSale($data)
    {
        $saleData = [
            'clientkeynum' => $data['clientkeynum'],
            'shop_id' => $data['shop_id'],
            'product_id' => $data['product_id'],
            'inventory_id' => $data['inventory_id'],
            'weight' => $data['weight'],
            'total_price' => $data['total_price'],
            'unit_price' => $data['unit_price'],
            'order_no' => $data['order_no'] ?? null,
            'sale_time' => date('Y-m-d H:i:s')
        ];
        
        return self::create($saleData);
    }
    
    /**
     * 获取门店计量商品销售统计
     */
    public static function getShopSalesStats($shopId, $startDate = null, $endDate = null)
    {
        $query = self::where('shop_id', $shopId);
        
        if ($startDate) {
            $query->where('sale_time', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->where('sale_time', '<=', $endDate);
        }
        
        return $query->field('product_id, SUM(weight) as total_weight, SUM(total_price) as total_amount, COUNT(*) as sale_count')
                    ->group('product_id')
                    ->select();
    }
    
    /**
     * 获取商品销售记录
     */
    public static function getProductSalesHistory($productId, $shopId = null, $limit = 50)
    {
        $query = self::where('product_id', $productId);
        
        if ($shopId) {
            $query->where('shop_id', $shopId);
        }
        
        return $query->order('sale_time desc')
                    ->limit($limit)
                    ->select();
    }
    
    /**
     * 获取订单关联的计量商品销售记录
     */
    public static function getOrderWeightProducts($orderNo)
    {
        return self::where('order_no', $orderNo)
                  ->select();
    }
} 