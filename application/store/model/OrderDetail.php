<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/10/12 15:16
 */

namespace app\store\model;

use think\Model;

class OrderDetail extends Model
{
    protected $table = 'order_detail';
    protected $pk = 'id';

    public function getProductJsonAttr($value)
    {
        if (!empty($value) && is_string($value)) {
            return json_decode($value, true);
        }
        return $value;
    }

    public function getInventoryJsonAttr($value)
    {
        if (!empty($value) && is_string($value)) {
            return json_decode($value, true);
        }
        return $value;
    }

}
