<?php
/**
 * 套餐卡模型
 * Create by Augment Agent
 * Date: 2024-12-19
 */

namespace app\store\model;

use think\Model;

class Card extends Model
{
    protected $table = 'card';
    protected $pk = 'id';
    
    // 卡状态：0-待销售，1-已销售，2-已兑换，3-已开卡，4-已关卡，5-已废卡，-1-已过期
    const STATUS_PENDING_SALE = 0;
    const STATUS_SOLD = 1;
    const STATUS_REDEEMED = 2;
    const STATUS_OPENED = 3;
    const STATUS_CLOSED = 4;
    const STATUS_WASTED = 5;
    const STATUS_EXPIRED = -1;

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = $data['status'];
        $statusText = '';
        
        switch ($status) {
            case self::STATUS_PENDING_SALE:
                $statusText = '待销售';
                break;
            case self::STATUS_SOLD:
                $statusText = '已销售';
                break;
            case self::STATUS_REDEEMED:
                $statusText = '已兑换';
                break;
            case self::STATUS_OPENED:
                $statusText = '已开卡';
                break;
            case self::STATUS_CLOSED:
                $statusText = '已关卡';
                break;
            case self::STATUS_WASTED:
                $statusText = '已废卡';
                break;
            case self::STATUS_EXPIRED:
                $statusText = '已过期';
                break;
            default:
                $statusText = '未知状态';
                break;
        }
        
        return $statusText;
    }
    
    /**
     * 关联卡型
     */
    public function cardType()
    {
        return $this->belongsTo('CardPackageType', 'type_id', 'id');
    }
    
    /**
     * 关联兑换记录
     */
    public function redemptions()
    {
        return $this->hasMany('CardPackageRedemption', 'card_id', 'id');
    }
    
    /**
     * 根据卡号获取卡信息
     */
    public function getCardByCardNo($cardNo, $clientkeynum)
    {
        return $this->where('card_no', $cardNo)
            ->where('clientkeynum', $clientkeynum)
            ->find();
    }
    
    /**
     * 更新卡状态
     */
    public function updateCardStatus($id, $status, $operatorId, $operatorName, $reason = '', $remark = '', $clientkeynum = '')
    {
        // 获取当前卡信息
        $card = $this->where('id', $id)->find();
        if (empty($card)) {
            return false;
        }
        
        $oldStatus = $card['status'];
        
        // 更新卡状态
        $result = $this->where('id', $id)->update([
            'status' => $status,
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        if ($result) {
            // 记录状态变更日志
            $statusRecord = new CardStatusRecord();
            $statusRecord->save([
                'clientkeynum' => $clientkeynum ?: $card['clientkeynum'],
                'card_id' => $id,
                'old_status' => $oldStatus,
                'new_status' => $status,
                'operator_id' => $operatorId,
                'operator_name' => $operatorName,
                'operation_time' => date('Y-m-d H:i:s'),
                'reason' => $reason,
                'remark' => $remark,
                'add_time' => date('Y-m-d H:i:s')
            ]);
        }
        
        return $result;
    }
} 