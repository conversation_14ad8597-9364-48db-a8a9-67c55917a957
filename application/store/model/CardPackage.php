<?php
/**
 * 套餐模型
 * Create by Augment Agent
 * Date: 2024-12-19
 */

namespace app\store\model;

use think\Model;

class CardPackage extends Model
{
    protected $table = 'card_package';
    protected $pk = 'id';
    
    /**
     * 获取启用的套餐列表
     */
    public function getEnabledPackages($clientkeynum)
    {
        return $this->where('clientkeynum', $clientkeynum)
            ->where('status', 1)
            ->select();
    }
    
    /**
     * 关联卡型
     */
    public function cardTypes()
    {
        return $this->belongsToMany('CardPackageType', 'card_type_package_relation', 'type_id', 'package_id');
    }
    
    /**
     * 关联套餐商品
     */
    public function products()
    {
        return $this->hasMany('CardPackageProduct', 'package_id', 'id');
    }
} 