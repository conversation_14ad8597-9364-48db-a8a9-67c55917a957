<?php

namespace app\store\model;

use think\Model;
use think\Db;

/**
 * Store模块 - 门店打印机配置模型
 */
class YlyShopPrinterConfig extends Model
{
    // 表名
    protected $table = 'yly_shop_printer_config';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 根据门店ID获取打印机配置
     * @param int $shopId
     * @param string $clientkeynum
     * @return array|null
     */
    public static function getByShop($shopId, $clientkeynum)
    {
        return self::where([
            'shop_id' => $shopId,
            'clientkeynum' => $clientkeynum,
            'status' => 1
        ])->find();
    }
    
    /**
     * 保存或更新门店打印机配置
     * @param array $data
     * @return bool
     */
    public static function saveConfig($data)
    {
        // 检查是否已存在配置
        $exists = self::where([
            'shop_id' => $data['shop_id'],
            'clientkeynum' => $data['clientkeynum']
        ])->find();
        
        if ($exists) {
            // 更新现有配置
            return self::where('id', $exists['id'])->update($data);
        } else {
            // 创建新配置
            return self::create($data);
        }
    }
    
    /**
     * 更新打印统计
     * @param int $shopId
     * @param string $clientkeynum
     * @return bool
     */
    public static function updatePrintCount($shopId, $clientkeynum)
    {
        return self::where([
            'shop_id' => $shopId,
            'clientkeynum' => $clientkeynum
        ])->update([
            'print_count' => Db::raw('print_count + 1'),
            'last_print_time' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 获取门店打印统计
     * @param int $shopId
     * @param string $clientkeynum
     * @return array
     */
    public static function getShopStatistics($shopId, $clientkeynum)
    {
        $config = self::getByShop($shopId, $clientkeynum);
        if (!$config) {
            return [
                'configured' => false,
                'auto_print' => false,
                'total_prints' => 0,
                'last_print_time' => null
            ];
        }
        
        return [
            'configured' => true,
            'auto_print' => $config['auto_print'] == 1,
            'total_prints' => $config['print_count'],
            'last_print_time' => $config['last_print_time'],
            'printer_name' => $config['printer_name']
        ];
    }
    
    /**
     * 验证打印机配置数据
     * @param array $data
     * @return array
     */
    public static function validateConfig($data)
    {
        $errors = [];
        
        if (empty($data['printer_sn'])) {
            $errors[] = '打印机序列号不能为空';
        }
        
        if (empty($data['printer_key'])) {
            $errors[] = '打印机密钥不能为空';
        }
        
        if (!empty($data['print_copies']) && ($data['print_copies'] < 1 || $data['print_copies'] > 5)) {
            $errors[] = '打印份数必须在1-5之间';
        }
        
        return $errors;
    }
} 