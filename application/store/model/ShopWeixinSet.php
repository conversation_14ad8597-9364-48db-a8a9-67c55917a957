<?php
/**
 * 客户微信配置模型
 * @date 2023-5-9
 * <AUTHOR>
 */
namespace app\store\model;

use think\Model;

class ShopWeixinSet extends Model
{
    protected $pk = 'id';

    protected $table = 'shop_wechat_set';

    /**
     * 通过basekeynum获取客户平台微信配置
     * @param $baseKeyNum
     * @return array|bool|\PDOStatement|string|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getConfigByBaseKeyNum($baseKeyNum)
    {
        $where = ['basekeynum' => $baseKeyNum];
        $config = self::where($where)->find();
        return $config;
    }
}
