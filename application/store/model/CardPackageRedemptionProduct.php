<?php
/**
 * 套餐卡兑换商品模型
 * Create by Augment Agent
 * Date: 2024-12-19
 */

namespace app\store\model;

use think\Model;

class CardPackageRedemptionProduct extends Model
{
    protected $table = 'card_package_redemption_product';
    protected $pk = 'id';
    
    /**
     * 关联兑换主表
     */
    public function redemption()
    {
        return $this->belongsTo('CardPackageRedemption', 'redemption_id', 'id');
    }
    
    /**
     * 关联商品库存
     */
    public function productInventory()
    {
        return $this->belongsTo('ProductInventory', 'product_inventory_id', 'id');
    }
    
    /**
     * 获取兑换商品
     */
    public function getRedemptionProducts($redemptionId, $clientkeynum)
    {
        return $this->with(['productInventory'])
            ->where('redemption_id', $redemptionId)
            ->where('clientkeynum', $clientkeynum)
            ->select();
    }
} 