<?php
/**
 * 卡状态记录模型
 * Create by Augment Agent
 * Date: 2024-12-19
 */

namespace app\store\model;

use think\Model;

class CardStatusRecord extends Model
{
    protected $table = 'card_status_record';
    protected $pk = 'id';
    
    /**
     * 关联卡
     */
    public function card()
    {
        return $this->belongsTo('Card', 'card_id', 'id');
    }
    
    /**
     * 获取卡状态变更记录
     */
    public function getCardStatusRecords($cardId, $clientkeynum)
    {
        return $this->where('card_id', $cardId)
            ->where('clientkeynum', $clientkeynum)
            ->order('operation_time', 'desc')
            ->select();
    }
} 