<?php
namespace app\store\model;

use think\Model;
use think\Db;
use think\facade\Log;

/**
 * 库存单明细表模型 - Store版本
 * Class InventoryOrderDetail
 * @package app\store\model
 */
class InventoryOrderDetail extends Model
{
    protected $name = 'inventory_order_detail';

    /**
     * 批量创建销售出库单明细
     * @param int $order_id 出库单ID
     * @param string $order_no 出库单编号
     * @param int $shop_id 门店ID
     * @param array $details 明细数组
     * @param string $clientkeynum 客户端标识
     * @return bool
     */
    public function createSalesOutDetails($order_id, $order_no, $shop_id, $details, $clientkeynum = '')
    {
        if (empty($details)) {
            return false;
        }

        $data = [];
        $total_amount = 0;

        foreach ($details as $item) {
            $amount = round($item['quantity'] * $item['price'], 2);
            $total_amount += $amount;

            $data[] = [
                'order_id' => $order_id,
                'order_no' => $order_no,
                'order_type' => 2, // 2-出库
                'shop_id' => $shop_id,
                'product_id' => $item['product_id'],
                'inventory_id' => $item['inventory_id'],
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'discount' => 0,
                'amount' => $amount,
                'remark' => $item['remark'] ?? '',
                'clientkeynum' => $clientkeynum,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }

        try {
            // 批量插入明细
            $result = $this->insertAll($data);
            
            // 更新主表总金额
            if ($result) {
                $inventoryOrder = new InventoryOrder();
                $inventoryOrder->updateTotalAmount($order_id, $total_amount);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('创建销售出库单明细失败：' . $e->getMessage());
            return false;
        }
    }
} 