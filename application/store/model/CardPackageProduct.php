<?php
/**
 * 套餐商品关联模型
 * Create by Augment Agent
 * Date: 2024-12-19
 */

namespace app\store\model;

use think\Model;

class CardPackageProduct extends Model
{
    protected $table = 'card_package_product';
    protected $pk = 'id';
    
    /**
     * 关联套餐
     */
    public function package()
    {
        return $this->belongsTo('CardPackage', 'package_id', 'id');
    }
    
    /**
     * 关联商品库存
     */
    public function productInventory()
    {
        return $this->belongsTo('ProductInventory', 'product_inventory_id', 'id');
    }
    
    /**
     * 获取套餐包含的商品列表
     */
    public function getProductsByPackageId($packageId, $clientkeynum)
    {
        return $this->with(['productInventory'])
            ->where('package_id', $packageId)
            ->where('clientkeynum', $clientkeynum)
            ->select();
    }
} 