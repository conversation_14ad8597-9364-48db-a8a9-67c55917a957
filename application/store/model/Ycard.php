<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/10/14 00:14
 */

namespace app\store\model;

use think\Model;

class Ycard extends Model
{
    protected $table = 'client_ycard';
    protected $pk = 'id';

    public function cardLevel()
    {
        return $this->belongsTo('CardLevel', 'card_level_id', 'id',);
    }

    public function user()
    {
        return $this->belongsTo('User', 'member_id', 'id',);
    }

//    public function getBeginDuiAttr($value)
//    {
//        if (!empty($value)) {
//            return date('Y-m-d H:i:s', $value);
//        }
//    }
//
//    public function getEndDuiAttr($value)
//    {
//        if (!empty($value)) {
//            return date('Y-m-d H:i:s', $value);
//        }
//    }

}
