<?php
/**
 * Create by AI
 * Date:2024/10/26
 */

namespace app\store\service;

use app\api\model\OrderLog;
use app\api\model\WechatRefundLog;
use app\api\model\OrderRefundLog;
use app\store\lib\Wechat;
use app\store\model\Order;
use app\store\model\PlatWechatSet;
use app\store\model\OrderPayLog;
use think\Db;
use think\facade\Log;

class PaymentService 
{
    /**
     * 创建微信Native支付
     *
     * @param int $order_id 订单ID
     * @param string $clientkeynum 客户端密钥号
     * @param array $user 用户信息
     * @return array 包含支付数据的数组或错误信息
     */
    public function createNativePay($order_id, $clientkeynum, $user)
    {
        // 获取订单信息
        $order = Order::where(['id' => $order_id])->find();
        if (empty($order)) {
            return ['status' => 0, 'message' => '订单不存在'];
        }
        
        // 检查订单状态
        if ($order['status'] != 0) {
            return ['status' => 0, 'message' => '该订单状态不允许支付'];
        }
        
        // 获取微信支付配置
        $wechat_config = PlatWechatSet::getInfoByKeyNum($clientkeynum);
        if (empty($wechat_config)) {
            return ['status' => 0, 'message' => '未配置微信支付参数'];
        }
        
        // 创建支付日志
        $log_data = [
            'order_no' => $order['order_no'],
            'user_id' => $order['user_id'],
            'clientkeynum' => $clientkeynum,
            'price' => $order['price'],
            'is_pay' => 0,
            'add_time' => date('Y-m-d H:i:s'),
            'operator' => isset($user['plat_account']) ? $user['plat_account']['accountname'] : '店铺操作员',
        ];
        
        $pay_log = OrderPayLog::create($log_data);
        
        // 生成支付二维码
        $body = '店铺订单-' . $order['order_no']; // 商品描述
        $native_result = Wechat::createNativePayment(
            $wechat_config, 
            $pay_log['id'], // 使用支付日志ID作为商户订单号
            $order['price'],
            $body
        );
        
        if (isset($native_result['error'])) {
            return ['status' => 0, 'message' => '创建支付失败: ' . $native_result['error']];
        }
        
        // 记录支付日志
        OrderLog::create([
            'order_no' => $order['order_no'],
            'clientkeynum' => $clientkeynum,
            'content' => '创建微信Native支付',
            'operator' => isset($user['plat_account']) ? $user['plat_account']['accountname'] : '店铺操作员',
            'add_time' => date('Y-m-d H:i:s')
        ]);
        
        // 返回支付二维码链接
        $pay_data = [
            'order_no' => $order['order_no'],
            'pay_log_id' => $pay_log['id'],
            'code_url' => $native_result['code_url'],
            'price' => $order['price'],
            'prepay_id' => $native_result['prepay_id']
        ];
        
        return ['status' => 1, 'message' => '创建支付成功', 'data' => $pay_data];
    }
    
    /**
     * 查询支付状态
     *
     * @param string $order_no 订单号
     * @param string $clientkeynum 客户端密钥号
     * @return array 查询结果
     */
    public function queryPayStatus($order_no, $clientkeynum)
    {
        // 获取订单信息
        $order = Order::where(['order_no' => $order_no])->find();
        if (empty($order)) {
            return ['status' => 0, 'message' => '订单不存在'];
        }
        
        // 如果订单已支付
        if ($order['status'] >= 1) {
            return [
                'status' => 1, 
                'message' => '订单已支付', 
                'data' => [
                    'order_no' => $order_no,
                    'status' => $order['status'],
                    'is_paid' => true
                ]
            ];
        }
        
        // 查询最近的支付日志
        $pay_log = OrderPayLog::where(['order_no' => $order_no])
            ->order('id', 'desc')
            ->find();
            
        if (empty($pay_log)) {
            return ['status' => 0, 'message' => '未找到支付记录'];
        }
        
        // 如果支付日志已标记支付成功
        if ($pay_log['is_pay'] == 1) {
            return [
                'status' => 1, 
                'message' => '订单已支付', 
                'data' => [
                    'order_no' => $order_no,
                    'status' => $order['status'],
                    'is_paid' => true
                ]
            ];
        }
        
        // 获取微信支付配置
        $wechat_config = PlatWechatSet::getInfoByKeyNum($clientkeynum);
        if (empty($wechat_config)) {
            return ['status' => 0, 'message' => '未配置微信支付参数'];
        }
        
        // 查询订单支付状态
        $query_result = Wechat::queryOrder($wechat_config, $pay_log['id']);
        
        // 检查支付状态
        if (isset($query_result['return_code']) && $query_result['return_code'] == 'SUCCESS' 
            && isset($query_result['trade_state']) && $query_result['trade_state'] == 'SUCCESS') {
            
            try {
                Db::startTrans();
                
                // 更新支付日志
                $pay_log['is_pay'] = 1;
                $pay_log['pay_time'] = date('Y-m-d H:i:s');
                $pay_log['transaction_id'] = $query_result['transaction_id'];
                $pay_log['real_price'] = $query_result['total_fee'] / 100; // 单位：分
                $pay_log->save();
                
                // 更新订单状态
                Order::where('order_no', $order_no)->update([
                    'status' => 100, // 已支付状态
                    'pay_time' => date('Y-m-d H:i:s'),
                    'transaction_id' => $query_result['transaction_id'],
                    'real_price' => $query_result['total_fee'] / 100  // 单位：分
                ]);
                
                // 创建订单日志
                OrderLog::create([
                    'order_no' => $order_no,
                    'clientkeynum' => $clientkeynum,
                    'content' => '微信支付成功',
                    'operator' => '系统',
                    'add_time' => date('Y-m-d H:i:s')
                ]);
                
                Db::commit();
                
                // 支付成功后自动打印小票
                $this->autoPrintOrderAfterPayment($order_no, $clientkeynum);
                
                return [
                    'status' => 1, 
                    'message' => '订单已支付', 
                    'data' => [
                        'order_no' => $order_no,
                        'status' => 1,
                        'is_paid' => true
                    ]
                ];
                
            } catch (\Exception $e) {
                Db::rollback();
                Log::error('更新支付状态异常：' . $e->getMessage());
                return ['status' => 0, 'message' => '查询支付状态异常'];
            }
        }
        
        // 返回当前支付状态
        return [
            'status' => 1, 
            'message' => '支付未完成', 
            'data' => [
                'order_no' => $order_no,
                'status' => $order['status'],
                'is_paid' => false
            ]
        ];
    }
    
    /**
     * 关闭支付
     *
     * @param string $order_no 订单号
     * @param string $clientkeynum 客户端密钥号
     * @param array $user 用户信息
     * @return array 操作结果
     */
    public function closePay($order_no, $clientkeynum, $user)
    {
        // 获取订单信息
        $order = Order::where(['order_no' => $order_no])->find();
        if (empty($order)) {
            return ['status' => 0, 'message' => '订单不存在'];
        }
        
        // 只有待支付的订单才能关闭支付
        if ($order['status'] != 0) {
            return ['status' => 0, 'message' => '订单状态不允许关闭支付'];
        }
        
        // 查询最近的支付日志
        $pay_log = OrderPayLog::where(['order_no' => $order_no])
            ->order('id', 'desc')
            ->find();
            
        if (empty($pay_log) || $pay_log['is_pay'] == 1) {
            return ['status' => 0, 'message' => '没有可关闭的支付记录'];
        }
        
        // 获取微信支付配置
        $wechat_config = PlatWechatSet::getInfoByKeyNum($clientkeynum);
        if (empty($wechat_config)) {
            return ['status' => 0, 'message' => '未配置微信支付参数'];
        }
        
        // 关闭微信支付订单
        $close_result = Wechat::closeOrder($wechat_config, $pay_log['id']);
        
        // 记录操作日志
        OrderLog::create([
            'order_no' => $order_no,
            'clientkeynum' => $clientkeynum,
            'content' => '关闭微信支付订单',
            'operator' => isset($user['plat_account']) ? $user['plat_account']['accountname'] : '店铺操作员',
            'add_time' => date('Y-m-d H:i:s')
        ]);
        
        return ['status' => 1, 'message' => '关闭支付成功'];
    }

    /**
     * 处理微信退款
     *
     * @param array $order 订单信息
     * @param string $clientkeynum 客户端密钥号
     * @param string $refund_reason 退款原因
     * @param string $operator 操作员
     * @return array 退款结果
     */
    public function handleWechatRefund($order, $clientkeynum, $refund_reason = '订单取消', $operator = '系统')
    {
        try {
            // 获取微信支付配置
            $wechat_config = PlatWechatSet::getInfoByKeyNum($clientkeynum);
            if (empty($wechat_config)) {
                return ['success' => false, 'message' => '未配置微信支付参数'];
            }

            // 查询支付记录
            $pay_log = OrderPayLog::where(['order_no' => $order['order_no'], 'is_pay' => 1])
                ->order('id', 'desc')
                ->find();

            if (empty($pay_log)) {
                return ['success' => false, 'message' => '未找到已支付记录'];
            }

            // 生成退款单号
            $refund_no = 'RF' . date('YmdHis') . rand(1000, 9999);

            // 计算退款金额（微信支付部分）
            $wechat_pay_amount = $order['price'] - $order['card_price'];
            if ($wechat_pay_amount <= 0) {
                return ['success' => false, 'message' => '无微信支付金额需要退款'];
            }

            // 创建微信退款日志
            $refund_log_data = [
                'order_no' => $order['order_no'],
                'refund_no' => $refund_no,
                'out_trade_no' => $pay_log['id'], // 使用支付日志ID作为商户订单号
                'transaction_id' => $pay_log['transaction_id'] ?? '',
                'total_fee' => $order['price'],
                'refund_fee' => $wechat_pay_amount,
                'refund_reason' => $refund_reason,
                'clientkeynum' => $clientkeynum,
                'user_id' => $order['user_id'],
                'operator' => $operator
            ];

            $wechat_refund_log = WechatRefundLog::createRefundLog($refund_log_data);
            if (!$wechat_refund_log) {
                return ['success' => false, 'message' => '创建退款日志失败'];
            }

            // 调用微信退款接口
            $refund_result = Wechat::handleRefund(
                $wechat_config,
                $pay_log['id'], // 商户订单号
                $refund_no, // 退款单号
                intval($order['price'] * 100), // 订单总金额（分）
                intval($wechat_pay_amount * 100) // 退款金额（分）
            );

            Log::info('微信退款接口返回：' . json_encode($refund_result));

            // 处理退款结果
            if (isset($refund_result['return_code']) && $refund_result['return_code'] == 'SUCCESS') {
                if (isset($refund_result['result_code']) && $refund_result['result_code'] == 'SUCCESS') {
                    // 退款成功
                    WechatRefundLog::updateRefundStatus($refund_no, 1, [
                        'refund_id' => $refund_result['refund_id'] ?? '',
                        'wechat_response' => $refund_result
                    ]);

                    // 创建订单退款记录
                    OrderRefundLog::createRefundRecord([
                        'order_no' => $order['order_no'],
                        'refund_type' => 1, // 微信退款
                        'refund_amount' => $wechat_pay_amount,
                        'refund_reason' => $refund_reason,
                        'status' => 1, // 成功
                        'clientkeynum' => $clientkeynum,
                        'user_id' => $order['user_id'],
                        'operator' => $operator,
                        'refund_data' => [
                            'refund_no' => $refund_no,
                            'refund_id' => $refund_result['refund_id'] ?? '',
                            'wechat_response' => $refund_result
                        ]
                    ]);

                    return [
                        'success' => true,
                        'message' => '微信退款成功',
                        'refund_amount' => $wechat_pay_amount,
                        'refund_no' => $refund_no,
                        'refund_id' => $refund_result['refund_id'] ?? ''
                    ];
                } else {
                    // 退款失败
                    $error_msg = $refund_result['err_code_des'] ?? '退款失败';
                    WechatRefundLog::updateRefundStatus($refund_no, 2, [
                        'error_msg' => $error_msg,
                        'wechat_response' => $refund_result
                    ]);

                    return ['success' => false, 'message' => '微信退款失败：' . $error_msg];
                }
            } else {
                // 通信失败
                $error_msg = $refund_result['return_msg'] ?? '退款通信失败';
                WechatRefundLog::updateRefundStatus($refund_no, 2, [
                    'error_msg' => $error_msg,
                    'wechat_response' => $refund_result
                ]);

                return ['success' => false, 'message' => '微信退款通信失败：' . $error_msg];
            }

        } catch (\Exception $e) {
            Log::error('微信退款异常：' . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            // 更新退款日志状态为失败
            if (isset($refund_no)) {
                WechatRefundLog::updateRefundStatus($refund_no, 2, [
                    'error_msg' => $e->getMessage()
                ]);
            }

            return ['success' => false, 'message' => '微信退款异常：' . $e->getMessage()];
        }
    }
    
    /**
     * 创建微信付款码支付
     *
     * @param int $order_id 订单ID
     * @param string $auth_code 付款码
     * @param string $clientkeynum 客户端密钥号
     * @param array $user 用户信息
     * @return array 包含支付数据的数组或错误信息
     */
    public function createMicropay($order_id, $auth_code, $clientkeynum, $user)
    {
        // 获取订单信息
        $order = Order::where(['id' => $order_id])->find();
        if (empty($order)) {
            return ['status' => 0, 'message' => '订单不存在'];
        }
        
        // 检查订单状态
        if ($order['status'] != 0) {
            return ['status' => 0, 'message' => '该订单状态不允许支付'];
        }
        
        // 获取微信支付配置
        $wechat_config = PlatWechatSet::getInfoByKeyNum($clientkeynum);
        if (empty($wechat_config)) {
            return ['status' => 0, 'message' => '未配置微信支付参数'];
        }
        
        // 创建支付日志
        $log_data = [
            'order_no' => $order['order_no'],
            'user_id' => $order['user_id'],
            'clientkeynum' => $clientkeynum,
            'price' => $order['price'],
            'is_pay' => 0,
            'add_time' => date('Y-m-d H:i:s'),
            'operator' => isset($user['plat_account']) ? $user['plat_account']['accountname'] : '店铺操作员',
        ];
        
        $pay_log = OrderPayLog::create($log_data);
        
        // 处理付款码支付
        $body = '店铺订单-' . $order['order_no']; // 商品描述
        $micropay_result = Wechat::createMicropay(
            $wechat_config, 
            $pay_log['id'], // 使用支付日志ID作为商户订单号
            $order['price'],
            $body,
            $auth_code
        );
        
        // 记录操作日志
        OrderLog::create([
            'order_no' => $order['order_no'],
            'clientkeynum' => $clientkeynum,
            'content' => '创建微信付款码支付',
            'operator' => isset($user['plat_account']) ? $user['plat_account']['accountname'] : '店铺操作员',
            'add_time' => date('Y-m-d H:i:s')
        ]);
        
        if ($micropay_result['status'] === 'SUCCESS') {
            // 支付成功，立即更新订单状态
            try {
                Db::startTrans();
                
                // 更新支付日志
                $pay_log['is_pay'] = 1;
                $pay_log['pay_time'] = date('Y-m-d H:i:s');
                $pay_log['transaction_id'] = $micropay_result['transaction_id'];
                $pay_log['real_price'] = round($micropay_result['total_fee'] / 100, 2); // 单位：分转元
                $pay_log->save();
                
                // 更新订单状态
                Order::where('order_no', $order['order_no'])->update([
                    'status' => 100, // 已支付状态
                    'pay_time' => date('Y-m-d H:i:s'),
                    'transaction_id' => $micropay_result['transaction_id'],
                    'real_price' => round($micropay_result['total_fee'] / 100, 2)  // 单位：分转元
                ]);
                
                // 创建订单日志
                OrderLog::create([
                    'order_no' => $order['order_no'],
                    'clientkeynum' => $clientkeynum,
                    'content' => '微信付款码支付成功',
                    'operator' => isset($user['plat_account']) ? $user['plat_account']['accountname'] : '店铺操作员',
                    'add_time' => date('Y-m-d H:i:s')
                ]);
                
                Db::commit();
                
                // 支付成功后自动打印小票
                $this->autoPrintOrderAfterPayment($order['order_no'], $clientkeynum);
                
                return [
                    'status' => 1, 
                    'message' => '付款码支付成功', 
                    'data' => [
                        'order_no' => $order['order_no'],
                        'pay_log_id' => $pay_log['id'],
                        'transaction_id' => $micropay_result['transaction_id'],
                        'price' => $order['price'],
                        'is_paid' => true
                    ]
                ];
                
            } catch (\Exception $e) {
                Db::rollback();
                Log::error('更新付款码支付状态异常：' . $e->getMessage());
                return ['status' => 0, 'message' => '更新支付状态异常'];
            }
            
        } elseif ($micropay_result['status'] === 'USERPAYING') {
            // 用户支付中，需要轮询查询支付状态
            return [
                'status' => 1, 
                'message' => '用户支付中，请输入密码', 
                'data' => [
                    'order_no' => $order['order_no'],
                    'pay_log_id' => $pay_log['id'],
                    'status' => 'USERPAYING',
                    'need_query' => true, // 需要轮询查询
                    'price' => $order['price']
                ]
            ];
            
        } elseif ($micropay_result['status'] === 'SYSTEMERROR') {
            // 系统错误，建议查询
            return [
                'status' => 1, 
                'message' => '系统处理中，请稍后查询', 
                'data' => [
                    'order_no' => $order['order_no'],
                    'pay_log_id' => $pay_log['id'],
                    'status' => 'SYSTEMERROR',
                    'need_query' => true, // 需要查询
                    'price' => $order['price']
                ]
            ];
            
        } else {
            // 支付失败
            return ['status' => 0, 'message' => '付款码支付失败: ' . ($micropay_result['error'] ?? '未知错误')];
        }
    }
    
    /**
     * 查询付款码支付状态
     * 用于轮询查询USERPAYING和SYSTEMERROR状态的订单
     *
     * @param string $order_no 订单号
     * @param string $clientkeynum 客户端密钥号
     * @return array 查询结果
     */
    public function queryMicropayStatus($order_no, $clientkeynum)
    {
        // 获取订单信息
        $order = Order::where(['order_no' => $order_no])->find();
        if (empty($order)) {
            return ['status' => 0, 'message' => '订单不存在'];
        }
        
        // 如果订单已支付
        if ($order['status'] == 1 || $order['status'] == 100) {
            return [
                'status' => 1, 
                'message' => '订单已支付', 
                'data' => [
                    'order_no' => $order_no,
                    'status' => $order['status'],
                    'is_paid' => true
                ]
            ];
        }
        
        // 查询最近的支付日志
        $pay_log = OrderPayLog::where(['order_no' => $order_no])
            ->order('id', 'desc')
            ->find();
            
        if (empty($pay_log)) {
            return ['status' => 0, 'message' => '未找到支付记录'];
        }
        
        // 如果支付日志已标记支付成功
        if ($pay_log['is_pay'] == 1) {
            return [
                'status' => 1, 
                'message' => '订单已支付', 
                'data' => [
                    'order_no' => $order_no,
                    'status' => $order['status'],
                    'is_paid' => true
                ]
            ];
        }
        
        // 使用已有的查询支付状态方法
        return $this->queryPayStatus($order_no, $clientkeynum);
    }
    
    /**
     * 撤销付款码支付
     * 当付款码支付超时或失败时调用
     *
     * @param string $order_no 订单号
     * @param string $clientkeynum 客户端密钥号
     * @param array $user 用户信息
     * @return array 撤销结果
     */
    public function reverseMicropay($order_no, $clientkeynum, $user)
    {
        // 获取订单信息
        $order = Order::where(['order_no' => $order_no])->find();
        if (empty($order)) {
            return ['status' => 0, 'message' => '订单不存在'];
        }
        
        // 查询最近的支付日志
        $pay_log = OrderPayLog::where(['order_no' => $order_no])
            ->order('id', 'desc')
            ->find();
            
        if (empty($pay_log)) {
            return ['status' => 0, 'message' => '未找到支付记录'];
        }
        
        // 如果已经支付成功，不能撤销
        if ($pay_log['is_pay'] == 1) {
            return ['status' => 0, 'message' => '订单已支付，无法撤销'];
        }
        
        // 获取微信支付配置
        $wechat_config = PlatWechatSet::getInfoByKeyNum($clientkeynum);
        if (empty($wechat_config)) {
            return ['status' => 0, 'message' => '未配置微信支付参数'];
        }
        
        // 撤销微信支付订单
        $reverse_result = Wechat::reverseOrder($wechat_config, $pay_log['id']);
        
        // 记录操作日志
        OrderLog::create([
            'order_no' => $order_no,
            'clientkeynum' => $clientkeynum,
            'content' => '撤销微信付款码支付',
            'operator' => isset($user['plat_account']) ? $user['plat_account']['accountname'] : '店铺操作员',
            'add_time' => date('Y-m-d H:i:s')
        ]);
        
        if (isset($reverse_result['return_code']) && $reverse_result['return_code'] === 'SUCCESS') {
            return ['status' => 1, 'message' => '撤销付款码支付成功'];
        } else {
            $error_msg = $reverse_result['return_msg'] ?? '撤销失败';
            return ['status' => 0, 'message' => '撤销付款码支付失败: ' . $error_msg];
        }
    }
    
    /**
     * 支付成功后自动打印订单小票
     * @param string $order_no 订单号
     * @param string $clientkeynum 客户端密钥
     * @return void
     */
    private function autoPrintOrderAfterPayment($order_no, $clientkeynum)
    {
        try {
            // 使用订单打印服务自动打印
            $result = \app\api\service\OrderPrintService::autoPrintOrder($order_no, $clientkeynum);
            
            if ($result['success']) {
                Log::info("支付成功后自动打印小票成功：订单号{$order_no}");
            } else {
                Log::warning("支付成功后自动打印小票失败：订单号{$order_no}，原因：{$result['message']}");
            }
        } catch (\Exception $e) {
            Log::error("支付成功后自动打印小票异常：订单号{$order_no}，错误信息：" . $e->getMessage());
        }
    }
} 