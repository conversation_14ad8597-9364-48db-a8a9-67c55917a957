<?php

namespace app\store\controller;

use app\store\model\YlyShopPrinterConfig;
use app\common\service\YlyPrintService;
use think\Request;
use think\Controller;

/**
 * 门店端打印机配置控制器
 */
class PrinterConfigController extends StoreController
{
    /**
     * 打印机配置页面
     */
    public function index()
    {
        $shopId = session('store_info.shop_id');
        $clientkeynum = session('store_info.clientkeynum');
        
        // 获取当前配置
        $config = YlyShopPrinterConfig::getByShop($shopId, $clientkeynum);
        
        // 获取统计信息
        $statistics = YlyShopPrinterConfig::getShopStatistics($shopId, $clientkeynum);
        
        $this->assign([
            'config' => $config,
            'statistics' => $statistics
        ]);
        
        return $this->fetch();
    }
    
    /**
     * 保存配置
     */
    public function save()
    {
        if (request()->isPost()) {
            $params = request()->param();
            $shopId = session('store_info.shop_id');
            $clientkeynum = session('store_info.clientkeynum');
            
            // 验证数据
            $errors = YlyShopPrinterConfig::validateConfig($params);
            if (!empty($errors)) {
                return json(['code' => 0, 'msg' => implode('，', $errors)]);
            }
            
            $data = [
                'clientkeynum' => $clientkeynum,
                'shop_id' => $shopId,
                'printer_sn' => trim($params['printer_sn']),
                'printer_key' => trim($params['printer_key']),
                'printer_name' => trim($params['printer_name'] ?? ''),
                'print_copies' => intval($params['print_copies'] ?? 1),
                'auto_print' => intval($params['auto_print'] ?? 1),
                'status' => intval($params['status'] ?? 1)
            ];
            
            try {
                $result = YlyShopPrinterConfig::saveConfig($data);
                
                if ($result) {
                    return json(['code' => 1, 'msg' => '保存成功']);
                } else {
                    return json(['code' => 0, 'msg' => '保存失败']);
                }
                
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '保存失败: ' . $e->getMessage()]);
            }
        }
        
        return json(['code' => 0, 'msg' => '请求方式错误']);
    }
    
    /**
     * 测试打印
     */
    public function testPrint()
    {
        if (request()->isPost()) {
            $shopId = session('store_info.shop_id');
            $clientkeynum = session('store_info.clientkeynum');
            
            try {
                $printService = new YlyPrintService($clientkeynum);
                $result = $printService->testPrint($shopId);
                
                if ($result['success']) {
                    return json(['code' => 1, 'msg' => $result['message']]);
                } else {
                    return json(['code' => 0, 'msg' => $result['message']]);
                }
                
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '测试打印失败: ' . $e->getMessage()]);
            }
        }
        
        return json(['code' => 0, 'msg' => '请求方式错误']);
    }
    
    /**
     * 手动打印订单
     */
    public function printOrder()
    {
        if (request()->isPost()) {
            $orderNo = request()->param('order_no');
            $shopId = session('store_info.shop_id');
            $clientkeynum = session('store_info.clientkeynum');
            
            if (empty($orderNo)) {
                return json(['code' => 0, 'msg' => '订单号不能为空']);
            }
            
            try {
                // 获取订单数据
                $orderData = $this->getOrderData($orderNo, $shopId);
                if (!$orderData) {
                    return json(['code' => 0, 'msg' => '订单不存在或不属于当前门店']);
                }
                
                // 执行打印
                $printService = new YlyPrintService($clientkeynum);
                $result = $printService->printOrderTicket($shopId, $orderNo, $orderData);
                
                if ($result['success']) {
                    return json(['code' => 1, 'msg' => $result['message']]);
                } else {
                    return json(['code' => 0, 'msg' => $result['message']]);
                }
                
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '打印失败: ' . $e->getMessage()]);
            }
        }
        
        return json(['code' => 0, 'msg' => '请求方式错误']);
    }
    
    /**
     * 检查配置状态
     */
    public function checkStatus()
    {
        $shopId = session('store_info.shop_id');
        $clientkeynum = session('store_info.clientkeynum');
        
        $config = YlyShopPrinterConfig::getByShop($shopId, $clientkeynum);
        $statistics = YlyShopPrinterConfig::getShopStatistics($shopId, $clientkeynum);
        
        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'configured' => !empty($config),
                'status' => $config ? $config['status'] : 0,
                'auto_print' => $config ? $config['auto_print'] : 0,
                'statistics' => $statistics
            ]
        ]);
    }
    
    /**
     * 删除配置
     */
    public function delete()
    {
        if (request()->isPost()) {
            $shopId = session('store_info.shop_id');
            $clientkeynum = session('store_info.clientkeynum');
            
            try {
                $result = YlyShopPrinterConfig::where([
                    'shop_id' => $shopId,
                    'clientkeynum' => $clientkeynum
                ])->delete();
                
                if ($result) {
                    return json(['code' => 1, 'msg' => '删除成功']);
                } else {
                    return json(['code' => 0, 'msg' => '删除失败']);
                }
                
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '删除失败: ' . $e->getMessage()]);
            }
        }
        
        return json(['code' => 0, 'msg' => '请求方式错误']);
    }
    
    /**
     * 获取订单数据
     * @param string $orderNo
     * @param int $shopId
     * @return array|null
     */
    private function getOrderData($orderNo, $shopId)
    {
        // 这里需要根据实际的订单表结构来获取数据
        // 示例代码，需要根据实际情况调整
        $order = db('order')
            ->where(['order_no' => $orderNo, 'shop_id' => $shopId])
            ->find();
        
        if (!$order) {
            return null;
        }
        
        // 获取订单商品
        $orderItems = db('order_item')
            ->alias('oi')
            ->join('product p', 'oi.product_id = p.id')
            ->where('oi.order_no', $orderNo)
            ->field('p.product_name, p.product_type, oi.spec_name, oi.quantity, oi.weight, oi.price, oi.total_amount')
            ->select();
        
        // 获取门店信息
        $shop = db('shop')->where('id', $shopId)->find();
        
        // 格式化订单数据
        $orderData = [
            'order_no' => $order['order_no'],
            'shop_name' => $shop['shop_name'] ?? '商店',
            'created_at' => $order['created_at'],
            'customer_name' => $order['customer_name'],
            'customer_phone' => $order['customer_phone'],
            'delivery_address' => $order['delivery_address'],
            'delivery_time' => $order['delivery_time'],
            'total_amount' => $order['total_amount'],
            'delivery_fee' => $order['delivery_fee'],
            'discount_amount' => $order['discount_amount'],
            'actual_amount' => $order['actual_amount'],
            'pay_type_text' => $this->getPayTypeText($order['pay_type']),
            'remark' => $order['remark'],
            'products' => $orderItems
        ];
        
        return $orderData;
    }
    
    /**
     * 获取支付方式文字
     * @param int $payType
     * @return string
     */
    private function getPayTypeText($payType)
    {
        $payTypes = [
            1 => '微信支付',
            2 => '卡支付',
            3 => '组合支付',
            4 => '现金支付',
            5 => '免单'
        ];
        
        return $payTypes[$payType] ?? '未知';
    }
} 