<?php
/**
 * 门店端计量商品控制器
 * Create by AI Assistant
 * Date: 2024/12/19
 */

namespace app\store\controller;

use app\admin\model\Product;
use app\store\model\ShopProductInventory;
use app\store\model\WeightProductSales;
use app\store\lib\Response;
use think\Controller;
use think\Db;
use think\Request;

class WeightProductController extends Controller
{
    /**
     * 获取门店计量商品列表
     */
    public function getShopWeightProducts(Request $request)
    {
        $shopId = $request->param('shop_id');
        
        if (empty($shopId)) {
            return Response::json(Response::ERROR, '门店ID不能为空');
        }
        
        // 验证门店权限
        $this->validateShopPermission($shopId);
        
        // 获取门店启用的计量商品
        $products = Db::name('products')->alias('p')
            ->join('shop_product_inventory spi', 'p.id = spi.product_id')
            ->where('p.product_type', Product::PRODUCT_TYPE_WEIGHT)
            ->where('spi.shop_id', $shopId)
            ->where('spi.status', 1)
            ->field('p.*, spi.weight_stock, spi.inventory_id, spi.stock_unit')
            ->select();
        
        return Response::json(Response::SUCCESS, $products, '获取成功');
    }
    
    /**
     * 处理计重秤数据
     */
    public function processScaleData(Request $request)
    {
        $shopId = $request->param('shop_id');
        $inventoryId = $request->param('inventory_id');
        $scaleData = $request->param('scale_data');
        
        if (empty($shopId) || empty($inventoryId) || empty($scaleData)) {
            return Response::json(Response::ERROR, '参数不能为空');
        }
        
        // 验证门店权限
        $this->validateShopPermission($shopId);
        
        // 验证数据完整性
        if (!isset($scaleData['weight']) || !isset($scaleData['total_price'])) {
            return Response::json(Response::ERROR, '计重秤数据不完整');
        }
        
        if ($scaleData['weight'] <= 0 || $scaleData['total_price'] <= 0) {
            return Response::json(Response::ERROR, '重量和价格必须大于0');
        }
        
        // 验证库存
        $inventory = ShopProductInventory::where([
            'shop_id' => $shopId,
            'inventory_id' => $inventoryId
        ])->find();
        
        if (!$inventory) {
            return Response::json(Response::ERROR, '库存记录不存在');
        }
        
        if ($inventory->weight_stock < $scaleData['weight']) {
            return Response::json(Response::ERROR, '重量库存不足，当前库存：' . $inventory->weight_stock . 'kg');
        }
        
        // 计算单价（如果没有提供）
        $unitPrice = $scaleData['unit_price'] ?? round($scaleData['total_price'] / $scaleData['weight'], 2);
        
        // 处理数据
        $processedData = [
            'shop_id' => $shopId,
            'inventory_id' => $inventoryId,
            'weight' => $scaleData['weight'],
            'total_price' => $scaleData['total_price'],
            'unit_price' => $unitPrice,
            'product_id' => $inventory->product_id,
            'clientkeynum' => $inventory->clientkeynum
        ];
        
        return Response::json(Response::SUCCESS, $processedData, '处理成功');
    }
    
    /**
     * 计量商品入库
     */
    public function weightInbound(Request $request)
    {
        $shopId = $request->param('shop_id');
        $inventoryId = $request->param('inventory_id');
        $weight = $request->param('weight');
        $remark = $request->param('remark', '手动入库');
        
        if (empty($shopId) || empty($inventoryId) || empty($weight)) {
            return Response::json(Response::ERROR, '参数不能为空');
        }
        
        if ($weight <= 0) {
            return Response::json(Response::ERROR, '入库重量必须大于0');
        }
        
        // 验证门店权限
        $this->validateShopPermission($shopId);
        
        $inventory = ShopProductInventory::where([
            'shop_id' => $shopId,
            'inventory_id' => $inventoryId
        ])->find();
        
        if (!$inventory) {
            return Response::json(Response::ERROR, '库存记录不存在');
        }
        
        try {
            $inventory->addWeightStock($weight, $remark);
            return Response::json(Response::SUCCESS, [], '入库成功');
        } catch (\Exception $e) {
            return Response::json(Response::ERROR, $e->getMessage());
        }
    }
    
    /**
     * 获取重量库存变动日志
     */
    public function getWeightStockLogs(Request $request)
    {
        $shopId = $request->param('shop_id');
        $inventoryId = $request->param('inventory_id');
        $page = $request->param('page', 1);
        $limit = $request->param('limit', 20);
        
        if (empty($shopId) || empty($inventoryId)) {
            return Response::json(Response::ERROR, '参数不能为空');
        }
        
        // 验证门店权限
        $this->validateShopPermission($shopId);
        
        $logs = Db::name('shop_product_stock_log')
            ->where('shop_id', $shopId)
            ->where('inventory_id', $inventoryId)
            ->where('change_type', 'in', [2, 3]) // 重量变动
            ->order('create_time desc')
            ->page($page, $limit)
            ->select();
        
        $total = Db::name('shop_product_stock_log')
            ->where('shop_id', $shopId)
            ->where('inventory_id', $inventoryId)
            ->where('change_type', 'in', [2, 3])
            ->count();
        
        $result = [
            'list' => $logs,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ];
        
        return Response::json(Response::SUCCESS, $result, '获取成功');
    }
    
    /**
     * 获取计量商品销售统计
     */
    public function getSalesStats(Request $request)
    {
        $shopId = $request->param('shop_id');
        $startDate = $request->param('start_date');
        $endDate = $request->param('end_date');
        
        if (empty($shopId)) {
            return Response::json(Response::ERROR, '门店ID不能为空');
        }
        
        // 验证门店权限
        $this->validateShopPermission($shopId);
        
        $stats = WeightProductSales::getShopSalesStats($shopId, $startDate, $endDate);
        
        return Response::json(Response::SUCCESS, $stats, '获取成功');
    }
    
    /**
     * 验证门店权限
     */
    private function validateShopPermission($shopId)
    {
        // 这里应该根据实际的权限验证逻辑来实现
        // 暂时简单验证门店是否存在
        $shop = Db::name('shop')->where('id', $shopId)->find();
        if (!$shop) {
            return Response::json(Response::ERROR, '门店不存在');
        }
        
        // 可以在这里添加更多权限验证逻辑
        // 比如验证当前用户是否有权限操作该门店
        return true;
    }
} 