<?php
/**
 * Create by AI
 * Date:2024/10/25
 */

namespace app\store\controller;

use app\store\lib\Response;
use app\store\service\PaymentService;
use think\Controller;
use think\Db;
use think\facade\Log;
use think\Request;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

class PayController extends Controller
{
    /**
     * 生成微信Native支付二维码
     *
     * @param Request $request 请求对象
     * @return \think\response\Json
     */
    public function createNativePay(Request $request)
    {
        $order_id = $request->param('order_id');
        $user = $request->user_info;
        $clientkeynum = $request->basekeynum;
        
        // 使用支付服务
        $payment_service = new PaymentService();
        $result = $payment_service->createNativePay($order_id, $clientkeynum, $user);
        
        if ($result['status'] === 1) {
            // 处理code_url 生成二维码base64
            $code_url = $result['data']['code_url'];
            $qrcode = new QrCode($code_url);
            $qrcode->setSize(200);
            $writer = new PngWriter();
            $pngresult = $writer->write($qrcode);
            $base64 = $pngresult->getDataUri();
            $result['data']['qrcode_base64'] = $base64;
            return Response::json(Response::SUCCESS, $result['message'], $result['data']);
        } else {
            return Response::json(Response::ERROR, $result['message']);
        }
    }
    
    /**
     * 查询支付状态
     *
     * @param Request $request 请求对象
     * @return \think\response\Json
     */
    public function queryPayStatus(Request $request)
    {
        $order_no = $request->param('order_no');
        $clientkeynum = $request->basekeynum;
        
        // 使用支付服务
        $payment_service = new PaymentService();
        $result = $payment_service->queryPayStatus($order_no, $clientkeynum);
        
        if ($result['status'] === 1) {
            return Response::json(Response::SUCCESS, $result['message'], $result['data']);
        } else {
            return Response::json(Response::ERROR, $result['message']);
        }
    }
    
    /**
     * 关闭支付
     *
     * @param Request $request 请求对象
     * @return \think\response\Json
     */
    public function closePay(Request $request)
    {
        $order_no = $request->param('order_no');
        $clientkeynum = $request->basekeynum;
        $user = $request->user_info;
        
        // 使用支付服务
        $payment_service = new PaymentService();
        $result = $payment_service->closePay($order_no, $clientkeynum, $user);
        
        if ($result['status'] === 1) {
            return Response::json(Response::SUCCESS, $result['message']);
        } else {
            return Response::json(Response::ERROR, $result['message']);
        }
    }

    /**
     * 创建微信付款码支付
     *
     * @param Request $request 请求对象
     * @return \think\response\Json
     */
    public function createMicropay(Request $request)
    {
        $order_id = $request->param('order_id');
        $auth_code = $request->param('auth_code'); // 用户付款码（18位数字）
        $user = $request->user_info;
        $clientkeynum = $request->basekeynum;
        
        // 验证必填参数
        if (empty($order_id)) {
            return Response::json(Response::ERROR, '订单ID不能为空');
        }
        
        if (empty($auth_code)) {
            return Response::json(Response::ERROR, '付款码不能为空');
        }
        
        // 验证付款码格式
        if (!preg_match('/^\d{18}$/', $auth_code)) {
            return Response::json(Response::ERROR, '付款码格式错误，应为18位数字');
        }
        
        // 使用支付服务
        $payment_service = new PaymentService();
        $result = $payment_service->createMicropay($order_id, $auth_code, $clientkeynum, $user);
        
        if ($result['status'] === 1) {
            return Response::json(Response::SUCCESS, $result['message'], $result['data']);
        } else {
            return Response::json(Response::ERROR, $result['message']);
        }
    }
    
    /**
     * 查询付款码支付状态
     *
     * @param Request $request 请求对象
     * @return \think\response\Json
     */
    public function queryMicropayStatus(Request $request)
    {
        $order_no = $request->param('order_no');
        $clientkeynum = $request->basekeynum;
        
        if (empty($order_no)) {
            return Response::json(Response::ERROR, '订单号不能为空');
        }
        
        // 使用支付服务
        $payment_service = new PaymentService();
        $result = $payment_service->queryMicropayStatus($order_no, $clientkeynum);
        
        if ($result['status'] === 1) {
            return Response::json(Response::SUCCESS, $result['message'], $result['data']);
        } else {
            return Response::json(Response::ERROR, $result['message']);
        }
    }
    
    /**
     * 撤销付款码支付
     *
     * @param Request $request 请求对象
     * @return \think\response\Json
     */
    public function reverseMicropay(Request $request)
    {
        $order_no = $request->param('order_no');
        $clientkeynum = $request->basekeynum;
        $user = $request->user_info;
        
        if (empty($order_no)) {
            return Response::json(Response::ERROR, '订单号不能为空');
        }
        
        // 使用支付服务
        $payment_service = new PaymentService();
        $result = $payment_service->reverseMicropay($order_no, $clientkeynum, $user);
        
        if ($result['status'] === 1) {
            return Response::json(Response::SUCCESS, $result['message']);
        } else {
            return Response::json(Response::ERROR, $result['message']);
        }
    }
} 