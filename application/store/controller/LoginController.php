<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/10/12 10:57
 */

namespace app\store\controller;

use app\api\lib\Wechat;
use app\api\model\Application;
use app\store\lib\Response;
use app\store\model\PlatAccount;
use app\store\model\ShopWeixinSet;
use app\store\model\StoreUser;
use think\Controller;
use think\Db;
use think\facade\Log;
use think\Request;
use think\facade\Cache;

class LoginController extends Controller
{
    /**
     * 处理微信登录流程
     *
     * 该函数通过微信认证用户，如果用户不存在则创建新用户，
     * 并为会话生成一个令牌。
     *
     * @param Request $request 传入的 HTTP 请求对象，包含：
     *                         - basekeynum: 客户端密钥号码
     *                         - code: 微信授权码
     *
     * @return \think\response\Json 返回 JSON 格式的响应：
     *                              - 成功时：返回成功状态码、令牌和成功消息
     *                              - 用户已存在时：返回错误状态码和成功消息
     */
    public function wechatLogin(Request $request)
    {
        $appid = $request->param('appid');
        $application = Application::findByAppid($appid);
        $clientkeynum = $application['clientkeynum'];
        $code = $request->param('code');
        $config = ShopWeixinSet::getConfigByBaseKeyNum($clientkeynum);
        $app = Wechat::getMiniWechatInstance($config);

        $response = $app->auth->session($code);

        $openid = $response['openid'];
        // 查询是否存在该openid
        $member = StoreUser::where('openid', $openid)->find();
        if (!$member) {
            // 如果不存在，直接注册
            $member = StoreUser::create([
                'openid' => $openid,
                'clientkeynum' => $clientkeynum,
                'type' => 0
            ]);
            // 跳转到首页并缓存 member_id
            // 生成token
            Log::info('member: '. json_encode($member));;
            $token = md5($member['id'] . time());
            Cache::set($token, json_encode($member), 3600); // 1小时缓存
            return Response::json(Response::SUCCESS, '登录成功', ['token' => $token]);
        } else {
            if ($member['account_id']) {
                $plat_account = PlatAccount::where('account_id', $member['account_id'])->find();
                if ($plat_account) {
                    $member['account_id'] = $plat_account['account_id'];
                    $member['type'] = 1;
                    $member->save();
                    $member['plat_account'] = $plat_account;
                    $member['choose_one_config'] = Db::table('choose_one_config')
                        ->where(['basekeynum' => $clientkeynum])
                        ->field('id, BaseNum, bkey, domain')
                        ->find();
                    $token = md5($member['id'] . time());
                    Cache::set($token, json_encode($member), 3600); // 1小时缓存
                    return Response::json(Response::SUCCESS, '登录成功', ['token' => $token]);
                }
            }
            // 如果存在，跳转到首页
            Log::info('member: '. json_encode($member));;
            $token = md5($member['id'] . time());
            Cache::set($token, json_encode($member), 3600); // 1小时缓存
            return Response::json(Response::SUCCESS, '登录成功', ['token' => $token]);
        }
    }

    /**
     * 处理账户登录过程
     *
     * 此函数根据用户的账户凭证和客户端密钥号码进行身份验证。
     * 它检查提供的信息是否与数据库匹配，在登录成功后更新用户信息，
     * 并缓存用户数据。
     *
     * @param Request $request 传入的 HTTP 请求对象
     * @return \think\response\Json 返回 JSON 格式的登录结果
     *     成功时返回成功状态码和 token
     *     失败时返回错误状态码和错误信息
     */
    public function accountLogin(Request $request)
    {
        $user = $request->user_info;
        $clientkeynum = $request->basekeynum;
        $token = $request->param('token');

        $user = StoreUser::where('id', $user['id'])->find();
        if (!$user || $user['clientkeynum'] != $clientkeynum ) {
            return Response::json(Response::ERROR, '账号或密码错误');
        }

        // 获取账号和密码
        $account = $request->param('account');
        $password = $request->param('password');

        // 检索是否存在该账号
        $plat_account = PlatAccount::where('accountname', $account)
            ->where(function ($query) use($clientkeynum) {
                $query->where('basekeynum', $clientkeynum)
                    ->whereOr('parent_basekeynum', $clientkeynum);
            })
        ->find();
        if (!$plat_account) {
            return Response::json(Response::ERROR, '账号或密码错误');
        }

        if ($plat_account['accountpassword'] != password($password)) {
            return Response::json(Response::ERROR, '账号或密码错误');
        }

        // 登录成功，更新用户信息
        $user['account_id'] = $plat_account['account_id'];
        $user['type'] = 1;
        $user->save();

        $user['plat_account'] = $plat_account;
        $user['choose_one_config'] = Db::table('choose_one_config')
            ->where(['basekeynum' => $clientkeynum])
            ->field('id, BaseNum, bkey, domain')
            ->find();

        // 缓存用户信息
        Cache::set($token, json_encode($user), 3600 * 24); // 24小时缓存
        return Response::json(Response::SUCCESS, ['token' => $token], '登录成功');
    }

    public function logout(Request $request)
    {
        $user = $request->user_info;
        $user = StoreUser::where('id', $user['id'])->find();
        // 更新用户信息
        $user['account_id'] = null;
        $user['type'] = 0;
        $user->save();
        // 删除 token
        Cache::rm($request->param('token'));
        return Response::json(Response::SUCCESS, [], '退出成功');
    }
}























