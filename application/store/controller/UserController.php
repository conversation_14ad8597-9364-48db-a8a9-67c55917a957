<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/10/12 14:23
 */

namespace app\store\controller;

use think\Controller;
use app\store\lib\Response; 
use think\Request;
use think\facade\Cache;
use think\Db;

class UserController extends Controller
{
    // 短信验证码限制常量
    const SMS_PHONE_LIMIT = 5;        // 同一手机号1小时内最多发送次数
    const SMS_PHONE_TIME_WINDOW = 3600; // 手机号限制时间窗口（秒）
    const SMS_IP_LIMIT = 10;          // 同一IP1小时内最多发送次数  
    const SMS_IP_TIME_WINDOW = 3600;  // IP限制时间窗口（秒）
    const SMS_INTERVAL = 60;          // 同一手机号发送间隔（秒）
    
    public function getUserInfo(Request $request){
        $user = $request->user_info;
        return Response::json(Response::SUCCESS, '获取用户信息成功', $user);
    }

    /**
     * 发送短信验证码（带防刷逻辑）
     * @param Request $request
     * @return \think\response\Json
     */
    public function sendSmsCode(Request $request){
        try {
            // $basekeynum = $request->user_info['plat_account']['basekeynum'];

            // $basekeynum = $request->user_info['plat_account']['keynum'];
            $basekeynum = $request->user_info['plat_account']['parent_basekeynum'];
            $phone = $request->param('phone');
            $ip = $request->ip();
            $userId = isset($request->user_info['id']) ? $request->user_info['id'] : 0;
            
            // 1. 参数验证
            if (empty($phone)) {
                return Response::json(Response::ERROR, '手机号不能为空');
            }
            
            // 手机号格式验证
            if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
                return Response::json(Response::ERROR, '请输入正确的手机号格式');
            }
            
            if (empty($basekeynum)) {
                return Response::json(Response::ERROR, '系统参数错误');
            }
            
            // 2. IP防刷限制
            if (!$this->checkIpSmsLimit($ip)) {
                $this->logSmsAttempt($phone, $ip, $userId, '发送失败-IP超出请求限制', $basekeynum, false);
                return Response::json(Response::ERROR, '请求过于频繁，请1小时后再试');
            }
            
            // 3. 手机号发送频率限制  
            if (!$this->checkPhoneSmsLimit($phone, $basekeynum)) {
                $this->logSmsAttempt($phone, $ip, $userId, '发送失败-手机号超出发送限制', $basekeynum, false);
                return Response::json(Response::ERROR, '该手机号发送次数过多，请1小时后再试');
            }
            
            // 4. 发送间隔限制
            if (!$this->checkPhoneSmsInterval($phone, $basekeynum)) {
                $this->logSmsAttempt($phone, $ip, $userId, '发送失败-发送间隔过短', $basekeynum, false);
                return Response::json(Response::ERROR, '发送过于频繁，请60秒后再试');
            }
            
            // 5. 记录本次发送尝试到缓存
            $this->recordSmsAttempt($phone, $ip, $basekeynum);
            
            // 6. 调用原始短信发送函数
            $result = send_phone_code_lizengbang_v2($basekeynum, $phone);
            
            // 7. 记录成功日志
            $this->logSmsAttempt($phone, $ip, $userId, '发送成功', $basekeynum, true);
            
            return Response::json(Response::SUCCESS, '发送短信验证码成功', $result);
            
        } catch (\Exception $e) {
            // 记录异常日志
            $this->logSmsAttempt($phone ?? '', $ip ?? '', $userId ?? 0, '发送异常-' . $e->getMessage(), $basekeynum ?? '', false);
            return Response::json(Response::ERROR, '发送失败，请稍后重试');
        }
    }
    
    /**
     * 检查IP短信发送限制
     * @param string $ip IP地址
     * @return bool
     */
    private function checkIpSmsLimit($ip)
    {
        $ipKey = "sms_ip_limit:{$ip}";
        $count = Cache::get($ipKey, 0);
        return $count < self::SMS_IP_LIMIT;
    }
    
    /**
     * 检查手机号短信发送限制
     * @param string $phone 手机号
     * @param string $basekeynum 商户标识
     * @return bool
     */
    private function checkPhoneSmsLimit($phone, $basekeynum)
    {
        $phoneKey = "sms_phone_limit:{$phone}:{$basekeynum}";
        $count = Cache::get($phoneKey, 0);
        return $count < self::SMS_PHONE_LIMIT;
    }
    
    /**
     * 检查手机号短信发送间隔
     * @param string $phone 手机号
     * @param string $basekeynum 商户标识
     * @return bool
     */
    private function checkPhoneSmsInterval($phone, $basekeynum)
    {
        $intervalKey = "sms_phone_interval:{$phone}:{$basekeynum}";
        return !Cache::get($intervalKey);
    }
    
    /**
     * 记录短信发送尝试
     * @param string $phone 手机号
     * @param string $ip IP地址
     * @param string $basekeynum 商户标识
     */
    private function recordSmsAttempt($phone, $ip, $basekeynum)
    {
        // 记录IP发送次数
        $ipKey = "sms_ip_limit:{$ip}";
        $ipCount = Cache::get($ipKey, 0) + 1;
        Cache::set($ipKey, $ipCount, self::SMS_IP_TIME_WINDOW);
        
        // 记录手机号发送次数
        $phoneKey = "sms_phone_limit:{$phone}:{$basekeynum}";
        $phoneCount = Cache::get($phoneKey, 0) + 1;
        Cache::set($phoneKey, $phoneCount, self::SMS_PHONE_TIME_WINDOW);
        
        // 设置发送间隔锁
        $intervalKey = "sms_phone_interval:{$phone}:{$basekeynum}";
        Cache::set($intervalKey, 1, self::SMS_INTERVAL);
    }
    
    /**
     * 记录短信发送日志
     * @param string $phone 手机号
     * @param string $ip IP地址
     * @param int $userId 用户ID
     * @param string $remark 备注
     * @param string $basekeynum 商户标识
     * @param bool $success 是否成功
     */
    private function logSmsAttempt($phone, $ip, $userId, $remark, $basekeynum, $success)
    {
        try {
            $logData = [
                'phone' => $phone,
                'ip' => $ip,
                'user_id' => $userId,
                'remark' => $remark,
                'basekeynum' => $basekeynum,
                'success' => $success ? 1 : 0,
                'create_time' => time(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'request_method' => $_SERVER['REQUEST_METHOD'] ?? '',
                'request_uri' => $_SERVER['REQUEST_URI'] ?? ''
            ];
            
            // 写入门店短信发送日志表
            Db::table('store_sms_log')->insert($logData);
            
        } catch (\Exception $e) {
            // 日志记录失败不影响主流程，只记录到系统日志
            \think\facade\Log::error('SMS log failed: ' . $e->getMessage());
        }
    }
}
