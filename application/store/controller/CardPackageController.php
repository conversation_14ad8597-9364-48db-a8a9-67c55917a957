<?php
/**
 * 套餐卡兑换控制器
 * Create by Augment Agent
 * Date: 2024-12-19
 */

namespace app\store\controller;

use app\store\lib\Response;
use app\store\model\Card;
use app\store\model\CardPackageRedemption;
use app\store\model\CardPackageRedemptionDetail;
use app\store\model\CardPackageRedemptionProduct;
use app\store\model\Shop;

use think\Controller;
use think\Db;
use think\facade\Log;
use think\Request;

class CardPackageController extends Controller
{
    /**
     * 验证卡号和密码
     */
    public function verifyCard(Request $request)
    {
        $cardNo = $request->param('card_no');
        $password = $request->param('password');
        $user = $request->user_info;
        $keynum = $user['plat_account']['keynum'];
        $parent_basekeynum = $user['plat_account']['basekeynum'];
        $clientkeynum = $request->basekeynum;
        // $clientkeynum = $request->param('clientkeynum');
        $userId = $request->param('user_id', 0);
        $ip = $request->ip();
        $shop = Shop::where(function ($query) use($keynum, $parent_basekeynum) {
            $query->where('keynum', $keynum)
                ->whereOr('keynum', $parent_basekeynum);
        })->find();
        if (empty($shop)) {
            return Response::json(Response::ERROR, '门店不存在');
        }
        $shopId = $shop['id'];
        
        if (empty($cardNo) || empty($password)) {
            return Response::json(Response::ERROR, '参数不能为空');
        }
        
        // IP防刷限制
        if (!$this->checkIpLimit($ip)) {
            return Response::json(Response::ERROR, '请求过于频繁，请稍后再试');
        }
        
        // 查询卡信息
        $cardModel = new Card();
        $card = $cardModel->getCardByCardNo($cardNo, $clientkeynum);
        
        if (empty($card)) {
            $this->logCardVerifyAttempt($cardNo, $ip, $userId, '验证失败-卡号不存在', $clientkeynum, false);
            return Response::json(Response::ERROR, '卡号不存在');
        }
        
        // 验证密码
        if ($card['card_password'] != encrypt($password)) {
            $this->logCardVerifyAttempt($cardNo, $ip, $userId, '验证失败-密码错误', $clientkeynum, false);
            return Response::json(Response::ERROR, '密码错误');
        }
        
        // 验证卡状态
        if ($card['status'] != Card::STATUS_OPENED) {
            $this->logCardVerifyAttempt($cardNo, $ip, $userId, '验证失败-卡状态异常', $clientkeynum, false);
            return Response::json(Response::ERROR, '卡状态异常，无法兑换');
        }
        
        // 验证有效期
        $now = time();
        if ($card['valid_start_time'] && strtotime($card['valid_start_time']) > $now) {
            return Response::json(Response::ERROR, '卡尚未到可使用时间');
        }
        
        if ($card['valid_end_time'] && strtotime($card['valid_end_time']) < $now) {
            return Response::json(Response::ERROR, '卡已过期');
        }
        
        // 获取卡型关联的套餐
        $cardTypeId = $card['type_id'];
        $packages = Db::name('card_type_package_relation')
            ->alias('r')
            ->join('card_package p', 'r.package_id = p.id')
            ->where('r.type_id', $cardTypeId)
            ->where('r.clientkeynum', $clientkeynum)
            ->where('p.status', 1)
            ->field('p.*')
            ->select();
        
        // 获取每个套餐包含的商品
        foreach ($packages as &$package) {
            $products = Db::name('card_package_product')
                ->alias('pp')
                ->join('product_inventory pi', 'pp.product_inventory_id = pi.id')
                ->join('products p', 'pi.product_id = p.id')
                ->where('pp.package_id', $package['id'])
                ->where('pp.clientkeynum', $clientkeynum)
                ->field('pi.*, pp.quantity, p.title as product_name')
                ->select();
            
            $package['products'] = $products;
        }
        
        $data = [
            'card' => $card,
            'packages' => $packages
        ];
        
        // 记录验证成功日志
        $this->logCardVerifyAttempt($cardNo, $ip, $userId, '验证成功', $clientkeynum, true);
        
        return Response::json(Response::SUCCESS, '验证成功', $data);
    }
    
    /**
     * 兑换套餐
     */
    public function redeemPackage(Request $request)
    {
        $user = $request->user_info;
        $keynum = $user['plat_account']['keynum'];
        $clientkeynum = $request->basekeynum;
        $cardId = $request->param('card_id');
        $packageId = $request->param('package_id');
        $redeemType = $request->param('redeem_type', CardPackageRedemption::TYPE_STORE);
        // $clientkeynum = $request->param('clientkeynum');
        $userId = $request->param('user_id', 0);
        $ip = $request->ip();
        $parent_basekeynum = $user['plat_account']['basekeynum'];
        $shop = Shop::where(function ($query) use($keynum, $parent_basekeynum) {
            $query->where('keynum', $keynum)
                ->whereOr('keynum', $parent_basekeynum);
        })->find();
        if (empty($shop)) {
            return Response::json(Response::ERROR, '门店不存在');
        }
        $shopId = $shop['id'];
        // 快递配送相关参数
        // $name = $request->param('name');
        // $phone = $request->param('phone');
        // $province = $request->param('province');
        // $city = $request->param('city');
        // $area = $request->param('area');
        // $address = $request->param('address');
        // $postalCode = $request->param('postal_code');
        
        // 到店兑换相关参数
        // $shopId = $request->param('shop_id');
        
        // 防重复提交
        $redeemKey = 'card_redeem_' . $cardId . '_' . $userId;
        if (cache($redeemKey)) {
            return Response::json(Response::ERROR, '请勿重复提交');
        }
        cache($redeemKey, 1, 10); // 10秒防重复
        
        // 参数验证
        if (empty($cardId) || empty($packageId) || empty($clientkeynum)) {
            cache($redeemKey, null);
            return Response::json(Response::ERROR, '参数错误');
        }
        
        
        
        // 快递配送时必须填写收货信息
        if ($redeemType == CardPackageRedemption::TYPE_EXPRESS) {
            if (empty($name) || empty($phone) || empty($province) || empty($city) || empty($area) || empty($address)) {
                cache($redeemKey, null);
                return Response::json(Response::ERROR, '请填写完整的收货信息');
            }
        }
        
        // 到店兑换时必须选择门店
        if ($redeemType == CardPackageRedemption::TYPE_STORE && empty($shopId)) {
            cache($redeemKey, null);
            return Response::json(Response::ERROR, '请选择兑换门店');
        }
        
        // 查询卡信息
        $cardModel = new Card();
        $card = $cardModel->where('id', $cardId)
            ->where('clientkeynum', $clientkeynum)
            ->find();
        
        if (empty($card)) {
            cache($redeemKey, null);
            return Response::json(Response::ERROR, '卡信息不存在');
        }
        
        // 验证卡状态
        if ($card['status'] != Card::STATUS_OPENED) {
            cache($redeemKey, null);
            return Response::json(Response::ERROR, '卡状态异常，无法兑换');
        }
        
        // 验证套餐是否与卡型匹配
        $cardTypeId = $card['type_id'];
        $matchedPackages = Db::name('card_type_package_relation')
            ->where('type_id', $cardTypeId)
            ->where('clientkeynum', $clientkeynum)
            ->column('package_id');
        

        if(!in_array($packageId, $matchedPackages)){
            cache($redeemKey, null);
            return Response::json(Response::ERROR, '选择的套餐与卡型不匹配');
        }

        // foreach ($packageIds as $packageId) {
        //     if (!in_array($packageId, $matchedPackages)) {
        //         cache($redeemKey, null);
        //         return Response::json(Response::ERROR, '选择的套餐与卡型不匹配');
        //     }
        // }
        
        // 开始兑换处理
        Db::startTrans();
        try {
            // 1. 创建兑换主记录
            $redemption = new CardPackageRedemption();
            $redemption->clientkeynum = $clientkeynum;
            $redemption->card_id = $cardId;
            $redemption->user_id = $userId;
            $redemption->redemption_time = date('Y-m-d H:i:s');
            $redemption->status = CardPackageRedemption::STATUS_REDEEMED;
            $redemption->type = $redeemType;
            $redemption->add_time = date('Y-m-d H:i:s');
            
            // 设置快递配送信息
            // if ($redeemType == CardPackageRedemption::TYPE_EXPRESS) {
            //     $redemption->name = $name;
            //     $redemption->phone = $phone;
            //     $redemption->province = $province;
            //     $redemption->city = $city;
            //     $redemption->area = $area;
            //     $redemption->address = $address;
            //     $redemption->postal_code = $postalCode;
            // } else {
                // 设置到店兑换信息
                $redemption->shop_id = $shopId;
            // }
            
            $redemption->save();
            $redemptionId = $redemption->id;
            
            // 2. 创建兑换明细记录
            // foreach ($packageIds as $packageId) {
                // 获取套餐信息
                $package = Db::name('card_package')->where('id', $packageId)->find();
                
                if (empty($package)) {
                    Db::rollback();
                    cache($redeemKey, null);
                    return Response::json(Response::ERROR, '套餐信息不存在');
                }
                
                // // 计算有效期
                // $validStartTime = date('Y-m-d H:i:s');
                // $validEndTime = date('Y-m-d H:i:s', strtotime("+{$package['validity_days']} day"));
                
                // 创建套餐兑换明细
                $detail = new CardPackageRedemptionDetail();
                $detail->clientkeynum = $clientkeynum;
                $detail->redemption_id = $redemptionId;
                $detail->package_id = $packageId;
                $detail->status = CardPackageRedemption::STATUS_REDEEMED;
                // $detail->valid_start_time = $validStartTime;
                // $detail->valid_end_time = $validEndTime;
                $detail->add_time = date('Y-m-d H:i:s');
                $detail->save();
                
                // 3. 创建兑换商品记录
                $packageProducts = Db::name('card_package_product')
                    ->where('package_id', $packageId)
                    ->where('clientkeynum', $clientkeynum)
                    ->select();
                
                $outStockDetails = [];
                foreach ($packageProducts as $product) {
                    $redemptionProduct = new CardPackageRedemptionProduct();
                    $redemptionProduct->clientkeynum = $clientkeynum;
                    $redemptionProduct->redemption_id = $redemptionId;
                    $redemptionProduct->product_inventory_id = $product['product_inventory_id'];
                    $redemptionProduct->quantity = $product['quantity'];
                    $redemptionProduct->add_time = date('Y-m-d H:i:s');
                    $redemptionProduct->save();
                    
                    // 获取商品信息和库存信息
                    $inventory = Db::name('product_inventory')
                        ->alias('pi')
                        ->join('products p', 'pi.product_id = p.id')
                        ->where('pi.id', $product['product_inventory_id'])
                        ->where('pi.clientkeynum', $clientkeynum)
                        ->field('pi.*, p.title as product_name, p.product_type')
                        ->find();
                    
                    if (empty($inventory)) {
                        throw new \Exception('商品库存信息不存在');
                    }
                    
                    // 获取门店库存信息
                    $shopInventory = Db::name('shop_product_inventory')
                        ->where('shop_id', $shopId)
                        ->where('product_id', $inventory['product_id'])
                        ->where('inventory_id', $product['product_inventory_id'])
                        ->where('clientkeynum', $clientkeynum)
                        ->find();
                    
                    // 如果门店库存不存在，则创建
                    if (empty($shopInventory)) {
                        $shopInventory = [
                            'shop_id' => $shopId,
                            'product_id' => $inventory['product_id'],
                            'inventory_id' => $product['product_inventory_id'],
                            'clientkeynum' => $clientkeynum,
                            'stock' => 0,
                            'weight_stock' => 0,
                            'add_time' => date('Y-m-d H:i:s'),
                            'update_time' => date('Y-m-d H:i:s')
                        ];
                        
                        $shopInventoryId = Db::name('shop_product_inventory')->insertGetId($shopInventory);
                        $shopInventory['id'] = $shopInventoryId;
                    }
                    
                    // 验证库存是否足够
                    $quantity = $product['quantity'];
                    $productType = $inventory['product_type'] ?? 1;
                    
                    // 检查库存是否足够
                    if ($productType == 2) {
                        // 计量商品
                        if ($shopInventory['weight_stock'] < $quantity) {
                            throw new \Exception('商品"' . $inventory['product_name'] . '"库存不足，当前库存：' . $shopInventory['weight_stock'] . 'kg，需要：' . $quantity . 'kg');
                        }
                    } else {
                        // 普通商品
                        if ($shopInventory['stock'] < $quantity) {
                            throw new \Exception('商品"' . $inventory['product_name'] . '"库存不足，当前库存：' . $shopInventory['stock'] . '件，需要：' . $quantity . '件');
                        }
                    }
                    
                    // 根据商品类型处理库存扣减
                    if ($productType == 2) {
                        // 计量商品：扣减门店重量库存
                        $beforeStock = $shopInventory['weight_stock'];
                        $afterStock = $beforeStock - $quantity;
                        
                        // 更新门店库存
                        $result = Db::name('shop_product_inventory')
                            ->where('id', $shopInventory['id'])
                            ->update([
                                'weight_stock' => $afterStock,
                                'update_time' => date('Y-m-d H:i:s')
                            ]);
                        
                        if (!$result) {
                            throw new \Exception('更新商品库存失败');
                        }
                        
                        // 记录库存日志 - 使用标准格式
                        Db::name('inventory_log')->insert([
                            'clientkeynum' => $clientkeynum,
                            'shop_id' => $shopId,
                            'product_id' => $inventory['product_id'],
                            'inventory_id' => $product['product_inventory_id'],
                            'before_quantity' => $beforeStock,
                            'change_quantity' => -$quantity, // 出库为负数
                            'after_quantity' => $afterStock,
                            'change_type' => 5, // 销售出库
                            'related_id' => $redemptionId,
                            'related_no' => 'CPR' . $redemptionId, // 套餐卡兑换编号
                            'remark' => '套餐卡兑换扣减库存',
                            'operator' => '用户',
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                        
                        // 同时记录到shop_product_inventory_log表，保持两个日志表的一致性
                        Db::name('shop_product_inventory_log')->insert([
                            'clientkeynum' => $clientkeynum,
                            'shop_id' => $shopId,
                            'product_inventory_id' => $product['product_inventory_id'],
                            'type' => 2, // 减库存
                            'quantity' => $quantity,
                            'before_stock' => $beforeStock,
                            'after_stock' => $afterStock,
                            'remark' => '套餐卡兑换扣减库存，兑换ID：' . $redemptionId,
                            'operator_id' => $userId,
                            'operator' => '用户',
                            'add_time' => date('Y-m-d H:i:s')
                        ]);
                    } else {
                        // 普通商品：扣减门店数量库存
                        $beforeStock = $shopInventory['stock'];
                        $afterStock = $beforeStock - $quantity;
                        
                        // 更新门店库存
                        $result = Db::name('shop_product_inventory')
                            ->where('id', $shopInventory['id'])
                            ->update([
                                'stock' => $afterStock,
                                'update_time' => date('Y-m-d H:i:s')
                            ]);
                        
                        if (!$result) {
                            throw new \Exception('更新商品库存失败');
                        }
                        
                        // 记录库存日志 - 使用标准格式
                        Db::name('inventory_log')->insert([
                            'clientkeynum' => $clientkeynum,
                            'shop_id' => $shopId,
                            'product_id' => $inventory['product_id'],
                            'inventory_id' => $product['product_inventory_id'],
                            'before_quantity' => $beforeStock,
                            'change_quantity' => -$quantity, // 出库为负数
                            'after_quantity' => $afterStock,
                            'change_type' => 5, // 销售出库
                            'related_id' => $redemptionId,
                            'related_no' => 'CPR' . $redemptionId, // 套餐卡兑换编号
                            'remark' => '套餐卡兑换扣减库存',
                            'operator' => '用户',
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                        
                        // 同时记录到shop_product_inventory_log表，保持两个日志表的一致性
                        Db::name('shop_product_inventory_log')->insert([
                            'clientkeynum' => $clientkeynum,
                            'shop_id' => $shopId,
                            'product_inventory_id' => $product['product_inventory_id'],
                            'type' => 2, // 减库存
                            'quantity' => $quantity,
                            'before_stock' => $beforeStock,
                            'after_stock' => $afterStock,
                            'remark' => '套餐卡兑换扣减库存，兑换ID：' . $redemptionId,
                            'operator_id' => $userId,
                            'operator' => '用户',
                            'add_time' => date('Y-m-d H:i:s')
                        ]);
                    }
                    
                    // 收集出库单明细数据
                    $outStockDetails[] = [
                        'product_id' => $inventory['product_id'],
                        'inventory_id' => $product['product_inventory_id'],
                        'quantity' => $quantity,
                        'price' => $inventory['price'] ?? 0,
                        'remark' => '套餐卡兑换商品'
                    ];
                }
                
                // 创建标准出库单
                $orderNo = 'CPOUT' . date('YmdHis') . mt_rand(1000, 9999);
                
                // 创建出库单主表记录
                $outStockOrderData = [
                    'order_type' => 2, // 出库单
                    'business_type' => 4, // 其他出库
                    'order_no' => $orderNo,
                    'shop_id' => $shopId,
                    'clientkeynum' => $clientkeynum,
                    'status' => 3, // 已完成
                    'remark' => '套餐卡兑换出库，兑换ID：' . $redemptionId,
                    'created_by' => '用户',
                    'created_at' => date('Y-m-d H:i:s'),
                    'reviewed_by' => '系统',
                    'reviewed_time' => date('Y-m-d H:i:s'),
                    'completed_time' => date('Y-m-d H:i:s')
                ];
                
                $outStockId = Db::name('inventory_order')->insertGetId($outStockOrderData);
                
                // 计算总金额
                $totalAmount = 0;
                
                // 创建出库单明细
                $outStockDetailData = [];
                foreach ($outStockDetails as $detail) {
                    $amount = round($detail['quantity'] * $detail['price'], 2);
                    $totalAmount += $amount;
                    
                    $outStockDetailData[] = [
                        'order_id' => $outStockId,
                        'order_no' => $orderNo,
                        'order_type' => 2, // 出库单
                        'shop_id' => $shopId,
                        'product_id' => $detail['product_id'],
                        'inventory_id' => $detail['inventory_id'],
                        'quantity' => $detail['quantity'],
                        'price' => $detail['price'],
                        'amount' => $amount,
                        'remark' => $detail['remark'],
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                
                // 批量插入出库单明细
                if (!empty($outStockDetailData)) {
                    Db::name('inventory_order_detail')->insertAll($outStockDetailData);
                }
                
                // 更新出库单总金额
                Db::name('inventory_order')->where('id', $outStockId)->update([
                    'total_amount' => $totalAmount,
                    'actual_amount' => $totalAmount
                ]);
            // }
            
            // 4. 更新卡状态为已兑换
            $cardModel->updateCardStatus(
                $cardId,
                Card::STATUS_REDEEMED,
                $userId,
                '用户',
                '套餐兑换',
                '用户进行套餐兑换',
                $clientkeynum
            );
            
            // 5. 记录兑换日志
            $this->logCardRedemption($cardId, $ip, $userId, $packageId, '兑换成功', $clientkeynum, true, $redemptionId);
            
            Db::commit();
            cache($redeemKey, null);
            
            return Response::json(Response::SUCCESS, '兑换成功', ['redemption_id' => $redemptionId]);
        } catch (\Exception $e) {
            Db::rollback();
            cache($redeemKey, null);
            
            Log::error('套餐卡兑换异常: ' . $e->getMessage());
            $this->logCardRedemption($cardId, $ip, $userId, $packageId, '兑换失败-系统异常: ' . $e->getMessage(), $clientkeynum, false);
            
            return Response::json(Response::ERROR, '兑换失败，系统异常');
        }
    }
    
    /**
     * 获取兑换记录
     */
    public function getRedemptionRecords(Request $request)
    {
        $user = $request->user_info;
        $keynum = $user['plat_account']['keynum'];
        $clientkeynum = $request->basekeynum;
        $page = $request->param('page', 1);
        $pageSize = $request->param('page_size', 10);
        
        if (empty($clientkeynum)) {
            return Response::json(Response::ERROR, '参数错误');
        }
        
        // 查询兑换记录
        $redemptionModel = new CardPackageRedemption();
        $count = $redemptionModel
            ->where('clientkeynum', $clientkeynum)
            ->count();
        
        $records = $redemptionModel->with(['details', 'products', 'card', 'shop'])
            ->where('clientkeynum', $clientkeynum)
            ->order('redemption_time', 'desc')
            ->page($page, $pageSize)
            ->select();
        
        // 处理数据，获取套餐和商品详情
        foreach ($records as &$record) {
            // 获取套餐详情
            foreach ($record['details'] as &$detail) {
                $package = Db::name('card_package')->where('id', $detail['package_id'])->find();
                $detail['package'] = $package;
            }
        
            // 获取商品详情
            foreach ($record['products'] as &$product) {
                $productInfo = Db::name('product_inventory')
                    ->alias('pi')
                    ->join('products p', 'pi.product_id = p.id')
                    ->where('pi.id', $product['product_inventory_id'])
                    ->field('pi.*, p.title as product_name')
                    ->find();
                $product['product_info'] = $productInfo;
            }
            $record['type_text'] = $redemptionModel->getTypeText($record['type']);
            $record['status_text'] = $redemptionModel->getStatusText($record['status']);
        }
        
        $data = [
            'total' => $count,
            'page' => (int)$page,
            'page_size' => (int)$pageSize,
            'list' => $records
        ];
        
        return Response::json(Response::SUCCESS, '获取成功', $data);
    }
    
    /**
     * 获取兑换详情
     */
    public function getRedemptionDetail(Request $request)
    {
        $user = $request->user_info;
        $keynum = $user['plat_account']['keynum'];
        $clientkeynum = $request->basekeynum;
        $redemptionId = $request->param('redemption_id');
        
        if (empty($redemptionId) || empty($clientkeynum)) {
            return Response::json(Response::ERROR, '参数错误');
        }
        
        // 查询兑换记录
        $redemptionModel = new CardPackageRedemption();
        $record = $redemptionModel->with(['details', 'products', 'card', 'shop'])
            ->where('id', $redemptionId)
            ->where('clientkeynum', $clientkeynum)
            ->find();
        
        if (empty($record)) {
            return Response::json(Response::ERROR, '兑换记录不存在');
        }
        
        // 获取套餐详情
        foreach ($record['details'] as &$detail) {
            $package = Db::name('card_package')->where('id', $detail['package_id'])->find();
            $detail['package'] = $package;
        }
        
        // 获取商品详情
        foreach ($record['products'] as &$product) {
            $productInfo = Db::name('product_inventory')
                ->alias('pi')
                ->join('products p', 'pi.product_id = p.id')
                ->where('pi.id', $product['product_inventory_id'])
                ->field('pi.*, p.title as product_name')
                ->find();
            $product['product_info'] = $productInfo;
        }

        $record['type_text'] = $redemptionModel->getTypeText($record['type']);
        $record['status_text'] = $redemptionModel->getStatusText($record['status']);
        
        return Response::json(Response::SUCCESS, '获取成功', $record);
    }
    
    /**
     * 获取门店列表
     */
    public function getShops(Request $request)
    {
        $user = $request->user_info;
        $keynum = $user['plat_account']['keynum'];
        $clientkeynum = $request->basekeynum;
        
        if (empty($clientkeynum)) {
            return Response::json(Response::ERROR, '参数错误');
        }
        
        $shops = Db::name('shop')
            ->where('clientkeynum', $clientkeynum)
            ->where('status', 1)
            ->field('id, title, address, phone')
            ->select();
        
        return Response::json(Response::SUCCESS, '获取成功', $shops);
    }
    
    /**
     * IP限制检查
     */
    private function checkIpLimit($ip)
    {
        $key = 'card_verify_ip_' . $ip;
        $count = cache($key) ?: 0;
        
        if ($count >= 10) { // 每分钟最多10次
            return false;
        }
        
        cache($key, $count + 1, 60);
        return true;
    }
    
    /**
     * 记录卡验证尝试日志
     */
    private function logCardVerifyAttempt($cardNo, $ip, $userId, $remark, $clientkeynum, $isSuccess)
    {
        $data = [
            'clientkeynum' => $clientkeynum,
            'card_no' => $cardNo,
            'user_id' => $userId,
            'ip' => $ip,
            'verify_time' => date('Y-m-d H:i:s'),
            'status' => $isSuccess ? 1 : 0,
            'remark' => $remark,
            'add_time' => date('Y-m-d H:i:s')
        ];
        
        return Db::name('card_verify_log')->insertGetId($data);
    }
    
    /**
     * 记录卡兑换日志
     */
    private function logCardRedemption($cardId, $ip, $userId, $packageIds, $remark, $clientkeynum, $isSuccess, $redemptionId = 0)
    {
        // 获取卡号
        $cardNo = '';
        $card = Db::name('card')->where('id', $cardId)->find();
        if ($card) {
            $cardNo = $card['card_no'];
        }
        
        // 格式化套餐ID
        if (is_array($packageIds)) {
            $packageIds = implode(',', $packageIds);
        }
        
        $data = [
            'clientkeynum' => $clientkeynum,
            'card_id' => $cardId,
            'card_no' => $cardNo,
            'user_id' => $userId,
            'ip' => $ip,
            'package_ids' => $packageIds,
            'redemption_id' => $redemptionId,
            'redemption_time' => date('Y-m-d H:i:s'),
            'status' => $isSuccess ? 1 : 0,
            'remark' => $remark,
            'add_time' => date('Y-m-d H:i:s')
        ];
        
        return Db::name('card_redemption_log')->insertGetId($data);
    }
} 