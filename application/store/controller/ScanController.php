<?php

/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/10/14 00:09
 */

namespace app\store\controller;


use app\api\model\OrderLog;
use app\store\lib\Response;
use app\store\model\Ycard;
use app\store\model\Shop;
use app\store\model\StoreCheckCardnumLog;
use think\Controller;
use think\Exception;
use think\facade\Log;
use think\Request;
use app\api\model\CardUseLog;
use think\Db;
use app\store\model\CardLevel;

class ScanController extends Controller
{
    public function checkCard(Request $request)
    {
        $user = $request->user_info;
        $user_id = $request->param('user_id');
        $cardnum = $request->param('cardnum');
        $money = $request->param('money');
        $remark = $request->param('remark');
        $basekeynum = $user['plat_account']['keynum'];
        $parent_basekeynum = $user['plat_account']['basekeynum'];
        $shop = Shop::where(function ($query) use($basekeynum, $parent_basekeynum) {
            $query->where('keynum', $basekeynum)
                ->whereOr('keynum', $parent_basekeynum);
        })->find();

        if (empty($shop)) fail(-1, '门店不存在');


        if (empty($user_id) || empty($cardnum) || empty($money) || empty($remark)) {
            return Response::json(Response::ERROR, '参数不能为空');
        }
        $cardnum = urldecode($cardnum);
        // 查询该储值卡是否存在
        $cards = Ycard::where(['member_id' => $user_id])->whereIn('cardnum', $cardnum)->select();
        if (empty($cards)) fail(-1, '储值卡不存在');

        $remaining_money = $money;
        try {
            \think\Db::startTrans();
            foreach ($cards as $card) {
                // 判断是否在有效期
                if ($card['begin_dui'] > time() || $card['end_dui'] < time()) {
                    throw new Exception("卡号【{$card['cardnum']}】 不在有效期");
                }
                // 判断是否是开卡状态
                if ($card['status'] != 3) {
                    throw new Exception("卡号【{$card['cardnum']}】 未开卡");
                }

                // 当前卡是否有足够的余额
                if ($card['yu_money'] > $remaining_money) {
                    // 余额足够的部分使用
                    $use_money = $remaining_money;
                    Ycard::where(['cardnum' => $card['cardnum']])->update(['yu_money' => Db::raw("yu_money-$use_money")]);
                    // 记录使用日志
                    CardUseLog::create([
                        'member_id' => $user_id,
                        'cardnum' => $card['cardnum'],
                        'yu_money' => $card['yu_money'] - $remaining_money,
                        'use_money' => $use_money,
                        'remark' => '门店核销：' . $remark,
                        'basekeynum' => $user['plat_account']['parent_basekeynum'],
                        'add_time' => date('Y-m-d H:i:s'),
                    ]);

                    StoreCheckCardnumLog::create([
                        'user_id' => $user_id,
                        'cardnum' => $card['cardnum'],
                        'money' => $use_money,
                        'accountname' => $user['plat_account']['accountname'],
                        'shop_id' => $shop['id'],
                        'remark' => '门店核销：' . $remark,
                    ]);
                    $remaining_money = 0;
                    break;
                } else {
                    Ycard::where(['cardnum' => $card['cardnum']])->update(['yu_money' => 0]);
                    $remaining_money -= $card['yu_money'];
                    // 移除卡内的余额， 记录使用记录
                    CardUseLog::create([
                        'member_id' => $user_id,
                        'cardnum' => $card['cardnum'],
                        'yu_money' => 0,
                        'aftermoney' => $card['yu_money'],
                        'use_money' => $card['yu_money'],
                        'remark' => '门店核销：' . $remark,
                        'basekeynum' => $user['plat_account']['parent_basekeynum'],
                        'add_time' => date('Y-m-d H:i:s'),
                    ]);
                    StoreCheckCardnumLog::create([
                        'user_id' => $user_id,
                        'cardnum' => $card['cardnum'],
                        'money' => $card['yu_money'],
                        'accountname' => $user['plat_account']['accountname'],
                        'shop_id' => $shop['id'],
                        'remark' => '门店核销：' . $remark,
                    ]);
                }

                if ($remaining_money >= 0) {
                    throw new Exception("金额不足！");
                }
            }
            \think\Db::commit();
            return Response::json(Response::SUCCESS, '核销成功');
        } catch (\Exception $e) {
            Log::info($e->getMessage());;
            \think\Db::rollback();
            return Response::json(Response::ERROR, $e->getMessage());
        }
    }

    public function getYcardByPassword(Request $request)
    {
        $clientkeynum = $request->basekeynum;
        $cardnum = $request->param('cardnum');
        $card_password = $request->param('card_password');

        // 查询该储值卡是否存在
        $card = Ycard::where(['cardnum' => $cardnum, 'clientkeynum' => $clientkeynum])->with(['card_level', 'user'])->find();
        if (empty($card)) {
            return Response::json(Response::ERROR, '卡号不存在');
        }

        if ($card['cardpwd'] != encrypt($card_password)) {
            return Response::json(Response::ERROR, '卡密码错误');
        }

        if ($card['status'] != 3) {
            return Response::json(Response::ERROR, '卡未开卡');
        }

        if ($card['begin_dui'] > time() || $card['end_dui'] < time()) {
            return Response::json(Response::ERROR, '卡不在有效期');
        }

        return Response::json(Response::SUCCESS, '查询成功', $card);
    }

    public function getUserCard(Request $request)
    {
        $user_id = $request->param('user_id');
        $page = $request->param('page', 1);
        $page_size = $request->param('page_size', 10);
        // 查询该用户的储值卡信息
        $list = Ycard::where(['member_id' => $user_id])->page($page, $page_size)->where('yu_money', '>', 0)
            ->where('status', 3)->where(function ($query) {
                $query->where('begin_dui', '<', time())
                    ->where('end_dui', '>', time());
            })->order('end_dui', 'asc')->select();
        $count = Ycard::where(['member_id' => $user_id])->where('yu_money', '>', 0)
            ->where('status', 3)->where(function ($query) {
                $query->where('begin_dui', '<', time())
                    ->where('end_dui', '>', time());
            })->count();
        success(0, '查询成功', ['list' => $list, 'count' => $count]);
    }


    public function scanUser(Request $request)
    {
        $keyword = $request->param('keyword');

        // 查询用户是否存在
        $user = \app\store\model\User::with(['ycard'])->where('qrcode_value', $keyword)->whereOr('phone', $keyword)->find();

        if (!empty($user['ycard']) && $user['ycard']['card_level_id'] > 0) {
            $user['ycard']['cardLevel'] = CardLevel::where('id', $user['ycard']['card_level_id'])->find();
        }

        if ($user) {
            success(0, '扫码成功', ['user' => $user]);
        } else {
            fail(-1, '扫码失败，用户不存在');
        }
    }

    public function scanOrder(Request $request)
    {
        $clientkeynum = $request->basekeynum;
        $order_no = $request->param('order_no');

        // 查询该订单是否存在
        $order = \app\store\model\Order::where('verify_code', $order_no)
            ->where('clientkeynum', $clientkeynum)
            ->with(['orderDetail', 'shop', 'user'])
            ->find();

        if (!$order) {
            fail(-1, '订单不存在');
        }

        if ($order->type != 1) {
            fail(-1, '当前订单不是自提订单！');
        }

        if ($order->status == 100) {
            fail(-1, '该订单已核销');
        }

        if ($order->status != 1) {
            fail(-1, '当前订单状态不为待核销');
        }

        if ($order) {
            success(0, '扫码成功', ['order' => $order]);
        } else {
            fail(-1, '扫码失败，订单不存在');
        }
    }

    // 核销订单
    public
    function writeOffOrder(Request $request)
    {
        $user = $request->user_info;
        $order_id = $request->param('order_id');

        $order = \app\store\model\Order::where('id', $order_id)->find();

        if (!$order) {
            fail(-1, '订单不存在');
        }

        if ($order->type != 1) {
            fail(-1, '当前订单不是自提订单！');
        }

        if ($order->status == 100) {
            fail(-1, '该订单已核销');
        }

        if ($order->status != 1) {
            fail(-1, '当前订单状态不为待核销');
        }


        $order->status = 100;
        $result = $order->save();

        OrderLog::create([
            'order_no' => $order['order_no'],
            'clientkeynum' => $order['clientkeynum'],
            'content' => '商家核销订单',
            'operator' => $user['plat_account']['accountname'],
            'add_time' => date('Y-m-d H:i:s')
        ]);


        if ($result) {
            success(0, '订单已核销');
        } else {
            fail(-1, '更新订单状态失败');
        }
    }
}
