<?php
/**
 * Create by AI
 * Date:2024/10/25
 */

namespace app\store\controller;

use app\api\model\OrderLog;
use app\store\lib\Wechat;
use app\store\model\Order;
use app\store\model\OrderPayLog;
use app\store\model\PlatWechatSet;
use think\Controller;
use think\Db;
use think\facade\Log;

class NotifyController extends Controller
{
    /**
     * 微信支付通知处理
     */
    public function wechat_notify()
    {
        $request = file_get_contents('php://input');
        Log::info('微信支付回调原始数据: ' . $request);
        
        // 解析XML数据
        $temp = json_encode(simplexml_load_string($request, 'SimpleXMLElement', LIBXML_NOCDATA));
        $data = json_decode($temp, true);
        Log::info('微信支付回调解析数据: ' . json_encode($data));
        
        // 验证支付日志
        $pay_log = OrderPayLog::where('id', '=', $data['out_trade_no'])->find();
        if (empty($pay_log)) {
            return $this->returnXml('FAIL', '订单不存在');
        }
        
        // 获取微信支付配置
        $wechat_config = PlatWechatSet::getInfoByKeyNum($pay_log['clientkeynum']);
        if (empty($wechat_config)) {
            return $this->returnXml('FAIL', '支付配置不存在');
        }
        
        $app = Wechat::getPaymentWechatInstance($wechat_config);
        
        // 使用EasyWeChat处理微信支付通知
        $response = $app->handlePaidNotify(function($message, $fail) {
            // 查询支付日志
            $pay_log = OrderPayLog::where('id', '=', $message['out_trade_no'])->find();
            if (empty($pay_log)) {
                return $fail('订单不存在');
            }
            
            // 如果已经处理过，直接返回成功
            if ($pay_log['is_pay'] == 1) {
                return true;
            }
            
            // 检查支付状态
            if ($message['return_code'] === 'SUCCESS') {
                // 检查业务结果
                if ($message['result_code'] === 'SUCCESS') {
                    try {
                        Db::startTrans();
                        
                        // 更新支付日志
                        $pay_log['is_pay'] = 1;
                        $pay_log['pay_time'] = date('Y-m-d H:i:s');
                        $pay_log['transaction_id'] = $message['transaction_id'];
                        $pay_log['real_price'] = $message['total_fee'] / 100; // 单位：分
                        $pay_log->save();
                        
                        // 更新订单状态
                        Order::where('order_no', '=', $pay_log['order_no'])->update([
                            'status' => 1, // 已支付状态
                            'pay_time' => date('Y-m-d H:i:s'),
                            'transaction_id' => $message['transaction_id'],
                            'real_price' => $message['total_fee'] / 100  // 单位：分
                        ]);
                        
                        // 记录订单日志
                        OrderLog::create([
                            'order_no' => $pay_log['order_no'],
                            'clientkeynum' => $pay_log['clientkeynum'],
                            'content' => '微信支付成功',
                            'operator' => '系统',
                            'add_time' => date('Y-m-d H:i:s')
                        ]);
                        
                        Db::commit();
                        
                    } catch (\Exception $e) {
                        Db::rollback();
                        Log::error('支付通知处理异常: ' . $e->getMessage());
                        return $fail('处理失败');
                    }
                } else {
                    // 支付失败
                    Log::error('支付失败: ' . json_encode($message));
                    return $fail('支付结果失败');
                }
            } else {
                return $fail('通信失败');
            }
            
            return true; // 处理成功，返回true
        });
        
        // 返回响应
        return $response;
    }
    
    /**
     * 返回XML格式响应
     *
     * @param string $return_code 返回码
     * @param string $return_msg 返回消息
     * @return string XML响应
     */
    private function returnXml($return_code, $return_msg)
    {
        return '<xml><return_code><![CDATA[' . $return_code . ']]></return_code><return_msg><![CDATA[' . $return_msg . ']]></return_msg></xml>';
    }
} 