<?php

/**
 * 结算台控制器
 * Create by Augment Agent
 * Date: 2024-10-15
 */

namespace app\store\controller;

use app\store\lib\Response;
use app\store\model\Product;
use app\store\model\ProductInventory;
use app\store\model\ShopProductInventory;
use app\store\model\Shop;
use app\store\model\User;
use think\Controller;
use think\Exception;
use think\facade\Cache;
use think\facade\Log;
use think\Request;

class CheckoutController extends Controller
{
    /**
     * 扫描商品二维码
     *
     * @param Request $request
     * @return \think\response\Json
     */
    public function scanProduct(Request $request)
    {
        $user = $request->user_info;
        $basekeynum = $user['plat_account']['keynum'];
        $parent_basekeynum = $user['plat_account']['basekeynum'];
        $clientkeynum = $request->basekeynum;
        $product_code = $request->param('product_code'); // 商品二维码内容

        if (empty($product_code)) {
            return Response::json(Response::ERROR, '商品二维码不能为空');
        }

        // 判断是否为计量商品（13位或18位二维码）
        // 取头两位和头三位 判断不是 69码 就是计量商品
        $product_sn = substr($product_code, 0, 2);
        $product_sn2 = substr($product_code, 0, 3);

        if ((strlen($product_code) == 13 || strlen($product_code) == 18) && $product_sn != '69' && $product_sn2 != '069') {
            return $this->handleWeightedProduct($product_code, $clientkeynum, $basekeynum, $parent_basekeynum);
        }

        // 取前三位判断是否为条码商品
        $product_sn = substr($product_code, 0, 3);
        if ($product_sn == '069' && strlen($product_code) == 14) {
            // 去掉 0 前缀
            $product_code = substr($product_code, 1, strlen($product_code) - 1);
        }



        // 查询商品信息
        $product = ProductInventory::with(['product'])->where(['clientkeynum' => $clientkeynum])
            ->where(function ($query) use ($product_code) {
                $query->where('id', $product_code)
                    ->whereOr('sn', $product_code);
            })
            ->find();

        if (empty($product)) {
            return Response::json(Response::ERROR, '未找到商品信息');
        }

        // 获取门店信息
        $shop = Shop::where(function ($query) use($basekeynum, $parent_basekeynum) {
            $query->where('keynum', $basekeynum)
                ->whereOr('keynum', $parent_basekeynum);
        })->find();
        if (empty($shop)) {
            return Response::json(Response::ERROR, '门店不存在');
        }

        $shopInventory = ShopProductInventory::where([
            'product_id' => $product['product_id'],
            'shop_id' => $shop['id'],
            'inventory_id' => $product['id']
        ])->find();

        if (empty($shopInventory) || $shopInventory['stock'] <= 0) {
            return Response::json(Response::ERROR, '该商品在当前门店无库存或者未分配当前门店');
        }

        // 获取详细的库存规格信息
        $productInventory = ProductInventory::where([
            'product_id' => $product['product_id'],
            'id' => $shopInventory['inventory_id']
        ])->find();

        if (empty($productInventory)) {
            return Response::json(Response::ERROR, '商品库存信息不完整');
        }

        // 合并库存信息
        $inventory = array_merge($productInventory->toArray(), [
            'shop_inventory_id' => $shopInventory['id'],
            'stock' => $shopInventory['stock']
        ]);

        return Response::json(Response::SUCCESS, '扫描成功', [
            'product' => $product['product'],
            'inventory' => $inventory,
            'is_multi_spec' => false,
            'is_weighted' => false
        ]);
    }

    /**
     * 处理计量商品扫描
     *
     * @param string $product_code 18位商品二维码
     * @param string $clientkeynum 客户端标识
     * @param string $basekeynum 门店标识
     * @return \think\response\Json
     */
    private function handleWeightedProduct($product_code, $clientkeynum, $basekeynum, $parent_basekeynum)
    {
        try {
            // 解析18位二维码：4455格式
            $product_sn = substr($product_code, 0, 4);    // 第一段：商品sn（4位）
            $weight_raw = substr($product_code, 4, 4);    // 第二段：商品重量（4位）
            $unit_price_raw = substr($product_code, 8, 5); // 第三段：单价（5位）

            $weight = intval($weight_raw) / 1000;  // 重量，单位：kg
            $unit_price = intval($unit_price_raw) / 100;  // 单价，单位：元/kg

            if (strlen($product_code) == 18) {
                $total_price_raw = substr($product_code, 13, 5); // 第四段：总价（5位）
                $total_price = intval($total_price_raw) / 100;  // 总价，单位：元


                
            } else {
                // 取小数点两位
                $total_price = round($weight * $unit_price, 2);
            }

            // 验证计算是否正确
            $calculated_total = round($weight * $unit_price, 2);
            Log::info('计量商品扫描' . $calculated_total . ' - ' . $total_price);
            if (abs($calculated_total - $total_price) > 0.01) {
                return Response::json(Response::ERROR, '计量商品价格计算异常');
            }



        } catch (Exception $e) {
            Log::error('计量商品二维码解析失败: ' . $e->getMessage());
            return Response::json(Response::ERROR, '计量商品二维码格式错误');
        }

        // 通过商品sn查找商品信息
        $product = ProductInventory::with(['product'])->where(['clientkeynum' => $clientkeynum])
            ->where('sn', $product_sn)
            ->find();

        if (empty($product)) {
            return Response::json(Response::ERROR, '未找到商品信息，商品编号：' . $product_sn);
        }

        // 验证商品是否为计量商品
        if ($product['product']['product_type'] != 2) {
            return Response::json(Response::ERROR, '该商品不是计量商品');
        }

        // 获取门店信息
        $shop = Shop::where(function ($query) use($basekeynum, $parent_basekeynum) {
            $query->where('keynum', $basekeynum)
                ->whereOr('keynum', $parent_basekeynum);
        })->find();
        if (empty($shop)) {
            return Response::json(Response::ERROR, '门店不存在');
        }

        // 检查门店库存
        $shopInventory = ShopProductInventory::where([
            'product_id' => $product['product_id'],
            'shop_id' => $shop['id'],
            'inventory_id' => $product['id']
        ])->find();

        if (empty($shopInventory)) {
            return Response::json(Response::ERROR, '该商品在当前门店无库存');
        }

        // 检查重量库存是否充足
        if ($shopInventory['weight_stock'] < $weight) {
            return Response::json(Response::ERROR, '库存不足，需要重量：' . $weight . 'kg，库存重量：' . $shopInventory['weight_stock'] . 'kg');
        }

        // 获取详细的库存规格信息
        $productInventory = ProductInventory::where([
            'product_id' => $product['product_id'],
            'id' => $shopInventory['inventory_id']
        ])->find();

        if (empty($productInventory)) {
            return Response::json(Response::ERROR, '商品库存信息不完整');
        }

        // 合并库存信息，添加计量商品特有字段
        $inventory = array_merge($productInventory->toArray(), [
            'shop_inventory_id' => $shopInventory['id'],
            'stock' => $shopInventory['stock'],
            'weight_stock' => $shopInventory['weight_stock'],
            'scanned_weight' => $weight,
            'scanned_unit_price' => $unit_price,
            'scanned_total_price' => $total_price,
            'weight_unit' => $product['product']['weight_unit'] ?? 'kg'
        ]);

        return Response::json(Response::SUCCESS, '扫描成功', [
            'product' => $product['product'],
            'inventory' => $inventory,
            'is_multi_spec' => false,
            'is_weighted' => true,
            'weight_info' => [
                'weight' => $weight,
                'unit_price' => $unit_price,
                'total_price' => $total_price,
                'weight_unit' => $product['product']['weight_unit'] ?? 'kg'
            ]
        ]);
    }

    /**
     * 添加商品到结算台
     *
     * @param Request $request
     * @return \think\response\Json
     */
    public function addToCheckout(Request $request)
    {
        $user = $request->user_info;
        $keynum = $user['plat_account']['keynum'];
        $clientkeynum = $request->basekeynum;
        $product_id = $request->param('product_id');
        $inventory_id = $request->param('inventory_id');
        $shop_inventory_id = $request->param('shop_inventory_id');
        $quantity = $request->param('quantity', 1);
        
        // 计量商品特有参数
        $is_weighted = $request->param('is_weighted', false);
        $weight = $request->param('weight', 0);
        $unit_price = $request->param('unit_price', 0);
        $total_price = $request->param('total_price', 0);

        if (empty($product_id) || empty($inventory_id)) {
            return Response::json(Response::ERROR, '参数不能为空');
        }

        // 获取门店信息
        $basekeynum = $user['plat_account']['keynum'];
        $parent_basekeynum = $user['plat_account']['basekeynum'];
        $shop = Shop::where(function ($query) use($basekeynum, $parent_basekeynum) {
            $query->where('keynum', $basekeynum)
                ->whereOr('keynum', $parent_basekeynum);
        })->find();
        if (empty($shop)) {
            return Response::json(Response::ERROR, '门店不存在');
        }

        // 查询商品信息
        $product = Product::where(['id' => $product_id, 'clientkeynum' => $clientkeynum])->find();
        if (empty($product)) {
            return Response::json(Response::ERROR, '商品不存在');
        }

        // 查询库存信息
        $shopInventory = null;
        if (!empty($shop_inventory_id)) {
            $shopInventory = ShopProductInventory::where(['id' => $shop_inventory_id])->find();
        } else {
            $shopInventory = ShopProductInventory::where([
                'product_id' => $product_id,
                'inventory_id' => $inventory_id,
                'shop_id' => $shop['id']
            ])->find();
        }

        if (empty($shopInventory)) {
            return Response::json(Response::ERROR, '商品库存不存在');
        }
        // 根据商品类型检查库存
        if ($is_weighted && $product['product_type'] == 2) {
            // 计量商品检查重量库存
            if ($shopInventory['weight_stock'] < $weight) {
                return Response::json(Response::ERROR, '库存不足，需要重量：' . $weight . 'kg，库存重量：' . $shopInventory['weight_stock'] . 'kg');
            }
        } else {
            // 普通商品检查数量库存
            if ($shopInventory['stock'] < $quantity) {
                return Response::json(Response::ERROR, '库存不足，当前库存: ' . $shopInventory['stock']);
            }
        }

        // 获取商品规格信息
        $productInventory = ProductInventory::where([
            'id' => $inventory_id,
            'product_id' => $product_id
        ])->find();

        if (empty($productInventory)) {
            return Response::json(Response::ERROR, '商品规格信息不存在');
        }

        // 获取当前结算台商品列表
        $checkout_key = 'checkout_' . $user['id'] . '_' . $keynum;
        $checkout_items = Cache::get($checkout_key, []);

        // 计量商品每次扫描都作为独立商品添加（因为重量和价格可能不同）
        if ($is_weighted && $product['product_type'] == 2) {
            Log::info('计量商品扫描');
            // 计量商品，直接添加新项目
            $checkout_items[] = [
                'product_id' => $product_id,
                'inventory_id' => $inventory_id,
                'shop_inventory_id' => $shopInventory['id'],
                'product_name' => $product['title'],
                'spec_name' => $productInventory['title'],
                'price' => $total_price, // 使用扫描的总价作为价格
                'quantity' => 1, // 计量商品数量固定为1
                'subtotal' => $total_price,
                'add_time' => date('Y-m-d H:i:s'),
                'is_weighted' => true,
                'weight' => $weight,
                'unit_price' => $unit_price,
                'weight_unit' => $product['weight_unit'] ?? 'kg'
            ];
        } else {
            // 普通商品，检查是否已存在该商品
            Log::info('普通商品扫描');
            $item_exists = false;
            foreach ($checkout_items as &$item) {
                if ($item['product_id'] == $product_id && $item['inventory_id'] == $inventory_id) {
                    // 已存在，增加数量
                    $new_quantity = $item['quantity'] + $quantity;

                    // 检查新数量是否超过库存
                    if ($new_quantity > $shopInventory['stock']) {
                        return Response::json(Response::ERROR, '库存不足，当前库存: ' . $shopInventory['stock']);
                    }

                    $item['quantity'] = $new_quantity;
                    $item['subtotal'] = $item['price'] * $new_quantity;
                    $item_exists = true;
                    break;
                }
            }

            // 如果不存在，添加新商品
            if (!$item_exists) {
                $checkout_items[] = [
                    'product_id' => $product_id,
                    'inventory_id' => $inventory_id,
                    'shop_inventory_id' => $shopInventory['id'],
                    'product_name' => $product['title'],
                    'spec_name' => $productInventory['title'],
                    'price' => $productInventory['price'],
                    'quantity' => $quantity,
                    'subtotal' => $productInventory['price'] * $quantity,
                    'add_time' => date('Y-m-d H:i:s'),
                    'is_weighted' => false
                ];
            }
        }

        // 更新缓存
        Cache::set($checkout_key, $checkout_items, 7200); // 2小时过期

        return Response::json(Response::SUCCESS, '添加成功', [
            'items' => $checkout_items,
            'total' => $this->calculateTotal($checkout_items)
        ]);
    }

    /**
     * 获取结算台商品列表
     *
     * @param Request $request
     * @return \think\response\Json
     */
    public function getCheckoutItems(Request $request)
    {
        $user = $request->user_info;
        $keynum = $user['plat_account']['keynum'];
        $clientkeynum = $request->basekeynum;

        // 获取当前结算台商品列表
        $checkout_key = 'checkout_' . $user['id'] . '_' . $keynum;
        $checkout_items = Cache::get($checkout_key, []);

        return Response::json(Response::SUCCESS, '获取成功', [
            'items' => $checkout_items,
            'total' => $this->calculateTotal($checkout_items)
        ]);
    }

    /**
     * 从结算台移除商品
     *
     * @param Request $request
     * @return \think\response\Json
     */
    public function removeItem(Request $request)
    {
        $user = $request->user_info;
        $keynum = $user['plat_account']['keynum'];
        $product_id = $request->param('product_id');
        $inventory_id = $request->param('inventory_id');
        $item_index = $request->param('item_index'); // 用于精确移除计量商品的索引

        if (empty($product_id) || empty($inventory_id)) {
            return Response::json(Response::ERROR, '参数不能为空');
        }

        // 获取当前结算台商品列表
        $checkout_key = 'checkout_' . $user['id'] . '_' . $keynum;
        $checkout_items = Cache::get($checkout_key, []);

        $new_items = [];
        $removed_count = 0;
        
        foreach ($checkout_items as $index => $item) {
            $should_remove = false;
            
            // 如果指定了索引（用于计量商品精确移除）
            if (!empty($item_index) && $index == $item_index) {
                $should_remove = true;
            } 
            // 普通移除逻辑：匹配商品ID和库存ID
            else if (empty($item_index) && $item['product_id'] == $product_id && $item['inventory_id'] == $inventory_id) {
                // 对于计量商品，只移除第一个匹配的项目
                if (isset($item['is_weighted']) && $item['is_weighted'] && $removed_count == 0) {
                    $should_remove = true;
                    $removed_count++;
                } else if (!isset($item['is_weighted']) || !$item['is_weighted']) {
                    // 普通商品移除所有匹配项
                    $should_remove = true;
                }
            }
            
            if (!$should_remove) {
                $new_items[] = $item;
            }
        }

        // 更新缓存
        Cache::set($checkout_key, $new_items, 7200); // 2小时过期

        return Response::json(Response::SUCCESS, '移除成功', [
            'items' => $new_items,
            'total' => $this->calculateTotal($new_items)
        ]);
    }

    /**
     * 更新商品数量
     *
     * @param Request $request
     * @return \think\response\Json
     */
    public function updateQuantity(Request $request)
    {
        $user = $request->user_info;
        $keynum = $user['plat_account']['keynum'];
        $product_id = $request->param('product_id');
        $inventory_id = $request->param('inventory_id');
        $shop_inventory_id = $request->param('shop_inventory_id');
        $quantity = $request->param('quantity', 1);

        if (empty($product_id) || empty($inventory_id) || $quantity < 1) {
            return Response::json(Response::ERROR, '参数不能为空或数量必须大于0');
        }

        // 获取门店信息
        $basekeynum = $user['plat_account']['keynum'];
        $parent_basekeynum = $user['plat_account']['basekeynum'];
        $shop = Shop::where(function ($query) use($basekeynum, $parent_basekeynum) {
            $query->where('keynum', $basekeynum)
                ->whereOr('keynum', $parent_basekeynum);
        })->find();
        if (empty($shop)) {
            return Response::json(Response::ERROR, '门店不存在');
        }

        // 查询库存信息
        $shopInventory = null;
        if (!empty($shop_inventory_id)) {
            $shopInventory = ShopProductInventory::where(['id' => $shop_inventory_id])->find();
        } else {
            $shopInventory = ShopProductInventory::where([
                'product_id' => $product_id,
                'inventory_id' => $inventory_id,
                'shop_id' => $shop['id']
            ])->find();
        }

        if (empty($shopInventory) || $shopInventory['stock'] < $quantity) {
            return Response::json(Response::ERROR, '库存不足，当前库存: ' . $shopInventory['stock']);
        }

        // 获取当前结算台商品列表
        $checkout_key = 'checkout_' . $user['id'] . '_' . $keynum;
        $checkout_items = Cache::get($checkout_key, []);

        // 更新指定商品数量
        $item_found = false;
        foreach ($checkout_items as &$item) {
            if ($item['product_id'] == $product_id && $item['inventory_id'] == $inventory_id) {
                // 计量商品不允许更新数量
                if (isset($item['is_weighted']) && $item['is_weighted']) {
                    return Response::json(Response::ERROR, '计量商品不支持修改数量，请移除后重新扫描');
                }
                
                $item['quantity'] = $quantity;
                $item['subtotal'] = $item['price'] * $quantity;
                $item_found = true;
                break;
            }
        }

        if (!$item_found) {
            return Response::json(Response::ERROR, '商品不在结算台中');
        }

        // 更新缓存
        Cache::set($checkout_key, $checkout_items, 7200); // 2小时过期

        return Response::json(Response::SUCCESS, '更新成功', [
            'items' => $checkout_items,
            'total' => $this->calculateTotal($checkout_items)
        ]);
    }

    /**
     * 清空结算台
     *
     * @param Request $request
     * @return \think\response\Json
     */
    public function clearCheckout(Request $request)
    {
        $user = $request->user_info;
        $keynum = $user['plat_account']['keynum'];

        // 清空结算台
        $checkout_key = 'checkout_' . $user['id'] . '_' . $keynum;
        Cache::rm($checkout_key);

        return Response::json(Response::SUCCESS, '清空成功');
    }

    /**
     * 计算总金额
     *
     * @param array $items
     * @return float
     */
    private function calculateTotal($items)
    {
        $total = 0;
        foreach ($items as $item) {
            // 计量商品直接使用subtotal，普通商品使用price * quantity
            if (isset($item['is_weighted']) && $item['is_weighted']) {
                $total += $item['subtotal'];
            } else {
                $total += $item['price'] * $item['quantity'];
            }
        }
        return round($total, 2);
    }
}
