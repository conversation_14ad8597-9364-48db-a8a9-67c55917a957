<?php
/**
 * Create by 张林晨
 * E-mail: <EMAIL>
 * Date:2024/10/12 14:52
 */

namespace app\store\controller;

use app\admin\model\ClientMember;
use app\api\model\OrderLog;
use app\store\controller\PayController;
use app\store\lib\Response;
use app\store\model\Order;
use app\store\model\OrderDetail;
use app\store\model\Shop;
use app\store\model\StoreCheckCardnumLog;
use think\Controller;
use think\Db;
use think\facade\Cache;
use think\facade\Log;
use think\Request;
use app\store\model\Product;
use app\store\model\ProductInventory;
use app\store\model\ShopProductInventory;
use app\store\model\WeightProductSales;
use app\store\model\Ycard;
use app\admin\model\Product as AdminProduct;
use app\common\service\YlyPrintService;
use app\store\model\YlyShopPrinterConfig;
use app\common\service\PickupNumberService;
use app\api\model\CardUseLog;
use app\store\model\InventoryOrder;
use app\store\model\InventoryOrderDetail;
use app\store\model\User;

class OrderController extends Controller
{

    /**
     * 创建订单
     *
     * @param Request $request 请求对象
     * @return \think\response\Json 包含状态、消息和订单ID的JSON响应
     */
    public function createOrder(Request $request)
    {
        try {
            // 验证并获取基本参数
            $params = $this->validateOrderParams($request);
            if (!is_array($params)) {
                return $params; // 返回错误响应
            }

            // 提取参数
            [$user, $clientkeynum, $shop, $pay_type, $customer_id, $cardnum, $card_password, $checkout_items, $sms_code] = $params;

            // 生成订单号
            $order_no = Order::makeOrderNo();

            // 处理订单商品、计算价格并验证库存
            $productResult = $this->processOrderItems($checkout_items, $order_no, $shop, $clientkeynum);
            if (!is_array($productResult)) {
                return $productResult; // 返回错误响应
            }

            [$order_list, $product_info, $total_price] = $productResult;

            // 处理支付方式和折扣
            $paymentResult = $this->processPaymentMethod($pay_type, $total_price, $cardnum, $card_password, $customer_id, $clientkeynum, $order_no, $sms_code);
            if (!is_array($paymentResult)) {
                return $paymentResult; // 返回错误响应
            }

            // 提取支付处理结果
            // 修改这里处理四个元素的情况，因为我们移除了type
            $status = $paymentResult[0];
            $discount_price = $paymentResult[1];
            $discount_amount = $paymentResult[2];
            $need_pay = $paymentResult[3];
            $card_price = $paymentResult[4];
            $offline_price = $paymentResult[5];
            // 组装订单主表数据
            $order = $this->assembleOrderData($order_no, $clientkeynum, $shop['id'], $status, 3, $total_price,
                $discount_price, $pay_type, $product_info, $discount_amount, $customer_id, $card_price, $offline_price);

            // 保存订单
            $result = $this->saveOrder($checkout_items, $user, $clientkeynum, $order, $order_list);

            if ($result->status == Response::SUCCESS) {
                // 创建订单日志
                $this->createOrderLog($order_no, '创建订单成功', $clientkeynum, '店铺操作员');
                
                // 生成拣货编号
                \app\common\service\PickupNumberService::setPickupNumberForOrder(
                    $order_no, 
                    $shop['id'], 
                    $clientkeynum
                );

                // 订单创建成功后自动打印小票 不为微信支付
                if ($pay_type != 1) {
                    $this->autoPrintOrderTicket($order_no, $clientkeynum, $shop['id'], $order, $checkout_items);
                }
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('创建订单异常：' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return Response::json(Response::ERROR, '创建订单失败：系统异常');
        }
    }

    /**
     * 验证订单参数
     *
     * @param Request $request 请求对象
     * @return array|object 成功返回参数数组，失败返回错误响应
     */
    private function validateOrderParams(Request $request)
    {
        $user_id = $request->user_info['id'];
        $user = $request->user_info;
        $basekeynum = $user['plat_account']['keynum'];
        $parent_basekeynum = $user['plat_account']['basekeynum'];
        $clientkeynum = $request->basekeynum;

        // 获取商店信息
        $shop = Shop::where(function ($query) use($basekeynum, $parent_basekeynum) {
            $query->where('keynum', $basekeynum)
                ->whereOr('keynum', $parent_basekeynum);
        })->find();
        if (empty($shop)) {
            return Response::json(Response::ERROR, '门店信息不存在');
        }

        // 获取支付类型参数
        // 1在线支付  2卡支付 4现金支付
        $pay_type = $request->param('pay_type', 1);
        // 用户id 为0则为无用户信息
        $customer_id = $request->param('customer_id', 0);
        // 用户储值卡信息
        $cardnum = $request->param('cardnum', '');
        // 用户储值卡密码
        $card_password = $request->param('card_password', '');
        // 验证码
        $sms_code = $request->param('sms_code', '');

        // 获取结算台商品列表
        $checkout_key = 'checkout_' . $user['id'] . '_' . $basekeynum;
        $checkout_items = Cache::get($checkout_key, []);
        Log::info('checkout_items' . json_encode($checkout_items));

        if (empty($checkout_items)) {
            return Response::json(Response::ERROR, '结算台为空');
        }

        return [$user, $clientkeynum, $shop, $pay_type, $customer_id, $cardnum, $card_password, $checkout_items, $sms_code];
    }

    /**
     * 处理订单商品、计算价格并验证库存
     *
     * @param array $checkout_items 结算台商品
     * @param string $order_no 订单号
     * @param array $shop 商店信息
     * @param string $clientkeynum 客户端密钥
     * @return array|object 成功返回订单商品信息数组，失败返回错误响应
     */
    private function processOrderItems($checkout_items, $order_no, $shop, $clientkeynum)
    {
        $total_price = 0;
        $product_info = '';
        $order_list = [];

        foreach ($checkout_items as $item) {
            // 查询商品和库存信息
            $product = Product::where(['id' => $item['product_id']])->find();
            $inventory = ProductInventory::where(['id' => $item['inventory_id']])->find();

            if (empty($product) || empty($inventory)) {
                return Response::json(Response::ERROR, '商品或规格信息不存在');
            }

            // 查询商品库存
            $shop_product_inventory = ShopProductInventory::where([
                'shop_id' => $shop['id'],
                'product_id' => $item['product_id'],
                'inventory_id' => $item['inventory_id']
            ])->find();

            if (empty($shop_product_inventory)) {
                return Response::json(Response::ERROR, '商品库存信息不存在');
            }

            // 检查是否为计量商品
            $is_weight_product = isset($item['is_weighted']) && $item['is_weighted'];
            
            if ($is_weight_product) {
                // 计量商品处理逻辑
                if (!isset($item['weight']) || !isset($item['price']) || !isset($item['unit_price'])) {
                    return Response::json(Response::ERROR, '计量商品缺少重量或价格信息');
                }
                
                // 验证重量库存是否足够
                if ($shop_product_inventory['weight_stock'] < $item['weight']) {
                    return Response::json(Response::ERROR, "计量商品 【{$product['title']}】 重量库存不足！当前库存：{$shop_product_inventory['weight_stock']}kg");
                }
                
                // 累计商品信息和价格（计量商品）
                $product_info .= "{$product['title']}：{$item['weight']}kg*{$item['unit_price']}元/kg = " . round($item['unit_price'] * $item['weight'], 2) . "元<br>";
                $item_price = $item['price'];
                $total_price += $item_price;
                
                // 构建订单详情数据（计量商品）
                $order_list[] = [
                    'product_json' => json_encode($product),
                    'inventory_json' => json_encode($inventory),
                    'number' => 1, // 计量商品数量固定为1
                    'price' => $item['price'], // 使用总价作为单价
                    'amount' => $item['price'], // 小计金额等于总价
                    'actual_weight' => $item['weight'], // 实际重量
                    'weight_unit_price' => $item['unit_price'], // 单位价格
                    'shop_id' => $shop['id'],
                    'order_no' => $order_no,
                    'add_time' => date('Y-m-d H:i:s'),
                    'clientkeynum' => $clientkeynum
                ];
            } else {
                // 普通商品处理逻辑
                // 验证库存是否足够
                if ($shop_product_inventory['stock'] < $item['quantity']) {
                    return Response::json(Response::ERROR, "商品 【{$product['title']} - {$inventory['title']}】 库存不足！");
                }
                
                // 累计商品信息和价格（普通商品）
                $item_price = $item['price'] * $item['quantity'];
                $total_price += $item_price;
                $product_info .= "{$product['title']} - {$inventory['title']}：{$item['quantity']}件 * {$inventory['price']}元/件 = " . round($inventory['price'] * $item['quantity'], 2) . "元<br>";
                
                // 使用checkout_items中的价格作为单价（这个价格可能已经包含了结算时的折扣）
                $unit_price = $item['price'];
                $amount = round($unit_price * $item['quantity'], 2);
                
                // 构建订单详情数据（普通商品）- 统一字段结构
                $order_list[] = [
                    'product_json' => json_encode($product),
                    'inventory_json' => json_encode($inventory),
                    'number' => $item['quantity'],
                    'price' => $unit_price, // 添加单价字段
                    'amount' => $amount, // 添加小计金额字段
                    'actual_weight' => null, // 普通商品实际重量为空
                    'weight_unit_price' => null, // 普通商品单位价格为空
                    'shop_id' => $shop['id'],
                    'order_no' => $order_no,
                    'add_time' => date('Y-m-d H:i:s'),
                    'clientkeynum' => $clientkeynum
                ];
            }
        }

        return [$order_list, $product_info, $total_price];
    }

    /**
     * 处理支付方式和折扣
     *
     * @param int $pay_type 支付类型
     * @param float $total_price 订单总价
     * @param string $cardnum 卡号
     * @param string $card_password 卡密码
     * @param int $customer_id 客户ID
     * @param string $clientkeynum 客户端密钥
     * @return array|object 成功返回支付处理结果数组，失败返回错误响应
     */
    private function processPaymentMethod($pay_type, $total_price, $cardnum, $card_password, $customer_id, $clientkeynum, $order_no, $sms_code)
    {
        // $type = 3; // 移除此行，因为type在assembleOrderData中固定为3
        $status = 0; // 默认订单状态（待支付）
        $discount_price = $total_price; // 默认折扣后价格等于总价
        $discount_amount = 0; // 默认折扣金额为0
        $need_pay = true; // 默认需要调用支付
        $card_price = 0; // 卡支付金额
        $offline_price = 0; // 线下支付金额

        if ($pay_type == 4) { // 现金支付
            // 移除type设置，使用固定值3
            $status = 100; // 已完成状态
            $need_pay = false; // 不需要调用支付
            $offline_price = $total_price; // 线下支付金额等于总价
        } elseif ($pay_type == 2 && !empty($cardnum) && !empty($customer_id)) { // 卡支付
            // 移除type设置，使用固定值3
            $status = 100; // 已完成状态
            $need_pay = false; // 不需要调用支付
            $now = time();

            // 获取卡信息和等级
            $card_info = Ycard::where([
                'cardnum' => $cardnum,
                'member_id' => $customer_id,
                'clientkeynum' => $clientkeynum,
                'status' => 3,
            ])->where('begin_dui', '<=', $now)->where('end_dui', '>=', $now)->with(['card_level'])->find();

            $member = User::where(['id' => $customer_id])->find();
            // 获取会员手机号
            $member_phone = $member['phone'];
            // 查询日志
            $sms_code_data = Db::table('plat_sms_code')->where(['phone' => $member_phone])->order('id', 'desc')->find();


            // 五分钟有效期
            if (!empty($sms_code_data) && $sms_code_data['code'] != $sms_code && $sms_code_data['time'] > time() - 300) {
                return Response::json(Response::ERROR, '验证码错误');
            }

            if (empty($card_info)) {
                return Response::json(Response::ERROR, '卡号不存在');
            }

            // 获取卡等级折扣
            $card_level = $card_info['card_level'];
            if (empty($card_level)) {
                $discount = 1; // 无折扣
            } else {
                $discount = $card_level['discount'] / 100; // 卡等级折扣
            }

            // 计算折扣金额
            $discount_price = $total_price * $discount;
            $discount_amount = $total_price - $discount_price;

            // 判断卡余额是否足够
            if ($card_info['yu_money'] < $discount_price) {
                return Response::json(Response::ERROR, '卡余额不足');
            }

            // 更新卡余额
            $remaining = $card_info['yu_money'] - $discount_price;
            $this->updateCardBalance($card_info, $remaining, $order_no, $discount_price, $customer_id, $clientkeynum);
            $card_price = $discount_price;
        } elseif ($pay_type == 2 && !empty($card_password) && !empty($cardnum)){
            // 移除type设置，使用固定值3
            $status = 100; // 已完成状态
            $need_pay = false; // 不需要调用支付
            // 判断卡密码是否正确
            $now = time();

            // 获取卡信息和等级
            $card_info = Ycard::where([
                'cardnum' => $cardnum,
                'clientkeynum' => $clientkeynum,
                'status' => 3
            ])->where('begin_dui', '<=', $now)
            ->where('end_dui', '>=', $now)
            ->with(['card_level', 'user'])->find();

            if (empty($card_info)) {
                return Response::json(Response::ERROR, '卡号不存在');
            }

            if ($card_info['cardpwd'] != encrypt($card_password)) {
                return Response::json(Response::ERROR, '卡密码错误');
            }

            if (!empty($card_info['user'])) {
                // 获取会员手机号
                $member_phone = $card_info['user']['phone'];
                // 查询日志
                $sms_code_data = Db::table('plat_sms_code')->where(['phone' => $member_phone])->order('id', 'desc')->find();
                
                // 五分钟有效期
                if (!empty($sms_code_data) && $sms_code_data['code'] != $sms_code && $sms_code_data['time'] > time() - 300) {
                    return Response::json(Response::ERROR, '验证码错误');
                }
            }

            // 获取卡等级折扣
            $card_level = $card_info['card_level'];
            if (empty($card_level)) {
                $discount = 1; // 无折扣
            } else {
                $discount = $card_level['discount'] / 100; // 卡等级折扣
            }

            // 计算折扣金额
            $discount_price = $total_price * $discount;
            $discount_amount = $total_price - $discount_price;

            // 判断卡余额是否足够
            if ($card_info['yu_money'] < $discount_price) {
                return Response::json(Response::ERROR, '卡余额不足');
            }

            // 更新卡余额
            $remaining = $card_info['yu_money'] - $discount_price;
            // Ycard::where(['id' => $card_info['id']])->update(['yu_money' => $remaining]);
            // 记录卡消费日志
            $this->updateCardBalance($card_info, $remaining, $order_no, $discount_price, $customer_id, $clientkeynum);
            $card_price = $discount_price;
        } elseif ($pay_type == 1) { // 在线支付
            // 移除type设置，使用固定值3
            $status = 0; // 待支付状态
            $need_pay = true; // 需要调用支付
        } elseif ($pay_type == 0) { // 免单
            // 移除type设置，使用固定值3
            $status = 100; // 已完成状态
            $need_pay = false; // 不需要调用支付
            $discount_price = 0; // 免单金额为0
            $discount_amount = $total_price; // 折扣金额等于总价
        }

        return [$status, $discount_price, $discount_amount, $need_pay, $card_price, $offline_price];
    }

    /**
     * 更新卡余额并记录
     */
    private function updateCardBalance($card, $remaining, $order_no, $use_price, $user_id, $clientkeynum)
    {
        // 更新卡信息
        Ycard::where(['id' => $card['id']])->update(['yu_money' => $remaining]);

        // 记录卡使用日志
        CardUseLog::create([
            'basekeynum' => $clientkeynum,
            'member_id' => $user_id,
            'order_sn' => $order_no,
            'cardnum' => $card['cardnum'],
            'yu_money' => $remaining,
            'after_money' => $remaining,
            'use_money' => $use_price,
            'status' => 1,
            'add_time' => date('Y-m-d H:i:s'),
        ]);
    }

    /**
     * 组装订单主表数据
     *
     * @param string $order_no 订单号
     * @param string $clientkeynum 客户端密钥
     * @param int $shop_id 商店ID
     * @param int $status 订单状态
     * @param int $type 订单类型
     * @param float $total_price 订单总价（原价）
     * @param float $discount_price 折扣后价格
     * @param int $pay_type 支付类型
     * @param string $product_info 商品信息
     * @param float $discount_amount 折扣金额
     * @param int $customer_id 客户ID
     * @param float $card_price 卡支付金额
     * @return array 订单主表数据
     */
    private function assembleOrderData($order_no, $clientkeynum, $shop_id, $status, $type, $total_price,
                                      $discount_price, $pay_type, $product_info, $discount_amount = 0, $customer_id = 0, $card_price = 0, $offline_price = 0)
    {
        return [
            'order_no' => $order_no,
            'clientkeynum' => $clientkeynum,
            'shop_id' => $shop_id,
            'status' => $status,
            'type' => 3,
            'pay_type' => $pay_type,
            'add_time' => date('Y-m-d H:i:s'),
            'product_price' => $discount_price, // Store版本：保存折扣后的商品价格
            'origin_price' => $total_price, // 原始总价
            'price' => $discount_price, // 实际支付价格（折扣后）
            'discount_amount' => $discount_amount, // 折扣金额
            'pay_time' => date('Y-m-d H:i:s'),
            'product_info' => $product_info,
            'user_id' => $customer_id,
            'card_price' => $card_price,
            'update_time' => date('Y-m-d H:i:s'),
            'pickup_date' => date('Y-m-d'),
            'pickup_time' => date('H:i'),
            'offline_price' => $offline_price,
        ];
    }

    /**
     * 保存订单
     *
     * @param array $checkout_items 结算台商品
     * @param array $user 用户信息
     * @param string $clientkeynum 客户端密钥
     * @param array $order 订单数据
     * @param array $order_list 订单详情数据
     * @return object 保存结果
     */
    private function saveOrder($checkout_items, $user, $clientkeynum, $order, $order_list)
    {
        try {
            Db::startTrans();

            // 清空结算台
            $checkout_key = 'checkout_' . $user['id'] . '_' . $clientkeynum;
            Cache::rm($checkout_key);

            // // 处理自提订单特殊信息
            // if ($order['type'] == 1) { // 自提订单
            //     $order = $this->processPickupOrder($order, request());
            // }
            $product_total = 0;
            foreach ($checkout_items as $item) {
                $is_weight_product = isset($item['is_weighted']) && $item['is_weighted'];
                
                if ($is_weight_product) {
                    // 计量商品：扣减重量库存
                    $product_total += $item['weight'];
                    
                } else {
                    // 普通商品：扣减数量库存
                    $product_total += $item['quantity'];
                    
                }
            }
            $order['product_total'] = $product_total;

            // 保存订单和订单详情
            $order_model = \app\store\model\Order::create($order);
            $order_id = $order_model->id;
            OrderDetail::insertAll($order_list);

            // 扣减库存并创建出库单
            $this->processInventoryAndCreateOutOrder($checkout_items, $order, $clientkeynum, $user);

            Db::commit();

            // 获取完整订单信息
            $complete_order = \app\store\model\Order::where('id', $order_id)->with('orderDetail')->find();

            // 判断是否需要调用支付
            $need_pay = ($order['pay_type'] == 1 && $order['status'] == 0);

            // 如果是微信支付，自动生成支付二维码（仅在需要支付时）
            $pay_data = [];
            if ($need_pay) {
                $pay_controller = new PayController();
                // 创建新的请求对象并设置需要的属性
                $pay_request = new Request();
                $pay_request->basekeynum = $clientkeynum;
                $pay_request->user_info = $user;
                // 设置请求参数
                $pay_request->order_id = $order_id;
                // 调用支付控制器创建支付
                $pay_result = $pay_controller->createNativePay($pay_request);

                if (is_object($pay_result) && $pay_result->getData()['status'] === 1) {
                    $pay_data = $pay_result->getData()['data'];
                }
            }

            // 构建返回信息
            $return_data = [
                'order_id' => $order_id,
                'order_no' => $order['order_no'],
                'status' => $order['status'],
                'type' => $order['type'],
                'pay_type' => $order['pay_type'],
                'price' => $order['price'],
                'product_price' => $order['product_price'],
                'discount_amount' => $order['discount_amount'],
                'need_pay' => $need_pay, // 是否需要调用支付
                'complete_order' => $complete_order, // 完整订单信息
                'pay_data' => $pay_data // 微信支付数据，如二维码URL等
            ];

            return Response::json(Response::SUCCESS, '创建订单成功', $return_data);
        } catch (\Exception $e) {
            Db::rollback();
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return Response::json(Response::ERROR, '创建订单失败：' . $e->getMessage());
        }
    }

    /**
     * 处理库存扣减并创建出库单
     *
     * @param array $checkout_items 结算台商品
     * @param array $order 订单数据
     * @param string $clientkeynum 客户端密钥
     * @param array $user 用户信息
     */
    private function processInventoryAndCreateOutOrder($checkout_items, $order, $clientkeynum, $user)
    {
        // 准备出库单明细数据
        $out_order_details = [];
        
        foreach ($checkout_items as $item) {
            $shop_inventory = ShopProductInventory::where([
                'shop_id' => $order['shop_id'],
                'product_id' => $item['product_id'],
                'inventory_id' => $item['inventory_id']
            ])->find();
            
            if (empty($shop_inventory)) {
                Log::error("库存记录不存在：shop_id={$order['shop_id']}, product_id={$item['product_id']}, inventory_id={$item['inventory_id']}");
                continue;
            }
            
            // 获取商品和库存信息
            $product = Product::where('id', $item['product_id'])->find();
            $inventory = ProductInventory::where('id', $item['inventory_id'])->find();
            
            if (empty($product) || empty($inventory)) {
                Log::error("商品或规格信息不存在：product_id={$item['product_id']}, inventory_id={$item['inventory_id']}");
                continue;
            }
            
            // 检查是否为计量商品
            $is_weight_product = isset($item['is_weighted']) && $item['is_weighted'];
            
            if ($is_weight_product) {
                // 计量商品：扣减重量库存
                $before_quantity = $shop_inventory->weight_stock;
                $change_quantity = $item['weight'];
                $after_quantity = $before_quantity - $change_quantity;
                
                $shop_inventory->reduceWeightStock($item['weight'], '订单销售');
                
                // 记录库存变动日志
                $this->createInventoryLog([
                    'clientkeynum' => $clientkeynum,
                    'shop_id' => $order['shop_id'],
                    'product_id' => $item['product_id'],
                    'inventory_id' => $item['inventory_id'],
                    'before_quantity' => $before_quantity,
                    'change_quantity' => -$change_quantity, // 出库为负数
                    'after_quantity' => $after_quantity,
                    'change_type' => 2, // 2-销售出库
                    'related_no' => $order['order_no'],
                    'operator' => $user['plat_account']['accountname'] ?? '店铺操作员',
                    'remark' => "门店订单销售：{$product['title']}，重量：{$item['weight']}kg",
                    'unit' => 'kg'
                ]);
                
                // 记录计量商品销售数据
                WeightProductSales::recordSale([
                    'clientkeynum' => $clientkeynum,
                    'shop_id' => $order['shop_id'],
                    'product_id' => $item['product_id'],
                    'inventory_id' => $item['inventory_id'],
                    'weight' => $item['weight'],
                    'total_price' => $item['price'],
                    'unit_price' => $item['unit_price'],
                    'order_no' => $order['order_no']
                ]);
                
                // 准备出库单明细（计量商品）
                $out_order_details[] = [
                    'product_id' => $item['product_id'],
                    'inventory_id' => $item['inventory_id'],
                    'quantity' => $item['weight'], // 计量商品使用重量作为数量
                    'price' => $item['unit_price'], // 使用单位价格
                    'remark' => "计量商品：{$product['title']}，重量：{$item['weight']}kg"
                ];
            } else {
                // 普通商品：扣减数量库存
                $before_quantity = $shop_inventory->stock;
                $change_quantity = $item['quantity'];
                $after_quantity = $before_quantity - $change_quantity;
                
                ShopProductInventory::where([
                    'shop_id' => $order['shop_id'],
                    'product_id' => $item['product_id'],
                    'inventory_id' => $item['inventory_id']
                ])->update([
                    'stock' => Db::raw('stock - ' . $item['quantity'])
                ]);
                
                // 记录库存变动日志
                $this->createInventoryLog([
                    'clientkeynum' => $clientkeynum,
                    'shop_id' => $order['shop_id'],
                    'product_id' => $item['product_id'],
                    'inventory_id' => $item['inventory_id'],
                    'before_quantity' => $before_quantity,
                    'change_quantity' => -$change_quantity, // 出库为负数
                    'after_quantity' => $after_quantity,
                    'change_type' => 2, // 2-销售出库
                    'related_no' => $order['order_no'],
                    'operator' => $user['plat_account']['accountname'] ?? '店铺操作员',
                    'remark' => "门店订单销售：{$product['title']} - {$inventory['title']}，数量：{$item['quantity']}",
                    'unit' => 'pcs'
                ]);
                
                // 准备出库单明细（普通商品）
                $out_order_details[] = [
                    'product_id' => $item['product_id'],
                    'inventory_id' => $item['inventory_id'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'], // 使用订单中的单价
                    'remark' => "普通商品：{$product['title']} - {$inventory['title']}，数量：{$item['quantity']}"
                ];
            }
        }
        
        // 创建销售出库单
        if (!empty($out_order_details)) {
            $this->createSalesOutOrder($order, $clientkeynum, $user, $out_order_details);
        }
    }
    
    /**
     * 创建库存变动日志
     *
     * @param array $data 日志数据
     */
    private function createInventoryLog($data)
    {
        try {
            Db::name('inventory_log')->insert([
                'clientkeynum' => $data['clientkeynum'],
                'shop_id' => $data['shop_id'],
                'product_id' => $data['product_id'],
                'inventory_id' => $data['inventory_id'],
                'before_quantity' => $data['before_quantity'],
                'change_quantity' => $data['change_quantity'],
                'after_quantity' => $data['after_quantity'],
                'change_type' => $data['change_type'],
                'related_no' => $data['related_no'],
                'operator' => $data['operator'],
                'remark' => $data['remark'],
                'unit' => $data['unit'] ?? 'pcs',
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            Log::error('创建库存变动日志失败：' . $e->getMessage());
        }
    }
    
    /**
     * 创建销售出库单
     *
     * @param array $order 订单数据
     * @param string $clientkeynum 客户端密钥
     * @param array $user 用户信息
     * @param array $details 出库明细
     */
    private function createSalesOutOrder($order, $clientkeynum, $user, $details)
    {
        try {
            // 创建出库单主表
            $inventoryOrder = new InventoryOrder();
            $out_order_result = $inventoryOrder->createSalesOutOrder([
                'clientkeynum' => $clientkeynum,
                'shop_id' => $order['shop_id'],
                'related_order_no' => $order['order_no'],
                'remark' => "门店订单销售出库，订单号：{$order['order_no']}",
                'created_by' => $user['plat_account']['accountname'] ?? '店铺操作员',
                'reviewed_by' => $user['plat_account']['accountname'] ?? '店铺操作员'
            ]);
            
            if ($out_order_result) {
                // 创建出库单明细
                $inventoryOrderDetail = new InventoryOrderDetail();
                $result = $inventoryOrderDetail->createSalesOutDetails(
                    $out_order_result['id'],
                    $out_order_result['order_no'],
                    $order['shop_id'],
                    $details,
                    $clientkeynum
                );
                
                if ($result) {
                    Log::info("销售出库单创建成功：订单号{$order['order_no']}，出库单ID{$out_order_result['id']}，出库单号{$out_order_result['order_no']}");
                } else {
                    Log::error("销售出库单明细创建失败：订单号{$order['order_no']}");
                }
            } else {
                Log::error("销售出库单主表创建失败：订单号{$order['order_no']}");
            }
        } catch (\Exception $e) {
            Log::error('创建销售出库单异常：' . $e->getMessage());
        }
    }

    /**
     * 创建订单日志
     *
     * @param string $order_no 订单号
     * @param string $content 日志内容
     * @param string $clientkeynum 客户端密钥
     * @param string $operator 操作员
     */
    private function createOrderLog($order_no, $content, $clientkeynum, $operator = '系统')
    {
        OrderLog::create([
            'order_no' => $order_no,
            'clientkeynum' => $clientkeynum,
            'content' => $content,
            'operator' => $operator,
            'add_time' => date('Y-m-d H:i:s')
        ]);
    }



    public function orderList(Request $request)
    {
        $clientkeynum = $request->basekeynum;
        $user_id = $request->user_info['id'];
        $basekeynum = $request->user_info['plat_account']['keynum'];
        $parent_basekeynum = $request->user_info['plat_account']['basekeynum'];
        $type = $request->param('type', 1);
        $page = $request->param('page', 1);
        $page_size = $request->param('page_size', 10);

        $shop = Shop::where(function ($query) use($basekeynum, $parent_basekeynum) {
            $query->where('keynum', $basekeynum)
                ->whereOr('keynum', $parent_basekeynum);
        })->find();
        if (empty($shop)) {
            return Response::json(Response::ERROR, '门店不存在');
        }

        $order_list = \app\store\model\Order::where(['clientkeynum' => $shop['clientkeynum'], 'shop_id' => $shop['id']])
            ->with('orderDetail')
            ->order('id', 'desc')
            ->page($page, $page_size);

        if (!empty($type)) {
            $order_list = $order_list->where('type', $type);
        }


        $count = $order_list->count();
        $order_list = $order_list->select();

        if (!empty($order_list)) {
            foreach ($order_list as &$item) {
                $item['pay_type_text'] = $this->getPayType($item['pay_type']);
            }
        }

        return Response::json(Response::SUCCESS, '', ['list' => $order_list, 'count' => $count]);

    }

    public function cardCheckLog(Request $request)
    {
        $clientkeynum = $request->basekeynum;
        $user = $request->user_info;
        $basekeynum = $user['plat_account']['keynum'];
        $parent_basekeynum = $user['plat_account']['basekeynum'];
        $shop = Shop::where(function ($query) use($basekeynum, $parent_basekeynum) {
            $query->where('keynum', $basekeynum)
                ->whereOr('keynum', $parent_basekeynum);
        })->find();
        if (empty($shop)) {
            return Response::json(Response::ERROR, '门店不存在');
        }
        $page = $request->param('page', 1);
        $page_size = $request->param('page_size', 10);
        $remark = $request->param('remark', '');

        $data = StoreCheckCardnumLog::where(['shop_id' => $shop['id'], 'clientkeynum' => $shop['clientkeynum']])
            ->order('id', 'desc')
            ->page($page, $page_size);

        if (!empty($remark)) {
            $data = $data->where('remark', 'like', "%$remark%");
        }

        $list = $data->select();
        $count = $data->count();

        foreach ($list as &$item) {
            $item['user_name'] = ClientMember::where('id', $item['user_id'])->value('phone');
        }

        return  Response::json(Response::SUCCESS, '', ['list' => $list, 'count' => $count]);

    }

    public function checkedOrderList(Request $request)
    {
        $clientkeynum = $request->basekeynum;
        $user = $request->user_info;
        $basekeynum = $user['plat_account']['keynum'];
        $parent_basekeynum = $user['plat_account']['basekeynum'];
        $shop = Shop::where(function ($query) use($basekeynum, $parent_basekeynum) {
            $query->where('keynum', $basekeynum)
                ->whereOr('keynum', $parent_basekeynum);
        })->find();
        if (empty($shop)) {
            return Response::json(Response::ERROR, '门店不存在');
        }
        $type = $request->param('type', 1);
        $page = $request->param('page', 1);
        $page_size = $request->param('page_size', 10);

        $order_list = \app\store\model\Order::where(['clientkeynum' => $shop['clientkeynum']])
            ->where('type', 1)
            ->where('shop_id', $shop['id'])
            ->with('orderDetail')
            ->order('id', 'desc')
            ->page($page, $page_size);

        if (!empty($type)) {
            $order_list = $order_list->where('type', $type);
        }


        $count = $order_list->count();
        $order_list = $order_list->select();

        return Response::json(Response::SUCCESS, '', ['list' => $order_list, 'count' => $count]);
    }

    // 订单详情
    public function orderDetail(Request $request){
        $order_id = $request->param('order_id');
        $order_info = \app\store\model\Order::where('id', $order_id)->with('orderDetail')->find();
        if (!$order_info) {
            return Response::json(Response::ERROR, '订单不存在');
        }
        $order_info['pay_type_text'] = $this->getPayType($order_info['pay_type']);
        return Response::json(Response::SUCCESS, '', ['order_info' => $order_info]);
    }

    // 接单
    public function immediatelyOrder(Request $request)
    {
        $user = $request->user_info;
        $order_id = $request->param('order_id');
        $order = \app\store\model\Order::where('id', $order_id)->find();

        if (!$order) {
            return Response::json(Response::ERROR, '订单不存在');
        }

        if ($order->status!= 1) {
            return Response::json(Response::ERROR, '订单状态不允许更改为立即下单');
        }

        $order->status = 2;
        $result = $order->save();
        OrderLog::create([
            'order_no' => $order['order_no'],
            'clientkeynum' => $order['clientkeynum'],
            'content' => '商家已接单',
            'operator' => $user['plat_account']['accountname'],
            'add_time' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            // 接单成功后自动打印订单小票
            $this->autoPrintOrderTicketAfterAccept($order['order_no'], $order['clientkeynum'], $order['shop_id'], $order->toArray());
            
            return Response::json(Response::SUCCESS, '订单状态已更新为立即下单');
        } else {
            return Response::json(Response::ERROR, '更新订单状态失败');
        }
    }

    /**
     * 开始配货 自提订单操作
     */
    public function startDelivery(Request $request)
    {
        $user = $request->user_info;
        $order_id = $request->param('order_id');
        $order = \app\store\model\Order::where('id', $order_id)->find();

        if (!$order) {
            return Response::json(Response::ERROR, '订单不存在');
        }

        if ($order->status != 2) {
            return Response::json(Response::ERROR, '订单状态不允许更改为开始配货');
        }

        if ($order->type != 1) {
            return Response::json(Response::ERROR, '订单类型不允许更改为开始配货');
        }

        $order->status = 3;
        $result = $order->save();
        OrderLog::create([
            'order_no' => $order['order_no'],
            'clientkeynum' => $order['clientkeynum'],
            'content' => '商家开始配货',
            'operator' => $user['plat_account']['accountname'],
            'add_time' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            return Response::json(Response::SUCCESS, '订单状态已更新为开始配货');
        } else {
            return Response::json(Response::ERROR, '更新订单状态失败');
        }
    }

    /**
     * 订单核销
     */
    public function checkOrder(Request $request)
    {
        $user = $request->user_info;
        $order_id = $request->param('order_id');
        $order = \app\store\model\Order::where('id', $order_id)->find();
        $verify_code = $request->param('verify_code');

        if (!$order) {
            return Response::json(Response::ERROR, '订单不存在');
        }

        if ($order->status != 3) {
            return Response::json(Response::ERROR, '订单状态不允许更改为核销');
        }

        if ($order->type != 1) {
            return Response::json(Response::ERROR, '订单类型不允许更改为核销');
        }

        if ($order->verify_code != $verify_code) {
            return Response::json(Response::ERROR, '核销码错误');
        }

        $order->status = 100;
        $result = $order->save();
        OrderLog::create([
            'order_no' => $order['order_no'],
            'clientkeynum' => $order['clientkeynum'],
            'content' => '商家核销订单',
            'operator' => $user['plat_account']['accountname'],
            'add_time' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            return Response::json(Response::SUCCESS, '订单状态已更新为核销');
        } else {
            return Response::json(Response::ERROR, '更新订单状态失败');
        }
    }

    // 开始配送
    public function orderDelivering(Request $request)
    {
        $user = $request->user_info;
        $order_id = $request->param('order_id');
        $order = \app\store\model\Order::where('id', $order_id)->find();

        if (!$order) {
            return Response::json(Response::ERROR, '订单不存在');
        }

        if ($order->status != 2) {
            return Response::json(Response::ERROR, '订单状态不允许更改为配送中');
        }

        if ($order->type != 2) {
            return Response::json(Response::ERROR, '订单类型不允许更改为配送中');
        }

        $order->status = 3;
        $result = $order->save();
        OrderLog::create([
            'order_no' => $order['order_no'],
            'clientkeynum' => $order['clientkeynum'],
            'content' => '商家开始配送',
            'operator' => $user['plat_account']['accountname'],
            'add_time' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            return Response::json(Response::SUCCESS, '订单状态已更新为配送中');
        } else {
            return Response::json(Response::ERROR, '更新订单状态失败');
        }
    }

    // 配送完成
    public function orderDelivered(Request $request)
    {
        $user = $request->user_info;
        $order_id = $request->param('order_id');
        $order = \app\store\model\Order::where('id', $order_id)->find();

        if (!$order) {
            return Response::json(Response::ERROR, '订单不存在');
        }

        if ($order->status != 3) {
            return Response::json(Response::ERROR, '订单状态不允许更改为配送完成');
        }

        if ($order->type != 2) {
            return Response::json(Response::ERROR, '订单类型不允许更改为配送完成');
        }

        $order->status = 4;
        $result = $order->save();
        OrderLog::create([
            'order_no' => $order['order_no'],
            'clientkeynum' => $order['clientkeynum'],
            'content' => '商家配送完成',
            'operator' => $user['plat_account']['accountname'],
            'add_time' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            return Response::json(Response::SUCCESS, '订单状态已更新为配送完成');
        } else {
            return Response::json(Response::ERROR, '更新订单状态失败');
        }
    }

    private function getPayType($pay_type)
    {
        switch ($pay_type) {
            case 1:
                return '微信支付';
            case 2:
                return '卡支付';
            case 3:
                return '组合支付';
            case 4:
                return '现金支付';
            default:
                return '未知';
        }
    }

    /**
     * 微信支付订单
     *
     * @param Request $request 请求对象
     * @return \think\response\Json
     */
    public function wechatPay(Request $request)
    {
        $order_id = $request->param('order_id');
        $order = \app\store\model\Order::where('id', $order_id)->find();

        if (!$order) {
            return Response::json(Response::ERROR, '订单不存在');
        }

        // 检查订单状态，只有待支付的订单才能发起支付
        if ($order->status != 0) {
            return Response::json(Response::ERROR, '订单状态不允许支付');
        }

        // 调用支付控制器创建支付
        $pay_controller = new PayController();
        $pay_result = $pay_controller->createNativePay($request);


        return $pay_result;
    }

    /**
     * 查询支付状态
     *
     * @param Request $request 请求对象
     * @return \think\response\Json
     */
    public function checkPayStatus(Request $request)
    {
        $order_no = $request->param('order_no');

        // 调用支付控制器查询支付状态
        $pay_controller = new PayController();
        return $pay_controller->queryPayStatus($request);
    }

    /**
     * 取消支付
     *
     * @param Request $request 请求对象
     * @return \think\response\Json
     */
    public function cancelPay(Request $request)
    {
        $order_no = $request->param('order_no');
        $order = \app\store\model\Order::where('order_no', $order_no)->find();

        if (!$order) {
            return Response::json(Response::ERROR, '订单不存在');
        }

        // 只有待支付的订单才能取消支付
        if ($order->status != 0) {
            return Response::json(Response::ERROR, '订单状态不允许取消支付');
        }

        // 调用支付控制器关闭支付
        $pay_controller = new PayController();
        return $pay_controller->closePay($request);
    }

    /**
     * 取消订单
     *
     * @param Request $request 请求对象
     * @return \think\response\Json 包含状态和消息的JSON响应
     */
    public function cancelOrder(Request $request)
    {
        try {
            $user = $request->user_info;
            $clientkeynum = $request->basekeynum;
            $order_id = $request->param('order_id');

            if (empty($order_id)) {
                return Response::json(Response::ERROR, '订单ID不能为空');
            }

            // 开启事务
            Db::startTrans();

            // 验证订单
            $order = \app\store\model\Order::where(['id' => $order_id, 'clientkeynum' => $clientkeynum])->find();
            if (empty($order)) {
                Db::rollback();
                return Response::json(Response::ERROR, '订单不存在');
            }

            // 检查订单状态，只有待支付(0)、待审核(1)、已接单(2)状态可以取消
            if (!in_array($order['status'], [0, 1, 2])) {
                Db::rollback();
                return Response::json(Response::ERROR, '当前订单状态不允许取消');
            }

            // 处理退款
            $refund_result = $this->processOrderRefund($order, $clientkeynum);
            if (!$refund_result['success']) {
                Db::rollback();
                return Response::json(Response::ERROR, $refund_result['message']);
            }

            // 恢复库存
            $this->restoreOrderInventory($order['order_no'], $clientkeynum);

            // 更新订单状态为已取消
            \app\store\model\Order::where(['id' => $order_id])->update([
                'status' => -1,
                'cancel_time' => date('Y-m-d H:i:s'),
                'refund_status' => isset($refund_result['refund_info']['wechat_refund']) ? 2 : 0, // 有微信退款则为退款成功，否则为无退款
                'refund_amount' => ($refund_result['refund_info']['wechat_refund'] ?? 0) + ($refund_result['refund_info']['card_refund'] ?? 0),
            ]);

            // 记录订单日志
            $this->createOrderLog($order['order_no'], '店铺取消订单', $clientkeynum, $user['plat_account']['accountname']);

            // 提交事务
            Db::commit();

            return Response::json(Response::SUCCESS, '订单取消成功', [
                'refund_info' => $refund_result['refund_info']
            ]);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            Log::error('取消订单异常：' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return Response::json(Response::ERROR, '取消订单失败：' . $e->getMessage());
        }
    }

    /**
     * 退款订单
     *
     * @param Request $request 请求对象
     * @return \think\response\Json
     */
    public function refundOrder(Request $request)
    {
        try {
            $user = $request->user_info;
            $clientkeynum = $request->basekeynum;
            $order_id = $request->param('order_id');

            if (empty($order_id)) {
                return Response::json(Response::ERROR, '订单ID不能为空');
            }

            // 开启事务
            Db::startTrans();

            // 验证订单
            $order = \app\store\model\Order::where(['id' => $order_id, 'clientkeynum' => $clientkeynum])->find();
            if (empty($order)) {
                Db::rollback();
                return Response::json(Response::ERROR, '订单不存在');
            }

            if (!($order->status >= 1)) {
                return Response::json(Response::ERROR, '订单状态不允许退款');
            }

            // 处理退款
            $refund_result = $this->processOrderRefund($order, $clientkeynum);
            if (!$refund_result['success']) {
                Db::rollback();
                return Response::json(Response::ERROR, $refund_result['message']);
            }

            // 恢复库存
            $this->restoreOrderInventory($order['order_no'], $clientkeynum);

            // 更新订单状态为已取消
            \app\store\model\Order::where(['id' => $order_id])->update([
                'status' => -1,
                'cancel_time' => date('Y-m-d H:i:s'),
                'refund_status' => isset($refund_result['refund_info']['wechat_refund']) ? 2 : 0, // 有微信退款则为退款成功，否则为无退款
                'refund_amount' => ($refund_result['refund_info']['wechat_refund'] ?? 0) + ($refund_result['refund_info']['card_refund'] ?? 0),
            ]);

            // 记录订单日志
            $this->createOrderLog($order['order_no'], '店铺退款订单', $clientkeynum, $user['plat_account']['accountname']);

            // 提交事务
            Db::commit();

            return Response::json(Response::SUCCESS, '订单退款成功', [
                'refund_info' => $refund_result['refund_info']
            ]);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            Log::error('退款订单异常：' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return Response::json(Response::ERROR, '退款订单失败：' . $e->getMessage());
        }
        
    }

    

    /**
     * 处理订单退款
     * @param array $order 订单信息
     * @param string $clientkeynum 客户端密钥
     * @return array
     */
    private function processOrderRefund($order, $clientkeynum)
    {
        $refund_info = [];

        try {
            // 根据支付类型处理退款
            switch ($order['pay_type']) {
                case 1: // 微信支付
                    if ($order['price'] > 0 && $order['status'] > 0) {
                        // 调用微信退款接口
                        $payment_service = new \app\store\service\PaymentService();
                        $refund_result = $payment_service->handleWechatRefund(
                            $order,
                            $clientkeynum,
                            '订单取消',
                            request()->user_info['plat_account']['accountname'] ?? '店铺操作员'
                        );

                        if (!$refund_result['success']) {
                            return ['success' => false, 'message' => $refund_result['message']];
                        }

                        $refund_info['wechat_refund'] = $refund_result['refund_amount'];
                        $refund_info['refund_no'] = $refund_result['refund_no'];
                        $refund_info['message'] = "微信支付退款成功：{$refund_result['refund_amount']}元，退款单号：{$refund_result['refund_no']}";
                    } else {
                        $refund_info['message'] = '订单已取消';
                    }
                    break;

                case 2: // 卡支付
                    if ($order['card_price'] > 0) {
                        $card_refund = $this->processStoreCardRefund($order, $clientkeynum);
                        if (!$card_refund['success']) {
                            return ['success' => false, 'message' => $card_refund['message']];
                        }
                        $refund_info['card_refund'] = $card_refund['amount'];
                        $refund_info['message'] = "卡余额退款成功：{$card_refund['amount']}元";
                    }
                    break;

                case 3: // 组合支付
                    // 处理微信支付部分
                    $wechat_amount = $order['price'] - $order['card_price'];
                    if ($wechat_amount > 0 && $order['status'] > 0) {
                        $payment_service = new \app\store\service\PaymentService();
                        $refund_result = $payment_service->handleWechatRefund(
                            $order,
                            $clientkeynum,
                            '订单取消',
                            request()->user_info['plat_account']['accountname'] ?? '店铺操作员'
                        );

                        if (!$refund_result['success']) {
                            return ['success' => false, 'message' => $refund_result['message']];
                        }

                        $refund_info['wechat_refund'] = $refund_result['refund_amount'];
                        $refund_info['refund_no'] = $refund_result['refund_no'];
                        $refund_info['message'] = "微信支付退款成功：{$refund_result['refund_amount']}元，退款单号：{$refund_result['refund_no']}";
                    }

                    // 处理卡支付部分
                    if ($order['card_price'] > 0) {
                        $card_refund = $this->processStoreCardRefund($order, $clientkeynum);
                        if (!$card_refund['success']) {
                            return ['success' => false, 'message' => $card_refund['message']];
                        }
                        $refund_info['card_refund'] = $card_refund['amount'];
                        $refund_info['message'] .= "，卡余额退款成功：{$card_refund['amount']}元";
                    }
                    break;

                case 4: // 现金支付
                    $refund_info['message'] = '现金支付订单已取消，请联系客户退款';
                    break;

                case 0: // 免单
                    $refund_info['message'] = '免单订单已取消';
                    break;

                default:
                    return ['success' => false, 'message' => '不支持的支付方式'];
            }

            return [
                'success' => true,
                'refund_info' => $refund_info
            ];

        } catch (\Exception $e) {
            Log::error('处理退款异常：' . $e->getMessage());
            return ['success' => false, 'message' => '退款处理失败：' . $e->getMessage()];
        }
    }

    /**
     * 处理店铺卡支付退款
     * @param array $order 订单信息
     * @param string $clientkeynum 客户端密钥
     * @return array
     */
    private function processStoreCardRefund($order, $clientkeynum)
    {
        try {
            // if ($order['user_id'] <= 0) {
            //     return ['success' => false, 'message' => '无用户信息的订单无法退还卡余额'];
            // }

            $card_use_log = CardUseLog::where(['order_sn' => $order['order_no']])
                ->where('status', 1)
                ->order('id desc')
                ->find();

                if (empty($card_use_log)) {
                    return ['success' => false, 'message' => '未找到卡使用记录'];
                }
    
                // 获取卡信息
                $card = Ycard::where(['cardnum' => $card_use_log['cardnum']])->find();
                if (empty($card)) {
                    return ['success' => false, 'message' => '卡信息不存在'];
                }
    
                // 退还卡余额
                $refund_amount = $card_use_log['use_money'];
                $new_balance = bcadd($card['yu_money'], $refund_amount, 2);
                
                Ycard::where(['id' => $card['id']])->update([
                    'yu_money' => $new_balance,
                ]);
    
                // 记录退款日志
                CardUseLog::create([
                    'basekeynum' => $clientkeynum,
                    'member_id' => $order['user_id'],
                    'order_sn' => $order['order_no'],
                    'cardnum' => $card['cardnum'],
                    'yu_money' => $new_balance, // 使用前余额
                    'after_money' => $card['yu_money'], // 退款后余额
                    'use_money' => $refund_amount, // 负数表示退款
                    'status' => 2,
                    'add_time' => date('Y-m-d H:i:s'),
                ]);
    
                // 记录退款日志
                $this->createRefundLog($order, 'CARD_' . date('YmdHis'), $refund_amount, 'card', $clientkeynum);
    
                return [
                    'success' => true,
                    'amount' => $refund_amount,
                    'cardnum' => $card['cardnum']
                ];

        } catch (\Exception $e) {
            Log::error('卡退款异常：' . $e->getMessage());
            return ['success' => false, 'message' => '卡退款失败：' . $e->getMessage()];
        }
    }

    /**
     * 创建退款日志
     * @param array $order 订单信息
     * @param string $refund_no 退款单号
     * @param float $amount 退款金额
     * @param string $type 退款类型
     * @param string $clientkeynum 客户端密钥
     */
    private function createRefundLog($order, $refund_no, $amount, $type, $clientkeynum)
    {
        // 这里可以创建退款日志表来记录退款信息
        // 由于项目中可能没有退款日志表，我们通过订单日志来记录
        $type_text = $type === 'wechat' ? '微信退款' : '卡余额退款';
        $content = "订单取消{$type_text}：退款单号{$refund_no}，退款金额{$amount}元";
        
        $this->createOrderLog($order['order_no'], $content, $clientkeynum);
    }

    /**
     * 恢复订单库存
     * @param string $order_no 订单号
     * @param string $clientkeynum 客户端密钥
     */
    private function restoreOrderInventory($order_no, $clientkeynum)
    {
        // 获取订单详情
        $order_details = OrderDetail::where(['order_no' => $order_no, 'clientkeynum' => $clientkeynum])->select();
        
        // 获取订单信息
        $order = \app\store\model\Order::where(['order_no' => $order_no, 'clientkeynum' => $clientkeynum])->find();
        if (empty($order)) {
            Log::error("订单不存在：order_no={$order_no}");
            return;
        }

        // 准备入库单明细数据
        $inventory_details = [];

        foreach ($order_details as $detail) {
            if (is_string($detail['product_json'])) {
                $product_data = json_decode($detail['product_json'], true);
            } else {
                $product_data = $detail['product_json'];
            }
            if (is_string($detail['inventory_json'])) {
                $inventory_data = json_decode($detail['inventory_json'], true);
            } else {
                $inventory_data = $detail['inventory_json'];
            }

            if (empty($product_data) || empty($inventory_data)) {
                Log::error("订单详情数据异常：order_no={$order_no}, detail_id={$detail['id']}");
                continue;
            }

            // 获取库存记录
            $shop_inventory = ShopProductInventory::where([
                'shop_id' => $detail['shop_id'],
                'product_id' => $product_data['id'],
                'inventory_id' => $inventory_data['id']
            ])->find();

            if (empty($shop_inventory)) {
                Log::error("库存记录不存在：shop_id={$order['shop_id']}, product_id={$product_data['id']}, inventory_id={$inventory_data['id']}");
                continue;
            }

            // 检查是否为计量商品（通过订单详情中的actual_weight字段判断）
            if (!empty($detail['actual_weight'])) {
                // 计量商品：恢复重量库存
                $before_quantity = $shop_inventory->weight_stock;
                $change_quantity = $detail['actual_weight'];
                $after_quantity = $before_quantity + $change_quantity;
                
                $shop_inventory->addWeightStock($detail['actual_weight'], '订单取消退库');
                
                // 记录库存变动日志
                Db::name('inventory_log')->insert([
                    'clientkeynum' => $clientkeynum,
                    'shop_id' => $detail['shop_id'],
                    'product_id' => $product_data['id'],
                    'inventory_id' => $inventory_data['id'],
                    'before_quantity' => $before_quantity,
                    'change_quantity' => $change_quantity,
                    'after_quantity' => $after_quantity,
                    'change_type' => 6, // 6-退货
                    'related_no' => $order_no,
                    'operator' => request()->user_info['plat_account']['accountname'] ?? '店铺操作员',
                    'remark' => "订单取消退库：{$product_data['title']}，重量：{$detail['actual_weight']}kg",
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                
                Log::info("恢复重量库存：订单{$order_no}，商品{$product_data['title']}，重量{$detail['actual_weight']}kg");
                
                // 准备入库单明细数据（计量商品）
                $inventory_details[] = [
                    'product_id' => $product_data['id'],
                    'inventory_id' => $inventory_data['id'],
                    'quantity' => $detail['actual_weight'], // 计量商品使用重量作为数量
                    'price' => $detail['weight_unit_price'] ?? 0, // 使用单位价格
                    'discount' => 0,
                    'amount' => $detail['price'], // 使用订单中的总价
                    'product' => $product_data,
                    'inventory' => $inventory_data,
                    'is_weight_product' => true
                ];
            } else {
                // 普通商品：恢复数量库存
                $before_quantity = $shop_inventory->stock;
                $change_quantity = $detail['number'];
                $after_quantity = $before_quantity + $change_quantity;
                
                ShopProductInventory::where([
                    'shop_id' => $detail['shop_id'],
                    'product_id' => $product_data['id'],
                    'inventory_id' => $inventory_data['id']
                ])->update([
                    'stock' => Db::raw('stock + ' . $detail['number'])
                ]);
                
                // 记录库存变动日志
                Db::name('inventory_log')->insert([
                    'clientkeynum' => $clientkeynum,
                    'shop_id' => $detail['shop_id'],
                    'product_id' => $product_data['id'],
                    'inventory_id' => $inventory_data['id'],
                    'before_quantity' => $before_quantity,
                    'change_quantity' => $change_quantity,
                    'after_quantity' => $after_quantity,
                    'change_type' => 6, // 6-退货
                    'related_no' => $order_no,
                    'operator' => request()->user_info['plat_account']['accountname'] ?? '店铺操作员',
                    'remark' => "订单取消退库：{$product_data['title']} - {$inventory_data['title']}，数量：{$detail['number']}",
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                
                Log::info("恢复数量库存：订单{$order_no}，商品{$product_data['title']} - {$inventory_data['title']}，数量{$detail['number']}");
                
                // 准备入库单明细数据（普通商品）
                $inventory_details[] = [
                    'product_id' => $product_data['id'],
                    'inventory_id' => $inventory_data['id'],
                    'quantity' => $detail['number'],
                    'price' => $detail['price'] ?? 0, // 使用订单中的单价
                    'discount' => 0,
                    'amount' => $detail['amount'] ?? ($detail['price'] * $detail['number']), // 使用订单中的金额
                    'product' => $product_data,
                    'inventory' => $inventory_data,
                    'is_weight_product' => false
                ];
            }
        }
        
        // 创建入库单
        if (!empty($inventory_details)) {
            $this->createInventoryInOrder($order['shop_id'], $clientkeynum, $order_no, $inventory_details);
        }
    }

    /**
     * 创建订单取消入库单
     * @param int $shop_id 商店ID
     * @param string $clientkeynum 客户端密钥
     * @param string $order_no 关联订单号
     * @param array $inventory_details 入库明细数组
     * @return bool 创建结果
     */
    private function createInventoryInOrder($shop_id, $clientkeynum, $order_no, $inventory_details)
    {
        if (empty($inventory_details)) {
            return false;
        }

        // 创建入库单主表数据
        $inventoryOrderData = [
            'order_no' => 'II' . date('YmdHis') . mt_rand(1000, 9999), // 自动生成入库单号
            'clientkeynum' => $clientkeynum,
            'shop_id' => $shop_id,
            'order_type' => 1, // 1-入库
            'business_type' => 4, // 4-其他入库
            'related_order_no' => $order_no, // 关联订单号
            'status' => 3, // 已完成状态
            'remark' => "门店订单取消自动入库，订单号：{$order_no}",
            'created_by' => request()->user_info['plat_account']['accountname'] ?? '店铺操作员',
            'reviewed_by' => request()->user_info['plat_account']['accountname'] ?? '店铺操作员',
            'reviewed_time' => date('Y-m-d H:i:s'),
            'completed_time' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
        ];

        try {
            // 创建入库单主表记录
            $inventoryOrder = new \app\store\model\InventoryOrder();
            $inventoryOrderResult = $inventoryOrder->save($inventoryOrderData);

            if (!$inventoryOrderResult) {
                throw new \Exception('创建入库单失败');
            }

            $total_amount = 0;
            $details = [];

            // 准备所有入库单明细数据
            foreach ($inventory_details as $detail) {
                $product = $detail['product'];
                $inventory = $detail['inventory'];
                $is_weight_product = $detail['is_weight_product'] ?? false;

                $detail_data = [
                    'order_id' => $inventoryOrder->id,
                    'order_no' => $inventoryOrderData['order_no'],
                    'order_type' => 1, // 1-入库
                    'product_id' => $detail['product_id'],
                    'inventory_id' => $detail['inventory_id'],
                    'quantity' => $detail['quantity'],
                    'price' => $detail['price'],
                    'discount' => $detail['discount'],
                    'amount' => $detail['amount'],
                    'remark' => $is_weight_product 
                        ? "计量商品：{$product['title']}，重量：{$detail['quantity']}kg" 
                        : "普通商品：{$product['title']} - {$inventory['title']}，数量：{$detail['quantity']}",
                    'created_at' => date('Y-m-d H:i:s'),
                    'shop_id' => $shop_id,
                    'clientkeynum' => $clientkeynum,
                ];

                $details[] = $detail_data;
                $total_amount += $detail['amount'];
            }

            // 批量创建入库单明细
            $detailModel = new \app\store\model\InventoryOrderDetail();
            $detailResult = $detailModel->saveAll($details);

            if (!$detailResult) {
                throw new \Exception('创建入库单明细失败');
            }

            // 更新入库单主表的总金额
            $inventoryOrder->total_amount = $total_amount;
            $inventoryOrder->save();

            Log::info("订单取消入库单创建成功：入库单号{$inventoryOrderData['order_no']}，关联订单{$order_no}，总金额{$total_amount}");
            return true;

        } catch (\Exception $e) {
            Log::error('创建订单取消入库单失败：' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }
    }

    /**
     * 自动打印订单小票
     * @param string $order_no 订单号
     * @param string $clientkeynum 客户端密钥
     * @param int $shop_id 商店ID
     * @param array $order 订单数据
     * @param array $checkout_items 结算台商品
     */
    private function autoPrintOrderTicket($order_no, $clientkeynum, $shop_id, $order, $checkout_items)
    {
        try {
            // 检查门店是否配置了打印机且开启自动打印
            $printerConfig = YlyShopPrinterConfig::getByShop($shop_id, $clientkeynum);
            if (!$printerConfig) {
                Log::info("门店{$shop_id}未配置打印机，跳过打印小票");
                return;
            }

            // 构建打印数据
            $printData = $this->buildPrintData($order, $checkout_items);
            
            // 使用易联云打印服务打印小票
            $printService = new YlyPrintService($clientkeynum);
            $result = $printService->printOrderTicket($shop_id, $order_no, $printData);
            
            if ($result['success']) {
                Log::info("订单{$order_no}小票打印成功");
            } else {
                Log::error("订单{$order_no}小票打印失败：" . $result['message']);
            }
            
        } catch (\Exception $e) {
            Log::error("自动打印小票异常：订单号{$order_no}，错误信息：" . $e->getMessage());
        }
    }

    /**
     * 构建打印数据
     * @param array $order 订单数据
     * @param array $checkout_items 结算台商品
     * @return array
     */
    private function buildPrintData($order, $checkout_items)
    {
        // 获取商店信息
        $shop = Shop::where('id', $order['shop_id'])->find();
        Log::info("商店信息：" . json_encode($shop));
        
        // 获取客户信息
        $customer = [];
        if ($order['user_id'] > 0) {
            $customer = ClientMember::where('id', $order['user_id'])->find();
            Log::info("客户信息：" . json_encode($customer));
        }
        
        // 获取拣货单号
        $pickup_number = '';
        if (isset($order['pickup_number']) && !empty($order['pickup_number'])) {
            $pickup_number = \app\common\service\PickupNumberService::formatNumber($order['pickup_number']);
        } else {
            // 如果订单数据中没有拣货号，从数据库中查询
            $order_info = \app\store\model\Order::where('order_no', $order['order_no'])->find();
            if ($order_info && !empty($order_info['pickup_number'])) {
                $pickup_number = \app\common\service\PickupNumberService::formatNumber($order_info['pickup_number']);
            }
        }
        Log::info("拣货单号：" . $pickup_number);
        
        // 构建商品详情
        $products = [];
        foreach ($checkout_items as $item) {
            // 记录调试信息
            Log::info("构建打印商品信息：" . json_encode($item));
            
            $product = Product::where('id', $item['product_id'])->find();
            $inventory = ProductInventory::where('id', $item['inventory_id'])->find();
            
            if (empty($product) || empty($inventory)) {
                Log::error("商品或库存信息查询失败：product_id={$item['product_id']}, inventory_id={$item['inventory_id']}");
                continue;
            }
            
            $is_weight_product = isset($item['is_weighted']) && $item['is_weighted'];
            
            $product_info = [
                'product_name' => $product['title'] ?? '未知商品',
                'product_type' => $is_weight_product ? 2 : 1, // 2为计量商品，1为普通商品
                'spec_name' => $inventory['title'] ?? '标准规格',
                'quantity' => $is_weight_product ? 1 : $item['quantity'],
                'weight' => $is_weight_product ? $item['weight'] : 0,
                'price' => $is_weight_product ? $item['unit_price'] : $item['price'],
                'total_amount' => $is_weight_product ? $item['price'] : ($item['price'] * $item['quantity'])
            ];
            
            Log::info("构建的商品信息：" . json_encode($product_info));
            $products[] = $product_info;
        }

        $address = '';
        if (!empty($order['address_json'])) {
            // address_json {"id":32,"clientkeynum":"668AD46FFC93C10F32C8F6D0C6B1F6EB","user_id":128,"province":"\u5317\u4eac\u5e02","city":"\u5317\u4eac\u5e02","area":"\u897f\u57ce\u533a","address":"\u65b0\u534e\u6751","is_default":1,"add_time":"2025-04-30 14:46:31","update_time":"2025-04-30 14:46:31","consignee":"\u5f20\u6797\u6668","phone":"19833227324","lat":"38.88353","lng":"115.49715"}
            if (is_array($order['address_json'])) {
                $address_json = $order['address_json'];
            } else {
                $address_json = json_decode($order['address_json'], true);
            }
            
            $address = $address_json['province'] . $address_json['city'] . $address_json['area'] . $address_json['address'];
        }
        
        // 记录最终的打印数据
        $printData = [
            'order_no' => $order['order_no'],
            'pickup_number' => $pickup_number, // 添加拣货单号
            'shop_id' => $order['shop_id'],
            'shop_name' => $shop['title'] ?? '生鲜商店',
            'order_type' => $order['type'], // 门店订单类型：3=结算台订单
            'created_at' => $order['add_time'] ?? date('Y-m-d H:i:s'),
            'customer_name' => $customer['name'] ?? '',
            'customer_phone' => $customer['phone'] ?? '',
            'delivery_address' => $address, // 门店订单通常没有配送地址
            'delivery_time' => ($order['pickup_date'] ?? date('Y-m-d')) . ' ' . ($order['pickup_time'] ?? date('H:i')),
            'total_amount' => $order['origin_price'] ?? $order['price'], // 原始总价
            'delivery_fee' => $order['shipping_price'] ?? 0, // 门店订单无配送费
            'discount_amount' => $order['discount_amount'] ?? 0,
            'actual_amount' => $order['price'], // 实际支付金额
            'pay_type_text' => $this->getPayType($order['pay_type']),
            'remark' => $order['remark'] ?? '',
            'products' => $products,
        ];
        
        Log::info("最终打印数据：" . json_encode($printData));
        return $printData;
    }

    /**
     * 手动打印订单小票
     * @param Request $request
     * @return \think\response\Json
     */
    public function printOrderTicket(Request $request)
    {
        try {
            $order_id = $request->param('order_id');
            $clientkeynum = $request->basekeynum;
            
            if (empty($order_id)) {
                return Response::json(Response::ERROR, '订单ID不能为空');
            }
            
            // 获取订单信息
            $order = \app\store\model\Order::where(['id' => $order_id, 'clientkeynum' => $clientkeynum])->find();
            if (empty($order)) {
                return Response::json(Response::ERROR, '订单不存在');
            }
            
            // 获取订单详情
            $order_details = OrderDetail::where(['order_no' => $order['order_no'], 'clientkeynum' => $clientkeynum])->select();
            if (empty($order_details)) {
                return Response::json(Response::ERROR, '订单详情不存在');
            }
            
            // 构建结算台商品格式数据
            $checkout_items = [];
            foreach ($order_details as $detail) {
                // 安全处理JSON数据，判断是否已经是数组
                $product_data = is_array($detail['product_json']) 
                    ? $detail['product_json'] 
                    : json_decode($detail['product_json'], true);
                $inventory_data = is_array($detail['inventory_json']) 
                    ? $detail['inventory_json'] 
                    : json_decode($detail['inventory_json'], true);
                
                if (empty($product_data) || empty($inventory_data)) {
                    Log::error("手动打印订单详情JSON数据异常：order_no={$order['order_no']}, detail_id={$detail['id']}");
                    continue;
                }
                
                $checkout_items[] = [
                    'product_id' => $product_data['id'],
                    'inventory_id' => $inventory_data['id'],
                    'quantity' => $detail['number'],
                    'price' => $detail['price'],
                    'is_weighted' => !empty($detail['actual_weight']),
                    'weight' => $detail['actual_weight'] ?? 0,
                    'unit_price' => $detail['weight_unit_price'] ?? 0
                ];
            }
            
            // 检查门店是否配置了打印机
            $printerConfig = YlyShopPrinterConfig::getByShop($order['shop_id'], $clientkeynum);
            if (!$printerConfig || $printerConfig['status'] != 1) {
                return Response::json(Response::ERROR, '门店未配置打印机或打印机已禁用');
            }
            
            // 构建打印数据
            $printData = $this->buildPrintData($order->toArray(), $checkout_items);
            
            // 执行打印
            $printService = new YlyPrintService($clientkeynum);
            $result = $printService->printOrderTicket($order['shop_id'], $order['order_no'], $printData);
            
            if ($result['success']) {
                return Response::json(Response::SUCCESS, '打印成功');
            } else {
                return Response::json(Response::ERROR, '打印失败：' . $result['message']);
            }
            
        } catch (\Exception $e) {
            Log::error('手动打印小票异常：' . $e->getMessage());
            return Response::json(Response::ERROR, '打印失败：' . $e->getMessage());
        }
    }

    /**
     * 接单后自动打印订单小票
     * @param string $order_no 订单号
     * @param string $clientkeynum 客户端密钥
     * @param int $shop_id 商店ID
     * @param array $order 订单数据
     */
    private function autoPrintOrderTicketAfterAccept($order_no, $clientkeynum, $shop_id, $order)
    {
        try {
            // 检查门店是否配置了打印机且开启自动打印
            $printerConfig = YlyShopPrinterConfig::getByShop($shop_id, $clientkeynum);
            if (!$printerConfig) {
                Log::info("门店{$shop_id}未配置打印机，跳过接单后打印小票");
                return;
            }

            // 获取订单详情
            $order_details = OrderDetail::where(['order_no' => $order_no, 'clientkeynum' => $clientkeynum])->select();
            if (empty($order_details)) {
                Log::error("订单{$order_no}详情不存在，无法打印小票");
                return;
            }

            Log::info("获取到订单详情：" . json_encode($order_details->toArray()));

            // 参考手动打印的逻辑，构建结算台商品格式数据
            $checkout_items = [];
            foreach ($order_details as $detail) {
                // 安全处理JSON数据，判断是否已经是数组
                $product_data = is_array($detail['product_json']) 
                    ? $detail['product_json'] 
                    : json_decode($detail['product_json'], true);
                $inventory_data = is_array($detail['inventory_json']) 
                    ? $detail['inventory_json'] 
                    : json_decode($detail['inventory_json'], true);
                
                if (empty($product_data) || empty($inventory_data)) {
                    Log::error("订单详情JSON数据异常：order_no={$order_no}, detail_id={$detail['id']}");
                    Log::error("product_json类型：" . gettype($detail['product_json']) . "，内容：" . json_encode($detail['product_json']));
                    Log::error("inventory_json类型：" . gettype($detail['inventory_json']) . "，内容：" . json_encode($detail['inventory_json']));
                    continue;
                }
                
                $checkout_items[] = [
                    'product_id' => $product_data['id'],
                    'inventory_id' => $inventory_data['id'],
                    'quantity' => $detail['number'],
                    'price' => $detail['price'],
                    'is_weighted' => !empty($detail['actual_weight']),
                    'weight' => $detail['actual_weight'] ?? 0,
                    'unit_price' => $detail['weight_unit_price'] ?? 0
                ];
            }

            Log::info("构建的checkout_items：" . json_encode($checkout_items));

            // 使用与创建订单时相同的构建方法
            $printData = $this->buildPrintData($order, $checkout_items);
            
            // 使用易联云打印服务打印小票
            $printService = new YlyPrintService($clientkeynum);
            $result = $printService->printOrderTicket($shop_id, $order_no, $printData);
            
            if ($result['success']) {
                Log::info("接单后订单{$order_no}小票打印成功");
            } else {
                Log::error("接单后订单{$order_no}小票打印失败：" . $result['message']);
            }
            
        } catch (\Exception $e) {
            Log::error("接单后自动打印小票异常：订单号{$order_no}，错误信息：" . $e->getMessage());
            Log::error($e->getTraceAsString());
        }
    }

    /**
     * 查询支付状态
     *
     * @param Request $request 请求对象
     * @return \think\response\Json 包含支付状态信息的JSON响应
     */
    public function queryPayStatus(Request $request)
    {
        try {
            $clientkeynum = $request->basekeynum;
            $order_no = $request->param('order_no');

            if (empty($order_no)) {
                return Response::json(Response::ERROR, '订单号不能为空');
            }

            // 验证订单是否属于当前门店
            $user = $request->user_info;
            $basekeynum = $user['plat_account']['keynum'];
            $parent_basekeynum = $user['plat_account']['basekeynum'];
            
            $shop = Shop::where(function ($query) use($basekeynum, $parent_basekeynum) {
                $query->where('keynum', $basekeynum)
                    ->whereOr('keynum', $parent_basekeynum);
            })->find();
            
            if (empty($shop)) {
                return Response::json(Response::ERROR, '门店信息不存在');
            }

            // 验证订单是否存在且属于当前门店
            $order = \app\store\model\Order::where([
                'order_no' => $order_no,
                'clientkeynum' => $clientkeynum,
                'shop_id' => $shop['id']
            ])->find();

            if (empty($order)) {
                return Response::json(Response::ERROR, '订单不存在或不属于当前门店');
            }

            // 调用PaymentService查询支付状态
            $payment_service = new \app\store\service\PaymentService();
            $result = $payment_service->queryPayStatus($order_no, $clientkeynum);

            if ($result['status'] == 1) {
                return Response::json(Response::SUCCESS, $result['message'], $result['data'] ?? []);
            } else {
                return Response::json(Response::ERROR, $result['message']);
            }

        } catch (\Exception $e) {
            Log::error('查询支付状态异常：' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return Response::json(Response::ERROR, '查询支付状态失败：' . $e->getMessage());
        }
    }

    /**
     * 重新处理订单库存扣减
     *
     * @param Request $request 请求对象
     * @return \think\response\Json 包含处理结果的JSON响应
     */
    public function reprocessInventory(Request $request)
    {
        try {
            $user = $request->user_info;
            $clientkeynum = $request->basekeynum;
            $order_id = $request->param('order_id');

            if (empty($order_id)) {
                return Response::json(Response::ERROR, '订单ID不能为空');
            }

            // 获取门店信息
            $basekeynum = $user['plat_account']['keynum'];
            $parent_basekeynum = $user['plat_account']['basekeynum'];
            
            $shop = Shop::where(function ($query) use($basekeynum, $parent_basekeynum) {
                $query->where('keynum', $basekeynum)
                    ->whereOr('keynum', $parent_basekeynum);
            })->find();
            
            if (empty($shop)) {
                return Response::json(Response::ERROR, '门店信息不存在');
            }

            // 验证订单是否存在且属于当前门店
            $order = \app\store\model\Order::where([
                'id' => $order_id,
                'clientkeynum' => $clientkeynum,
                'shop_id' => $shop['id']
            ])->find();

            if (empty($order)) {
                return Response::json(Response::ERROR, '订单不存在或不属于当前门店');
            }

            // 检查订单状态，只允许已支付的订单重新处理库存
            if ($order['status'] < 1) {
                return Response::json(Response::ERROR, '订单状态不允许重新处理库存，请确保订单已支付');
            }

            // 检查是否已经存在出库单
            $existing_out_order = \app\store\model\InventoryOrder::where([
                'related_order_no' => $order['order_no'],
                'order_type' => 2, // 2-出库
                'business_type' => 2 // 2-销售出库
            ])->find();

            if ($existing_out_order) {
                return Response::json(Response::ERROR, '该订单已存在销售出库单，请勿重复处理。如需重新处理，请先删除相关出库单');
            }

            // 获取订单详情
            $order_details = OrderDetail::where([
                'order_no' => $order['order_no'],
                'clientkeynum' => $clientkeynum
            ])->select();

            if (empty($order_details)) {
                return Response::json(Response::ERROR, '订单详情不存在');
            }

            // 将订单详情转换为checkout_items格式
            $checkout_items = $this->convertOrderDetailsToCheckoutItems($order_details);

            if (empty($checkout_items)) {
                return Response::json(Response::ERROR, '无法解析订单商品信息');
            }

            // 开启事务
            Db::startTrans();

            // 调用库存扣减和出库单创建方法
            $this->processInventoryAndCreateOutOrder($checkout_items, $order->toArray(), $clientkeynum, $user);

            // 记录操作日志
            $this->createOrderLog(
                $order['order_no'], 
                '重新处理库存扣减和出库单创建', 
                $clientkeynum, 
                $user['plat_account']['accountname'] ?? '店铺操作员'
            );

            // 提交事务
            Db::commit();

            return Response::json(Response::SUCCESS, '订单库存重新处理成功', [
                'order_id' => $order_id,
                'order_no' => $order['order_no'],
                'processed_items' => count($checkout_items)
            ]);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            Log::error('重新处理订单库存异常：' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return Response::json(Response::ERROR, '重新处理订单库存失败：' . $e->getMessage());
        }
    }

    /**
     * 将订单详情转换为checkout_items格式
     *
     * @param array $order_details 订单详情数组
     * @return array checkout_items格式的数组
     */
    private function convertOrderDetailsToCheckoutItems($order_details)
    {
        $checkout_items = [];

        foreach ($order_details as $detail) {
            try {
                // 安全处理JSON数据
                $product_data = is_array($detail['product_json']) 
                    ? $detail['product_json'] 
                    : json_decode($detail['product_json'], true);
                $inventory_data = is_array($detail['inventory_json']) 
                    ? $detail['inventory_json'] 
                    : json_decode($detail['inventory_json'], true);

                if (empty($product_data) || empty($inventory_data)) {
                    Log::error("订单详情JSON数据异常：detail_id={$detail['id']}");
                    continue;
                }

                // 判断是否为计量商品
                $is_weight_product = !empty($detail['actual_weight']);

                if ($is_weight_product) {
                    // 计量商品
                    $checkout_items[] = [
                        'product_id' => $product_data['id'],
                        'inventory_id' => $inventory_data['id'],
                        'quantity' => 1, // 计量商品数量固定为1
                        'price' => $detail['price'], // 总价
                        'is_weighted' => true,
                        'weight' => $detail['actual_weight'],
                        'unit_price' => $detail['weight_unit_price'] ?? 0
                    ];
                } else {
                    // 普通商品
                    $checkout_items[] = [
                        'product_id' => $product_data['id'],
                        'inventory_id' => $inventory_data['id'],
                        'quantity' => $detail['number'],
                        'price' => $detail['price'], // 单价
                        'is_weighted' => false,
                        'weight' => 0,
                        'unit_price' => 0
                    ];
                }

            } catch (\Exception $e) {
                Log::error("转换订单详情异常：detail_id={$detail['id']}, error=" . $e->getMessage());
                continue;
            }
        }

        return $checkout_items;
    }

    /**
     * 订单重新入库操作（公开接口，无需登录验证）
     * 
     * @param string $order_no 订单号
     * @param string $clientkeynum 客户端密钥
     * @return \think\response\Json
     */
    public function restock($order_no = '', $clientkeynum = '')
    {
        try {
            // 验证参数
            if (empty($order_no)) {
                return Response::json(Response::ERROR, '订单号不能为空');
            }
            
            if (empty($clientkeynum)) {
                return Response::json(Response::ERROR, '客户端密钥不能为空');
            }
            
            // 验证订单是否存在
            $order = \app\store\model\Order::where([
                'order_no' => $order_no,
                'clientkeynum' => $clientkeynum
            ])->find();
            
            if (empty($order)) {
                return Response::json(Response::ERROR, '订单不存在');
            }
            
            // 获取订单详情
            $order_details = OrderDetail::where([
                'order_no' => $order_no,
                'clientkeynum' => $clientkeynum
            ])->select();
            
            if (empty($order_details)) {
                return Response::json(Response::ERROR, '订单详情不存在');
            }
            
            // 准备入库单明细数据
            $inventory_details = [];
            
            foreach ($order_details as $detail) {
                if (is_string($detail['product_json'])) {
                    $product_data = json_decode($detail['product_json'], true);
                } else {
                    $product_data = $detail['product_json'];
                }
                
                if (is_string($detail['inventory_json'])) {
                    $inventory_data = json_decode($detail['inventory_json'], true);
                } else {
                    $inventory_data = $detail['inventory_json'];
                }
                
                if (empty($product_data) || empty($inventory_data)) {
                    Log::error("订单详情数据异常：order_no={$order_no}, detail_id={$detail['id']}");
                    continue;
                }
                
                // 获取库存记录
                $shop_inventory = ShopProductInventory::where([
                    'shop_id' => $detail['shop_id'],
                    'product_id' => $product_data['id'],
                    'inventory_id' => $inventory_data['id']
                ])->find();
                
                if (empty($shop_inventory)) {
                    Log::error("库存记录不存在：shop_id={$detail['shop_id']}, product_id={$product_data['id']}, inventory_id={$inventory_data['id']}");
                    continue;
                }
                
                // 检查是否为计量商品（通过订单详情中的actual_weight字段判断）
                if (!empty($detail['actual_weight'])) {
                    // 计量商品：恢复重量库存
                    $before_quantity = $shop_inventory->weight_stock;
                    $change_quantity = $detail['actual_weight'];
                    $after_quantity = $before_quantity + $change_quantity;
                    
                    // $shop_inventory->addWeightStock($detail['actual_weight'], '订单重新入库');
                    
                    // 记录库存变动日志
                    // Db::name('inventory_log')->insert([
                    //     'clientkeynum' => $clientkeynum,
                    //     'shop_id' => $detail['shop_id'],
                    //     'product_id' => $product_data['id'],
                    //     'inventory_id' => $inventory_data['id'],
                    //     'before_quantity' => $before_quantity,
                    //     'change_quantity' => $change_quantity,
                    //     'after_quantity' => $after_quantity,
                    //     'change_type' => 6, // 6-退货
                    //     'related_no' => $order_no,
                    //     'operator' => '系统自动入库',
                    //     'remark' => "订单重新入库：{$product_data['title']}，重量：{$detail['actual_weight']}kg",
                    //     'created_at' => date('Y-m-d H:i:s')
                    // ]);
                    
                    Log::info("恢复重量库存：订单{$order_no}，商品{$product_data['title']}，重量{$detail['actual_weight']}kg");
                    
                    // 准备入库单明细数据（计量商品）
                    $inventory_details[] = [
                        'product_id' => $product_data['id'],
                        'inventory_id' => $inventory_data['id'],
                        'quantity' => $detail['actual_weight'], // 计量商品使用重量作为数量
                        'price' => $detail['weight_unit_price'] ?? 0, // 使用单位价格
                        'discount' => 0,
                        'amount' => $detail['price'], // 使用订单中的总价
                        'product' => $product_data,
                        'inventory' => $inventory_data,
                        'is_weight_product' => true
                    ];
                } else {
                    // 普通商品：恢复数量库存
                    $before_quantity = $shop_inventory->stock;
                    $change_quantity = $detail['number'];
                    $after_quantity = $before_quantity + $change_quantity;
                    
                    // ShopProductInventory::where([
                    //     'shop_id' => $detail['shop_id'],
                    //     'product_id' => $product_data['id'],
                    //     'inventory_id' => $inventory_data['id']
                    // ])->update([
                    //     'stock' => Db::raw('stock + ' . $detail['number'])
                    // ]);
                    
                    // 记录库存变动日志
                    Db::name('inventory_log')->insert([
                        'clientkeynum' => $clientkeynum,
                        'shop_id' => $detail['shop_id'],
                        'product_id' => $product_data['id'],
                        'inventory_id' => $inventory_data['id'],
                        'before_quantity' => $before_quantity,
                        'change_quantity' => $change_quantity,
                        'after_quantity' => $after_quantity,
                        'change_type' => 6, // 6-退货
                        'related_no' => $order_no,
                        'operator' => '系统自动入库',
                        'remark' => "订单重新入库：{$product_data['title']} - {$inventory_data['title']}，数量：{$detail['number']}",
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                    
                    Log::info("恢复数量库存：订单{$order_no}，商品{$product_data['title']} - {$inventory_data['title']}，数量{$detail['number']}");
                    
                    // 准备入库单明细数据（普通商品）
                    $inventory_details[] = [
                        'product_id' => $product_data['id'],
                        'inventory_id' => $inventory_data['id'],
                        'quantity' => $detail['number'],
                        'price' => $detail['price'] ?? 0, // 使用订单中的单价
                        'discount' => 0,
                        'amount' => $detail['amount'] ?? ($detail['price'] * $detail['number']), // 使用订单中的金额
                        'product' => $product_data,
                        'inventory' => $inventory_data,
                        'is_weight_product' => false
                    ];
                }
            }
            
            // 创建入库单
            if (!empty($inventory_details)) {
                // 创建入库单主表数据
                $inventoryOrderData = [
                    'order_no' => 'II' . date('YmdHis') . mt_rand(1000, 9999), // 自动生成入库单号
                    'clientkeynum' => $clientkeynum,
                    'shop_id' => $order['shop_id'],
                    'order_type' => 1, // 1-入库
                    'business_type' => 4, // 4-其他入库
                    'related_order_no' => $order_no, // 关联订单号
                    'status' => 3, // 已完成状态
                    'remark' => "订单重新入库，订单号：{$order_no}",
                    'created_by' => '系统自动入库',
                    'reviewed_by' => '系统自动入库',
                    'reviewed_time' => date('Y-m-d H:i:s'),
                    'completed_time' => date('Y-m-d H:i:s'),
                    'created_at' => date('Y-m-d H:i:s'),
                ];

                try {
                    // 创建入库单主表记录
                    $inventoryOrder = new \app\store\model\InventoryOrder();
                    $inventoryOrderResult = $inventoryOrder->save($inventoryOrderData);

                    if (!$inventoryOrderResult) {
                        throw new \Exception('创建入库单失败');
                    }

                    $total_amount = 0;
                    $details = [];

                    // 准备所有入库单明细数据
                    foreach ($inventory_details as $detail) {
                        $product = $detail['product'];
                        $inventory = $detail['inventory'];
                        $is_weight_product = $detail['is_weight_product'] ?? false;

                        $detail_data = [
                            'order_id' => $inventoryOrder->id,
                            'order_no' => $inventoryOrderData['order_no'],
                            'order_type' => 1, // 1-入库
                            'product_id' => $detail['product_id'],
                            'inventory_id' => $detail['inventory_id'],
                            'quantity' => $detail['quantity'],
                            'price' => $detail['price'],
                            'discount' => $detail['discount'],
                            'amount' => $detail['amount'],
                            'remark' => $is_weight_product 
                                ? "计量商品：{$product['title']}，重量：{$detail['quantity']}kg" 
                                : "普通商品：{$product['title']} - {$inventory['title']}，数量：{$detail['quantity']}",
                            'created_at' => date('Y-m-d H:i:s'),
                            'shop_id' => $order['shop_id'],
                            'clientkeynum' => $clientkeynum,
                        ];

                        $details[] = $detail_data;
                        $total_amount += $detail['amount'];
                    }

                    // 批量创建入库单明细
                    $detailModel = new \app\store\model\InventoryOrderDetail();
                    $detailResult = $detailModel->saveAll($details);

                    if (!$detailResult) {
                        throw new \Exception('创建入库单明细失败');
                    }

                    // 更新入库单主表的总金额
                    $inventoryOrder->total_amount = $total_amount;
                    $inventoryOrder->save();

                    // 记录订单日志
                    $this->createOrderLog($order_no, '系统自动将订单重新入库', $clientkeynum, '系统自动入库');

                    Log::info("订单重新入库成功：入库单号{$inventoryOrderData['order_no']}，关联订单{$order_no}，总金额{$total_amount}");
                    
                    return Response::json(Response::SUCCESS, '订单重新入库成功', [
                        'inventory_order_no' => $inventoryOrderData['order_no'],
                        'total_amount' => $total_amount,
                        'processed_items' => count($inventory_details)
                    ]);

                } catch (\Exception $e) {
                    Log::error('创建订单入库单失败：' . $e->getMessage());
                    Log::error($e->getTraceAsString());
                    return Response::json(Response::ERROR, '创建入库单失败：' . $e->getMessage());
                }
            } else {
                return Response::json(Response::ERROR, '没有可入库的商品');
            }
        } catch (\Exception $e) {
            Log::error('订单重新入库异常：' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return Response::json(Response::ERROR, '订单重新入库失败：' . $e->getMessage());
        }
    }

}
