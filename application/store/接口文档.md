# 门店扫码接口文档

## 简介

本文档描述门店扫码管理模块的接口，包括储值卡核销、订单核销等功能。这些接口主要由商家端使用，用于处理用户到店自提或使用储值卡消费的场景。

## 接口列表

### 1. 储值卡核销

#### 接口描述
通过扫码核销用户的储值卡，从卡内扣除指定金额。

#### 请求URL
```
POST /store/scan/checkCard
```

#### 请求参数

| 参数名 | 类型 | 必选 | 描述 |
| --- | --- | --- | --- |
| user_id | int | 是 | 用户ID |
| cardnum | string | 是 | 卡号（URL编码格式） |
| money | float | 是 | 核销金额 |
| remark | string | 是 | 核销备注 |

#### 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| code | int | 状态码，0表示成功，-1表示失败 |
| msg | string | 返回信息 |

#### 响应示例
```json
{
    "code": 0,
    "msg": "核销成功"
}
```

#### 错误码说明

| 错误码 | 说明 |
| --- | --- |
| -1 | 门店不存在 |
| -1 | 参数不能为空 |
| -1 | 储值卡不存在 |
| -1 | 卡号不在有效期 |
| -1 | 卡号未开卡 |
| -1 | 金额不足 |

### 2. 获取用户储值卡

#### 接口描述
获取指定用户的有效储值卡列表，包括余额、有效期等信息。

#### 请求URL
```
GET /store/scan/getUserCard
```

#### 请求参数

| 参数名 | 类型 | 必选 | 描述 |
| --- | --- | --- | --- |
| user_id | int | 是 | 用户ID |
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页记录数，默认10 |

#### 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| code | int | 状态码，0表示成功 |
| msg | string | 返回信息 |
| data | object | 返回数据 |
| data.list | array | 储值卡列表 |
| data.count | int | 总记录数 |

#### 响应示例
```json
{
    "code": 0,
    "msg": "查询成功",
    "data": {
        "list": [
            {
                "id": 1,
                "cardnum": "CARD123456",
                "member_id": 10001,
                "yu_money": 100.00,
                "status": 3,
                "begin_dui": 1634172000,
                "end_dui": 1665708000
            }
        ],
        "count": 1
    }
}
```

### 3. 扫码查询用户

#### 接口描述
通过扫码获取用户信息，可使用用户UUID或手机号进行查询。

#### 请求URL
```
GET /store/scan/scanUser
```

#### 请求参数

| 参数名 | 类型 | 必选 | 描述 |
| --- | --- | --- | --- |
| keyword | string | 是 | 查询关键词（用户UUID或手机号） |

#### 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| code | int | 状态码，0表示成功，-1表示失败 |
| msg | string | 返回信息 |
| data | object | 返回数据 |
| data.user | object | 用户信息 |

#### 响应示例
```json
{
    "code": 0,
    "msg": "扫码成功",
    "data": {
        "user": {
            "id": 10001,
            "uuid": "UUID12345",
            "phone": "13800138000",
            "nickname": "测试用户"
        }
    }
}
```

### 4. 扫码查询订单

#### 接口描述
通过扫码获取订单详情信息，包括订单商品、门店信息等。

#### 请求URL
```
GET /store/scan/scanOrder
```

#### 请求参数

| 参数名 | 类型 | 必选 | 描述 |
| --- | --- | --- | --- |
| order_no | string | 是 | 订单验证码 |

#### 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| code | int | 状态码，0表示成功，-1表示失败 |
| msg | string | 返回信息 |
| data | object | 返回数据 |
| data.order | object | 订单信息，包括orderDetail和shop关联数据 |

#### 响应示例
```json
{
    "code": 0,
    "msg": "扫码成功",
    "data": {
        "order": {
            "id": 1,
            "order_no": "ORDER2024101400001",
            "verify_code": "VERIFY123",
            "status": 1,
            "type": 1,
            "orderDetail": [
                {
                    "id": 1,
                    "order_no": "ORDER2024101400001",
                    "product_id": 101,
                    "product_name": "测试商品",
                    "price": 10.00,
                    "num": 2
                }
            ],
            "shop": {
                "id": 1,
                "name": "测试门店",
                "address": "测试地址"
            }
        }
    }
}
```

### 5. 核销订单

#### 接口描述
核销自提订单，将订单状态修改为已核销。

#### 请求URL
```
POST /store/scan/writeOffOrder
```

#### 请求参数

| 参数名 | 类型 | 必选 | 描述 |
| --- | --- | --- | --- |
| order_id | int | 是 | 订单ID |

#### 响应参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| code | int | 状态码，0表示成功，-1表示失败 |
| msg | string | 返回信息 |

#### 响应示例
```json
{
    "code": 0,
    "msg": "订单已核销"
}
```

#### 错误码说明

| 错误码 | 说明 |
| --- | --- |
| -1 | 订单不存在 |
| -1 | 当前订单不是自提订单 |
| -1 | 该订单已核销 |
| -1 | 当前订单状态不为待核销 |
| -1 | 更新订单状态失败 |

## 状态码说明

| 状态码 | 说明 |
| --- | --- |
| 0 | 请求成功 |
| -1 | 请求失败 |

## 订单状态说明

| 状态码 | 说明 |
| --- | --- |
| 1 | 待核销 |
| 100 | 已核销 |

## 储值卡状态说明

| 状态码 | 说明 |
| --- | --- |
| 3 | 已开卡（有效） | 