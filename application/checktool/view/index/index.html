<!DOCTYPE html>
<html>
<title>监控面板</title>
{include file="public/iframeheader"/}

<body>
  <div class="layui-fluid" id="LAY-component-timeline">
    <div class="layui-row layui-col-space15">

      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">监控面板:共<font link="count" ></font>台后端服务器</div>
          <div class="layui-card-body">

            <ul class="layui-timeline">
              
              <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe67f;</i>
                <div class="layui-timeline-content layui-text">
                  <h3 class="layui-timeline-title">服务器信息</h3>
                  <p></p>
                  <ul link="serverlist">
                  </ul>
                </div>
              </li>              
              

              <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe7ae;</i>
                <div class="layui-timeline-content layui-text">
                  <h3 class="layui-timeline-title">web服务器</h3>
                  <p></p>
                  <ul link="webserverlist">
                  </ul>
                </div>
              </li>  


              <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe636;</i>
                <div class="layui-timeline-content layui-text">
                  <h3 class="layui-timeline-title">thinkphp版本</h3>
                  <p></p>
                  <ul link="thinkversionlist">
                  </ul>
                </div>
              </li>  

              <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe756;</i>
                <div class="layui-timeline-content layui-text">
                  <h3 class="layui-timeline-title">php版本</h3>
                  <p></p>
                  <ul link="phpversionlist">
                  </ul>
                </div>
              </li>  


              <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe62d;</i>
                <div class="layui-timeline-content layui-text">
                  <h3 class="layui-timeline-title">mysql信息</h3>
                  <p></p>
                  <ul link="mysql">
                  </ul>
                </div>
              </li>

              <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe62c;</i>
                <div class="layui-timeline-content layui-text">
                  <h3 class="layui-timeline-title">redis信息</h3>
                  <p></p>
                  <ul link="redis">
                  </ul>
                </div>
              </li>

              <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe66a;</i>
                <div class="layui-timeline-content layui-text">
                  <h3 class="layui-timeline-title">.env检查</h3>
                  <p></p>
                  <ul link="env">
                  </ul>
                </div>
              </li>



              <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe628;</i>
                <div class="layui-timeline-content layui-text">
                  <div class="layui-timeline-title">
                    opcache信息：
                    <button type="button" link="go_opcache" class="layui-btn layui-btn-sm">查看</button>
                    <button type="button" link="clearcode" class="layui-btn layui-btn-sm">清理字节码缓存</button>
                  </div>
                </div>
              </li>

              <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe639;</i>
                <div class="layui-timeline-content layui-text">
                  <div class="layui-timeline-title">
                    runtime下面temp：
                    <button type="button" link="cleartemp" class="layui-btn layui-btn-sm">清理temp</button>
                  </div>
                </div>
              </li>




            </ul>

          </div>

        </div>
      </div>

    </div>

  </div>
</body>

</html>


<script>
  //一般直接写在一个js文件中
  layui.use(['element', 'form', 'layedit'], function () {
    $("[link=clearcode]").click(function () {
      $.post("{:url('clearcode')}", {}, function (data) {
        if (data.sta == 1) {
          layer.alert(data.html);
        } else {
          layer.alert(data.msg);
        }
      }, "json");
    });

    $("[link=cleartemp]").click(function () {
      $.post("{:url('cleartemp')}", {}, function (data) {
        if (data.sta == 1) {
          layer.alert(data.html);
        } else {
          layer.alert(data.msg);
        }
      }, "json");
    });

    //页面加载完毕先用js把后端服务器存入sesion，存好之后回调成功里面获取msyql，redis等其他连接信息
    $.ajax({
      type: 'get',
      dataType: "json",
      data: {type:"{:input('type')}"},
      url: "{:url('get_server_list')}",
      timeout: 5000,//5秒超时
      success: function (data) {
        //获取后端服务器列表
        $("[link=serverlist]").html(data.html);
        $("[link=count]").html(data.count);

        //ajax获取web服务器信息
        $.ajax({
          type: 'get',
          dataType: "json",
          data: {},
          url: "{:url('get_webserver_info')}",
          timeout: 5000,//5秒超时
          success: function (data) {
            $("[link=webserverlist]").html(data.html);
          },
          error: function (xhr, textStatus) {
            if (textStatus == 'timeout') {
              //处理超时的逻辑
              layer.alert("获取web服务器信息获取请求超时");
            }
            else {
              //其他错误的逻辑
              layer.alert("web服务器信息获取：" + textStatus + ":" + xhr.status + ",详细信息:" + xhr.statusText);
            }
          },
        });

        //ajax获取thinkphp版本信息
        $.ajax({
          type: 'get',
          dataType: "json",
          data: {},
          url: "{:url('get_tp_info')}",
          timeout: 5000,//5秒超时
          success: function (data) {
            $("[link=thinkversionlist]").html(data.html);
          },
          error: function (xhr, textStatus) {
            if (textStatus == 'timeout') {
              //处理超时的逻辑
              layer.alert("获取thinkphp版本信息获取请求超时");
            }
            else {
              //其他错误的逻辑
              layer.alert("thinkphp版本信息获取：" + textStatus + ":" + xhr.status + ",详细信息:" + xhr.statusText);
            }
          },
        });

        //ajax获取php版本信息
        $.ajax({
          type: 'get',
          dataType: "json",
          data: {},
          url: "{:url('get_php_info')}",
          timeout: 5000,//5秒超时
          success: function (data) {
            $("[link=phpversionlist]").html(data.html);
          },
          error: function (xhr, textStatus) {
            if (textStatus == 'timeout') {
              //处理超时的逻辑
              layer.alert("获取php版本信息获取请求超时");
            }
            else {
              //其他错误的逻辑
              layer.alert("php版本信息获取：" + textStatus + ":" + xhr.status + ",详细信息:" + xhr.statusText);
            }
          },
        });
        



        //ajax获取msyql连接信息
        $.ajax({
          type: 'get',
          dataType: "json",
          data: {},
          url: "{:url('get_msyql_info')}",
          timeout: 5000,//5秒超时
          success: function (data) {
            $("[link=mysql]").html(data.html);
          },
          error: function (xhr, textStatus) {
            if (textStatus == 'timeout') {
              //处理超时的逻辑
              layer.alert("mysql信息获取请求超时");
            }
            else {
              //其他错误的逻辑
              layer.alert("msyql信息获取：" + textStatus + ":" + xhr.status + ",详细信息:" + xhr.statusText);
            }
          },
        });

        //ajax获取redis连接信息
        $.ajax({
          type: 'get',
          dataType: "json",
          data: {},
          url: "{:url('get_redis_info')}",
          timeout: 5000,//5秒超时
          success: function (data) {
            $("[link=redis]").html(data.html);
          },
          error: function (xhr, textStatus) {
            if (textStatus == 'timeout') {
              //处理超时的逻辑
              layer.alert("redis信息获取请求超时");
            }
            else {
              //其他错误的逻辑
              layer.alert("redis信息获取：" + textStatus + ":" + xhr.status + ",详细信息:" + xhr.statusText);
            }
          },
        });

        //ajax获取evn信息
        $.ajax({
          type: 'get',
          dataType: "json",
          data: {},
          url: "{:url('get_env_info')}",
          timeout: 5000,//5秒超时
          success: function (data) {
            $("[link=env]").html(data.html);
          },
          error: function (xhr, textStatus) {
            if (textStatus == 'timeout') {
              //处理超时的逻辑
              layer.alert("env信息获取请求超时");
            }
            else {
              //其他错误的逻辑
              layer.alert("env信息获取：" + textStatus + ":" + xhr.status + ",详细信息:" + xhr.statusText);
            }
          },
        });


      },
      error: function (xhr, textStatus) {
        if (textStatus == 'timeout') {
          //处理超时的逻辑
          layer.alert("获取后端服务器信息请求超时");
        }
        else {
          //其他错误的逻辑
          layer.alert("服务器信息获取：" + textStatus + ":" + xhr.status + ",详细信息:" + xhr.statusText);
        }
      },
    });

    //opcache跳转
    $("[link=go_opcache]").click(function (){
        window.location.href="{:url('opcache')}?token={$token}";
    });

  });
</script>