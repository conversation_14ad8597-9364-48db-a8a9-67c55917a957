<?php
// +----------------------------------------------------------------------
// | admin [ Programming makes me happy ]
// +----------------------------------------------------------------------
// | Copyright ( c ) 2020 04 07 http://www.wlphp.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: wl < <EMAIL> >
// +----------------------------------------------------------------------
namespace app\checktool\controller;

use think\Controller;
use think\Db;

use think\facade\Request;
use think\facade\Session;
use think\facade\Cache;

class IndexController extends Controller
 {

    public function _initialize()
 {
        //slb面板地址: /checktool.php?token = wl905507&type = 1
        //普通部署地址:/checktool.php?token = wl905507
        $this->token = 'wl905507';
        //查看密码
        $this->cycelnum = 20;
        //循环次数
    }

    //探针环境监测专用

    public function index()
 {

        /*
        $trace = config( 'app_trace' );
        var_dump( $trace );

        $checkfile = ROOT_PATH.'.env';
        if ( file_exists( $checkfile ) ) {
            echo '.env存在';
        } else {
            echo '.env不存在';
        }
        */

        $request = Request::instance();
        $param = $request->param();
        $token = $param['token'];

        //鉴权
        if ( $token != $this->token ) {
            error_tips( '当前连接未授权，请授权后在查询！' );
            die;
        }
        $this->assign( 'token', $this->token );

        return  $this->fetch( 'index' );

    }

    //opcache面板

    public function opcache() {

        print_r( $_SERVER );
        echo  '<br/>';

        $request = Request::instance();
        $param = $request->param();
        $token = $param['token'];
        //鉴权
        if ( $token != $this->token ) {
            error_tips( '当前连接未授权，请授权后在查询！' );
            die;
        }

        $http = get_http_type();
        $server_ip = $_SERVER['SERVER_ADDR'] == '::1'?'127.0.0.1':$_SERVER['SERVER_ADDR'];
        $port = $_SERVER['SERVER_PORT'];
        echo  $http. $server_ip.':'.$port;

        //定义常量
        define( 'OPCACHE_FLAG', 1 );
        require_once './check-tool/opcache.php';
    }

    //获取后端服务器列表

    public function get_server_list() {
        $request = Request::instance();
        $param = $request->param();
        $type = $param['type'];
        //循环n次然后去重复
        for ( $x = 0; $x <= $this->cycelnum; $x++ ) {
            //调用接口清理code码
            $url = get_http_type().$_SERVER['HTTP_HOST'].'/checktool.php/index/get_server_api?type='.$type;
            $rs = file_get_contents( $url );
            //兼容trace打印
            $end = strpos( $rs, '<div' );
            if ( $end != '0' ) {
                $rs = substr( $rs, 0, $end );
            }
            $arr[] = $rs;
        }
        //数组去重复
        $arr = array_unique( $arr );
        session( 'cn_plat_server_list', $arr );
        $html = '';
        foreach ( $arr as $k=>$v ) {
            $html .= '<li>'.$v.'</li>';
        }
        $rt['sta'] = 1;
        $rt['msg'] = '操作成功';
        $rt['html'] = $html;
        $rt['count'] = count( $arr );
        echo json_encode( $rt );
        die;

    }

    //获取服务器方法

    public function get_server_api () {
        $request = Request::instance();
        $param = $request->param();
        $type = $param['type'];

        $http = get_http_type();
        if ( $type == 'slb' ) {
            //内网地址
            $server_ip = $_SERVER['SERVER_ADDR'] == '::1'?'127.0.0.1':$_SERVER['SERVER_ADDR'];
        } else {
            //域名外网地址
            $server_ip = $_SERVER['HTTP_HOST'];
        }
        $port = $_SERVER['SERVER_PORT'];
        return $http. $server_ip.':'.$port;
    }

    //获取web服务器信息接口

    public function get_webserver_info () {
        //后端服务器列表
        $serverlist = session( 'cn_plat_server_list' );
        foreach ( $serverlist as $k=>$v ) {
            $url = $v.'/checktool.php/index/webserver_info_api';
            $rs = file_get_contents( $url );
            $arr[] = $rs;
        }
        //数组去重复
        //$arr = array_unique( $arr );
        $html = '';
        foreach ( $arr as $k=>$v ) {
            $html .= '<li>'.$v.'</li>';
        }
        $rt['sta'] = 1;
        $rt['msg'] = '获取成功';
        $rt['html'] = $html;
        echo json_encode( $rt );
        die;
    }

    //获取web服务器方法

    public function  webserver_info_api() {
        $server_addr = $_SERVER['SERVER_ADDR'];
        //获取web服务器
        if ( PHP_SAPI == 'fpm-fcgi' ) {
            $http_version = 'nginx + php-fpm';
        } else if ( PHP_SAPI == 'cgi-fcgi' ) {
            $http_version = 'nginx + fastcgi';
        } else if ( PHP_SAPI == 'apache2handler' ) {
            $http_version = 'apache';
        } else if ( PHP_SAPI == 'cli' ) {
            $http_version = 'php命令行';
        } else {
            $http_version = '未知';
        }
        return '服务器'.$server_addr.'的webserver:'.$http_version;
    }

    //获取thinkphp版本信息

    public function get_tp_info () {
        //后端服务器列表
        $serverlist = session( 'cn_plat_server_list' );
        foreach ( $serverlist as $k=>$v ) {
            $url = $v.'/checktool.php/index/tp_info_api';
            $rs = file_get_contents( $url );
            $arr[] = $rs;
        }
        //数组去重复
        //$arr = array_unique( $arr );
        $html = '';
        foreach ( $arr as $k=>$v ) {
            $html .= '<li>'.$v.'</li>';
        }
        $rt['sta'] = 1;
        $rt['msg'] = '获取成功';
        $rt['html'] = $html;
        echo json_encode( $rt );
        die;
    }

    //获取thinkphp版本方法

    public function  tp_info_api() {
        $server_addr = $_SERVER['SERVER_ADDR'];
        $tpversion = THINK_VERSION;
        return '服务器'.$server_addr.'的thinkphp版本:'.$tpversion;
    }

    //获取php版本信息

    public function get_php_info () {
        //后端服务器列表
        $serverlist = session( 'cn_plat_server_list' );
        foreach ( $serverlist as $k=>$v ) {
            $url = $v.'/checktool.php/index/php_info_api';
            $rs = file_get_contents( $url );
            $arr[] = $rs;
        }
        //数组去重复
        //$arr = array_unique( $arr );
        $html = '';
        foreach ( $arr as $k=>$v ) {
            $html .= '<li>'.$v.'</li>';
        }
        $rt['sta'] = 1;
        $rt['msg'] = '获取成功';
        $rt['html'] = $html;
        echo json_encode( $rt );
        die;
    }

    //获取php版本方法

    public function  php_info_api() {
        $server_addr = $_SERVER['SERVER_ADDR'];
        $phpversion = PHP_VERSION;
        return '服务器'.$server_addr.'的thinkphp版本:'.$phpversion;
    }

    //获取mysql信息接口

    public function get_msyql_info () {
        //后端服务器列表
        $serverlist = session( 'cn_plat_server_list' );
        foreach ( $serverlist as $k=>$v ) {
            $url = $v.'/checktool.php/index/mysql_info_api';
            $rs = file_get_contents( $url );
            $arr[] = $rs;
        }
        //数组去重复
        //$arr = array_unique( $arr );
        $html = '';
        foreach ( $arr as $k=>$v ) {
            $html .= '<li>'.$v.'</li>';
        }
        $rt['sta'] = 1;
        $rt['msg'] = '获取成功';
        $rt['html'] = $html;
        echo json_encode( $rt );
        die;
    }

    //获取msyql方法

    public function  mysql_info_api() {
        $server_addr = $_SERVER['SERVER_ADDR'];
        $rs = Db::query( 'select VERSION() as sqlversion' );
        $mysql_version = $rs[0]['sqlversion'];
        $hostname = config( 'database' )['hostname'];
        return '服务器'.$server_addr.'连接'. $hostname.'获取msyql版本信息'.$mysql_version.',连接成功';
    }

    //获取redis信息接口

    public function get_redis_info () {
        //后端服务器列表
        $serverlist = session( 'cn_plat_server_list' );
        foreach ( $serverlist as $k=>$v ) {
            $url = $v.'/checktool.php/index/redis_info_api';
            $rs = file_get_contents( $url );
            $arr[] = $rs;
        }
        //数组去重复
        //$arr = array_unique( $arr );
        $html = '';
        foreach ( $arr as $k=>$v ) {
            $html .= '<li>'.$v.'</li>';
        }
        $rt['sta'] = 1;
        $rt['msg'] = '获取成功';
        $rt['html'] = $html;
        echo json_encode( $rt );
        die;
    }

    //获取redis方法

    public function  redis_info_api() {
        $server_addr = $_SERVER['SERVER_ADDR'];
        //获取redis版本
        $redis = new \Redis();
        $redis->connect( config( 'session' )['host'], config( 'session' )['port'] );
        $redis->auth( config( 'session' )['password'] );
        //密码验证
        $info = $redis->info();
        $redis->close();
        return '服务器'.$server_addr.'链接'.config( 'session' )['host'].'获取redis版本信息'.$info['redis_version'] .',连接成功';
    }

    //清理字节码接口

    public function clearcode () {

        //后端服务器列表
        $serverlist = session( 'cn_plat_server_list' );
        foreach ( $serverlist as $k=>$v ) {
            $url = $v.'/checktool.php/index/clearcodeapi';
            $rs = file_get_contents( $url );
            $arr[] = $rs;
        }
        //数组去重复
        //$arr = array_unique( $arr );
        $str = implode( '<br/>', $arr );
        $rt['sta'] = 1;
        $rt['msg'] = '操作成功';
        $rt['html'] = $str;
        echo json_encode( $rt );
        die;
    }

    //清理字节码具体方法

    public function  clearcodeapi() {
        $server_addr = $_SERVER['SERVER_ADDR'];
        if ( !extension_loaded( 'Zend OPcache' ) ) {
            return '服务器'.$server_addr.'Zend OPcache 暂未开启，请在生产环境安装扩展，已提高php的效率！';
        }
        $flag = opcache_reset();
        return '服务器'.$server_addr.'清理字节码缓存成功!';
    }

    //清理runtime下面的temp文件夹里面内容

    public function cleartemp () {
        //后端服务器列表
        $serverlist = session( 'cn_plat_server_list' );
        foreach ( $serverlist as $k=>$v ) {
            $url = $v.'/checktool.php/index/cleartempapi';
            $rs = file_get_contents( $url );
            $arr[] = $rs;
        }
        //数组去重复
        //$arr = array_unique( $arr );
        $str = implode( '<br/>', $arr );
        $rt['sta'] = 1;
        $rt['msg'] = '操作成功';
        $rt['html'] = $str;
        echo json_encode( $rt );
        die;
    }

    //清理temp具体方法

    public function  cleartempapi() {
        $server_addr = $_SERVER['SERVER_ADDR'];
        //清理runtime下面的temp文件夹里面内容
        $arr = array_map( 'unlink', glob( TEMP_PATH.'*.php' ) );
        $num = count( $arr );
        return '服务器'.$server_addr.'清理runtime下面的temp成功，共计文件数'.$num;
    }

    //获取env信息接口

    public function get_env_info () {
        //后端服务器列表
        $serverlist = session( 'cn_plat_server_list' );
        foreach ( $serverlist as $k=>$v ) {
            $url = $v.'/checktool.php/index/env_info_api';
            $rs = file_get_contents( $url );
            $arr[] = $rs;
        }
        //数组去重复
        //$arr = array_unique( $arr );
        $html = '';
        foreach ( $arr as $k=>$v ) {
            $html .= '<li>'.$v.'</li>';
        }
        $rt['sta'] = 1;
        $rt['msg'] = '获取成功';
        $rt['html'] = $html;
        echo json_encode( $rt );
        die;
    }

    //获取env方法

    public function  env_info_api() {
        $server_addr = $_SERVER['SERVER_ADDR'];
        $checkfile = ROOT_PATH.'.env';
        if ( file_exists( $checkfile ) ) {
            return '服务器'.$server_addr.'<font color=red>.env文件存在，请删除</font>';
        } else {
            return '服务器'.$server_addr.'.env文件不存在，系统正常';
        }
    }

}
