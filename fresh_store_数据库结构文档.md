# 生鲜小程序 数据库结构文档

### 库存管理模块

#### 1. 库存单主表 (inventory_order)

该表用于统一记录商品入库、出库和销售出库单的主要信息。

| 字段名 | 数据类型 | 允许为空 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| id | int(11) | 否 | 自增 | 主键ID |
| order_no | varchar(32) | 否 | - | 单据编号 |
| clientkeynum | varchar(35) | 否 | - | 平台客户唯一标识 |
| shop_id | int(11) | 是 | null | 门店ID，为空表示平台操作 |
| supplier_name | varchar(50) | 是 | null | 供应商名称（入库时使用） |
| member_id | int(11) | 是 | null | 会员ID（销售出库时使用） |
| order_type | tinyint(1) | 否 | 1 | 单据类型：1-入库，2-出库，3-销售出库 |
| business_type | tinyint(1) | 否 | 1 | 业务类型：入库(1-采购入库，2-调拨入库，3-退货入库，4-其他入库)；出库(1-销售出库，2-调拨出库，3-报损出库，4-其他出库) |
| total_amount | decimal(10,2) | 否 | 0.00 | 总金额 |
| discount_amount | decimal(10,2) | 否 | 0.00 | 优惠金额（销售出库时使用） |
| actual_amount | decimal(10,2) | 否 | 0.00 | 实际金额（销售出库时使用） |
| related_order_no | varchar(32) | 是 | null | 关联单号（如采购单号、销售单号、订单号） |
| remark | varchar(255) | 是 | null | 备注 |
| created_by | varchar(50) | 是 | null | 创建人 |
| completed_time | datetime | 是 | null | 完成时间（入库/出库时间） |
| created_at | datetime | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | 是 | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |


#### 2. 库存单明细表 (inventory_order_detail)

该表用于统一记录入库、出库和销售出库单中的商品明细信息。

| 字段名 | 数据类型 | 允许为空 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| id | int(11) | 否 | 自增 | 主键ID |
| order_id | int(11) | 否 | - | 库存单ID |
| order_no | varchar(32) | 否 | - | 库存单号 |
| order_type | tinyint(1) | 否 | 1 | 单据类型：1-入库，2-出库，3-销售出库 |
| product_id | int(11) | 否 | - | 商品ID |
| inventory_id | int(11) | 否 | - | 商品库存ID（规格ID） |
| quantity | int(11) | 否 | 0 | 数量 |
| price | decimal(10,2) | 否 | 0.00 | 单价 |
| discount | decimal(10,2) | 否 | 0.00 | 折扣（销售出库时使用） |
| amount | decimal(10,2) | 否 | 0.00 | 金额 |
| remark | varchar(255) | 是 | null | 备注 |
| created_at | datetime | 是 | CURRENT_TIMESTAMP | 创建时间 |



## 3. 库存变动日志表 (inventory_log)

该表用于记录所有库存变动的明细信息。

| 字段名 | 数据类型 | 允许为空 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| id | int(11) | 否 | 自增 | 主键ID |
| clientkeynum | varchar(35) | 否 | - | 平台客户唯一标识 |
| shop_id | int(11) | 是 | null | 门店ID，为空表示平台库存变动 |
| product_id | int(11) | 否 | - | 商品ID |
| inventory_id | int(11) | 否 | - | 商品库存ID（规格ID） |
| before_quantity | int(11) | 否 | 0 | 变动前数量 |
| change_quantity | int(11) | 否 | 0 | 变动数量 |
| after_quantity | int(11) | 否 | 0 | 变动后数量 |
| change_type | tinyint(1) | 否 | - | 变动类型：1-入库，2-出库，3-调拨入，4-调拨出，5-销售，6-退货，7-盘点，8-其他 |
| related_id | int(11) | 是 | null | 关联单据ID |
| related_no | varchar(32) | 是 | null | 关联单据号 |
| remark | varchar(255) | 是 | null | 备注 |
| operator | varchar(50) | 是 | null | 操作人 |
| created_at | datetime | 是 | CURRENT_TIMESTAMP | 创建时间 |

### 商品管理模块

#### 商品表 (products)

存储商品基本信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| clientkeynum | varchar(35) | 客户唯一标识 |
| category_id | int(11) | 分类ID |
| title | varchar(255) | 商品标题 |
| subtitle | varchar(255) | 副标题 |
| cover | varchar(255) | 封面图片 |
| banner | text | 轮播图 |
| kucun | int(11) | 库存 |
| price | decimal(10,2) | 价格 |
| market_price | decimal(10,2) | 市场价 |
| state | tinyint(4) | 状态: 0-保存 1-上架 -1-下架 |
| is_attribute | tinyint(1) | 是否开启多规格 |
| content | text | 商品详情 |

#### 商品分类表 (product_category)

商品分类信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| clientkeynum | varchar(35) | 客户唯一标识 |
| title | varchar(255) | 分类名称 |
| sort | int(11) | 排序 |

#### 商品规格表 (product_spec)

定义商品规格。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | bigint(20) | 主键ID |
| spec_name | varchar(128) | 规格名称 |
| attr_type | varchar(20) | 规格类型 |
| sort | tinyint(4) | 排序 |
| product_id | int(11) | 商品ID |

#### 商品库存表 (product_inventory)

存储商品不同规格的库存信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | bigint(20) | 主键ID |
| clientkeynum | varchar(35) | 客户唯一标识 |
| sn | varchar(32) | 商品编码 |
| title | varchar(255) | 标题 |
| spec_ids | varchar(50) | 属性组ID |
| attr_ids | varchar(191) | 属性ID |
| attr_vals | varchar(191) | 属性值 |
| product_id | bigint(20) | 商品ID |
| price | decimal(10,2) | 价格 |
| kucun | decimal(10,0) | 库存 |
| sale_num | int(11) | 销量 |

### 门店管理模块

#### 门店表 (shop)

存储门店基本信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| keynum | varchar(35) | 唯一标识 |
| clientkeynum | varchar(35) | 客户唯一标识 |
| background_image | varchar(255) | 背景图 |
| title | varchar(255) | 标题 |
| phone | varchar(20) | 联系方式 |
| address | varchar(255) | 地址 |
| lat | varchar(20) | 纬度 |
| lng | varchar(20) | 经度 |
| code | varchar(20) | 编码 |

#### 门店商品表 (shop_product)

关联门店和商品。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| shop_id | int(11) | 门店ID |
| product_id | int(11) | 商品ID |
| status | tinyint(4) | 状态：0下架 1上架 |
| clientkeynum | varchar(35) | 客户唯一标识 |
| is_audit | int(1) | 是否审核 |

#### 门店商品库存表 (shop_product_inventory)

存储门店商品库存信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| shop_id | int(11) | 门店ID |
| product_id | int(11) | 商品ID |
| inventory_id | int(11) | 库存ID |
| stock | int(11) | 库存 |
| clientkeynum | varchar(35) | 客户唯一标识 |

#### 门店商品库存变更日志表 (shop_product_stock_log)

记录门店商品库存变更历史。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| shop_id | int(11) | 门店ID |
| product_id | int(11) | 商品ID |
| spec_id | int(11) | 规格ID |
| old_stock | int(11) | 变更前库存 |
| new_stock | int(11) | 变更后库存 |
| time | datetime | 变更时间 |
| operator | varchar(255) | 操作人 |
| order_no | varchar(35) | 关联订单号 |

### 订单管理模块

#### 订单表 (order)

存储订单主要信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| clientkeynum | varchar(35) | 客户唯一标识 |
| order_no | varchar(20) | 订单编号 |
| shop_id | int(11) | 门店ID |
| user_id | int(11) | 用户ID |
| status | tinyint(4) | 订单状态 |
| type | tinyint(4) | 订单类型：1自提 2配送 |
| address_json | text | 收货地址JSON |
| product_price | decimal(10,2) | 商品价格 |
| shipping_price | decimal(10,2) | 运费 |
| card_price | decimal(10,2) | 卡支付金额 |
| price | decimal(10,2) | 微信支付金额 |
| pay_type | tinyint(4) | 支付类型 |
| pay_time | datetime | 支付时间 |

#### 订单详情表 (order_detail)

存储订单商品详情。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| clientkeynum | varchar(35) | 客户唯一标识 |
| order_no | varchar(255) | 订单编号 |
| shop_id | int(11) | 门店ID |
| user_id | int(11) | 用户ID |
| product_json | text | 商品JSON |
| inventory_json | text | 库存JSON |
| number | int(11) | 数量 |

#### 订单支付记录表 (order_pay_log)

记录订单支付信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| clientkeynum | varchar(35) | 客户唯一标识 |
| order_no | varchar(35) | 订单编号 |
| user_id | int(11) | 用户ID |
| price | decimal(10,2) | 支付金额 |
| real_price | decimal(10,2) | 实际支付金额 |
| is_pay | tinyint(4) | 是否支付 |
| pay_time | datetime | 支付时间 |
| transaction_id | varchar(50) | 交易ID |

### 会员管理模块

#### 会员表 (client_member)

存储会员基本信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| uuid | varchar(20) | 唯一标识 |
| name | varchar(100) | 会员姓名 |
| phone | varchar(11) | 会员手机号 |
| balance | decimal(10,2) | 会员拥有积分 |
| password | varchar(255) | 登录密码 |
| openid | varchar(50) | 微信openid |
| avatar | varchar(255) | 微信头像 |
| state | int(255) | 状态：1启用，0冻结禁用 |
| reg_from | tinyint(1) | 注册来源 |
| keynum | varchar(35) | 会员唯一标示 |
| clientkeynum | varchar(35) | 会员所属平台客户 |
| level | int(11) | 会员级别 |

#### 会员地址表 (user_address)

存储会员收货地址。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| clientkeynum | varchar(35) | 客户唯一标识 |
| user_id | int(11) | 用户ID |
| province | varchar(50) | 省份 |
| city | varchar(50) | 城市 |
| area | varchar(50) | 区域 |
| address | varchar(255) | 详细地址 |
| is_default | tinyint(4) | 是否默认 |
| consignee | varchar(50) | 收货人 |
| phone | varchar(20) | 联系电话 |

#### 购物车表 (user_cart)

存储用户购物车信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| clientkeynum | varchar(35) | 客户唯一标识 |
| user_id | int(11) | 用户ID |
| product_id | int(11) | 商品ID |
| inventory_id | int(11) | 库存ID |
| number | int(11) | 数量 |
| shop_id | int(11) | 门店ID |

### 储值卡管理模块

#### 储值卡表 (client_ycard)

存储储值卡基本信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(100) | 主键ID |
| member_id | int(11) | 绑定的用户ID |
| is_bind_member | tinyint(1) | 是否绑定了用户 |
| cardnum | varchar(100) | 卡号 |
| cardpwd | varchar(100) | 密码 |
| status | int(5) | 卡号状态 |
| kai_money | decimal(10,2) | 开卡金额 |
| yu_money | decimal(10,2) | 卡内剩余金额 |
| kai_exchange_num | int(10) | 最大兑换次数 |
| yu_exchange_num | int(10) | 剩余兑换次数 |
| begin_dui | varchar(255) | 开始兑换时间 |
| end_dui | varchar(255) | 结束兑换时间 |
| clientkeynum | varchar(100) | 客户唯一标识 |
| customer_id | int(11) | 档案客户ID |
| sale_id | int(11) | 档案销售ID |
| cardtype_id | int(11) | 卡型ID |

#### 卡状态变更表 (card_status_change)

记录卡状态变更信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| basekeynum | varchar(50) | 基础唯一标识 |
| no | varchar(25) | 批次单号 |
| customer_id | int(11) | 客户ID |
| sale_id | int(11) | 销售ID |
| status | tinyint(1) | 类型 |
| cardnum | varchar(1000) | 卡号列表 |
| operator | varchar(255) | 操作人 |
| remarks | varchar(255) | 备注 |
| add_time | datetime | 添加时间 |

#### 卡使用日志表 (client_card_use_log)

记录卡使用情况。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| basekeynum | varchar(50) | 基础唯一标识 |
| member_id | int(11) | 会员ID |
| order_sn | varchar(255) | 订单编号 |
| cardnum | varchar(255) | 卡号 |
| yu_money | decimal(10,2) | 剩余金额 |
| after_money | decimal(10,2) | 使用后金额 |
| use_money | decimal(10,2) | 使用金额 |
| status | tinyint(1) | 是否已支付 |
| add_time | datetime | 添加时间 |


### 食谱管理模块

#### 食谱表 (recipe)

存储食谱基本信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| title | varchar(100) | 食谱标题 |
| user_id | int(11) | 创建用户ID |
| category_id | int(11) | 分类ID |
| cover_image | varchar(255) | 封面图片URL |
| description | text | 食谱描述 |
| cooking_time | int(11) | 烹饪时间（分钟） |
| serving_size | int(11) | 适合人数 |
| difficulty | tinyint(1) | 难度 |
| content_type | tinyint(1) | 内容类型 |
| video_url | varchar(255) | 视频URL |
| views | int(11) | 浏览次数 |
| likes | int(11) | 点赞次数 |
| favorites | int(11) | 收藏次数 |
| status | tinyint(1) | 状态 |

#### 食谱分类表 (recipe_category)

存储食谱分类信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | int(11) | 主键ID |
| name | varchar(50) | 分类名称 |
| parent_id | int(11) | 父分类ID |
| sort | int(11) | 排序 |
| status | tinyint(1) | 状态 |

#### 食谱标签关联表 (recipe_tag_relation)

| 字段名 | 数据类型 | 描述 |
| --- | --- | --- |
| id | int | 关联ID，自增主键 |
| recipe_id | int | 食谱ID |
| tag_id | int | 标签ID |
| create_time | datetime | 创建时间 |

## 库存管理模块

### 入库单表 (inventory_inbound)

| 字段名 | 数据类型 | 描述 |
| --- | --- | --- |
| id | int | 入库单ID，自增主键 |
| inbound_no | varchar(32) | 入库单号，唯一 |
| clientkeynum | varchar(35) | 平台客户唯一标识 |
| shop_id | int | 门店ID，为空表示平台入库 |
| supplier_id | int | 供应商ID |
| inbound_type | tinyint(1) | 入库类型：1-采购入库，2-调拨入库，3-退货入库，4-其他入库 |
| total_amount | decimal(10,2) | 总金额 |
| related_order_no | varchar(32) | 关联单号（如采购单号） |
| status | tinyint(1) | 状态：0-草稿，1-已提交，2-已审核，3-已入库，-1-已取消 |
| remark | varchar(255) | 备注 |
| created_by | varchar(50) | 创建人 |
| reviewed_by | varchar(50) | 审核人 |
| reviewed_time | datetime | 审核时间 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### 入库单明细表 (inventory_inbound_detail)

| 字段名 | 数据类型 | 描述 |
| --- | --- | --- |
| id | int | 明细ID，自增主键 |
| inbound_id | int | 入库单ID |
| inbound_no | varchar(32) | 入库单号 |
| product_id | int | 商品ID |
| inventory_id | int | 商品库存ID（规格ID） |
| quantity | int | 入库数量 |
| price | decimal(10,2) | 单价 |
| amount | decimal(10,2) | 金额 |
| remark | varchar(255) | 备注 |
| created_at | datetime | 创建时间 |

### 出库单表 (inventory_outbound)

| 字段名 | 数据类型 | 描述 |
| --- | --- | --- |
| id | int | 出库单ID，自增主键 |
| outbound_no | varchar(32) | 出库单号，唯一 |
| clientkeynum | varchar(35) | 平台客户唯一标识 |
| shop_id | int | 门店ID，为空表示平台出库 |
| outbound_type | tinyint(1) | 出库类型：1-销售出库，2-调拨出库，3-报损出库，4-其他出库 |
| total_amount | decimal(10,2) | 总金额 |
| related_order_no | varchar(32) | 关联单号（如销售单号） |
| status | tinyint(1) | 状态：0-草稿，1-已提交，2-已审核，3-已出库，-1-已取消 |
| remark | varchar(255) | 备注 |
| created_by | varchar(50) | 创建人 |
| reviewed_by | varchar(50) | 审核人 |
| reviewed_time | datetime | 审核时间 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### 出库单明细表 (inventory_outbound_detail)

| 字段名 | 数据类型 | 描述 |
| --- | --- | --- |
| id | int | 明细ID，自增主键 |
| outbound_id | int | 出库单ID |
| outbound_no | varchar(32) | 出库单号 |
| product_id | int | 商品ID |
| inventory_id | int | 商品库存ID（规格ID） |
| quantity | int | 出库数量 |
| price | decimal(10,2) | 单价 |
| amount | decimal(10,2) | 金额 |
| remark | varchar(255) | 备注 |
| created_at | datetime | 创建时间 |

### 调拨单表 (inventory_transfer)

| 字段名 | 数据类型 | 描述 |
| --- | --- | --- |
| id | int | 调拨单ID，自增主键 |
| transfer_no | varchar(32) | 调拨单号，唯一 |
| clientkeynum | varchar(35) | 平台客户唯一标识 |
| from_shop_id | int | 调出门店ID，为空表示从平台调出 |
| to_shop_id | int | 调入门店ID，为空表示调入平台 |
| total_amount | decimal(10,2) | 总金额 |
| status | tinyint(1) | 状态：0-草稿，1-已提交，2-已审核，3-调出完成，4-调入完成，-1-已取消 |
| remark | varchar(255) | 备注 |
| created_by | varchar(50) | 创建人 |
| reviewed_by | varchar(50) | 审核人 |
| reviewed_time | datetime | 审核时间 |
| out_time | datetime | 调出时间 |
| in_time | datetime | 调入时间 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### 调拨单明细表 (inventory_transfer_detail)

| 字段名 | 数据类型 | 描述 |
| --- | --- | --- |
| id | int | 明细ID，自增主键 |
| transfer_id | int | 调拨单ID |
| transfer_no | varchar(32) | 调拨单号 |
| product_id | int | 商品ID |
| inventory_id | int | 商品库存ID（规格ID） |
| quantity | int | 调拨数量 |
| price | decimal(10,2) | 单价 |
| amount | decimal(10,2) | 金额 |
| remark | varchar(255) | 备注 |
| created_at | datetime | 创建时间 |

### 销货出库单表 (inventory_sales)

| 字段名 | 数据类型 | 描述 |
| --- | --- | --- |
| id | int | 销货单ID，自增主键 |
| sales_no | varchar(32) | 销货单号，唯一 |
| clientkeynum | varchar(35) | 平台客户唯一标识 |
| shop_id | int | 门店ID，为空表示平台销售 |
| order_no | varchar(32) | 关联订单号 |
| member_id | int | 会员ID |
| total_amount | decimal(10,2) | 总金额 |
| discount_amount | decimal(10,2) | 优惠金额 |
| actual_amount | decimal(10,2) | 实际金额 |
| status | tinyint(1) | 状态：0-草稿，1-已提交，2-已审核，3-已出库，-1-已取消 |
| remark | varchar(255) | 备注 |
| created_by | varchar(50) | 创建人 |
| reviewed_by | varchar(50) | 审核人 |
| reviewed_time | datetime | 审核时间 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### 销货出库单明细表 (inventory_sales_detail)

| 字段名 | 数据类型 | 描述 |
| --- | --- | --- |
| id | int | 明细ID，自增主键 |
| sales_id | int | 销货单ID |
| sales_no | varchar(32) | 销货单号 |
| product_id | int | 商品ID |
| inventory_id | int | 商品库存ID（规格ID） |
| quantity | int | 销售数量 |
| price | decimal(10,2) | 单价 |
| discount | decimal(10,2) | 折扣 |
| amount | decimal(10,2) | 金额 |
| remark | varchar(255) | 备注 |
| created_at | datetime | 创建时间 |

### 库存变动日志表 (inventory_log)

| 字段名 | 数据类型 | 描述 |
| --- | --- | --- |
| id | int | 日志ID，自增主键 |
| clientkeynum | varchar(35) | 平台客户唯一标识 |
| shop_id | int | 门店ID，为空表示平台库存变动 |
| product_id | int | 商品ID |
| inventory_id | int | 商品库存ID（规格ID） |
| before_quantity | int | 变动前数量 |
| change_quantity | int | 变动数量 |
| after_quantity | int | 变动后数量 |
| change_type | tinyint(1) | 变动类型：1-入库，2-出库，3-调拨入，4-调拨出，5-销售，6-退货，7-盘点，8-其他 |
| related_id | int | 关联单据ID |
| related_no | varchar(32) | 关联单据号 |
| remark | varchar(255) | 备注 |
| operator | varchar(50) | 操作人 |
| created_at | datetime | 创建时间 |
